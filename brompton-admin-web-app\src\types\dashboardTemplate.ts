import { DashboardState } from './dashboard';

export type Customer = {
  id: number;
  name_id: string;
  name: string;
  address: string;
};

export type AssetType = {
  id: number;
  parentType: number;
  name: string;
};

export type Metric = {
  id: number;
  name: string;
};

export type AssetTemplate = {
  id: number;
  manufacturer: string;
  modelNumber: string;
  assetType: AssetType;
};

export type Measurement = {
  id: number;
  metric: Metric;
  assetTemplate: AssetTemplate;
  measurementType: number;
  dataType: number;
  location: number;
  valueType: number;
  datasource: number;
  description: string;
  meterFactor: any; // or null
};

export type AssetTemplateDetails = {
  id: number;
  manufacturer: string;
  modelNumber: string;
  assetType: AssetType;
  measurements: Measurement[];
};

export type DashboardTemplate = {
  id: number;
  customer: Customer;
  title: string;
  data: string | null;
  // data: DashboardState['widget'];
  asset_template: AssetTemplateDetails;
  createdby: number;
  updatedby: number | null;
  createdat: string;
  updatedat: string | null;
};

export type DashboardTemplateDTO = {
  total: number;
  items: DashboardTemplate[];
};
