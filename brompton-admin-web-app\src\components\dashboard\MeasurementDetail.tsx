import { Box, Button, Stack, Typography } from '@mui/material';
import { useMemo } from 'react';
import {
  AssetMeasurement,
  DataType,
  Datasource,
  MeasurementLocation,
  MeasurementType,
  UnitOfMeasure,
  ValueType,
} from '~/measurements/domain/types';

type MeasurementDetailProps = {
  assetMeasurement: AssetMeasurement;
  measurementTypeList: MeasurementType[];
  dataTypeList: DataType[];
  valueTypeList: ValueType[];
  unitOfMeasureList: UnitOfMeasure[];
  locationList: MeasurementLocation[];
  datasourceList: Datasource[];
  onDelete: (assetMeasurementId: number) => unknown;
};

const useMemoType = <Type extends { id: number; name: string }>(
  assetMeasurementTypeId: number,
  typeList: Type[],
): Type | undefined =>
  useMemo(
    () => typeList.find((type) => type.id === assetMeasurementTypeId),
    [assetMeasurementTypeId, typeList],
  );

export default function MeasurementDetail({
  assetMeasurement,
  measurementTypeList,
  dataTypeList,
  valueTypeList,
  unitOfMeasureList,
  locationList,
  datasourceList,
  onDelete,
}: MeasurementDetailProps): JSX.Element {
  const measurementType = useMemoType(assetMeasurement.typeId, measurementTypeList);
  const dataType = useMemoType(assetMeasurement.dataTypeId, dataTypeList);
  const valueType = useMemoType(assetMeasurement.valueTypeId, valueTypeList);
  const unitOfMeasure = useMemoType(assetMeasurement.unitOfMeasureId ?? NaN, unitOfMeasureList);
  const meterLocation = useMemoType(assetMeasurement.locationId ?? NaN, locationList);
  const datasource = useMemoType(assetMeasurement.datasourceId ?? NaN, datasourceList);

  return (
    <Box sx={{ display: 'flex', flexDirection: 'column' }}>
      <Stack direction={'row'}>
        <Typography variant="h4">Measurement Detail</Typography>
        <Button sx={{ ml: 'auto' }} onClick={() => onDelete(assetMeasurement.id)}>
          Delete
        </Button>
      </Stack>
      <Typography variant="h5" mt={2}>
        {assetMeasurement.id} - &quot;{assetMeasurement.tag}
        &quot;
      </Typography>

      <Typography variant="overline" mt={2}>
        Measurement ID {assetMeasurement.measurementId}
      </Typography>

      <Typography variant="h6" mt={2}>
        Type
      </Typography>
      <Typography variant="body2" paragraph>
        {measurementType?.name}
      </Typography>
      <Typography variant="h6">Description</Typography>
      <Typography variant="body2" paragraph>
        {assetMeasurement.description ?? 'No description available.'}
      </Typography>
      <Typography variant="h6">Information</Typography>
      <Typography variant="body2" paragraph>
        Data type: {dataType?.name}
      </Typography>
      <Typography variant="body2" paragraph>
        Value type: {valueType?.name}
      </Typography>
      <Typography variant="body2" paragraph>
        Unit of measure: {unitOfMeasure?.name ?? 'N/A'}
      </Typography>
      <Typography variant="body2" paragraph>
        Meter location: {meterLocation?.name ?? 'N/A'}
      </Typography>
      <Typography variant="body2" paragraph>
        Datasource: {datasource?.name ?? 'N/A'}
      </Typography>
      <Typography variant="body2" paragraph>
        Meter Factor: {assetMeasurement.meterFactor ?? 'N/A'}
      </Typography>
    </Box>
  );
}
