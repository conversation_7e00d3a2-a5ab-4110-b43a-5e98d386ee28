import { test, expect, locator } from '@playwright/test';
import { LoginDetails } from '../../POM/LoginDetails';
import { waitForDebugger } from 'inspector';

test('loginpage', async ({ page }) => {
  //Login
  const Login1 = new LoginDetails(page);
  await Login1.lauchURL(); // launching the URL
  await Login1.login('test', 'asdfasdf'); // Valid <PERSON>ls
  /*  await Login1.login('test1', 'asdfasdf122'); // inValid Deatils
    await page.waitForTimeout(2000);

    await Login1.forgetpass();// click on forget password
    await page.waitForTimeout(10000);
    await Login1.forgetpass('<EMAIL>');
    await page.waitForTimeout(5000);
*/
  // new dashboard click
  await page.click(
    '#__next > div > div.MuiStack-root.css-92qf02 > div > div > div.MuiBox-root.css-xy51px > div.MuiBox-root.css-9nra4q > button',
  );
  /*
    // selct the customer
      await page.getByLabel('Select Customer').click();
      await page.getByRole('combobox', { name: 'Customer' }).click();
      await page.getByRole('option', { name: 'Customer', exact: true }).click();
      await page.getByTestId('ArrowDropDownIcon').nth(2).waitFor({ state: 'visible', timeout: 2000 });
      await page.getByText('Cancel').click();
 
      var button= await page.locator('//p[normalize-space()=Customer]'); 

      await button.click({button: 'right'});
*/
  /*
    // click on widget 
    await page.locator('id=widgets-icon').click();
    await page.waitForTimeout(10000);

    //drag the widget
    await page.locator('#stats').dragTo(page.locator('.react-grid-layout.layout'));
    await page.waitForTimeout(2000);
    await page.locator('#title').dragTo(page.locator('.react-grid-layout.layout'));
    await page.waitForTimeout(2000);
    await page.locator('#table').dragTo(page.locator('.react-grid-layout.layout'));
    await page.waitForTimeout(2000);
    await page.locator('id=widgets-icon').click();

    //click on widget setting
    //await page.click('//div[@class=MuiDialog-root MuiModal-root css-126xj0f]');
*/

  // CLick on add new User
  //await page.waitForTimeout(2000);
  await page.click('//*[@id="__next"]/div/div[1]/div/ul/li[4]/a/div[2]/span');
  /*     
    // await page.waitForTimeout(5000);

     await page.click('//*[@id="__next"]/div/div[2]/p/button');
     
     await page.getByLabel('First Name').click();
     await page.getByLabel('First Name').fill('Martin');
     await page.getByLabel('Last Name').click();
     await page.getByLabel('Last Name').fill('Jak');
     await page.getByLabel('Username *').click();
     await page.getByLabel('Username *').fill('martin');
     await page.getByLabel('Password *').click();
     await page.getByLabel('Password *').fill('qwerty');
     await page.getByLabel('Email *').click();
     await page.getByLabel('Email *').fill('<EMAIL>');
     await page.getByLabel('Country Code *').click();
     await page.getByLabel('Country Code *').fill('+91');
     await page.getByLabel('Phone Number *').click();
     await page.getByLabel('Phone Number *').fill('9307646191');

     await page.getByLabel('Select Customer for User').click();
     //await page.click('/html/body/div[2]/div[3]/div/div[1]/form/div/div[7]/div/div/fieldset/legend');
      await page.getByRole('option', { name: 'Brompton Energy Inc.', exact: true }).click();
     //const drop= page.getByTestId('ArrowDropDownIcon').nth(2);//waitFor({ state: 'visible', timeout: 2000 });
     //await drop.click();

     await page.waitForTimeout(5000);
     await page.getByRole('button', { name: 'Submit' }).click();
*/
  // edit user
  //await page.click('//*[@id="__next"]/div/div[1]/div/ul/li[4]/a/div[2]/span');

  const searchField = page.locator('input[placeholder="Search…"]'); // Use the appropriate selector
  await searchField.fill('yogi1');
  await page.waitForTimeout(2000);
  const moveh = page.click('//*[@id="__next"]/div/div[2]/div[2]/div[2]/div[2]/div/div/div/div[6]');
  await page.waitForTimeout(5000);
  await page.click('//*[@id="__next"]/div/div[2]/div[2]/div[2]/div[2]/div/div/div/div[6]/button');
  //moveh.click();
  await page.getByLabel('First Name').click();
  await page.getByLabel('First Name').fill('Martin mick');
  //await page.mouse.move(0, 100);
  await page.getByRole('button', { name: 'Update' }).click();
  await page.reload();
  const searchField1 = page.locator('input[placeholder="Search…"]'); // Use the appropriate selector
  await searchField1.fill('yogi1');

  await page.waitForTimeout(5000);

  await page.close();
});
