import CommonWidgetContainer from '~/components/common/CommonWidgetContainer';
import { KPITable } from '~/types/widgets';
import KPITableDialog from './KPITableDialog';
import { Box, Typography } from '@mui/material';
import { useFetchKPITableData } from '~/hooks/useFetchKPITableData';
import dynamic from 'next/dynamic';
import {
  DataGrid,
  GridColDef,
  GridRenderCellParams,
  GridTreeNodeWithRender,
} from '@mui/x-data-grid';
import CustomNoRowsOverlay from '~/components/common/NoRowsOverlay';
import NoMeasureSelected from '~/components/common/NoMeasureSelected';
import Loader from '~/components/common/Loader';
const Plot = dynamic(() => import('react-plotly.js'), { ssr: false });

type KPITableContainerProps = {
  id: string;
  settings: KPITable;
};

function hasNoMeasuresSelected(settings: KPITable): boolean {
  if (settings.mode === 'template') {
    return settings.selectedTitles.length === 0;
  }
  if (settings.mode === 'dashboard') {
    if (!settings.assetMeasure?.length) return true;
    return settings.assetMeasure.every(
      (am) => !am.measureId.length || am.measureId.every((mId) => mId.trim() === ''),
    );
  }
  return !settings.selectedTitles.length;
}

const KPITableContainer = ({ id, settings }: KPITableContainerProps) => {
  const { res, isLoading, removedResults, successAndFailedMeasurements } = useFetchKPITableData(
    id,
    settings,
  );
  const columns: GridColDef[] = [
    { field: 'name', headerName: 'Measure Name', width: 500 },
    settings.min && settings.min.show
      ? {
          field: 'min',
          headerName: settings.min.label ?? 'Min',
          width: 150,
          align: 'center',
          headerAlign: 'center',
          sortable: true,
          renderCell: (params: GridRenderCellParams<any, any, any, GridTreeNodeWithRender>) => {
            return <>{params.row.stats.stats.min}</>;
          },
        }
      : undefined,
    settings.max && settings.max.show
      ? {
          field: 'max',
          headerName: settings.max.label ?? 'Max',
          width: 150,
          align: 'center',
          headerAlign: 'center',
          sortable: true,
          renderCell: (params: GridRenderCellParams<any, any, any, GridTreeNodeWithRender>) => {
            return <>{params.row.stats.stats.max}</>;
          },
        }
      : undefined,
    settings.mean && settings.mean.show
      ? {
          field: 'avg',
          headerName: settings.mean.label ?? 'Mean',
          headerAlign: 'center',
          align: 'center',
          width: 150,
          sortable: true,
          renderCell: (params: GridRenderCellParams<any, any, any, GridTreeNodeWithRender>) => {
            return <>{params.row.stats.stats.avg}</>;
          },
        }
      : undefined,
    settings.delta && settings.delta.show
      ? {
          field: 'delta',
          headerName: settings.delta.label ?? 'Delta',
          headerAlign: 'center',
          align: 'center',
          width: 150,
          sortable: true,
          renderCell: (params: GridRenderCellParams<any, any, any, GridTreeNodeWithRender>) => {
            return <>{params.row.stats.stats.delta}</>;
          },
        }
      : undefined,
    settings.sum && settings.sum.show
      ? {
          field: 'sum',
          headerName: settings.sum.label ?? 'Sum',
          headerAlign: 'center',
          align: 'center',
          width: 150,
          sortable: true,
          renderCell: (params: GridRenderCellParams<any, any, any, GridTreeNodeWithRender>) => {
            return <>{params.row.stats.stats.sum}</>;
          },
        }
      : undefined,
    settings.current && settings.current.show
      ? {
          field: 'current',
          headerName: settings.current.label ?? 'Current',
          headerAlign: 'center',
          align: 'center',
          width: 150,
          sortable: true,
          renderCell: (params: GridRenderCellParams<any, any, any, GridTreeNodeWithRender>) => {
            return <>{params.row.stats.stats.current}</>;
          },
        }
      : undefined,
    {
      field: 'chart',
      headerName: 'Chart',
      width: 150,
      sortable: true,
      renderCell: (params: GridRenderCellParams<any, any, any, GridTreeNodeWithRender>) => {
        return (
          <Plot
            data={params.row.chartData || []}
            useResizeHandler={true}
            style={{ width: '100%', height: '100%' }}
            layout={{
              xaxis: {
                // title: chartLabel.xAsisLabel,
                position: 0,
              },
              yaxis: {
                title: 'Value',
                position: 0,
              },
              legend: {
                x: 0, // Position legend at the left
                y: -0.1, // Position legend slightly below the x-axis
                xanchor: 'left', // Anchor the legend to the right side of the x position
                yanchor: 'top', // Anchor the legend to the top side of the y position
              },
              autosize: true,
              ...params.row?.stats?.layout,
            }}
            config={{
              responsive: true,
              displaylogo: false,
              displayModeBar: false, // This will hide the entire mode bar
              modeBarButtonsToRemove: ['pan2d', 'lasso2d'],
            }}
          />
        );
      },
    },
  ].filter(Boolean) as GridColDef[]; // Filter out undefined values and cast to GridColDef[]

  const noMeasuresSelected = hasNoMeasuresSelected(settings);
  return (
    <CommonWidgetContainer
      id={id}
      successAndFailedMeasurements={successAndFailedMeasurements}
      removedResults={removedResults}
      settings={settings}
      widgetType="kpi-table"
      widgetName="KPI Table"
      settingsDialog={KPITableDialog}
      widgetContent={
        <Box height={'100%'}>
          {noMeasuresSelected && <NoMeasureSelected />}
          {isLoading && <Loader />}
          {res && (
            <DataGrid
              rows={res ?? []}
              columns={columns}
              initialState={{
                pagination: {
                  paginationModel: {
                    pageSize: 5,
                  },
                },
              }}
              slots={{
                noRowsOverlay: CustomNoRowsOverlay,
                toolbar: () => (
                  <>
                    {settings.title.isVisible && (
                      <Typography
                        variant="h4"
                        component="div"
                        sx={{ flexGrow: 1 }}
                        style={{
                          textAlign: 'center',
                          fontSize: settings.title.fontSize + 'px',
                          fontWeight: settings.title.fontWeight,
                          color: settings.title.color,
                        }}
                      >
                        {settings.title.value}
                      </Typography>
                    )}
                  </>
                ),
              }}
              pageSizeOptions={[5, 10]}
              disableRowSelectionOnClick
              loading={isLoading}
            />
          )}
        </Box>
      }
    />
  );
};

export default KPITableContainer;
