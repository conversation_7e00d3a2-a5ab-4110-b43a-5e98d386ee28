import { Box, Button, Typography } from '@mui/material';
import Link from 'next/link';
const Custom404Page = () => {
  return (
    <Box
      sx={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        flexDirection: 'column',
        minHeight: '90vh',
      }}
    >
      <Typography variant="h1">404</Typography>
      <Typography variant="h6">The page you’re looking for doesn’t exist.</Typography>
      <Button variant="contained" sx={{ mt: 3 }} href="/" LinkComponent={Link}>
        Back Home
      </Button>
    </Box>
  );
};

export default Custom404Page;
