import { Button, Typography } from '@mui/material';
import CustomDialog from './CustomDialog';
import { useDispatch } from 'react-redux';
import { dashboardSlice } from '~/redux/slices/dashboardSlice';
import DeleteIcon from '@mui/icons-material/Delete';
import CancelIcon from '@mui/icons-material/Cancel';
type DeleteWidgetDialogProps = {
  open: boolean;
  widgetId: string;
  widgetName: string;
  onCancel: () => void;
};
const DeleteWidgetDialog = ({ widgetId, widgetName, open, onCancel }: DeleteWidgetDialogProps) => {
  const dispatch = useDispatch();
  const handleDeleteWidget = () => {
    dispatch(dashboardSlice.actions.deleteSpecificWidget(widgetId));
    onCancel();
  };
  return (
    <CustomDialog
      content={null}
      dialogActions={
        <>
          <Button onClick={onCancel} variant="outlined" startIcon={<CancelIcon />}>
            Cancel
          </Button>
          <Button
            onClick={handleDeleteWidget}
            variant="contained"
            color="error"
            startIcon={<DeleteIcon />}
          >
            Delete
          </Button>
        </>
      }
      title={
        <>
          <Typography variant="h4">Delete Widget?</Typography>
          <Typography color="error">This action cannot be undone.</Typography>
        </>
      }
      onClose={onCancel}
      open={open}
    />
  );
};

export default DeleteWidgetDialog;
