import { yupResolver } from '@hookform/resolvers/yup';
import { Alert, Autocomplete, Box, Button, TextField } from '@mui/material';
import { useEffect } from 'react';
import { Controller, useForm } from 'react-hook-form';
import { ControlledTextField } from '~/shared/forms/components/ControlledTextField';
import { AlertMessage } from '~/shared/forms/types';
import {
  AssetTypeOption,
  EditAsset,
  EditAssetDo,
  TimeZoneDto,
  editAssetSchema,
} from '../../types/asset';

type EditAssetFormProps = {
  timeZoneList: TimeZoneDto[];
  assetTypesWithPath: AssetTypeOption[];
  loading: boolean;
  parentId?: number;
  alertMessage: AlertMessage | undefined;
  asset: EditAssetDo | undefined;
  onValidSubmit: (data: EditAsset) => Promise<unknown>;
};
const EditAssetForm = ({
  timeZoneList,
  assetTypesWithPath,
  loading,
  parentId,
  asset,
  alertMessage,
  onValidSubmit,
}: EditAssetFormProps) => {
  const { control, handleSubmit, reset, trigger } = useForm<EditAsset>({
    defaultValues: { ...asset, parent_ids: parentId ? [parentId] : [] },
    resolver: yupResolver(editAssetSchema),
  });
  useEffect(() => {
    reset(asset);
  }, [asset]);
  return (
    <form
      onSubmit={handleSubmit(async (data) => {
        try {
          await onValidSubmit(data);
          //   reset();
        } catch (err) {}
      })}
      noValidate
    >
      <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
        <ControlledTextField
          control={control}
          fieldName="tag"
          label="Tag"
          loading={loading}
          required
        />
        <Controller
          name="type_id"
          control={control}
          render={({ field: { onChange, onBlur, value }, fieldState }) => (
            <Autocomplete
              options={assetTypesWithPath.map((item) => ({
                id: item.value,
                label: item.label,
              }))}
              disabled
              renderInput={(params) => (
                <TextField
                  {...params}
                  label="Asset type"
                  error={!!fieldState.error}
                  helperText={fieldState.error?.message}
                />
              )}
              onChange={(_, selectedOption) => onChange(selectedOption?.id)}
              onBlur={onBlur}
              value={
                assetTypesWithPath
                  .map((item) => ({ id: item.value, label: item.label }))
                  .find((assetType) => assetType.id === value) ?? null
              }
              sx={{ mt: 2, mb: 1 }}
              fullWidth
            />
          )}
        />

        <ControlledTextField
          control={control}
          fieldName="description"
          label="Description"
          loading={loading}
          multiline
        />
        <Controller
          name="time_zone"
          control={control}
          render={({ field: { onChange, onBlur, value }, fieldState }) => (
            <Autocomplete
              options={timeZoneList}
              renderInput={(params) => (
                <TextField
                  {...params}
                  label="Select a time zone"
                  error={!!fieldState.error}
                  helperText={fieldState.error?.message}
                />
              )}
              onChange={(_, value) => onChange(value)}
              onBlur={onBlur}
              value={value ?? null}
              sx={{ mt: 2, mb: 1 }}
              fullWidth
            />
          )}
        />

        <Controller
          name="latitude"
          control={control}
          render={({ field: { onChange, onBlur, value }, fieldState, formState }) => (
            <TextField
              error={!!fieldState.error}
              helperText={fieldState.error?.message}
              onChange={(e) => {
                onChange(e.target.value === '' ? null : e.target.value);
                if (formState.isSubmitted) {
                  trigger('longitude');
                }
              }}
              onBlur={onBlur}
              type="number"
              value={value ?? ''}
              label="Latitude"
              variant="outlined"
              margin="normal"
              disabled={loading}
              fullWidth
            />
          )}
        />
        <Controller
          name="longitude"
          control={control}
          render={({ field: { onChange, onBlur, value }, fieldState, formState }) => (
            <TextField
              error={!!fieldState.error}
              helperText={fieldState.error?.message}
              onChange={(e) => {
                onChange(e.target.value === '' ? null : e.target.value);
                if (formState.isSubmitted) {
                  trigger('latitude');
                }
              }}
              onBlur={onBlur}
              type="number"
              value={value ?? ''}
              label="Longitude"
              variant="outlined"
              margin="normal"
              disabled={loading}
              fullWidth
            />
          )}
        />
        <Button
          type="submit"
          variant="contained"
          size="large"
          sx={{ mt: 2, width: 200 }}
          disabled={loading}
        >
          Submit
        </Button>
      </Box>
      {alertMessage && (
        <Alert severity={alertMessage.severity} sx={{ mt: 3 }}>
          {alertMessage.message}
        </Alert>
      )}
      {/* {Object.keys(touchedFields).length > 0 && !!asset?.assetTemplate && (
        <Alert severity="warning" sx={{ mt: 3 }}>
          <strong>Important Notice:</strong> Any modifications made to this asset will automatically
          detach it from the associated asset template. Please review your changes carefully.
        </Alert>
      )} */}
    </form>
  );
};

export default EditAssetForm;
