"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7847],{91819:function(tn,ti,ta){ta.r(ti),ta.d(ti,{AcroForm:function(){return tF},AcroFormAppearance:function(){return tP},AcroFormButton:function(){return mt},AcroFormCheckBox:function(){return wt},AcroFormChoiceField:function(){return ft},AcroFormComboBox:function(){return pt},AcroFormEditBox:function(){return gt},AcroFormListBox:function(){return dt},AcroFormPasswordField:function(){return Lt},AcroFormPushButton:function(){return vt},AcroFormRadioButton:function(){return bt},AcroFormTextField:function(){return Nt},GState:function(){return j},ShadingPattern:function(){return B},TilingPattern:function(){return M},jsPDF:function(){return E}});var to=ta(71002),ts=ta(93778),tc=function(){return"undefined"!=typeof window?window:void 0!==ta.g?ta.g:"undefined"!=typeof self?self:this}();function i(){tc.console&&"function"==typeof tc.console.log&&tc.console.log.apply(tc.console,arguments)}var tu={log:i,warn:function(tn){tc.console&&("function"==typeof tc.console.warn?tc.console.warn.apply(tc.console,arguments):i.call(null,arguments))},error:function(tn){tc.console&&("function"==typeof tc.console.error?tc.console.error.apply(tc.console,arguments):i(tn))}};function o(tn,ti,ta){var to=new XMLHttpRequest;to.open("GET",tn),to.responseType="blob",to.onload=function(){tf(to.response,ti,ta)},to.onerror=function(){tu.error("could not download file")},to.send()}function s(tn){var ti=new XMLHttpRequest;ti.open("HEAD",tn,!1);try{ti.send()}catch(tn){}return ti.status>=200&&ti.status<=299}function c(tn){try{tn.dispatchEvent(new MouseEvent("click"))}catch(ta){var ti=document.createEvent("MouseEvents");ti.initMouseEvent("click",!0,!0,window,0,0,0,80,20,!1,!1,!1,!1,0,null),tn.dispatchEvent(ti)}}var th,tl,tf=tc.saveAs||("object"!==("undefined"==typeof window?"undefined":(0,to.Z)(window))||window!==tc?function(){}:"undefined"!=typeof HTMLAnchorElement&&"download"in HTMLAnchorElement.prototype?function(tn,ti,ta){var to=tc.URL||tc.webkitURL,ts=document.createElement("a");ti=ti||tn.name||"download",ts.download=ti,ts.rel="noopener","string"==typeof tn?(ts.href=tn,ts.origin!==location.origin?s(ts.href)?o(tn,ti,ta):c(ts,ts.target="_blank"):c(ts)):(ts.href=to.createObjectURL(tn),setTimeout(function(){to.revokeObjectURL(ts.href)},4e4),setTimeout(function(){c(ts)},0))}:"msSaveOrOpenBlob"in navigator?function(tn,ti,ta){if(ti=ti||tn.name||"download","string"==typeof tn){if(s(tn))o(tn,ti,ta);else{var ts,tc=document.createElement("a");tc.href=tn,tc.target="_blank",setTimeout(function(){c(tc)})}}else navigator.msSaveOrOpenBlob((void 0===(ts=ta)?ts={autoBom:!1}:"object"!==(0,to.Z)(ts)&&(tu.warn("Deprecated: Expected third argument to be a object"),ts={autoBom:!ts}),ts.autoBom&&/^\s*(?:text\/\S*|application\/xml|\S*\/\S*\+xml)\s*;.*charset\s*=\s*utf-8/i.test(tn.type)?new Blob([String.fromCharCode(65279),tn],{type:tn.type}):tn),ti)}:function(tn,ti,ta,ts){if((ts=ts||open("","_blank"))&&(ts.document.title=ts.document.body.innerText="downloading..."),"string"==typeof tn)return o(tn,ti,ta);var tu="application/octet-stream"===tn.type,th=/constructor/i.test(tc.HTMLElement)||tc.safari,tl=/CriOS\/[\d]+/.test(navigator.userAgent);if((tl||tu&&th)&&"object"===("undefined"==typeof FileReader?"undefined":(0,to.Z)(FileReader))){var tf=new FileReader;tf.onloadend=function(){var tn=tf.result;tn=tl?tn:tn.replace(/^data:[^;]*;/,"data:attachment/file;"),ts?ts.location.href=tn:location=tn,ts=null},tf.readAsDataURL(tn)}else{var td=tc.URL||tc.webkitURL,tp=td.createObjectURL(tn);ts?ts.location=tp:location.href=tp,ts=null,setTimeout(function(){td.revokeObjectURL(tp)},4e4)}});/**
 * A class to parse color values
 * <AUTHOR> Stefanov <<EMAIL>>
 * {@link   http://www.phpied.com/rgb-color-parser-in-javascript/}
 * @license Use it if you like it
 */function f(tn){var ti;tn=tn||"",this.ok=!1,"#"==tn.charAt(0)&&(tn=tn.substr(1,6)),tn=({aliceblue:"f0f8ff",antiquewhite:"faebd7",aqua:"00ffff",aquamarine:"7fffd4",azure:"f0ffff",beige:"f5f5dc",bisque:"ffe4c4",black:"000000",blanchedalmond:"ffebcd",blue:"0000ff",blueviolet:"8a2be2",brown:"a52a2a",burlywood:"deb887",cadetblue:"5f9ea0",chartreuse:"7fff00",chocolate:"d2691e",coral:"ff7f50",cornflowerblue:"6495ed",cornsilk:"fff8dc",crimson:"dc143c",cyan:"00ffff",darkblue:"00008b",darkcyan:"008b8b",darkgoldenrod:"b8860b",darkgray:"a9a9a9",darkgreen:"006400",darkkhaki:"bdb76b",darkmagenta:"8b008b",darkolivegreen:"556b2f",darkorange:"ff8c00",darkorchid:"9932cc",darkred:"8b0000",darksalmon:"e9967a",darkseagreen:"8fbc8f",darkslateblue:"483d8b",darkslategray:"2f4f4f",darkturquoise:"00ced1",darkviolet:"9400d3",deeppink:"ff1493",deepskyblue:"00bfff",dimgray:"696969",dodgerblue:"1e90ff",feldspar:"d19275",firebrick:"b22222",floralwhite:"fffaf0",forestgreen:"228b22",fuchsia:"ff00ff",gainsboro:"dcdcdc",ghostwhite:"f8f8ff",gold:"ffd700",goldenrod:"daa520",gray:"808080",green:"008000",greenyellow:"adff2f",honeydew:"f0fff0",hotpink:"ff69b4",indianred:"cd5c5c",indigo:"4b0082",ivory:"fffff0",khaki:"f0e68c",lavender:"e6e6fa",lavenderblush:"fff0f5",lawngreen:"7cfc00",lemonchiffon:"fffacd",lightblue:"add8e6",lightcoral:"f08080",lightcyan:"e0ffff",lightgoldenrodyellow:"fafad2",lightgrey:"d3d3d3",lightgreen:"90ee90",lightpink:"ffb6c1",lightsalmon:"ffa07a",lightseagreen:"20b2aa",lightskyblue:"87cefa",lightslateblue:"8470ff",lightslategray:"778899",lightsteelblue:"b0c4de",lightyellow:"ffffe0",lime:"00ff00",limegreen:"32cd32",linen:"faf0e6",magenta:"ff00ff",maroon:"800000",mediumaquamarine:"66cdaa",mediumblue:"0000cd",mediumorchid:"ba55d3",mediumpurple:"9370d8",mediumseagreen:"3cb371",mediumslateblue:"7b68ee",mediumspringgreen:"00fa9a",mediumturquoise:"48d1cc",mediumvioletred:"c71585",midnightblue:"191970",mintcream:"f5fffa",mistyrose:"ffe4e1",moccasin:"ffe4b5",navajowhite:"ffdead",navy:"000080",oldlace:"fdf5e6",olive:"808000",olivedrab:"6b8e23",orange:"ffa500",orangered:"ff4500",orchid:"da70d6",palegoldenrod:"eee8aa",palegreen:"98fb98",paleturquoise:"afeeee",palevioletred:"d87093",papayawhip:"ffefd5",peachpuff:"ffdab9",peru:"cd853f",pink:"ffc0cb",plum:"dda0dd",powderblue:"b0e0e6",purple:"800080",red:"ff0000",rosybrown:"bc8f8f",royalblue:"4169e1",saddlebrown:"8b4513",salmon:"fa8072",sandybrown:"f4a460",seagreen:"2e8b57",seashell:"fff5ee",sienna:"a0522d",silver:"c0c0c0",skyblue:"87ceeb",slateblue:"6a5acd",slategray:"708090",snow:"fffafa",springgreen:"00ff7f",steelblue:"4682b4",tan:"d2b48c",teal:"008080",thistle:"d8bfd8",tomato:"ff6347",turquoise:"40e0d0",violet:"ee82ee",violetred:"d02090",wheat:"f5deb3",white:"ffffff",whitesmoke:"f5f5f5",yellow:"ffff00",yellowgreen:"9acd32"})[tn=(tn=tn.replace(/ /g,"")).toLowerCase()]||tn;for(var ta=[{re:/^rgb\((\d{1,3}),\s*(\d{1,3}),\s*(\d{1,3})\)$/,example:["rgb(123, 234, 45)","rgb(255,234,245)"],process:function(tn){return[parseInt(tn[1]),parseInt(tn[2]),parseInt(tn[3])]}},{re:/^(\w{2})(\w{2})(\w{2})$/,example:["#00ff00","336699"],process:function(tn){return[parseInt(tn[1],16),parseInt(tn[2],16),parseInt(tn[3],16)]}},{re:/^(\w{1})(\w{1})(\w{1})$/,example:["#fb0","f0f"],process:function(tn){return[parseInt(tn[1]+tn[1],16),parseInt(tn[2]+tn[2],16),parseInt(tn[3]+tn[3],16)]}}],to=0;to<ta.length;to++){var ts=ta[to].re,tc=ta[to].process,tu=ts.exec(tn);tu&&(ti=tc(tu),this.r=ti[0],this.g=ti[1],this.b=ti[2],this.ok=!0)}this.r=this.r<0||isNaN(this.r)?0:this.r>255?255:this.r,this.g=this.g<0||isNaN(this.g)?0:this.g>255?255:this.g,this.b=this.b<0||isNaN(this.b)?0:this.b>255?255:this.b,this.toRGB=function(){return"rgb("+this.r+", "+this.g+", "+this.b+")"},this.toHex=function(){var tn=this.r.toString(16),ti=this.g.toString(16),ta=this.b.toString(16);return 1==tn.length&&(tn="0"+tn),1==ti.length&&(ti="0"+ti),1==ta.length&&(ta="0"+ta),"#"+tn+ti+ta}}/**
 * @license
 * Joseph Myers does not specify a particular license for his work.
 *
 * Author: Joseph Myers
 * Accessed from: http://www.myersdaily.org/joseph/javascript/md5.js
 *
 * Modified by: Owen Leong
 */function d(tn,ti){var ta=tn[0],to=tn[1],ts=tn[2],tc=tn[3];ta=g(ta,to,ts,tc,ti[0],7,-680876936),tc=g(tc,ta,to,ts,ti[1],12,-389564586),ts=g(ts,tc,ta,to,ti[2],17,606105819),to=g(to,ts,tc,ta,ti[3],22,-**********),ta=g(ta,to,ts,tc,ti[4],7,-176418897),tc=g(tc,ta,to,ts,ti[5],12,**********),ts=g(ts,tc,ta,to,ti[6],17,-**********),to=g(to,ts,tc,ta,ti[7],22,-45705983),ta=g(ta,to,ts,tc,ti[8],7,**********),tc=g(tc,ta,to,ts,ti[9],12,-**********),ts=g(ts,tc,ta,to,ti[10],17,-42063),to=g(to,ts,tc,ta,ti[11],22,-**********),ta=g(ta,to,ts,tc,ti[12],7,**********),tc=g(tc,ta,to,ts,ti[13],12,-40341101),ts=g(ts,tc,ta,to,ti[14],17,-**********),ta=m(ta,to=g(to,ts,tc,ta,ti[15],22,**********),ts,tc,ti[1],5,-165796510),tc=m(tc,ta,to,ts,ti[6],9,-**********),ts=m(ts,tc,ta,to,ti[11],14,643717713),to=m(to,ts,tc,ta,ti[0],20,-373897302),ta=m(ta,to,ts,tc,ti[5],5,-701558691),tc=m(tc,ta,to,ts,ti[10],9,38016083),ts=m(ts,tc,ta,to,ti[15],14,-660478335),to=m(to,ts,tc,ta,ti[4],20,-405537848),ta=m(ta,to,ts,tc,ti[9],5,568446438),tc=m(tc,ta,to,ts,ti[14],9,-1019803690),ts=m(ts,tc,ta,to,ti[3],14,-187363961),to=m(to,ts,tc,ta,ti[8],20,1163531501),ta=m(ta,to,ts,tc,ti[13],5,-1444681467),tc=m(tc,ta,to,ts,ti[2],9,-51403784),ts=m(ts,tc,ta,to,ti[7],14,1735328473),ta=v(ta,to=m(to,ts,tc,ta,ti[12],20,-1926607734),ts,tc,ti[5],4,-378558),tc=v(tc,ta,to,ts,ti[8],11,-2022574463),ts=v(ts,tc,ta,to,ti[11],16,1839030562),to=v(to,ts,tc,ta,ti[14],23,-35309556),ta=v(ta,to,ts,tc,ti[1],4,-1530992060),tc=v(tc,ta,to,ts,ti[4],11,1272893353),ts=v(ts,tc,ta,to,ti[7],16,-155497632),to=v(to,ts,tc,ta,ti[10],23,-1094730640),ta=v(ta,to,ts,tc,ti[13],4,681279174),tc=v(tc,ta,to,ts,ti[0],11,-358537222),ts=v(ts,tc,ta,to,ti[3],16,-722521979),to=v(to,ts,tc,ta,ti[6],23,76029189),ta=v(ta,to,ts,tc,ti[9],4,-640364487),tc=v(tc,ta,to,ts,ti[12],11,-421815835),ts=v(ts,tc,ta,to,ti[15],16,530742520),ta=b(ta,to=v(to,ts,tc,ta,ti[2],23,-995338651),ts,tc,ti[0],6,-198630844),tc=b(tc,ta,to,ts,ti[7],10,1126891415),ts=b(ts,tc,ta,to,ti[14],15,-1416354905),to=b(to,ts,tc,ta,ti[5],21,-57434055),ta=b(ta,to,ts,tc,ti[12],6,1700485571),tc=b(tc,ta,to,ts,ti[3],10,-1894986606),ts=b(ts,tc,ta,to,ti[10],15,-1051523),to=b(to,ts,tc,ta,ti[1],21,-2054922799),ta=b(ta,to,ts,tc,ti[8],6,1873313359),tc=b(tc,ta,to,ts,ti[15],10,-30611744),ts=b(ts,tc,ta,to,ti[6],15,-1560198380),to=b(to,ts,tc,ta,ti[13],21,1309151649),ta=b(ta,to,ts,tc,ti[4],6,-145523070),tc=b(tc,ta,to,ts,ti[11],10,-1120210379),ts=b(ts,tc,ta,to,ti[2],15,718787259),to=b(to,ts,tc,ta,ti[9],21,-343485551),tn[0]=_(ta,tn[0]),tn[1]=_(to,tn[1]),tn[2]=_(ts,tn[2]),tn[3]=_(tc,tn[3])}function p(tn,ti,ta,to,ts,tc){return ti=_(_(ti,tn),_(to,tc)),_(ti<<ts|ti>>>32-ts,ta)}function g(tn,ti,ta,to,ts,tc,tu){return p(ti&ta|~ti&to,tn,ti,ts,tc,tu)}function m(tn,ti,ta,to,ts,tc,tu){return p(ti&to|ta&~to,tn,ti,ts,tc,tu)}function v(tn,ti,ta,to,ts,tc,tu){return p(ti^ta^to,tn,ti,ts,tc,tu)}function b(tn,ti,ta,to,ts,tc,tu){return p(ta^(ti|~to),tn,ti,ts,tc,tu)}function y(tn){var ti,ta=tn.length,to=[1732584193,-271733879,-1732584194,271733878];for(ti=64;ti<=tn.length;ti+=64)d(to,function(tn){var ti,ta=[];for(ti=0;ti<64;ti+=4)ta[ti>>2]=tn.charCodeAt(ti)+(tn.charCodeAt(ti+1)<<8)+(tn.charCodeAt(ti+2)<<16)+(tn.charCodeAt(ti+3)<<24);return ta}(tn.substring(ti-64,ti)));tn=tn.substring(ti-64);var ts=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0];for(ti=0;ti<tn.length;ti++)ts[ti>>2]|=tn.charCodeAt(ti)<<(ti%4<<3);if(ts[ti>>2]|=128<<(ti%4<<3),ti>55)for(d(to,ts),ti=0;ti<16;ti++)ts[ti]=0;return ts[14]=8*ta,d(to,ts),to}th=tc.atob.bind(tc),tl=tc.btoa.bind(tc);var td="0123456789abcdef".split("");function A(tn){return String.fromCharCode((255&tn)>>0,(65280&tn)>>8,(16711680&tn)>>16,(**********&tn)>>24)}function x(tn){return y(tn).map(A).join("")}var tp="5d41402abc4b2a76b9719d911017c592"!=function(tn){for(var ti=0;ti<tn.length;ti++)tn[ti]=function(tn){for(var ti="",ta=0;ta<4;ta++)ti+=td[tn>>8*ta+4&15]+td[tn>>8*ta&15];return ti}(tn[ti]);return tn.join("")}(y("hello"));function _(tn,ti){if(tp){var ta=(65535&tn)+(65535&ti);return(tn>>16)+(ti>>16)+(ta>>16)<<16|65535&ta}return tn+ti&**********}/**
 * @license
 * FPDF is released under a permissive license: there is no usage restriction.
 * You may embed it freely in your application (commercial or not), with or
 * without modifications.
 *
 * Reference: http://www.fpdf.org/en/script/script37.php
 */function P(tn,ti){if(tn!==ta){for(var ta,to,ts=Array(1+(256/tn.length>>0)+1).join(tn),tc=[],tu=0;tu<256;tu++)tc[tu]=tu;var th=0;for(tu=0;tu<256;tu++){var tl=tc[tu];th=(th+tl+ts.charCodeAt(tu))%256,tc[tu]=tc[th],tc[th]=tl}ta=tn,to=tc}else tc=to;var tf=ti.length,td=0,tp=0,tg="";for(tu=0;tu<tf;tu++)tp=(tp+(tl=tc[td=(td+1)%256]))%256,tc[td]=tc[tp],tc[tp]=tl,ts=tc[(tc[td]+tc[tp])%256],tg+=String.fromCharCode(ti.charCodeAt(tu)^ts);return tg}/**
 * @license
 * Licensed under the MIT License.
 * http://opensource.org/licenses/mit-license
 * Author: Owen Leong (@owenl131)
 * Date: 15 Oct 2020
 * References:
 * https://www.cs.cmu.edu/~dst/Adobe/Gallery/anon21jul01-pdf-encryption.txt
 * https://github.com/foliojs/pdfkit/blob/master/lib/security.js
 * http://www.fpdf.org/en/script/script37.php
 */var tg={print:4,modify:8,copy:16,"annot-forms":32};function I(tn,ti,ta,to){this.v=1,this.r=2;var ts=192;tn.forEach(function(tn){if(void 0!==tg.perm)throw Error("Invalid permission: "+tn);ts+=tg[tn]}),this.padding="(\xbfN^Nu\x8aAd\x00NV\xff\xfa\x01\b..\x00\xb6\xd0h>\x80/\f\xa9\xfedSiz";var tc=(ti+this.padding).substr(0,32),tu=(ta+this.padding).substr(0,32);this.O=this.processOwnerPassword(tc,tu),this.P=-(1+(255^ts)),this.encryptionKey=x(tc+this.O+this.lsbFirstWord(this.P)+this.hexToBytes(to)).substr(0,5),this.U=P(this.encryptionKey,this.padding)}function F(tn){if(/[^\u0000-\u00ff]/.test(tn))throw Error("Invalid PDF Name Object: "+tn+", Only accept ASCII characters.");for(var ti="",ta=tn.length,to=0;to<ta;to++){var ts=tn.charCodeAt(to);ts<33||35===ts||37===ts||40===ts||41===ts||47===ts||60===ts||62===ts||91===ts||93===ts||123===ts||125===ts||ts>126?ti+="#"+("0"+ts.toString(16)).slice(-2):ti+=tn[to]}return ti}function C(tn){if("object"!==(0,to.Z)(tn))throw Error("Invalid Context passed to initialize PubSub (jsPDF-module)");var ti={};this.subscribe=function(tn,ta,to){if(to=to||!1,"string"!=typeof tn||"function"!=typeof ta||"boolean"!=typeof to)throw Error("Invalid arguments passed to PubSub.subscribe (jsPDF-module)");ti.hasOwnProperty(tn)||(ti[tn]={});var ts=Math.random().toString(35);return ti[tn][ts]=[ta,!!to],ts},this.unsubscribe=function(tn){for(var ta in ti)if(ti[ta][tn])return delete ti[ta][tn],0===Object.keys(ti[ta]).length&&delete ti[ta],!0;return!1},this.publish=function(ta){if(ti.hasOwnProperty(ta)){var to=Array.prototype.slice.call(arguments,1),ts=[];for(var th in ti[ta]){var tl=ti[ta][th];try{tl[0].apply(tn,to)}catch(tn){tc.console&&tu.error("jsPDF PubSub Error",tn.message,tn)}tl[1]&&ts.push(th)}ts.length&&ts.forEach(this.unsubscribe)}},this.getTopics=function(){return ti}}function j(tn){if(!(this instanceof j))return new j(tn);var ti="opacity,stroke-opacity".split(",");for(var ta in tn)tn.hasOwnProperty(ta)&&ti.indexOf(ta)>=0&&(this[ta]=tn[ta]);this.id="",this.objectNumber=-1}function O(tn,ti){this.gState=tn,this.matrix=ti,this.id="",this.objectNumber=-1}function B(tn,ti,ta,to,ts){if(!(this instanceof B))return new B(tn,ti,ta,to,ts);this.type="axial"===tn?2:3,this.coords=ti,this.colors=ta,O.call(this,to,ts)}function M(tn,ti,ta,to,ts){if(!(this instanceof M))return new M(tn,ti,ta,to,ts);this.boundingBox=tn,this.xStep=ti,this.yStep=ta,this.stream="",this.cloneIndex=0,O.call(this,to,ts)}function E(tn){var ti,ta="string"==typeof arguments[0]?arguments[0]:"p",ts=arguments[1],th=arguments[2],td=arguments[3],tp=[],tg=1,tm=16,tv="S",tb=null;"object"===(0,to.Z)(tn=tn||{})&&(ta=tn.orientation,ts=tn.unit||ts,th=tn.format||th,td=tn.compress||tn.compressPdf||td,null!==(tb=tn.encryption||null)&&(tb.userPassword=tb.userPassword||"",tb.ownerPassword=tb.ownerPassword||"",tb.userPermissions=tb.userPermissions||[]),tg="number"==typeof tn.userUnit?Math.abs(tn.userUnit):1,void 0!==tn.precision&&(ti=tn.precision),void 0!==tn.floatPrecision&&(tm=tn.floatPrecision),tv=tn.defaultPathOperation||"S"),tp=tn.filters||(!0===td?["FlateEncode"]:tp),ts=ts||"mm",ta=(""+(ta||"P")).toLowerCase();var ty=tn.putOnlyUsedFonts||!1,tw={},tN={internal:{},__private__:{}};tN.__private__.PubSub=C;var tL="1.3",tx=tN.__private__.getPdfVersion=function(){return tL};tN.__private__.setPdfVersion=function(tn){tL=tn};var tA={a0:[2383.94,3370.39],a1:[1683.78,2383.94],a2:[1190.55,1683.78],a3:[841.89,1190.55],a4:[595.28,841.89],a5:[419.53,595.28],a6:[297.64,419.53],a7:[209.76,297.64],a8:[147.4,209.76],a9:[104.88,147.4],a10:[73.7,104.88],b0:[2834.65,4008.19],b1:[2004.09,2834.65],b2:[1417.32,2004.09],b3:[1000.63,1417.32],b4:[708.66,1000.63],b5:[498.9,708.66],b6:[354.33,498.9],b7:[249.45,354.33],b8:[175.75,249.45],b9:[124.72,175.75],b10:[87.87,124.72],c0:[2599.37,3676.54],c1:[1836.85,2599.37],c2:[1298.27,1836.85],c3:[918.43,1298.27],c4:[649.13,918.43],c5:[459.21,649.13],c6:[323.15,459.21],c7:[229.61,323.15],c8:[161.57,229.61],c9:[113.39,161.57],c10:[79.37,113.39],dl:[311.81,623.62],letter:[612,792],"government-letter":[576,756],legal:[612,1008],"junior-legal":[576,360],ledger:[1224,792],tabloid:[792,1224],"credit-card":[153,243]};tN.__private__.getPageFormats=function(){return tA};var tS=tN.__private__.getPageFormat=function(tn){return tA[tn]};th=th||"a4";var t_={COMPAT:"compat",ADVANCED:"advanced"},tP=t_.COMPAT;function _(){this.saveGraphicsState(),tQ(new Vt(es,0,0,-es,0,rA()*es).toString()+" cm"),this.setFontSize(this.getFontSize()/es),tv="n",tP=t_.ADVANCED}function P(){this.restoreGraphicsState(),tv="S",tP=t_.COMPAT}var tk=tN.__private__.combineFontStyleAndFontWeight=function(tn,ti){if("bold"==tn&&"normal"==ti||"bold"==tn&&400==ti||"normal"==tn&&"italic"==ti||"bold"==tn&&"italic"==ti)throw Error("Invalid Combination of fontweight and fontstyle");return ti&&(tn=400==ti||"normal"===ti?"italic"===tn?"italic":"normal":700!=ti&&"bold"!==ti||"normal"!==tn?(700==ti?"bold":ti)+""+tn:"bold"),tn};tN.advancedAPI=function(tn){var ti=tP===t_.COMPAT;return ti&&_.call(this),"function"!=typeof tn||(tn(this),ti&&P.call(this)),this},tN.compatAPI=function(tn){var ti=tP===t_.ADVANCED;return ti&&P.call(this),"function"!=typeof tn||(tn(this),ti&&_.call(this)),this},tN.isAdvancedAPI=function(){return tP===t_.ADVANCED};var tF,q=function(tn){if(tP!==t_.ADVANCED)throw Error(tn+" is only available in 'advanced' API mode. You need to call advancedAPI() first.")},tI=tN.roundToPrecision=tN.__private__.roundToPrecision=function(tn,ta){var to=ti||ta;if(isNaN(tn)||isNaN(to))throw Error("Invalid argument passed to jsPDF.roundToPrecision");return tn.toFixed(to).replace(/0+$/,"")};tF=tN.hpf=tN.__private__.hpf="number"==typeof tm?function(tn){if(isNaN(tn))throw Error("Invalid argument passed to jsPDF.hpf");return tI(tn,tm)}:"smart"===tm?function(tn){if(isNaN(tn))throw Error("Invalid argument passed to jsPDF.hpf");return tI(tn,tn>-1&&tn<1?16:5)}:function(tn){if(isNaN(tn))throw Error("Invalid argument passed to jsPDF.hpf");return tI(tn,16)};var tC=tN.f2=tN.__private__.f2=function(tn){if(isNaN(tn))throw Error("Invalid argument passed to jsPDF.f2");return tI(tn,2)},tj=tN.__private__.f3=function(tn){if(isNaN(tn))throw Error("Invalid argument passed to jsPDF.f3");return tI(tn,3)},tO=tN.scale=tN.__private__.scale=function(tn){if(isNaN(tn))throw Error("Invalid argument passed to jsPDF.scale");return tP===t_.COMPAT?tn*es:tP===t_.ADVANCED?tn:void 0},H=function(tn){return tO(tP===t_.COMPAT?rA()-tn:tP===t_.ADVANCED?tn:void 0)};tN.__private__.setPrecision=tN.setPrecision=function(tn){"number"==typeof parseInt(tn,10)&&(ti=parseInt(tn,10))};var tE,tB="00000000000000000000000000000000",tM=tN.__private__.getFileId=function(){return tB},tq=tN.__private__.setFileId=function(tn){return tB=void 0!==tn&&/^[a-fA-F0-9]{32}$/.test(tn)?tn.toUpperCase():tB.split("").map(function(){return"ABCDEF0123456789".charAt(Math.floor(16*Math.random()))}).join(""),null!==tb&&(eZ=new I(tb.userPermissions,tb.userPassword,tb.ownerPassword,tB)),tB};tN.setFileId=function(tn){return tq(tn),this},tN.getFileId=function(){return tM()};var tD=tN.__private__.convertDateToPDFDate=function(tn){var ti=tn.getTimezoneOffset(),ta=[ti<0?"+":"-",tV(Math.floor(Math.abs(ti/60))),"'",tV(Math.abs(ti%60)),"'"].join("");return["D:",tn.getFullYear(),tV(tn.getMonth()+1),tV(tn.getDate()),tV(tn.getHours()),tV(tn.getMinutes()),tV(tn.getSeconds()),ta].join("")},tR=tN.__private__.convertPDFDateToDate=function(tn){var ti=parseInt(tn.substr(2,4),10),ta=parseInt(tn.substr(6,2),10)-1,to=parseInt(tn.substr(8,2),10),ts=parseInt(tn.substr(10,2),10),tc=parseInt(tn.substr(12,2),10),tu=parseInt(tn.substr(14,2),10);return new Date(ti,ta,to,ts,tc,tu,0)},tT=tN.__private__.setCreationDate=function(tn){var ti;if(void 0===tn&&(tn=new Date),tn instanceof Date)ti=tD(tn);else{if(!/^D:(20[0-2][0-9]|203[0-7]|19[7-9][0-9])(0[0-9]|1[0-2])([0-2][0-9]|3[0-1])(0[0-9]|1[0-9]|2[0-3])(0[0-9]|[1-5][0-9])(0[0-9]|[1-5][0-9])(\+0[0-9]|\+1[0-4]|-0[0-9]|-1[0-1])'(0[0-9]|[1-5][0-9])'?$/.test(tn))throw Error("Invalid argument passed to jsPDF.setCreationDate");ti=tn}return tE=ti},tU=tN.__private__.getCreationDate=function(tn){var ti=tE;return"jsDate"===tn&&(ti=tR(tE)),ti};tN.setCreationDate=function(tn){return tT(tn),this},tN.getCreationDate=function(tn){return tU(tn)};var tz,tV=tN.__private__.padd2=function(tn){return("0"+parseInt(tn)).slice(-2)},tH=tN.__private__.padd2Hex=function(tn){return("00"+(tn=tn.toString())).substr(tn.length)},tW=0,tG=[],tJ=[],tY=0,tX=[],tZ=[],tK=!1,t$=tJ,ut=function(){tW=0,tY=0,tJ=[],tG=[],tX=[],eC=eF(),ej=eF()};tN.__private__.setCustomOutputDestination=function(tn){tK=!0,t$=tn};var ht=function(tn){tK||(t$=tn)};tN.__private__.resetCustomOutputDestination=function(){tK=!1,t$=tJ};var tQ=tN.__private__.out=function(tn){return tn=tn.toString(),tY+=tn.length+1,t$.push(tn),t$},t1=tN.__private__.write=function(tn){return tQ(1==arguments.length?tn.toString():Array.prototype.join.call(arguments," "))},t2=tN.__private__.getArrayBuffer=function(tn){for(var ti=tn.length,ta=new ArrayBuffer(ti),to=new Uint8Array(ta);ti--;)to[ti]=tn.charCodeAt(ti);return ta},t5=[["Helvetica","helvetica","normal","WinAnsiEncoding"],["Helvetica-Bold","helvetica","bold","WinAnsiEncoding"],["Helvetica-Oblique","helvetica","italic","WinAnsiEncoding"],["Helvetica-BoldOblique","helvetica","bolditalic","WinAnsiEncoding"],["Courier","courier","normal","WinAnsiEncoding"],["Courier-Bold","courier","bold","WinAnsiEncoding"],["Courier-Oblique","courier","italic","WinAnsiEncoding"],["Courier-BoldOblique","courier","bolditalic","WinAnsiEncoding"],["Times-Roman","times","normal","WinAnsiEncoding"],["Times-Bold","times","bold","WinAnsiEncoding"],["Times-Italic","times","italic","WinAnsiEncoding"],["Times-BoldItalic","times","bolditalic","WinAnsiEncoding"],["ZapfDingbats","zapfdingbats","normal",null],["Symbol","symbol","normal",null]];tN.__private__.getStandardFonts=function(){return t5};var t0=tn.fontSize||16;tN.__private__.setFontSize=tN.setFontSize=function(tn){return t0=tP===t_.ADVANCED?tn/es:tn,this};var t3,t4=tN.__private__.getFontSize=tN.getFontSize=function(){return tP===t_.COMPAT?t0:t0*es},t6=tn.R2L||!1;tN.__private__.setR2L=tN.setR2L=function(tn){return t6=tn,this},tN.__private__.getR2L=tN.getR2L=function(){return t6};var t8,t7=tN.__private__.setZoomMode=function(tn){if(/^(?:\d+\.\d*|\d*\.\d+|\d+)%$/.test(tn))t3=tn;else if(isNaN(tn)){if(-1===[void 0,null,"fullwidth","fullheight","fullpage","original"].indexOf(tn))throw Error('zoom must be Integer (e.g. 2), a percentage Value (e.g. 300%) or fullwidth, fullheight, fullpage, original. "'+tn+'" is not recognized.');t3=tn}else t3=parseInt(tn,10)};tN.__private__.getZoomMode=function(){return t3};var t9,en=tN.__private__.setPageMode=function(tn){if(-1==[void 0,null,"UseNone","UseOutlines","UseThumbs","FullScreen"].indexOf(tn))throw Error('Page mode must be one of UseNone, UseOutlines, UseThumbs, or FullScreen. "'+tn+'" is not recognized.');t8=tn};tN.__private__.getPageMode=function(){return t8};var ei=tN.__private__.setLayoutMode=function(tn){if(-1==[void 0,null,"continuous","single","twoleft","tworight","two"].indexOf(tn))throw Error('Layout mode must be one of continuous, single, twoleft, tworight. "'+tn+'" is not recognized.');t9=tn};tN.__private__.getLayoutMode=function(){return t9},tN.__private__.setDisplayMode=tN.setDisplayMode=function(tn,ti,ta){return t7(tn),ei(ti),en(ta),this};var ea={title:"",subject:"",author:"",keywords:"",creator:""};tN.__private__.getDocumentProperty=function(tn){if(-1===Object.keys(ea).indexOf(tn))throw Error("Invalid argument passed to jsPDF.getDocumentProperty");return ea[tn]},tN.__private__.getDocumentProperties=function(){return ea},tN.__private__.setDocumentProperties=tN.setProperties=tN.setDocumentProperties=function(tn){for(var ti in ea)ea.hasOwnProperty(ti)&&tn[ti]&&(ea[ti]=tn[ti]);return this},tN.__private__.setDocumentProperty=function(tn,ti){if(-1===Object.keys(ea).indexOf(tn))throw Error("Invalid arguments passed to jsPDF.setDocumentProperty");return ea[tn]=ti};var eo,es,ec,eu,eh,el={},ef={},ed=[],ep={},eg={},em={},ev={},eb=null,ey=0,ew=[],eN=new C(tN),eL=tn.hotfixes||[],ex={},eA={},eS=[],Vt=function t(tn,ti,ta,to,ts,tc){if(!(this instanceof t))return new t(tn,ti,ta,to,ts,tc);isNaN(tn)&&(tn=1),isNaN(ti)&&(ti=0),isNaN(ta)&&(ta=0),isNaN(to)&&(to=1),isNaN(ts)&&(ts=0),isNaN(tc)&&(tc=0),this._matrix=[tn,ti,ta,to,ts,tc]};Object.defineProperty(Vt.prototype,"sx",{get:function(){return this._matrix[0]},set:function(tn){this._matrix[0]=tn}}),Object.defineProperty(Vt.prototype,"shy",{get:function(){return this._matrix[1]},set:function(tn){this._matrix[1]=tn}}),Object.defineProperty(Vt.prototype,"shx",{get:function(){return this._matrix[2]},set:function(tn){this._matrix[2]=tn}}),Object.defineProperty(Vt.prototype,"sy",{get:function(){return this._matrix[3]},set:function(tn){this._matrix[3]=tn}}),Object.defineProperty(Vt.prototype,"tx",{get:function(){return this._matrix[4]},set:function(tn){this._matrix[4]=tn}}),Object.defineProperty(Vt.prototype,"ty",{get:function(){return this._matrix[5]},set:function(tn){this._matrix[5]=tn}}),Object.defineProperty(Vt.prototype,"a",{get:function(){return this._matrix[0]},set:function(tn){this._matrix[0]=tn}}),Object.defineProperty(Vt.prototype,"b",{get:function(){return this._matrix[1]},set:function(tn){this._matrix[1]=tn}}),Object.defineProperty(Vt.prototype,"c",{get:function(){return this._matrix[2]},set:function(tn){this._matrix[2]=tn}}),Object.defineProperty(Vt.prototype,"d",{get:function(){return this._matrix[3]},set:function(tn){this._matrix[3]=tn}}),Object.defineProperty(Vt.prototype,"e",{get:function(){return this._matrix[4]},set:function(tn){this._matrix[4]=tn}}),Object.defineProperty(Vt.prototype,"f",{get:function(){return this._matrix[5]},set:function(tn){this._matrix[5]=tn}}),Object.defineProperty(Vt.prototype,"rotation",{get:function(){return Math.atan2(this.shx,this.sx)}}),Object.defineProperty(Vt.prototype,"scaleX",{get:function(){return this.decompose().scale.sx}}),Object.defineProperty(Vt.prototype,"scaleY",{get:function(){return this.decompose().scale.sy}}),Object.defineProperty(Vt.prototype,"isIdentity",{get:function(){return 1===this.sx&&0===this.shy&&0===this.shx&&1===this.sy&&0===this.tx&&0===this.ty}}),Vt.prototype.join=function(tn){return[this.sx,this.shy,this.shx,this.sy,this.tx,this.ty].map(tF).join(tn)},Vt.prototype.multiply=function(tn){var ti=tn.sx*this.sx+tn.shy*this.shx,ta=tn.sx*this.shy+tn.shy*this.sy,to=tn.shx*this.sx+tn.sy*this.shx,ts=tn.shx*this.shy+tn.sy*this.sy,tc=tn.tx*this.sx+tn.ty*this.shx+this.tx,tu=tn.tx*this.shy+tn.ty*this.sy+this.ty;return new Vt(ti,ta,to,ts,tc,tu)},Vt.prototype.decompose=function(){var tn=this.sx,ti=this.shy,ta=this.shx,to=this.sy,ts=this.tx,tc=this.ty,tu=Math.sqrt(tn*tn+ti*ti),th=(tn/=tu)*ta+(ti/=tu)*to,tl=Math.sqrt((ta-=tn*th)*ta+(to-=ti*th)*to);return th/=tl,tn*(to/=tl)<ti*(ta/=tl)&&(tn=-tn,ti=-ti,th=-th,tu=-tu),{scale:new Vt(tu,0,0,tl,0,0),translate:new Vt(1,0,0,1,ts,tc),rotate:new Vt(tn,ti,-ti,tn,0,0),skew:new Vt(1,0,th,1,0,0)}},Vt.prototype.toString=function(tn){return this.join(" ")},Vt.prototype.inversed=function(){var tn=this.sx,ti=this.shy,ta=this.shx,to=this.sy,ts=this.tx,tc=this.ty,tu=1/(tn*to-ti*ta),th=to*tu,tl=-ti*tu,tf=-ta*tu,td=tn*tu;return new Vt(th,tl,tf,td,-th*ts-tf*tc,-tl*ts-td*tc)},Vt.prototype.applyToPoint=function(tn){var ti=tn.x*this.sx+tn.y*this.shx+this.tx,ta=tn.x*this.shy+tn.y*this.sy+this.ty;return new Cr(ti,ta)},Vt.prototype.applyToRectangle=function(tn){var ti=this.applyToPoint(tn),ta=this.applyToPoint(new Cr(tn.x+tn.w,tn.y+tn.h));return new jr(ti.x,ti.y,ta.x-ti.x,ta.y-ti.y)},Vt.prototype.clone=function(){var tn=this.sx,ti=this.shy,ta=this.shx,to=this.sy,ts=this.tx,tc=this.ty;return new Vt(tn,ti,ta,to,ts,tc)},tN.Matrix=Vt;var e_=tN.matrixMult=function(tn,ti){return ti.multiply(tn)},eP=new Vt(1,0,0,1,0,0);tN.unitMatrix=tN.identityMatrix=eP;var Jt=function(tn,ti){if(!eg[tn]){var ta=(ti instanceof B?"Sh":"P")+(Object.keys(ep).length+1).toString(10);ti.id=ta,eg[tn]=ta,ep[ta]=ti,eN.publish("addPattern",ti)}};tN.ShadingPattern=B,tN.TilingPattern=M,tN.addShadingPattern=function(tn,ti){return q("addShadingPattern()"),Jt(tn,ti),this},tN.beginTilingPattern=function(tn){q("beginTilingPattern()"),Br(tn.boundingBox[0],tn.boundingBox[1],tn.boundingBox[2]-tn.boundingBox[0],tn.boundingBox[3]-tn.boundingBox[1],tn.matrix)},tN.endTilingPattern=function(tn,ti){q("endTilingPattern()"),ti.stream=tZ[tz].join("\n"),Jt(tn,ti),eN.publish("endTilingPattern",ti),eS.pop().restore()};var ek=tN.__private__.newObject=function(){var tn=eF();return Zt(tn,!0),tn},eF=tN.__private__.newObjectDeferred=function(){return tG[++tW]=function(){return tY},tW},Zt=function(tn,ti){return ti="boolean"==typeof ti&&ti,tG[tn]=tY,ti&&tQ(tn+" 0 obj"),tn},eI=tN.__private__.newAdditionalObject=function(){var tn={objId:eF(),content:""};return tX.push(tn),tn},eC=eF(),ej=eF(),eO=tN.__private__.decodeColorString=function(tn){var ti=tn.split(" ");if(2!==ti.length||"g"!==ti[1]&&"G"!==ti[1])5===ti.length&&("k"===ti[4]||"K"===ti[4])&&(ti=[(1-ti[0])*(1-ti[3]),(1-ti[1])*(1-ti[3]),(1-ti[2])*(1-ti[3]),"r"]);else{var ta=parseFloat(ti[0]);ti=[ta,ta,ta,"r"]}for(var to="#",ts=0;ts<3;ts++)to+=("0"+Math.floor(255*parseFloat(ti[ts])).toString(16)).slice(-2);return to},eE=tN.__private__.encodeColorString=function(tn){"string"==typeof tn&&(tn={ch1:tn});var ti,ta=tn.ch1,ts=tn.ch2,tc=tn.ch3,tu=tn.ch4,th="draw"===tn.pdfColorType?["G","RG","K"]:["g","rg","k"];if("string"==typeof ta&&"#"!==ta.charAt(0)){var tl=new f(ta);if(tl.ok)ta=tl.toHex();else if(!/^\d*\.?\d*$/.test(ta))throw Error('Invalid color "'+ta+'" passed to jsPDF.encodeColorString.')}if("string"==typeof ta&&/^#[0-9A-Fa-f]{3}$/.test(ta)&&(ta="#"+ta[1]+ta[1]+ta[2]+ta[2]+ta[3]+ta[3]),"string"==typeof ta&&/^#[0-9A-Fa-f]{6}$/.test(ta)){var tf=parseInt(ta.substr(1),16);ta=tf>>16&255,ts=tf>>8&255,tc=255&tf}if(void 0===ts||void 0===tu&&ta===ts&&ts===tc)ti="string"==typeof ta?ta+" "+th[0]:2===tn.precision?tC(ta/255)+" "+th[0]:tj(ta/255)+" "+th[0];else if(void 0===tu||"object"===(0,to.Z)(tu)){if(tu&&!isNaN(tu.a)&&0===tu.a)return["1.","1.","1.",th[1]].join(" ");ti="string"==typeof ta?[ta,ts,tc,th[1]].join(" "):2===tn.precision?[tC(ta/255),tC(ts/255),tC(tc/255),th[1]].join(" "):[tj(ta/255),tj(ts/255),tj(tc/255),th[1]].join(" ")}else ti="string"==typeof ta?[ta,ts,tc,tu,th[2]].join(" "):2===tn.precision?[tC(ta),tC(ts),tC(tc),tC(tu),th[2]].join(" "):[tj(ta),tj(ts),tj(tc),tj(tu),th[2]].join(" ");return ti},eB=tN.__private__.getFilters=function(){return tp},eM=tN.__private__.putStream=function(tn){var ti=(tn=tn||{}).data||"",ta=tn.filters||eB(),to=tn.alreadyAppliedFilters||[],ts=tn.addLength1||!1,tc=ti.length,tu=tn.objectId,s=function(tn){return tn};if(null!==tb&&void 0===tu)throw Error("ObjectId must be passed to putStream for file encryption");null!==tb&&(s=eZ.encryptor(tu,0));var th={};!0===ta&&(ta=["FlateEncode"]);var tl=tn.additionalKeyValues||[],tf=(th=void 0!==E.API.processDataByFilters?E.API.processDataByFilters(ti,ta):{data:ti,reverseChain:[]}).reverseChain+(Array.isArray(to)?to.join(" "):to.toString());if(0!==th.data.length&&(tl.push({key:"Length",value:th.data.length}),!0===ts&&tl.push({key:"Length1",value:tc})),0!=tf.length){if(tf.split("/").length-1==1)tl.push({key:"Filter",value:tf});else{tl.push({key:"Filter",value:"["+tf+"]"});for(var td=0;td<tl.length;td+=1)if("DecodeParms"===tl[td].key){for(var tp=[],tg=0;tg<th.reverseChain.split("/").length-1;tg+=1)tp.push("null");tp.push(tl[td].value),tl[td].value="["+tp.join(" ")+"]"}}}tQ("<<");for(var tm=0;tm<tl.length;tm++)tQ("/"+tl[tm].key+" "+tl[tm].value);tQ(">>"),0!==th.data.length&&(tQ("stream"),tQ(s(th.data)),tQ("endstream"))},eq=tN.__private__.putPage=function(tn){var ti=tn.number,ta=tn.data,to=tn.objId,ts=tn.contentsObjId;Zt(to,!0),tQ("<</Type /Page"),tQ("/Parent "+tn.rootDictionaryObjId+" 0 R"),tQ("/Resources "+tn.resourceDictionaryObjId+" 0 R"),tQ("/MediaBox ["+parseFloat(tF(tn.mediaBox.bottomLeftX))+" "+parseFloat(tF(tn.mediaBox.bottomLeftY))+" "+tF(tn.mediaBox.topRightX)+" "+tF(tn.mediaBox.topRightY)+"]"),null!==tn.cropBox&&tQ("/CropBox ["+tF(tn.cropBox.bottomLeftX)+" "+tF(tn.cropBox.bottomLeftY)+" "+tF(tn.cropBox.topRightX)+" "+tF(tn.cropBox.topRightY)+"]"),null!==tn.bleedBox&&tQ("/BleedBox ["+tF(tn.bleedBox.bottomLeftX)+" "+tF(tn.bleedBox.bottomLeftY)+" "+tF(tn.bleedBox.topRightX)+" "+tF(tn.bleedBox.topRightY)+"]"),null!==tn.trimBox&&tQ("/TrimBox ["+tF(tn.trimBox.bottomLeftX)+" "+tF(tn.trimBox.bottomLeftY)+" "+tF(tn.trimBox.topRightX)+" "+tF(tn.trimBox.topRightY)+"]"),null!==tn.artBox&&tQ("/ArtBox ["+tF(tn.artBox.bottomLeftX)+" "+tF(tn.artBox.bottomLeftY)+" "+tF(tn.artBox.topRightX)+" "+tF(tn.artBox.topRightY)+"]"),"number"==typeof tn.userUnit&&1!==tn.userUnit&&tQ("/UserUnit "+tn.userUnit),eN.publish("putPage",{objId:to,pageContext:ew[ti],pageNumber:ti,page:ta}),tQ("/Contents "+ts+" 0 R"),tQ(">>"),tQ("endobj");var tc=ta.join("\n");return tP===t_.ADVANCED&&(tc+="\nQ"),Zt(ts,!0),eM({data:tc,filters:eB(),objectId:ts}),tQ("endobj"),to},eD=tN.__private__.putPages=function(){var tn,ti,ta=[];for(tn=1;tn<=ey;tn++)ew[tn].objId=eF(),ew[tn].contentsObjId=eF();for(tn=1;tn<=ey;tn++)ta.push(eq({number:tn,data:tZ[tn],objId:ew[tn].objId,contentsObjId:ew[tn].contentsObjId,mediaBox:ew[tn].mediaBox,cropBox:ew[tn].cropBox,bleedBox:ew[tn].bleedBox,trimBox:ew[tn].trimBox,artBox:ew[tn].artBox,userUnit:ew[tn].userUnit,rootDictionaryObjId:eC,resourceDictionaryObjId:ej}));Zt(eC,!0),tQ("<</Type /Pages");var to="/Kids [";for(ti=0;ti<ey;ti++)to+=ta[ti]+" 0 R ";tQ(to+"]"),tQ("/Count "+ey),tQ(">>"),tQ("endobj"),eN.publish("postPutPages")},se=function(tn){eN.publish("putFont",{font:tn,out:tQ,newObject:ek,putStream:eM}),!0!==tn.isAlreadyPutted&&(tn.objectNumber=ek(),tQ("<<"),tQ("/Type /Font"),tQ("/BaseFont /"+F(tn.postScriptName)),tQ("/Subtype /Type1"),"string"==typeof tn.encoding&&tQ("/Encoding /"+tn.encoding),tQ("/FirstChar 32"),tQ("/LastChar 255"),tQ(">>"),tQ("endobj"))},ce=function(){for(var tn in el)el.hasOwnProperty(tn)&&(!1===ty||!0===ty&&tw.hasOwnProperty(tn))&&se(el[tn])},ue=function(tn){tn.objectNumber=ek();var ti=[];ti.push({key:"Type",value:"/XObject"}),ti.push({key:"Subtype",value:"/Form"}),ti.push({key:"BBox",value:"["+[tF(tn.x),tF(tn.y),tF(tn.x+tn.width),tF(tn.y+tn.height)].join(" ")+"]"}),ti.push({key:"Matrix",value:"["+tn.matrix.toString()+"]"}),eM({data:tn.pages[1].join("\n"),additionalKeyValues:ti,objectId:tn.objectNumber}),tQ("endobj")},he=function(){for(var tn in ex)ex.hasOwnProperty(tn)&&ue(ex[tn])},le=function(tn,ti){var ta,to=[],ts=1/(ti-1);for(ta=0;ta<1;ta+=ts)to.push(ta);if(to.push(1),0!=tn[0].offset){var tc={offset:0,color:tn[0].color};tn.unshift(tc)}if(1!=tn[tn.length-1].offset){var tu={offset:1,color:tn[tn.length-1].color};tn.push(tu)}for(var th="",tl=0,tf=0;tf<to.length;tf++){for(ta=to[tf];ta>tn[tl+1].offset;)tl++;var td=tn[tl].offset,tp=(ta-td)/(tn[tl+1].offset-td),tg=tn[tl].color,tm=tn[tl+1].color;th+=tH(Math.round((1-tp)*tg[0]+tp*tm[0]).toString(16))+tH(Math.round((1-tp)*tg[1]+tp*tm[1]).toString(16))+tH(Math.round((1-tp)*tg[2]+tp*tm[2]).toString(16))}return th.trim()},fe=function(tn,ti){ti||(ti=21);var ta=ek(),to=le(tn.colors,ti),ts=[];ts.push({key:"FunctionType",value:"0"}),ts.push({key:"Domain",value:"[0.0 1.0]"}),ts.push({key:"Size",value:"["+ti+"]"}),ts.push({key:"BitsPerSample",value:"8"}),ts.push({key:"Range",value:"[0.0 1.0 0.0 1.0 0.0 1.0]"}),ts.push({key:"Decode",value:"[0.0 1.0 0.0 1.0 0.0 1.0]"}),eM({data:to,additionalKeyValues:ts,alreadyAppliedFilters:["/ASCIIHexDecode"],objectId:ta}),tQ("endobj"),tn.objectNumber=ek(),tQ("<< /ShadingType "+tn.type),tQ("/ColorSpace /DeviceRGB");var tc="/Coords ["+tF(parseFloat(tn.coords[0]))+" "+tF(parseFloat(tn.coords[1]))+" ";2===tn.type?tc+=tF(parseFloat(tn.coords[2]))+" "+tF(parseFloat(tn.coords[3])):tc+=tF(parseFloat(tn.coords[2]))+" "+tF(parseFloat(tn.coords[3]))+" "+tF(parseFloat(tn.coords[4]))+" "+tF(parseFloat(tn.coords[5])),tQ(tc+="]"),tn.matrix&&tQ("/Matrix ["+tn.matrix.toString()+"]"),tQ("/Function "+ta+" 0 R"),tQ("/Extend [true true]"),tQ(">>"),tQ("endobj")},de=function(tn,ti){var ta=eF(),to=ek();ti.push({resourcesOid:ta,objectOid:to}),tn.objectNumber=to;var ts=[];ts.push({key:"Type",value:"/Pattern"}),ts.push({key:"PatternType",value:"1"}),ts.push({key:"PaintType",value:"1"}),ts.push({key:"TilingType",value:"1"}),ts.push({key:"BBox",value:"["+tn.boundingBox.map(tF).join(" ")+"]"}),ts.push({key:"XStep",value:tF(tn.xStep)}),ts.push({key:"YStep",value:tF(tn.yStep)}),ts.push({key:"Resources",value:ta+" 0 R"}),tn.matrix&&ts.push({key:"Matrix",value:"["+tn.matrix.toString()+"]"}),eM({data:tn.stream,additionalKeyValues:ts,objectId:tn.objectNumber}),tQ("endobj")},pe=function(tn){var ti;for(ti in ep)ep.hasOwnProperty(ti)&&(ep[ti]instanceof B?fe(ep[ti]):ep[ti]instanceof M&&de(ep[ti],tn))},ge=function(tn){for(var ti in tn.objectNumber=ek(),tQ("<<"),tn)switch(ti){case"opacity":tQ("/ca "+tC(tn[ti]));break;case"stroke-opacity":tQ("/CA "+tC(tn[ti]))}tQ(">>"),tQ("endobj")},me=function(){var tn;for(tn in em)em.hasOwnProperty(tn)&&ge(em[tn])},ve=function(){for(var tn in tQ("/XObject <<"),ex)ex.hasOwnProperty(tn)&&ex[tn].objectNumber>=0&&tQ("/"+tn+" "+ex[tn].objectNumber+" 0 R");eN.publish("putXobjectDict"),tQ(">>")},be=function(){eZ.oid=ek(),tQ("<<"),tQ("/Filter /Standard"),tQ("/V "+eZ.v),tQ("/R "+eZ.r),tQ("/U <"+eZ.toHexString(eZ.U)+">"),tQ("/O <"+eZ.toHexString(eZ.O)+">"),tQ("/P "+eZ.P),tQ(">>"),tQ("endobj")},ye=function(){for(var tn in tQ("/Font <<"),el)el.hasOwnProperty(tn)&&(!1===ty||!0===ty&&tw.hasOwnProperty(tn))&&tQ("/"+tn+" "+el[tn].objectNumber+" 0 R");tQ(">>")},we=function(){if(Object.keys(ep).length>0){for(var tn in tQ("/Shading <<"),ep)ep.hasOwnProperty(tn)&&ep[tn]instanceof B&&ep[tn].objectNumber>=0&&tQ("/"+tn+" "+ep[tn].objectNumber+" 0 R");eN.publish("putShadingPatternDict"),tQ(">>")}},Ne=function(tn){if(Object.keys(ep).length>0){for(var ti in tQ("/Pattern <<"),ep)ep.hasOwnProperty(ti)&&ep[ti]instanceof tN.TilingPattern&&ep[ti].objectNumber>=0&&ep[ti].objectNumber<tn&&tQ("/"+ti+" "+ep[ti].objectNumber+" 0 R");eN.publish("putTilingPatternDict"),tQ(">>")}},Le=function(){if(Object.keys(em).length>0){var tn;for(tn in tQ("/ExtGState <<"),em)em.hasOwnProperty(tn)&&em[tn].objectNumber>=0&&tQ("/"+tn+" "+em[tn].objectNumber+" 0 R");eN.publish("putGStateDict"),tQ(">>")}},Ae=function(tn){Zt(tn.resourcesOid,!0),tQ("<<"),tQ("/ProcSet [/PDF /Text /ImageB /ImageC /ImageI]"),ye(),we(),Ne(tn.objectOid),Le(),ve(),tQ(">>"),tQ("endobj")},xe=function(){var tn=[];ce(),me(),he(),pe(tn),eN.publish("putResources"),tn.forEach(Ae),Ae({resourcesOid:ej,objectOid:Number.MAX_SAFE_INTEGER}),eN.publish("postPutResources")},Se=function(){eN.publish("putAdditionalObjects");for(var tn=0;tn<tX.length;tn++){var ti=tX[tn];Zt(ti.objId,!0),tQ(ti.content),tQ("endobj")}eN.publish("postPutAdditionalObjects")},_e=function(tn){ef[tn.fontName]=ef[tn.fontName]||{},ef[tn.fontName][tn.fontStyle]=tn.id},Pe=function(tn,ti,ta,to,ts){var tc={id:"F"+(Object.keys(el).length+1).toString(10),postScriptName:tn,fontName:ti,fontStyle:ta,encoding:to,isStandardFont:ts||!1,metadata:{}};return eN.publish("addFont",{font:tc,instance:this}),el[tc.id]=tc,_e(tc),tc.id},Fe=function(tn,ti){var ta,to,ts,tc,tu,th,tl,tf,td;if(ts=(ti=ti||{}).sourceEncoding||"Unicode",tu=ti.outputEncoding,(ti.autoencode||tu)&&el[eo].metadata&&el[eo].metadata[ts]&&el[eo].metadata[ts].encoding&&(tc=el[eo].metadata[ts].encoding,!tu&&el[eo].encoding&&(tu=el[eo].encoding),!tu&&tc.codePages&&(tu=tc.codePages[0]),"string"==typeof tu&&(tu=tc[tu]),tu)){for(tl=!1,th=[],ta=0,to=tn.length;ta<to;ta++)(tf=tu[tn.charCodeAt(ta)])?th.push(String.fromCharCode(tf)):th.push(tn[ta]),th[ta].charCodeAt(0)>>8&&(tl=!0);tn=th.join("")}for(ta=tn.length;void 0===tl&&0!==ta;)tn.charCodeAt(ta-1)>>8&&(tl=!0),ta--;if(!tl)return tn;for(th=ti.noBOM?[]:[254,255],ta=0,to=tn.length;ta<to;ta++){if((td=(tf=tn.charCodeAt(ta))>>8)>>8)throw Error("Character at position "+ta+" of string '"+tn+"' exceeds 16bits. Cannot be encoded into UCS-2 BE");th.push(td),th.push(tf-(td<<8))}return String.fromCharCode.apply(void 0,th)},eR=tN.__private__.pdfEscape=tN.pdfEscape=function(tn,ti){return Fe(tn,ti).replace(/\\/g,"\\\\").replace(/\(/g,"\\(").replace(/\)/g,"\\)")},eT=tN.__private__.beginPage=function(tn){tZ[++ey]=[],ew[ey]={objId:0,contentsObjId:0,userUnit:Number(tg),artBox:null,bleedBox:null,cropBox:null,trimBox:null,mediaBox:{bottomLeftX:0,bottomLeftY:0,topRightX:Number(tn[0]),topRightY:Number(tn[1])}},Me(ey),ht(tZ[tz])},Oe=function(tn,ti){var to,ts,tc;switch(ta=ti||ta,"string"==typeof tn&&Array.isArray(to=tS(tn.toLowerCase()))&&(ts=to[0],tc=to[1]),Array.isArray(tn)&&(ts=tn[0]*es,tc=tn[1]*es),isNaN(ts)&&(ts=th[0],tc=th[1]),(ts>14400||tc>14400)&&(tu.warn("A page in a PDF can not be wider or taller than 14400 userUnit. jsPDF limits the width/height to 14400"),ts=Math.min(14400,ts),tc=Math.min(14400,tc)),th=[ts,tc],ta.substr(0,1)){case"l":tc>ts&&(th=[tc,ts]);break;case"p":ts>tc&&(th=[tc,ts])}eT(th),ra(rn),tQ(rd),0!==ry&&tQ(ry+" J"),0!==rw&&tQ(rw+" j"),eN.publish("addPage",{pageNumber:ey})},Be=function(tn){tn>0&&tn<=ey&&(tZ.splice(tn,1),ew.splice(tn,1),ey--,tz>ey&&(tz=ey),this.setPage(tz))},Me=function(tn){tn>0&&tn<=ey&&(tz=tn)},eU=tN.__private__.getNumberOfPages=tN.getNumberOfPages=function(){return tZ.length-1},qe=function(tn,ti,ta){var to,ts=void 0;return ta=ta||{},tn=void 0!==tn?tn:el[eo].fontName,ti=void 0!==ti?ti:el[eo].fontStyle,void 0!==ef[to=tn.toLowerCase()]&&void 0!==ef[to][ti]?ts=ef[to][ti]:void 0!==ef[tn]&&void 0!==ef[tn][ti]?ts=ef[tn][ti]:!1===ta.disableWarning&&tu.warn("Unable to look up font label for font '"+tn+"', '"+ti+"'. Refer to getFontList() for available fonts."),ts||ta.noFallback||null==(ts=ef.times[ti])&&(ts=ef.times.normal),ts},ez=tN.__private__.putInfo=function(){var tn=ek(),e=function(tn){return tn};for(var ti in null!==tb&&(e=eZ.encryptor(tn,0)),tQ("<<"),tQ("/Producer ("+eR(e("jsPDF "+E.version))+")"),ea)ea.hasOwnProperty(ti)&&ea[ti]&&tQ("/"+ti.substr(0,1).toUpperCase()+ti.substr(1)+" ("+eR(e(ea[ti]))+")");tQ("/CreationDate ("+eR(e(tE))+")"),tQ(">>"),tQ("endobj")},eV=tN.__private__.putCatalog=function(tn){var ti=(tn=tn||{}).rootDictionaryObjId||eC;switch(ek(),tQ("<<"),tQ("/Type /Catalog"),tQ("/Pages "+ti+" 0 R"),t3||(t3="fullwidth"),t3){case"fullwidth":tQ("/OpenAction [3 0 R /FitH null]");break;case"fullheight":tQ("/OpenAction [3 0 R /FitV null]");break;case"fullpage":tQ("/OpenAction [3 0 R /Fit]");break;case"original":tQ("/OpenAction [3 0 R /XYZ null null 1]");break;default:var ta=""+t3;"%"===ta.substr(ta.length-1)&&(t3=parseInt(t3)/100),"number"==typeof t3&&tQ("/OpenAction [3 0 R /XYZ null null "+tC(t3)+"]")}switch(t9||(t9="continuous"),t9){case"continuous":tQ("/PageLayout /OneColumn");break;case"single":tQ("/PageLayout /SinglePage");break;case"two":case"twoleft":tQ("/PageLayout /TwoColumnLeft");break;case"tworight":tQ("/PageLayout /TwoColumnRight")}t8&&tQ("/PageMode /"+t8),eN.publish("putCatalog"),tQ(">>"),tQ("endobj")},eH=tN.__private__.putTrailer=function(){tQ("trailer"),tQ("<<"),tQ("/Size "+(tW+1)),tQ("/Root "+tW+" 0 R"),tQ("/Info "+(tW-1)+" 0 R"),null!==tb&&tQ("/Encrypt "+eZ.oid+" 0 R"),tQ("/ID [ <"+tB+"> <"+tB+"> ]"),tQ(">>")},eW=tN.__private__.putHeader=function(){tQ("%PDF-"+tL),tQ("%\xba\xdf\xac\xe0")},eG=tN.__private__.putXRef=function(){var tn="0000000000";tQ("xref"),tQ("0 "+(tW+1)),tQ("0000000000 65535 f ");for(var ti=1;ti<=tW;ti++)"function"==typeof tG[ti]?tQ((tn+tG[ti]()).slice(-10)+" 00000 n "):void 0!==tG[ti]?tQ((tn+tG[ti]).slice(-10)+" 00000 n "):tQ("0000000000 00000 n ")},eJ=tN.__private__.buildDocument=function(){ut(),ht(tJ),eN.publish("buildDocument"),eW(),eD(),Se(),xe(),null!==tb&&be(),ez(),eV();var tn=tY;return eG(),eH(),tQ("startxref"),tQ(""+tn),tQ("%%EOF"),ht(tZ[tz]),tJ.join("\n")},eY=tN.__private__.getBlob=function(tn){return new Blob([t2(tn)],{type:"application/pdf"})},eX=tN.output=tN.__private__.output=((e7=function(tn,ti){switch("string"==typeof(ti=ti||{})?ti={filename:ti}:ti.filename=ti.filename||"generated.pdf",tn){case void 0:return eJ();case"save":tN.save(ti.filename);break;case"arraybuffer":return t2(eJ());case"blob":return eY(eJ());case"bloburi":case"bloburl":if(void 0!==tc.URL&&"function"==typeof tc.URL.createObjectURL)return tc.URL&&tc.URL.createObjectURL(eY(eJ()))||void 0;tu.warn("bloburl is not supported by your system, because URL.createObjectURL is not supported by your browser.");break;case"datauristring":case"dataurlstring":var ta="",to=eJ();try{ta=tl(to)}catch(tn){ta=tl(unescape(encodeURIComponent(to)))}return"data:application/pdf;filename="+ti.filename+";base64,"+ta;case"pdfobjectnewwindow":if("[object Window]"===Object.prototype.toString.call(tc)){var ts="https://cdnjs.cloudflare.com/ajax/libs/pdfobject/2.1.1/pdfobject.min.js",th=' integrity="sha512-4ze/a9/4jqu+tX9dfOqJYSvyYd5M6qum/3HpCLr+/Jqf0whc37VUbkpNGHR7/8pSnCFw47T1fmIpwBV7UySh3g==" crossorigin="anonymous"';ti.pdfObjectUrl&&(ts=ti.pdfObjectUrl,th="");var tf='<html><style>html, body { padding: 0; margin: 0; } iframe { width: 100%; height: 100%; border: 0;}  </style><body><script src="'+ts+'"'+th+'></script><script >PDFObject.embed("'+this.output("dataurlstring")+'", '+JSON.stringify(ti)+");</script></body></html>",td=tc.open();return null!==td&&td.document.write(tf),td}throw Error("The option pdfobjectnewwindow just works in a browser-environment.");case"pdfjsnewwindow":if("[object Window]"===Object.prototype.toString.call(tc)){var tp='<html><style>html, body { padding: 0; margin: 0; } iframe { width: 100%; height: 100%; border: 0;}  </style><body><iframe id="pdfViewer" src="'+(ti.pdfJsUrl||"examples/PDF.js/web/viewer.html")+"?file=&downloadName="+ti.filename+'" width="500px" height="400px" /></body></html>',tg=tc.open();if(null!==tg){tg.document.write(tp);var tm=this;tg.document.documentElement.querySelector("#pdfViewer").onload=function(){tg.document.title=ti.filename,tg.document.documentElement.querySelector("#pdfViewer").contentWindow.PDFViewerApplication.open(tm.output("bloburl"))}}return tg}throw Error("The option pdfjsnewwindow just works in a browser-environment.");case"dataurlnewwindow":if("[object Window]"!==Object.prototype.toString.call(tc))throw Error("The option dataurlnewwindow just works in a browser-environment.");var tv='<html><style>html, body { padding: 0; margin: 0; } iframe { width: 100%; height: 100%; border: 0;}  </style><body><iframe src="'+this.output("datauristring",ti)+'"></iframe></body></html>',tb=tc.open();if(null!==tb&&(tb.document.write(tv),tb.document.title=ti.filename),tb||"undefined"==typeof safari)return tb;break;case"datauri":case"dataurl":return tc.document.location.href=this.output("datauristring",ti);default:return null}}).foo=function(){try{return e7.apply(this,arguments)}catch(ta){var tn=ta.stack||"";~tn.indexOf(" at ")&&(tn=tn.split(" at ")[1]);var ti="Error in function "+tn.split("\n")[0].split("<")[0]+": "+ta.message;if(!tc.console)throw Error(ti);tc.console.error(ti,ta),tc.alert&&alert(ti)}},e7.foo.bar=e7,e7.foo),Ge=function(tn){return!0===Array.isArray(eL)&&eL.indexOf(tn)>-1};switch(ts){case"pt":es=1;break;case"mm":es=72/25.4;break;case"cm":es=72/2.54;break;case"in":es=72;break;case"px":es=1==Ge("px_scaling")?.75:96/72;break;case"pc":case"em":es=12;break;case"ex":es=6;break;default:if("number"!=typeof ts)throw Error("Invalid unit: "+ts);es=ts}var eZ=null;tT(),tq();var eK=tN.__private__.getPageInfo=tN.getPageInfo=function(tn){if(isNaN(tn)||tn%1!=0)throw Error("Invalid argument passed to jsPDF.getPageInfo");return{objId:ew[tn].objId,pageNumber:tn,pageContext:ew[tn]}},e$=tN.__private__.getPageInfoByObjId=function(tn){if(isNaN(tn)||tn%1!=0)throw Error("Invalid argument passed to jsPDF.getPageInfoByObjId");for(var ti in ew)if(ew[ti].objId===tn)break;return eK(ti)},eQ=tN.__private__.getCurrentPageInfo=tN.getCurrentPageInfo=function(){return{objId:ew[tz].objId,pageNumber:tz,pageContext:ew[tz]}};tN.addPage=function(){return Oe.apply(this,arguments),this},tN.setPage=function(){return Me.apply(this,arguments),ht.call(this,tZ[tz]),this},tN.insertPage=function(tn){return this.addPage(),this.movePage(tz,tn),this},tN.movePage=function(tn,ti){var ta,to;if(tn>ti){ta=tZ[tn],to=ew[tn];for(var ts=tn;ts>ti;ts--)tZ[ts]=tZ[ts-1],ew[ts]=ew[ts-1];tZ[ti]=ta,ew[ti]=to,this.setPage(ti)}else if(tn<ti){ta=tZ[tn],to=ew[tn];for(var tc=tn;tc<ti;tc++)tZ[tc]=tZ[tc+1],ew[tc]=ew[tc+1];tZ[ti]=ta,ew[ti]=to,this.setPage(ti)}return this},tN.deletePage=function(){return Be.apply(this,arguments),this},tN.__private__.text=tN.text=function(tn,ti,ta,ts,tc){var tu,th,tl,tf,td,tp,tg,tm,tv,tb=(ts=ts||{}).scope||this;if("number"==typeof tn&&"number"==typeof ti&&("string"==typeof ta||Array.isArray(ta))){var ty=ta;ta=ti,ti=tn,tn=ty}if(arguments[3]instanceof Vt==!1?(tl=arguments[4],tf=arguments[5],"object"===(0,to.Z)(tg=arguments[3])&&null!==tg||("string"==typeof tl&&(tf=tl,tl=null),"string"==typeof tg&&(tf=tg,tg=null),"number"==typeof tg&&(tl=tg,tg=null),ts={flags:tg,angle:tl,align:tf})):(q("The transform parameter of text() with a Matrix value"),tv=tc),isNaN(ti)||isNaN(ta)||null==tn)throw Error("Invalid arguments passed to jsPDF.text");if(0===tn.length)return tb;var tN="",tL=!1,tx="number"==typeof ts.lineHeightFactor?ts.lineHeightFactor:e9,tA=tb.internal.scaleFactor;function A(tn){for(var ti,ta=tn.concat(),to=[],ts=ta.length;ts--;)"string"==typeof(ti=ta.shift())?to.push(ti):Array.isArray(tn)&&(1===ti.length||void 0===ti[1]&&void 0===ti[2])?to.push(ti[0]):to.push([ti[0],ti[1],ti[2]]);return to}function _(tn,ti){var ta;if("string"==typeof tn)ta=ti(tn)[0];else if(Array.isArray(tn)){for(var to,ts,tc=tn.concat(),tu=[],th=tc.length;th--;)"string"==typeof(to=tc.shift())?tu.push(ti(to)[0]):Array.isArray(to)&&"string"==typeof to[0]&&tu.push([(ts=ti(to[0],to[1],to[2]))[0],ts[1],ts[2]]);ta=tu}return ta}var tS=!1,tk=!0;if("string"==typeof tn)tS=!0;else if(Array.isArray(tn)){var tI=tn.concat();th=[];for(var tC,tj=tI.length;tj--;)("string"!=typeof(tC=tI.shift())||Array.isArray(tC)&&"string"!=typeof tC[0])&&(tk=!1);tS=tk}if(!1===tS)throw Error('Type of text must be string or Array. "'+tn+'" is not recognized.');"string"==typeof tn&&(tn=tn.match(/[\r?\n]/)?tn.split(/\r\n|\r|\n/g):[tn]);var tE=t0/tb.internal.scaleFactor,tB=tE*(tx-1);switch(ts.baseline){case"bottom":ta-=tB;break;case"top":ta+=tE-tB;break;case"hanging":ta+=tE-2*tB;break;case"middle":ta+=tE/2-tB}if((tp=ts.maxWidth||0)>0&&("string"==typeof tn?tn=tb.splitTextToSize(tn,tp):"[object Array]"===Object.prototype.toString.call(tn)&&(tn=tn.reduce(function(tn,ti){return tn.concat(tb.splitTextToSize(ti,tp))},[]))),tu={text:tn,x:ti,y:ta,options:ts,mutex:{pdfEscape:eR,activeFontKey:eo,fonts:el,activeFontSize:t0}},eN.publish("preProcessText",tu),tn=tu.text,tl=(ts=tu.options).angle,tv instanceof Vt==!1&&tl&&"number"==typeof tl){tl*=Math.PI/180,0===ts.rotationDirection&&(tl=-tl),tP===t_.ADVANCED&&(tl=-tl);var tM=Math.cos(tl),tq=Math.sin(tl);tv=new Vt(tM,tq,-tq,tM,0,0)}else tl&&tl instanceof Vt&&(tv=tl);tP!==t_.ADVANCED||tv||(tv=eP),void 0!==(td=ts.charSpace||rv)&&(tN+=tF(tO(td))+" Tc\n",this.setCharSpace(this.getCharSpace()||0)),void 0!==(tm=ts.horizontalScale)&&(tN+=tF(100*tm)+" Tz\n"),ts.lang;var tD=-1,tR=void 0!==ts.renderingMode?ts.renderingMode:ts.stroke,tT=tb.internal.getCurrentPageInfo().pageContext;switch(tR){case 0:case!1:case"fill":tD=0;break;case 1:case!0:case"stroke":tD=1;break;case 2:case"fillThenStroke":tD=2;break;case 3:case"invisible":tD=3;break;case 4:case"fillAndAddForClipping":tD=4;break;case 5:case"strokeAndAddPathForClipping":tD=5;break;case 6:case"fillThenStrokeAndAddToPathForClipping":tD=6;break;case 7:case"addToPathForClipping":tD=7}var tU=void 0!==tT.usedRenderingMode?tT.usedRenderingMode:-1;-1!==tD?tN+=tD+" Tr\n":-1!==tU&&(tN+="0 Tr\n"),-1!==tD&&(tT.usedRenderingMode=tD),tf=ts.align||"left";var tz,tV=t0*tx,tH=tb.internal.pageSize.getWidth(),tW=el[eo];td=ts.charSpace||rv,tp=ts.maxWidth||0,tg=Object.assign({autoencode:!0,noBOM:!0},ts.flags);var tG=[];if("[object Array]"===Object.prototype.toString.call(tn)){th=A(tn),"left"!==tf&&(tz=th.map(function(tn){return tb.getStringUnitWidth(tn,{font:tW,charSpace:td,fontSize:t0,doKerning:!1})*t0/tA}));var tJ,tY,tX=0;if("right"===tf){ti-=tz[0],tn=[],tj=th.length;for(var tZ=0;tZ<tj;tZ++)0===tZ?(tY=ru(ti),tJ=rh(ta)):(tY=tO(tX-tz[tZ]),tJ=-tV),tn.push([th[tZ],tY,tJ]),tX=tz[tZ]}else if("center"===tf){ti-=tz[0]/2,tn=[],tj=th.length;for(var tK=0;tK<tj;tK++)0===tK?(tY=ru(ti),tJ=rh(ta)):(tY=tO((tX-tz[tK])/2),tJ=-tV),tn.push([th[tK],tY,tJ]),tX=tz[tK]}else if("left"===tf){tn=[],tj=th.length;for(var t$=0;t$<tj;t$++)tn.push(th[t$])}else{if("justify"!==tf)throw Error('Unrecognized alignment option, use "left", "center", "right" or "justify".');tn=[],tj=th.length,tp=0!==tp?tp:tH;for(var t1=0;t1<tj;t1++)tJ=0===t1?rh(ta):-tV,tY=0===t1?ru(ti):0,t1<tj-1?tG.push(tF(tO((tp-tz[t1])/(th[t1].split(" ").length-1)))):tG.push(0),tn.push([th[t1],tY,tJ])}}!0===("boolean"==typeof ts.R2L?ts.R2L:t6)&&(tn=_(tn,function(tn,ti,ta){return[tn.split("").reverse().join(""),ti,ta]})),tu={text:tn,x:ti,y:ta,options:ts,mutex:{pdfEscape:eR,activeFontKey:eo,fonts:el,activeFontSize:t0}},eN.publish("postProcessText",tu),tn=tu.text,tL=tu.mutex.isHex||!1;var t2=el[eo].encoding;"WinAnsiEncoding"!==t2&&"StandardEncoding"!==t2||(tn=_(tn,function(tn,ti,ta){return[eR(tn.split("	").join(Array(ts.TabLen||9).join(" ")),tg),ti,ta]})),th=A(tn),tn=[];for(var t5,t3,t4,t8=Array.isArray(th[0])?1:0,t7="",ht=function(tn,ti,ta){var to="";return ta instanceof Vt?(ta="number"==typeof ts.angle?e_(ta,new Vt(1,0,0,1,tn,ti)):e_(new Vt(1,0,0,1,tn,ti),ta),tP===t_.ADVANCED&&(ta=e_(new Vt(1,0,0,-1,0,0),ta)),to=ta.join(" ")+" Tm\n"):to=tF(tn)+" "+tF(ti)+" Td\n",to},t9=0;t9<th.length;t9++){switch(t7="",t8){case 1:t4=(tL?"<":"(")+th[t9][0]+(tL?">":")"),t5=parseFloat(th[t9][1]),t3=parseFloat(th[t9][2]);break;case 0:t4=(tL?"<":"(")+th[t9]+(tL?">":")"),t5=ru(ti),t3=rh(ta)}void 0!==tG&&void 0!==tG[t9]&&(t7=tG[t9]+" Tw\n"),0===t9?tn.push(t7+ht(t5,t3,tv)+t4):0===t8?tn.push(t7+t4):1===t8&&tn.push(t7+ht(t5,t3,tv)+t4)}tn=(0===t8?tn.join(" Tj\nT* "):tn.join(" Tj\n"))+" Tj\n";var en="BT\n/";return tQ(en+=eo+" "+t0+" Tf\n"+tF(t0*tx)+" TL\n"+rg+"\n"+tN+tn+"ET"),tw[eo]=!0,tb};var e1=tN.__private__.clip=tN.clip=function(tn){return tQ("evenodd"===tn?"W*":"W"),this};tN.clipEvenOdd=function(){return e1("evenodd")},tN.__private__.discardPath=tN.discardPath=function(){return tQ("n"),this};var e2=tN.__private__.isValidStyle=function(tn){var ti=!1;return -1!==[void 0,null,"S","D","F","DF","FD","f","f*","B","B*","n"].indexOf(tn)&&(ti=!0),ti};tN.__private__.setDefaultPathOperation=tN.setDefaultPathOperation=function(tn){return e2(tn)&&(tv=tn),this};var e5=tN.__private__.getStyle=tN.getStyle=function(tn){var ti=tv;switch(tn){case"D":case"S":ti="S";break;case"F":ti="f";break;case"FD":case"DF":ti="B";break;case"f":case"f*":case"B":case"B*":ti=tn}return ti},e0=tN.close=function(){return tQ("h"),this};tN.stroke=function(){return tQ("S"),this},tN.fill=function(tn){return rr("f",tn),this},tN.fillEvenOdd=function(tn){return rr("f*",tn),this},tN.fillStroke=function(tn){return rr("B",tn),this},tN.fillStrokeEvenOdd=function(tn){return rr("B*",tn),this};var rr=function(tn,ti){"object"===(0,to.Z)(ti)?ar(ti,tn):tQ(tn)},nr=function(tn){null===tn||tP===t_.ADVANCED&&void 0===tn||tQ(tn=e5(tn))};function ir(tn,ti,ta,to,ts){var tc=new M(ti||this.boundingBox,ta||this.xStep,to||this.yStep,this.gState,ts||this.matrix);return tc.stream=this.stream,Jt(tn+"$$"+this.cloneIndex+++"$$",tc),tc}var ar=function(tn,ti){var ta=eg[tn.key],to=ep[ta];if(to instanceof B)tQ("q"),tQ(or(ti)),to.gState&&tN.setGState(to.gState),tQ(tn.matrix.toString()+" cm"),tQ("/"+ta+" sh"),tQ("Q");else if(to instanceof M){var ts=new Vt(1,0,0,-1,0,rA());tn.matrix&&(ts=ts.multiply(tn.matrix||eP),ta=ir.call(to,tn.key,tn.boundingBox,tn.xStep,tn.yStep,ts).id),tQ("q"),tQ("/Pattern cs"),tQ("/"+ta+" scn"),to.gState&&tN.setGState(to.gState),tQ(ti),tQ("Q")}},or=function(tn){switch(tn){case"f":case"F":case"n":return"W n";case"f*":return"W* n";case"B":case"S":return"W S";case"B*":return"W* S"}},e3=tN.moveTo=function(tn,ti){return tQ(tF(tO(tn))+" "+tF(H(ti))+" m"),this},e4=tN.lineTo=function(tn,ti){return tQ(tF(tO(tn))+" "+tF(H(ti))+" l"),this},e6=tN.curveTo=function(tn,ti,ta,to,ts,tc){return tQ([tF(tO(tn)),tF(H(ti)),tF(tO(ta)),tF(H(to)),tF(tO(ts)),tF(H(tc)),"c"].join(" ")),this};tN.__private__.line=tN.line=function(tn,ti,ta,to,ts){if(isNaN(tn)||isNaN(ti)||isNaN(ta)||isNaN(to)||!e2(ts))throw Error("Invalid arguments passed to jsPDF.line");return tP===t_.COMPAT?this.lines([[ta-tn,to-ti]],tn,ti,[1,1],ts||"S"):this.lines([[ta-tn,to-ti]],tn,ti,[1,1]).stroke()},tN.__private__.lines=tN.lines=function(tn,ti,ta,to,ts,tc){var tu,th,tl,tf,td,tp,tg,tm,tv,tb,ty;if("number"==typeof tn&&(ty=ta,ta=ti,ti=tn,tn=ty),to=to||[1,1],tc=tc||!1,isNaN(ti)||isNaN(ta)||!Array.isArray(tn)||!Array.isArray(to)||!e2(ts)||"boolean"!=typeof tc)throw Error("Invalid arguments passed to jsPDF.lines");for(e3(ti,ta),tu=to[0],th=to[1],tf=tn.length,tv=ti,tb=ta,tl=0;tl<tf;tl++)2===(td=tn[tl]).length?e4(tv=td[0]*tu+tv,tb=td[1]*th+tb):(tp=td[0]*tu+tv,tg=td[1]*th+tb,tm=td[2]*tu+tv,e6(tp,tg,tm,td[3]*th+tb,tv=td[4]*tu+tv,tb=td[5]*th+tb));return tc&&e0(),nr(ts),this},tN.path=function(tn){for(var ti=0;ti<tn.length;ti++){var ta=tn[ti],to=ta.c;switch(ta.op){case"m":e3(to[0],to[1]);break;case"l":e4(to[0],to[1]);break;case"c":e6.apply(this,to);break;case"h":e0()}}return this},tN.__private__.rect=tN.rect=function(tn,ti,ta,to,ts){if(isNaN(tn)||isNaN(ti)||isNaN(ta)||isNaN(to)||!e2(ts))throw Error("Invalid arguments passed to jsPDF.rect");return tP===t_.COMPAT&&(to=-to),tQ([tF(tO(tn)),tF(H(ti)),tF(tO(ta)),tF(tO(to)),"re"].join(" ")),nr(ts),this},tN.__private__.triangle=tN.triangle=function(tn,ti,ta,to,ts,tc,tu){if(isNaN(tn)||isNaN(ti)||isNaN(ta)||isNaN(to)||isNaN(ts)||isNaN(tc)||!e2(tu))throw Error("Invalid arguments passed to jsPDF.triangle");return this.lines([[ta-tn,to-ti],[ts-ta,tc-to],[tn-ts,ti-tc]],tn,ti,[1,1],tu,!0),this},tN.__private__.roundedRect=tN.roundedRect=function(tn,ti,ta,to,ts,tc,tu){if(isNaN(tn)||isNaN(ti)||isNaN(ta)||isNaN(to)||isNaN(ts)||isNaN(tc)||!e2(tu))throw Error("Invalid arguments passed to jsPDF.roundedRect");var th=4/3*(Math.SQRT2-1);return ts=Math.min(ts,.5*ta),tc=Math.min(tc,.5*to),this.lines([[ta-2*ts,0],[ts*th,0,ts,tc-tc*th,ts,tc],[0,to-2*tc],[0,tc*th,-ts*th,tc,-ts,tc],[2*ts-ta,0],[-ts*th,0,-ts,-tc*th,-ts,-tc],[0,2*tc-to],[0,-tc*th,ts*th,-tc,ts,-tc]],tn+ts,ti,[1,1],tu,!0),this},tN.__private__.ellipse=tN.ellipse=function(tn,ti,ta,to,ts){if(isNaN(tn)||isNaN(ti)||isNaN(ta)||isNaN(to)||!e2(ts))throw Error("Invalid arguments passed to jsPDF.ellipse");var tc=4/3*(Math.SQRT2-1)*ta,tu=4/3*(Math.SQRT2-1)*to;return e3(tn+ta,ti),e6(tn+ta,ti-tu,tn+tc,ti-to,tn,ti-to),e6(tn-tc,ti-to,tn-ta,ti-tu,tn-ta,ti),e6(tn-ta,ti+tu,tn-tc,ti+to,tn,ti+to),e6(tn+tc,ti+to,tn+ta,ti+tu,tn+ta,ti),nr(ts),this},tN.__private__.circle=tN.circle=function(tn,ti,ta,to){if(isNaN(tn)||isNaN(ti)||isNaN(ta)||!e2(to))throw Error("Invalid arguments passed to jsPDF.circle");return this.ellipse(tn,ti,ta,ta,to)},tN.setFont=function(tn,ti,ta){return ta&&(ti=tk(ti,ta)),eo=qe(tn,ti,{disableWarning:!1}),this};var e8=tN.__private__.getFont=tN.getFont=function(){return el[qe.apply(tN,arguments)]};tN.__private__.getFontList=tN.getFontList=function(){var tn,ti,ta={};for(tn in ef)if(ef.hasOwnProperty(tn))for(ti in ta[tn]=[],ef[tn])ef[tn].hasOwnProperty(ti)&&ta[tn].push(ti);return ta},tN.addFont=function(tn,ti,ta,to,ts){var tc=["StandardEncoding","MacRomanEncoding","Identity-H","WinAnsiEncoding"];return arguments[3]&&-1!==tc.indexOf(arguments[3])?ts=arguments[3]:arguments[3]&&-1==tc.indexOf(arguments[3])&&(ta=tk(ta,to)),ts=ts||"Identity-H",Pe.call(this,tn,ti,ta,ts)};var e7,e9,rn=tn.lineWidth||.200025,ri=tN.__private__.getLineWidth=tN.getLineWidth=function(){return rn},ra=tN.__private__.setLineWidth=tN.setLineWidth=function(tn){return rn=tn,tQ(tF(tO(tn))+" w"),this};tN.__private__.setLineDash=E.API.setLineDash=E.API.setLineDashPattern=function(tn,ti){if(tn=tn||[],isNaN(ti=ti||0)||!Array.isArray(tn))throw Error("Invalid arguments passed to jsPDF.setLineDash");return tQ("["+(tn=tn.map(function(tn){return tF(tO(tn))}).join(" "))+"] "+(ti=tF(tO(ti)))+" d"),this};var ro=tN.__private__.getLineHeight=tN.getLineHeight=function(){return t0*e9};tN.__private__.getLineHeight=tN.getLineHeight=function(){return t0*e9};var rs=tN.__private__.setLineHeightFactor=tN.setLineHeightFactor=function(tn){return"number"==typeof(tn=tn||1.15)&&(e9=tn),this},rc=tN.__private__.getLineHeightFactor=tN.getLineHeightFactor=function(){return e9};rs(tn.lineHeight);var ru=tN.__private__.getHorizontalCoordinate=function(tn){return tO(tn)},rh=tN.__private__.getVerticalCoordinate=function(tn){return tP===t_.ADVANCED?tn:ew[tz].mediaBox.topRightY-ew[tz].mediaBox.bottomLeftY-tO(tn)},rl=tN.__private__.getHorizontalCoordinateString=tN.getHorizontalCoordinateString=function(tn){return tF(ru(tn))},rf=tN.__private__.getVerticalCoordinateString=tN.getVerticalCoordinateString=function(tn){return tF(rh(tn))},rd=tn.strokeColor||"0 G";tN.__private__.getStrokeColor=tN.getDrawColor=function(){return eO(rd)},tN.__private__.setStrokeColor=tN.setDrawColor=function(tn,ti,ta,to){return tQ(rd=eE({ch1:tn,ch2:ti,ch3:ta,ch4:to,pdfColorType:"draw",precision:2})),this};var rp=tn.fillColor||"0 g";tN.__private__.getFillColor=tN.getFillColor=function(){return eO(rp)},tN.__private__.setFillColor=tN.setFillColor=function(tn,ti,ta,to){return tQ(rp=eE({ch1:tn,ch2:ti,ch3:ta,ch4:to,pdfColorType:"fill",precision:2})),this};var rg=tn.textColor||"0 g",rm=tN.__private__.getTextColor=tN.getTextColor=function(){return eO(rg)};tN.__private__.setTextColor=tN.setTextColor=function(tn,ti,ta,to){return rg=eE({ch1:tn,ch2:ti,ch3:ta,ch4:to,pdfColorType:"text",precision:3}),this};var rv=tn.charSpace,rb=tN.__private__.getCharSpace=tN.getCharSpace=function(){return parseFloat(rv||0)};tN.__private__.setCharSpace=tN.setCharSpace=function(tn){if(isNaN(tn))throw Error("Invalid argument passed to jsPDF.setCharSpace");return rv=tn,this};var ry=0;tN.CapJoinStyles={0:0,butt:0,but:0,miter:0,1:1,round:1,rounded:1,circle:1,2:2,projecting:2,project:2,square:2,bevel:2},tN.__private__.setLineCap=tN.setLineCap=function(tn){var ti=tN.CapJoinStyles[tn];if(void 0===ti)throw Error("Line cap style of '"+tn+"' is not recognized. See or extend .CapJoinStyles property for valid styles");return ry=ti,tQ(ti+" J"),this};var rw=0;tN.__private__.setLineJoin=tN.setLineJoin=function(tn){var ti=tN.CapJoinStyles[tn];if(void 0===ti)throw Error("Line join style of '"+tn+"' is not recognized. See or extend .CapJoinStyles property for valid styles");return rw=ti,tQ(ti+" j"),this},tN.__private__.setLineMiterLimit=tN.__private__.setMiterLimit=tN.setLineMiterLimit=tN.setMiterLimit=function(tn){if(isNaN(tn=tn||0))throw Error("Invalid argument passed to jsPDF.setLineMiterLimit");return tQ(tF(tO(tn))+" M"),this},tN.GState=j,tN.setGState=function(tn){(tn="string"==typeof tn?em[ev[tn]]:Fr(null,tn)).equals(eb)||(tQ("/"+tn.id+" gs"),eb=tn)};var Fr=function(tn,ti){if(!tn||!ev[tn]){var ta=!1;for(var to in em)if(em.hasOwnProperty(to)&&em[to].equals(ti)){ta=!0;break}if(ta)ti=em[to];else{var ts="GS"+(Object.keys(em).length+1).toString(10);em[ts]=ti,ti.id=ts}return tn&&(ev[tn]=ti.id),eN.publish("addGState",ti),ti}};tN.addGState=function(tn,ti){return Fr(tn,ti),this},tN.saveGraphicsState=function(){return tQ("q"),ed.push({key:eo,size:t0,color:rg}),this},tN.restoreGraphicsState=function(){tQ("Q");var tn=ed.pop();return eo=tn.key,t0=tn.size,rg=tn.color,eb=null,this},tN.setCurrentTransformationMatrix=function(tn){return tQ(tn.toString()+" cm"),this},tN.comment=function(tn){return tQ("#"+tn),this};var Cr=function(tn,ti){var ta=tn||0;Object.defineProperty(this,"x",{enumerable:!0,get:function(){return ta},set:function(tn){isNaN(tn)||(ta=parseFloat(tn))}});var to=ti||0;Object.defineProperty(this,"y",{enumerable:!0,get:function(){return to},set:function(tn){isNaN(tn)||(to=parseFloat(tn))}});var ts="pt";return Object.defineProperty(this,"type",{enumerable:!0,get:function(){return ts},set:function(tn){ts=tn.toString()}}),this},jr=function(tn,ti,ta,to){Cr.call(this,tn,ti),this.type="rect";var ts=ta||0;Object.defineProperty(this,"w",{enumerable:!0,get:function(){return ts},set:function(tn){isNaN(tn)||(ts=parseFloat(tn))}});var tc=to||0;return Object.defineProperty(this,"h",{enumerable:!0,get:function(){return tc},set:function(tn){isNaN(tn)||(tc=parseFloat(tn))}}),this},Or=function(){this.page=ey,this.currentPage=tz,this.pages=tZ.slice(0),this.pagesContext=ew.slice(0),this.x=ec,this.y=eu,this.matrix=eh,this.width=rL(tz),this.height=rA(tz),this.outputDestination=t$,this.id="",this.objectNumber=-1};Or.prototype.restore=function(){ey=this.page,tz=this.currentPage,ew=this.pagesContext,tZ=this.pages,ec=this.x,eu=this.y,eh=this.matrix,rx(tz,this.width),rS(tz,this.height),t$=this.outputDestination};var Br=function(tn,ti,ta,to,ts){eS.push(new Or),ey=tz=0,tZ=[],ec=tn,eu=ti,eh=ts,eT([ta,to])},Mr=function(tn){if(eA[tn])eS.pop().restore();else{var ti=new Or,ta="Xo"+(Object.keys(ex).length+1).toString(10);ti.id=ta,eA[tn]=ta,ex[ta]=ti,eN.publish("addFormObject",ti),eS.pop().restore()}};for(var rN in tN.beginFormObject=function(tn,ti,ta,to,ts){return Br(tn,ti,ta,to,ts),this},tN.endFormObject=function(tn){return Mr(tn),this},tN.doFormObject=function(tn,ti){var ta=ex[eA[tn]];return tQ("q"),tQ(ti.toString()+" cm"),tQ("/"+ta.id+" Do"),tQ("Q"),this},tN.getFormObject=function(tn){var ti=ex[eA[tn]];return{x:ti.x,y:ti.y,width:ti.width,height:ti.height,matrix:ti.matrix}},tN.save=function(tn,ti){return tn=tn||"generated.pdf",(ti=ti||{}).returnPromise=ti.returnPromise||!1,!1===ti.returnPromise?(tf(eY(eJ()),tn),"function"==typeof tf.unload&&tc.setTimeout&&setTimeout(tf.unload,911),this):new Promise(function(ti,ta){try{var to=tf(eY(eJ()),tn);"function"==typeof tf.unload&&tc.setTimeout&&setTimeout(tf.unload,911),ti(to)}catch(tn){ta(tn.message)}})},E.API)E.API.hasOwnProperty(rN)&&("events"===rN&&E.API.events.length?function(tn,ti){var ta,to,ts;for(ts=ti.length-1;-1!==ts;ts--)ta=ti[ts][0],to=ti[ts][1],tn.subscribe.apply(tn,[ta].concat("function"==typeof to?[to]:to))}(eN,E.API.events):tN[rN]=E.API[rN]);var rL=tN.getPageWidth=function(tn){return(ew[tn=tn||tz].mediaBox.topRightX-ew[tn].mediaBox.bottomLeftX)/es},rx=tN.setPageWidth=function(tn,ti){ew[tn].mediaBox.topRightX=ti*es+ew[tn].mediaBox.bottomLeftX},rA=tN.getPageHeight=function(tn){return(ew[tn=tn||tz].mediaBox.topRightY-ew[tn].mediaBox.bottomLeftY)/es},rS=tN.setPageHeight=function(tn,ti){ew[tn].mediaBox.topRightY=ti*es+ew[tn].mediaBox.bottomLeftY};return tN.internal={pdfEscape:eR,getStyle:e5,getFont:e8,getFontSize:t4,getCharSpace:rb,getTextColor:rm,getLineHeight:ro,getLineHeightFactor:rc,getLineWidth:ri,write:t1,getHorizontalCoordinate:ru,getVerticalCoordinate:rh,getCoordinateString:rl,getVerticalCoordinateString:rf,collections:{},newObject:ek,newAdditionalObject:eI,newObjectDeferred:eF,newObjectDeferredBegin:Zt,getFilters:eB,putStream:eM,events:eN,scaleFactor:es,pageSize:{getWidth:function(){return rL(tz)},setWidth:function(tn){rx(tz,tn)},getHeight:function(){return rA(tz)},setHeight:function(tn){rS(tz,tn)}},encryptionOptions:tb,encryption:eZ,getEncryptor:function(tn){return null!==tb?eZ.encryptor(tn,0):function(tn){return tn}},output:eX,getNumberOfPages:eU,pages:tZ,out:tQ,f2:tC,f3:tj,getPageInfo:eK,getPageInfoByObjId:e$,getCurrentPageInfo:eQ,getPDFVersion:tx,Point:Cr,Rectangle:jr,Matrix:Vt,hasHotfix:Ge},Object.defineProperty(tN.internal.pageSize,"width",{get:function(){return rL(tz)},set:function(tn){rx(tz,tn)},enumerable:!0,configurable:!0}),Object.defineProperty(tN.internal.pageSize,"height",{get:function(){return rA(tz)},set:function(tn){rS(tz,tn)},enumerable:!0,configurable:!0}),(function(tn){for(var ti=0,ta=t5.length;ti<ta;ti++){var to=Pe.call(this,tn[ti][0],tn[ti][1],tn[ti][2],t5[ti][3],!0);!1===ty&&(tw[to]=!0);var ts=tn[ti][0].split("-");_e({id:to,fontName:ts[0],fontStyle:ts[1]||""})}eN.publish("addFonts",{fonts:el,dictionary:ef})}).call(tN,t5),eo="F1",Oe(th,ta),eN.publish("initialized"),tN}I.prototype.lsbFirstWord=function(tn){return String.fromCharCode(tn>>0&255,tn>>8&255,tn>>16&255,tn>>24&255)},I.prototype.toHexString=function(tn){return tn.split("").map(function(tn){return("0"+(255&tn.charCodeAt(0)).toString(16)).slice(-2)}).join("")},I.prototype.hexToBytes=function(tn){for(var ti=[],ta=0;ta<tn.length;ta+=2)ti.push(String.fromCharCode(parseInt(tn.substr(ta,2),16)));return ti.join("")},I.prototype.processOwnerPassword=function(tn,ti){return P(x(ti).substr(0,5),tn)},I.prototype.encryptor=function(tn,ti){var ta=x(this.encryptionKey+String.fromCharCode(255&tn,tn>>8&255,tn>>16&255,255&ti,ti>>8&255)).substr(0,10);return function(tn){return P(ta,tn)}},j.prototype.equals=function(tn){var ti,ta="id,objectNumber,equals";if(!tn||(0,to.Z)(tn)!==(0,to.Z)(this))return!1;var ts=0;for(ti in this)if(!(ta.indexOf(ti)>=0)){if(this.hasOwnProperty(ti)&&!tn.hasOwnProperty(ti)||this[ti]!==tn[ti])return!1;ts++}for(ti in tn)tn.hasOwnProperty(ti)&&0>ta.indexOf(ti)&&ts--;return 0===ts},E.API={events:[]},E.version="2.5.1";var tm=E.API,tv=1,R=function(tn){return tn.replace(/\\/g,"\\\\").replace(/\(/g,"\\(").replace(/\)/g,"\\)")},T=function(tn){return tn.replace(/\\\\/g,"\\").replace(/\\\(/g,"(").replace(/\\\)/g,")")},U=function(tn){return tn.toFixed(2)},z=function(tn){return tn.toFixed(5)};tm.__acroform__={};var H=function(tn,ti){tn.prototype=Object.create(ti.prototype),tn.prototype.constructor=tn},W=function(tn){return tn*tv},V=function(tn){var ti=new ut,ta=tP.internal.getHeight(tn)||0,to=tP.internal.getWidth(tn)||0;return ti.BBox=[0,0,Number(U(to)),Number(U(ta))],ti},tb=tm.__acroform__.setBit=function(tn,ti){if(ti=ti||0,isNaN(tn=tn||0)||isNaN(ti))throw Error("Invalid arguments passed to jsPDF.API.__acroform__.setBit");return tn|1<<ti},ty=tm.__acroform__.clearBit=function(tn,ti){if(ti=ti||0,isNaN(tn=tn||0)||isNaN(ti))throw Error("Invalid arguments passed to jsPDF.API.__acroform__.clearBit");return tn&~(1<<ti)},tw=tm.__acroform__.getBit=function(tn,ti){if(isNaN(tn)||isNaN(ti))throw Error("Invalid arguments passed to jsPDF.API.__acroform__.getBit");return 0==(tn&1<<ti)?0:1},tN=tm.__acroform__.getBitForPdf=function(tn,ti){if(isNaN(tn)||isNaN(ti))throw Error("Invalid arguments passed to jsPDF.API.__acroform__.getBitForPdf");return tw(tn,ti-1)},tL=tm.__acroform__.setBitForPdf=function(tn,ti){if(isNaN(tn)||isNaN(ti))throw Error("Invalid arguments passed to jsPDF.API.__acroform__.setBitForPdf");return tb(tn,ti-1)},tx=tm.__acroform__.clearBitForPdf=function(tn,ti){if(isNaN(tn)||isNaN(ti))throw Error("Invalid arguments passed to jsPDF.API.__acroform__.clearBitForPdf");return ty(tn,ti-1)},tA=tm.__acroform__.calculateCoordinates=function(tn,ti){var ta=ti.internal.getHorizontalCoordinate,to=ti.internal.getVerticalCoordinate,ts=tn[0],tc=tn[1],tu=tn[2],th=tn[3],tl={};return tl.lowerLeft_X=ta(ts)||0,tl.lowerLeft_Y=to(tc+th)||0,tl.upperRight_X=ta(ts+tu)||0,tl.upperRight_Y=to(tc)||0,[Number(U(tl.lowerLeft_X)),Number(U(tl.lowerLeft_Y)),Number(U(tl.upperRight_X)),Number(U(tl.upperRight_Y))]},Q=function(tn){if(tn.appearanceStreamContent)return tn.appearanceStreamContent;if(tn.V||tn.DV){var ti=[],ta=tn._V||tn.DV,to=tt(tn,ta),ts=tn.scope.internal.getFont(tn.fontName,tn.fontStyle).id;ti.push("/Tx BMC"),ti.push("q"),ti.push("BT"),ti.push(tn.scope.__private__.encodeColorString(tn.color)),ti.push("/"+ts+" "+U(to.fontSize)+" Tf"),ti.push("1 0 0 1 0 0 Tm"),ti.push(to.text),ti.push("ET"),ti.push("Q"),ti.push("EMC");var tc=V(tn);return tc.scope=tn.scope,tc.stream=ti.join("\n"),tc}},tt=function(tn,ti){var ta=0===tn.fontSize?tn.maxFontSize:tn.fontSize,to={text:"",fontSize:""},ts=(ti=")"==(ti="("==ti.substr(0,1)?ti.substr(1):ti).substr(ti.length-1)?ti.substr(0,ti.length-1):ti).split(" ");ts=tn.multiline?ts.map(function(tn){return tn.split("\n")}):ts.map(function(tn){return[tn]});var tc=ta,tu=tP.internal.getHeight(tn)||0;tu=tu<0?-tu:tu;var th=tP.internal.getWidth(tn)||0;th=th<0?-th:th,tc++;t:for(;tc>0;){ti="";var tl,tf,td=et("3",tn,--tc).height,tp=tn.multiline?tu-tc:(tu-td)/2,tg=tp+=2,tm=0,tv=0,tb=0;if(tc<=0){ti="(...) Tj\n",ti+="% Width of Text: "+et(ti,tn,tc=12).width+", FieldWidth:"+th+"\n";break}for(var ty="",tw=0,tN=0;tN<ts.length;tN++)if(ts.hasOwnProperty(tN)){var tL=!1;if(1!==ts[tN].length&&tb!==ts[tN].length-1){if((td+2)*(tw+2)+2>tu)continue t;ty+=ts[tN][tb],tL=!0,tv=tN,tN--}else{ty=" "==(ty+=ts[tN][tb]+" ").substr(ty.length-1)?ty.substr(0,ty.length-1):ty;var tx,tA,tS=parseInt(tN),t_=(tx=ty,tA=tc,tS+1<ts.length&&et(tx+" "+ts[tS+1][0],tn,tA).width<=th-4),tk=tN>=ts.length-1;if(t_&&!tk){ty+=" ",tb=0;continue}if(t_||tk){if(tk)tv=tS;else if(tn.multiline&&(td+2)*(tw+2)+2>tu)continue t}else{if(!tn.multiline||(td+2)*(tw+2)+2>tu)continue t;tv=tS}}for(var tF="",tI=tm;tI<=tv;tI++){var tC=ts[tI];if(tn.multiline){if(tI===tv){tF+=tC[tb]+" ",tb=(tb+1)%tC.length;continue}if(tI===tm){tF+=tC[tC.length-1]+" ";continue}}tF+=tC[0]+" "}switch(tf=et(tF=" "==tF.substr(tF.length-1)?tF.substr(0,tF.length-1):tF,tn,tc).width,tn.textAlign){case"right":tl=th-tf-2;break;case"center":tl=(th-tf)/2;break;default:tl=2}ti+=U(tl)+" "+U(tg)+" Td\n("+R(tF)+") Tj\n"+-U(tl)+" 0 Td\n",tg=-(tc+2),tf=0,tm=tL?tv:tv+1,tw++,ty=""}break}return to.text=ti,to.fontSize=tc,to},et=function(tn,ti,ta){var to=ti.scope.internal.getFont(ti.fontName,ti.fontStyle),ts=ti.scope.getStringUnitWidth(tn,{font:to,fontSize:parseFloat(ta),charSpace:0})*parseFloat(ta);return{height:ti.scope.getStringUnitWidth("3",{font:to,fontSize:parseFloat(ta),charSpace:0})*parseFloat(ta)*1.5,width:ts}},tS={fields:[],xForms:[],acroFormDictionaryRoot:null,printedOut:!1,internal:null,isInitialized:!1},nt=function(tn,ti){var ta={type:"reference",object:tn};void 0===ti.internal.getPageInfo(tn.page).pageContext.annotations.find(function(tn){return tn.type===ta.type&&tn.object===ta.object})&&ti.internal.getPageInfo(tn.page).pageContext.annotations.push(ta)},it=function(tn,ti){for(var ta in tn)if(tn.hasOwnProperty(ta)){var ts=tn[ta];ti.internal.newObjectDeferredBegin(ts.objId,!0),"object"===(0,to.Z)(ts)&&"function"==typeof ts.putStream&&ts.putStream(),delete tn[ta]}},at=function(tn,ti){if(ti.scope=tn,void 0!==tn.internal&&(void 0===tn.internal.acroformPlugin||!1===tn.internal.acroformPlugin.isInitialized)){if(lt.FieldNum=0,tn.internal.acroformPlugin=JSON.parse(JSON.stringify(tS)),tn.internal.acroformPlugin.acroFormDictionaryRoot)throw Error("Exception while creating AcroformDictionary");tv=tn.internal.scaleFactor,tn.internal.acroformPlugin.acroFormDictionaryRoot=new ht,tn.internal.acroformPlugin.acroFormDictionaryRoot.scope=tn,tn.internal.acroformPlugin.acroFormDictionaryRoot._eventID=tn.internal.events.subscribe("postPutResources",function(){tn.internal.events.unsubscribe(tn.internal.acroformPlugin.acroFormDictionaryRoot._eventID),delete tn.internal.acroformPlugin.acroFormDictionaryRoot._eventID,tn.internal.acroformPlugin.printedOut=!0}),tn.internal.events.subscribe("buildDocument",function(){!function(tn){tn.internal.acroformPlugin.acroFormDictionaryRoot.objId=void 0;var ti=tn.internal.acroformPlugin.acroFormDictionaryRoot.Fields;for(var ta in ti)if(ti.hasOwnProperty(ta)){var to=ti[ta];to.objId=void 0,to.hasAnnotation&&nt(to,tn)}}(tn)}),tn.internal.events.subscribe("putCatalog",function(){!function(tn){if(void 0===tn.internal.acroformPlugin.acroFormDictionaryRoot)throw Error("putCatalogCallback: Root missing.");tn.internal.write("/AcroForm "+tn.internal.acroformPlugin.acroFormDictionaryRoot.objId+" 0 R")}(tn)}),tn.internal.events.subscribe("postPutPages",function(ti){!function(tn,ti){var ta=!tn;for(var ts in tn||(ti.internal.newObjectDeferredBegin(ti.internal.acroformPlugin.acroFormDictionaryRoot.objId,!0),ti.internal.acroformPlugin.acroFormDictionaryRoot.putStream()),tn=tn||ti.internal.acroformPlugin.acroFormDictionaryRoot.Kids)if(tn.hasOwnProperty(ts)){var tc=tn[ts],tu=[],th=tc.Rect;if(tc.Rect&&(tc.Rect=tA(tc.Rect,ti)),ti.internal.newObjectDeferredBegin(tc.objId,!0),tc.DA=tP.createDefaultAppearanceStream(tc),"object"===(0,to.Z)(tc)&&"function"==typeof tc.getKeyValueListForStream&&(tu=tc.getKeyValueListForStream()),tc.Rect=th,tc.hasAppearanceStream&&!tc.appearanceStreamContent){var tl=Q(tc);tu.push({key:"AP",value:"<</N "+tl+">>"}),ti.internal.acroformPlugin.xForms.push(tl)}if(tc.appearanceStreamContent){var tf="";for(var td in tc.appearanceStreamContent)if(tc.appearanceStreamContent.hasOwnProperty(td)){var tp=tc.appearanceStreamContent[td];if(tf+="/"+td+" <<",Object.keys(tp).length>=1||Array.isArray(tp)){for(var ts in tp)if(tp.hasOwnProperty(ts)){var tg=tp[ts];"function"==typeof tg&&(tg=tg.call(ti,tc)),tf+="/"+ts+" "+tg+" ",ti.internal.acroformPlugin.xForms.indexOf(tg)>=0||ti.internal.acroformPlugin.xForms.push(tg)}}else"function"==typeof(tg=tp)&&(tg=tg.call(ti,tc)),tf+="/"+ts+" "+tg,ti.internal.acroformPlugin.xForms.indexOf(tg)>=0||ti.internal.acroformPlugin.xForms.push(tg);tf+=">>"}tu.push({key:"AP",value:"<<\n"+tf+">>"})}ti.internal.putStream({additionalKeyValues:tu,objectId:tc.objId}),ti.internal.out("endobj")}ta&&it(ti.internal.acroformPlugin.xForms,ti)}(ti,tn)}),tn.internal.acroformPlugin.isInitialized=!0}},t_=tm.__acroform__.arrayToPdfArray=function(tn,ti,ta){var i=function(tn){return tn};if(Array.isArray(tn)){for(var ts="[",tc=0;tc<tn.length;tc++)switch(0!==tc&&(ts+=" "),(0,to.Z)(tn[tc])){case"boolean":case"number":case"object":ts+=tn[tc].toString();break;case"string":"/"!==tn[tc].substr(0,1)?(void 0!==ti&&ta&&(i=ta.internal.getEncryptor(ti)),ts+="("+R(i(tn[tc].toString()))+")"):ts+=tn[tc].toString()}return ts+"]"}throw Error("Invalid argument passed to jsPDF.__acroform__.arrayToPdfArray")},st=function(tn,ti,ta){var n=function(tn){return tn};return void 0!==ti&&ta&&(n=ta.internal.getEncryptor(ti)),(tn=tn||"").toString(),tn="("+R(n(tn))+")"},ct=function(){this._objId=void 0,this._scope=void 0,Object.defineProperty(this,"objId",{get:function(){if(void 0===this._objId){if(void 0===this.scope)return;this._objId=this.scope.internal.newObjectDeferred()}return this._objId},set:function(tn){this._objId=tn}}),Object.defineProperty(this,"scope",{value:this._scope,writable:!0})};ct.prototype.toString=function(){return this.objId+" 0 R"},ct.prototype.putStream=function(){var tn=this.getKeyValueListForStream();this.scope.internal.putStream({data:this.stream,additionalKeyValues:tn,objectId:this.objId}),this.scope.internal.out("endobj")},ct.prototype.getKeyValueListForStream=function(){var tn=[],ti=Object.getOwnPropertyNames(this).filter(function(tn){return"content"!=tn&&"appearanceStreamContent"!=tn&&"scope"!=tn&&"objId"!=tn&&"_"!=tn.substring(0,1)});for(var ta in ti)if(!1===Object.getOwnPropertyDescriptor(this,ti[ta]).configurable){var to=ti[ta],ts=this[to];ts&&(Array.isArray(ts)?tn.push({key:to,value:t_(ts,this.objId,this.scope)}):ts instanceof ct?(ts.scope=this.scope,tn.push({key:to,value:ts.objId+" 0 R"})):"function"!=typeof ts&&tn.push({key:to,value:ts}))}return tn};var ut=function(){ct.call(this),Object.defineProperty(this,"Type",{value:"/XObject",configurable:!1,writable:!0}),Object.defineProperty(this,"Subtype",{value:"/Form",configurable:!1,writable:!0}),Object.defineProperty(this,"FormType",{value:1,configurable:!1,writable:!0});var tn,ti=[];Object.defineProperty(this,"BBox",{configurable:!1,get:function(){return ti},set:function(tn){ti=tn}}),Object.defineProperty(this,"Resources",{value:"2 0 R",configurable:!1,writable:!0}),Object.defineProperty(this,"stream",{enumerable:!1,configurable:!0,set:function(ti){tn=ti.trim()},get:function(){return tn||null}})};H(ut,ct);var ht=function(){ct.call(this);var tn,ti=[];Object.defineProperty(this,"Kids",{enumerable:!1,configurable:!0,get:function(){return ti.length>0?ti:void 0}}),Object.defineProperty(this,"Fields",{enumerable:!1,configurable:!1,get:function(){return ti}}),Object.defineProperty(this,"DA",{enumerable:!1,configurable:!1,get:function(){if(tn){var e=function(tn){return tn};return this.scope&&(e=this.scope.internal.getEncryptor(this.objId)),"("+R(e(tn))+")"}},set:function(ti){tn=ti}})};H(ht,ct);var lt=function t(){ct.call(this);var tn=4;Object.defineProperty(this,"F",{enumerable:!1,configurable:!1,get:function(){return tn},set:function(ti){if(isNaN(ti))throw Error('Invalid value "'+ti+'" for attribute F supplied.');tn=ti}}),Object.defineProperty(this,"showWhenPrinted",{enumerable:!0,configurable:!0,get:function(){return!!tN(tn,3)},set:function(ti){!0==!!ti?this.F=tL(tn,3):this.F=tx(tn,3)}});var ti=0;Object.defineProperty(this,"Ff",{enumerable:!1,configurable:!1,get:function(){return ti},set:function(tn){if(isNaN(tn))throw Error('Invalid value "'+tn+'" for attribute Ff supplied.');ti=tn}});var ta=[];Object.defineProperty(this,"Rect",{enumerable:!1,configurable:!1,get:function(){if(0!==ta.length)return ta},set:function(tn){ta=void 0!==tn?tn:[]}}),Object.defineProperty(this,"x",{enumerable:!0,configurable:!0,get:function(){return!ta||isNaN(ta[0])?0:ta[0]},set:function(tn){ta[0]=tn}}),Object.defineProperty(this,"y",{enumerable:!0,configurable:!0,get:function(){return!ta||isNaN(ta[1])?0:ta[1]},set:function(tn){ta[1]=tn}}),Object.defineProperty(this,"width",{enumerable:!0,configurable:!0,get:function(){return!ta||isNaN(ta[2])?0:ta[2]},set:function(tn){ta[2]=tn}}),Object.defineProperty(this,"height",{enumerable:!0,configurable:!0,get:function(){return!ta||isNaN(ta[3])?0:ta[3]},set:function(tn){ta[3]=tn}});var to="";Object.defineProperty(this,"FT",{enumerable:!0,configurable:!1,get:function(){return to},set:function(tn){switch(tn){case"/Btn":case"/Tx":case"/Ch":case"/Sig":to=tn;break;default:throw Error('Invalid value "'+tn+'" for attribute FT supplied.')}}});var ts=null;Object.defineProperty(this,"T",{enumerable:!0,configurable:!1,get:function(){if(!ts||ts.length<1){if(this instanceof yt)return;ts="FieldObject"+t.FieldNum++}var e=function(tn){return tn};return this.scope&&(e=this.scope.internal.getEncryptor(this.objId)),"("+R(e(ts))+")"},set:function(tn){ts=tn.toString()}}),Object.defineProperty(this,"fieldName",{configurable:!0,enumerable:!0,get:function(){return ts},set:function(tn){ts=tn}});var tc="helvetica";Object.defineProperty(this,"fontName",{enumerable:!0,configurable:!0,get:function(){return tc},set:function(tn){tc=tn}});var tu="normal";Object.defineProperty(this,"fontStyle",{enumerable:!0,configurable:!0,get:function(){return tu},set:function(tn){tu=tn}});var th=0;Object.defineProperty(this,"fontSize",{enumerable:!0,configurable:!0,get:function(){return th},set:function(tn){th=tn}});var tl=void 0;Object.defineProperty(this,"maxFontSize",{enumerable:!0,configurable:!0,get:function(){return void 0===tl?50/tv:tl},set:function(tn){tl=tn}});var tf="black";Object.defineProperty(this,"color",{enumerable:!0,configurable:!0,get:function(){return tf},set:function(tn){tf=tn}});var td="/F1 0 Tf 0 g";Object.defineProperty(this,"DA",{enumerable:!0,configurable:!1,get:function(){if(!(!td||this instanceof yt||this instanceof Nt))return st(td,this.objId,this.scope)},set:function(tn){td=tn=tn.toString()}});var tp=null;Object.defineProperty(this,"DV",{enumerable:!1,configurable:!1,get:function(){if(tp)return this instanceof mt==!1?st(tp,this.objId,this.scope):tp},set:function(tn){tn=tn.toString(),tp=this instanceof mt==!1?"("===tn.substr(0,1)?T(tn.substr(1,tn.length-2)):T(tn):tn}}),Object.defineProperty(this,"defaultValue",{enumerable:!0,configurable:!0,get:function(){return this instanceof mt==!0?T(tp.substr(1,tp.length-1)):tp},set:function(tn){tn=tn.toString(),tp=this instanceof mt==!0?"/"+tn:tn}});var tg=null;Object.defineProperty(this,"_V",{enumerable:!1,configurable:!1,get:function(){if(tg)return tg},set:function(tn){this.V=tn}}),Object.defineProperty(this,"V",{enumerable:!1,configurable:!1,get:function(){if(tg)return this instanceof mt==!1?st(tg,this.objId,this.scope):tg},set:function(tn){tn=tn.toString(),tg=this instanceof mt==!1?"("===tn.substr(0,1)?T(tn.substr(1,tn.length-2)):T(tn):tn}}),Object.defineProperty(this,"value",{enumerable:!0,configurable:!0,get:function(){return this instanceof mt==!0?T(tg.substr(1,tg.length-1)):tg},set:function(tn){tn=tn.toString(),tg=this instanceof mt==!0?"/"+tn:tn}}),Object.defineProperty(this,"hasAnnotation",{enumerable:!0,configurable:!0,get:function(){return this.Rect}}),Object.defineProperty(this,"Type",{enumerable:!0,configurable:!1,get:function(){return this.hasAnnotation?"/Annot":null}}),Object.defineProperty(this,"Subtype",{enumerable:!0,configurable:!1,get:function(){return this.hasAnnotation?"/Widget":null}});var tm,tb=!1;Object.defineProperty(this,"hasAppearanceStream",{enumerable:!0,configurable:!0,get:function(){return tb},set:function(tn){tb=tn=!!tn}}),Object.defineProperty(this,"page",{enumerable:!0,configurable:!0,get:function(){if(tm)return tm},set:function(tn){tm=tn}}),Object.defineProperty(this,"readOnly",{enumerable:!0,configurable:!0,get:function(){return!!tN(this.Ff,1)},set:function(tn){!0==!!tn?this.Ff=tL(this.Ff,1):this.Ff=tx(this.Ff,1)}}),Object.defineProperty(this,"required",{enumerable:!0,configurable:!0,get:function(){return!!tN(this.Ff,2)},set:function(tn){!0==!!tn?this.Ff=tL(this.Ff,2):this.Ff=tx(this.Ff,2)}}),Object.defineProperty(this,"noExport",{enumerable:!0,configurable:!0,get:function(){return!!tN(this.Ff,3)},set:function(tn){!0==!!tn?this.Ff=tL(this.Ff,3):this.Ff=tx(this.Ff,3)}});var ty=null;Object.defineProperty(this,"Q",{enumerable:!0,configurable:!1,get:function(){if(null!==ty)return ty},set:function(tn){if(-1===[0,1,2].indexOf(tn))throw Error('Invalid value "'+tn+'" for attribute Q supplied.');ty=tn}}),Object.defineProperty(this,"textAlign",{get:function(){var tn;switch(ty){case 0:default:tn="left";break;case 1:tn="center";break;case 2:tn="right"}return tn},configurable:!0,enumerable:!0,set:function(tn){switch(tn){case"right":case 2:ty=2;break;case"center":case 1:ty=1;break;default:ty=0}}})};H(lt,ct);var ft=function(){lt.call(this),this.FT="/Ch",this.V="()",this.fontName="zapfdingbats";var tn=0;Object.defineProperty(this,"TI",{enumerable:!0,configurable:!1,get:function(){return tn},set:function(ti){tn=ti}}),Object.defineProperty(this,"topIndex",{enumerable:!0,configurable:!0,get:function(){return tn},set:function(ti){tn=ti}});var ti=[];Object.defineProperty(this,"Opt",{enumerable:!0,configurable:!1,get:function(){return t_(ti,this.objId,this.scope)},set:function(tn){var ta;ta=[],"string"==typeof tn&&(ta=function(tn,ti,ta){ta||(ta=1);for(var to,ts=[];to=ti.exec(tn);)ts.push(to[ta]);return ts}(tn,/\((.*?)\)/g)),ti=ta}}),this.getOptions=function(){return ti},this.setOptions=function(tn){ti=tn,this.sort&&ti.sort()},this.addOption=function(tn){tn=(tn=tn||"").toString(),ti.push(tn),this.sort&&ti.sort()},this.removeOption=function(tn,ta){for(ta=ta||!1,tn=(tn=tn||"").toString();-1!==ti.indexOf(tn)&&(ti.splice(ti.indexOf(tn),1),!1!==ta););},Object.defineProperty(this,"combo",{enumerable:!0,configurable:!0,get:function(){return!!tN(this.Ff,18)},set:function(tn){!0==!!tn?this.Ff=tL(this.Ff,18):this.Ff=tx(this.Ff,18)}}),Object.defineProperty(this,"edit",{enumerable:!0,configurable:!0,get:function(){return!!tN(this.Ff,19)},set:function(tn){!0===this.combo&&(!0==!!tn?this.Ff=tL(this.Ff,19):this.Ff=tx(this.Ff,19))}}),Object.defineProperty(this,"sort",{enumerable:!0,configurable:!0,get:function(){return!!tN(this.Ff,20)},set:function(tn){!0==!!tn?(this.Ff=tL(this.Ff,20),ti.sort()):this.Ff=tx(this.Ff,20)}}),Object.defineProperty(this,"multiSelect",{enumerable:!0,configurable:!0,get:function(){return!!tN(this.Ff,22)},set:function(tn){!0==!!tn?this.Ff=tL(this.Ff,22):this.Ff=tx(this.Ff,22)}}),Object.defineProperty(this,"doNotSpellCheck",{enumerable:!0,configurable:!0,get:function(){return!!tN(this.Ff,23)},set:function(tn){!0==!!tn?this.Ff=tL(this.Ff,23):this.Ff=tx(this.Ff,23)}}),Object.defineProperty(this,"commitOnSelChange",{enumerable:!0,configurable:!0,get:function(){return!!tN(this.Ff,27)},set:function(tn){!0==!!tn?this.Ff=tL(this.Ff,27):this.Ff=tx(this.Ff,27)}}),this.hasAppearanceStream=!1};H(ft,lt);var dt=function(){ft.call(this),this.fontName="helvetica",this.combo=!1};H(dt,ft);var pt=function(){dt.call(this),this.combo=!0};H(pt,dt);var gt=function(){pt.call(this),this.edit=!0};H(gt,pt);var mt=function(){lt.call(this),this.FT="/Btn",Object.defineProperty(this,"noToggleToOff",{enumerable:!0,configurable:!0,get:function(){return!!tN(this.Ff,15)},set:function(tn){!0==!!tn?this.Ff=tL(this.Ff,15):this.Ff=tx(this.Ff,15)}}),Object.defineProperty(this,"radio",{enumerable:!0,configurable:!0,get:function(){return!!tN(this.Ff,16)},set:function(tn){!0==!!tn?this.Ff=tL(this.Ff,16):this.Ff=tx(this.Ff,16)}}),Object.defineProperty(this,"pushButton",{enumerable:!0,configurable:!0,get:function(){return!!tN(this.Ff,17)},set:function(tn){!0==!!tn?this.Ff=tL(this.Ff,17):this.Ff=tx(this.Ff,17)}}),Object.defineProperty(this,"radioIsUnison",{enumerable:!0,configurable:!0,get:function(){return!!tN(this.Ff,26)},set:function(tn){!0==!!tn?this.Ff=tL(this.Ff,26):this.Ff=tx(this.Ff,26)}});var tn,ti={};Object.defineProperty(this,"MK",{enumerable:!1,configurable:!1,get:function(){var t=function(tn){return tn};if(this.scope&&(t=this.scope.internal.getEncryptor(this.objId)),0!==Object.keys(ti).length){var tn,ta=[];for(tn in ta.push("<<"),ti)ta.push("/"+tn+" ("+R(t(ti[tn]))+")");return ta.push(">>"),ta.join("\n")}},set:function(tn){"object"===(0,to.Z)(tn)&&(ti=tn)}}),Object.defineProperty(this,"caption",{enumerable:!0,configurable:!0,get:function(){return ti.CA||""},set:function(tn){"string"==typeof tn&&(ti.CA=tn)}}),Object.defineProperty(this,"AS",{enumerable:!1,configurable:!1,get:function(){return tn},set:function(ti){tn=ti}}),Object.defineProperty(this,"appearanceState",{enumerable:!0,configurable:!0,get:function(){return tn.substr(1,tn.length-1)},set:function(ti){tn="/"+ti}})};H(mt,lt);var vt=function(){mt.call(this),this.pushButton=!0};H(vt,mt);var bt=function(){mt.call(this),this.radio=!0,this.pushButton=!1;var tn=[];Object.defineProperty(this,"Kids",{enumerable:!0,configurable:!1,get:function(){return tn},set:function(ti){tn=void 0!==ti?ti:[]}})};H(bt,mt);var yt=function(){lt.call(this),Object.defineProperty(this,"Parent",{enumerable:!1,configurable:!1,get:function(){return tn},set:function(ti){tn=ti}}),Object.defineProperty(this,"optionName",{enumerable:!1,configurable:!0,get:function(){return ti},set:function(tn){ti=tn}});var tn,ti,ta,ts={};Object.defineProperty(this,"MK",{enumerable:!1,configurable:!1,get:function(){var t=function(tn){return tn};this.scope&&(t=this.scope.internal.getEncryptor(this.objId));var tn,ti=[];for(tn in ti.push("<<"),ts)ti.push("/"+tn+" ("+R(t(ts[tn]))+")");return ti.push(">>"),ti.join("\n")},set:function(tn){"object"===(0,to.Z)(tn)&&(ts=tn)}}),Object.defineProperty(this,"caption",{enumerable:!0,configurable:!0,get:function(){return ts.CA||""},set:function(tn){"string"==typeof tn&&(ts.CA=tn)}}),Object.defineProperty(this,"AS",{enumerable:!1,configurable:!1,get:function(){return ta},set:function(tn){ta=tn}}),Object.defineProperty(this,"appearanceState",{enumerable:!0,configurable:!0,get:function(){return ta.substr(1,ta.length-1)},set:function(tn){ta="/"+tn}}),this.caption="l",this.appearanceState="Off",this._AppearanceType=tP.RadioButton.Circle,this.appearanceStreamContent=this._AppearanceType.createAppearanceStream(this.optionName)};H(yt,lt),bt.prototype.setAppearance=function(tn){if(!("createAppearanceStream"in tn)||!("getCA"in tn))throw Error("Couldn't assign Appearance to RadioButton. Appearance was Invalid!");for(var ti in this.Kids)if(this.Kids.hasOwnProperty(ti)){var ta=this.Kids[ti];ta.appearanceStreamContent=tn.createAppearanceStream(ta.optionName),ta.caption=tn.getCA()}},bt.prototype.createOption=function(tn){var ti=new yt;return ti.Parent=this,ti.optionName=tn,this.Kids.push(ti),tk.call(this.scope,ti),ti};var wt=function(){mt.call(this),this.fontName="zapfdingbats",this.caption="3",this.appearanceState="On",this.value="On",this.textAlign="center",this.appearanceStreamContent=tP.CheckBox.createAppearanceStream()};H(wt,mt);var Nt=function(){lt.call(this),this.FT="/Tx",Object.defineProperty(this,"multiline",{enumerable:!0,configurable:!0,get:function(){return!!tN(this.Ff,13)},set:function(tn){!0==!!tn?this.Ff=tL(this.Ff,13):this.Ff=tx(this.Ff,13)}}),Object.defineProperty(this,"fileSelect",{enumerable:!0,configurable:!0,get:function(){return!!tN(this.Ff,21)},set:function(tn){!0==!!tn?this.Ff=tL(this.Ff,21):this.Ff=tx(this.Ff,21)}}),Object.defineProperty(this,"doNotSpellCheck",{enumerable:!0,configurable:!0,get:function(){return!!tN(this.Ff,23)},set:function(tn){!0==!!tn?this.Ff=tL(this.Ff,23):this.Ff=tx(this.Ff,23)}}),Object.defineProperty(this,"doNotScroll",{enumerable:!0,configurable:!0,get:function(){return!!tN(this.Ff,24)},set:function(tn){!0==!!tn?this.Ff=tL(this.Ff,24):this.Ff=tx(this.Ff,24)}}),Object.defineProperty(this,"comb",{enumerable:!0,configurable:!0,get:function(){return!!tN(this.Ff,25)},set:function(tn){!0==!!tn?this.Ff=tL(this.Ff,25):this.Ff=tx(this.Ff,25)}}),Object.defineProperty(this,"richText",{enumerable:!0,configurable:!0,get:function(){return!!tN(this.Ff,26)},set:function(tn){!0==!!tn?this.Ff=tL(this.Ff,26):this.Ff=tx(this.Ff,26)}});var tn=null;Object.defineProperty(this,"MaxLen",{enumerable:!0,configurable:!1,get:function(){return tn},set:function(ti){tn=ti}}),Object.defineProperty(this,"maxLength",{enumerable:!0,configurable:!0,get:function(){return tn},set:function(ti){Number.isInteger(ti)&&(tn=ti)}}),Object.defineProperty(this,"hasAppearanceStream",{enumerable:!0,configurable:!0,get:function(){return this.V||this.DV}})};H(Nt,lt);var Lt=function(){Nt.call(this),Object.defineProperty(this,"password",{enumerable:!0,configurable:!0,get:function(){return!!tN(this.Ff,14)},set:function(tn){!0==!!tn?this.Ff=tL(this.Ff,14):this.Ff=tx(this.Ff,14)}}),this.password=!0};H(Lt,Nt);var tP={CheckBox:{createAppearanceStream:function(){return{N:{On:tP.CheckBox.YesNormal},D:{On:tP.CheckBox.YesPushDown,Off:tP.CheckBox.OffPushDown}}},YesPushDown:function(tn){var ti=V(tn);ti.scope=tn.scope;var ta=[],to=tn.scope.internal.getFont(tn.fontName,tn.fontStyle).id,ts=tn.scope.__private__.encodeColorString(tn.color),tc=tt(tn,tn.caption);return ta.push("0.749023 g"),ta.push("0 0 "+U(tP.internal.getWidth(tn))+" "+U(tP.internal.getHeight(tn))+" re"),ta.push("f"),ta.push("BMC"),ta.push("q"),ta.push("0 0 1 rg"),ta.push("/"+to+" "+U(tc.fontSize)+" Tf "+ts),ta.push("BT"),ta.push(tc.text),ta.push("ET"),ta.push("Q"),ta.push("EMC"),ti.stream=ta.join("\n"),ti},YesNormal:function(tn){var ti=V(tn);ti.scope=tn.scope;var ta=tn.scope.internal.getFont(tn.fontName,tn.fontStyle).id,to=tn.scope.__private__.encodeColorString(tn.color),ts=[],tc=tP.internal.getHeight(tn),tu=tP.internal.getWidth(tn),th=tt(tn,tn.caption);return ts.push("1 g"),ts.push("0 0 "+U(tu)+" "+U(tc)+" re"),ts.push("f"),ts.push("q"),ts.push("0 0 1 rg"),ts.push("0 0 "+U(tu-1)+" "+U(tc-1)+" re"),ts.push("W"),ts.push("n"),ts.push("0 g"),ts.push("BT"),ts.push("/"+ta+" "+U(th.fontSize)+" Tf "+to),ts.push(th.text),ts.push("ET"),ts.push("Q"),ti.stream=ts.join("\n"),ti},OffPushDown:function(tn){var ti=V(tn);ti.scope=tn.scope;var ta=[];return ta.push("0.749023 g"),ta.push("0 0 "+U(tP.internal.getWidth(tn))+" "+U(tP.internal.getHeight(tn))+" re"),ta.push("f"),ti.stream=ta.join("\n"),ti}},RadioButton:{Circle:{createAppearanceStream:function(tn){var ti={D:{Off:tP.RadioButton.Circle.OffPushDown},N:{}};return ti.N[tn]=tP.RadioButton.Circle.YesNormal,ti.D[tn]=tP.RadioButton.Circle.YesPushDown,ti},getCA:function(){return"l"},YesNormal:function(tn){var ti=V(tn);ti.scope=tn.scope;var ta=[],to=tP.internal.getWidth(tn)<=tP.internal.getHeight(tn)?tP.internal.getWidth(tn)/4:tP.internal.getHeight(tn)/4,ts=Number(((to=Number((.9*to).toFixed(5)))*tP.internal.Bezier_C).toFixed(5));return ta.push("q"),ta.push("1 0 0 1 "+z(tP.internal.getWidth(tn)/2)+" "+z(tP.internal.getHeight(tn)/2)+" cm"),ta.push(to+" 0 m"),ta.push(to+" "+ts+" "+ts+" "+to+" 0 "+to+" c"),ta.push("-"+ts+" "+to+" -"+to+" "+ts+" -"+to+" 0 c"),ta.push("-"+to+" -"+ts+" -"+ts+" -"+to+" 0 -"+to+" c"),ta.push(ts+" -"+to+" "+to+" -"+ts+" "+to+" 0 c"),ta.push("f"),ta.push("Q"),ti.stream=ta.join("\n"),ti},YesPushDown:function(tn){var ti=V(tn);ti.scope=tn.scope;var ta=[],to=tP.internal.getWidth(tn)<=tP.internal.getHeight(tn)?tP.internal.getWidth(tn)/4:tP.internal.getHeight(tn)/4,ts=Number((2*(to=Number((.9*to).toFixed(5)))).toFixed(5)),tc=Number((ts*tP.internal.Bezier_C).toFixed(5)),tu=Number((to*tP.internal.Bezier_C).toFixed(5));return ta.push("0.749023 g"),ta.push("q"),ta.push("1 0 0 1 "+z(tP.internal.getWidth(tn)/2)+" "+z(tP.internal.getHeight(tn)/2)+" cm"),ta.push(ts+" 0 m"),ta.push(ts+" "+tc+" "+tc+" "+ts+" 0 "+ts+" c"),ta.push("-"+tc+" "+ts+" -"+ts+" "+tc+" -"+ts+" 0 c"),ta.push("-"+ts+" -"+tc+" -"+tc+" -"+ts+" 0 -"+ts+" c"),ta.push(tc+" -"+ts+" "+ts+" -"+tc+" "+ts+" 0 c"),ta.push("f"),ta.push("Q"),ta.push("0 g"),ta.push("q"),ta.push("1 0 0 1 "+z(tP.internal.getWidth(tn)/2)+" "+z(tP.internal.getHeight(tn)/2)+" cm"),ta.push(to+" 0 m"),ta.push(to+" "+tu+" "+tu+" "+to+" 0 "+to+" c"),ta.push("-"+tu+" "+to+" -"+to+" "+tu+" -"+to+" 0 c"),ta.push("-"+to+" -"+tu+" -"+tu+" -"+to+" 0 -"+to+" c"),ta.push(tu+" -"+to+" "+to+" -"+tu+" "+to+" 0 c"),ta.push("f"),ta.push("Q"),ti.stream=ta.join("\n"),ti},OffPushDown:function(tn){var ti=V(tn);ti.scope=tn.scope;var ta=[],to=tP.internal.getWidth(tn)<=tP.internal.getHeight(tn)?tP.internal.getWidth(tn)/4:tP.internal.getHeight(tn)/4,ts=Number((2*(to=Number((.9*to).toFixed(5)))).toFixed(5)),tc=Number((ts*tP.internal.Bezier_C).toFixed(5));return ta.push("0.749023 g"),ta.push("q"),ta.push("1 0 0 1 "+z(tP.internal.getWidth(tn)/2)+" "+z(tP.internal.getHeight(tn)/2)+" cm"),ta.push(ts+" 0 m"),ta.push(ts+" "+tc+" "+tc+" "+ts+" 0 "+ts+" c"),ta.push("-"+tc+" "+ts+" -"+ts+" "+tc+" -"+ts+" 0 c"),ta.push("-"+ts+" -"+tc+" -"+tc+" -"+ts+" 0 -"+ts+" c"),ta.push(tc+" -"+ts+" "+ts+" -"+tc+" "+ts+" 0 c"),ta.push("f"),ta.push("Q"),ti.stream=ta.join("\n"),ti}},Cross:{createAppearanceStream:function(tn){var ti={D:{Off:tP.RadioButton.Cross.OffPushDown},N:{}};return ti.N[tn]=tP.RadioButton.Cross.YesNormal,ti.D[tn]=tP.RadioButton.Cross.YesPushDown,ti},getCA:function(){return"8"},YesNormal:function(tn){var ti=V(tn);ti.scope=tn.scope;var ta=[],to=tP.internal.calculateCross(tn);return ta.push("q"),ta.push("1 1 "+U(tP.internal.getWidth(tn)-2)+" "+U(tP.internal.getHeight(tn)-2)+" re"),ta.push("W"),ta.push("n"),ta.push(U(to.x1.x)+" "+U(to.x1.y)+" m"),ta.push(U(to.x2.x)+" "+U(to.x2.y)+" l"),ta.push(U(to.x4.x)+" "+U(to.x4.y)+" m"),ta.push(U(to.x3.x)+" "+U(to.x3.y)+" l"),ta.push("s"),ta.push("Q"),ti.stream=ta.join("\n"),ti},YesPushDown:function(tn){var ti=V(tn);ti.scope=tn.scope;var ta=tP.internal.calculateCross(tn),to=[];return to.push("0.749023 g"),to.push("0 0 "+U(tP.internal.getWidth(tn))+" "+U(tP.internal.getHeight(tn))+" re"),to.push("f"),to.push("q"),to.push("1 1 "+U(tP.internal.getWidth(tn)-2)+" "+U(tP.internal.getHeight(tn)-2)+" re"),to.push("W"),to.push("n"),to.push(U(ta.x1.x)+" "+U(ta.x1.y)+" m"),to.push(U(ta.x2.x)+" "+U(ta.x2.y)+" l"),to.push(U(ta.x4.x)+" "+U(ta.x4.y)+" m"),to.push(U(ta.x3.x)+" "+U(ta.x3.y)+" l"),to.push("s"),to.push("Q"),ti.stream=to.join("\n"),ti},OffPushDown:function(tn){var ti=V(tn);ti.scope=tn.scope;var ta=[];return ta.push("0.749023 g"),ta.push("0 0 "+U(tP.internal.getWidth(tn))+" "+U(tP.internal.getHeight(tn))+" re"),ta.push("f"),ti.stream=ta.join("\n"),ti}}},createDefaultAppearanceStream:function(tn){var ti=tn.scope.internal.getFont(tn.fontName,tn.fontStyle).id,ta=tn.scope.__private__.encodeColorString(tn.color);return"/"+ti+" "+tn.fontSize+" Tf "+ta}};tP.internal={Bezier_C:.551915024494,calculateCross:function(tn){var ti=tP.internal.getWidth(tn),ta=tP.internal.getHeight(tn),to=Math.min(ti,ta);return{x1:{x:(ti-to)/2,y:(ta-to)/2+to},x2:{x:(ti-to)/2+to,y:(ta-to)/2},x3:{x:(ti-to)/2,y:(ta-to)/2},x4:{x:(ti-to)/2+to,y:(ta-to)/2+to}}}},tP.internal.getWidth=function(tn){var ti=0;return"object"===(0,to.Z)(tn)&&(ti=W(tn.Rect[2])),ti},tP.internal.getHeight=function(tn){var ti=0;return"object"===(0,to.Z)(tn)&&(ti=W(tn.Rect[3])),ti};var tk=tm.addField=function(tn){if(at(this,tn),!(tn instanceof lt))throw Error("Invalid argument passed to jsPDF.addField.");return tn.scope.internal.acroformPlugin.printedOut&&(tn.scope.internal.acroformPlugin.printedOut=!1,tn.scope.internal.acroformPlugin.acroFormDictionaryRoot=null),tn.scope.internal.acroformPlugin.acroFormDictionaryRoot.Fields.push(tn),tn.page=tn.scope.internal.getCurrentPageInfo().pageNumber,this};tm.AcroFormChoiceField=ft,tm.AcroFormListBox=dt,tm.AcroFormComboBox=pt,tm.AcroFormEditBox=gt,tm.AcroFormButton=mt,tm.AcroFormPushButton=vt,tm.AcroFormRadioButton=bt,tm.AcroFormCheckBox=wt,tm.AcroFormTextField=Nt,tm.AcroFormPasswordField=Lt,tm.AcroFormAppearance=tP,tm.AcroForm={ChoiceField:ft,ListBox:dt,ComboBox:pt,EditBox:gt,Button:mt,PushButton:vt,RadioButton:bt,CheckBox:wt,TextField:Nt,PasswordField:Lt,Appearance:tP},E.AcroForm={ChoiceField:ft,ListBox:dt,ComboBox:pt,EditBox:gt,Button:mt,PushButton:vt,RadioButton:bt,CheckBox:wt,TextField:Nt,PasswordField:Lt,Appearance:tP};var tF=E.AcroForm;function _t(tn){return tn.reduce(function(tn,ti,ta){return tn[ti]=ta,tn},{})}(tq=E.API).__addimage__={},tD="UNKNOWN",tR={PNG:[[137,80,78,71]],TIFF:[[77,77,0,42],[73,73,42,0]],JPEG:[[255,216,255,224,void 0,void 0,74,70,73,70,0],[255,216,255,225,void 0,void 0,69,120,105,102,0,0],[255,216,255,219],[255,216,255,238]],JPEG2000:[[0,0,0,12,106,80,32,32]],GIF87a:[[71,73,70,56,55,97]],GIF89a:[[71,73,70,56,57,97]],WEBP:[[82,73,70,70,void 0,void 0,void 0,void 0,87,69,66,80]],BMP:[[66,77],[66,65],[67,73],[67,80],[73,67],[80,84]]},tT=tq.__addimage__.getImageFileTypeByImageData=function(tn,ti){var ta,to,ts,tc,tu,th=tD;if("RGBA"===(ti=ti||tD)||void 0!==tn.data&&tn.data instanceof Uint8ClampedArray&&"height"in tn&&"width"in tn)return"RGBA";if(t4(tn))for(tu in tR)for(ts=tR[tu],ta=0;ta<ts.length;ta+=1){for(tc=!0,to=0;to<ts[ta].length;to+=1)if(void 0!==ts[ta][to]&&ts[ta][to]!==tn[to]){tc=!1;break}if(!0===tc){th=tu;break}}else for(tu in tR)for(ts=tR[tu],ta=0;ta<ts.length;ta+=1){for(tc=!0,to=0;to<ts[ta].length;to+=1)if(void 0!==ts[ta][to]&&ts[ta][to]!==tn.charCodeAt(to)){tc=!1;break}if(!0===tc){th=tu;break}}return th===tD&&ti!==tD&&(th=ti),th},tU=function t(tn){for(var ti=this.internal.write,ta=this.internal.putStream,to=(0,this.internal.getFilters)();-1!==to.indexOf("FlateEncode");)to.splice(to.indexOf("FlateEncode"),1);tn.objectId=this.internal.newObject();var ts=[];if(ts.push({key:"Type",value:"/XObject"}),ts.push({key:"Subtype",value:"/Image"}),ts.push({key:"Width",value:tn.width}),ts.push({key:"Height",value:tn.height}),tn.colorSpace===tQ.INDEXED?ts.push({key:"ColorSpace",value:"[/Indexed /DeviceRGB "+(tn.palette.length/3-1)+" "+("sMask"in tn&&void 0!==tn.sMask?tn.objectId+2:tn.objectId+1)+" 0 R]"}):(ts.push({key:"ColorSpace",value:"/"+tn.colorSpace}),tn.colorSpace===tQ.DEVICE_CMYK&&ts.push({key:"Decode",value:"[1 0 1 0 1 0 1 0]"})),ts.push({key:"BitsPerComponent",value:tn.bitsPerComponent}),"decodeParameters"in tn&&void 0!==tn.decodeParameters&&ts.push({key:"DecodeParms",value:"<<"+tn.decodeParameters+">>"}),"transparency"in tn&&Array.isArray(tn.transparency)){for(var tc="",tu=0,th=tn.transparency.length;tu<th;tu++)tc+=tn.transparency[tu]+" "+tn.transparency[tu]+" ";ts.push({key:"Mask",value:"["+tc+"]"})}void 0!==tn.sMask&&ts.push({key:"SMask",value:tn.objectId+1+" 0 R"});var tl=void 0!==tn.filter?["/"+tn.filter]:void 0;if(ta({data:tn.data,additionalKeyValues:ts,alreadyAppliedFilters:tl,objectId:tn.objectId}),ti("endobj"),"sMask"in tn&&void 0!==tn.sMask){var tf="/Predictor "+tn.predictor+" /Colors 1 /BitsPerComponent "+tn.bitsPerComponent+" /Columns "+tn.width,td={width:tn.width,height:tn.height,colorSpace:"DeviceGray",bitsPerComponent:tn.bitsPerComponent,decodeParameters:tf,data:tn.sMask};"filter"in tn&&(td.filter=tn.filter),t.call(this,td)}if(tn.colorSpace===tQ.INDEXED){var tp=this.internal.newObject();ta({data:t8(new Uint8Array(tn.palette)),objectId:tp}),ti("endobj")}},tz=function(){var tn=this.internal.collections.addImage_images;for(var ti in tn)tU.call(this,tn[ti])},tV=function(){var tn,ti=this.internal.collections.addImage_images,ta=this.internal.write;for(var to in ti)ta("/I"+(tn=ti[to]).index,tn.objectId,"0","R")},tH=function(){this.internal.collections.addImage_images||(this.internal.collections.addImage_images={},this.internal.events.subscribe("putResources",tz),this.internal.events.subscribe("putXobjectDict",tV))},tW=function(){var tn=this.internal.collections.addImage_images;return tH.call(this),tn},tG=function(){return Object.keys(this.internal.collections.addImage_images).length},tJ=function(tn){return"function"==typeof tq["process"+tn.toUpperCase()]},tY=function(tn){return"object"===(0,to.Z)(tn)&&1===tn.nodeType},tX=function(tn,ti){if("IMG"===tn.nodeName&&tn.hasAttribute("src")){var ta,to=""+tn.getAttribute("src");if(0===to.indexOf("data:image/"))return th(unescape(to).split("base64,").pop());var ts=tq.loadFile(to,!0);if(void 0!==ts)return ts}if("CANVAS"===tn.nodeName){if(0===tn.width||0===tn.height)throw Error("Given canvas must have data. Canvas width: "+tn.width+", height: "+tn.height);switch(ti){case"PNG":ta="image/png";break;case"WEBP":ta="image/webp";break;default:ta="image/jpeg"}return th(tn.toDataURL(ta,1).split("base64,").pop())}},tZ=function(tn){var ti=this.internal.collections.addImage_images;if(ti){for(var ta in ti)if(tn===ti[ta].alias)return ti[ta]}},tK=function(tn,ti,ta){return tn||ti||(tn=-96,ti=-96),tn<0&&(tn=-1*ta.width*72/tn/this.internal.scaleFactor),ti<0&&(ti=-1*ta.height*72/ti/this.internal.scaleFactor),0===tn&&(tn=ti*ta.width/ta.height),0===ti&&(ti=tn*ta.height/ta.width),[tn,ti]},t$=function(tn,ti,ta,to,ts,tc){var tu=tK.call(this,ta,to,ts),th=this.internal.getCoordinateString,tl=this.internal.getVerticalCoordinateString,tf=tW.call(this);if(ta=tu[0],to=tu[1],tf[ts.index]=ts,tc)var td=Math.cos(tc*=Math.PI/180),tp=Math.sin(tc),d=function(tn){return tn.toFixed(4)},tg=[d(td),d(tp),d(-1*tp),d(td),0,0,"cm"];this.internal.write("q"),tc?(this.internal.write([1,"0","0",1,th(tn),tl(ti+to),"cm"].join(" ")),this.internal.write(tg.join(" ")),this.internal.write([th(ta),"0","0",th(to),"0","0","cm"].join(" "))):this.internal.write([th(ta),"0","0",th(to),th(tn),tl(ti+to),"cm"].join(" ")),this.isAdvancedAPI()&&this.internal.write("1 0 0 -1 0 0 cm"),this.internal.write("/I"+ts.index+" Do"),this.internal.write("Q")},tQ=tq.color_spaces={DEVICE_RGB:"DeviceRGB",DEVICE_GRAY:"DeviceGray",DEVICE_CMYK:"DeviceCMYK",CAL_GREY:"CalGray",CAL_RGB:"CalRGB",LAB:"Lab",ICC_BASED:"ICCBased",INDEXED:"Indexed",PATTERN:"Pattern",SEPARATION:"Separation",DEVICE_N:"DeviceN"},tq.decode={DCT_DECODE:"DCTDecode",FLATE_DECODE:"FlateDecode",LZW_DECODE:"LZWDecode",JPX_DECODE:"JPXDecode",JBIG2_DECODE:"JBIG2Decode",ASCII85_DECODE:"ASCII85Decode",ASCII_HEX_DECODE:"ASCIIHexDecode",RUN_LENGTH_DECODE:"RunLengthDecode",CCITT_FAX_DECODE:"CCITTFaxDecode"},t1=tq.image_compression={NONE:"NONE",FAST:"FAST",MEDIUM:"MEDIUM",SLOW:"SLOW"},t2=tq.__addimage__.sHashCode=function(tn){var ti,ta,to=0;if("string"==typeof tn)for(ta=tn.length,ti=0;ti<ta;ti++)to=(to<<5)-to+tn.charCodeAt(ti)|0;else if(t4(tn))for(ta=tn.byteLength/2,ti=0;ti<ta;ti++)to=(to<<5)-to+tn[ti]|0;return to},t5=tq.__addimage__.validateStringAsBase64=function(tn){(tn=tn||"").toString().trim();var ti=!0;return 0===tn.length&&(ti=!1),tn.length%4!=0&&(ti=!1),!1===/^[A-Za-z0-9+/]+$/.test(tn.substr(0,tn.length-2))&&(ti=!1),!1===/^[A-Za-z0-9/][A-Za-z0-9+/]|[A-Za-z0-9+/]=|==$/.test(tn.substr(-2))&&(ti=!1),ti},t0=tq.__addimage__.extractImageFromDataUrl=function(tn){var ti=(tn=tn||"").split("base64,"),ta=null;if(2===ti.length){var to=/^data:(\w*\/\w*);*(charset=(?!charset=)[\w=-]*)*;*$/.exec(ti[0]);Array.isArray(to)&&(ta={mimeType:to[1],charset:to[2],data:ti[1]})}return ta},t3=tq.__addimage__.supportsArrayBuffer=function(){return"undefined"!=typeof ArrayBuffer&&"undefined"!=typeof Uint8Array},tq.__addimage__.isArrayBuffer=function(tn){return t3()&&tn instanceof ArrayBuffer},t4=tq.__addimage__.isArrayBufferView=function(tn){return t3()&&"undefined"!=typeof Uint32Array&&(tn instanceof Int8Array||tn instanceof Uint8Array||"undefined"!=typeof Uint8ClampedArray&&tn instanceof Uint8ClampedArray||tn instanceof Int16Array||tn instanceof Uint16Array||tn instanceof Int32Array||tn instanceof Uint32Array||tn instanceof Float32Array||tn instanceof Float64Array)},t6=tq.__addimage__.binaryStringToUint8Array=function(tn){for(var ti=tn.length,ta=new Uint8Array(ti),to=0;to<ti;to++)ta[to]=tn.charCodeAt(to);return ta},t8=tq.__addimage__.arrayBufferToBinaryString=function(tn){for(var ti="",ta=t4(tn)?tn:new Uint8Array(tn),to=0;to<ta.length;to+=8192)ti+=String.fromCharCode.apply(null,ta.subarray(to,to+8192));return ti},tq.addImage=function(){if("number"==typeof arguments[1]?(ti=tD,ta=arguments[1],ts=arguments[2],tc=arguments[3],tu=arguments[4],th=arguments[5],tl=arguments[6],tf=arguments[7]):(ti=arguments[1],ta=arguments[2],ts=arguments[3],tc=arguments[4],tu=arguments[5],th=arguments[6],tl=arguments[7],tf=arguments[8]),"object"===(0,to.Z)(tn=arguments[0])&&!tY(tn)&&"imageData"in tn){var tn,ti,ta,ts,tc,tu,th,tl,tf,td=tn;tn=td.imageData,ti=td.format||ti||tD,ta=td.x||ta||0,ts=td.y||ts||0,tc=td.w||td.width||tc,tu=td.h||td.height||tu,th=td.alias||th,tl=td.compression||tl,tf=td.rotation||td.angle||tf}var tp=this.internal.getFilters();if(void 0===tl&&-1!==tp.indexOf("FlateEncode")&&(tl="SLOW"),isNaN(ta)||isNaN(ts))throw Error("Invalid coordinates passed to jsPDF.addImage");tH.call(this);var tg=t7.call(this,tn,ti,th,tl);return t$.call(this,ta,ts,tc,tu,tg,tf),this},t7=function(tn,ti,ta,to){if("string"==typeof tn&&tT(tn)===tD){var ts,tc,tu,th,tl,tf=t9(tn=unescape(tn),!1);(""!==tf||void 0!==(tf=tq.loadFile(tn,!0)))&&(tn=tf)}if(tY(tn)&&(tn=tX(tn,ti)),!tJ(ti=tT(tn,ti)))throw Error("addImage does not support files of type '"+ti+"', please ensure that a plugin for '"+ti+"' support is added.");if((null==(tu=ta)||0===tu.length)&&(ta="string"==typeof(th=tn)||t4(th)?t2(th):t4(th.data)?t2(th.data):null),(ts=tZ.call(this,ta))||(t3()&&(tn instanceof Uint8Array||"RGBA"===ti||(tc=tn,tn=t6(tn))),ts=this["process"+ti.toUpperCase()](tn,tG.call(this),ta,((tl=to)&&"string"==typeof tl&&(tl=tl.toUpperCase()),tl in tq.image_compression?tl:t1.NONE),tc)),!ts)throw Error("An unknown error occurred whilst processing the image.");return ts},t9=tq.__addimage__.convertBase64ToBinaryString=function(tn,ti){ti="boolean"!=typeof ti||ti;var ta,to,ts="";if("string"==typeof tn){to=null!==(ta=t0(tn))?ta.data:tn;try{ts=th(to)}catch(tn){if(ti)throw t5(to)?Error("atob-Error in jsPDF.convertBase64ToBinaryString "+tn.message):Error("Supplied Data is not a valid base64-String jsPDF.convertBase64ToBinaryString ")}}return ts},tq.getImageProperties=function(tn){var ti,ta,to="";if(tY(tn)&&(tn=tX(tn)),"string"==typeof tn&&tT(tn)===tD&&(""===(to=t9(tn,!1))&&(to=tq.loadFile(tn)||""),tn=to),!tJ(ta=tT(tn)))throw Error("addImage does not support files of type '"+ta+"', please ensure that a plugin for '"+ta+"' support is added.");if(!t3()||tn instanceof Uint8Array||(tn=t6(tn)),!(ti=this["process"+ta.toUpperCase()](tn)))throw Error("An unknown error occurred whilst processing the image");return ti.fileType=ta,ti},en=E.API,ei=function(tn){if(void 0!==tn&&""!=tn)return!0},E.API.events.push(["addPage",function(tn){this.internal.getPageInfo(tn.pageNumber).pageContext.annotations=[]}]),en.events.push(["putPage",function(tn){for(var ti,ta,to,ts=this.internal.getCoordinateString,tc=this.internal.getVerticalCoordinateString,tu=this.internal.getPageInfoByObjId(tn.objId),th=tn.pageContext.annotations,tl=!1,tf=0;tf<th.length&&!tl;tf++)switch((ti=th[tf]).type){case"link":(ei(ti.options.url)||ei(ti.options.pageNumber))&&(tl=!0);break;case"reference":case"text":case"freetext":tl=!0}if(0!=tl){this.internal.write("/Annots [");for(var td=0;td<th.length;td++){ti=th[td];var tp=this.internal.pdfEscape,tg=this.internal.getEncryptor(tn.objId);switch(ti.type){case"reference":this.internal.write(" "+ti.object.objId+" 0 R ");break;case"text":var tm=this.internal.newAdditionalObject(),tv=this.internal.newAdditionalObject(),tb=this.internal.getEncryptor(tm.objId),ty=ti.title||"Note";to="<</Type /Annot /Subtype /Text "+(ta="/Rect ["+ts(ti.bounds.x)+" "+tc(ti.bounds.y+ti.bounds.h)+" "+ts(ti.bounds.x+ti.bounds.w)+" "+tc(ti.bounds.y)+"] ")+"/Contents ("+tp(tb(ti.contents))+") /Popup "+tv.objId+" 0 R /P "+tu.objId+" 0 R /T ("+tp(tb(ty))+") >>",tm.content=to;var tw=tm.objId+" 0 R";to="<</Type /Annot /Subtype /Popup "+(ta="/Rect ["+ts(ti.bounds.x+30)+" "+tc(ti.bounds.y+ti.bounds.h)+" "+ts(ti.bounds.x+ti.bounds.w+30)+" "+tc(ti.bounds.y)+"] ")+" /Parent "+tw,ti.open&&(to+=" /Open true"),to+=" >>",tv.content=to,this.internal.write(tm.objId,"0 R",tv.objId,"0 R");break;case"freetext":ta="/Rect ["+ts(ti.bounds.x)+" "+tc(ti.bounds.y)+" "+ts(ti.bounds.x+ti.bounds.w)+" "+tc(ti.bounds.y+ti.bounds.h)+"] ";var tN=ti.color||"#000000";to="<</Type /Annot /Subtype /FreeText "+ta+"/Contents ("+tp(tg(ti.contents))+") /DS(font: Helvetica,sans-serif 12.0pt; text-align:left; color:#"+tN+") /Border [0 0 0] >>",this.internal.write(to);break;case"link":if(ti.options.name){var tL=this.annotations._nameMap[ti.options.name];ti.options.pageNumber=tL.page,ti.options.top=tL.y}else ti.options.top||(ti.options.top=0);if(ta="/Rect ["+ti.finalBounds.x+" "+ti.finalBounds.y+" "+ti.finalBounds.w+" "+ti.finalBounds.h+"] ",to="",ti.options.url)to="<</Type /Annot /Subtype /Link "+ta+"/Border [0 0 0] /A <</S /URI /URI ("+tp(tg(ti.options.url))+") >>";else if(ti.options.pageNumber)switch(to="<</Type /Annot /Subtype /Link "+ta+"/Border [0 0 0] /Dest ["+this.internal.getPageInfo(ti.options.pageNumber).objId+" 0 R",ti.options.magFactor=ti.options.magFactor||"XYZ",ti.options.magFactor){case"Fit":to+=" /Fit]";break;case"FitH":to+=" /FitH "+ti.options.top+"]";break;case"FitV":ti.options.left=ti.options.left||0,to+=" /FitV "+ti.options.left+"]";break;default:var tx=tc(ti.options.top);ti.options.left=ti.options.left||0,void 0===ti.options.zoom&&(ti.options.zoom=0),to+=" /XYZ "+ti.options.left+" "+tx+" "+ti.options.zoom+"]"}""!=to&&(to+=" >>",this.internal.write(to))}}this.internal.write("]")}}]),en.createAnnotation=function(tn){var ti=this.internal.getCurrentPageInfo();switch(tn.type){case"link":this.link(tn.bounds.x,tn.bounds.y,tn.bounds.w,tn.bounds.h,tn);break;case"text":case"freetext":ti.pageContext.annotations.push(tn)}},en.link=function(tn,ti,ta,to,ts){var tc=this.internal.getCurrentPageInfo(),tu=this.internal.getCoordinateString,th=this.internal.getVerticalCoordinateString;tc.pageContext.annotations.push({finalBounds:{x:tu(tn),y:th(ti),w:tu(tn+ta),h:th(ti+to)},options:ts,type:"link"})},en.textWithLink=function(tn,ti,ta,to){var ts,tc,tu=this.getTextWidth(tn),th=this.internal.getLineHeight()/this.internal.scaleFactor;return void 0!==to.maxWidth?(tc=to.maxWidth,ts=Math.ceil(th*this.splitTextToSize(tn,tc).length)):(tc=tu,ts=th),this.text(tn,ti,ta,to),ta+=.2*th,"center"===to.align&&(ti-=tu/2),"right"===to.align&&(ti-=tu),this.link(ti,ta-th,tc,ts,to),tu},en.getTextWidth=function(tn){var ti=this.internal.getFontSize();return this.getStringUnitWidth(tn)*ti/this.internal.scaleFactor},/**
 * @license
 * Copyright (c) 2017 Aras Abbasi
 *
 * Licensed under the MIT License.
 * http://opensource.org/licenses/mit-license
 */function(tn){var ti={1569:[65152],1570:[65153,65154],1571:[65155,65156],1572:[65157,65158],1573:[65159,65160],1574:[65161,65162,65163,65164],1575:[65165,65166],1576:[65167,65168,65169,65170],1577:[65171,65172],1578:[65173,65174,65175,65176],1579:[65177,65178,65179,65180],1580:[65181,65182,65183,65184],1581:[65185,65186,65187,65188],1582:[65189,65190,65191,65192],1583:[65193,65194],1584:[65195,65196],1585:[65197,65198],1586:[65199,65200],1587:[65201,65202,65203,65204],1588:[65205,65206,65207,65208],1589:[65209,65210,65211,65212],1590:[65213,65214,65215,65216],1591:[65217,65218,65219,65220],1592:[65221,65222,65223,65224],1593:[65225,65226,65227,65228],1594:[65229,65230,65231,65232],1601:[65233,65234,65235,65236],1602:[65237,65238,65239,65240],1603:[65241,65242,65243,65244],1604:[65245,65246,65247,65248],1605:[65249,65250,65251,65252],1606:[65253,65254,65255,65256],1607:[65257,65258,65259,65260],1608:[65261,65262],1609:[65263,65264,64488,64489],1610:[65265,65266,65267,65268],1649:[64336,64337],1655:[64477],1657:[64358,64359,64360,64361],1658:[64350,64351,64352,64353],1659:[64338,64339,64340,64341],1662:[64342,64343,64344,64345],1663:[64354,64355,64356,64357],1664:[64346,64347,64348,64349],1667:[64374,64375,64376,64377],1668:[64370,64371,64372,64373],1670:[64378,64379,64380,64381],1671:[64382,64383,64384,64385],1672:[64392,64393],1676:[64388,64389],1677:[64386,64387],1678:[64390,64391],1681:[64396,64397],1688:[64394,64395],1700:[64362,64363,64364,64365],1702:[64366,64367,64368,64369],1705:[64398,64399,64400,64401],1709:[64467,64468,64469,64470],1711:[64402,64403,64404,64405],1713:[64410,64411,64412,64413],1715:[64406,64407,64408,64409],1722:[64414,64415],1723:[64416,64417,64418,64419],1726:[64426,64427,64428,64429],1728:[64420,64421],1729:[64422,64423,64424,64425],1733:[64480,64481],1734:[64473,64474],1735:[64471,64472],1736:[64475,64476],1737:[64482,64483],1739:[64478,64479],1740:[64508,64509,64510,64511],1744:[64484,64485,64486,64487],1746:[64430,64431],1747:[64432,64433]},ta={65247:{65154:65269,65156:65271,65160:65273,65166:65275},65248:{65154:65270,65156:65272,65160:65274,65166:65276},65165:{65247:{65248:{65258:65010}}},1617:{1612:64606,1613:64607,1614:64608,1615:64609,1616:64610}},to={1612:64606,1613:64607,1614:64608,1615:64609,1616:64610},ts=[1570,1571,1573,1575];tn.__arabicParser__={};var tc=tn.__arabicParser__.isInArabicSubstitutionA=function(tn){return void 0!==ti[tn.charCodeAt(0)]},tu=tn.__arabicParser__.isArabicLetter=function(tn){return"string"==typeof tn&&/^[\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF]+$/.test(tn)},th=tn.__arabicParser__.isArabicEndLetter=function(tn){return tu(tn)&&tc(tn)&&ti[tn.charCodeAt(0)].length<=2},tl=tn.__arabicParser__.isArabicAlfLetter=function(tn){return tu(tn)&&ts.indexOf(tn.charCodeAt(0))>=0};tn.__arabicParser__.arabicLetterHasIsolatedForm=function(tn){return tu(tn)&&tc(tn)&&ti[tn.charCodeAt(0)].length>=1};var tf=tn.__arabicParser__.arabicLetterHasFinalForm=function(tn){return tu(tn)&&tc(tn)&&ti[tn.charCodeAt(0)].length>=2};tn.__arabicParser__.arabicLetterHasInitialForm=function(tn){return tu(tn)&&tc(tn)&&ti[tn.charCodeAt(0)].length>=3};var td=tn.__arabicParser__.arabicLetterHasMedialForm=function(tn){return tu(tn)&&tc(tn)&&4==ti[tn.charCodeAt(0)].length},tp=tn.__arabicParser__.resolveLigatures=function(tn){var ti=0,to=ta,ts="",tc=0;for(ti=0;ti<tn.length;ti+=1)void 0!==to[tn.charCodeAt(ti)]?(tc++,"number"==typeof(to=to[tn.charCodeAt(ti)])&&(ts+=String.fromCharCode(to),to=ta,tc=0),ti===tn.length-1&&(to=ta,ts+=tn.charAt(ti-(tc-1)),ti-=tc-1,tc=0)):(to=ta,ts+=tn.charAt(ti-tc),ti-=tc,tc=0);return ts};tn.__arabicParser__.isArabicDiacritic=function(tn){return void 0!==tn&&void 0!==to[tn.charCodeAt(0)]};var tg=tn.__arabicParser__.getCorrectForm=function(tn,ti,ta){return tu(tn)?!1===tc(tn)?-1:!tf(tn)||!tu(ti)&&!tu(ta)||!tu(ta)&&th(ti)||th(tn)&&!tu(ti)||th(tn)&&tl(ti)||th(tn)&&th(ti)?0:td(tn)&&tu(ti)&&!th(ti)&&tu(ta)&&tf(ta)?3:th(tn)||!tu(ta)?1:2:-1},d=function(tn){var ta=0,to=0,ts=0,tc="",th="",tl="",tf=(tn=tn||"").split("\\s+"),td=[];for(ta=0;ta<tf.length;ta+=1){for(td.push(""),to=0;to<tf[ta].length;to+=1)tc=tf[ta][to],th=tf[ta][to-1],tl=tf[ta][to+1],tu(tc)?(ts=tg(tc,th,tl),td[ta]+=-1!==ts?String.fromCharCode(ti[tc.charCodeAt(0)][ts]):tc):td[ta]+=tc;td[ta]=tp(td[ta])}return td.join(" ")},tm=tn.__arabicParser__.processArabic=tn.processArabic=function(){var tn,ti="string"==typeof arguments[0]?arguments[0]:arguments[0].text,ta=[];if(Array.isArray(ti)){var to=0;for(ta=[],to=0;to<ti.length;to+=1)Array.isArray(ti[to])?ta.push([d(ti[to][0]),ti[to][1],ti[to][2]]):ta.push([d(ti[to])]);tn=ta}else tn=d(ti);return"string"==typeof arguments[0]?tn:(arguments[0].text=tn,arguments[0])};tn.events.push(["preProcessText",tm])}(E.API),E.API.autoPrint=function(tn){var ti;return((tn=tn||{}).variant=tn.variant||"non-conform","javascript"===tn.variant)?this.addJS("print({});"):(this.internal.events.subscribe("postPutResources",function(){ti=this.internal.newObject(),this.internal.out("<<"),this.internal.out("/S /Named"),this.internal.out("/Type /Action"),this.internal.out("/N /Print"),this.internal.out(">>"),this.internal.out("endobj")}),this.internal.events.subscribe("putCatalog",function(){this.internal.out("/OpenAction "+ti+" 0 R")})),this},ea=E.API,(eo=function(){var tn=void 0;Object.defineProperty(this,"pdf",{get:function(){return tn},set:function(ti){tn=ti}});var ti=150;Object.defineProperty(this,"width",{get:function(){return ti},set:function(tn){ti=isNaN(tn)||!1===Number.isInteger(tn)||tn<0?150:tn,this.getContext("2d").pageWrapXEnabled&&(this.getContext("2d").pageWrapX=ti+1)}});var ta=300;Object.defineProperty(this,"height",{get:function(){return ta},set:function(tn){ta=isNaN(tn)||!1===Number.isInteger(tn)||tn<0?300:tn,this.getContext("2d").pageWrapYEnabled&&(this.getContext("2d").pageWrapY=ta+1)}});var to=[];Object.defineProperty(this,"childNodes",{get:function(){return to},set:function(tn){to=tn}});var ts={};Object.defineProperty(this,"style",{get:function(){return ts},set:function(tn){ts=tn}}),Object.defineProperty(this,"parentNode",{})}).prototype.getContext=function(tn,ti){var ta;if("2d"!==(tn=tn||"2d"))return null;for(ta in ti)this.pdf.context2d.hasOwnProperty(ta)&&(this.pdf.context2d[ta]=ti[ta]);return this.pdf.context2d._canvas=this,this.pdf.context2d},eo.prototype.toDataURL=function(){throw Error("toDataURL is not implemented.")},ea.events.push(["initialized",function(){this.canvas=new eo,this.canvas.pdf=this}]),es=E.API,ec={left:0,top:0,bottom:0,right:0},eu=!1,eh=function(){void 0===this.internal.__cell__&&(this.internal.__cell__={},this.internal.__cell__.padding=3,this.internal.__cell__.headerFunction=void 0,this.internal.__cell__.margins=Object.assign({},ec),this.internal.__cell__.margins.width=this.getPageWidth(),el.call(this))},el=function(){this.internal.__cell__.lastCell=new ef,this.internal.__cell__.pages=1},(ef=function(){var tn=arguments[0];Object.defineProperty(this,"x",{enumerable:!0,get:function(){return tn},set:function(ti){tn=ti}});var ti=arguments[1];Object.defineProperty(this,"y",{enumerable:!0,get:function(){return ti},set:function(tn){ti=tn}});var ta=arguments[2];Object.defineProperty(this,"width",{enumerable:!0,get:function(){return ta},set:function(tn){ta=tn}});var to=arguments[3];Object.defineProperty(this,"height",{enumerable:!0,get:function(){return to},set:function(tn){to=tn}});var ts=arguments[4];Object.defineProperty(this,"text",{enumerable:!0,get:function(){return ts},set:function(tn){ts=tn}});var tc=arguments[5];Object.defineProperty(this,"lineNumber",{enumerable:!0,get:function(){return tc},set:function(tn){tc=tn}});var tu=arguments[6];return Object.defineProperty(this,"align",{enumerable:!0,get:function(){return tu},set:function(tn){tu=tn}}),this}).prototype.clone=function(){return new ef(this.x,this.y,this.width,this.height,this.text,this.lineNumber,this.align)},ef.prototype.toArray=function(){return[this.x,this.y,this.width,this.height,this.text,this.lineNumber,this.align]},es.setHeaderFunction=function(tn){return eh.call(this),this.internal.__cell__.headerFunction="function"==typeof tn?tn:void 0,this},es.getTextDimensions=function(tn,ti){eh.call(this);var ta=(ti=ti||{}).fontSize||this.getFontSize(),to=ti.font||this.getFont(),ts=ti.scaleFactor||this.internal.scaleFactor,tc=0,tu=0,th=0,tl=this;if(!Array.isArray(tn)&&"string"!=typeof tn){if("number"!=typeof tn)throw Error("getTextDimensions expects text-parameter to be of type String or type Number or an Array of Strings.");tn=String(tn)}var tf=ti.maxWidth;tf>0?"string"==typeof tn?tn=this.splitTextToSize(tn,tf):"[object Array]"===Object.prototype.toString.call(tn)&&(tn=tn.reduce(function(tn,ti){return tn.concat(tl.splitTextToSize(ti,tf))},[])):tn=Array.isArray(tn)?tn:[tn];for(var td=0;td<tn.length;td++)tc<(th=this.getStringUnitWidth(tn[td],{font:to})*ta)&&(tc=th);return 0!==tc&&(tu=tn.length),{w:tc/=ts,h:Math.max((tu*ta*this.getLineHeightFactor()-ta*(this.getLineHeightFactor()-1))/ts,0)}},es.cellAddPage=function(){eh.call(this),this.addPage();var tn=this.internal.__cell__.margins||ec;return this.internal.__cell__.lastCell=new ef(tn.left,tn.top,void 0,void 0),this.internal.__cell__.pages+=1,this},ed=es.cell=function(){tn=arguments[0]instanceof ef?arguments[0]:new ef(arguments[0],arguments[1],arguments[2],arguments[3],arguments[4],arguments[5]),eh.call(this);var tn,ti=this.internal.__cell__.lastCell,ta=this.internal.__cell__.padding,to=this.internal.__cell__.margins||ec,ts=this.internal.__cell__.tableHeaderRow,tc=this.internal.__cell__.printHeaders;return void 0!==ti.lineNumber&&(ti.lineNumber===tn.lineNumber?(tn.x=(ti.x||0)+(ti.width||0),tn.y=ti.y||0):ti.y+ti.height+tn.height+to.bottom>this.getPageHeight()?(this.cellAddPage(),tn.y=to.top,tc&&ts&&(this.printHeaderRow(tn.lineNumber,!0),tn.y+=ts[0].height)):tn.y=ti.y+ti.height||tn.y),void 0!==tn.text[0]&&(this.rect(tn.x,tn.y,tn.width,tn.height,!0===eu?"FD":void 0),"right"===tn.align?this.text(tn.text,tn.x+tn.width-ta,tn.y+ta,{align:"right",baseline:"top"}):"center"===tn.align?this.text(tn.text,tn.x+tn.width/2,tn.y+ta,{align:"center",baseline:"top",maxWidth:tn.width-ta-ta}):this.text(tn.text,tn.x+ta,tn.y+ta,{align:"left",baseline:"top",maxWidth:tn.width-ta-ta})),this.internal.__cell__.lastCell=tn,this},es.table=function(tn,ti,ta,ts,tc){if(eh.call(this),!ta)throw Error("No data for PDF table.");var tu,th,tl,tf,td=[],tp=[],tg=[],tm={},tv={},tb=[],ty=[],tw=(tc=tc||{}).autoSize||!1,tN=!1!==tc.printHeaders,tL=tc.css&&void 0!==tc.css["font-size"]?16*tc.css["font-size"]:tc.fontSize||12,tx=tc.margins||Object.assign({width:this.getPageWidth()},ec),tA="number"==typeof tc.padding?tc.padding:3,tS=tc.headerBackgroundColor||"#c8c8c8",t_=tc.headerTextColor||"#000";if(el.call(this),this.internal.__cell__.printHeaders=tN,this.internal.__cell__.margins=tx,this.internal.__cell__.table_font_size=tL,this.internal.__cell__.padding=tA,this.internal.__cell__.headerBackgroundColor=tS,this.internal.__cell__.headerTextColor=t_,this.setFontSize(tL),null==ts)tp=td=Object.keys(ta[0]),tg=td.map(function(){return"left"});else if(Array.isArray(ts)&&"object"===(0,to.Z)(ts[0]))for(td=ts.map(function(tn){return tn.name}),tp=ts.map(function(tn){return tn.prompt||tn.name||""}),tg=ts.map(function(tn){return tn.align||"left"}),tu=0;tu<ts.length;tu+=1)tv[ts[tu].name]=ts[tu].width*(19.049976/25.4);else Array.isArray(ts)&&"string"==typeof ts[0]&&(tp=td=ts,tg=td.map(function(){return"left"}));if(tw||Array.isArray(ts)&&"string"==typeof ts[0])for(tu=0;tu<td.length;tu+=1){for(tm[tf=td[tu]]=ta.map(function(tn){return tn[tf]}),this.setFont(void 0,"bold"),tb.push(this.getTextDimensions(tp[tu],{fontSize:this.internal.__cell__.table_font_size,scaleFactor:this.internal.scaleFactor}).w),th=tm[tf],this.setFont(void 0,"normal"),tl=0;tl<th.length;tl+=1)tb.push(this.getTextDimensions(th[tl],{fontSize:this.internal.__cell__.table_font_size,scaleFactor:this.internal.scaleFactor}).w);tv[tf]=Math.max.apply(null,tb)+tA+tA,tb=[]}if(tN){var tP={};for(tu=0;tu<td.length;tu+=1)tP[td[tu]]={},tP[td[tu]].text=tp[tu],tP[td[tu]].align=tg[tu];var tk=ep.call(this,tP,tv);ty=td.map(function(ta){return new ef(tn,ti,tv[ta],tk,tP[ta].text,void 0,tP[ta].align)}),this.setTableHeaderRow(ty),this.printHeaderRow(1,!1)}var tF=ts.reduce(function(tn,ti){return tn[ti.name]=ti.align,tn},{});for(tu=0;tu<ta.length;tu+=1){"rowStart"in tc&&tc.rowStart instanceof Function&&tc.rowStart({row:tu,data:ta[tu]},this);var tI=ep.call(this,ta[tu],tv);for(tl=0;tl<td.length;tl+=1){var tC=ta[tu][td[tl]];"cellStart"in tc&&tc.cellStart instanceof Function&&tc.cellStart({row:tu,col:tl,data:tC},this),ed.call(this,new ef(tn,ti,tv[td[tl]],tI,tC,tu+2,tF[td[tl]]))}}return this.internal.__cell__.table_x=tn,this.internal.__cell__.table_y=ti,this},ep=function(tn,ti){var ta=this.internal.__cell__.padding,to=this.internal.__cell__.table_font_size,ts=this.internal.scaleFactor;return Object.keys(tn).map(function(to){var ts=tn[to];return this.splitTextToSize(ts.hasOwnProperty("text")?ts.text:ts,ti[to]-ta-ta)},this).map(function(tn){return this.getLineHeightFactor()*tn.length*to/ts+ta+ta},this).reduce(function(tn,ti){return Math.max(tn,ti)},0)},es.setTableHeaderRow=function(tn){eh.call(this),this.internal.__cell__.tableHeaderRow=tn},es.printHeaderRow=function(tn,ti){if(eh.call(this),!this.internal.__cell__.tableHeaderRow)throw Error("Property tableHeaderRow does not exist.");if(eu=!0,"function"==typeof this.internal.__cell__.headerFunction){var ta,to=this.internal.__cell__.headerFunction(this,this.internal.__cell__.pages);this.internal.__cell__.lastCell=new ef(to[0],to[1],to[2],to[3],void 0,-1)}this.setFont(void 0,"bold");for(var ts=[],tc=0;tc<this.internal.__cell__.tableHeaderRow.length;tc+=1){ta=this.internal.__cell__.tableHeaderRow[tc].clone(),ti&&(ta.y=this.internal.__cell__.margins.top||0,ts.push(ta)),ta.lineNumber=tn;var tu=this.getTextColor();this.setTextColor(this.internal.__cell__.headerTextColor),this.setFillColor(this.internal.__cell__.headerBackgroundColor),ed.call(this,ta),this.setTextColor(tu)}ts.length>0&&this.setTableHeaderRow(ts),this.setFont(void 0,"normal"),eu=!1};var tI={italic:["italic","oblique","normal"],oblique:["oblique","italic","normal"],normal:["normal","oblique","italic"]},tC=["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded"],tj=_t(tC),tO=[100,200,300,400,500,600,700,800,900],tE=_t(tO);function jt(tn){var ti,ta,to,ts=tn.family.replace(/"|'/g,"").toLowerCase(),tc=tI[ta=(ta=tn.style)||"normal"]?ta:"normal",tu=(ti=tn.weight)?"number"==typeof ti?ti>=100&&ti<=900&&ti%100==0?ti:400:/^\d00$/.test(ti)?parseInt(ti):"bold"===ti?700:400:400,th="number"==typeof tj[to=(to=tn.stretch)||"normal"]?to:"normal";return{family:ts,style:tc,weight:tu,stretch:th,src:tn.src||[],ref:tn.ref||{name:ts,style:[th,tc,tu].join(" ")}}}function Ot(tn,ti,ta,to){var ts;for(ts=ta;ts>=0&&ts<ti.length;ts+=to)if(tn[ti[ts]])return tn[ti[ts]];for(ts=ta;ts>=0&&ts<ti.length;ts-=to)if(tn[ti[ts]])return tn[ti[ts]]}var tB={"sans-serif":"helvetica",fixed:"courier",monospace:"courier",terminal:"courier",cursive:"times",fantasy:"times",serif:"times"},tM={caption:"times",icon:"times",menu:"times","message-box":"times","small-caption":"times","status-bar":"times"};function Et(tn){return[tn.stretch,tn.style,tn.weight,tn.family].join(" ")}function Dt(tn){return tn.trimLeft()}var tq,tD,tR,tT,tU,tz,tV,tH,tW,tG,tJ,tY,tX,tZ,tK,t$,tQ,t1,t2,t5,t0,t3,t4,t6,t8,t7,t9,en,ei,ea,eo,es,ec,eu,eh,el,ef,ed,ep,eg,em,ev,eb=["times"];!function(tn){var ti,ta,ts,tc,th,tl,tf,td,tp,d=function(tn){return tn=tn||{},this.isStrokeTransparent=tn.isStrokeTransparent||!1,this.strokeOpacity=tn.strokeOpacity||1,this.strokeStyle=tn.strokeStyle||"#000000",this.fillStyle=tn.fillStyle||"#000000",this.isFillTransparent=tn.isFillTransparent||!1,this.fillOpacity=tn.fillOpacity||1,this.font=tn.font||"10px sans-serif",this.textBaseline=tn.textBaseline||"alphabetic",this.textAlign=tn.textAlign||"left",this.lineWidth=tn.lineWidth||1,this.lineJoin=tn.lineJoin||"miter",this.lineCap=tn.lineCap||"butt",this.path=tn.path||[],this.transform=void 0!==tn.transform?tn.transform.clone():new td,this.globalCompositeOperation=tn.globalCompositeOperation||"normal",this.globalAlpha=tn.globalAlpha||1,this.clip_path=tn.clip_path||[],this.currentPoint=tn.currentPoint||new tl,this.miterLimit=tn.miterLimit||10,this.lastPoint=tn.lastPoint||new tl,this.lineDashOffset=tn.lineDashOffset||0,this.lineDash=tn.lineDash||[],this.margin=tn.margin||[0,0,0,0],this.prevPageLastElemOffset=tn.prevPageLastElemOffset||0,this.ignoreClearRect="boolean"!=typeof tn.ignoreClearRect||tn.ignoreClearRect,this};tn.events.push(["initialized",function(){this.context2d=new p(this),ti=this.internal.f2,ta=this.internal.getCoordinateString,ts=this.internal.getVerticalCoordinateString,tc=this.internal.getHorizontalCoordinate,th=this.internal.getVerticalCoordinate,tl=this.internal.Point,tf=this.internal.Rectangle,td=this.internal.Matrix,tp=new d}]);var p=function(tn){Object.defineProperty(this,"canvas",{get:function(){return{parentNode:!1,style:!1}}}),Object.defineProperty(this,"pdf",{get:function(){return tn}});var ti=!1;Object.defineProperty(this,"pageWrapXEnabled",{get:function(){return ti},set:function(tn){ti=!!tn}});var ta=!1;Object.defineProperty(this,"pageWrapYEnabled",{get:function(){return ta},set:function(tn){ta=!!tn}});var to=0;Object.defineProperty(this,"posX",{get:function(){return to},set:function(tn){isNaN(tn)||(to=tn)}});var ts=0;Object.defineProperty(this,"posY",{get:function(){return ts},set:function(tn){isNaN(tn)||(ts=tn)}}),Object.defineProperty(this,"margin",{get:function(){return tp.margin},set:function(tn){var ti;"number"==typeof tn?ti=[tn,tn,tn,tn]:((ti=[,,,,])[0]=tn[0],ti[1]=tn.length>=2?tn[1]:ti[0],ti[2]=tn.length>=3?tn[2]:ti[0],ti[3]=tn.length>=4?tn[3]:ti[1]),tp.margin=ti}});var tc=!1;Object.defineProperty(this,"autoPaging",{get:function(){return tc},set:function(tn){tc=tn}});var tu=0;Object.defineProperty(this,"lastBreak",{get:function(){return tu},set:function(tn){tu=tn}});var th=[];Object.defineProperty(this,"pageBreaks",{get:function(){return th},set:function(tn){th=tn}}),Object.defineProperty(this,"ctx",{get:function(){return tp},set:function(tn){tn instanceof d&&(tp=tn)}}),Object.defineProperty(this,"path",{get:function(){return tp.path},set:function(tn){tp.path=tn}});var tl=[];Object.defineProperty(this,"ctxStack",{get:function(){return tl},set:function(tn){tl=tn}}),Object.defineProperty(this,"fillStyle",{get:function(){return this.ctx.fillStyle},set:function(tn){var ti;ti=g(tn),this.ctx.fillStyle=ti.style,this.ctx.isFillTransparent=0===ti.a,this.ctx.fillOpacity=ti.a,this.pdf.setFillColor(ti.r,ti.g,ti.b,{a:ti.a}),this.pdf.setTextColor(ti.r,ti.g,ti.b,{a:ti.a})}}),Object.defineProperty(this,"strokeStyle",{get:function(){return this.ctx.strokeStyle},set:function(tn){var ti=g(tn);this.ctx.strokeStyle=ti.style,this.ctx.isStrokeTransparent=0===ti.a,this.ctx.strokeOpacity=ti.a,0===ti.a?this.pdf.setDrawColor(255,255,255):(ti.a,this.pdf.setDrawColor(ti.r,ti.g,ti.b))}}),Object.defineProperty(this,"lineCap",{get:function(){return this.ctx.lineCap},set:function(tn){-1!==["butt","round","square"].indexOf(tn)&&(this.ctx.lineCap=tn,this.pdf.setLineCap(tn))}}),Object.defineProperty(this,"lineWidth",{get:function(){return this.ctx.lineWidth},set:function(tn){isNaN(tn)||(this.ctx.lineWidth=tn,this.pdf.setLineWidth(tn))}}),Object.defineProperty(this,"lineJoin",{get:function(){return this.ctx.lineJoin},set:function(tn){-1!==["bevel","round","miter"].indexOf(tn)&&(this.ctx.lineJoin=tn,this.pdf.setLineJoin(tn))}}),Object.defineProperty(this,"miterLimit",{get:function(){return this.ctx.miterLimit},set:function(tn){isNaN(tn)||(this.ctx.miterLimit=tn,this.pdf.setMiterLimit(tn))}}),Object.defineProperty(this,"textBaseline",{get:function(){return this.ctx.textBaseline},set:function(tn){this.ctx.textBaseline=tn}}),Object.defineProperty(this,"textAlign",{get:function(){return this.ctx.textAlign},set:function(tn){-1!==["right","end","center","left","start"].indexOf(tn)&&(this.ctx.textAlign=tn)}});var tf=null,td=null;Object.defineProperty(this,"fontFaces",{get:function(){return td},set:function(tn){tf=null,td=tn}}),Object.defineProperty(this,"font",{get:function(){return this.ctx.font},set:function(tn){var ti;if(this.ctx.font=tn,null!==(ti=/^\s*(?=(?:(?:[-a-z]+\s*){0,2}(italic|oblique))?)(?=(?:(?:[-a-z]+\s*){0,2}(small-caps))?)(?=(?:(?:[-a-z]+\s*){0,2}(bold(?:er)?|lighter|[1-9]00))?)(?:(?:normal|\1|\2|\3)\s*){0,3}((?:xx?-)?(?:small|large)|medium|smaller|larger|[.\d]+(?:\%|in|[cem]m|ex|p[ctx]))(?:\s*\/\s*(normal|[.\d]+(?:\%|in|[cem]m|ex|p[ctx])))?\s*([-_,\"\'\sa-z]+?)\s*$/i.exec(tn))){var ta=ti[1],to=(ti[2],ti[3]),ts=ti[4],tc=(ti[5],ti[6]),tu=/^([.\d]+)((?:%|in|[cem]m|ex|p[ctx]))$/i.exec(ts)[2];ts="px"===tu?Math.floor(parseFloat(ts)*this.pdf.internal.scaleFactor):"em"===tu?Math.floor(parseFloat(ts)*this.pdf.getFontSize()):Math.floor(parseFloat(ts)*this.pdf.internal.scaleFactor),this.pdf.setFontSize(ts);var th=function(tn){var ti,ta,to=[],ts=tn.trim();if(""===ts)return eb;if(ts in tM)return[tM[ts]];for(;""!==ts;){switch(ta=null,ti=(ts=Dt(ts)).charAt(0)){case'"':case"'":ta=function(tn,ti){for(var ta=0;ta<tn.length;){if(tn.charAt(ta)===ti)return[tn.substring(0,ta),tn.substring(ta+1)];ta+=1}return null}(ts.substring(1),ti);break;default:ta=function(tn){var ti=tn.match(/^(-[a-z_]|[a-z_])[a-z0-9_-]*/i);return null===ti?null:[ti[0],tn.substring(ti[0].length)]}(ts)}if(null===ta||(to.push(ta[0]),""!==(ts=Dt(ta[1]))&&","!==ts.charAt(0)))return eb;ts=ts.replace(/^,/,"")}return to}(tc);if(this.fontFaces){var tl=function(tn,ti,ta){for(var to=(ta=ta||{}).defaultFontFamily||"times",ts=Object.assign({},tB,ta.genericFontFamilies||{}),tc=null,tu=null,th=0;th<ti.length;++th)if(ts[(tc=jt(ti[th])).family]&&(tc.family=ts[tc.family]),tn.hasOwnProperty(tc.family)){tu=tn[tc.family];break}if(!(tu=tu||tn[to]))throw Error("Could not find a font-family for the rule '"+Et(tc)+"' and default family '"+to+"'.");if(tu=function(tn,ti){if(ti[tn])return ti[tn];var ta=tj[tn],to=ta<=tj.normal?-1:1,ts=Ot(ti,tC,ta,to);if(!ts)throw Error("Could not find a matching font-stretch value for "+tn);return ts}(tc.stretch,tu),tu=function(tn,ti){if(ti[tn])return ti[tn];for(var ta=tI[tn],to=0;to<ta.length;++to)if(ti[ta[to]])return ti[ta[to]];throw Error("Could not find a matching font-style for "+tn)}(tc.style,tu),!(tu=function(tn,ti){if(ti[tn])return ti[tn];if(400===tn&&ti[500])return ti[500];if(500===tn&&ti[400])return ti[400];var ta=Ot(ti,tO,tE[tn],tn<400?-1:1);if(!ta)throw Error("Could not find a matching font-weight for value "+tn);return ta}(tc.weight,tu)))throw Error("Failed to resolve a font for the rule '"+Et(tc)+"'.");return tu}(function(tn,ti){if(null===tf){var ta,to;tf=function(tn){for(var ti={},ta=0;ta<tn.length;++ta){var to=jt(tn[ta]),ts=to.family,tc=to.stretch,tu=to.style,th=to.weight;ti[ts]=ti[ts]||{},ti[ts][tc]=ti[ts][tc]||{},ti[ts][tc][tu]=ti[ts][tc][tu]||{},ti[ts][tc][tu][th]=to}return ti}((ta=tn.getFontList(),to=[],Object.keys(ta).forEach(function(tn){ta[tn].forEach(function(ti){var ta=null;switch(ti){case"bold":ta={family:tn,weight:"bold"};break;case"italic":ta={family:tn,style:"italic"};break;case"bolditalic":ta={family:tn,weight:"bold",style:"italic"};break;case"":case"normal":ta={family:tn}}null!==ta&&(ta.ref={name:tn,style:ti},to.push(ta))})}),to).concat(ti))}return tf}(this.pdf,this.fontFaces),th.map(function(tn){return{family:tn,stretch:"normal",weight:to,style:ta}}));this.pdf.setFont(tl.ref.name,tl.ref.style)}else{var td="";("bold"===to||parseInt(to,10)>=700||"bold"===ta)&&(td="bold"),"italic"===ta&&(td+="italic"),0===td.length&&(td="normal");for(var tp="",tg={arial:"Helvetica",Arial:"Helvetica",verdana:"Helvetica",Verdana:"Helvetica",helvetica:"Helvetica",Helvetica:"Helvetica","sans-serif":"Helvetica",fixed:"Courier",monospace:"Courier",terminal:"Courier",cursive:"Times",fantasy:"Times",serif:"Times"},tm=0;tm<th.length;tm++){if(void 0!==this.pdf.internal.getFont(th[tm],td,{noFallback:!0,disableWarning:!0})){tp=th[tm];break}if("bolditalic"===td&&void 0!==this.pdf.internal.getFont(th[tm],"bold",{noFallback:!0,disableWarning:!0}))tp=th[tm],td="bold";else if(void 0!==this.pdf.internal.getFont(th[tm],"normal",{noFallback:!0,disableWarning:!0})){tp=th[tm],td="normal";break}}if(""===tp){for(var tv=0;tv<th.length;tv++)if(tg[th[tv]]){tp=tg[th[tv]];break}}tp=""===tp?"Times":tp,this.pdf.setFont(tp,td)}}}}),Object.defineProperty(this,"globalCompositeOperation",{get:function(){return this.ctx.globalCompositeOperation},set:function(tn){this.ctx.globalCompositeOperation=tn}}),Object.defineProperty(this,"globalAlpha",{get:function(){return this.ctx.globalAlpha},set:function(tn){this.ctx.globalAlpha=tn}}),Object.defineProperty(this,"lineDashOffset",{get:function(){return this.ctx.lineDashOffset},set:function(tn){this.ctx.lineDashOffset=tn,T.call(this)}}),Object.defineProperty(this,"lineDash",{get:function(){return this.ctx.lineDash},set:function(tn){this.ctx.lineDash=tn,T.call(this)}}),Object.defineProperty(this,"ignoreClearRect",{get:function(){return this.ctx.ignoreClearRect},set:function(tn){this.ctx.ignoreClearRect=!!tn}})};p.prototype.setLineDash=function(tn){this.lineDash=tn},p.prototype.getLineDash=function(){return this.lineDash.length%2?this.lineDash.concat(this.lineDash):this.lineDash.slice()},p.prototype.fill=function(){A.call(this,"fill",!1)},p.prototype.stroke=function(){A.call(this,"stroke",!1)},p.prototype.beginPath=function(){this.path=[{type:"begin"}]},p.prototype.moveTo=function(tn,ti){if(isNaN(tn)||isNaN(ti))throw tu.error("jsPDF.context2d.moveTo: Invalid arguments",arguments),Error("Invalid arguments passed to jsPDF.context2d.moveTo");var ta=this.ctx.transform.applyToPoint(new tl(tn,ti));this.path.push({type:"mt",x:ta.x,y:ta.y}),this.ctx.lastPoint=new tl(tn,ti)},p.prototype.closePath=function(){var tn=new tl(0,0),ti=0;for(ti=this.path.length-1;-1!==ti;ti--)if("begin"===this.path[ti].type&&"object"===(0,to.Z)(this.path[ti+1])&&"number"==typeof this.path[ti+1].x){tn=new tl(this.path[ti+1].x,this.path[ti+1].y);break}this.path.push({type:"close"}),this.ctx.lastPoint=new tl(tn.x,tn.y)},p.prototype.lineTo=function(tn,ti){if(isNaN(tn)||isNaN(ti))throw tu.error("jsPDF.context2d.lineTo: Invalid arguments",arguments),Error("Invalid arguments passed to jsPDF.context2d.lineTo");var ta=this.ctx.transform.applyToPoint(new tl(tn,ti));this.path.push({type:"lt",x:ta.x,y:ta.y}),this.ctx.lastPoint=new tl(ta.x,ta.y)},p.prototype.clip=function(){this.ctx.clip_path=JSON.parse(JSON.stringify(this.path)),A.call(this,null,!0)},p.prototype.quadraticCurveTo=function(tn,ti,ta,to){if(isNaN(ta)||isNaN(to)||isNaN(tn)||isNaN(ti))throw tu.error("jsPDF.context2d.quadraticCurveTo: Invalid arguments",arguments),Error("Invalid arguments passed to jsPDF.context2d.quadraticCurveTo");var ts=this.ctx.transform.applyToPoint(new tl(ta,to)),tc=this.ctx.transform.applyToPoint(new tl(tn,ti));this.path.push({type:"qct",x1:tc.x,y1:tc.y,x:ts.x,y:ts.y}),this.ctx.lastPoint=new tl(ts.x,ts.y)},p.prototype.bezierCurveTo=function(tn,ti,ta,to,ts,tc){if(isNaN(ts)||isNaN(tc)||isNaN(tn)||isNaN(ti)||isNaN(ta)||isNaN(to))throw tu.error("jsPDF.context2d.bezierCurveTo: Invalid arguments",arguments),Error("Invalid arguments passed to jsPDF.context2d.bezierCurveTo");var th=this.ctx.transform.applyToPoint(new tl(ts,tc)),tf=this.ctx.transform.applyToPoint(new tl(tn,ti)),td=this.ctx.transform.applyToPoint(new tl(ta,to));this.path.push({type:"bct",x1:tf.x,y1:tf.y,x2:td.x,y2:td.y,x:th.x,y:th.y}),this.ctx.lastPoint=new tl(th.x,th.y)},p.prototype.arc=function(tn,ti,ta,to,ts,tc){if(isNaN(tn)||isNaN(ti)||isNaN(ta)||isNaN(to)||isNaN(ts))throw tu.error("jsPDF.context2d.arc: Invalid arguments",arguments),Error("Invalid arguments passed to jsPDF.context2d.arc");if(tc=!!tc,!this.ctx.transform.isIdentity){var th=this.ctx.transform.applyToPoint(new tl(tn,ti));tn=th.x,ti=th.y;var tf=this.ctx.transform.applyToPoint(new tl(0,ta)),td=this.ctx.transform.applyToPoint(new tl(0,0));ta=Math.sqrt(Math.pow(tf.x-td.x,2)+Math.pow(tf.y-td.y,2))}Math.abs(ts-to)>=2*Math.PI&&(to=0,ts=2*Math.PI),this.path.push({type:"arc",x:tn,y:ti,radius:ta,startAngle:to,endAngle:ts,counterclockwise:tc})},p.prototype.arcTo=function(tn,ti,ta,to,ts){throw Error("arcTo not implemented.")},p.prototype.rect=function(tn,ti,ta,to){if(isNaN(tn)||isNaN(ti)||isNaN(ta)||isNaN(to))throw tu.error("jsPDF.context2d.rect: Invalid arguments",arguments),Error("Invalid arguments passed to jsPDF.context2d.rect");this.moveTo(tn,ti),this.lineTo(tn+ta,ti),this.lineTo(tn+ta,ti+to),this.lineTo(tn,ti+to),this.lineTo(tn,ti),this.lineTo(tn+ta,ti),this.lineTo(tn,ti)},p.prototype.fillRect=function(tn,ti,ta,to){if(isNaN(tn)||isNaN(ti)||isNaN(ta)||isNaN(to))throw tu.error("jsPDF.context2d.fillRect: Invalid arguments",arguments),Error("Invalid arguments passed to jsPDF.context2d.fillRect");if(!m.call(this)){var ts={};"butt"!==this.lineCap&&(ts.lineCap=this.lineCap,this.lineCap="butt"),"miter"!==this.lineJoin&&(ts.lineJoin=this.lineJoin,this.lineJoin="miter"),this.beginPath(),this.rect(tn,ti,ta,to),this.fill(),ts.hasOwnProperty("lineCap")&&(this.lineCap=ts.lineCap),ts.hasOwnProperty("lineJoin")&&(this.lineJoin=ts.lineJoin)}},p.prototype.strokeRect=function(tn,ti,ta,to){if(isNaN(tn)||isNaN(ti)||isNaN(ta)||isNaN(to))throw tu.error("jsPDF.context2d.strokeRect: Invalid arguments",arguments),Error("Invalid arguments passed to jsPDF.context2d.strokeRect");v.call(this)||(this.beginPath(),this.rect(tn,ti,ta,to),this.stroke())},p.prototype.clearRect=function(tn,ti,ta,to){if(isNaN(tn)||isNaN(ti)||isNaN(ta)||isNaN(to))throw tu.error("jsPDF.context2d.clearRect: Invalid arguments",arguments),Error("Invalid arguments passed to jsPDF.context2d.clearRect");this.ignoreClearRect||(this.fillStyle="#ffffff",this.fillRect(tn,ti,ta,to))},p.prototype.save=function(tn){tn="boolean"!=typeof tn||tn;for(var ti=this.pdf.internal.getCurrentPageInfo().pageNumber,ta=0;ta<this.pdf.internal.getNumberOfPages();ta++)this.pdf.setPage(ta+1),this.pdf.internal.out("q");if(this.pdf.setPage(ti),tn){this.ctx.fontSize=this.pdf.internal.getFontSize();var to=new d(this.ctx);this.ctxStack.push(this.ctx),this.ctx=to}},p.prototype.restore=function(tn){tn="boolean"!=typeof tn||tn;for(var ti=this.pdf.internal.getCurrentPageInfo().pageNumber,ta=0;ta<this.pdf.internal.getNumberOfPages();ta++)this.pdf.setPage(ta+1),this.pdf.internal.out("Q");this.pdf.setPage(ti),tn&&0!==this.ctxStack.length&&(this.ctx=this.ctxStack.pop(),this.fillStyle=this.ctx.fillStyle,this.strokeStyle=this.ctx.strokeStyle,this.font=this.ctx.font,this.lineCap=this.ctx.lineCap,this.lineWidth=this.ctx.lineWidth,this.lineJoin=this.ctx.lineJoin,this.lineDash=this.ctx.lineDash,this.lineDashOffset=this.ctx.lineDashOffset)},p.prototype.toDataURL=function(){throw Error("toDataUrl not implemented.")};var g=function(tn){var ti,ta,to,ts;if(!0===tn.isCanvasGradient&&(tn=tn.getColor()),!tn)return{r:0,g:0,b:0,a:0,style:tn};if(/transparent|rgba\s*\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*,\s*0+\s*\)/.test(tn))ti=0,ta=0,to=0,ts=0;else{var tc=/rgb\s*\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*\)/.exec(tn);if(null!==tc)ti=parseInt(tc[1]),ta=parseInt(tc[2]),to=parseInt(tc[3]),ts=1;else if(null!==(tc=/rgba\s*\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*,\s*([\d.]+)\s*\)/.exec(tn)))ti=parseInt(tc[1]),ta=parseInt(tc[2]),to=parseInt(tc[3]),ts=parseFloat(tc[4]);else{if(ts=1,"string"==typeof tn&&"#"!==tn.charAt(0)){var tu=new f(tn);tn=tu.ok?tu.toHex():"#000000"}4===tn.length?(ti=tn.substring(1,2),ti+=ti,ta=tn.substring(2,3),ta+=ta,to=tn.substring(3,4),to+=to):(ti=tn.substring(1,3),ta=tn.substring(3,5),to=tn.substring(5,7)),ti=parseInt(ti,16),ta=parseInt(ta,16),to=parseInt(to,16)}}return{r:ti,g:ta,b:to,a:ts,style:tn}},m=function(){return this.ctx.isFillTransparent||0==this.globalAlpha},v=function(){return!!(this.ctx.isStrokeTransparent||0==this.globalAlpha)};p.prototype.fillText=function(tn,ti,ta,to){if(isNaN(ti)||isNaN(ta)||"string"!=typeof tn)throw tu.error("jsPDF.context2d.fillText: Invalid arguments",arguments),Error("Invalid arguments passed to jsPDF.context2d.fillText");if(to=isNaN(to)?void 0:to,!m.call(this)){var ts=q(this.ctx.transform.rotation);C.call(this,{text:tn,x:ti,y:ta,scale:this.ctx.transform.scaleX,angle:ts,align:this.textAlign,maxWidth:to})}},p.prototype.strokeText=function(tn,ti,ta,to){if(isNaN(ti)||isNaN(ta)||"string"!=typeof tn)throw tu.error("jsPDF.context2d.strokeText: Invalid arguments",arguments),Error("Invalid arguments passed to jsPDF.context2d.strokeText");if(!v.call(this)){to=isNaN(to)?void 0:to;var ts=q(this.ctx.transform.rotation);C.call(this,{text:tn,x:ti,y:ta,scale:this.ctx.transform.scaleX,renderingMode:"stroke",angle:ts,align:this.textAlign,maxWidth:to})}},p.prototype.measureText=function(tn){if("string"!=typeof tn)throw tu.error("jsPDF.context2d.measureText: Invalid arguments",arguments),Error("Invalid arguments passed to jsPDF.context2d.measureText");var ti=this.pdf,ta=this.pdf.internal.scaleFactor,to=ti.internal.getFontSize(),ts=ti.getStringUnitWidth(tn)*to/ti.internal.scaleFactor;return new function(tn){var ti=(tn=tn||{}).width||0;return Object.defineProperty(this,"width",{get:function(){return ti}}),this}({width:ts*=Math.round(96*ta/72*1e4)/1e4})},p.prototype.scale=function(tn,ti){if(isNaN(tn)||isNaN(ti))throw tu.error("jsPDF.context2d.scale: Invalid arguments",arguments),Error("Invalid arguments passed to jsPDF.context2d.scale");var ta=new td(tn,0,0,ti,0,0);this.ctx.transform=this.ctx.transform.multiply(ta)},p.prototype.rotate=function(tn){if(isNaN(tn))throw tu.error("jsPDF.context2d.rotate: Invalid arguments",arguments),Error("Invalid arguments passed to jsPDF.context2d.rotate");var ti=new td(Math.cos(tn),Math.sin(tn),-Math.sin(tn),Math.cos(tn),0,0);this.ctx.transform=this.ctx.transform.multiply(ti)},p.prototype.translate=function(tn,ti){if(isNaN(tn)||isNaN(ti))throw tu.error("jsPDF.context2d.translate: Invalid arguments",arguments),Error("Invalid arguments passed to jsPDF.context2d.translate");var ta=new td(1,0,0,1,tn,ti);this.ctx.transform=this.ctx.transform.multiply(ta)},p.prototype.transform=function(tn,ti,ta,to,ts,tc){if(isNaN(tn)||isNaN(ti)||isNaN(ta)||isNaN(to)||isNaN(ts)||isNaN(tc))throw tu.error("jsPDF.context2d.transform: Invalid arguments",arguments),Error("Invalid arguments passed to jsPDF.context2d.transform");var th=new td(tn,ti,ta,to,ts,tc);this.ctx.transform=this.ctx.transform.multiply(th)},p.prototype.setTransform=function(tn,ti,ta,to,ts,tc){tn=isNaN(tn)?1:tn,ti=isNaN(ti)?0:ti,ta=isNaN(ta)?0:ta,to=isNaN(to)?1:to,ts=isNaN(ts)?0:ts,tc=isNaN(tc)?0:tc,this.ctx.transform=new td(tn,ti,ta,to,ts,tc)};var b=function(){return this.margin[0]>0||this.margin[1]>0||this.margin[2]>0||this.margin[3]>0};p.prototype.drawImage=function(tn,ti,ta,to,ts,tc,tu,th,tl){var tp=this.pdf.getImageProperties(tn),tg=1,tm=1,tv=1,tb=1;void 0!==to&&void 0!==th&&(tv=th/to,tb=tl/ts,tg=tp.width/to*th/to,tm=tp.height/ts*tl/ts),void 0===tc&&(tc=ti,tu=ta,ti=0,ta=0),void 0!==to&&void 0===th&&(th=to,tl=ts),void 0===to&&void 0===th&&(th=tp.width,tl=tp.height);for(var ty,tw=this.ctx.transform.decompose(),tN=q(tw.rotate.shx),tL=new td,tx=(tL=(tL=(tL=tL.multiply(tw.translate)).multiply(tw.skew)).multiply(tw.scale)).applyToRectangle(new tf(tc-ti*tv,tu-ta*tb,to*tg,ts*tm)),tA=y.call(this,tx),tS=[],t_=0;t_<tA.length;t_+=1)-1===tS.indexOf(tA[t_])&&tS.push(tA[t_]);if(L(tS),this.autoPaging)for(var tP=tS[0],tk=tS[tS.length-1],tF=tP;tF<tk+1;tF++){this.pdf.setPage(tF);var tI=this.pdf.internal.pageSize.width-this.margin[3]-this.margin[1],tC=1===tF?this.posY+this.margin[0]:this.margin[0],tj=this.pdf.internal.pageSize.height-this.posY-this.margin[0]-this.margin[2],tO=this.pdf.internal.pageSize.height-this.margin[0]-this.margin[2],tE=1===tF?0:tj+(tF-2)*tO;if(0!==this.ctx.clip_path.length){var tB=this.path;ty=JSON.parse(JSON.stringify(this.ctx.clip_path)),this.path=N(ty,this.posX+this.margin[3],-tE+tC+this.ctx.prevPageLastElemOffset),x.call(this,"fill",!0),this.path=tB}var tM=JSON.parse(JSON.stringify(tx));tM=N([tM],this.posX+this.margin[3],-tE+tC+this.ctx.prevPageLastElemOffset)[0];var tq=(tF>tP||tF<tk)&&b.call(this);tq&&(this.pdf.saveGraphicsState(),this.pdf.rect(this.margin[3],this.margin[0],tI,tO,null).clip().discardPath()),this.pdf.addImage(tn,"JPEG",tM.x,tM.y,tM.w,tM.h,null,null,tN),tq&&this.pdf.restoreGraphicsState()}else this.pdf.addImage(tn,"JPEG",tx.x,tx.y,tx.w,tx.h,null,null,tN)};var y=function(tn,ti,ta){var to=[];ti=ti||this.pdf.internal.pageSize.width,ta=ta||this.pdf.internal.pageSize.height-this.margin[0]-this.margin[2];var ts=this.posY+this.ctx.prevPageLastElemOffset;switch(tn.type){default:case"mt":case"lt":to.push(Math.floor((tn.y+ts)/ta)+1);break;case"arc":to.push(Math.floor((tn.y+ts-tn.radius)/ta)+1),to.push(Math.floor((tn.y+ts+tn.radius)/ta)+1);break;case"qct":var tc=D(this.ctx.lastPoint.x,this.ctx.lastPoint.y,tn.x1,tn.y1,tn.x,tn.y);to.push(Math.floor((tc.y+ts)/ta)+1),to.push(Math.floor((tc.y+tc.h+ts)/ta)+1);break;case"bct":var tu=R(this.ctx.lastPoint.x,this.ctx.lastPoint.y,tn.x1,tn.y1,tn.x2,tn.y2,tn.x,tn.y);to.push(Math.floor((tu.y+ts)/ta)+1),to.push(Math.floor((tu.y+tu.h+ts)/ta)+1);break;case"rect":to.push(Math.floor((tn.y+ts)/ta)+1),to.push(Math.floor((tn.y+tn.h+ts)/ta)+1)}for(var th=0;th<to.length;th+=1)for(;this.pdf.internal.getNumberOfPages()<to[th];)w.call(this);return to},w=function(){var tn=this.fillStyle,ti=this.strokeStyle,ta=this.font,to=this.lineCap,ts=this.lineWidth,tc=this.lineJoin;this.pdf.addPage(),this.fillStyle=tn,this.strokeStyle=ti,this.font=ta,this.lineCap=to,this.lineWidth=ts,this.lineJoin=tc},N=function(tn,ti,ta){for(var to=0;to<tn.length;to++)switch(tn[to].type){case"bct":tn[to].x2+=ti,tn[to].y2+=ta;case"qct":tn[to].x1+=ti,tn[to].y1+=ta;default:tn[to].x+=ti,tn[to].y+=ta}return tn},L=function(tn){return tn.sort(function(tn,ti){return tn-ti})},A=function(tn,ti){for(var ta,to,ts=this.fillStyle,tc=this.strokeStyle,tu=this.lineCap,th=this.lineWidth,tl=Math.abs(th*this.ctx.transform.scaleX),tf=this.lineJoin,td=JSON.parse(JSON.stringify(this.path)),tp=JSON.parse(JSON.stringify(this.path)),tg=[],tm=0;tm<tp.length;tm++)if(void 0!==tp[tm].x)for(var tv=y.call(this,tp[tm]),tb=0;tb<tv.length;tb+=1)-1===tg.indexOf(tv[tb])&&tg.push(tv[tb]);for(var ty=0;ty<tg.length;ty++)for(;this.pdf.internal.getNumberOfPages()<tg[ty];)w.call(this);if(L(tg),this.autoPaging)for(var tw=tg[0],tN=tg[tg.length-1],tL=tw;tL<tN+1;tL++){this.pdf.setPage(tL),this.fillStyle=ts,this.strokeStyle=tc,this.lineCap=tu,this.lineWidth=tl,this.lineJoin=tf;var tx=this.pdf.internal.pageSize.width-this.margin[3]-this.margin[1],tA=1===tL?this.posY+this.margin[0]:this.margin[0],tS=this.pdf.internal.pageSize.height-this.posY-this.margin[0]-this.margin[2],t_=this.pdf.internal.pageSize.height-this.margin[0]-this.margin[2],tP=1===tL?0:tS+(tL-2)*t_;if(0!==this.ctx.clip_path.length){var tk=this.path;ta=JSON.parse(JSON.stringify(this.ctx.clip_path)),this.path=N(ta,this.posX+this.margin[3],-tP+tA+this.ctx.prevPageLastElemOffset),x.call(this,tn,!0),this.path=tk}if(to=JSON.parse(JSON.stringify(td)),this.path=N(to,this.posX+this.margin[3],-tP+tA+this.ctx.prevPageLastElemOffset),!1===ti||0===tL){var tF=(tL>tw||tL<tN)&&b.call(this);tF&&(this.pdf.saveGraphicsState(),this.pdf.rect(this.margin[3],this.margin[0],tx,t_,null).clip().discardPath()),x.call(this,tn,ti),tF&&this.pdf.restoreGraphicsState()}this.lineWidth=th}else this.lineWidth=tl,x.call(this,tn,ti),this.lineWidth=th;this.path=td},x=function(tn,ti){if(("stroke"!==tn||ti||!v.call(this))&&("stroke"===tn||ti||!m.call(this))){for(var ta,to,ts=[],tc=this.path,tu=0;tu<tc.length;tu++){var th=tc[tu];switch(th.type){case"begin":ts.push({begin:!0});break;case"close":ts.push({close:!0});break;case"mt":ts.push({start:th,deltas:[],abs:[]});break;case"lt":var tl=ts.length;if(tc[tu-1]&&!isNaN(tc[tu-1].x)&&(ta=[th.x-tc[tu-1].x,th.y-tc[tu-1].y],tl>0)){for(;tl>=0;tl--)if(!0!==ts[tl-1].close&&!0!==ts[tl-1].begin){ts[tl-1].deltas.push(ta),ts[tl-1].abs.push(th);break}}break;case"bct":ta=[th.x1-tc[tu-1].x,th.y1-tc[tu-1].y,th.x2-tc[tu-1].x,th.y2-tc[tu-1].y,th.x-tc[tu-1].x,th.y-tc[tu-1].y],ts[ts.length-1].deltas.push(ta);break;case"qct":var tf=tc[tu-1].x+2/3*(th.x1-tc[tu-1].x),td=tc[tu-1].y+2/3*(th.y1-tc[tu-1].y),tp=th.x+2/3*(th.x1-th.x),tg=th.y+2/3*(th.y1-th.y),tm=th.x,tv=th.y;ta=[tf-tc[tu-1].x,td-tc[tu-1].y,tp-tc[tu-1].x,tg-tc[tu-1].y,tm-tc[tu-1].x,tv-tc[tu-1].y],ts[ts.length-1].deltas.push(ta);break;case"arc":ts.push({deltas:[],abs:[],arc:!0}),Array.isArray(ts[ts.length-1].abs)&&ts[ts.length-1].abs.push(th)}}to=ti?null:"stroke"===tn?"stroke":"fill";for(var tb=!1,ty=0;ty<ts.length;ty++)if(ts[ty].arc)for(var tw=ts[ty].abs,tN=0;tN<tw.length;tN++){var tL=tw[tN];"arc"===tL.type?P.call(this,tL.x,tL.y,tL.radius,tL.startAngle,tL.endAngle,tL.counterclockwise,void 0,ti,!tb):j.call(this,tL.x,tL.y),tb=!0}else if(!0===ts[ty].close)this.pdf.internal.out("h"),tb=!1;else if(!0!==ts[ty].begin){var tx=ts[ty].start.x,tA=ts[ty].start.y;O.call(this,ts[ty].deltas,tx,tA),tb=!0}to&&k.call(this,to),ti&&I.call(this)}},S=function(tn){var ti=this.pdf.internal.getFontSize()/this.pdf.internal.scaleFactor,ta=ti*(this.pdf.internal.getLineHeightFactor()-1);switch(this.ctx.textBaseline){case"bottom":return tn-ta;case"top":return tn+ti-ta;case"hanging":return tn+ti-2*ta;case"middle":return tn+ti/2-ta;default:return tn}},_=function(tn){return tn+this.pdf.internal.getFontSize()/this.pdf.internal.scaleFactor*(this.pdf.internal.getLineHeightFactor()-1)};p.prototype.createLinearGradient=function(){var t=function(){};return t.colorStops=[],t.addColorStop=function(tn,ti){this.colorStops.push([tn,ti])},t.getColor=function(){return 0===this.colorStops.length?"#000000":this.colorStops[0][1]},t.isCanvasGradient=!0,t},p.prototype.createPattern=function(){return this.createLinearGradient()},p.prototype.createRadialGradient=function(){return this.createLinearGradient()};var P=function(tn,ti,ta,to,ts,tc,tu,th,tl){for(var tf=M.call(this,ta,to,ts,tc),td=0;td<tf.length;td++){var tp=tf[td];0===td&&(tl?F.call(this,tp.x1+tn,tp.y1+ti):j.call(this,tp.x1+tn,tp.y1+ti)),B.call(this,tn,ti,tp.x2,tp.y2,tp.x3,tp.y3,tp.x4,tp.y4)}th?I.call(this):k.call(this,tu)},k=function(tn){switch(tn){case"stroke":this.pdf.internal.out("S");break;case"fill":this.pdf.internal.out("f")}},I=function(){this.pdf.clip(),this.pdf.discardPath()},F=function(tn,ti){this.pdf.internal.out(ta(tn)+" "+ts(ti)+" m")},C=function(tn){switch(tn.align){case"right":case"end":th="right";break;case"center":th="center";break;default:th="left"}var ti=this.pdf.getTextDimensions(tn.text),ta=S.call(this,tn.y),to=_.call(this,ta)-ti.h,ts=this.ctx.transform.applyToPoint(new tl(tn.x,ta)),tc=this.ctx.transform.decompose(),tu=new td;tu=(tu=(tu=tu.multiply(tc.translate)).multiply(tc.skew)).multiply(tc.scale);for(var th,tp,tg,tm,tv=this.ctx.transform.applyToRectangle(new tf(tn.x,ta,ti.w,ti.h)),tb=tu.applyToRectangle(new tf(tn.x,to,ti.w,ti.h)),ty=y.call(this,tb),tw=[],tN=0;tN<ty.length;tN+=1)-1===tw.indexOf(ty[tN])&&tw.push(ty[tN]);if(L(tw),this.autoPaging)for(var tL=tw[0],tx=tw[tw.length-1],tA=tL;tA<tx+1;tA++){this.pdf.setPage(tA);var tS=1===tA?this.posY+this.margin[0]:this.margin[0],t_=this.pdf.internal.pageSize.height-this.posY-this.margin[0]-this.margin[2],tP=this.pdf.internal.pageSize.height-this.margin[2],tk=tP-this.margin[0],tF=this.pdf.internal.pageSize.width-this.margin[1],tI=tF-this.margin[3],tC=1===tA?0:t_+(tA-2)*tk;if(0!==this.ctx.clip_path.length){var tj=this.path;tp=JSON.parse(JSON.stringify(this.ctx.clip_path)),this.path=N(tp,this.posX+this.margin[3],-1*tC+tS),x.call(this,"fill",!0),this.path=tj}var tO=N([JSON.parse(JSON.stringify(tb))],this.posX+this.margin[3],-tC+tS+this.ctx.prevPageLastElemOffset)[0];tn.scale>=.01&&(tg=this.pdf.internal.getFontSize(),this.pdf.setFontSize(tg*tn.scale),tm=this.lineWidth,this.lineWidth=tm*tn.scale);var tE="text"!==this.autoPaging;if(tE||tO.y+tO.h<=tP){if(tE||tO.y>=tS&&tO.x<=tF){var tB=tE?tn.text:this.pdf.splitTextToSize(tn.text,tn.maxWidth||tF-tO.x)[0],tM=N([JSON.parse(JSON.stringify(tv))],this.posX+this.margin[3],-tC+tS+this.ctx.prevPageLastElemOffset)[0],tq=tE&&(tA>tL||tA<tx)&&b.call(this);tq&&(this.pdf.saveGraphicsState(),this.pdf.rect(this.margin[3],this.margin[0],tI,tk,null).clip().discardPath()),this.pdf.text(tB,tM.x,tM.y,{angle:tn.angle,align:th,renderingMode:tn.renderingMode}),tq&&this.pdf.restoreGraphicsState()}}else tO.y<tP&&(this.ctx.prevPageLastElemOffset+=tP-tO.y);tn.scale>=.01&&(this.pdf.setFontSize(tg),this.lineWidth=tm)}else tn.scale>=.01&&(tg=this.pdf.internal.getFontSize(),this.pdf.setFontSize(tg*tn.scale),tm=this.lineWidth,this.lineWidth=tm*tn.scale),this.pdf.text(tn.text,ts.x+this.posX,ts.y+this.posY,{angle:tn.angle,align:th,renderingMode:tn.renderingMode,maxWidth:tn.maxWidth}),tn.scale>=.01&&(this.pdf.setFontSize(tg),this.lineWidth=tm)},j=function(tn,ti,to,tc){to=to||0,tc=tc||0,this.pdf.internal.out(ta(tn+to)+" "+ts(ti+tc)+" l")},O=function(tn,ti,ta){return this.pdf.lines(tn,ti,ta,null,null)},B=function(tn,ta,to,ts,tu,tl,tf,td){this.pdf.internal.out([ti(tc(to+tn)),ti(th(ts+ta)),ti(tc(tu+tn)),ti(th(tl+ta)),ti(tc(tf+tn)),ti(th(td+ta)),"c"].join(" "))},M=function(tn,ti,ta,to){for(var ts=2*Math.PI,tc=Math.PI/2;ti>ta;)ti-=ts;var tu=Math.abs(ta-ti);tu<ts&&to&&(tu=ts-tu);for(var th=[],tl=to?-1:1,tf=ti;tu>1e-5;){var td=tf+tl*Math.min(tu,tc);th.push(E.call(this,tn,tf,td)),tu-=Math.abs(td-tf),tf=td}return th},E=function(tn,ti,ta){var to=(ta-ti)/2,ts=tn*Math.cos(to),tc=tn*Math.sin(to),tu=-tc,th=ts*ts+tu*tu,tl=th+ts*ts+tu*tc,tf=4/3*(Math.sqrt(2*th*tl)-tl)/(ts*tc-tu*ts),td=ts-tf*tu,tp=tu+tf*ts,tg=-tp,tm=to+ti,tv=Math.cos(tm),tb=Math.sin(tm);return{x1:tn*Math.cos(ti),y1:tn*Math.sin(ti),x2:td*tv-tp*tb,y2:td*tb+tp*tv,x3:td*tv-tg*tb,y3:td*tb+tg*tv,x4:tn*Math.cos(ta),y4:tn*Math.sin(ta)}},q=function(tn){return 180*tn/Math.PI},D=function(tn,ti,ta,to,ts,tc){var tu=tn+.5*(ta-tn),th=ti+.5*(to-ti),tl=ts+.5*(ta-ts),td=tc+.5*(to-tc),tp=Math.min(tn,ts,tu,tl),tg=Math.max(tn,ts,tu,tl),tm=Math.min(ti,tc,th,td),tv=Math.max(ti,tc,th,td);return new tf(tp,tm,tg-tp,tv-tm)},R=function(tn,ti,ta,to,ts,tc,tu,th){var tl,td,tp,tg,tm,tv,tb,ty,tw,tN,tL,tx,tA,tS,t_=ta-tn,tP=to-ti,tk=ts-ta,tF=tc-to,tI=tu-ts,tC=th-tc;for(td=0;td<41;td++)tw=(tb=(tp=tn+(tl=td/40)*t_)+tl*((tm=ta+tl*tk)-tp))+tl*(tm+tl*(ts+tl*tI-tm)-tb),tN=(ty=(tg=ti+tl*tP)+tl*((tv=to+tl*tF)-tg))+tl*(tv+tl*(tc+tl*tC-tv)-ty),0==td?(tL=tw,tx=tN,tA=tw,tS=tN):(tL=Math.min(tL,tw),tx=Math.min(tx,tN),tA=Math.max(tA,tw),tS=Math.max(tS,tN));return new tf(Math.round(tL),Math.round(tx),Math.round(tA-tL),Math.round(tS-tx))},T=function(){if(this.prevLineDash||this.ctx.lineDash.length||this.ctx.lineDashOffset){var tn=JSON.stringify({lineDash:this.ctx.lineDash,lineDashOffset:this.ctx.lineDashOffset});this.prevLineDash!==tn&&(this.pdf.setLineDash(this.ctx.lineDash,this.ctx.lineDashOffset),this.prevLineDash=tn)}}}(E.API),ey=E.API,ew=function(tn){var ti,ta,to,ts,tc,tu,th,tl,tf,td;for(/[^\x00-\xFF]/.test(tn),ta=[],to=0,ts=(tn+=ti="\x00\x00\x00\x00".slice(tn.length%4||4)).length;ts>to;to+=4)0!==(tc=(tn.charCodeAt(to)<<24)+(tn.charCodeAt(to+1)<<16)+(tn.charCodeAt(to+2)<<8)+tn.charCodeAt(to+3))?(tu=(tc=((tc=((tc=((tc=(tc-(td=tc%85))/85)-(tf=tc%85))/85)-(tl=tc%85))/85)-(th=tc%85))/85)%85,ta.push(tu+33,th+33,tl+33,tf+33,td+33)):ta.push(122);return function(tn,ti){for(var ta=ti;ta>0;ta--)tn.pop()}(ta,ti.length),String.fromCharCode.apply(String,ta)+"~>"},eN=function(tn){var ti,ta,to,ts,tc,tu=String,th="length",tl="charCodeAt",tf="slice",td="replace";for(tn[tf](-2),tn=tn[tf](0,-2)[td](/\s/g,"")[td]("z","!!!!!"),to=[],ts=0,tc=(tn+=ti="uuuuu"[tf](tn[th]%5||5))[th];tc>ts;ts+=5)ta=52200625*(tn[tl](ts)-33)+614125*(tn[tl](ts+1)-33)+7225*(tn[tl](ts+2)-33)+85*(tn[tl](ts+3)-33)+(tn[tl](ts+4)-33),to.push(255&ta>>24,255&ta>>16,255&ta>>8,255&ta);return function(tn,ti){for(var ta=ti;ta>0;ta--)tn.pop()}(to,ti[th]),tu.fromCharCode.apply(tu,to)},eL=function(tn){var ti=new RegExp(/^([0-9A-Fa-f]{2})+$/);if(-1!==(tn=tn.replace(/\s/g,"")).indexOf(">")&&(tn=tn.substr(0,tn.indexOf(">"))),tn.length%2&&(tn+="0"),!1===ti.test(tn))return"";for(var ta="",to=0;to<tn.length;to+=2)ta+=String.fromCharCode("0x"+(tn[to]+tn[to+1]));return ta},ex=function(tn){for(var ti=new Uint8Array(tn.length),ta=tn.length;ta--;)ti[ta]=tn.charCodeAt(ta);return tn=(ti=(0,ts.iZ)(ti)).reduce(function(tn,ti){return tn+String.fromCharCode(ti)},"")},ey.processDataByFilters=function(tn,ti){var ta=0,to=tn||"",ts=[];for("string"==typeof(ti=ti||[])&&(ti=[ti]),ta=0;ta<ti.length;ta+=1)switch(ti[ta]){case"ASCII85Decode":case"/ASCII85Decode":to=eN(to),ts.push("/ASCII85Encode");break;case"ASCII85Encode":case"/ASCII85Encode":to=ew(to),ts.push("/ASCII85Decode");break;case"ASCIIHexDecode":case"/ASCIIHexDecode":to=eL(to),ts.push("/ASCIIHexEncode");break;case"ASCIIHexEncode":case"/ASCIIHexEncode":to=to.split("").map(function(tn){return("0"+tn.charCodeAt().toString(16)).slice(-2)}).join("")+">",ts.push("/ASCIIHexDecode");break;case"FlateEncode":case"/FlateEncode":to=ex(to),ts.push("/FlateDecode");break;default:throw Error('The filter: "'+ti[ta]+'" is not implemented')}return{data:to,reverseChain:ts.reverse().join(" ")}},(eA=E.API).loadFile=function(tn,ti,ta){return function(tn,ti,ta){ti=!1!==ti,ta="function"==typeof ta?ta:function(){};var to=void 0;try{to=function(tn,ti,ta){var to=new XMLHttpRequest,ts=0,a=function(tn){var ti=tn.length,ta=[],to=String.fromCharCode;for(ts=0;ts<ti;ts+=1)ta.push(to(255&tn.charCodeAt(ts)));return ta.join("")};if(to.open("GET",tn,!ti),to.overrideMimeType("text/plain; charset=x-user-defined"),!1===ti&&(to.onload=function(){200===to.status?ta(a(this.responseText)):ta(void 0)}),to.send(null),ti&&200===to.status)return a(to.responseText)}(tn,ti,ta)}catch(tn){}return to}(tn,ti,ta)},eA.loadImageFile=eA.loadFile,function(tn){function r(){return(tc.html2canvas?Promise.resolve(tc.html2canvas):Promise.resolve().then(ta.t.bind(ta,61120,23))).catch(function(tn){return Promise.reject(Error("Could not load html2canvas: "+tn))}).then(function(tn){return tn.default?tn.default:tn})}function i(){return(tc.DOMPurify?Promise.resolve(tc.DOMPurify):ta.e(7856).then(ta.t.bind(ta,27856,23))).catch(function(tn){return Promise.reject(Error("Could not load dompurify: "+tn))}).then(function(tn){return tn.default?tn.default:tn})}var a=function(tn){var ti=(0,to.Z)(tn);return"undefined"===ti?"undefined":"string"===ti||tn instanceof String?"string":"number"===ti||tn instanceof Number?"number":"function"===ti||tn instanceof Function?"function":tn&&tn.constructor===Array?"array":tn&&1===tn.nodeType?"element":"object"===ti?"object":"unknown"},o=function(tn,ti){var ta=document.createElement(tn);for(var to in ti.className&&(ta.className=ti.className),ti.innerHTML&&ti.dompurify&&(ta.innerHTML=ti.dompurify.sanitize(ti.innerHTML)),ti.style)ta.style[to]=ti.style[to];return ta},s=function t(tn){var ti=Object.assign(t.convert(Promise.resolve()),JSON.parse(JSON.stringify(t.template))),ta=t.convert(Promise.resolve(),ti);return(ta=ta.setProgress(1,t,1,[t])).set(tn)};(s.prototype=Object.create(Promise.prototype)).constructor=s,s.convert=function(tn,ti){return tn.__proto__=ti||s.prototype,tn},s.template={prop:{src:null,container:null,overlay:null,canvas:null,img:null,pdf:null,pageSize:null,callback:function(){}},progress:{val:0,state:null,n:0,stack:[]},opt:{filename:"file.pdf",margin:[0,0,0,0],enableLinks:!0,x:0,y:0,html2canvas:{},jsPDF:{},backgroundColor:"transparent"}},s.prototype.from=function(tn,ti){return this.then(function(){switch(ti=ti||function(tn){switch(a(tn)){case"string":return"string";case"element":return"canvas"===tn.nodeName.toLowerCase()?"canvas":"element";default:return"unknown"}}(tn)){case"string":return this.then(i).then(function(ti){return this.set({src:o("div",{innerHTML:tn,dompurify:ti})})});case"element":return this.set({src:tn});case"canvas":return this.set({canvas:tn});case"img":return this.set({img:tn});default:return this.error("Unknown source type.")}})},s.prototype.to=function(tn){switch(tn){case"container":return this.toContainer();case"canvas":return this.toCanvas();case"img":return this.toImg();case"pdf":return this.toPdf();default:return this.error("Invalid target.")}},s.prototype.toContainer=function(){return this.thenList([function(){return this.prop.src||this.error("Cannot duplicate - no source HTML.")},function(){return this.prop.pageSize||this.setPageSize()}]).then(function(){var tn={position:"relative",display:"inline-block",width:("number"!=typeof this.opt.width||isNaN(this.opt.width)||"number"!=typeof this.opt.windowWidth||isNaN(this.opt.windowWidth)?Math.max(this.prop.src.clientWidth,this.prop.src.scrollWidth,this.prop.src.offsetWidth):this.opt.windowWidth)+"px",left:0,right:0,top:0,margin:"auto",backgroundColor:this.opt.backgroundColor},ti=function t(tn,ti){for(var ta=3===tn.nodeType?document.createTextNode(tn.nodeValue):tn.cloneNode(!1),to=tn.firstChild;to;to=to.nextSibling)!0!==ti&&1===to.nodeType&&"SCRIPT"===to.nodeName||ta.appendChild(t(to,ti));return 1===tn.nodeType&&("CANVAS"===tn.nodeName?(ta.width=tn.width,ta.height=tn.height,ta.getContext("2d").drawImage(tn,0,0)):"TEXTAREA"!==tn.nodeName&&"SELECT"!==tn.nodeName||(ta.value=tn.value),ta.addEventListener("load",function(){ta.scrollTop=tn.scrollTop,ta.scrollLeft=tn.scrollLeft},!0)),ta}(this.prop.src,this.opt.html2canvas.javascriptEnabled);"BODY"===ti.tagName&&(tn.height=Math.max(document.body.scrollHeight,document.body.offsetHeight,document.documentElement.clientHeight,document.documentElement.scrollHeight,document.documentElement.offsetHeight)+"px"),this.prop.overlay=o("div",{className:"html2pdf__overlay",style:{position:"fixed",overflow:"hidden",zIndex:1e3,left:"-100000px",right:0,bottom:0,top:0}}),this.prop.container=o("div",{className:"html2pdf__container",style:tn}),this.prop.container.appendChild(ti),this.prop.container.firstChild.appendChild(o("div",{style:{clear:"both",border:"0 none transparent",margin:0,padding:0,height:0}})),this.prop.container.style.float="none",this.prop.overlay.appendChild(this.prop.container),document.body.appendChild(this.prop.overlay),this.prop.container.firstChild.style.position="relative",this.prop.container.height=Math.max(this.prop.container.firstChild.clientHeight,this.prop.container.firstChild.scrollHeight,this.prop.container.firstChild.offsetHeight)+"px"})},s.prototype.toCanvas=function(){var tn=[function(){return document.body.contains(this.prop.container)||this.toContainer()}];return this.thenList(tn).then(r).then(function(tn){var ti=Object.assign({},this.opt.html2canvas);return delete ti.onrendered,tn(this.prop.container,ti)}).then(function(tn){(this.opt.html2canvas.onrendered||function(){})(tn),this.prop.canvas=tn,document.body.removeChild(this.prop.overlay)})},s.prototype.toContext2d=function(){var tn=[function(){return document.body.contains(this.prop.container)||this.toContainer()}];return this.thenList(tn).then(r).then(function(tn){var ti=this.opt.jsPDF,ta=this.opt.fontFaces,to=Object.assign({async:!0,allowTaint:!0,scale:"number"!=typeof this.opt.width||isNaN(this.opt.width)||"number"!=typeof this.opt.windowWidth||isNaN(this.opt.windowWidth)?1:this.opt.width/this.opt.windowWidth,scrollX:this.opt.scrollX||0,scrollY:this.opt.scrollY||0,backgroundColor:"#ffffff",imageTimeout:15e3,logging:!0,proxy:null,removeContainer:!0,foreignObjectRendering:!1,useCORS:!1},this.opt.html2canvas);if(delete to.onrendered,ti.context2d.autoPaging=void 0===this.opt.autoPaging||this.opt.autoPaging,ti.context2d.posX=this.opt.x,ti.context2d.posY=this.opt.y,ti.context2d.margin=this.opt.margin,ti.context2d.fontFaces=ta,ta)for(var ts=0;ts<ta.length;++ts){var tc=ta[ts],tu=tc.src.find(function(tn){return"truetype"===tn.format});tu&&ti.addFont(tu.url,tc.ref.name,tc.ref.style)}return to.windowHeight=to.windowHeight||0,to.windowHeight=0==to.windowHeight?Math.max(this.prop.container.clientHeight,this.prop.container.scrollHeight,this.prop.container.offsetHeight):to.windowHeight,ti.context2d.save(!0),tn(this.prop.container,to)}).then(function(tn){this.opt.jsPDF.context2d.restore(!0),(this.opt.html2canvas.onrendered||function(){})(tn),this.prop.canvas=tn,document.body.removeChild(this.prop.overlay)})},s.prototype.toImg=function(){return this.thenList([function(){return this.prop.canvas||this.toCanvas()}]).then(function(){var tn=this.prop.canvas.toDataURL("image/"+this.opt.image.type,this.opt.image.quality);this.prop.img=document.createElement("img"),this.prop.img.src=tn})},s.prototype.toPdf=function(){return this.thenList([function(){return this.toContext2d()}]).then(function(){this.prop.pdf=this.prop.pdf||this.opt.jsPDF})},s.prototype.output=function(tn,ti,ta){return"img"===(ta=ta||"pdf").toLowerCase()||"image"===ta.toLowerCase()?this.outputImg(tn,ti):this.outputPdf(tn,ti)},s.prototype.outputPdf=function(tn,ti){return this.thenList([function(){return this.prop.pdf||this.toPdf()}]).then(function(){return this.prop.pdf.output(tn,ti)})},s.prototype.outputImg=function(tn){return this.thenList([function(){return this.prop.img||this.toImg()}]).then(function(){switch(tn){case void 0:case"img":return this.prop.img;case"datauristring":case"dataurlstring":return this.prop.img.src;case"datauri":case"dataurl":return document.location.href=this.prop.img.src;default:throw'Image output type "'+tn+'" is not supported.'}})},s.prototype.save=function(tn){return this.thenList([function(){return this.prop.pdf||this.toPdf()}]).set(tn?{filename:tn}:null).then(function(){this.prop.pdf.save(this.opt.filename)})},s.prototype.doCallback=function(){return this.thenList([function(){return this.prop.pdf||this.toPdf()}]).then(function(){this.prop.callback(this.prop.pdf)})},s.prototype.set=function(tn){if("object"!==a(tn))return this;var ti=Object.keys(tn||{}).map(function(ti){if(ti in s.template.prop)return function(){this.prop[ti]=tn[ti]};switch(ti){case"margin":return this.setMargin.bind(this,tn.margin);case"jsPDF":return function(){return this.opt.jsPDF=tn.jsPDF,this.setPageSize()};case"pageSize":return this.setPageSize.bind(this,tn.pageSize);default:return function(){this.opt[ti]=tn[ti]}}},this);return this.then(function(){return this.thenList(ti)})},s.prototype.get=function(tn,ti){return this.then(function(){var ta=tn in s.template.prop?this.prop[tn]:this.opt[tn];return ti?ti(ta):ta})},s.prototype.setMargin=function(tn){return this.then(function(){switch(a(tn)){case"number":tn=[tn,tn,tn,tn];case"array":if(2===tn.length&&(tn=[tn[0],tn[1],tn[0],tn[1]]),4===tn.length)break;default:return this.error("Invalid margin array.")}this.opt.margin=tn}).then(this.setPageSize)},s.prototype.setPageSize=function(tn){function e(tn,ti){return Math.floor(tn*ti/72*96)}return this.then(function(){(tn=tn||E.getPageSize(this.opt.jsPDF)).hasOwnProperty("inner")||(tn.inner={width:tn.width-this.opt.margin[1]-this.opt.margin[3],height:tn.height-this.opt.margin[0]-this.opt.margin[2]},tn.inner.px={width:e(tn.inner.width,tn.k),height:e(tn.inner.height,tn.k)},tn.inner.ratio=tn.inner.height/tn.inner.width),this.prop.pageSize=tn})},s.prototype.setProgress=function(tn,ti,ta,to){return null!=tn&&(this.progress.val=tn),null!=ti&&(this.progress.state=ti),null!=ta&&(this.progress.n=ta),null!=to&&(this.progress.stack=to),this.progress.ratio=this.progress.val/this.progress.state,this},s.prototype.updateProgress=function(tn,ti,ta,to){return this.setProgress(tn?this.progress.val+tn:null,ti||null,ta?this.progress.n+ta:null,to?this.progress.stack.concat(to):null)},s.prototype.then=function(tn,ti){var ta=this;return this.thenCore(tn,ti,function(tn,ti){return ta.updateProgress(null,null,1,[tn]),Promise.prototype.then.call(this,function(ti){return ta.updateProgress(null,tn),ti}).then(tn,ti).then(function(tn){return ta.updateProgress(1),tn})})},s.prototype.thenCore=function(tn,ti,ta){ta=ta||Promise.prototype.then,tn&&(tn=tn.bind(this)),ti&&(ti=ti.bind(this));var to=-1!==Promise.toString().indexOf("[native code]")&&"Promise"===Promise.name?this:s.convert(Object.assign({},this),Promise.prototype),ts=ta.call(to,tn,ti);return s.convert(ts,this.__proto__)},s.prototype.thenExternal=function(tn,ti){return Promise.prototype.then.call(this,tn,ti)},s.prototype.thenList=function(tn){var ti=this;return tn.forEach(function(tn){ti=ti.thenCore(tn)}),ti},s.prototype.catch=function(tn){tn&&(tn=tn.bind(this));var ti=Promise.prototype.catch.call(this,tn);return s.convert(ti,this)},s.prototype.catchExternal=function(tn){return Promise.prototype.catch.call(this,tn)},s.prototype.error=function(tn){return this.then(function(){throw Error(tn)})},s.prototype.using=s.prototype.set,s.prototype.saveAs=s.prototype.save,s.prototype.export=s.prototype.output,s.prototype.run=s.prototype.then,E.getPageSize=function(tn,ti,ta){if("object"===(0,to.Z)(tn)){var ts=tn;tn=ts.orientation,ti=ts.unit||ti,ta=ts.format||ta}ti=ti||"mm",ta=ta||"a4",tn=(""+(tn||"P")).toLowerCase();var tc,tu=(""+ta).toLowerCase(),th={a0:[2383.94,3370.39],a1:[1683.78,2383.94],a2:[1190.55,1683.78],a3:[841.89,1190.55],a4:[595.28,841.89],a5:[419.53,595.28],a6:[297.64,419.53],a7:[209.76,297.64],a8:[147.4,209.76],a9:[104.88,147.4],a10:[73.7,104.88],b0:[2834.65,4008.19],b1:[2004.09,2834.65],b2:[1417.32,2004.09],b3:[1000.63,1417.32],b4:[708.66,1000.63],b5:[498.9,708.66],b6:[354.33,498.9],b7:[249.45,354.33],b8:[175.75,249.45],b9:[124.72,175.75],b10:[87.87,124.72],c0:[2599.37,3676.54],c1:[1836.85,2599.37],c2:[1298.27,1836.85],c3:[918.43,1298.27],c4:[649.13,918.43],c5:[459.21,649.13],c6:[323.15,459.21],c7:[229.61,323.15],c8:[161.57,229.61],c9:[113.39,161.57],c10:[79.37,113.39],dl:[311.81,623.62],letter:[612,792],"government-letter":[576,756],legal:[612,1008],"junior-legal":[576,360],ledger:[1224,792],tabloid:[792,1224],"credit-card":[153,243]};switch(ti){case"pt":tc=1;break;case"mm":tc=72/25.4;break;case"cm":tc=72/2.54;break;case"in":tc=72;break;case"px":tc=.75;break;case"pc":case"em":tc=12;break;case"ex":tc=6;break;default:throw"Invalid unit: "+ti}var tl,tf=0,td=0;if(th.hasOwnProperty(tu))tf=th[tu][1]/tc,td=th[tu][0]/tc;else try{tf=ta[1],td=ta[0]}catch(tn){throw Error("Invalid format: "+ta)}if("p"===tn||"portrait"===tn)tn="p",td>tf&&(tl=td,td=tf,tf=tl);else{if("l"!==tn&&"landscape"!==tn)throw"Invalid orientation: "+tn;tn="l",tf>td&&(tl=td,td=tf,tf=tl)}return{width:td,height:tf,unit:ti,k:tc,orientation:tn}},tn.html=function(tn,ti){(ti=ti||{}).callback=ti.callback||function(){},ti.html2canvas=ti.html2canvas||{},ti.html2canvas.canvas=ti.html2canvas.canvas||this.canvas,ti.jsPDF=ti.jsPDF||this,ti.fontFaces=ti.fontFaces?ti.fontFaces.map(jt):null;var ta=new s(ti);return ti.worker?ta:ta.from(tn).doCallback()}}(E.API),E.API.addJS=function(tn){return ev=tn,this.internal.events.subscribe("postPutResources",function(){eg=this.internal.newObject(),this.internal.out("<<"),this.internal.out("/Names [(EmbeddedJS) "+(eg+1)+" 0 R]"),this.internal.out(">>"),this.internal.out("endobj"),em=this.internal.newObject(),this.internal.out("<<"),this.internal.out("/S /JavaScript"),this.internal.out("/JS ("+ev+")"),this.internal.out(">>"),this.internal.out("endobj")}),this.internal.events.subscribe("putCatalog",function(){void 0!==eg&&void 0!==em&&this.internal.out("/Names <</JavaScript "+eg+" 0 R>>")}),this},(eS=E.API).events.push(["postPutResources",function(){var tn=/^(\d+) 0 obj$/;if(this.outline.root.children.length>0)for(var ti=this.outline.render().split(/\r\n/),ta=0;ta<ti.length;ta++){var to=ti[ta],ts=tn.exec(to);if(null!=ts){var tc=ts[1];this.internal.newObjectDeferredBegin(tc,!1)}this.internal.write(to)}if(this.outline.createNamedDestinations){var tu=this.internal.pages.length,th=[];for(ta=0;ta<tu;ta++){var tl=this.internal.newObject();th.push(tl);var tf=this.internal.getPageInfo(ta+1);this.internal.write("<< /D["+tf.objId+" 0 R /XYZ null null null]>> endobj")}var td=this.internal.newObject();for(this.internal.write("<< /Names [ "),ta=0;ta<th.length;ta++)this.internal.write("(page_"+(ta+1)+")"+th[ta]+" 0 R");this.internal.write(" ] >>","endobj"),e_=this.internal.newObject(),this.internal.write("<< /Dests "+td+" 0 R"),this.internal.write(">>","endobj")}}]),eS.events.push(["putCatalog",function(){this.outline.root.children.length>0&&(this.internal.write("/Outlines",this.outline.makeRef(this.outline.root)),this.outline.createNamedDestinations&&this.internal.write("/Names "+e_+" 0 R"))}]),eS.events.push(["initialized",function(){var tn=this;tn.outline={createNamedDestinations:!1,root:{children:[]}},tn.outline.add=function(tn,ti,ta){var to={title:ti,options:ta,children:[]};return null==tn&&(tn=this.root),tn.children.push(to),to},tn.outline.render=function(){return this.ctx={},this.ctx.val="",this.ctx.pdf=tn,this.genIds_r(this.root),this.renderRoot(this.root),this.renderItems(this.root),this.ctx.val},tn.outline.genIds_r=function(ti){ti.id=tn.internal.newObjectDeferred();for(var ta=0;ta<ti.children.length;ta++)this.genIds_r(ti.children[ta])},tn.outline.renderRoot=function(tn){this.objStart(tn),this.line("/Type /Outlines"),tn.children.length>0&&(this.line("/First "+this.makeRef(tn.children[0])),this.line("/Last "+this.makeRef(tn.children[tn.children.length-1]))),this.line("/Count "+this.count_r({count:0},tn)),this.objEnd()},tn.outline.renderItems=function(ti){for(var ta=this.ctx.pdf.internal.getVerticalCoordinateString,to=0;to<ti.children.length;to++){var ts=ti.children[to];this.objStart(ts),this.line("/Title "+this.makeString(ts.title)),this.line("/Parent "+this.makeRef(ti)),to>0&&this.line("/Prev "+this.makeRef(ti.children[to-1])),to<ti.children.length-1&&this.line("/Next "+this.makeRef(ti.children[to+1])),ts.children.length>0&&(this.line("/First "+this.makeRef(ts.children[0])),this.line("/Last "+this.makeRef(ts.children[ts.children.length-1])));var tc=this.count=this.count_r({count:0},ts);if(tc>0&&this.line("/Count "+tc),ts.options&&ts.options.pageNumber){var tu=tn.internal.getPageInfo(ts.options.pageNumber);this.line("/Dest ["+tu.objId+" 0 R /XYZ 0 "+ta(0)+" 0]")}this.objEnd()}for(var th=0;th<ti.children.length;th++)this.renderItems(ti.children[th])},tn.outline.line=function(tn){this.ctx.val+=tn+"\r\n"},tn.outline.makeRef=function(tn){return tn.id+" 0 R"},tn.outline.makeString=function(ti){return"("+tn.internal.pdfEscape(ti)+")"},tn.outline.objStart=function(tn){this.ctx.val+="\r\n"+tn.id+" 0 obj\r\n<<\r\n"},tn.outline.objEnd=function(){this.ctx.val+=">> \r\nendobj\r\n"},tn.outline.count_r=function(tn,ti){for(var ta=0;ta<ti.children.length;ta++)tn.count++,this.count_r(tn,ti.children[ta]);return tn.count}}]),eP=E.API,ek=[192,193,194,195,196,197,198,199],eP.processJPEG=function(tn,ti,ta,to,ts,tc){var tu,th=this.decode.DCT_DECODE,tl=null;if("string"==typeof tn||this.__addimage__.isArrayBuffer(tn)||this.__addimage__.isArrayBufferView(tn)){switch(tn=ts||tn,tn=this.__addimage__.isArrayBuffer(tn)?new Uint8Array(tn):tn,(tu=function(tn){for(var ti,ta=256*tn.charCodeAt(4)+tn.charCodeAt(5),to=tn.length,ts={width:0,height:0,numcomponents:1},tc=4;tc<to;tc+=2){if(tc+=ta,-1!==ek.indexOf(tn.charCodeAt(tc+1))){ti=256*tn.charCodeAt(tc+5)+tn.charCodeAt(tc+6),ts={width:256*tn.charCodeAt(tc+7)+tn.charCodeAt(tc+8),height:ti,numcomponents:tn.charCodeAt(tc+9)};break}ta=256*tn.charCodeAt(tc+2)+tn.charCodeAt(tc+3)}return ts}(tn=this.__addimage__.isArrayBufferView(tn)?this.__addimage__.arrayBufferToBinaryString(tn):tn)).numcomponents){case 1:tc=this.color_spaces.DEVICE_GRAY;break;case 4:tc=this.color_spaces.DEVICE_CMYK;break;case 3:tc=this.color_spaces.DEVICE_RGB}tl={data:tn,width:tu.width,height:tu.height,colorSpace:tc,bitsPerComponent:8,filter:th,index:ti,alias:ta}}return tl};var ey,ew,eN,eL,ex,eA,eS,e_,eP,ek,eF,eI,eC,ej,eO,eE=function(){function a(tn){var ti,ta,to,ts,tc,tu,th,tl,tf,td,tp,tg,tm,tv;for(this.data=tn,this.pos=8,this.palette=[],this.imgData=[],this.transparency={},this.animation=null,this.text={},tu=null;;){switch(ti=this.readUInt32(),tf=(function(){var tn,ti;for(ti=[],tn=0;tn<4;++tn)ti.push(String.fromCharCode(this.data[this.pos++]));return ti}).call(this).join("")){case"IHDR":this.width=this.readUInt32(),this.height=this.readUInt32(),this.bits=this.data[this.pos++],this.colorType=this.data[this.pos++],this.compressionMethod=this.data[this.pos++],this.filterMethod=this.data[this.pos++],this.interlaceMethod=this.data[this.pos++];break;case"acTL":this.animation={numFrames:this.readUInt32(),numPlays:this.readUInt32()||1/0,frames:[]};break;case"PLTE":this.palette=this.read(ti);break;case"fcTL":tu&&this.animation.frames.push(tu),this.pos+=4,tu={width:this.readUInt32(),height:this.readUInt32(),xOffset:this.readUInt32(),yOffset:this.readUInt32()},tc=this.readUInt16(),ts=this.readUInt16()||100,tu.delay=1e3*tc/ts,tu.disposeOp=this.data[this.pos++],tu.blendOp=this.data[this.pos++],tu.data=[];break;case"IDAT":case"fdAT":for("fdAT"===tf&&(this.pos+=4,ti-=4),tn=(null!=tu?tu.data:void 0)||this.imgData,tg=0;0<=ti?tg<ti:tg>ti;0<=ti?++tg:--tg)tn.push(this.data[this.pos++]);break;case"tRNS":switch(this.transparency={},this.colorType){case 3:if(to=this.palette.length/3,this.transparency.indexed=this.read(ti),this.transparency.indexed.length>to)throw Error("More transparent colors than palette size");if((td=to-this.transparency.indexed.length)>0)for(tm=0;0<=td?tm<td:tm>td;0<=td?++tm:--tm)this.transparency.indexed.push(255);break;case 0:this.transparency.grayscale=this.read(ti)[0];break;case 2:this.transparency.rgb=this.read(ti)}break;case"tEXt":th=(tp=this.read(ti)).indexOf(0),tl=String.fromCharCode.apply(String,tp.slice(0,th)),this.text[tl]=String.fromCharCode.apply(String,tp.slice(th+1));break;case"IEND":return tu&&this.animation.frames.push(tu),this.colors=(function(){switch(this.colorType){case 0:case 3:case 4:return 1;case 2:case 6:return 3}}).call(this),this.hasAlphaChannel=4===(tv=this.colorType)||6===tv,ta=this.colors+(this.hasAlphaChannel?1:0),this.pixelBitlength=this.bits*ta,this.colorSpace=(function(){switch(this.colors){case 1:return"DeviceGray";case 3:return"DeviceRGB"}}).call(this),void(this.imgData=new Uint8Array(this.imgData));default:this.pos+=ti}if(this.pos+=4,this.pos>this.data.length)throw Error("Incomplete or corrupt PNG file")}}a.prototype.read=function(tn){var ti,ta;for(ta=[],ti=0;0<=tn?ti<tn:ti>tn;0<=tn?++ti:--ti)ta.push(this.data[this.pos++]);return ta},a.prototype.readUInt32=function(){return this.data[this.pos++]<<24|this.data[this.pos++]<<16|this.data[this.pos++]<<8|this.data[this.pos++]},a.prototype.readUInt16=function(){return this.data[this.pos++]<<8|this.data[this.pos++]},a.prototype.decodePixels=function(tn){var ti=this.pixelBitlength/8,ta=new Uint8Array(this.width*this.height*ti),to=0,tc=this;if(null==tn&&(tn=this.imgData),0===tn.length)return new Uint8Array(0);function o(ts,tu,th,tl){var tf,td,tp,tg,tm,tv,tb,ty,tw,tN,tL,tx,tA,tS,t_,tP,tk,tF,tI,tC,tj,tO=Math.ceil((tc.width-ts)/th),tE=Math.ceil((tc.height-tu)/tl),tB=tc.width==tO&&tc.height==tE;for(tS=ti*tO,tx=tB?ta:new Uint8Array(tS*tE),tv=tn.length,tA=0,td=0;tA<tE&&to<tv;){switch(tn[to++]){case 0:for(tg=tk=0;tk<tS;tg=tk+=1)tx[td++]=tn[to++];break;case 1:for(tg=tF=0;tF<tS;tg=tF+=1)tf=tn[to++],tm=tg<ti?0:tx[td-ti],tx[td++]=(tf+tm)%256;break;case 2:for(tg=tI=0;tI<tS;tg=tI+=1)tf=tn[to++],tp=(tg-tg%ti)/ti,t_=tA&&tx[(tA-1)*tS+tp*ti+tg%ti],tx[td++]=(t_+tf)%256;break;case 3:for(tg=tC=0;tC<tS;tg=tC+=1)tf=tn[to++],tp=(tg-tg%ti)/ti,tm=tg<ti?0:tx[td-ti],t_=tA&&tx[(tA-1)*tS+tp*ti+tg%ti],tx[td++]=(tf+Math.floor((tm+t_)/2))%256;break;case 4:for(tg=tj=0;tj<tS;tg=tj+=1)tf=tn[to++],tp=(tg-tg%ti)/ti,tm=tg<ti?0:tx[td-ti],0===tA?t_=tP=0:(t_=tx[(tA-1)*tS+tp*ti+tg%ti],tP=tp&&tx[(tA-1)*tS+(tp-1)*ti+tg%ti]),ty=Math.abs((tb=tm+t_-tP)-tm),tN=Math.abs(tb-t_),tL=Math.abs(tb-tP),tw=ty<=tN&&ty<=tL?tm:tN<=tL?t_:tP,tx[td++]=(tf+tw)%256;break;default:throw Error("Invalid filter algorithm: "+tn[to-1])}if(!tB){var tM=((tu+tA*tl)*tc.width+ts)*ti,tq=tA*tS;for(tg=0;tg<tO;tg+=1){for(var tD=0;tD<ti;tD+=1)ta[tM++]=tx[tq++];tM+=(th-1)*ti}}tA++}}return tn=(0,ts.HT)(tn),1==tc.interlaceMethod?(o(0,0,8,8),o(4,0,8,8),o(0,4,4,8),o(2,0,4,4),o(0,2,2,4),o(1,0,2,2),o(0,1,1,2)):o(0,0,1,1),ta},a.prototype.decodePalette=function(){var tn,ti,ta,to,ts,tc,tu,th,tl;for(ta=this.palette,tc=this.transparency.indexed||[],ts=new Uint8Array((tc.length||0)+ta.length),to=0,tn=0,ti=tu=0,th=ta.length;tu<th;ti=tu+=3)ts[to++]=ta[ti],ts[to++]=ta[ti+1],ts[to++]=ta[ti+2],ts[to++]=null!=(tl=tc[tn++])?tl:255;return ts},a.prototype.copyToImageData=function(tn,ti){var ta,to,ts,tc,tu,th,tl,tf,td,tp,tg;if(to=this.colors,td=null,ta=this.hasAlphaChannel,this.palette.length&&(td=null!=(tg=this._decodedPalette)?tg:this._decodedPalette=this.decodePalette(),to=4,ta=!0),tf=(ts=tn.data||tn).length,tu=td||ti,tc=th=0,1===to)for(;tc<tf;)tl=td?4*ti[tc/4]:th,tp=tu[tl++],ts[tc++]=tp,ts[tc++]=tp,ts[tc++]=tp,ts[tc++]=ta?tu[tl++]:255,th=tl;else for(;tc<tf;)tl=td?4*ti[tc/4]:th,ts[tc++]=tu[tl++],ts[tc++]=tu[tl++],ts[tc++]=tu[tl++],ts[tc++]=ta?tu[tl++]:255,th=tl},a.prototype.decode=function(){var tn;return tn=new Uint8Array(this.width*this.height*4),this.copyToImageData(tn,this.decodePixels()),tn};var tn,ti,ta,o=function(){if("[object Window]"===Object.prototype.toString.call(tc)){try{ta=(ti=tc.document.createElement("canvas")).getContext("2d")}catch(tn){return!1}return!0}return!1};return o(),tn=function(tn){var to;if(!0===o())return ta.width=tn.width,ta.height=tn.height,ta.clearRect(0,0,tn.width,tn.height),ta.putImageData(tn,0,0),(to=new Image).src=ti.toDataURL(),to;throw Error("This method requires a Browser with Canvas-capability.")},a.prototype.decodeFrames=function(ti){var ta,to,ts,tc,tu,th,tl,tf;if(this.animation){for(tf=[],to=tu=0,th=(tl=this.animation.frames).length;tu<th;to=++tu)ta=tl[to],ts=ti.createImageData(ta.width,ta.height),tc=this.decodePixels(new Uint8Array(ta.data)),this.copyToImageData(ts,tc),ta.imageData=ts,tf.push(ta.image=tn(ts));return tf}},a.prototype.renderFrame=function(tn,ti){var ta,to,ts;return ta=(to=this.animation.frames)[ti],ts=to[ti-1],0===ti&&tn.clearRect(0,0,this.width,this.height),1===(null!=ts?ts.disposeOp:void 0)?tn.clearRect(ts.xOffset,ts.yOffset,ts.width,ts.height):2===(null!=ts?ts.disposeOp:void 0)&&tn.putImageData(ts.imageData,ts.xOffset,ts.yOffset),0===ta.blendOp&&tn.clearRect(ta.xOffset,ta.yOffset,ta.width,ta.height),tn.drawImage(ta.image,ta.xOffset,ta.yOffset)},a.prototype.animate=function(tn){var ti,ta,to,ts,tc,tu,th=this;return ta=0,ts=(tu=this.animation).numFrames,to=tu.frames,tc=tu.numPlays,(ti=function(){var tu,tl;if(tl=to[tu=ta++%ts],th.renderFrame(tn,tu),ts>1&&ta/ts<tc)return th.animation._timeout=setTimeout(ti,tl.delay)})()},a.prototype.stopAnimation=function(){var tn;return clearTimeout(null!=(tn=this.animation)?tn._timeout:void 0)},a.prototype.render=function(tn){var ti,ta;return tn._png&&tn._png.stopAnimation(),tn._png=this,tn.width=this.width,tn.height=this.height,ti=tn.getContext("2d"),this.animation?(this.decodeFrames(ti),this.animate(ti)):(ta=ti.createImageData(this.width,this.height),this.copyToImageData(ta,this.decodePixels()),ti.putImageData(ta,0,0))},a}();/**
 * @license
 *
 * Copyright (c) 2014 James Robb, https://github.com/jamesbrobb
 *
 * Permission is hereby granted, free of charge, to any person obtaining
 * a copy of this software and associated documentation files (the
 * "Software"), to deal in the Software without restriction, including
 * without limitation the rights to use, copy, modify, merge, publish,
 * distribute, sublicense, and/or sell copies of the Software, and to
 * permit persons to whom the Software is furnished to do so, subject to
 * the following conditions:
 *
 * The above copyright notice and this permission notice shall be
 * included in all copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND,
 * EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND
 * NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE
 * LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION
 * OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION
 * WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
 * ====================================================================
 *//**
 * @license
 * (c) Dean McNamee <<EMAIL>>, 2013.
 *
 * https://github.com/deanm/omggif
 *
 * Permission is hereby granted, free of charge, to any person obtaining a copy
 * of this software and associated documentation files (the "Software"), to
 * deal in the Software without restriction, including without limitation the
 * rights to use, copy, modify, merge, publish, distribute, sublicense, and/or
 * sell copies of the Software, and to permit persons to whom the Software is
 * furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in
 * all copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING
 * FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS
 * IN THE SOFTWARE.
 *
 * omggif is a JavaScript implementation of a GIF 89a encoder and decoder,
 * including animation and compression.  It does not rely on any specific
 * underlying system, so should run in the browser, Node, or Plask.
 */function Zt(tn){var ti=0;if(71!==tn[ti++]||73!==tn[ti++]||70!==tn[ti++]||56!==tn[ti++]||56!=(tn[ti++]+1&253)||97!==tn[ti++])throw Error("Invalid GIF 87a/89a header.");var ta=tn[ti++]|tn[ti++]<<8,to=tn[ti++]|tn[ti++]<<8,ts=tn[ti++],tc=1<<(7&ts)+1;tn[ti++],tn[ti++];var tu=null,th=null;ts>>7&&(tu=ti,th=tc,ti+=3*tc);var tl=!0,tf=[],td=0,tp=null,tg=0,tm=null;for(this.width=ta,this.height=to;tl&&ti<tn.length;)switch(tn[ti++]){case 33:switch(tn[ti++]){case 255:if(11!==tn[ti]||78==tn[ti+1]&&69==tn[ti+2]&&84==tn[ti+3]&&83==tn[ti+4]&&67==tn[ti+5]&&65==tn[ti+6]&&80==tn[ti+7]&&69==tn[ti+8]&&50==tn[ti+9]&&46==tn[ti+10]&&48==tn[ti+11]&&3==tn[ti+12]&&1==tn[ti+13]&&0==tn[ti+16])ti+=14,tm=tn[ti++]|tn[ti++]<<8,ti++;else for(ti+=12;;){if(!((tb=tn[ti++])>=0))throw Error("Invalid block size");if(0===tb)break;ti+=tb}break;case 249:if(4!==tn[ti++]||0!==tn[ti+4])throw Error("Invalid graphics extension block.");var tv=tn[ti++];td=tn[ti++]|tn[ti++]<<8,tp=tn[ti++],0==(1&tv)&&(tp=null),tg=tv>>2&7,ti++;break;case 254:for(;;){if(!((tb=tn[ti++])>=0))throw Error("Invalid block size");if(0===tb)break;ti+=tb}break;default:throw Error("Unknown graphic control label: 0x"+tn[ti-1].toString(16))}break;case 44:var tb,ty=tn[ti++]|tn[ti++]<<8,tw=tn[ti++]|tn[ti++]<<8,tN=tn[ti++]|tn[ti++]<<8,tL=tn[ti++]|tn[ti++]<<8,tx=tn[ti++],tA=tx>>6&1,tS=1<<(7&tx)+1,t_=tu,tP=th,tk=!1;tx>>7&&(tk=!0,t_=ti,tP=tS,ti+=3*tS);var tF=ti;for(ti++;;){if(!((tb=tn[ti++])>=0))throw Error("Invalid block size");if(0===tb)break;ti+=tb}tf.push({x:ty,y:tw,width:tN,height:tL,has_local_palette:tk,palette_offset:t_,palette_size:tP,data_offset:tF,data_length:ti-tF,transparent_index:tp,interlaced:!!tA,delay:td,disposal:tg});break;case 59:tl=!1;break;default:throw Error("Unknown gif block: 0x"+tn[ti-1].toString(16))}this.numFrames=function(){return tf.length},this.loopCount=function(){return tm},this.frameInfo=function(tn){if(tn<0||tn>=tf.length)throw Error("Frame index out of range.");return tf[tn]},this.decodeAndBlitFrameBGRA=function(ti,to){var ts=this.frameInfo(ti),tc=ts.width*ts.height,tu=new Uint8Array(tc);$t(tn,ts.data_offset,tu,tc);var th=ts.palette_offset,tl=ts.transparent_index;null===tl&&(tl=256);var tf=ts.width,td=ta-tf,tp=tf,tg=4*(ts.y*ta+ts.x),tm=4*((ts.y+ts.height)*ta+ts.x),tv=tg,tb=4*td;!0===ts.interlaced&&(tb+=4*ta*7);for(var ty=8,tw=0,tN=tu.length;tw<tN;++tw){var tL=tu[tw];if(0===tp&&(tp=tf,(tv+=tb)>=tm&&(tb=4*td+4*ta*(ty-1),tv=tg+(tf+td)*(ty<<1),ty>>=1)),tL===tl)tv+=4;else{var tx=tn[th+3*tL],tA=tn[th+3*tL+1],tS=tn[th+3*tL+2];to[tv++]=tS,to[tv++]=tA,to[tv++]=tx,to[tv++]=255}--tp}},this.decodeAndBlitFrameRGBA=function(ti,to){var ts=this.frameInfo(ti),tc=ts.width*ts.height,tu=new Uint8Array(tc);$t(tn,ts.data_offset,tu,tc);var th=ts.palette_offset,tl=ts.transparent_index;null===tl&&(tl=256);var tf=ts.width,td=ta-tf,tp=tf,tg=4*(ts.y*ta+ts.x),tm=4*((ts.y+ts.height)*ta+ts.x),tv=tg,tb=4*td;!0===ts.interlaced&&(tb+=4*ta*7);for(var ty=8,tw=0,tN=tu.length;tw<tN;++tw){var tL=tu[tw];if(0===tp&&(tp=tf,(tv+=tb)>=tm&&(tb=4*td+4*ta*(ty-1),tv=tg+(tf+td)*(ty<<1),ty>>=1)),tL===tl)tv+=4;else{var tx=tn[th+3*tL],tA=tn[th+3*tL+1],tS=tn[th+3*tL+2];to[tv++]=tx,to[tv++]=tA,to[tv++]=tS,to[tv++]=255}--tp}}}function $t(tn,ti,ta,to){for(var ts=tn[ti++],tc=1<<ts,th=tc+1,tl=th+1,tf=ts+1,td=(1<<tf)-1,tp=0,tg=0,tm=0,tv=tn[ti++],tb=new Int32Array(4096),ty=null;;){for(;tp<16&&0!==tv;)tg|=tn[ti++]<<tp,tp+=8,1===tv?tv=tn[ti++]:--tv;if(tp<tf)break;var tw=tg&td;if(tg>>=tf,tp-=tf,tw!==tc){if(tw===th)break;for(var tN=tw<tl?tw:ty,tL=0,tx=tN;tx>tc;)tx=tb[tx]>>8,++tL;var tA=tx;if(tm+tL+(tN!==tw?1:0)>to)return void tu.log("Warning, gif stream longer than expected.");ta[tm++]=tA;var tS=tm+=tL;for(tN!==tw&&(ta[tm++]=tA),tx=tN;tL--;)tx=tb[tx],ta[--tS]=255&tx,tx>>=8;null!==ty&&tl<4096&&(tb[tl++]=ty<<8|tA,tl>=td+1&&tf<12&&(++tf,td=td<<1|1)),ty=tw}else tl=th+1,td=(1<<(tf=ts+1))-1,ty=null}return tm!==to&&tu.log("Warning, gif stream shorter than expected."),ta}/**
 * @license
  Copyright (c) 2008, Adobe Systems Incorporated
  All rights reserved.

  Redistribution and use in source and binary forms, with or without 
  modification, are permitted provided that the following conditions are
  met:

  * Redistributions of source code must retain the above copyright notice, 
    this list of conditions and the following disclaimer.
  
  * Redistributions in binary form must reproduce the above copyright
    notice, this list of conditions and the following disclaimer in the 
    documentation and/or other materials provided with the distribution.
  
  * Neither the name of Adobe Systems Incorporated nor the names of its 
    contributors may be used to endorse or promote products derived from 
    this software without specific prior written permission.

  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS
  IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO,
  THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR
  PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR 
  CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL,
  EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO,
  PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR
  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF
  LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING
  NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
  SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
*/function Qt(tn){var ti,ta,to,ts,tc,tu=Math.floor,th=Array(64),tl=Array(64),tf=Array(64),td=Array(64),tp=Array(65535),tg=Array(65535),tm=Array(64),tv=Array(64),tb=[],ty=0,tw=7,tN=Array(64),tL=Array(64),tx=Array(64),tA=Array(256),tS=Array(2048),t_=[0,1,5,6,14,15,27,28,2,4,7,13,16,26,29,42,3,8,12,17,25,30,41,43,9,11,18,24,31,40,44,53,10,19,23,32,39,45,52,54,20,22,33,38,46,51,55,60,21,34,37,47,50,56,59,61,35,36,48,49,57,58,62,63],tP=[0,0,1,5,1,1,1,1,1,1,0,0,0,0,0,0,0],tk=[0,1,2,3,4,5,6,7,8,9,10,11],tF=[0,0,2,1,3,3,2,4,3,5,5,4,4,0,0,1,125],tI=[1,2,3,0,4,17,5,18,33,49,65,6,19,81,97,7,34,113,20,50,129,145,161,8,35,66,177,193,21,82,209,240,36,51,98,114,130,9,10,22,23,24,25,26,37,38,39,40,41,42,52,53,54,55,56,57,58,67,68,69,70,71,72,73,74,83,84,85,86,87,88,89,90,99,100,101,102,103,104,105,106,115,116,117,118,119,120,121,122,131,132,133,134,135,136,137,138,146,147,148,149,150,151,152,153,154,162,163,164,165,166,167,168,169,170,178,179,180,181,182,183,184,185,186,194,195,196,197,198,199,200,201,202,210,211,212,213,214,215,216,217,218,225,226,227,228,229,230,231,232,233,234,241,242,243,244,245,246,247,248,249,250],tC=[0,0,3,1,1,1,1,1,1,1,1,1,0,0,0,0,0],tj=[0,1,2,3,4,5,6,7,8,9,10,11],tO=[0,0,2,1,2,4,4,3,4,7,5,4,4,0,1,2,119],tE=[0,1,2,3,17,4,5,33,49,6,18,65,81,7,97,113,19,34,50,129,8,20,66,145,161,177,193,9,35,51,82,240,21,98,114,209,10,22,36,52,225,37,241,23,24,25,26,38,39,40,41,42,53,54,55,56,57,58,67,68,69,70,71,72,73,74,83,84,85,86,87,88,89,90,99,100,101,102,103,104,105,106,115,116,117,118,119,120,121,122,130,131,132,133,134,135,136,137,138,146,147,148,149,150,151,152,153,154,162,163,164,165,166,167,168,169,170,178,179,180,181,182,183,184,185,186,194,195,196,197,198,199,200,201,202,210,211,212,213,214,215,216,217,218,226,227,228,229,230,231,232,233,234,242,243,244,245,246,247,248,249,250];function j(tn,ti){for(var ta=0,to=0,ts=[],tc=1;tc<=16;tc++){for(var tu=1;tu<=tn[tc];tu++)ts[ti[to]]=[],ts[ti[to]][0]=ta,ts[ti[to]][1]=tc,to++,ta++;ta*=2}return ts}function O(tn){for(var ti=tn[0],ta=tn[1]-1;ta>=0;)ti&1<<ta&&(ty|=1<<tw),ta--,--tw<0&&(255==ty?(B(255),B(0)):B(ty),tw=7,ty=0)}function B(tn){tb.push(tn)}function M(tn){B(tn>>8&255),B(255&tn)}function E(tn,ti,ta,to,ts){for(var tc,tu=ts[0],th=ts[240],tl=function(tn,ti){var ta,to,ts,tc,tu,th,tl,tf,td,tp,tg=0;for(td=0;td<8;++td){ta=tn[tg],to=tn[tg+1],ts=tn[tg+2],tc=tn[tg+3],tu=tn[tg+4],th=tn[tg+5],tl=tn[tg+6];var tv=ta+(tf=tn[tg+7]),tb=ta-tf,ty=to+tl,tw=to-tl,tN=ts+th,tL=ts-th,tx=tc+tu,tA=tc-tu,tS=tv+tx,t_=tv-tx,tP=ty+tN,tk=ty-tN;tn[tg]=tS+tP,tn[tg+4]=tS-tP;var tF=.707106781*(tk+t_);tn[tg+2]=t_+tF,tn[tg+6]=t_-tF;var tI=.382683433*((tS=tA+tL)-(tk=tw+tb)),tC=.5411961*tS+tI,tj=1.306562965*tk+tI,tO=.707106781*(tP=tL+tw),tE=tb+tO,tB=tb-tO;tn[tg+5]=tB+tC,tn[tg+3]=tB-tC,tn[tg+1]=tE+tj,tn[tg+7]=tE-tj,tg+=8}for(tg=0,td=0;td<8;++td){ta=tn[tg],to=tn[tg+8],ts=tn[tg+16],tc=tn[tg+24],tu=tn[tg+32],th=tn[tg+40],tl=tn[tg+48];var tM=ta+(tf=tn[tg+56]),tq=ta-tf,tD=to+tl,tR=to-tl,tT=ts+th,tU=ts-th,tz=tc+tu,tV=tc-tu,tH=tM+tz,tW=tM-tz,tG=tD+tT,tJ=tD-tT;tn[tg]=tH+tG,tn[tg+32]=tH-tG;var tY=.707106781*(tJ+tW);tn[tg+16]=tW+tY,tn[tg+48]=tW-tY;var tX=.382683433*((tH=tV+tU)-(tJ=tR+tq)),tZ=.5411961*tH+tX,tK=1.306562965*tJ+tX,t$=.707106781*(tG=tU+tR),tQ=tq+t$,t1=tq-t$;tn[tg+40]=t1+tZ,tn[tg+24]=t1-tZ,tn[tg+8]=tQ+tK,tn[tg+56]=tQ-tK,tg++}for(td=0;td<64;++td)tp=tn[td]*ti[td],tm[td]=tp>0?tp+.5|0:tp-.5|0;return tm}(tn,ti),tf=0;tf<64;++tf)tv[t_[tf]]=tl[tf];var td=tv[0]-ta;ta=tv[0],0==td?O(to[0]):(O(to[tg[tc=32767+td]]),O(tp[tc]));for(var tb=63;tb>0&&0==tv[tb];)tb--;if(0==tb)return O(tu),ta;for(var ty,tw=1;tw<=tb;){for(var tN=tw;0==tv[tw]&&tw<=tb;)++tw;var tL=tw-tN;if(tL>=16){ty=tL>>4;for(var tx=1;tx<=ty;++tx)O(th);tL&=15}O(ts[(tL<<4)+tg[tc=32767+tv[tw]]]),O(tp[tc]),tw++}return 63!=tb&&O(tu),ta}function q(tn){tc!=(tn=Math.min(Math.max(tn,1),100))&&(function(tn){for(var ti=[16,11,10,16,24,40,51,61,12,12,14,19,26,58,60,55,14,13,16,24,40,57,69,56,14,17,22,29,51,87,80,62,18,22,37,56,68,109,103,77,24,35,55,64,81,104,113,92,49,64,78,87,103,121,120,101,72,92,95,98,112,100,103,99],ta=0;ta<64;ta++){var to=tu((ti[ta]*tn+50)/100);to=Math.min(Math.max(to,1),255),th[t_[ta]]=to}for(var ts=[17,18,24,47,99,99,99,99,18,21,26,66,99,99,99,99,24,26,56,99,99,99,99,99,47,66,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99],tc=0;tc<64;tc++){var tp=tu((ts[tc]*tn+50)/100);tp=Math.min(Math.max(tp,1),255),tl[t_[tc]]=tp}for(var tg=[1,1.387039845,1.306562965,1.175875602,1,.785694958,.5411961,.275899379],tm=0,tv=0;tv<8;tv++)for(var tb=0;tb<8;tb++)tf[tm]=1/(th[t_[tm]]*tg[tv]*tg[tb]*8),td[tm]=1/(tl[t_[tm]]*tg[tv]*tg[tb]*8),tm++}(tn<50?Math.floor(5e3/tn):Math.floor(200-2*tn)),tc=tn)}this.encode=function(tn,tc){tc&&q(tc),tb=[],ty=0,tw=7,M(65496),M(65504),M(16),B(74),B(70),B(73),B(70),B(0),B(1),B(1),B(0),M(1),M(1),B(0),B(0),function(){M(65499),M(132),B(0);for(var tn=0;tn<64;tn++)B(th[tn]);B(1);for(var ti=0;ti<64;ti++)B(tl[ti])}(),tm=tn.width,tv=tn.height,M(65472),M(17),B(8),M(tv),M(tm),B(3),B(1),B(17),B(0),B(2),B(17),B(1),B(3),B(17),B(1),function(){M(65476),M(418),B(0);for(var tn=0;tn<16;tn++)B(tP[tn+1]);for(var ti=0;ti<=11;ti++)B(tk[ti]);B(16);for(var ta=0;ta<16;ta++)B(tF[ta+1]);for(var to=0;to<=161;to++)B(tI[to]);B(1);for(var ts=0;ts<16;ts++)B(tC[ts+1]);for(var tc=0;tc<=11;tc++)B(tj[tc]);B(17);for(var tu=0;tu<16;tu++)B(tO[tu+1]);for(var th=0;th<=161;th++)B(tE[th])}(),M(65498),M(12),B(3),B(1),B(0),B(2),B(17),B(3),B(17),B(0),B(63),B(0);var tu=0,tp=0,tg=0;ty=0,tw=7,this.encode.displayName="_encode_";for(var tm,tv,tA,t_,tB,tM,tq,tD,tR,tT,tU,tz=tn.data,tV=tn.width,tH=tn.height,tW=4*tV,tG=0;tG<tH;){for(tA=0;tA<tW;){for(tq=tW*tG+tA,tR=-1,tT=0,tU=0;tU<64;tU++)tD=tq+(tT=tU>>3)*tW+(tR=4*(7&tU)),tG+tT>=tH&&(tD-=tW*(tG+1+tT-tH)),tA+tR>=tW&&(tD-=tA+tR-tW+4),t_=tz[tD++],tB=tz[tD++],tM=tz[tD++],tN[tU]=(tS[t_]+tS[tB+256>>0]+tS[tM+512>>0]>>16)-128,tL[tU]=(tS[t_+768>>0]+tS[tB+1024>>0]+tS[tM+1280>>0]>>16)-128,tx[tU]=(tS[t_+1280>>0]+tS[tB+1536>>0]+tS[tM+1792>>0]>>16)-128;tu=E(tN,tf,tu,ti,to),tp=E(tL,td,tp,ta,ts),tg=E(tx,td,tg,ta,ts),tA+=32}tG+=8}if(tw>=0){var tJ=[];tJ[1]=tw+1,tJ[0]=(1<<tw+1)-1,O(tJ)}return M(65497),new Uint8Array(tb)},tn=tn||50,function(){for(var tn=String.fromCharCode,ti=0;ti<256;ti++)tA[ti]=tn(ti)}(),ti=j(tP,tk),ta=j(tC,tj),to=j(tF,tI),ts=j(tO,tE),function(){for(var tn=1,ti=2,ta=1;ta<=15;ta++){for(var to=tn;to<ti;to++)tg[32767+to]=ta,tp[32767+to]=[],tp[32767+to][1]=ta,tp[32767+to][0]=to;for(var ts=-(ti-1);ts<=-tn;ts++)tg[32767+ts]=ta,tp[32767+ts]=[],tp[32767+ts][1]=ta,tp[32767+ts][0]=ti-1+ts;tn<<=1,ti<<=1}}(),function(){for(var tn=0;tn<256;tn++)tS[tn]=19595*tn,tS[tn+256>>0]=38470*tn,tS[tn+512>>0]=7471*tn+32768,tS[tn+768>>0]=-11059*tn,tS[tn+1024>>0]=-21709*tn,tS[tn+1280>>0]=32768*tn+8421375,tS[tn+1536>>0]=-27439*tn,tS[tn+1792>>0]=-5329*tn}(),q(tn)}/**
 * @license
 * Copyright (c) 2017 Aras Abbasi
 *
 * Licensed under the MIT License.
 * http://opensource.org/licenses/mit-license
 */function te(tn,ti){if(this.pos=0,this.buffer=tn,this.datav=new DataView(tn.buffer),this.is_with_alpha=!!ti,this.bottom_up=!0,this.flag=String.fromCharCode(this.buffer[0])+String.fromCharCode(this.buffer[1]),this.pos+=2,-1===["BM","BA","CI","CP","IC","PT"].indexOf(this.flag))throw Error("Invalid BMP File");this.parseHeader(),this.parseBGR()}function ee(tn){function e(tn){if(!tn)throw Error("assert :P")}function r(tn,ti,ta){for(var to=0;4>to;to++)if(tn[ti+to]!=ta.charCodeAt(to))return!0;return!1}function n(tn,ti,ta,to,ts){for(var tc=0;tc<ts;tc++)tn[ti+tc]=ta[to+tc]}function i(tn,ti,ta,to){for(var ts=0;ts<to;ts++)tn[ti+ts]=ta}function a(tn){return new Int32Array(tn)}function o(tn,ti){for(var ta=[],to=0;to<tn;to++)ta.push(new ti);return ta}function s(tn,ti){var ta=[];return function t(tn,ta,to){for(var ts=to[ta],tc=0;tc<ts&&(tn.push(to.length>ta+1?[]:new ti),!(to.length<ta+1));tc++)t(tn[tc],ta+1,to)}(ta,0,tn),ta}var c=function(){var tn=this;function c(tn,ti){for(var ta=1<<ti-1>>>0;tn&ta;)ta>>>=1;return ta?(tn&ta-1)+ta:tn}function u(tn,ti,ta,to,ts){e(!(to%ta));do tn[ti+(to-=ta)]=ts;while(0<to)}function h(tn,ti,ta,to,ts){if(e(2328>=ts),512>=ts)var tc=a(512);else if(null==(tc=a(ts)))return 0;return function(tn,ti,ta,to,ts,tc){var tu,th,tl=ti,tf=1<<ta,td=a(16),tp=a(16);for(e(0!=ts),e(null!=to),e(null!=tn),e(0<ta),th=0;th<ts;++th){if(15<to[th])return 0;++td[to[th]]}if(td[0]==ts)return 0;for(tp[1]=0,tu=1;15>tu;++tu){if(td[tu]>1<<tu)return 0;tp[tu+1]=tp[tu]+td[tu]}for(th=0;th<ts;++th)tu=to[th],0<to[th]&&(tc[tp[tu]++]=th);if(1==tp[15])return(to=new l).g=0,to.value=tc[0],u(tn,tl,1,tf,to),tf;var tg,tm=-1,tv=tf-1,tb=0,ty=1,tw=1,tN=1<<ta;for(th=0,tu=1,ts=2;tu<=ta;++tu,ts<<=1){if(ty+=tw<<=1,0>(tw-=td[tu]))return 0;for(;0<td[tu];--td[tu])(to=new l).g=tu,to.value=tc[th++],u(tn,tl+tb,ts,tN,to),tb=c(tb,tu)}for(tu=ta+1,ts=2;15>=tu;++tu,ts<<=1){if(ty+=tw<<=1,0>(tw-=td[tu]))return 0;for(;0<td[tu];--td[tu]){if(to=new l,(tb&tv)!=tm){for(tl+=tN,tg=1<<(tm=tu)-ta;15>tm&&!(0>=(tg-=td[tm]));)++tm,tg<<=1;tf+=tN=1<<(tg=tm-ta),tn[ti+(tm=tb&tv)].g=tg+ta,tn[ti+tm].value=tl-ti-tm}to.g=tu-ta,to.value=tc[th++],u(tn,tl+(tb>>ta),ts,tN,to),tb=c(tb,tu)}}return ty!=2*tp[15]-1?0:tf}(tn,ti,ta,to,ts,tc)}function l(){this.value=this.g=0}function f(){this.value=this.g=0}function d(){this.G=o(5,l),this.H=a(5),this.jc=this.Qb=this.qb=this.nd=0,this.pd=o(ti,f)}function p(tn,ti,ta,to){e(null!=tn),e(null!=ti),e(2147483648>to),tn.Ca=254,tn.I=0,tn.b=-8,tn.Ka=0,tn.oa=ti,tn.pa=ta,tn.Jd=ti,tn.Yc=ta+to,tn.Zc=4<=to?ta+to-4+1:ta,_(tn)}function g(tn,ti){for(var ta=0;0<ti--;)ta|=k(tn,128)<<ti;return ta}function m(tn,ti){var ta=g(tn,ti);return P(tn)?-ta:ta}function v(tn,ti,ta,to){var ts,tc=0;for(e(null!=tn),e(null!=ti),e(4294967288>to),tn.Sb=to,tn.Ra=0,tn.u=0,tn.h=0,4<to&&(to=4),ts=0;ts<to;++ts)tc+=ti[ta+ts]<<8*ts;tn.Ra=tc,tn.bb=to,tn.oa=ti,tn.pa=ta}function b(tn){for(;8<=tn.u&&tn.bb<tn.Sb;)tn.Ra>>>=8,tn.Ra+=tn.oa[tn.pa+tn.bb]<<ts-8>>>0,++tn.bb,tn.u-=8;A(tn)&&(tn.h=1,tn.u=0)}function y(tn,ti){if(e(0<=ti),!tn.h&&ti<=to){var ts=L(tn)&ta[ti];return tn.u+=ti,b(tn),ts}return tn.h=1,tn.u=0}function w(){this.b=this.Ca=this.I=0,this.oa=[],this.pa=0,this.Jd=[],this.Yc=0,this.Zc=[],this.Ka=0}function N(){this.Ra=0,this.oa=[],this.h=this.u=this.bb=this.Sb=this.pa=0}function L(tn){return tn.Ra>>>(tn.u&ts-1)>>>0}function A(tn){return e(tn.bb<=tn.Sb),tn.h||tn.bb==tn.Sb&&tn.u>ts}function x(tn,ti){tn.u=ti,tn.h=A(tn)}function S(tn){tn.u>=tc&&(e(tn.u>=tc),b(tn))}function _(tn){e(null!=tn&&null!=tn.oa),tn.pa<tn.Zc?(tn.I=(tn.oa[tn.pa++]|tn.I<<8)>>>0,tn.b+=8):(e(null!=tn&&null!=tn.oa),tn.pa<tn.Yc?(tn.b+=8,tn.I=tn.oa[tn.pa++]|tn.I<<8):tn.Ka?tn.b=0:(tn.I<<=8,tn.b+=8,tn.Ka=1))}function P(tn){return g(tn,1)}function k(tn,ti){var ta=tn.Ca;0>tn.b&&_(tn);var to=tn.b,ts=ta*ti>>>8,tc=(tn.I>>>to>ts)+0;for(tc?(ta-=ts,tn.I-=ts+1<<to>>>0):ta=ts+1,to=ta,ts=0;256<=to;)ts+=8,to>>=8;return to=7^ts+tu[to],tn.b-=to,tn.Ca=(ta<<to)-1,tc}function I(tn,ti,ta){tn[ti+0]=ta>>24&255,tn[ti+1]=ta>>16&255,tn[ti+2]=ta>>8&255,tn[ti+3]=ta>>0&255}function F(tn,ti){return tn[ti+0]<<0|tn[ti+1]<<8}function C(tn,ti){return F(tn,ti)|tn[ti+2]<<16}function j(tn,ti){return F(tn,ti)|F(tn,ti+2)<<16}function O(tn,ti){var ta=1<<ti;return e(null!=tn),e(0<ti),tn.X=a(ta),null==tn.X?0:(tn.Mb=32-ti,tn.Xa=ti,1)}function B(tn,ti){e(null!=tn),e(null!=ti),e(tn.Xa==ti.Xa),n(ti.X,0,tn.X,0,1<<ti.Xa)}function M(){this.X=[],this.Xa=this.Mb=0}function E(tn,ti,ta,to){e(null!=ta),e(null!=to);var ts=ta[0],tc=to[0];return 0==ts&&(ts=(tn*tc+ti/2)/ti),0==tc&&(tc=(ti*ts+tn/2)/tn),0>=ts||0>=tc?0:(ta[0]=ts,to[0]=tc,1)}function q(tn,ti){return tn+(1<<ti)-1>>>ti}function D(tn,ti){return((4278255360&tn)+(4278255360&ti)>>>0&4278255360)+((16711935&tn)+(16711935&ti)>>>0&16711935)>>>0}function R(ti,ta){tn[ta]=function(ta,to,ts,tc,tu,th,tl){var tf;for(tf=0;tf<tu;++tf){var td=tn[ti](th[tl+tf-1],ts,tc+tf);th[tl+tf]=D(ta[to+tf],td)}}}function T(){this.ud=this.hd=this.jd=0}function U(tn,ti){return((4278124286&(tn^ti))>>>1)+(tn&ti)>>>0}function z(tn){return 0<=tn&&256>tn?tn:0>tn?0:255<tn?255:void 0}function H(tn,ti){return z(tn+(tn-ti+.5>>1))}function W(tn,ti,ta){return Math.abs(ti-ta)-Math.abs(tn-ta)}function V(tn,ti,ta,to,ts,tc,tu){for(to=tc[tu-1],ta=0;ta<ts;++ta)tc[tu+ta]=to=D(tn[ti+ta],to)}function G(tn,ti,ta,to,ts){var tc;for(tc=0;tc<ta;++tc){var tu=tn[ti+tc],th=tu>>8&255,tl=16711935&(tl=(tl=16711935&tu)+((th<<16)+th));to[ts+tc]=(4278255360&tu)+tl>>>0}}function Y(tn,ti){ti.jd=tn>>0&255,ti.hd=tn>>8&255,ti.ud=tn>>16&255}function J(tn,ti,ta,to,ts,tc){var tu;for(tu=0;tu<to;++tu){var th=ti[ta+tu],tl=th>>>8,tf=th,td=255&(td=(td=th>>>16)+((tn.jd<<24>>24)*(tl<<24>>24)>>>5));tf=255&(tf=(tf+=(tn.hd<<24>>24)*(tl<<24>>24)>>>5)+((tn.ud<<24>>24)*(td<<24>>24)>>>5)),ts[tc+tu]=(4278255360&th)+(td<<16)+tf}}function X(ti,ta,to,ts,tc){tn[ta]=function(tn,ti,ta,to,tu,th,tl,tf,td){for(to=tl;to<tf;++to)for(tl=0;tl<td;++tl)tu[th++]=tc(ta[ts(tn[ti++])])},tn[ti]=function(ti,ta,tu,th,tl,tf,td){var tp=8>>ti.b,tg=ti.Ea,tm=ti.K[0],tv=ti.w;if(8>tp)for(ti=(1<<ti.b)-1,tv=(1<<tp)-1;ta<tu;++ta){var tb,ty=0;for(tb=0;tb<tg;++tb)tb&ti||(ty=ts(th[tl++])),tf[td++]=tc(tm[ty&tv]),ty>>=tp}else tn["VP8LMapColor"+to](th,tl,tm,tv,tf,td,ta,tu,tg)}}function K(tn,ti,ta,to,ts){for(ta=ti+ta;ti<ta;){var tc=tn[ti++];to[ts++]=tc>>16&255,to[ts++]=tc>>8&255,to[ts++]=tc>>0&255}}function Z(tn,ti,ta,to,ts){for(ta=ti+ta;ti<ta;){var tc=tn[ti++];to[ts++]=tc>>16&255,to[ts++]=tc>>8&255,to[ts++]=tc>>0&255,to[ts++]=tc>>24&255}}function $(tn,ti,ta,to,ts){for(ta=ti+ta;ti<ta;){var tc=(tu=tn[ti++])>>16&240|tu>>12&15,tu=tu>>0&240|tu>>28&15;to[ts++]=tc,to[ts++]=tu}}function Q(tn,ti,ta,to,ts){for(ta=ti+ta;ti<ta;){var tc=(tu=tn[ti++])>>16&248|tu>>13&7,tu=tu>>5&224|tu>>3&31;to[ts++]=tc,to[ts++]=tu}}function tt(tn,ti,ta,to,ts){for(ta=ti+ta;ti<ta;){var tc=tn[ti++];to[ts++]=tc>>0&255,to[ts++]=tc>>8&255,to[ts++]=tc>>16&255}}function et(tn,ti,ta,to,ts,tc){if(0==tc)for(ta=ti+ta;ti<ta;)I(to,((tc=tn[ti++])[0]>>24|tc[1]>>8&65280|tc[2]<<8&16711680|tc[3]<<24)>>>0),ts+=32;else n(to,ts,tn,ti,ta)}function rt(ti,ta){tn[ta][0]=tn[ti+"0"],tn[ta][1]=tn[ti+"1"],tn[ta][2]=tn[ti+"2"],tn[ta][3]=tn[ti+"3"],tn[ta][4]=tn[ti+"4"],tn[ta][5]=tn[ti+"5"],tn[ta][6]=tn[ti+"6"],tn[ta][7]=tn[ti+"7"],tn[ta][8]=tn[ti+"8"],tn[ta][9]=tn[ti+"9"],tn[ta][10]=tn[ti+"10"],tn[ta][11]=tn[ti+"11"],tn[ta][12]=tn[ti+"12"],tn[ta][13]=tn[ti+"13"],tn[ta][14]=tn[ti+"0"],tn[ta][15]=tn[ti+"0"]}function nt(tn){return tn==t7||tn==t9||tn==en||tn==ei}function it(){this.eb=[],this.size=this.A=this.fb=0}function at(){this.y=[],this.f=[],this.ea=[],this.F=[],this.Tc=this.Ed=this.Cd=this.Fd=this.lb=this.Db=this.Ab=this.fa=this.J=this.W=this.N=this.O=0}function ot(){this.Rd=this.height=this.width=this.S=0,this.f={},this.f.RGBA=new it,this.f.kb=new at,this.sd=null}function st(){this.width=[0],this.height=[0],this.Pd=[0],this.Qd=[0],this.format=[0]}function ct(){this.Id=this.fd=this.Md=this.hb=this.ib=this.da=this.bd=this.cd=this.j=this.v=this.Da=this.Sd=this.ob=0}function ut(tn){return alert("todo:WebPSamplerProcessPlane"),tn.T}function ht(tn,ti){var ta=tn.T,to=ti.ba.f.RGBA,ts=to.eb,tc=to.fb+tn.ka*to.A,tu=eF[ti.ba.S],th=tn.y,tl=tn.O,tf=tn.f,td=tn.N,tp=tn.ea,tg=tn.W,tm=ti.cc,tv=ti.dc,tb=ti.Mc,ty=ti.Nc,tw=tn.ka,tN=tn.ka+tn.T,tL=tn.U,tx=tL+1>>1;for(0==tw?tu(th,tl,null,null,tf,td,tp,tg,tf,td,tp,tg,ts,tc,null,null,tL):(tu(ti.ec,ti.fc,th,tl,tm,tv,tb,ty,tf,td,tp,tg,ts,tc-to.A,ts,tc,tL),++ta);tw+2<tN;tw+=2)tm=tf,tv=td,tb=tp,ty=tg,td+=tn.Rc,tg+=tn.Rc,tc+=2*to.A,tu(th,(tl+=2*tn.fa)-tn.fa,th,tl,tm,tv,tb,ty,tf,td,tp,tg,ts,tc-to.A,ts,tc,tL);return tl+=tn.fa,tn.j+tN<tn.o?(n(ti.ec,ti.fc,th,tl,tL),n(ti.cc,ti.dc,tf,td,tx),n(ti.Mc,ti.Nc,tp,tg,tx),ta--):1&tN||tu(th,tl,null,null,tf,td,tp,tg,tf,td,tp,tg,ts,tc+to.A,null,null,tL),ta}function lt(tn,ti,ta){var to=tn.F,ts=[tn.J];if(null!=to){var tc=tn.U,tu=ti.ba.S,th=tu==t4||tu==en;ti=ti.ba.f.RGBA;var tl=[0],tf=tn.ka;tl[0]=tn.T,tn.Kb&&(0==tf?--tl[0]:(--tf,ts[0]-=tn.width),tn.j+tn.ka+tn.T==tn.o&&(tl[0]=tn.o-tn.j-tf));var td=ti.eb;tf=ti.fb+tf*ti.A,tn=tH(to,ts[0],tn.width,tc,tl,td,tf+(th?0:3),ti.A),e(ta==tl),tn&&nt(tu)&&tz(td,tf,th,tc,tl,ti.A)}return 0}function ft(tn){var ti=tn.ma,ta=ti.ba.S,to=11>ta,ts=ta==t5||ta==t3||ta==t4||ta==t6||12==ta||nt(ta);if(ti.memory=null,ti.Ib=null,ti.Jb=null,ti.Nd=null,!Mr(ti.Oa,tn,ts?11:12))return 0;if(ts&&nt(ta)&&br(),tn.da)alert("todo:use_scaling");else{if(to){if(ti.Ib=ut,tn.Kb){if(ta=tn.U+1>>1,ti.memory=a(tn.U+2*ta),null==ti.memory)return 0;ti.ec=ti.memory,ti.fc=0,ti.cc=ti.ec,ti.dc=ti.fc+tn.U,ti.Mc=ti.cc,ti.Nc=ti.dc+ta,ti.Ib=ht,br()}}else alert("todo:EmitYUV");ts&&(ti.Jb=lt,to&&mr())}if(to&&!eV){for(tn=0;256>tn;++tn)eH[tn]=89858*(tn-128)+eD>>eq,eJ[tn]=-22014*(tn-128)+eD,eG[tn]=-45773*(tn-128),eW[tn]=113618*(tn-128)+eD>>eq;for(tn=eR;tn<eT;++tn)ti=76283*(tn-16)+eD>>eq,eY[tn-eR]=Vt(ti,255),eX[tn-eR]=Vt(ti+8>>4,15);eV=1}return 1}function dt(tn){var ti=tn.ma,ta=tn.U,to=tn.T;return e(!(1&tn.ka)),0>=ta||0>=to?0:(ta=ti.Ib(tn,ti),null!=ti.Jb&&ti.Jb(tn,ti,ta),ti.Dc+=ta,1)}function pt(tn){tn.ma.memory=null}function gt(tn,ti,ta,to){return 47!=y(tn,8)?0:(ti[0]=y(tn,14)+1,ta[0]=y(tn,14)+1,to[0]=y(tn,1),0!=y(tn,3)?0:!tn.h)}function mt(tn,ti){if(4>tn)return tn+1;var ta=tn-2>>1;return(2+(1&tn)<<ta)+y(ti,ta)+1}function vt(tn,ti){var ta;return 120<ti?ti-120:1<=(ta=((ta=eh[ti-1])>>4)*tn+(8-(15&ta)))?ta:1}function bt(tn,ti,ta){var to=L(ta),ts=tn[ti+=255&to].g-8;return 0<ts&&(x(ta,ta.u+8),to=L(ta),ti+=tn[ti].value,ti+=to&(1<<ts)-1),x(ta,ta.u+tn[ti].g),tn[ti].value}function yt(tn,ti,ta){return ta.g+=tn.g,ta.value+=tn.value<<ti>>>0,e(8>=ta.g),tn.g}function wt(tn,ti,ta){var to=tn.xc;return e((ti=0==to?0:tn.vc[tn.md*(ta>>to)+(ti>>to)])<tn.Wb),tn.Ya[ti]}function Nt(tn,ti,ta,to){var ts=tn.ab,tc=tn.c*ti,tu=tn.C;ti=tu+ti;var tp=ta,tg=to;for(to=tn.Ta,ta=tn.Ua;0<ts--;){var tm=tn.gc[ts],ty=tu,tw=ti,tN=tp,tL=tg,tx=(tg=to,tp=ta,tm.Ea);switch(e(ty<tw),e(tw<=tm.nc),tm.hc){case 2:tf(tN,tL,(tw-ty)*tx,tg,tp);break;case 0:var tA=ty,tS=tw,t_=tg,tP=tp,tk=(tO=tm).Ea;0==tA&&(th(tN,tL,null,null,1,t_,tP),V(tN,tL+1,0,0,tk-1,t_,tP+1),tL+=tk,tP+=tk,++tA);for(var tF=1<<tO.b,tI=tF-1,tC=q(tk,tO.b),tj=tO.K,tO=tO.w+(tA>>tO.b)*tC;tA<tS;){var tE=tj,tB=tO,tM=1;for(tl(tN,tL,t_,tP-tk,1,t_,tP);tM<tk;){var tq=(tM&~tI)+tF;tq>tk&&(tq=tk),(0,tv[tE[tB++]>>8&15])(tN,tL+ +tM,t_,tP+tM-tk,tq-tM,t_,tP+tM),tM=tq}tL+=tk,tP+=tk,++tA&tI||(tO+=tC)}tw!=tm.nc&&n(tg,tp-tx,tg,tp+(tw-ty-1)*tx,tx);break;case 1:for(tx=tN,tS=tL,tk=(tN=tm.Ea)-(tP=tN&~(t_=(tL=1<<tm.b)-1)),tA=q(tN,tm.b),tF=tm.K,tm=tm.w+(ty>>tm.b)*tA;ty<tw;){for(tI=tF,tC=tm,tj=new T,tO=tS+tP,tE=tS+tN;tS<tO;)Y(tI[tC++],tj),tb(tj,tx,tS,tL,tg,tp),tS+=tL,tp+=tL;tS<tE&&(Y(tI[tC++],tj),tb(tj,tx,tS,tk,tg,tp),tS+=tk,tp+=tk),++ty&t_||(tm+=tA)}break;case 3:if(tN==tg&&tL==tp&&0<tm.b){for(tS=tg,tN=tx=tp+(tw-ty)*tx-(tP=(tw-ty)*q(tm.Ea,tm.b)),tL=tg,t_=tp,tA=[],tP=(tk=tP)-1;0<=tP;--tP)tA[tP]=tL[t_+tP];for(tP=tk-1;0<=tP;--tP)tS[tN+tP]=tA[tP];td(tm,ty,tw,tg,tx,tg,tp)}else td(tm,ty,tw,tN,tL,tg,tp)}tp=to,tg=ta}tg!=ta&&n(to,ta,tp,tg,tc)}function Lt(tn,ti){var ta=tn.V,to=tn.Ba+tn.c*tn.C,ts=ti-tn.C;if(e(ti<=tn.l.o),e(16>=ts),0<ts){var tc=tn.l,tu=tn.Ta,th=tn.Ua,tl=tc.width;if(Nt(tn,ts,ta,to),ts=th=[th],e((ta=tn.C)<(to=ti)),e(tc.v<tc.va),to>tc.o&&(to=tc.o),ta<tc.j){var tf=tc.j-ta;ta=tc.j,ts[0]+=tf*tl}if(ta>=to?ta=0:(ts[0]+=4*tc.v,tc.ka=ta-tc.j,tc.U=tc.va-tc.v,tc.T=to-ta,ta=1),ta){if(th=th[0],11>(ta=tn.ca).S){var td=ta.f.RGBA,tp=(to=ta.S,ts=tc.U,tc=tc.T,tf=td.eb,td.A),tg=tc;for(td=td.fb+tn.Ma*td.A;0<tg--;){var tm=th,tv=ts,tb=tf,tA=td;switch(to){case t2:ty(tu,tm,tv,tb,tA);break;case t5:tw(tu,tm,tv,tb,tA);break;case t7:tw(tu,tm,tv,tb,tA),tz(tb,tA,0,tv,1,0);break;case t0:tx(tu,tm,tv,tb,tA);break;case t3:et(tu,tm,tv,tb,tA,1);break;case t9:et(tu,tm,tv,tb,tA,1),tz(tb,tA,0,tv,1,0);break;case t4:et(tu,tm,tv,tb,tA,0);break;case en:et(tu,tm,tv,tb,tA,0),tz(tb,tA,1,tv,1,0);break;case t6:tN(tu,tm,tv,tb,tA);break;case ei:tN(tu,tm,tv,tb,tA),tV(tb,tA,tv,1,0);break;case t8:tL(tu,tm,tv,tb,tA);break;default:e(0)}th+=tl,td+=tp}tn.Ma+=tc}else alert("todo:EmitRescaledRowsYUVA");e(tn.Ma<=ta.height)}}tn.C=ti,e(tn.C<=tn.i)}function At(tn){var ti;if(0<tn.ua)return 0;for(ti=0;ti<tn.Wb;++ti){var ta=tn.Ya[ti].G,to=tn.Ya[ti].H;if(0<ta[1][to[1]+0].g||0<ta[2][to[2]+0].g||0<ta[3][to[3]+0].g)return 0}return 1}function xt(tn,ti,ta,to,ts,tc){if(0!=tn.Z){var tu=tn.qd,th=tn.rd;for(e(null!=ek[tn.Z]);ti<ta;++ti)ek[tn.Z](tu,th,to,ts,to,ts,tc),tu=to,th=ts,ts+=tc;tn.qd=tu,tn.rd=th}}function St(tn,ti){var ta=tn.l.ma,to=0==ta.Z||1==ta.Z?tn.l.j:tn.C;if(to=tn.C<to?to:tn.C,e(ti<=tn.l.o),ti>to){var ts=tn.l.width,tc=ta.ca,tu=ta.tb+ts*to,th=tn.V,tl=tn.Ba+tn.c*to,tf=tn.gc;e(1==tn.ab),e(3==tf[0].hc),tg(tf[0],to,ti,th,tl,tc,tu),xt(ta,to,ti,tc,tu,ts)}tn.C=tn.Ma=ti}function _t(tn,ta,to,ts,tc,tu,th){var tl=tn.$/ts,tf=tn.$%ts,td=tn.m,tp=tn.s,tg=to+tn.$,tm=tg;tc=to+ts*tc;var tv=to+ts*tu,tb=280+tp.ua,ty=tn.Pb?tl:16777216,tw=0<tp.ua?tp.Wa:null,tN=tp.wc,tL=tg<tv?wt(tp,tf,tl):null;e(tn.C<tu),e(tv<=tc);var tx=!1;t:for(;;){for(;tx||tg<tv;){var tA=0;if(tl>=ty){var tS=tg-to;e((ty=tn).Pb),ty.wd=ty.m,ty.xd=tS,0<ty.s.ua&&B(ty.s.Wa,ty.s.vb),ty=tl+ef}if(tf&tN||(tL=wt(tp,tf,tl)),e(null!=tL),tL.Qb&&(ta[tg]=tL.qb,tx=!0),!tx){if(S(td),tL.jc){tA=td,tS=ta;var t_=tg,tP=tL.pd[L(tA)&ti-1];e(tL.jc),256>tP.g?(x(tA,tA.u+tP.g),tS[t_]=tP.value,tA=0):(x(tA,tA.u+tP.g-256),e(256<=tP.value),tA=tP.value),0==tA&&(tx=!0)}else tA=bt(tL.G[0],tL.H[0],td)}if(td.h)break;if(tx||256>tA){if(!tx){if(tL.nd)ta[tg]=(tL.qb|tA<<8)>>>0;else{if(S(td),tx=bt(tL.G[1],tL.H[1],td),S(td),tS=bt(tL.G[2],tL.H[2],td),t_=bt(tL.G[3],tL.H[3],td),td.h)break;ta[tg]=(t_<<24|tx<<16|tA<<8|tS)>>>0}}if(tx=!1,++tg,++tf>=ts&&(tf=0,++tl,null!=th&&tl<=tu&&!(tl%16)&&th(tn,tl),null!=tw))for(;tm<tg;)tA=ta[tm++],tw.X[(506832829*tA&**********)>>>tw.Mb]=tA}else if(280>tA){if(tA=mt(tA-256,td),tS=bt(tL.G[4],tL.H[4],td),S(td),tS=vt(ts,tS=mt(tS,td)),td.h)break;if(tg-to<tS||tc-tg<tA)break t;for(t_=0;t_<tA;++t_)ta[tg+t_]=ta[tg+t_-tS];for(tg+=tA,tf+=tA;tf>=ts;)tf-=ts,++tl,null!=th&&tl<=tu&&!(tl%16)&&th(tn,tl);if(e(tg<=tc),tf&tN&&(tL=wt(tp,tf,tl)),null!=tw)for(;tm<tg;)tA=ta[tm++],tw.X[(506832829*tA&**********)>>>tw.Mb]=tA}else{if(!(tA<tb))break t;for(tx=tA-280,e(null!=tw);tm<tg;)tA=ta[tm++],tw.X[(506832829*tA&**********)>>>tw.Mb]=tA;tA=tg,e(!(tx>>>(tS=tw).Xa)),ta[tA]=tS.X[tx],tx=!0}tx||e(td.h==A(td))}if(tn.Pb&&td.h&&tg<tc)e(tn.m.h),tn.a=5,tn.m=tn.wd,tn.$=tn.xd,0<tn.s.ua&&B(tn.s.vb,tn.s.Wa);else{if(td.h)break;null!=th&&th(tn,tl>tu?tu:tl),tn.a=0,tn.$=tg-to}return 1}return tn.a=3,0}function Pt(tn){e(null!=tn),tn.vc=null,tn.yc=null,tn.Ya=null;var ti=tn.Wa;null!=ti&&(ti.X=null),tn.vb=null,e(null!=tn)}function kt(){var ti=new or;return null==ti?null:(ti.a=0,ti.xb=eP,rt("Predictor","VP8LPredictors"),rt("Predictor","VP8LPredictors_C"),rt("PredictorAdd","VP8LPredictorsAdd"),rt("PredictorAdd","VP8LPredictorsAdd_C"),tf=G,tb=J,ty=K,tw=Z,tN=$,tL=Q,tx=tt,tn.VP8LMapColor32b=tp,tn.VP8LMapColor8b=tm,ti)}function It(tn,ta,to,ts,tc){for(var tu=1,th=[tn],tl=[ta],tf=ts.m,td=ts.s,tp=null,tg=0;;){if(to)for(;tu&&y(tf,1);){var tm=th,tv=tl,tb=1,ty=ts.m,tw=ts.gc[ts.ab],tN=y(ty,2);if(ts.Oc&1<<tN)tu=0;else{switch(ts.Oc|=1<<tN,tw.hc=tN,tw.Ea=tm[0],tw.nc=tv[0],tw.K=[null],++ts.ab,e(4>=ts.ab),tN){case 0:case 1:tw.b=y(ty,3)+2,tb=It(q(tw.Ea,tw.b),q(tw.nc,tw.b),0,ts,tw.K),tw.K=tw.K[0];break;case 3:var tL,tx=y(ty,8)+1,tA=16<tx?0:4<tx?1:2<tx?2:3;if(tm[0]=q(tw.Ea,tA),tw.b=tA,tL=tb=It(tx,1,0,ts,tw.K)){var tS,t_=1<<(8>>tw.b),tP=a(t_);if(null==tP)tL=0;else{var tk=tw.K[0],tF=tw.w;for(tP[0]=tw.K[0][0],tS=1;tS<1*tx;++tS)tP[tS]=D(tk[tF+tS],tP[tS-1]);for(;tS<4*t_;++tS)tP[tS]=0;tw.K[0]=null,tw.K[0]=tP,tL=1}}tb=tL;break;case 2:break;default:e(0)}tu=tb}}if(th=th[0],tl=tl[0],tu&&y(tf,1)&&!(tu=1<=(tg=y(tf,4))&&11>=tg)){ts.a=3;break}if(tI=tu)e:{var tI,tC,tj,tO,tE=th,tB=tl,tM=tg,tq=ts.m,tD=ts.s,tR=[null],tT=1,tU=0,tz=el[tM];r:for(;;){if(to&&y(tq,1)){var tV=y(tq,3)+2,tH=q(tE,tV),tW=q(tB,tV),tG=tH*tW;if(!It(tH,tW,0,ts,tR))break;for(tR=tR[0],tD.xc=tV,tC=0;tC<tG;++tC){var tJ=tR[tC]>>8&65535;tR[tC]=tJ,tJ>=tT&&(tT=tJ+1)}}if(tq.h)break;for(tj=0;5>tj;++tj){var tY=es[tj];!tj&&0<tM&&(tY+=1<<tM),tU<tY&&(tU=tY)}var tX=o(tT*tz,l),tZ=tT,tK=o(tZ,d);if(null==tK)var t$=null;else e(65536>=tZ),t$=tK;var tQ=a(tU);if(null==t$||null==tQ||null==tX){ts.a=1;break}for(tC=tO=0;tC<tT;++tC){var t1,t2=t$[tC],t5=t2.G,t0=t2.H,t3=0,t4=1,t6=0;for(tj=0;5>tj;++tj){tY=es[tj],t5[tj]=tX,t0[tj]=tO,!tj&&0<tM&&(tY+=1<<tM);n:{var t8,t7=tY,t9=tO,en=0,ei=ts.m,eh=y(ei,1);if(i(tQ,0,0,t7),eh){var ef=y(ei,1)+1,ed=y(ei,1),ep=y(ei,0==ed?1:8);tQ[ep]=1,2==ef&&(tQ[ep=y(ei,8)]=1);var eg=1}else{var em=a(19),ev=y(ei,4)+4;if(19<ev){ts.a=3;var eb=0;break n}for(t8=0;t8<ev;++t8)em[eu[t8]]=y(ei,3);var ey=void 0,ew=void 0,eN=0,eL=ts.m,ex=8,eA=o(128,l);i:for(;h(eA,0,7,em,19);){if(y(eL,1)){var eS=2+2*y(eL,3);if((ey=2+y(eL,eS))>t7)break}else ey=t7;for(ew=0;ew<t7&&ey--;){S(eL);var eP=eA[0+(127&L(eL))];x(eL,eL.u+eP.g);var ek=eP.value;if(16>ek)tQ[ew++]=ek,0!=ek&&(ex=ek);else{var eF=16==ek,eI=ek-16,eC=eo[eI],ej=y(eL,ea[eI])+eC;if(ew+ej>t7)break i;for(var eO=eF?ex:0;0<ej--;)tQ[ew++]=eO}}eN=1;break}eN||(ts.a=3),eg=eN}(eg=eg&&!ei.h)&&(en=h(tX,t9,8,tQ,t7)),eg&&0!=en?eb=en:(ts.a=3,eb=0)}if(0==eb)break r;if(t4&&1==ec[tj]&&(t4=0==tX[tO].g),t3+=tX[tO].g,tO+=eb,3>=tj){var eE,eB=tQ[0];for(eE=1;eE<tY;++eE)tQ[eE]>eB&&(eB=tQ[eE]);t6+=eB}}if(t2.nd=t4,t2.Qb=0,t4&&(t2.qb=(t5[3][t0[3]+0].value<<24|t5[1][t0[1]+0].value<<16|t5[2][t0[2]+0].value)>>>0,0==t3&&256>t5[0][t0[0]+0].value&&(t2.Qb=1,t2.qb+=t5[0][t0[0]+0].value<<8)),t2.jc=!t2.Qb&&6>t6,t2.jc)for(t1=0;t1<ti;++t1){var eM=t1,eq=t2.pd[eM],eD=t2.G[0][t2.H[0]+eM];256<=eD.value?(eq.g=eD.g+256,eq.value=eD.value):(eq.g=0,eq.value=0,eM>>=yt(eD,8,eq),eM>>=yt(t2.G[1][t2.H[1]+eM],16,eq),eM>>=yt(t2.G[2][t2.H[2]+eM],0,eq),yt(t2.G[3][t2.H[3]+eM],24,eq))}}tD.vc=tR,tD.Wb=tT,tD.Ya=t$,tD.yc=tX,tI=1;break e}tI=0}if(!(tu=tI)){ts.a=3;break}if(0<tg){if(td.ua=1<<tg,!O(td.Wa,tg)){ts.a=1,tu=0;break}}else td.ua=0;var eR=th,eT=tl,eU=ts.s,ez=eU.xc;if(ts.c=eR,ts.i=eT,eU.md=q(eR,ez),eU.wc=0==ez?-1:(1<<ez)-1,to){ts.xb=e_;break}if(null==(tp=a(th*tl))){ts.a=1,tu=0;break}tu=(tu=_t(ts,tp,0,th,tl,tl,null))&&!tf.h;break}return tu?(null!=tc?tc[0]=tp:(e(null==tp),e(to)),ts.$=0,to||Pt(td)):Pt(td),tu}function Ft(tn,ti){var ta=tn.c*tn.i,to=ta+ti+16*ti;return e(tn.c<=ti),tn.V=a(to),null==tn.V?(tn.Ta=null,tn.Ua=0,tn.a=1,0):(tn.Ta=tn.V,tn.Ua=tn.Ba+ta+ti,1)}function Ct(tn,ti){var ta=tn.C,to=ti-ta,ts=tn.V,tc=tn.Ba+tn.c*ta;for(e(ti<=tn.l.o);0<to;){var tu=16<to?16:to,th=tn.l.ma,tl=tn.l.width,tf=tl*tu,td=th.ca,tp=th.tb+tl*ta,tg=tn.Ta,tm=tn.Ua;Nt(tn,tu,ts,tc),tW(tg,tm,td,tp,tf),xt(th,ta,ta+tu,td,tp,tl),to-=tu,ts+=tu*tn.c,ta+=tu}e(ta==ti),tn.C=tn.Ma=ti}function jt(){this.ub=this.yd=this.td=this.Rb=0}function Ot(){this.Kd=this.Ld=this.Ud=this.Td=this.i=this.c=0}function Bt(){this.Fb=this.Bb=this.Cb=0,this.Zb=a(4),this.Lb=a(4)}function Mt(){var tn;this.Yb=(function t(tn,ti,ta){for(var to=ta[ti],ts=0;ts<to&&(tn.push(ta.length>ti+1?[]:0),!(ta.length<ti+1));ts++)t(tn[ts],ti+1,ta)}(tn=[],0,[3,11]),tn)}function Et(){this.jb=a(3),this.Wc=s([4,8],Mt),this.Xc=s([4,17],Mt)}function qt(){this.Pc=this.wb=this.Tb=this.zd=0,this.vd=new a(4),this.od=new a(4)}function Dt(){this.ld=this.La=this.dd=this.tc=0}function Rt(){this.Na=this.la=0}function Tt(){this.Sc=[0,0],this.Eb=[0,0],this.Qc=[0,0],this.ia=this.lc=0}function Ut(){this.ad=a(384),this.Za=0,this.Ob=a(16),this.$b=this.Ad=this.ia=this.Gc=this.Hc=this.Dd=0}function zt(){this.uc=this.M=this.Nb=0,this.wa=Array(new Dt),this.Y=0,this.ya=Array(new Ut),this.aa=0,this.l=new Gt}function Ht(){this.y=a(16),this.f=a(8),this.ea=a(8)}function Wt(){this.cb=this.a=0,this.sc="",this.m=new w,this.Od=new jt,this.Kc=new Ot,this.ed=new qt,this.Qa=new Bt,this.Ic=this.$c=this.Aa=0,this.D=new zt,this.Xb=this.Va=this.Hb=this.zb=this.yb=this.Ub=this.za=0,this.Jc=o(8,w),this.ia=0,this.pb=o(4,Tt),this.Pa=new Et,this.Bd=this.kc=0,this.Ac=[],this.Bc=0,this.zc=[0,0,0,0],this.Gd=Array(new Ht),this.Hd=0,this.rb=Array(new Rt),this.sb=0,this.wa=Array(new Dt),this.Y=0,this.oc=[],this.pc=0,this.sa=[],this.ta=0,this.qa=[],this.ra=0,this.Ha=[],this.B=this.R=this.Ia=0,this.Ec=[],this.M=this.ja=this.Vb=this.Fc=0,this.ya=Array(new Ut),this.L=this.aa=0,this.gd=s([4,2],Dt),this.ga=null,this.Fa=[],this.Cc=this.qc=this.P=0,this.Gb=[],this.Uc=0,this.mb=[],this.nb=0,this.rc=[],this.Ga=this.Vc=0}function Vt(tn,ti){return 0>tn?0:tn>ti?ti:tn}function Gt(){this.T=this.U=this.ka=this.height=this.width=0,this.y=[],this.f=[],this.ea=[],this.Rc=this.fa=this.W=this.N=this.O=0,this.ma="void",this.put="VP8IoPutHook",this.ac="VP8IoSetupHook",this.bc="VP8IoTeardownHook",this.ha=this.Kb=0,this.data=[],this.hb=this.ib=this.da=this.o=this.j=this.va=this.v=this.Da=this.ob=this.w=0,this.F=[],this.J=0}function Yt(){var tn=new Wt;return null!=tn&&(tn.a=0,tn.sc="OK",tn.cb=0,tn.Xb=0,eg||(eg=Zt)),tn}function Jt(tn,ti,ta){return 0==tn.a&&(tn.a=ti,tn.sc=ta,tn.cb=0),0}function Xt(tn,ti,ta){return 3<=ta&&157==tn[ti+0]&&1==tn[ti+1]&&42==tn[ti+2]}function Kt(tn,ti){if(null==tn)return 0;if(tn.a=0,tn.sc="OK",null==ti)return Jt(tn,2,"null VP8Io passed to VP8GetHeaders()");var ta=ti.data,to=ti.w,ts=ti.ha;if(4>ts)return Jt(tn,7,"Truncated header.");var tc=ta[to+0]|ta[to+1]<<8|ta[to+2]<<16,tu=tn.Od;if(tu.Rb=!(1&tc),tu.td=tc>>1&7,tu.yd=tc>>4&1,tu.ub=tc>>5,3<tu.td)return Jt(tn,3,"Incorrect keyframe parameters.");if(!tu.yd)return Jt(tn,4,"Frame not displayable.");to+=3,ts-=3;var th=tn.Kc;if(tu.Rb){if(7>ts)return Jt(tn,7,"cannot parse picture header");if(!Xt(ta,to,ts))return Jt(tn,3,"Bad code word");th.c=16383&(ta[to+4]<<8|ta[to+3]),th.Td=ta[to+4]>>6,th.i=16383&(ta[to+6]<<8|ta[to+5]),th.Ud=ta[to+6]>>6,to+=7,ts-=7,tn.za=th.c+15>>4,tn.Ub=th.i+15>>4,ti.width=th.c,ti.height=th.i,ti.Da=0,ti.j=0,ti.v=0,ti.va=ti.width,ti.o=ti.height,ti.da=0,ti.ib=ti.width,ti.hb=ti.height,ti.U=ti.width,ti.T=ti.height,i((tc=tn.Pa).jb,0,255,tc.jb.length),e(null!=(tc=tn.Qa)),tc.Cb=0,tc.Bb=0,tc.Fb=1,i(tc.Zb,0,0,tc.Zb.length),i(tc.Lb,0,0,tc.Lb)}if(tu.ub>ts)return Jt(tn,7,"bad partition length");p(tc=tn.m,ta,to,tu.ub),to+=tu.ub,ts-=tu.ub,tu.Rb&&(th.Ld=P(tc),th.Kd=P(tc)),th=tn.Qa;var tl,tf=tn.Pa;if(e(null!=tc),e(null!=th),th.Cb=P(tc),th.Cb){if(th.Bb=P(tc),P(tc)){for(th.Fb=P(tc),tl=0;4>tl;++tl)th.Zb[tl]=P(tc)?m(tc,7):0;for(tl=0;4>tl;++tl)th.Lb[tl]=P(tc)?m(tc,6):0}if(th.Bb)for(tl=0;3>tl;++tl)tf.jb[tl]=P(tc)?g(tc,8):255}else th.Bb=0;if(tc.Ka)return Jt(tn,3,"cannot parse segment header");if((th=tn.ed).zd=P(tc),th.Tb=g(tc,6),th.wb=g(tc,3),th.Pc=P(tc),th.Pc&&P(tc)){for(tf=0;4>tf;++tf)P(tc)&&(th.vd[tf]=m(tc,6));for(tf=0;4>tf;++tf)P(tc)&&(th.od[tf]=m(tc,6))}if(tn.L=0==th.Tb?0:th.zd?1:2,tc.Ka)return Jt(tn,3,"cannot parse filter header");var td=ts;if(ts=tl=to,to=tl+td,th=td,tn.Xb=(1<<g(tn.m,2))-1,td<3*(tf=tn.Xb))ta=7;else{for(tl+=3*tf,th-=3*tf,td=0;td<tf;++td){var tp=ta[ts+0]|ta[ts+1]<<8|ta[ts+2]<<16;tp>th&&(tp=th),p(tn.Jc[+td],ta,tl,tp),tl+=tp,th-=tp,ts+=3}p(tn.Jc[+tf],ta,tl,th),ta=tl<to?0:5}if(0!=ta)return Jt(tn,ta,"cannot parse partitions");for(ta=g(tl=tn.m,7),ts=P(tl)?m(tl,4):0,to=P(tl)?m(tl,4):0,th=P(tl)?m(tl,4):0,tf=P(tl)?m(tl,4):0,tl=P(tl)?m(tl,4):0,td=tn.Qa,tp=0;4>tp;++tp){if(td.Cb){var tg=td.Zb[tp];td.Fb||(tg+=ta)}else{if(0<tp){tn.pb[tp]=tn.pb[0];continue}tg=ta}var tm=tn.pb[tp];tm.Sc[0]=ed[Vt(tg+ts,127)],tm.Sc[1]=ep[Vt(tg+0,127)],tm.Eb[0]=2*ed[Vt(tg+to,127)],tm.Eb[1]=101581*ep[Vt(tg+th,127)]>>16,8>tm.Eb[1]&&(tm.Eb[1]=8),tm.Qc[0]=ed[Vt(tg+tf,117)],tm.Qc[1]=ep[Vt(tg+tl,127)],tm.lc=tg+tl}if(!tu.Rb)return Jt(tn,4,"Not a key frame.");for(P(tc),tu=tn.Pa,ta=0;4>ta;++ta){for(ts=0;8>ts;++ts)for(to=0;3>to;++to)for(th=0;11>th;++th)tf=k(tc,eN[ta][ts][to][th])?g(tc,8):ey[ta][ts][to][th],tu.Wc[ta][ts].Yb[to][th]=tf;for(ts=0;17>ts;++ts)tu.Xc[ta][ts]=tu.Wc[ta][eL[ts]]}return tn.kc=P(tc),tn.kc&&(tn.Bd=g(tc,8)),tn.cb=1}function Zt(tn,ti,ta,to,ts,tc,tu){var th=ti[ts].Yb[ta];for(ta=0;16>ts;++ts){if(!k(tn,th[ta+0]))return ts;for(;!k(tn,th[ta+1]);)if(th=ti[++ts].Yb[0],ta=0,16==ts)return 16;var tl=ti[ts+1].Yb;if(k(tn,th[ta+2])){var tf=tn,td=0;if(k(tf,(tg=th)[(tp=ta)+3])){if(k(tf,tg[tp+6])){for(th=0,tp=2*(td=k(tf,tg[tp+8]))+(tg=k(tf,tg[tp+9+td])),td=0,tg=em[tp];tg[th];++th)td+=td+k(tf,tg[th]);td+=3+(8<<tp)}else td=k(tf,tg[tp+7])?7+2*k(tf,165)+k(tf,145):5+k(tf,159)}else td=k(tf,tg[tp+4])?3+k(tf,tg[tp+5]):2;th=tl[2]}else td=1,th=tl[1];tl=tu+ev[ts],0>(tf=tn).b&&_(tf);var tp,tg=tf.b,tm=(tp=tf.Ca>>1)-(tf.I>>tg)>>31;--tf.b,tf.Ca+=tm,tf.Ca|=1,tf.I-=(tp+1&tm)<<tg,tc[tl]=((td^tm)-tm)*to[(0<ts)+0]}return 16}function $t(tn){var ti=tn.rb[tn.sb-1];ti.la=0,ti.Na=0,i(tn.zc,0,0,tn.zc.length),tn.ja=0}function te(tn,ti,ta,to,ts){ts=tn[ti+ta+32*to]+(ts>>3),tn[ti+ta+32*to]=-256&ts?0>ts?0:255:ts}function ee(tn,ti,ta,to,ts,tc){te(tn,ti,0,ta,to+ts),te(tn,ti,1,ta,to+tc),te(tn,ti,2,ta,to-tc),te(tn,ti,3,ta,to-ts)}function re(tn){return(20091*tn>>16)+tn}function ne(tn,ti,ta,to){var ts,tc=0,tu=a(16);for(ts=0;4>ts;++ts){var th=tn[ti+0]+tn[ti+8],tl=tn[ti+0]-tn[ti+8],tf=(35468*tn[ti+4]>>16)-re(tn[ti+12]),td=re(tn[ti+4])+(35468*tn[ti+12]>>16);tu[tc+0]=th+td,tu[tc+1]=tl+tf,tu[tc+2]=tl-tf,tu[tc+3]=th-td,tc+=4,ti++}for(ts=tc=0;4>ts;++ts)th=(tn=tu[tc+0]+4)+tu[tc+8],tl=tn-tu[tc+8],tf=(35468*tu[tc+4]>>16)-re(tu[tc+12]),te(ta,to,0,0,th+(td=re(tu[tc+4])+(35468*tu[tc+12]>>16))),te(ta,to,1,0,tl+tf),te(ta,to,2,0,tl-tf),te(ta,to,3,0,th-td),tc++,to+=32}function ie(tn,ti,ta,to){var ts=tn[ti+0]+4,tc=35468*tn[ti+4]>>16,tu=re(tn[ti+4]),th=35468*tn[ti+1]>>16;ee(ta,to,0,ts+tu,tn=re(tn[ti+1]),th),ee(ta,to,1,ts+tc,tn,th),ee(ta,to,2,ts-tc,tn,th),ee(ta,to,3,ts-tu,tn,th)}function ae(tn,ti,ta,to,ts){ne(tn,ti,ta,to),ts&&ne(tn,ti+16,ta,to+4)}function oe(tn,ti,ta,to){tS(tn,ti+0,ta,to,1),tS(tn,ti+32,ta,to+128,1)}function se(tn,ti,ta,to){var ts;for(tn=tn[ti+0]+4,ts=0;4>ts;++ts)for(ti=0;4>ti;++ti)te(ta,to,ti,ts,tn)}function ce(tn,ti,ta,to){tn[ti+0]&&tk(tn,ti+0,ta,to),tn[ti+16]&&tk(tn,ti+16,ta,to+4),tn[ti+32]&&tk(tn,ti+32,ta,to+128),tn[ti+48]&&tk(tn,ti+48,ta,to+128+4)}function ue(tn,ti,ta,to){var ts,tc=a(16);for(ts=0;4>ts;++ts){var tu=tn[ti+0+ts]+tn[ti+12+ts],th=tn[ti+4+ts]+tn[ti+8+ts],tl=tn[ti+4+ts]-tn[ti+8+ts],tf=tn[ti+0+ts]-tn[ti+12+ts];tc[0+ts]=tu+th,tc[8+ts]=tu-th,tc[4+ts]=tf+tl,tc[12+ts]=tf-tl}for(ts=0;4>ts;++ts)tu=(tn=tc[0+4*ts]+3)+tc[3+4*ts],th=tc[1+4*ts]+tc[2+4*ts],tl=tc[1+4*ts]-tc[2+4*ts],tf=tn-tc[3+4*ts],ta[to+0]=tu+th>>3,ta[to+16]=tf+tl>>3,ta[to+32]=tu-th>>3,ta[to+48]=tf-tl>>3,to+=64}function he(tn,ti,ta){var to,ts=ti-32,tc=255-tn[ts-1];for(to=0;to<ta;++to){var tu,th=tc+tn[ti-1];for(tu=0;tu<ta;++tu)tn[ti+tu]=tQ[th+tn[ts+tu]];ti+=32}}function le(tn,ti){he(tn,ti,4)}function fe(tn,ti){he(tn,ti,8)}function de(tn,ti){he(tn,ti,16)}function pe(tn,ti){var ta;for(ta=0;16>ta;++ta)n(tn,ti+32*ta,tn,ti-32,16)}function ge(tn,ti){var ta;for(ta=16;0<ta;--ta)i(tn,ti,tn[ti-1],16),ti+=32}function me(tn,ti,ta){var to;for(to=0;16>to;++to)i(ti,ta+32*to,tn,16)}function ve(tn,ti){var ta,to=16;for(ta=0;16>ta;++ta)to+=tn[ti-1+32*ta]+tn[ti+ta-32];me(to>>5,tn,ti)}function be(tn,ti){var ta,to=8;for(ta=0;16>ta;++ta)to+=tn[ti-1+32*ta];me(to>>4,tn,ti)}function ye(tn,ti){var ta,to=8;for(ta=0;16>ta;++ta)to+=tn[ti+ta-32];me(to>>4,tn,ti)}function we(tn,ti){me(128,tn,ti)}function Ne(tn,ti,ta){return tn+2*ti+ta+2>>2}function Le(tn,ti){var ta,to=ti-32;for(ta=0,to=new Uint8Array([Ne(tn[to-1],tn[to+0],tn[to+1]),Ne(tn[to+0],tn[to+1],tn[to+2]),Ne(tn[to+1],tn[to+2],tn[to+3]),Ne(tn[to+2],tn[to+3],tn[to+4])]);4>ta;++ta)n(tn,ti+32*ta,to,0,to.length)}function Ae(tn,ti){var ta=tn[ti-1],to=tn[ti-1+32],ts=tn[ti-1+64],tc=tn[ti-1+96];I(tn,ti+0,16843009*Ne(tn[ti-1-32],ta,to)),I(tn,ti+32,16843009*Ne(ta,to,ts)),I(tn,ti+64,16843009*Ne(to,ts,tc)),I(tn,ti+96,16843009*Ne(ts,tc,tc))}function xe(tn,ti){var ta,to=4;for(ta=0;4>ta;++ta)to+=tn[ti+ta-32]+tn[ti-1+32*ta];for(to>>=3,ta=0;4>ta;++ta)i(tn,ti+32*ta,to,4)}function Se(tn,ti){var ta=tn[ti-1+0],to=tn[ti-1+32],ts=tn[ti-1+64],tc=tn[ti-1-32],tu=tn[ti+0-32],th=tn[ti+1-32],tl=tn[ti+2-32],tf=tn[ti+3-32];tn[ti+0+96]=Ne(to,ts,tn[ti-1+96]),tn[ti+1+96]=tn[ti+0+64]=Ne(ta,to,ts),tn[ti+2+96]=tn[ti+1+64]=tn[ti+0+32]=Ne(tc,ta,to),tn[ti+3+96]=tn[ti+2+64]=tn[ti+1+32]=tn[ti+0+0]=Ne(tu,tc,ta),tn[ti+3+64]=tn[ti+2+32]=tn[ti+1+0]=Ne(th,tu,tc),tn[ti+3+32]=tn[ti+2+0]=Ne(tl,th,tu),tn[ti+3+0]=Ne(tf,tl,th)}function _e(tn,ti){var ta=tn[ti+1-32],to=tn[ti+2-32],ts=tn[ti+3-32],tc=tn[ti+4-32],tu=tn[ti+5-32],th=tn[ti+6-32],tl=tn[ti+7-32];tn[ti+0+0]=Ne(tn[ti+0-32],ta,to),tn[ti+1+0]=tn[ti+0+32]=Ne(ta,to,ts),tn[ti+2+0]=tn[ti+1+32]=tn[ti+0+64]=Ne(to,ts,tc),tn[ti+3+0]=tn[ti+2+32]=tn[ti+1+64]=tn[ti+0+96]=Ne(ts,tc,tu),tn[ti+3+32]=tn[ti+2+64]=tn[ti+1+96]=Ne(tc,tu,th),tn[ti+3+64]=tn[ti+2+96]=Ne(tu,th,tl),tn[ti+3+96]=Ne(th,tl,tl)}function Pe(tn,ti){var ta=tn[ti-1+0],to=tn[ti-1+32],ts=tn[ti-1+64],tc=tn[ti-1-32],tu=tn[ti+0-32],th=tn[ti+1-32],tl=tn[ti+2-32],tf=tn[ti+3-32];tn[ti+0+0]=tn[ti+1+64]=tc+tu+1>>1,tn[ti+1+0]=tn[ti+2+64]=tu+th+1>>1,tn[ti+2+0]=tn[ti+3+64]=th+tl+1>>1,tn[ti+3+0]=tl+tf+1>>1,tn[ti+0+96]=Ne(ts,to,ta),tn[ti+0+64]=Ne(to,ta,tc),tn[ti+0+32]=tn[ti+1+96]=Ne(ta,tc,tu),tn[ti+1+32]=tn[ti+2+96]=Ne(tc,tu,th),tn[ti+2+32]=tn[ti+3+96]=Ne(tu,th,tl),tn[ti+3+32]=Ne(th,tl,tf)}function ke(tn,ti){var ta=tn[ti+0-32],to=tn[ti+1-32],ts=tn[ti+2-32],tc=tn[ti+3-32],tu=tn[ti+4-32],th=tn[ti+5-32],tl=tn[ti+6-32],tf=tn[ti+7-32];tn[ti+0+0]=ta+to+1>>1,tn[ti+1+0]=tn[ti+0+64]=to+ts+1>>1,tn[ti+2+0]=tn[ti+1+64]=ts+tc+1>>1,tn[ti+3+0]=tn[ti+2+64]=tc+tu+1>>1,tn[ti+0+32]=Ne(ta,to,ts),tn[ti+1+32]=tn[ti+0+96]=Ne(to,ts,tc),tn[ti+2+32]=tn[ti+1+96]=Ne(ts,tc,tu),tn[ti+3+32]=tn[ti+2+96]=Ne(tc,tu,th),tn[ti+3+64]=Ne(tu,th,tl),tn[ti+3+96]=Ne(th,tl,tf)}function Ie(tn,ti){var ta=tn[ti-1+0],to=tn[ti-1+32],ts=tn[ti-1+64],tc=tn[ti-1+96];tn[ti+0+0]=ta+to+1>>1,tn[ti+2+0]=tn[ti+0+32]=to+ts+1>>1,tn[ti+2+32]=tn[ti+0+64]=ts+tc+1>>1,tn[ti+1+0]=Ne(ta,to,ts),tn[ti+3+0]=tn[ti+1+32]=Ne(to,ts,tc),tn[ti+3+32]=tn[ti+1+64]=Ne(ts,tc,tc),tn[ti+3+64]=tn[ti+2+64]=tn[ti+0+96]=tn[ti+1+96]=tn[ti+2+96]=tn[ti+3+96]=tc}function Fe(tn,ti){var ta=tn[ti-1+0],to=tn[ti-1+32],ts=tn[ti-1+64],tc=tn[ti-1+96],tu=tn[ti-1-32],th=tn[ti+0-32],tl=tn[ti+1-32],tf=tn[ti+2-32];tn[ti+0+0]=tn[ti+2+32]=ta+tu+1>>1,tn[ti+0+32]=tn[ti+2+64]=to+ta+1>>1,tn[ti+0+64]=tn[ti+2+96]=ts+to+1>>1,tn[ti+0+96]=tc+ts+1>>1,tn[ti+3+0]=Ne(th,tl,tf),tn[ti+2+0]=Ne(tu,th,tl),tn[ti+1+0]=tn[ti+3+32]=Ne(ta,tu,th),tn[ti+1+32]=tn[ti+3+64]=Ne(to,ta,tu),tn[ti+1+64]=tn[ti+3+96]=Ne(ts,to,ta),tn[ti+1+96]=Ne(tc,ts,to)}function Ce(tn,ti){var ta;for(ta=0;8>ta;++ta)n(tn,ti+32*ta,tn,ti-32,8)}function je(tn,ti){var ta;for(ta=0;8>ta;++ta)i(tn,ti,tn[ti-1],8),ti+=32}function Oe(tn,ti,ta){var to;for(to=0;8>to;++to)i(ti,ta+32*to,tn,8)}function Be(tn,ti){var ta,to=8;for(ta=0;8>ta;++ta)to+=tn[ti+ta-32]+tn[ti-1+32*ta];Oe(to>>4,tn,ti)}function Me(tn,ti){var ta,to=4;for(ta=0;8>ta;++ta)to+=tn[ti+ta-32];Oe(to>>3,tn,ti)}function Ee(tn,ti){var ta,to=4;for(ta=0;8>ta;++ta)to+=tn[ti-1+32*ta];Oe(to>>3,tn,ti)}function qe(tn,ti){Oe(128,tn,ti)}function De(tn,ti,ta){var to=tn[ti-ta],ts=tn[ti+0],tc=3*(ts-to)+tK[1020+tn[ti-2*ta]-tn[ti+ta]],tu=t$[112+(tc+4>>3)];tn[ti-ta]=tQ[255+to+t$[112+(tc+3>>3)]],tn[ti+0]=tQ[255+ts-tu]}function Re(tn,ti,ta,to){var ts=tn[ti+0],tc=tn[ti+ta];return t1[255+tn[ti-2*ta]-tn[ti-ta]]>to||t1[255+tc-ts]>to}function Te(tn,ti,ta,to){return 4*t1[255+tn[ti-ta]-tn[ti+0]]+t1[255+tn[ti-2*ta]-tn[ti+ta]]<=to}function Ue(tn,ti,ta,to,ts){var tc=tn[ti-3*ta],tu=tn[ti-2*ta],th=tn[ti-ta],tl=tn[ti+0],tf=tn[ti+ta],td=tn[ti+2*ta],tp=tn[ti+3*ta];return 4*t1[255+th-tl]+t1[255+tu-tf]>to?0:t1[255+tn[ti-4*ta]-tc]<=ts&&t1[255+tc-tu]<=ts&&t1[255+tu-th]<=ts&&t1[255+tp-td]<=ts&&t1[255+td-tf]<=ts&&t1[255+tf-tl]<=ts}function ze(tn,ti,ta,to){var ts=2*to+1;for(to=0;16>to;++to)Te(tn,ti+to,ta,ts)&&De(tn,ti+to,ta)}function He(tn,ti,ta,to){var ts=2*to+1;for(to=0;16>to;++to)Te(tn,ti+to*ta,1,ts)&&De(tn,ti+to*ta,1)}function We(tn,ti,ta,to){var ts;for(ts=3;0<ts;--ts)ze(tn,ti+=4*ta,ta,to)}function Ve(tn,ti,ta,to){var ts;for(ts=3;0<ts;--ts)He(tn,ti+=4,ta,to)}function Ge(tn,ti,ta,to,ts,tc,tu,th){for(tc=2*tc+1;0<ts--;){if(Ue(tn,ti,ta,tc,tu)){if(Re(tn,ti,ta,th))De(tn,ti,ta);else{var tl=ti,tf=tn[tl-2*ta],td=tn[tl-ta],tp=tn[tl+0],tg=tn[tl+ta],tm=tn[tl+2*ta],tv=27*(ty=tK[1020+3*(tp-td)+tK[1020+tf-tg]])+63>>7,tb=18*ty+63>>7,ty=9*ty+63>>7;tn[tl-3*ta]=tQ[255+tn[tl-3*ta]+ty],tn[tl-2*ta]=tQ[255+tf+tb],tn[tl-ta]=tQ[255+td+tv],tn[tl+0]=tQ[255+tp-tv],tn[tl+ta]=tQ[255+tg-tb],tn[tl+2*ta]=tQ[255+tm-ty]}}ti+=to}}function Ye(tn,ti,ta,to,ts,tc,tu,th){for(tc=2*tc+1;0<ts--;){if(Ue(tn,ti,ta,tc,tu)){if(Re(tn,ti,ta,th))De(tn,ti,ta);else{var tl=ti,tf=tn[tl-ta],td=tn[tl+0],tp=tn[tl+ta],tg=t$[112+((tm=3*(td-tf))+4>>3)],tm=t$[112+(tm+3>>3)],tv=tg+1>>1;tn[tl-2*ta]=tQ[255+tn[tl-2*ta]+tv],tn[tl-ta]=tQ[255+tf+tm],tn[tl+0]=tQ[255+td-tg],tn[tl+ta]=tQ[255+tp-tv]}}ti+=to}}function Je(tn,ti,ta,to,ts,tc){Ge(tn,ti,ta,1,16,to,ts,tc)}function Xe(tn,ti,ta,to,ts,tc){Ge(tn,ti,1,ta,16,to,ts,tc)}function Ke(tn,ti,ta,to,ts,tc){var tu;for(tu=3;0<tu;--tu)Ye(tn,ti+=4*ta,ta,1,16,to,ts,tc)}function Ze(tn,ti,ta,to,ts,tc){var tu;for(tu=3;0<tu;--tu)Ye(tn,ti+=4,1,ta,16,to,ts,tc)}function $e(tn,ti,ta,to,ts,tc,tu,th){Ge(tn,ti,ts,1,8,tc,tu,th),Ge(ta,to,ts,1,8,tc,tu,th)}function Qe(tn,ti,ta,to,ts,tc,tu,th){Ge(tn,ti,1,ts,8,tc,tu,th),Ge(ta,to,1,ts,8,tc,tu,th)}function tr(tn,ti,ta,to,ts,tc,tu,th){Ye(tn,ti+4*ts,ts,1,8,tc,tu,th),Ye(ta,to+4*ts,ts,1,8,tc,tu,th)}function er(tn,ti,ta,to,ts,tc,tu,th){Ye(tn,ti+4,1,ts,8,tc,tu,th),Ye(ta,to+4,1,ts,8,tc,tu,th)}function rr(){this.ba=new ot,this.ec=[],this.cc=[],this.Mc=[],this.Dc=this.Nc=this.dc=this.fc=0,this.Oa=new ct,this.memory=0,this.Ib="OutputFunc",this.Jb="OutputAlphaFunc",this.Nd="OutputRowFunc"}function nr(){this.data=[],this.offset=this.kd=this.ha=this.w=0,this.na=[],this.xa=this.gb=this.Ja=this.Sa=this.P=0}function ir(){this.nc=this.Ea=this.b=this.hc=0,this.K=[],this.w=0}function ar(){this.ua=0,this.Wa=new M,this.vb=new M,this.md=this.xc=this.wc=0,this.vc=[],this.Wb=0,this.Ya=new d,this.yc=new l}function or(){this.xb=this.a=0,this.l=new Gt,this.ca=new ot,this.V=[],this.Ba=0,this.Ta=[],this.Ua=0,this.m=new N,this.Pb=0,this.wd=new N,this.Ma=this.$=this.C=this.i=this.c=this.xd=0,this.s=new ar,this.ab=0,this.gc=o(4,ir),this.Oc=0}function sr(){this.Lc=this.Z=this.$a=this.i=this.c=0,this.l=new Gt,this.ic=0,this.ca=[],this.tb=0,this.qd=null,this.rd=0}function cr(tn,ti,ta,to,ts,tc,tu){for(tn=null==tn?0:tn[ti+0],ti=0;ti<tu;++ti)ts[tc+ti]=tn+ta[to+ti]&255,tn=ts[tc+ti]}function ur(tn,ti,ta,to,ts,tc,tu){var th;if(null==tn)cr(null,null,ta,to,ts,tc,tu);else for(th=0;th<tu;++th)ts[tc+th]=tn[ti+th]+ta[to+th]&255}function hr(tn,ti,ta,to,ts,tc,tu){if(null==tn)cr(null,null,ta,to,ts,tc,tu);else{var th,tl=tn[ti+0],tf=tl,td=tl;for(th=0;th<tu;++th)tf=td+(tl=tn[ti+th])-tf,td=ta[to+th]+(-256&tf?0>tf?0:255:tf)&255,tf=tl,ts[tc+th]=td}}function fr(tn,ti,ta,to,ts,tc){for(;0<ts--;){var tu,th=ti+(ta?1:0),tl=ti+(ta?0:3);for(tu=0;tu<to;++tu){var tf=tn[tl+4*tu];255!=tf&&(tf*=32897,tn[th+4*tu+0]=tn[th+4*tu+0]*tf>>23,tn[th+4*tu+1]=tn[th+4*tu+1]*tf>>23,tn[th+4*tu+2]=tn[th+4*tu+2]*tf>>23)}ti+=tc}}function dr(tn,ti,ta,to,ts){for(;0<to--;){var tc;for(tc=0;tc<ta;++tc){var tu=tn[ti+2*tc+0],th=15&(tf=tn[ti+2*tc+1]),tl=4369*th,tf=(240&tf|tf>>4)*tl>>16;tn[ti+2*tc+0]=(240&tu|tu>>4)*tl>>16&240|(15&tu|tu<<4)*tl>>16>>4&15,tn[ti+2*tc+1]=240&tf|th}ti+=ts}}function pr(tn,ti,ta,to,ts,tc,tu,th){var tl,tf,td=255;for(tf=0;tf<ts;++tf){for(tl=0;tl<to;++tl){var tp=tn[ti+tl];tc[tu+4*tl]=tp,td&=tp}ti+=ta,tu+=th}return 255!=td}function gr(tn,ti,ta,to,ts){var tc;for(tc=0;tc<ts;++tc)ta[to+tc]=tn[ti+tc]>>8}function mr(){tz=fr,tV=dr,tH=pr,tW=gr}function vr(ti,ta,to){tn[ti]=function(tn,ti,ts,tc,tu,th,tl,tf,td,tp,tg,tm,tv,tb,ty,tw,tN){var tL,tx=tN-1>>1,tA=tu[th+0]|tl[tf+0]<<16,tS=td[tp+0]|tg[tm+0]<<16;e(null!=tn);var t_=3*tA+tS+131074>>2;for(ta(tn[ti+0],255&t_,t_>>16,tv,tb),null!=ts&&(t_=3*tS+tA+131074>>2,ta(ts[tc+0],255&t_,t_>>16,ty,tw)),tL=1;tL<=tx;++tL){var tP=tu[th+tL]|tl[tf+tL]<<16,tk=td[tp+tL]|tg[tm+tL]<<16,tF=tA+tP+tS+tk+524296,tI=tF+2*(tP+tS)>>3;t_=tI+tA>>1,tA=(tF=tF+2*(tA+tk)>>3)+tP>>1,ta(tn[ti+2*tL-1],255&t_,t_>>16,tv,tb+(2*tL-1)*to),ta(tn[ti+2*tL-0],255&tA,tA>>16,tv,tb+(2*tL-0)*to),null!=ts&&(t_=tF+tS>>1,tA=tI+tk>>1,ta(ts[tc+2*tL-1],255&t_,t_>>16,ty,tw+(2*tL-1)*to),ta(ts[tc+2*tL+0],255&tA,tA>>16,ty,tw+(2*tL+0)*to)),tA=tP,tS=tk}1&tN||(t_=3*tA+tS+131074>>2,ta(tn[ti+tN-1],255&t_,t_>>16,tv,tb+(tN-1)*to),null!=ts&&(t_=3*tS+tA+131074>>2,ta(ts[tc+tN-1],255&t_,t_>>16,ty,tw+(tN-1)*to)))}}function br(){eF[t2]=eI,eF[t5]=ej,eF[t0]=eC,eF[t3]=eO,eF[t4]=eE,eF[t6]=eB,eF[t8]=eM,eF[t7]=ej,eF[t9]=eO,eF[en]=eE,eF[ei]=eB}function yr(tn){return tn&~ez?0>tn?0:255:tn>>eU}function wr(tn,ti){return yr((19077*tn>>8)+(26149*ti>>8)-14234)}function Nr(tn,ti,ta){return yr((19077*tn>>8)-(6419*ti>>8)-(13320*ta>>8)+8708)}function Lr(tn,ti){return yr((19077*tn>>8)+(33050*ti>>8)-17685)}function Ar(tn,ti,ta,to,ts){to[ts+0]=wr(tn,ta),to[ts+1]=Nr(tn,ti,ta),to[ts+2]=Lr(tn,ti)}function xr(tn,ti,ta,to,ts){to[ts+0]=Lr(tn,ti),to[ts+1]=Nr(tn,ti,ta),to[ts+2]=wr(tn,ta)}function Sr(tn,ti,ta,to,ts){var tc=Nr(tn,ti,ta);ti=tc<<3&224|Lr(tn,ti)>>3,to[ts+0]=248&wr(tn,ta)|tc>>5,to[ts+1]=ti}function _r(tn,ti,ta,to,ts){var tc=240&Lr(tn,ti)|15;to[ts+0]=240&wr(tn,ta)|Nr(tn,ti,ta)>>4,to[ts+1]=tc}function Pr(tn,ti,ta,to,ts){to[ts+0]=255,Ar(tn,ti,ta,to,ts+1)}function kr(tn,ti,ta,to,ts){xr(tn,ti,ta,to,ts),to[ts+3]=255}function Ir(tn,ti,ta,to,ts){Ar(tn,ti,ta,to,ts),to[ts+3]=255}function Vt(tn,ti){return 0>tn?0:tn>ti?ti:tn}function Fr(ti,ta,to){tn[ti]=function(tn,ti,ts,tc,tu,th,tl,tf,td){for(var tp=tf+(-2&td)*to;tf!=tp;)ta(tn[ti+0],ts[tc+0],tu[th+0],tl,tf),ta(tn[ti+1],ts[tc+0],tu[th+0],tl,tf+to),ti+=2,++tc,++th,tf+=2*to;1&td&&ta(tn[ti+0],ts[tc+0],tu[th+0],tl,tf)}}function Cr(tn,ti,ta){return 0==ta?0==tn?0==ti?6:5:0==ti?4:0:ta}function jr(tn,ti,ta,to,ts){switch(tn>>>30){case 3:tS(ti,ta,to,ts,0);break;case 2:t_(ti,ta,to,ts);break;case 1:tk(ti,ta,to,ts)}}function Or(tn,ti){var ta,to,ts=ti.M,tc=ti.Nb,tu=tn.oc,th=tn.pc+40,tl=tn.oc,tf=tn.pc+584,td=tn.oc,tp=tn.pc+600;for(ta=0;16>ta;++ta)tu[th+32*ta-1]=129;for(ta=0;8>ta;++ta)tl[tf+32*ta-1]=129,td[tp+32*ta-1]=129;for(0<ts?tu[th-1-32]=tl[tf-1-32]=td[tp-1-32]=129:(i(tu,th-32-1,127,21),i(tl,tf-32-1,127,9),i(td,tp-32-1,127,9)),to=0;to<tn.za;++to){var tg=ti.ya[ti.aa+to];if(0<to){for(ta=-1;16>ta;++ta)n(tu,th+32*ta-4,tu,th+32*ta+12,4);for(ta=-1;8>ta;++ta)n(tl,tf+32*ta-4,tl,tf+32*ta+4,4),n(td,tp+32*ta-4,td,tp+32*ta+4,4)}var tm=tn.Gd,tv=tn.Hd+to,tb=tg.ad,ty=tg.Hc;if(0<ts&&(n(tu,th-32,tm[tv].y,0,16),n(tl,tf-32,tm[tv].f,0,8),n(td,tp-32,tm[tv].ea,0,8)),tg.Za){var tw=tu,tN=th-32+16;for(0<ts&&(to>=tn.za-1?i(tw,tN,tm[tv].y[15],4):n(tw,tN,tm[tv+1].y,0,4)),ta=0;4>ta;ta++)tw[tN+128+ta]=tw[tN+256+ta]=tw[tN+384+ta]=tw[tN+0+ta];for(ta=0;16>ta;++ta,ty<<=2)tw=tu,tN=th+eZ[ta],eA[tg.Ob[ta]](tw,tN),jr(ty,tb,16*+ta,tw,tN)}else if(ex[tw=Cr(to,ts,tg.Ob[0])](tu,th),0!=ty)for(ta=0;16>ta;++ta,ty<<=2)jr(ty,tb,16*+ta,tu,th+eZ[ta]);for(ta=tg.Gc,eS[tw=Cr(to,ts,tg.Dd)](tl,tf),eS[tw](td,tp),ty=tb,tw=tl,tN=tf,255&(tg=ta>>0)&&(170&tg?tP(ty,256,tw,tN):tF(ty,256,tw,tN)),tg=td,ty=tp,255&(ta>>=8)&&(170&ta?tP(tb,320,tg,ty):tF(tb,320,tg,ty)),ts<tn.Ub-1&&(n(tm[tv].y,0,tu,th+480,16),n(tm[tv].f,0,tl,tf+224,8),n(tm[tv].ea,0,td,tp+224,8)),ta=8*tc*tn.B,tm=tn.sa,tv=tn.ta+16*to+16*tc*tn.R,tb=tn.qa,tg=tn.ra+8*to+ta,ty=tn.Ha,tw=tn.Ia+8*to+ta,ta=0;16>ta;++ta)n(tm,tv+ta*tn.R,tu,th+32*ta,16);for(ta=0;8>ta;++ta)n(tb,tg+ta*tn.B,tl,tf+32*ta,8),n(ty,tw+ta*tn.B,td,tp+32*ta,8)}}function Br(tn,ti,ta,to,ts,tc,tu,th,tl){var tf=[0],td=[0],tp=0,tg=null!=tl?tl.kd:0,tm=null!=tl?tl:new nr;if(null==tn||12>ta)return 7;tm.data=tn,tm.w=ti,tm.ha=ta,ti=[ti],ta=[ta],tm.gb=[tm.gb];t:{var tv=ti,tb=ta,ty=tm.gb;if(e(null!=tn),e(null!=tb),e(null!=ty),ty[0]=0,12<=tb[0]&&!r(tn,tv[0],"RIFF")){if(r(tn,tv[0]+8,"WEBP")){ty=3;break t}var tw=j(tn,tv[0]+4);if(12>tw||4294967286<tw){ty=3;break t}if(tg&&tw>tb[0]-8){ty=7;break t}ty[0]=tw,tv[0]+=12,tb[0]-=12}ty=0}if(0!=ty)return ty;for(tw=0<tm.gb[0],ta=ta[0];;){t:{var tN=tn;tb=ti,ty=ta;var tL=tf,tx=td,tA=tv=[0];if((tP=tp=[tp])[0]=0,8>ty[0])ty=7;else{if(!r(tN,tb[0],"VP8X")){if(10!=j(tN,tb[0]+4)){ty=3;break t}if(18>ty[0]){ty=7;break t}var tS=j(tN,tb[0]+8),t_=1+C(tN,tb[0]+12);if(2147483648<=t_*(tN=1+C(tN,tb[0]+15))){ty=3;break t}null!=tA&&(tA[0]=tS),null!=tL&&(tL[0]=t_),null!=tx&&(tx[0]=tN),tb[0]+=18,ty[0]-=18,tP[0]=1}ty=0}}if(tp=tp[0],tv=tv[0],0!=ty)return ty;if(tb=!!(2&tv),!tw&&tp)return 3;if(null!=tc&&(tc[0]=!!(16&tv)),null!=tu&&(tu[0]=tb),null!=th&&(th[0]=0),tu=0,tv=td[0],tp&&tb&&null==tl){ty=0;break}if(4>ta){ty=7;break}if(tw&&tp||!tw&&!tp&&!r(tn,ti[0],"ALPH")){ta=[ta],tm.na=[tm.na],tm.P=[tm.P],tm.Sa=[tm.Sa];t:{tS=tn,ty=ti,tw=ta;var tP=tm.gb;tL=tm.na,tx=tm.P,tA=tm.Sa,t_=22,e(null!=tS),e(null!=tw),tN=ty[0];var tk=tw[0];for(e(null!=tL),e(null!=tA),tL[0]=null,tx[0]=null,tA[0]=0;;){if(ty[0]=tN,tw[0]=tk,8>tk){ty=7;break t}var tF=j(tS,tN+4);if(4294967286<tF){ty=3;break t}var tI=8+tF+1&-2;if(t_+=tI,0<tP&&t_>tP){ty=3;break t}if(!r(tS,tN,"VP8 ")||!r(tS,tN,"VP8L")){ty=0;break t}if(tk[0]<tI){ty=7;break t}r(tS,tN,"ALPH")||(tL[0]=tS,tx[0]=tN+8,tA[0]=tF),tN+=tI,tk-=tI}}if(ta=ta[0],tm.na=tm.na[0],tm.P=tm.P[0],tm.Sa=tm.Sa[0],0!=ty)break}ta=[ta],tm.Ja=[tm.Ja],tm.xa=[tm.xa];t:if(tP=tn,ty=ti,tw=ta,tL=tm.gb[0],tx=tm.Ja,tA=tm.xa,tN=!r(tP,tS=ty[0],"VP8 "),t_=!r(tP,tS,"VP8L"),e(null!=tP),e(null!=tw),e(null!=tx),e(null!=tA),8>tw[0])ty=7;else{if(tN||t_){if(tP=j(tP,tS+4),12<=tL&&tP>tL-12){ty=3;break t}if(tg&&tP>tw[0]-8){ty=7;break t}tx[0]=tP,ty[0]+=8,tw[0]-=8,tA[0]=t_}else tA[0]=5<=tw[0]&&47==tP[tS+0]&&!(tP[tS+4]>>5),tx[0]=tw[0];ty=0}if(ta=ta[0],tm.Ja=tm.Ja[0],tm.xa=tm.xa[0],ti=ti[0],0!=ty)break;if(4294967286<tm.Ja)return 3;if(null==th||tb||(th[0]=tm.xa?2:1),tu=[tu],tv=[tv],tm.xa){if(5>ta){ty=7;break}th=tu,tg=tv,tb=tc,null==tn||5>ta?tn=0:5<=ta&&47==tn[ti+0]&&!(tn[ti+4]>>5)?(tw=[0],tP=[0],tL=[0],v(tx=new N,tn,ti,ta),gt(tx,tw,tP,tL)?(null!=th&&(th[0]=tw[0]),null!=tg&&(tg[0]=tP[0]),null!=tb&&(tb[0]=tL[0]),tn=1):tn=0):tn=0}else{if(10>ta){ty=7;break}th=tv,null==tn||10>ta||!Xt(tn,ti+3,ta-3)?tn=0:(tg=tn[ti+0]|tn[ti+1]<<8|tn[ti+2]<<16,tb=16383&(tn[ti+7]<<8|tn[ti+6]),tn=16383&(tn[ti+9]<<8|tn[ti+8]),1&tg||3<(tg>>1&7)||!(tg>>4&1)||tg>>5>=tm.Ja||!tb||!tn?tn=0:(tu&&(tu[0]=tb),th&&(th[0]=tn),tn=1))}if(!tn||(tu=tu[0],tv=tv[0],tp&&(0!=tu||td[0]!=tv)))return 3;null!=tl&&(tl[0]=tm,tl.offset=ti-tl.w,e(4294967286>ti-tl.w),e(tl.offset==tl.ha-ta));break}return 0==ty||7==ty&&tp&&null==tl?(null!=tc&&(tc[0]|=null!=tm.na&&0<tm.na.length),null!=to&&(to[0]=tu),null!=ts&&(ts[0]=tv),0):ty}function Mr(tn,ti,ta){var to=ti.width,ts=ti.height,tc=0,tu=0,th=to,tl=ts;if(ti.Da=null!=tn&&0<tn.Da,ti.Da&&(th=tn.cd,tl=tn.bd,tc=tn.v,tu=tn.j,11>ta||(tc&=-2,tu&=-2),0>tc||0>tu||0>=th||0>=tl||tc+th>to||tu+tl>ts))return 0;if(ti.v=tc,ti.j=tu,ti.va=tc+th,ti.o=tu+tl,ti.U=th,ti.T=tl,ti.da=null!=tn&&0<tn.da,ti.da){if(!E(th,tl,ta=[tn.ib],tc=[tn.hb]))return 0;ti.ib=ta[0],ti.hb=tc[0]}return ti.ob=null!=tn&&tn.ob,ti.Kb=null==tn||!tn.Sd,ti.da&&(ti.ob=ti.ib<3*to/4&&ti.hb<3*ts/4,ti.Kb=0),1}function Er(tn){if(null==tn)return 2;if(11>tn.S){var ti=tn.f.RGBA;ti.fb+=(tn.height-1)*ti.A,ti.A=-ti.A}else ti=tn.f.kb,tn=tn.height,ti.O+=(tn-1)*ti.fa,ti.fa=-ti.fa,ti.N+=(tn-1>>1)*ti.Ab,ti.Ab=-ti.Ab,ti.W+=(tn-1>>1)*ti.Db,ti.Db=-ti.Db,null!=ti.F&&(ti.J+=(tn-1)*ti.lb,ti.lb=-ti.lb);return 0}function qr(tn,ti,ta,to){if(null==to||0>=tn||0>=ti)return 2;if(null!=ta){if(ta.Da){var ts=ta.cd,tc=ta.bd,tu=-2&ta.v,th=-2&ta.j;if(0>tu||0>th||0>=ts||0>=tc||tu+ts>tn||th+tc>ti)return 2;tn=ts,ti=tc}if(ta.da){if(!E(tn,ti,ts=[ta.ib],tc=[ta.hb]))return 2;tn=ts[0],ti=tc[0]}}to.width=tn,to.height=ti;t:{var tl=to.width,tf=to.height;if(tn=to.S,0>=tl||0>=tf||!(tn>=t2&&13>tn))tn=2;else{if(0>=to.Rd&&null==to.sd){tu=tc=ts=ti=0;var td=(th=tl*eQ[tn])*tf;if(11>tn||(tc=(tf+1)/2*(ti=(tl+1)/2),12==tn&&(tu=(ts=tl)*tf)),null==(tf=a(td+2*tc+tu))){tn=1;break t}to.sd=tf,11>tn?((tl=to.f.RGBA).eb=tf,tl.fb=0,tl.A=th,tl.size=td):((tl=to.f.kb).y=tf,tl.O=0,tl.fa=th,tl.Fd=td,tl.f=tf,tl.N=0+td,tl.Ab=ti,tl.Cd=tc,tl.ea=tf,tl.W=0+td+tc,tl.Db=ti,tl.Ed=tc,12==tn&&(tl.F=tf,tl.J=0+td+2*tc),tl.Tc=tu,tl.lb=ts)}if(ti=1,ts=to.S,tc=to.width,tu=to.height,ts>=t2&&13>ts){if(11>ts)ti&=(th=Math.abs((tn=to.f.RGBA).A))*(tu-1)+tc<=tn.size,ti&=th>=tc*eQ[ts],ti&=null!=tn.eb;else{tn=to.f.kb,th=(tc+1)/2,td=(tu+1)/2,tl=Math.abs(tn.fa),tf=Math.abs(tn.Ab);var tp=Math.abs(tn.Db),tg=Math.abs(tn.lb),tm=tg*(tu-1)+tc;ti&=tl*(tu-1)+tc<=tn.Fd,ti&=tf*(td-1)+th<=tn.Cd,ti=(ti&=tp*(td-1)+th<=tn.Ed)&tl>=tc&tf>=th&tp>=th&null!=tn.y&null!=tn.f&null!=tn.ea,12==ts&&(ti&=tg>=tc,ti&=tm<=tn.Tc,ti&=null!=tn.F)}}else ti=0;tn=ti?0:2}}return 0!=tn||null!=ta&&ta.fd&&(tn=Er(to)),tn}var ti=64,ta=[0,1,3,7,15,31,63,127,255,511,1023,2047,4095,8191,16383,32767,65535,131071,262143,524287,1048575,2097151,4194303,8388607,16777215],to=24,ts=32,tc=8,tu=[0,0,1,1,2,2,2,2,3,3,3,3,3,3,3,3,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7];R("Predictor0","PredictorAdd0"),tn.Predictor0=function(){return **********},tn.Predictor1=function(tn){return tn},tn.Predictor2=function(tn,ti,ta){return ti[ta+0]},tn.Predictor3=function(tn,ti,ta){return ti[ta+1]},tn.Predictor4=function(tn,ti,ta){return ti[ta-1]},tn.Predictor5=function(tn,ti,ta){return U(U(tn,ti[ta+1]),ti[ta+0])},tn.Predictor6=function(tn,ti,ta){return U(tn,ti[ta-1])},tn.Predictor7=function(tn,ti,ta){return U(tn,ti[ta+0])},tn.Predictor8=function(tn,ti,ta){return U(ti[ta-1],ti[ta+0])},tn.Predictor9=function(tn,ti,ta){return U(ti[ta+0],ti[ta+1])},tn.Predictor10=function(tn,ti,ta){return U(U(tn,ti[ta-1]),U(ti[ta+0],ti[ta+1]))},tn.Predictor11=function(tn,ti,ta){var to=ti[ta+0];return 0>=W(to>>24&255,tn>>24&255,(ti=ti[ta-1])>>24&255)+W(to>>16&255,tn>>16&255,ti>>16&255)+W(to>>8&255,tn>>8&255,ti>>8&255)+W(255&to,255&tn,255&ti)?to:tn},tn.Predictor12=function(tn,ti,ta){var to=ti[ta+0];return(z((tn>>24&255)+(to>>24&255)-((ti=ti[ta-1])>>24&255))<<24|z((tn>>16&255)+(to>>16&255)-(ti>>16&255))<<16|z((tn>>8&255)+(to>>8&255)-(ti>>8&255))<<8|z((255&tn)+(255&to)-(255&ti)))>>>0},tn.Predictor13=function(tn,ti,ta){var to=ti[ta-1];return(H((tn=U(tn,ti[ta+0]))>>24&255,to>>24&255)<<24|H(tn>>16&255,to>>16&255)<<16|H(tn>>8&255,to>>8&255)<<8|H(tn>>0&255,to>>0&255))>>>0};var th=tn.PredictorAdd0;tn.PredictorAdd1=V,R("Predictor2","PredictorAdd2"),R("Predictor3","PredictorAdd3"),R("Predictor4","PredictorAdd4"),R("Predictor5","PredictorAdd5"),R("Predictor6","PredictorAdd6"),R("Predictor7","PredictorAdd7"),R("Predictor8","PredictorAdd8"),R("Predictor9","PredictorAdd9"),R("Predictor10","PredictorAdd10"),R("Predictor11","PredictorAdd11"),R("Predictor12","PredictorAdd12"),R("Predictor13","PredictorAdd13");var tl=tn.PredictorAdd2;X("ColorIndexInverseTransform","MapARGB","32b",function(tn){return tn>>8&255},function(tn){return tn}),X("VP8LColorIndexInverseTransformAlpha","MapAlpha","8b",function(tn){return tn},function(tn){return tn>>8&255});var tf,td=tn.ColorIndexInverseTransform,tp=tn.MapARGB,tg=tn.VP8LColorIndexInverseTransformAlpha,tm=tn.MapAlpha,tv=tn.VP8LPredictorsAdd=[];tv.length=16,(tn.VP8LPredictors=[]).length=16,(tn.VP8LPredictorsAdd_C=[]).length=16,(tn.VP8LPredictors_C=[]).length=16;var tb,ty,tw,tN,tL,tx,tA,tS,t_,tP,tk,tF,tI,tC,tj,tO,tE,tB,tM,tq,tD,tR,tT,tU,tz,tV,tH,tW,tG=a(511),tJ=a(2041),tY=a(225),tX=a(767),tZ=0,tK=tJ,t$=tY,tQ=tX,t1=tG,t2=0,t5=1,t0=2,t3=3,t4=4,t6=5,t8=6,t7=7,t9=8,en=9,ei=10,ea=[2,3,7],eo=[3,3,11],es=[280,256,256,256,40],ec=[0,1,1,1,0],eu=[17,18,0,1,2,3,4,5,16,6,7,8,9,10,11,12,13,14,15],eh=[24,7,23,25,40,6,39,41,22,26,38,42,56,5,55,57,21,27,54,58,37,43,72,4,71,73,20,28,53,59,70,74,36,44,88,69,75,52,60,3,87,89,19,29,86,90,35,45,68,76,85,91,51,61,104,2,103,105,18,30,102,106,34,46,84,92,67,77,101,107,50,62,120,1,119,121,83,93,17,31,100,108,66,78,118,122,33,47,117,123,49,63,99,109,82,94,0,116,124,65,79,16,32,98,110,48,115,125,81,95,64,114,126,97,111,80,113,127,96,112],el=[2954,2956,2958,2962,2970,2986,3018,3082,3212,3468,3980,5004],ef=8,ed=[4,5,6,7,8,9,10,10,11,12,13,14,15,16,17,17,18,19,20,20,21,21,22,22,23,23,24,25,25,26,27,28,29,30,31,32,33,34,35,36,37,37,38,39,40,41,42,43,44,45,46,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,76,77,78,79,80,81,82,83,84,85,86,87,88,89,91,93,95,96,98,100,101,102,104,106,108,110,112,114,116,118,122,124,126,128,130,132,134,136,138,140,143,145,148,151,154,157],ep=[4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,60,62,64,66,68,70,72,74,76,78,80,82,84,86,88,90,92,94,96,98,100,102,104,106,108,110,112,114,116,119,122,125,128,131,134,137,140,143,146,149,152,155,158,161,164,167,170,173,177,181,185,189,193,197,201,205,209,213,217,221,225,229,234,239,245,249,254,259,264,269,274,279,284],eg=null,em=[[173,148,140,0],[176,155,140,135,0],[180,157,141,134,130,0],[254,254,243,230,196,177,153,140,133,130,129,0]],ev=[0,1,4,8,5,2,3,6,9,12,13,10,7,11,14,15],eb=[-0,1,-1,2,-2,3,4,6,-3,5,-4,-5,-6,7,-7,8,-8,-9],ey=[[[[128,128,128,128,128,128,128,128,128,128,128],[128,128,128,128,128,128,128,128,128,128,128],[128,128,128,128,128,128,128,128,128,128,128]],[[253,136,254,255,228,219,128,128,128,128,128],[189,129,242,255,227,213,255,219,128,128,128],[106,126,227,252,214,209,255,255,128,128,128]],[[1,98,248,255,236,226,255,255,128,128,128],[181,133,238,254,221,234,255,154,128,128,128],[78,134,202,247,198,180,255,219,128,128,128]],[[1,185,249,255,243,255,128,128,128,128,128],[184,150,247,255,236,224,128,128,128,128,128],[77,110,216,255,236,230,128,128,128,128,128]],[[1,101,251,255,241,255,128,128,128,128,128],[170,139,241,252,236,209,255,255,128,128,128],[37,116,196,243,228,255,255,255,128,128,128]],[[1,204,254,255,245,255,128,128,128,128,128],[207,160,250,255,238,128,128,128,128,128,128],[102,103,231,255,211,171,128,128,128,128,128]],[[1,152,252,255,240,255,128,128,128,128,128],[177,135,243,255,234,225,128,128,128,128,128],[80,129,211,255,194,224,128,128,128,128,128]],[[1,1,255,128,128,128,128,128,128,128,128],[246,1,255,128,128,128,128,128,128,128,128],[255,128,128,128,128,128,128,128,128,128,128]]],[[[198,35,237,223,193,187,162,160,145,155,62],[131,45,198,221,172,176,220,157,252,221,1],[68,47,146,208,149,167,221,162,255,223,128]],[[1,149,241,255,221,224,255,255,128,128,128],[184,141,234,253,222,220,255,199,128,128,128],[81,99,181,242,176,190,249,202,255,255,128]],[[1,129,232,253,214,197,242,196,255,255,128],[99,121,210,250,201,198,255,202,128,128,128],[23,91,163,242,170,187,247,210,255,255,128]],[[1,200,246,255,234,255,128,128,128,128,128],[109,178,241,255,231,245,255,255,128,128,128],[44,130,201,253,205,192,255,255,128,128,128]],[[1,132,239,251,219,209,255,165,128,128,128],[94,136,225,251,218,190,255,255,128,128,128],[22,100,174,245,186,161,255,199,128,128,128]],[[1,182,249,255,232,235,128,128,128,128,128],[124,143,241,255,227,234,128,128,128,128,128],[35,77,181,251,193,211,255,205,128,128,128]],[[1,157,247,255,236,231,255,255,128,128,128],[121,141,235,255,225,227,255,255,128,128,128],[45,99,188,251,195,217,255,224,128,128,128]],[[1,1,251,255,213,255,128,128,128,128,128],[203,1,248,255,255,128,128,128,128,128,128],[137,1,177,255,224,255,128,128,128,128,128]]],[[[253,9,248,251,207,208,255,192,128,128,128],[175,13,224,243,193,185,249,198,255,255,128],[73,17,171,221,161,179,236,167,255,234,128]],[[1,95,247,253,212,183,255,255,128,128,128],[239,90,244,250,211,209,255,255,128,128,128],[155,77,195,248,188,195,255,255,128,128,128]],[[1,24,239,251,218,219,255,205,128,128,128],[201,51,219,255,196,186,128,128,128,128,128],[69,46,190,239,201,218,255,228,128,128,128]],[[1,191,251,255,255,128,128,128,128,128,128],[223,165,249,255,213,255,128,128,128,128,128],[141,124,248,255,255,128,128,128,128,128,128]],[[1,16,248,255,255,128,128,128,128,128,128],[190,36,230,255,236,255,128,128,128,128,128],[149,1,255,128,128,128,128,128,128,128,128]],[[1,226,255,128,128,128,128,128,128,128,128],[247,192,255,128,128,128,128,128,128,128,128],[240,128,255,128,128,128,128,128,128,128,128]],[[1,134,252,255,255,128,128,128,128,128,128],[213,62,250,255,255,128,128,128,128,128,128],[55,93,255,128,128,128,128,128,128,128,128]],[[128,128,128,128,128,128,128,128,128,128,128],[128,128,128,128,128,128,128,128,128,128,128],[128,128,128,128,128,128,128,128,128,128,128]]],[[[202,24,213,235,186,191,220,160,240,175,255],[126,38,182,232,169,184,228,174,255,187,128],[61,46,138,219,151,178,240,170,255,216,128]],[[1,112,230,250,199,191,247,159,255,255,128],[166,109,228,252,211,215,255,174,128,128,128],[39,77,162,232,172,180,245,178,255,255,128]],[[1,52,220,246,198,199,249,220,255,255,128],[124,74,191,243,183,193,250,221,255,255,128],[24,71,130,219,154,170,243,182,255,255,128]],[[1,182,225,249,219,240,255,224,128,128,128],[149,150,226,252,216,205,255,171,128,128,128],[28,108,170,242,183,194,254,223,255,255,128]],[[1,81,230,252,204,203,255,192,128,128,128],[123,102,209,247,188,196,255,233,128,128,128],[20,95,153,243,164,173,255,203,128,128,128]],[[1,222,248,255,216,213,128,128,128,128,128],[168,175,246,252,235,205,255,255,128,128,128],[47,116,215,255,211,212,255,255,128,128,128]],[[1,121,236,253,212,214,255,255,128,128,128],[141,84,213,252,201,202,255,219,128,128,128],[42,80,160,240,162,185,255,205,128,128,128]],[[1,1,255,128,128,128,128,128,128,128,128],[244,1,255,128,128,128,128,128,128,128,128],[238,1,255,128,128,128,128,128,128,128,128]]]],ew=[[[231,120,48,89,115,113,120,152,112],[152,179,64,126,170,118,46,70,95],[175,69,143,80,85,82,72,155,103],[56,58,10,171,218,189,17,13,152],[114,26,17,163,44,195,21,10,173],[121,24,80,195,26,62,44,64,85],[144,71,10,38,171,213,144,34,26],[170,46,55,19,136,160,33,206,71],[63,20,8,114,114,208,12,9,226],[81,40,11,96,182,84,29,16,36]],[[134,183,89,137,98,101,106,165,148],[72,187,100,130,157,111,32,75,80],[66,102,167,99,74,62,40,234,128],[41,53,9,178,241,141,26,8,107],[74,43,26,146,73,166,49,23,157],[65,38,105,160,51,52,31,115,128],[104,79,12,27,217,255,87,17,7],[87,68,71,44,114,51,15,186,23],[47,41,14,110,182,183,21,17,194],[66,45,25,102,197,189,23,18,22]],[[88,88,147,150,42,46,45,196,205],[43,97,183,117,85,38,35,179,61],[39,53,200,87,26,21,43,232,171],[56,34,51,104,114,102,29,93,77],[39,28,85,171,58,165,90,98,64],[34,22,116,206,23,34,43,166,73],[107,54,32,26,51,1,81,43,31],[68,25,106,22,64,171,36,225,114],[34,19,21,102,132,188,16,76,124],[62,18,78,95,85,57,50,48,51]],[[193,101,35,159,215,111,89,46,111],[60,148,31,172,219,228,21,18,111],[112,113,77,85,179,255,38,120,114],[40,42,1,196,245,209,10,25,109],[88,43,29,140,166,213,37,43,154],[61,63,30,155,67,45,68,1,209],[100,80,8,43,154,1,51,26,71],[142,78,78,16,255,128,34,197,171],[41,40,5,102,211,183,4,1,221],[51,50,17,168,209,192,23,25,82]],[[138,31,36,171,27,166,38,44,229],[67,87,58,169,82,115,26,59,179],[63,59,90,180,59,166,93,73,154],[40,40,21,116,143,209,34,39,175],[47,15,16,183,34,223,49,45,183],[46,17,33,183,6,98,15,32,183],[57,46,22,24,128,1,54,17,37],[65,32,73,115,28,128,23,128,205],[40,3,9,115,51,192,18,6,223],[87,37,9,115,59,77,64,21,47]],[[104,55,44,218,9,54,53,130,226],[64,90,70,205,40,41,23,26,57],[54,57,112,184,5,41,38,166,213],[30,34,26,133,152,116,10,32,134],[39,19,53,221,26,114,32,73,255],[31,9,65,234,2,15,1,118,73],[75,32,12,51,192,255,160,43,51],[88,31,35,67,102,85,55,186,85],[56,21,23,111,59,205,45,37,192],[55,38,70,124,73,102,1,34,98]],[[125,98,42,88,104,85,117,175,82],[95,84,53,89,128,100,113,101,45],[75,79,123,47,51,128,81,171,1],[57,17,5,71,102,57,53,41,49],[38,33,13,121,57,73,26,1,85],[41,10,67,138,77,110,90,47,114],[115,21,2,10,102,255,166,23,6],[101,29,16,10,85,128,101,196,26],[57,18,10,102,102,213,34,20,43],[117,20,15,36,163,128,68,1,26]],[[102,61,71,37,34,53,31,243,192],[69,60,71,38,73,119,28,222,37],[68,45,128,34,1,47,11,245,171],[62,17,19,70,146,85,55,62,70],[37,43,37,154,100,163,85,160,1],[63,9,92,136,28,64,32,201,85],[75,15,9,9,64,255,184,119,16],[86,6,28,5,64,255,25,248,1],[56,8,17,132,137,255,55,116,128],[58,15,20,82,135,57,26,121,40]],[[164,50,31,137,154,133,25,35,218],[51,103,44,131,131,123,31,6,158],[86,40,64,135,148,224,45,183,128],[22,26,17,131,240,154,14,1,209],[45,16,21,91,64,222,7,1,197],[56,21,39,155,60,138,23,102,213],[83,12,13,54,192,255,68,47,28],[85,26,85,85,128,128,32,146,171],[18,11,7,63,144,171,4,4,246],[35,27,10,146,174,171,12,26,128]],[[190,80,35,99,180,80,126,54,45],[85,126,47,87,176,51,41,20,32],[101,75,128,139,118,146,116,128,85],[56,41,15,176,236,85,37,9,62],[71,30,17,119,118,255,17,18,138],[101,38,60,138,55,70,43,26,142],[146,36,19,30,171,255,97,27,20],[138,45,61,62,219,1,81,188,64],[32,41,20,117,151,142,20,21,163],[112,19,12,61,195,128,48,4,24]]],eN=[[[[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]],[[176,246,255,255,255,255,255,255,255,255,255],[223,241,252,255,255,255,255,255,255,255,255],[249,253,253,255,255,255,255,255,255,255,255]],[[255,244,252,255,255,255,255,255,255,255,255],[234,254,254,255,255,255,255,255,255,255,255],[253,255,255,255,255,255,255,255,255,255,255]],[[255,246,254,255,255,255,255,255,255,255,255],[239,253,254,255,255,255,255,255,255,255,255],[254,255,254,255,255,255,255,255,255,255,255]],[[255,248,254,255,255,255,255,255,255,255,255],[251,255,254,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]],[[255,253,254,255,255,255,255,255,255,255,255],[251,254,254,255,255,255,255,255,255,255,255],[254,255,254,255,255,255,255,255,255,255,255]],[[255,254,253,255,254,255,255,255,255,255,255],[250,255,254,255,254,255,255,255,255,255,255],[254,255,255,255,255,255,255,255,255,255,255]],[[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]]],[[[217,255,255,255,255,255,255,255,255,255,255],[225,252,241,253,255,255,254,255,255,255,255],[234,250,241,250,253,255,253,254,255,255,255]],[[255,254,255,255,255,255,255,255,255,255,255],[223,254,254,255,255,255,255,255,255,255,255],[238,253,254,254,255,255,255,255,255,255,255]],[[255,248,254,255,255,255,255,255,255,255,255],[249,254,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]],[[255,253,255,255,255,255,255,255,255,255,255],[247,254,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]],[[255,253,254,255,255,255,255,255,255,255,255],[252,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]],[[255,254,254,255,255,255,255,255,255,255,255],[253,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]],[[255,254,253,255,255,255,255,255,255,255,255],[250,255,255,255,255,255,255,255,255,255,255],[254,255,255,255,255,255,255,255,255,255,255]],[[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]]],[[[186,251,250,255,255,255,255,255,255,255,255],[234,251,244,254,255,255,255,255,255,255,255],[251,251,243,253,254,255,254,255,255,255,255]],[[255,253,254,255,255,255,255,255,255,255,255],[236,253,254,255,255,255,255,255,255,255,255],[251,253,253,254,254,255,255,255,255,255,255]],[[255,254,254,255,255,255,255,255,255,255,255],[254,254,254,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]],[[255,254,255,255,255,255,255,255,255,255,255],[254,254,255,255,255,255,255,255,255,255,255],[254,255,255,255,255,255,255,255,255,255,255]],[[255,255,255,255,255,255,255,255,255,255,255],[254,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]],[[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]],[[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]],[[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]]],[[[248,255,255,255,255,255,255,255,255,255,255],[250,254,252,254,255,255,255,255,255,255,255],[248,254,249,253,255,255,255,255,255,255,255]],[[255,253,253,255,255,255,255,255,255,255,255],[246,253,253,255,255,255,255,255,255,255,255],[252,254,251,254,254,255,255,255,255,255,255]],[[255,254,252,255,255,255,255,255,255,255,255],[248,254,253,255,255,255,255,255,255,255,255],[253,255,254,254,255,255,255,255,255,255,255]],[[255,251,254,255,255,255,255,255,255,255,255],[245,251,254,255,255,255,255,255,255,255,255],[253,253,254,255,255,255,255,255,255,255,255]],[[255,251,253,255,255,255,255,255,255,255,255],[252,253,254,255,255,255,255,255,255,255,255],[255,254,255,255,255,255,255,255,255,255,255]],[[255,252,255,255,255,255,255,255,255,255,255],[249,255,254,255,255,255,255,255,255,255,255],[255,255,254,255,255,255,255,255,255,255,255]],[[255,255,253,255,255,255,255,255,255,255,255],[250,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]],[[255,255,255,255,255,255,255,255,255,255,255],[254,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]]]],eL=[0,1,2,3,6,4,5,6,6,6,6,6,6,6,6,7,0],ex=[],eA=[],eS=[],e_=1,eP=2,ek=[],eF=[];vr("UpsampleRgbLinePair",Ar,3),vr("UpsampleBgrLinePair",xr,3),vr("UpsampleRgbaLinePair",Ir,4),vr("UpsampleBgraLinePair",kr,4),vr("UpsampleArgbLinePair",Pr,4),vr("UpsampleRgba4444LinePair",_r,2),vr("UpsampleRgb565LinePair",Sr,2);var eI=tn.UpsampleRgbLinePair,eC=tn.UpsampleBgrLinePair,ej=tn.UpsampleRgbaLinePair,eO=tn.UpsampleBgraLinePair,eE=tn.UpsampleArgbLinePair,eB=tn.UpsampleRgba4444LinePair,eM=tn.UpsampleRgb565LinePair,eq=16,eD=32768,eR=-227,eT=482,eU=6,ez=16383,eV=0,eH=a(256),eW=a(256),eG=a(256),eJ=a(256),eY=a(eT-eR),eX=a(eT-eR);Fr("YuvToRgbRow",Ar,3),Fr("YuvToBgrRow",xr,3),Fr("YuvToRgbaRow",Ir,4),Fr("YuvToBgraRow",kr,4),Fr("YuvToArgbRow",Pr,4),Fr("YuvToRgba4444Row",_r,2),Fr("YuvToRgb565Row",Sr,2);var eZ=[0,4,8,12,128,132,136,140,256,260,264,268,384,388,392,396],eK=[0,2,8],e$=[8,7,6,4,4,2,2,2,1,1,1,1];this.WebPDecodeRGBA=function(tn,ti,ta,to,ts){var tc=t5,tu=new rr,th=new ot;tu.ba=th,th.S=tc,th.width=[th.width],th.height=[th.height];var tl=th.width,tf=th.height,td=new st;if(null==td||null==tn)var tp=2;else e(null!=td),tp=Br(tn,ti,ta,td.width,td.height,td.Pd,td.Qd,td.format,null);if(0!=tp?tl=0:(null!=tl&&(tl[0]=td.width[0]),null!=tf&&(tf[0]=td.height[0]),tl=1),tl){th.width=th.width[0],th.height=th.height[0],null!=to&&(to[0]=th.width),null!=ts&&(ts[0]=th.height);t:{if(to=new Gt,(ts=new nr).data=tn,ts.w=ti,ts.ha=ta,ts.kd=1,ti=[0],e(null!=ts),(0==(tn=Br(ts.data,ts.w,ts.ha,null,null,null,ti,null,ts))||7==tn)&&ti[0]&&(tn=4),0==(ti=tn)){if(e(null!=tu),to.data=ts.data,to.w=ts.w+ts.offset,to.ha=ts.ha-ts.offset,to.put=dt,to.ac=ft,to.bc=pt,to.ma=tu,ts.xa){if(null==(tn=kt())){tu=1;break t}if(function(tn,ti){for(var ta=[0],to=[0],ts=[0];;){if(null==tn)return 0;if(null==ti)return tn.a=2,0;if(tn.l=ti,tn.a=0,v(tn.m,ti.data,ti.w,ti.ha),!gt(tn.m,ta,to,ts)){tn.a=3;break}if(tn.xb=eP,ti.width=ta[0],ti.height=to[0],!It(ta[0],to[0],1,tn,null))break;return 1}return e(0!=tn.a),0}(tn,to)){if(to=0==(ti=qr(to.width,to.height,tu.Oa,tu.ba))){e:{for(to=tn;;){if(null==to){to=0;break e}if(e(null!=to.s.yc),e(null!=to.s.Ya),e(0<to.s.Wb),e(null!=(ta=to.l)),e(null!=(ts=ta.ma)),0!=to.xb){if(to.ca=ts.ba,to.tb=ts.tb,e(null!=to.ca),!Mr(ts.Oa,ta,t3)){to.a=2;break}if(!Ft(to,ta.width)||ta.da)break;if((ta.da||nt(to.ca.S))&&mr(),11>to.ca.S||(alert("todo:WebPInitConvertARGBToYUV"),null!=to.ca.f.kb.F&&mr()),to.Pb&&0<to.s.ua&&null==to.s.vb.X&&!O(to.s.vb,to.s.Wa.Xa)){to.a=1;break}to.xb=0}if(!_t(to,to.V,to.Ba,to.c,to.i,ta.o,Lt))break;ts.Dc=to.Ma,to=1;break e}e(0!=to.a),to=0}to=!to}to&&(ti=tn.a)}else ti=tn.a}else if((tn=new Yt).Fa=ts.na,tn.P=ts.P,tn.qc=ts.Sa,Kt(tn,to)){if(0==(ti=qr(to.width,to.height,tu.Oa,tu.ba))){if(tn.Aa=0,ta=tu.Oa,e(null!=(ts=tn)),null!=ta){if(0<(tl=0>(tl=ta.Md)?0:100<tl?255:255*tl/100)){for(tf=td=0;4>tf;++tf)12>(tp=ts.pb[tf]).lc&&(tp.ia=tl*e$[0>tp.lc?0:tp.lc]>>3),td|=tp.ia;td&&(alert("todo:VP8InitRandom"),ts.ia=1)}ts.Ga=ta.Id,100<ts.Ga?ts.Ga=100:0>ts.Ga&&(ts.Ga=0)}(function(tn,ti){if(null==tn)return 0;if(null==ti)return Jt(tn,2,"NULL VP8Io parameter in VP8Decode().");if(!tn.cb&&!Kt(tn,ti))return 0;if(e(tn.cb),null==ti.ac||ti.ac(ti)){ti.ob&&(tn.L=0);var ta=eK[tn.L];if(2==tn.L?(tn.yb=0,tn.zb=0):(tn.yb=ti.v-ta>>4,tn.zb=ti.j-ta>>4,0>tn.yb&&(tn.yb=0),0>tn.zb&&(tn.zb=0)),tn.Va=ti.o+15+ta>>4,tn.Hb=ti.va+15+ta>>4,tn.Hb>tn.za&&(tn.Hb=tn.za),tn.Va>tn.Ub&&(tn.Va=tn.Ub),0<tn.L){var to,ts=tn.ed;for(ta=0;4>ta;++ta){if(tn.Qa.Cb){var tc=tn.Qa.Lb[ta];tn.Qa.Fb||(tc+=ts.Tb)}else tc=ts.Tb;for(to=0;1>=to;++to){var tu=tn.gd[ta][to],th=tc;if(ts.Pc&&(th+=ts.vd[0],to&&(th+=ts.od[0])),0<(th=0>th?0:63<th?63:th)){var tl=th;0<ts.wb&&(tl=4<ts.wb?tl>>2:tl>>1)>9-ts.wb&&(tl=9-ts.wb),1>tl&&(tl=1),tu.dd=tl,tu.tc=2*th+tl,tu.ld=40<=th?2:15<=th?1:0}else tu.tc=0;tu.La=to}}}ta=0}else Jt(tn,6,"Frame setup failed"),ta=tn.a;if(ta=0==ta){if(ta){tn.$c=0,0<tn.Aa||(tn.Ic=1);t:{ta=tn.Ic,ts=4*(tl=tn.za);var tf=32*tl,td=tl+1,tp=0<tn.L?tl*(0<tn.Aa?2:1):0,tg=(2==tn.Aa?2:1)*tl;if((tu=ts+832+(to=3*(16*ta+eK[tn.L])/2*tf)+(tc=null!=tn.Fa&&0<tn.Fa.length?tn.Kc.c*tn.Kc.i:0))!=tu)ta=0;else{if(tu>tn.Vb){if(tn.Vb=0,tn.Ec=a(tu),tn.Fc=0,null==tn.Ec){ta=Jt(tn,1,"no memory during frame initialization.");break t}tn.Vb=tu}tu=tn.Ec,th=tn.Fc,tn.Ac=tu,tn.Bc=th,th+=ts,tn.Gd=o(tf,Ht),tn.Hd=0,tn.rb=o(td+1,Rt),tn.sb=1,tn.wa=tp?o(tp,Dt):null,tn.Y=0,tn.D.Nb=0,tn.D.wa=tn.wa,tn.D.Y=tn.Y,0<tn.Aa&&(tn.D.Y+=tl),e(!0),tn.oc=tu,tn.pc=th,th+=832,tn.ya=o(tg,Ut),tn.aa=0,tn.D.ya=tn.ya,tn.D.aa=tn.aa,2==tn.Aa&&(tn.D.aa+=tl),tn.R=16*tl,tn.B=8*tl,tl=(tf=eK[tn.L])*tn.R,tf=tf/2*tn.B,tn.sa=tu,tn.ta=th+tl,tn.qa=tn.sa,tn.ra=tn.ta+16*ta*tn.R+tf,tn.Ha=tn.qa,tn.Ia=tn.ra+8*ta*tn.B+tf,tn.$c=0,th+=to,tn.mb=tc?tu:null,tn.nb=tc?th:null,e(th+tc<=tn.Fc+tn.Vb),$t(tn),i(tn.Ac,tn.Bc,0,ts),ta=1}}if(ta){if(ti.ka=0,ti.y=tn.sa,ti.O=tn.ta,ti.f=tn.qa,ti.N=tn.ra,ti.ea=tn.Ha,ti.Vd=tn.Ia,ti.fa=tn.R,ti.Rc=tn.B,ti.F=null,ti.J=0,!tZ){for(ta=-255;255>=ta;++ta)tG[255+ta]=0>ta?-ta:ta;for(ta=-1020;1020>=ta;++ta)tJ[1020+ta]=-128>ta?-128:127<ta?127:ta;for(ta=-112;112>=ta;++ta)tY[112+ta]=-16>ta?-16:15<ta?15:ta;for(ta=-255;510>=ta;++ta)tX[255+ta]=0>ta?0:255<ta?255:ta;tZ=1}tA=ue,tS=ae,tP=oe,tk=se,tF=ce,t_=ie,tI=Je,tC=Xe,tj=$e,tO=Qe,tE=Ke,tB=Ze,tM=tr,tq=er,tD=ze,tR=He,tT=We,tU=Ve,eA[0]=xe,eA[1]=le,eA[2]=Le,eA[3]=Ae,eA[4]=Se,eA[5]=Pe,eA[6]=_e,eA[7]=ke,eA[8]=Fe,eA[9]=Ie,ex[0]=ve,ex[1]=de,ex[2]=pe,ex[3]=ge,ex[4]=be,ex[5]=ye,ex[6]=we,eS[0]=Be,eS[1]=fe,eS[2]=Ce,eS[3]=je,eS[4]=Ee,eS[5]=Me,eS[6]=qe,ta=1}else ta=0}ta&&(ta=function(tn,ti){for(tn.M=0;tn.M<tn.Va;++tn.M){var ta,to=tn.Jc[tn.M&tn.Xb],ts=tn.m,tc=tn;for(ta=0;ta<tc.za;++ta){var tu=ts,th=tc,tl=th.Ac,tf=th.Bc+4*ta,td=th.zc,tp=th.ya[th.aa+ta];if(th.Qa.Bb?tp.$b=k(tu,th.Pa.jb[0])?2+k(tu,th.Pa.jb[2]):k(tu,th.Pa.jb[1]):tp.$b=0,th.kc&&(tp.Ad=k(tu,th.Bd)),tp.Za=!k(tu,145)+0,tp.Za){var tg=tp.Ob,tm=0;for(th=0;4>th;++th){var tv,tb=td[0+th];for(tv=0;4>tv;++tv){tb=ew[tl[tf+tv]][tb];for(var ty=eb[k(tu,tb[0])];0<ty;)ty=eb[2*ty+k(tu,tb[ty])];tb=-ty,tl[tf+tv]=tb}n(tg,tm,tl,tf,4),tm+=4,td[0+th]=tb}}else tb=k(tu,156)?k(tu,128)?1:3:k(tu,163)?2:0,tp.Ob[0]=tb,i(tl,tf,tb,4),i(td,0,tb,4);tp.Dd=k(tu,142)?k(tu,114)?k(tu,183)?1:3:2:0}if(tc.m.Ka)return Jt(tn,7,"Premature end-of-partition0 encountered.");for(;tn.ja<tn.za;++tn.ja){if(tc=to,tu=(ts=tn).rb[ts.sb-1],tl=ts.rb[ts.sb+ts.ja],ta=ts.ya[ts.aa+ts.ja],tf=ts.kc?ta.Ad:0)tu.la=tl.la=0,ta.Za||(tu.Na=tl.Na=0),ta.Hc=0,ta.Gc=0,ta.ia=0;else{if(tu=tl,tl=tc,tf=ts.Pa.Xc,td=ts.ya[ts.aa+ts.ja],tp=ts.pb[td.$b],th=td.ad,tg=0,tm=ts.rb[ts.sb-1],tb=tv=0,i(th,tg,0,384),td.Za)var tw,tN,tL=0,tx=tf[3];else{ty=a(16);var tS=tu.Na+tm.Na;if(tS=eg(tl,tf[1],tS,tp.Eb,0,ty,0),tu.Na=tm.Na=(0<tS)+0,1<tS)tA(ty,0,th,tg);else{var t_=ty[0]+3>>3;for(ty=0;256>ty;ty+=16)th[tg+ty]=t_}tL=1,tx=tf[0]}var tP=15&tu.la,tk=15&tm.la;for(ty=0;4>ty;++ty){var tF=1&tk;for(t_=tN=0;4>t_;++t_)tP=tP>>1|(tF=(tS=eg(tl,tx,tS=tF+(1&tP),tp.Sc,tL,th,tg))>tL)<<7,tN=tN<<2|(3<tS?3:1<tS?2:0!=th[tg+0]),tg+=16;tP>>=4,tk=tk>>1|tF<<7,tv=(tv<<8|tN)>>>0}for(tx=tP,tL=tk>>4,tw=0;4>tw;tw+=2){for(tN=0,tP=tu.la>>4+tw,tk=tm.la>>4+tw,ty=0;2>ty;++ty){for(tF=1&tk,t_=0;2>t_;++t_)tS=tF+(1&tP),tP=tP>>1|(tF=0<(tS=eg(tl,tf[2],tS,tp.Qc,0,th,tg)))<<3,tN=tN<<2|(3<tS?3:1<tS?2:0!=th[tg+0]),tg+=16;tP>>=2,tk=tk>>1|tF<<5}tb|=tN<<4*tw,tx|=tP<<4<<tw,tL|=(240&tk)<<tw}tu.la=tx,tm.la=tL,td.Hc=tv,td.Gc=tb,td.ia=43690&tb?0:tp.ia,tf=!(tv|tb)}if(0<ts.L&&(ts.wa[ts.Y+ts.ja]=ts.gd[ta.$b][ta.Za],ts.wa[ts.Y+ts.ja].La|=!tf),tc.Ka)return Jt(tn,7,"Premature end-of-file encountered.")}if($t(tn),ts=ti,tc=1,ta=(to=tn).D,tu=0<to.L&&to.M>=to.zb&&to.M<=to.Va,0==to.Aa)t:{if(ta.M=to.M,ta.uc=tu,Or(to,ta),tc=1,ta=(tN=to.D).Nb,tu=(tb=eK[to.L])*to.R,tl=tb/2*to.B,ty=16*ta*to.R,t_=8*ta*to.B,tf=to.sa,td=to.ta-tu+ty,tp=to.qa,th=to.ra-tl+t_,tg=to.Ha,tm=to.Ia-tl+t_,tk=0==(tP=tN.M),tv=tP>=to.Va-1,2==to.Aa&&Or(to,tN),tN.uc)for(tF=(tS=to).D.M,e(tS.D.uc),tN=tS.yb;tN<tS.Hb;++tN){tL=tN,tx=tF;var tz=(tV=(t$=tS).D).Nb;tw=t$.R;var tV=tV.wa[tV.Y+tL],tH=t$.sa,tW=t$.ta+16*tz*tw+16*tL,tG=tV.dd,tJ=tV.tc;if(0!=tJ){if(e(3<=tJ),1==t$.L)0<tL&&tR(tH,tW,tw,tJ+4),tV.La&&tU(tH,tW,tw,tJ),0<tx&&tD(tH,tW,tw,tJ+4),tV.La&&tT(tH,tW,tw,tJ);else{var tY=t$.B,tX=t$.qa,tZ=t$.ra+8*tz*tY+8*tL,tK=t$.Ha,t$=t$.Ia+8*tz*tY+8*tL;tz=tV.ld,0<tL&&(tC(tH,tW,tw,tJ+4,tG,tz),tO(tX,tZ,tK,t$,tY,tJ+4,tG,tz)),tV.La&&(tB(tH,tW,tw,tJ,tG,tz),tq(tX,tZ,tK,t$,tY,tJ,tG,tz)),0<tx&&(tI(tH,tW,tw,tJ+4,tG,tz),tj(tX,tZ,tK,t$,tY,tJ+4,tG,tz)),tV.La&&(tE(tH,tW,tw,tJ,tG,tz),tM(tX,tZ,tK,t$,tY,tJ,tG,tz))}}}if(to.ia&&alert("todo:DitherRow"),null!=ts.put){if(tN=16*tP,tP=16*(tP+1),tk?(ts.y=to.sa,ts.O=to.ta+ty,ts.f=to.qa,ts.N=to.ra+t_,ts.ea=to.Ha,ts.W=to.Ia+t_):(tN-=tb,ts.y=tf,ts.O=td,ts.f=tp,ts.N=th,ts.ea=tg,ts.W=tm),tv||(tP-=tb),tP>ts.o&&(tP=ts.o),ts.F=null,ts.J=null,null!=to.Fa&&0<to.Fa.length&&tN<tP&&(ts.J=function(tn,ti,ta,to){var ts=ti.width,tc=ti.o;if(e(null!=tn&&null!=ti),0>ta||0>=to||ta+to>tc)return null;if(!tn.Cc){if(null==tn.ga){if(tn.ga=new sr,(tx=null==tn.ga)||(tx=ti.width*ti.o,e(0==tn.Gb.length),tn.Gb=a(tx),tn.Uc=0,null==tn.Gb?tx=0:(tn.mb=tn.Gb,tn.nb=tn.Uc,tn.rc=null,tx=1),tx=!tx),!tx){tx=tn.ga;var tu=tn.Fa,th=tn.P,tl=tn.qc,tf=tn.mb,td=tn.nb,tp=th+1,tg=tl-1,tm=tx.l;if(e(null!=tu&&null!=tf&&null!=ti),ek[0]=null,ek[1]=cr,ek[2]=ur,ek[3]=hr,tx.ca=tf,tx.tb=td,tx.c=ti.width,tx.i=ti.height,e(0<tx.c&&0<tx.i),1>=tl)ti=0;else if(tx.$a=tu[th+0]>>0&3,tx.Z=tu[th+0]>>2&3,tx.Lc=tu[th+0]>>4&3,th=tu[th+0]>>6&3,0>tx.$a||1<tx.$a||4<=tx.Z||1<tx.Lc||th)ti=0;else if(tm.put=dt,tm.ac=ft,tm.bc=pt,tm.ma=tx,tm.width=ti.width,tm.height=ti.height,tm.Da=ti.Da,tm.v=ti.v,tm.va=ti.va,tm.j=ti.j,tm.o=ti.o,tx.$a)t:{for(e(1==tx.$a),ti=kt();;){if(null==ti){ti=0;break t}if(e(null!=tx),tx.mc=ti,ti.c=tx.c,ti.i=tx.i,ti.l=tx.l,ti.l.ma=tx,ti.l.width=tx.c,ti.l.height=tx.i,ti.a=0,v(ti.m,tu,tp,tg),!It(tx.c,tx.i,1,ti,null)||(1==ti.ab&&3==ti.gc[0].hc&&At(ti.s)?(tx.ic=1,tu=ti.c*ti.i,ti.Ta=null,ti.Ua=0,ti.V=a(tu),ti.Ba=0,null==ti.V?(ti.a=1,ti=0):ti=1):(tx.ic=0,ti=Ft(ti,tx.c)),!ti))break;ti=1;break t}tx.mc=null,ti=0}else ti=tg>=tx.c*tx.i;tx=!ti}if(tx)return null;1!=tn.ga.Lc?tn.Ga=0:to=tc-ta}e(null!=tn.ga),e(ta+to<=tc);t:{if(ti=(tu=tn.ga).c,tc=tu.l.o,0==tu.$a){if(tp=tn.rc,tg=tn.Vc,tm=tn.Fa,th=tn.P+1+ta*ti,tl=tn.mb,tf=tn.nb+ta*ti,e(th<=tn.P+tn.qc),0!=tu.Z)for(e(null!=ek[tu.Z]),tx=0;tx<to;++tx)ek[tu.Z](tp,tg,tm,th,tl,tf,ti),tp=tl,tg=tf,tf+=ti,th+=ti;else for(tx=0;tx<to;++tx)n(tl,tf,tm,th,ti),tp=tl,tg=tf,tf+=ti,th+=ti;tn.rc=tp,tn.Vc=tg}else{if(e(null!=tu.mc),ti=ta+to,e(null!=(tx=tu.mc)),e(ti<=tx.i),tx.C>=ti)ti=1;else if(tu.ic||mr(),tu.ic){tu=tx.V,tp=tx.Ba,tg=tx.c;var tv=tx.i,tb=(tm=1,th=tx.$/tg,tl=tx.$%tg,tf=tx.m,td=tx.s,tx.$),ty=tg*tv,tw=tg*ti,tN=td.wc,tL=tb<tw?wt(td,tl,th):null;e(tb<=ty),e(ti<=tv),e(At(td));e:for(;;){for(;!tf.h&&tb<tw;){if(tl&tN||(tL=wt(td,tl,th)),e(null!=tL),S(tf),256>(tv=bt(tL.G[0],tL.H[0],tf)))tu[tp+tb]=tv,++tb,++tl>=tg&&(tl=0,++th<=ti&&!(th%16)&&St(tx,th));else{if(!(280>tv)){tm=0;break e}tv=mt(tv-256,tf);var tx,tA,tS=bt(tL.G[4],tL.H[4],tf);if(S(tf),!(tb>=(tS=vt(tg,tS=mt(tS,tf)))&&ty-tb>=tv)){tm=0;break e}for(tA=0;tA<tv;++tA)tu[tp+tb+tA]=tu[tp+tb+tA-tS];for(tb+=tv,tl+=tv;tl>=tg;)tl-=tg,++th<=ti&&!(th%16)&&St(tx,th);tb<tw&&tl&tN&&(tL=wt(td,tl,th))}e(tf.h==A(tf))}St(tx,th>ti?ti:th);break}!tm||tf.h&&tb<ty?(tm=0,tx.a=tf.h?5:3):tx.$=tb,ti=tm}else ti=_t(tx,tx.V,tx.Ba,tx.c,tx.i,ti,Ct);if(!ti){to=0;break t}}ta+to>=tc&&(tn.Cc=1),to=1}if(!to)return null;if(tn.Cc&&(null!=(to=tn.ga)&&(to.mc=null),tn.ga=null,0<tn.Ga))return alert("todo:WebPDequantizeLevels"),null}return tn.nb+ta*ts}(to,ts,tN,tP-tN),ts.F=to.mb,null==ts.F&&0==ts.F.length)){tc=Jt(to,3,"Could not decode alpha data.");break t}tN<ts.j&&(tb=ts.j-tN,tN=ts.j,e(!(1&tb)),ts.O+=to.R*tb,ts.N+=to.B*(tb>>1),ts.W+=to.B*(tb>>1),null!=ts.F&&(ts.J+=ts.width*tb)),tN<tP&&(ts.O+=ts.v,ts.N+=ts.v>>1,ts.W+=ts.v>>1,null!=ts.F&&(ts.J+=ts.v),ts.ka=tN-ts.j,ts.U=ts.va-ts.v,ts.T=tP-tN,tc=ts.put(ts))}ta+1!=to.Ic||tv||(n(to.sa,to.ta-tu,tf,td+16*to.R,tu),n(to.qa,to.ra-tl,tp,th+8*to.B,tl),n(to.Ha,to.Ia-tl,tg,tm+8*to.B,tl))}if(!tc)return Jt(tn,6,"Output aborted.")}return 1}(tn,ti)),null!=ti.bc&&ti.bc(ti),ta&=1}return ta?(tn.cb=0,ta):0})(tn,to)||(ti=tn.a)}}else ti=tn.a;0==ti&&null!=tu.Oa&&tu.Oa.fd&&(ti=Er(tu.ba))}tu=ti}tc=0!=tu?null:11>tc?th.f.RGBA.eb:th.f.kb.y}else tc=null;return tc};var eQ=[3,4,3,4,4,2,2,4,4,4,2,1,1]};function h(tn,ti){return(tn[ti+0]<<0|tn[ti+1]<<8|tn[ti+2]<<16)>>>0}function l(tn,ti){return(tn[ti+0]<<0|tn[ti+1]<<8|tn[ti+2]<<16|tn[ti+3]<<24)>>>0}new c;var ti=[0],ta=[0],to=[],ts=new c,tc=function(tn,ti){var ta={},to=0,ts=!1,tc=0,tu=0;if(ta.frames=[],!/** @license
   * Copyright (c) 2017 Dominik Homberger
  Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the "Software"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:
  The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.
  THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
  https://webpjs.appspot.com
  WebPRiffParser <EMAIL>
  */function(tn,ti,ta,to){for(var ts=0;ts<4;ts++)if(tn[ti+ts]!=ta.charCodeAt(ts))return!0;return!1}(tn,ti,"RIFF",0)){for(l(tn,ti+=4),ti+=8;ti<tn.length;){var th,tl,tf,td=function(tn,ti){for(var ta="",to=0;to<4;to++)ta+=String.fromCharCode(tn[ti++]);return ta}(tn,ti),tp=l(tn,ti+=4);ti+=4;var tg=tp+(1&tp);switch(td){case"VP8 ":case"VP8L":void 0===ta.frames[to]&&(ta.frames[to]={}),(tf=ta.frames[to]).src_off=ts?tu:ti-8,tf.src_size=tc+tp+8,to++,ts&&(ts=!1,tc=0,tu=0);break;case"VP8X":(tf=ta.header={}).feature_flags=tn[ti];var tm=ti+4;tf.canvas_width=1+h(tn,tm),tm+=3,tf.canvas_height=1+h(tn,tm),tm+=3;break;case"ALPH":ts=!0,tc=tg+8,tu=ti-8;break;case"ANIM":(tf=ta.header).bgcolor=l(tn,ti),tm=ti+4,tf.loop_count=tn[(th=tm)+0]<<0|tn[th+1]<<8,tm+=2;break;case"ANMF":(tf=ta.frames[to]={}).offset_x=2*h(tn,ti),ti+=3,tf.offset_y=2*h(tn,ti),ti+=3,tf.width=1+h(tn,ti),ti+=3,tf.height=1+h(tn,ti),ti+=3,tf.duration=h(tn,ti),ti+=3,tl=tn[ti++],tf.dispose=1&tl,tf.blend=tl>>1&1}"ANMF"!=td&&(ti+=tg)}return ta}}(tn,0);tc.response=tn,tc.rgbaoutput=!0,tc.dataurl=!1;var tu=tc.header?tc.header:null,th=tc.frames?tc.frames:null;if(tu){tu.loop_counter=tu.loop_count,ti=[tu.canvas_height],ta=[tu.canvas_width];for(var tl=0;tl<th.length&&0!=th[tl].blend;tl++);}var tf=th[0],td=ts.WebPDecodeRGBA(tn,tf.src_off,tf.src_size,ta,ti);tf.rgba=td,tf.imgwidth=ta[0],tf.imgheight=ti[0];for(var tp=0;tp<ta[0]*ti[0]*4;tp++)to[tp]=td[tp];return this.width=ta,this.height=ti,this.data=to,this}eB=E.API,eM=function(tn,ti,ta,to){var tc=4,tu=eT;switch(to){case eB.image_compression.FAST:tc=1,tu=eR;break;case eB.image_compression.MEDIUM:tc=6,tu=eU;break;case eB.image_compression.SLOW:tc=9,tu=ez}tn=eq(tn,ti,ta,tu);var th=(0,ts.iZ)(tn,{level:tc});return eB.__addimage__.arrayBufferToBinaryString(th)},eq=function(tn,ti,ta,to){for(var ts,tc,tu,th=tn.length/ti,tl=new Uint8Array(tn.length+th),tf=eH(),td=0;td<th;td+=1){if(tu=td*ti,ts=tn.subarray(tu,tu+ti),to)tl.set(to(ts,ta,tc),tu+td);else{for(var tp,tg=tf.length,tm=[];tp<tg;tp+=1)tm[tp]=tf[tp](ts,ta,tc);var tv=eW(tm.concat());tl.set(tm[tv],tu+td)}tc=ts}return tl},eD=function(tn){var ti=Array.apply([],tn);return ti.unshift(0),ti},eR=function(tn,ti){var ta,to=[],ts=tn.length;to[0]=1;for(var tc=0;tc<ts;tc+=1)ta=tn[tc-ti]||0,to[tc+1]=tn[tc]-ta+256&255;return to},eT=function(tn,ti,ta){var to,ts=[],tc=tn.length;ts[0]=2;for(var tu=0;tu<tc;tu+=1)to=ta&&ta[tu]||0,ts[tu+1]=tn[tu]-to+256&255;return ts},eU=function(tn,ti,ta){var to,ts,tc=[],tu=tn.length;tc[0]=3;for(var th=0;th<tu;th+=1)to=tn[th-ti]||0,ts=ta&&ta[th]||0,tc[th+1]=tn[th]+256-(to+ts>>>1)&255;return tc},ez=function(tn,ti,ta){var to,ts,tc,tu=[],th=tn.length;tu[0]=4;for(var tl=0;tl<th;tl+=1)to=tn[tl-ti]||0,ts=ta&&ta[tl]||0,tc=eV(to,ts,ta&&ta[tl-ti]||0),tu[tl+1]=tn[tl]-tc+256&255;return tu},eV=function(tn,ti,ta){if(tn===ti&&ti===ta)return tn;var to=Math.abs(ti-ta),ts=Math.abs(tn-ta),tc=Math.abs(tn+ti-ta-ta);return to<=ts&&to<=tc?tn:ts<=tc?ti:ta},eH=function(){return[eD,eR,eT,eU,ez]},eW=function(tn){var ti=tn.map(function(tn){return tn.reduce(function(tn,ti){return tn+Math.abs(ti)},0)});return ti.indexOf(Math.min.apply(null,ti))},eB.processPNG=function(tn,ti,ta,to){var tc,tu,th,tl,tf,td,tp,tg,tm,tv,tb,ty,tw,tN,tL,tx=this.decode.FLATE_DECODE,tA="";if(this.__addimage__.isArrayBuffer(tn)&&(tn=new Uint8Array(tn)),this.__addimage__.isArrayBufferView(tn)){if(tn=(th=new eE(tn)).imgData,tu=th.bits,tc=th.colorSpace,tf=th.colors,-1!==[4,6].indexOf(th.colorType)){if(8===th.bits){tm=(tg=32==th.pixelBitlength?new Uint32Array(th.decodePixels().buffer):16==th.pixelBitlength?new Uint16Array(th.decodePixels().buffer):new Uint8Array(th.decodePixels().buffer)).length,tb=new Uint8Array(tm*th.colors),tv=new Uint8Array(tm);var tS,t_=th.pixelBitlength-th.bits;for(tN=0,tL=0;tN<tm;tN++){for(tw=tg[tN],tS=0;tS<t_;)tb[tL++]=tw>>>tS&255,tS+=th.bits;tv[tN]=tw>>>tS&255}}if(16===th.bits){tm=(tg=new Uint32Array(th.decodePixels().buffer)).length,tb=new Uint8Array(tm*(32/th.pixelBitlength)*th.colors),tv=new Uint8Array(tm*(32/th.pixelBitlength)),ty=th.colors>1,tN=0,tL=0;for(var tP=0;tN<tm;)tw=tg[tN++],tb[tL++]=tw>>>0&255,ty&&(tb[tL++]=tw>>>16&255,tw=tg[tN++],tb[tL++]=tw>>>0&255),tv[tP++]=tw>>>16&255;tu=8}to!==eB.image_compression.NONE&&"function"==typeof ts.iZ?(tn=eM(tb,th.width*th.colors,th.colors,to),tp=eM(tv,th.width,1,to)):(tn=tb,tp=tv,tx=void 0)}if(3===th.colorType&&(tc=this.color_spaces.INDEXED,td=th.palette,th.transparency.indexed)){var tk=th.transparency.indexed,tF=0;for(tN=0,tm=tk.length;tN<tm;++tN)tF+=tk[tN];if((tF/=255)==tm-1&&-1!==tk.indexOf(0))tl=[tk.indexOf(0)];else if(tF!==tm){for(tg=th.decodePixels(),tv=new Uint8Array(tg.length),tN=0,tm=tg.length;tN<tm;tN++)tv[tN]=tk[tg[tN]];tp=eM(tv,th.width,1)}}var tI=function(tn){var ti;switch(tn){case eB.image_compression.FAST:ti=11;break;case eB.image_compression.MEDIUM:ti=13;break;case eB.image_compression.SLOW:ti=14;break;default:ti=12}return ti}(to);return tx===this.decode.FLATE_DECODE&&(tA="/Predictor "+tI+" "),tA+="/Colors "+tf+" /BitsPerComponent "+tu+" /Columns "+th.width,(this.__addimage__.isArrayBuffer(tn)||this.__addimage__.isArrayBufferView(tn))&&(tn=this.__addimage__.arrayBufferToBinaryString(tn)),(tp&&this.__addimage__.isArrayBuffer(tp)||this.__addimage__.isArrayBufferView(tp))&&(tp=this.__addimage__.arrayBufferToBinaryString(tp)),{alias:ta,data:tn,index:ti,filter:tx,decodeParameters:tA,transparency:tl,palette:td,sMask:tp,predictor:tI,width:th.width,height:th.height,bitsPerComponent:tu,colorSpace:tc}}},(eG=E.API).processGIF89A=function(tn,ti,ta,to){var ts=new Zt(tn),tc=ts.width,tu=ts.height,th=[];ts.decodeAndBlitFrameRGBA(0,th);var tl=new Qt(100).encode({data:th,width:tc,height:tu},100);return eG.processJPEG.call(this,tl,ti,ta,to)},eG.processGIF87A=eG.processGIF89A,te.prototype.parseHeader=function(){if(this.fileSize=this.datav.getUint32(this.pos,!0),this.pos+=4,this.reserved=this.datav.getUint32(this.pos,!0),this.pos+=4,this.offset=this.datav.getUint32(this.pos,!0),this.pos+=4,this.headerSize=this.datav.getUint32(this.pos,!0),this.pos+=4,this.width=this.datav.getUint32(this.pos,!0),this.pos+=4,this.height=this.datav.getInt32(this.pos,!0),this.pos+=4,this.planes=this.datav.getUint16(this.pos,!0),this.pos+=2,this.bitPP=this.datav.getUint16(this.pos,!0),this.pos+=2,this.compress=this.datav.getUint32(this.pos,!0),this.pos+=4,this.rawSize=this.datav.getUint32(this.pos,!0),this.pos+=4,this.hr=this.datav.getUint32(this.pos,!0),this.pos+=4,this.vr=this.datav.getUint32(this.pos,!0),this.pos+=4,this.colors=this.datav.getUint32(this.pos,!0),this.pos+=4,this.importantColors=this.datav.getUint32(this.pos,!0),this.pos+=4,16===this.bitPP&&this.is_with_alpha&&(this.bitPP=15),this.bitPP<15){var tn=0===this.colors?1<<this.bitPP:this.colors;this.palette=Array(tn);for(var ti=0;ti<tn;ti++){var ta=this.datav.getUint8(this.pos++,!0),to=this.datav.getUint8(this.pos++,!0),ts=this.datav.getUint8(this.pos++,!0),tc=this.datav.getUint8(this.pos++,!0);this.palette[ti]={red:ts,green:to,blue:ta,quad:tc}}}this.height<0&&(this.height*=-1,this.bottom_up=!1)},te.prototype.parseBGR=function(){this.pos=this.offset;try{var tn="bit"+this.bitPP,ti=this.width*this.height*4;this.data=new Uint8Array(ti),this[tn]()}catch(tn){tu.log("bit decode error:"+tn)}},te.prototype.bit1=function(){var tn,ti=Math.ceil(this.width/8),ta=ti%4;for(tn=this.height-1;tn>=0;tn--){for(var to=this.bottom_up?tn:this.height-1-tn,ts=0;ts<ti;ts++)for(var tc=this.datav.getUint8(this.pos++,!0),tu=to*this.width*4+8*ts*4,th=0;th<8&&8*ts+th<this.width;th++){var tl=this.palette[tc>>7-th&1];this.data[tu+4*th]=tl.blue,this.data[tu+4*th+1]=tl.green,this.data[tu+4*th+2]=tl.red,this.data[tu+4*th+3]=255}0!==ta&&(this.pos+=4-ta)}},te.prototype.bit4=function(){for(var tn=Math.ceil(this.width/2),ti=tn%4,ta=this.height-1;ta>=0;ta--){for(var to=this.bottom_up?ta:this.height-1-ta,ts=0;ts<tn;ts++){var tc=this.datav.getUint8(this.pos++,!0),tu=to*this.width*4+2*ts*4,th=tc>>4,tl=15&tc,tf=this.palette[th];if(this.data[tu]=tf.blue,this.data[tu+1]=tf.green,this.data[tu+2]=tf.red,this.data[tu+3]=255,2*ts+1>=this.width)break;tf=this.palette[tl],this.data[tu+4]=tf.blue,this.data[tu+4+1]=tf.green,this.data[tu+4+2]=tf.red,this.data[tu+4+3]=255}0!==ti&&(this.pos+=4-ti)}},te.prototype.bit8=function(){for(var tn=this.width%4,ti=this.height-1;ti>=0;ti--){for(var ta=this.bottom_up?ti:this.height-1-ti,to=0;to<this.width;to++){var ts=this.datav.getUint8(this.pos++,!0),tc=ta*this.width*4+4*to;if(ts<this.palette.length){var tu=this.palette[ts];this.data[tc]=tu.red,this.data[tc+1]=tu.green,this.data[tc+2]=tu.blue,this.data[tc+3]=255}else this.data[tc]=255,this.data[tc+1]=255,this.data[tc+2]=255,this.data[tc+3]=255}0!==tn&&(this.pos+=4-tn)}},te.prototype.bit15=function(){for(var tn=this.width%3,ti=parseInt("11111",2),ta=this.height-1;ta>=0;ta--){for(var to=this.bottom_up?ta:this.height-1-ta,ts=0;ts<this.width;ts++){var tc=this.datav.getUint16(this.pos,!0);this.pos+=2;var tu=(tc&ti)/ti*255|0,th=(tc>>5&ti)/ti*255|0,tl=(tc>>10&ti)/ti*255|0,tf=tc>>15?255:0,td=to*this.width*4+4*ts;this.data[td]=tl,this.data[td+1]=th,this.data[td+2]=tu,this.data[td+3]=tf}this.pos+=tn}},te.prototype.bit16=function(){for(var tn=this.width%3,ti=parseInt("11111",2),ta=parseInt("111111",2),to=this.height-1;to>=0;to--){for(var ts=this.bottom_up?to:this.height-1-to,tc=0;tc<this.width;tc++){var tu=this.datav.getUint16(this.pos,!0);this.pos+=2;var th=(tu&ti)/ti*255|0,tl=(tu>>5&ta)/ta*255|0,tf=(tu>>11)/ti*255|0,td=ts*this.width*4+4*tc;this.data[td]=tf,this.data[td+1]=tl,this.data[td+2]=th,this.data[td+3]=255}this.pos+=tn}},te.prototype.bit24=function(){for(var tn=this.height-1;tn>=0;tn--){for(var ti=this.bottom_up?tn:this.height-1-tn,ta=0;ta<this.width;ta++){var to=this.datav.getUint8(this.pos++,!0),ts=this.datav.getUint8(this.pos++,!0),tc=this.datav.getUint8(this.pos++,!0),tu=ti*this.width*4+4*ta;this.data[tu]=tc,this.data[tu+1]=ts,this.data[tu+2]=to,this.data[tu+3]=255}this.pos+=this.width%4}},te.prototype.bit32=function(){for(var tn=this.height-1;tn>=0;tn--)for(var ti=this.bottom_up?tn:this.height-1-tn,ta=0;ta<this.width;ta++){var to=this.datav.getUint8(this.pos++,!0),ts=this.datav.getUint8(this.pos++,!0),tc=this.datav.getUint8(this.pos++,!0),tu=this.datav.getUint8(this.pos++,!0),th=ti*this.width*4+4*ta;this.data[th]=tc,this.data[th+1]=ts,this.data[th+2]=to,this.data[th+3]=tu}},te.prototype.getData=function(){return this.data},(eJ=E.API).processBMP=function(tn,ti,ta,to){var ts=new te(tn,!1),tc=ts.width,tu=ts.height,th={data:ts.getData(),width:tc,height:tu},tl=new Qt(100).encode(th,100);return eJ.processJPEG.call(this,tl,ti,ta,to)},ee.prototype.getData=function(){return this.data},(eY=E.API).processWEBP=function(tn,ti,ta,to){var ts=new ee(tn,!1),tc=ts.width,tu=ts.height,th={data:ts.getData(),width:tc,height:tu},tl=new Qt(100).encode(th,100);return eY.processJPEG.call(this,tl,ti,ta,to)},E.API.processRGBA=function(tn,ti,ta){for(var to=tn.data,ts=to.length,tc=new Uint8Array(ts/4*3),tu=new Uint8Array(ts/4),th=0,tl=0,tf=0;tf<ts;tf+=4){var td=to[tf],tp=to[tf+1],tg=to[tf+2],tm=to[tf+3];tc[th++]=td,tc[th++]=tp,tc[th++]=tg,tu[tl++]=tm}var tv=this.__addimage__.arrayBufferToBinaryString(tc);return{alpha:this.__addimage__.arrayBufferToBinaryString(tu),data:tv,index:ti,alias:ta,colorSpace:"DeviceRGB",bitsPerComponent:8,width:tn.width,height:tn.height}},E.API.setLanguage=function(tn){return void 0===this.internal.languageSettings&&(this.internal.languageSettings={},this.internal.languageSettings.isSubscribed=!1),void 0!==({af:"Afrikaans",sq:"Albanian",ar:"Arabic (Standard)","ar-DZ":"Arabic (Algeria)","ar-BH":"Arabic (Bahrain)","ar-EG":"Arabic (Egypt)","ar-IQ":"Arabic (Iraq)","ar-JO":"Arabic (Jordan)","ar-KW":"Arabic (Kuwait)","ar-LB":"Arabic (Lebanon)","ar-LY":"Arabic (Libya)","ar-MA":"Arabic (Morocco)","ar-OM":"Arabic (Oman)","ar-QA":"Arabic (Qatar)","ar-SA":"Arabic (Saudi Arabia)","ar-SY":"Arabic (Syria)","ar-TN":"Arabic (Tunisia)","ar-AE":"Arabic (U.A.E.)","ar-YE":"Arabic (Yemen)",an:"Aragonese",hy:"Armenian",as:"Assamese",ast:"Asturian",az:"Azerbaijani",eu:"Basque",be:"Belarusian",bn:"Bengali",bs:"Bosnian",br:"Breton",bg:"Bulgarian",my:"Burmese",ca:"Catalan",ch:"Chamorro",ce:"Chechen",zh:"Chinese","zh-HK":"Chinese (Hong Kong)","zh-CN":"Chinese (PRC)","zh-SG":"Chinese (Singapore)","zh-TW":"Chinese (Taiwan)",cv:"Chuvash",co:"Corsican",cr:"Cree",hr:"Croatian",cs:"Czech",da:"Danish",nl:"Dutch (Standard)","nl-BE":"Dutch (Belgian)",en:"English","en-AU":"English (Australia)","en-BZ":"English (Belize)","en-CA":"English (Canada)","en-IE":"English (Ireland)","en-JM":"English (Jamaica)","en-NZ":"English (New Zealand)","en-PH":"English (Philippines)","en-ZA":"English (South Africa)","en-TT":"English (Trinidad & Tobago)","en-GB":"English (United Kingdom)","en-US":"English (United States)","en-ZW":"English (Zimbabwe)",eo:"Esperanto",et:"Estonian",fo:"Faeroese",fj:"Fijian",fi:"Finnish",fr:"French (Standard)","fr-BE":"French (Belgium)","fr-CA":"French (Canada)","fr-FR":"French (France)","fr-LU":"French (Luxembourg)","fr-MC":"French (Monaco)","fr-CH":"French (Switzerland)",fy:"Frisian",fur:"Friulian",gd:"Gaelic (Scots)","gd-IE":"Gaelic (Irish)",gl:"Galacian",ka:"Georgian",de:"German (Standard)","de-AT":"German (Austria)","de-DE":"German (Germany)","de-LI":"German (Liechtenstein)","de-LU":"German (Luxembourg)","de-CH":"German (Switzerland)",el:"Greek",gu:"Gujurati",ht:"Haitian",he:"Hebrew",hi:"Hindi",hu:"Hungarian",is:"Icelandic",id:"Indonesian",iu:"Inuktitut",ga:"Irish",it:"Italian (Standard)","it-CH":"Italian (Switzerland)",ja:"Japanese",kn:"Kannada",ks:"Kashmiri",kk:"Kazakh",km:"Khmer",ky:"Kirghiz",tlh:"Klingon",ko:"Korean","ko-KP":"Korean (North Korea)","ko-KR":"Korean (South Korea)",la:"Latin",lv:"Latvian",lt:"Lithuanian",lb:"Luxembourgish",mk:"North Macedonia",ms:"Malay",ml:"Malayalam",mt:"Maltese",mi:"Maori",mr:"Marathi",mo:"Moldavian",nv:"Navajo",ng:"Ndonga",ne:"Nepali",no:"Norwegian",nb:"Norwegian (Bokmal)",nn:"Norwegian (Nynorsk)",oc:"Occitan",or:"Oriya",om:"Oromo",fa:"Persian","fa-IR":"Persian/Iran",pl:"Polish",pt:"Portuguese","pt-BR":"Portuguese (Brazil)",pa:"Punjabi","pa-IN":"Punjabi (India)","pa-PK":"Punjabi (Pakistan)",qu:"Quechua",rm:"Rhaeto-Romanic",ro:"Romanian","ro-MO":"Romanian (Moldavia)",ru:"Russian","ru-MO":"Russian (Moldavia)",sz:"Sami (Lappish)",sg:"Sango",sa:"Sanskrit",sc:"Sardinian",sd:"Sindhi",si:"Singhalese",sr:"Serbian",sk:"Slovak",sl:"Slovenian",so:"Somani",sb:"Sorbian",es:"Spanish","es-AR":"Spanish (Argentina)","es-BO":"Spanish (Bolivia)","es-CL":"Spanish (Chile)","es-CO":"Spanish (Colombia)","es-CR":"Spanish (Costa Rica)","es-DO":"Spanish (Dominican Republic)","es-EC":"Spanish (Ecuador)","es-SV":"Spanish (El Salvador)","es-GT":"Spanish (Guatemala)","es-HN":"Spanish (Honduras)","es-MX":"Spanish (Mexico)","es-NI":"Spanish (Nicaragua)","es-PA":"Spanish (Panama)","es-PY":"Spanish (Paraguay)","es-PE":"Spanish (Peru)","es-PR":"Spanish (Puerto Rico)","es-ES":"Spanish (Spain)","es-UY":"Spanish (Uruguay)","es-VE":"Spanish (Venezuela)",sx:"Sutu",sw:"Swahili",sv:"Swedish","sv-FI":"Swedish (Finland)","sv-SV":"Swedish (Sweden)",ta:"Tamil",tt:"Tatar",te:"Teluga",th:"Thai",tig:"Tigre",ts:"Tsonga",tn:"Tswana",tr:"Turkish",tk:"Turkmen",uk:"Ukrainian",hsb:"Upper Sorbian",ur:"Urdu",ve:"Venda",vi:"Vietnamese",vo:"Volapuk",wa:"Walloon",cy:"Welsh",xh:"Xhosa",ji:"Yiddish",zu:"Zulu"})[tn]&&(this.internal.languageSettings.languageCode=tn,!1===this.internal.languageSettings.isSubscribed&&(this.internal.events.subscribe("putCatalog",function(){this.internal.write("/Lang ("+this.internal.languageSettings.languageCode+")")}),this.internal.languageSettings.isSubscribed=!0)),this},eI=(eF=E.API).getCharWidthsArray=function(tn,ti){var ta,ts,tc=(ti=ti||{}).font||this.internal.getFont(),tu=ti.fontSize||this.internal.getFontSize(),th=ti.charSpace||this.internal.getCharSpace(),tl=ti.widths?ti.widths:tc.metadata.Unicode.widths,tf=tl.fof?tl.fof:1,td=ti.kerning?ti.kerning:tc.metadata.Unicode.kerning,tp=td.fof?td.fof:1,tg=!1!==ti.doKerning,tm=0,tv=tn.length,tb=0,ty=tl[0]||tf,tw=[];for(ta=0;ta<tv;ta++)ts=tn.charCodeAt(ta),"function"==typeof tc.metadata.widthOfString?tw.push((tc.metadata.widthOfGlyph(tc.metadata.characterToGlyph(ts))+th*(1e3/tu)||0)/1e3):(tm=tg&&"object"===(0,to.Z)(td[ts])&&!isNaN(parseInt(td[ts][tb],10))?td[ts][tb]/tp:0,tw.push((tl[ts]||ty)/tf+tm)),tb=ts;return tw},eC=eF.getStringUnitWidth=function(tn,ti){var ta=(ti=ti||{}).fontSize||this.internal.getFontSize(),to=ti.font||this.internal.getFont(),ts=ti.charSpace||this.internal.getCharSpace();return eF.processArabic&&(tn=eF.processArabic(tn)),"function"==typeof to.metadata.widthOfString?to.metadata.widthOfString(tn,ta,ts)/ta:eI.apply(this,arguments).reduce(function(tn,ti){return tn+ti},0)},ej=function(tn,ti,ta,to){for(var ts=[],tc=0,tu=tn.length,th=0;tc!==tu&&th+ti[tc]<ta;)th+=ti[tc],tc++;ts.push(tn.slice(0,tc));var tl=tc;for(th=0;tc!==tu;)th+ti[tc]>to&&(ts.push(tn.slice(tl,tc)),th=0,tl=tc),th+=ti[tc],tc++;return tl!==tc&&ts.push(tn.slice(tl,tc)),ts},eO=function(tn,ti,ta){ta||(ta={});var to,ts,tc,tu,th,tl,tf,td=[],tp=[td],tg=ta.textIndent||0,tm=0,tv=0,tb=tn.split(" "),ty=eI.apply(this,[" ",ta])[0];if(tl=-1===ta.lineIndent?tb[0].length+2:ta.lineIndent||0){var tw=Array(tl).join(" "),tN=[];tb.map(function(tn){(tn=tn.split(/\s*\n/)).length>1?tN=tN.concat(tn.map(function(tn,ti){return(ti&&tn.length?"\n":"")+tn})):tN.push(tn[0])}),tb=tN,tl=eC.apply(this,[tw,ta])}for(tc=0,tu=tb.length;tc<tu;tc++){var tL=0;if(to=tb[tc],tl&&"\n"==to[0]&&(to=to.substr(1),tL=1),tg+tm+(tv=(ts=eI.apply(this,[to,ta])).reduce(function(tn,ti){return tn+ti},0))>ti||tL){if(tv>ti){for(th=ej.apply(this,[to,ts,ti-(tg+tm),ti]),td.push(th.shift()),td=[th.pop()];th.length;)tp.push([th.shift()]);tv=ts.slice(to.length-(td[0]?td[0].length:0)).reduce(function(tn,ti){return tn+ti},0)}else td=[to];tp.push(td),tg=tv+tl,tm=ty}else td.push(to),tg+=tm+tv,tm=ty}return tf=tl?function(tn,ti){return(ti?tw:"")+tn.join(" ")}:function(tn){return tn.join(" ")},tp.map(tf)},eF.splitTextToSize=function(tn,ti,ta){var to,ts=(ta=ta||{}).fontSize||this.internal.getFontSize(),tc=(function(tn){if(tn.widths&&tn.kerning)return{widths:tn.widths,kerning:tn.kerning};var ti=this.internal.getFont(tn.fontName,tn.fontStyle);return ti.metadata.Unicode?{widths:ti.metadata.Unicode.widths||{0:1},kerning:ti.metadata.Unicode.kerning||{}}:{font:ti.metadata,fontSize:this.internal.getFontSize(),charSpace:this.internal.getCharSpace()}}).call(this,ta);to=Array.isArray(tn)?tn:String(tn).split(/\r?\n/);var tu=1*this.internal.scaleFactor*ti/ts;tc.textIndent=ta.textIndent?1*ta.textIndent*this.internal.scaleFactor/ts:0,tc.lineIndent=ta.lineIndent;var th,tl,tf=[];for(th=0,tl=to.length;th<tl;th++)tf=tf.concat(eO.apply(this,[to[th],tu,tc]));return tf},function(tn){tn.__fontmetrics__=tn.__fontmetrics__||{};for(var ti="klmnopqrstuvwxyz",ta={},ts={},tc=0;tc<ti.length;tc++)ta[ti[tc]]="0123456789abcdef"[tc],ts["0123456789abcdef"[tc]]=ti[tc];var o=function(tn){return"0x"+parseInt(tn,10).toString(16)},tu=tn.__fontmetrics__.compress=function(tn){var ti,ta,tc,th,tl=["{"];for(var tf in tn){if(ti=tn[tf],ta=isNaN(parseInt(tf,10))?"'"+tf+"'":(ta=o(tf=parseInt(tf,10)).slice(2)).slice(0,-1)+ts[ta.slice(-1)],"number"==typeof ti)ti<0?(tc=o(ti).slice(3),th="-"):(tc=o(ti).slice(2),th=""),tc=th+tc.slice(0,-1)+ts[tc.slice(-1)];else{if("object"!==(0,to.Z)(ti))throw Error("Don't know what to do with value type "+(0,to.Z)(ti)+".");tc=tu(ti)}tl.push(ta+tc)}return tl.push("}"),tl.join("")},th=tn.__fontmetrics__.uncompress=function(tn){if("string"!=typeof tn)throw Error("Invalid argument passed to uncompress.");for(var ti,to,ts,tc,tu={},th=1,tl=tu,tf=[],td="",tp="",tg=tn.length-1,tm=1;tm<tg;tm+=1)"'"==(tc=tn[tm])?ti?(ts=ti.join(""),ti=void 0):ti=[]:ti?ti.push(tc):"{"==tc?(tf.push([tl,ts]),tl={},ts=void 0):"}"==tc?((to=tf.pop())[0][to[1]]=tl,ts=void 0,tl=to[0]):"-"==tc?th=-1:void 0===ts?ta.hasOwnProperty(tc)?(td+=ta[tc],ts=parseInt(td,16)*th,th=1,td=""):td+=tc:ta.hasOwnProperty(tc)?(tp+=ta[tc],tl[ts]=parseInt(tp,16)*th,th=1,ts=void 0,tp=""):tp+=tc;return tu},tl={codePages:["WinAnsiEncoding"],WinAnsiEncoding:th("{19m8n201n9q201o9r201s9l201t9m201u8m201w9n201x9o201y8o202k8q202l8r202m9p202q8p20aw8k203k8t203t8v203u9v2cq8s212m9t15m8w15n9w2dw9s16k8u16l9u17s9z17x8y17y9y}")},tf={Unicode:{Courier:tl,"Courier-Bold":tl,"Courier-BoldOblique":tl,"Courier-Oblique":tl,Helvetica:tl,"Helvetica-Bold":tl,"Helvetica-BoldOblique":tl,"Helvetica-Oblique":tl,"Times-Roman":tl,"Times-Bold":tl,"Times-BoldItalic":tl,"Times-Italic":tl}},td={Unicode:{"Courier-Oblique":th("{'widths'{k3w'fof'6o}'kerning'{'fof'-6o}}"),"Times-BoldItalic":th("{'widths'{k3o2q4ycx2r201n3m201o6o201s2l201t2l201u2l201w3m201x3m201y3m2k1t2l2r202m2n2n3m2o3m2p5n202q6o2r1w2s2l2t2l2u3m2v3t2w1t2x2l2y1t2z1w3k3m3l3m3m3m3n3m3o3m3p3m3q3m3r3m3s3m203t2l203u2l3v2l3w3t3x3t3y3t3z3m4k5n4l4m4m4m4n4m4o4s4p4m4q4m4r4s4s4y4t2r4u3m4v4m4w3x4x5t4y4s4z4s5k3x5l4s5m4m5n3r5o3x5p4s5q4m5r5t5s4m5t3x5u3x5v2l5w1w5x2l5y3t5z3m6k2l6l3m6m3m6n2w6o3m6p2w6q2l6r3m6s3r6t1w6u1w6v3m6w1w6x4y6y3r6z3m7k3m7l3m7m2r7n2r7o1w7p3r7q2w7r4m7s3m7t2w7u2r7v2n7w1q7x2n7y3t202l3mcl4mal2ram3man3mao3map3mar3mas2lat4uau1uav3maw3way4uaz2lbk2sbl3t'fof'6obo2lbp3tbq3mbr1tbs2lbu1ybv3mbz3mck4m202k3mcm4mcn4mco4mcp4mcq5ycr4mcs4mct4mcu4mcv4mcw2r2m3rcy2rcz2rdl4sdm4sdn4sdo4sdp4sdq4sds4sdt4sdu4sdv4sdw4sdz3mek3mel3mem3men3meo3mep3meq4ser2wes2wet2weu2wev2wew1wex1wey1wez1wfl3rfm3mfn3mfo3mfp3mfq3mfr3tfs3mft3rfu3rfv3rfw3rfz2w203k6o212m6o2dw2l2cq2l3t3m3u2l17s3x19m3m}'kerning'{cl{4qu5kt5qt5rs17ss5ts}201s{201ss}201t{cks4lscmscnscoscpscls2wu2yu201ts}201x{2wu2yu}2k{201ts}2w{4qx5kx5ou5qx5rs17su5tu}2x{17su5tu5ou}2y{4qx5kx5ou5qx5rs17ss5ts}'fof'-6ofn{17sw5tw5ou5qw5rs}7t{cksclscmscnscoscps4ls}3u{17su5tu5os5qs}3v{17su5tu5os5qs}7p{17su5tu}ck{4qu5kt5qt5rs17ss5ts}4l{4qu5kt5qt5rs17ss5ts}cm{4qu5kt5qt5rs17ss5ts}cn{4qu5kt5qt5rs17ss5ts}co{4qu5kt5qt5rs17ss5ts}cp{4qu5kt5qt5rs17ss5ts}6l{4qu5ou5qw5rt17su5tu}5q{ckuclucmucnucoucpu4lu}5r{ckuclucmucnucoucpu4lu}7q{cksclscmscnscoscps4ls}6p{4qu5ou5qw5rt17sw5tw}ek{4qu5ou5qw5rt17su5tu}el{4qu5ou5qw5rt17su5tu}em{4qu5ou5qw5rt17su5tu}en{4qu5ou5qw5rt17su5tu}eo{4qu5ou5qw5rt17su5tu}ep{4qu5ou5qw5rt17su5tu}es{17ss5ts5qs4qu}et{4qu5ou5qw5rt17sw5tw}eu{4qu5ou5qw5rt17ss5ts}ev{17ss5ts5qs4qu}6z{17sw5tw5ou5qw5rs}fm{17sw5tw5ou5qw5rs}7n{201ts}fo{17sw5tw5ou5qw5rs}fp{17sw5tw5ou5qw5rs}fq{17sw5tw5ou5qw5rs}7r{cksclscmscnscoscps4ls}fs{17sw5tw5ou5qw5rs}ft{17su5tu}fu{17su5tu}fv{17su5tu}fw{17su5tu}fz{cksclscmscnscoscps4ls}}}"),"Helvetica-Bold":th("{'widths'{k3s2q4scx1w201n3r201o6o201s1w201t1w201u1w201w3m201x3m201y3m2k1w2l2l202m2n2n3r2o3r2p5t202q6o2r1s2s2l2t2l2u2r2v3u2w1w2x2l2y1w2z1w3k3r3l3r3m3r3n3r3o3r3p3r3q3r3r3r3s3r203t2l203u2l3v2l3w3u3x3u3y3u3z3x4k6l4l4s4m4s4n4s4o4s4p4m4q3x4r4y4s4s4t1w4u3r4v4s4w3x4x5n4y4s4z4y5k4m5l4y5m4s5n4m5o3x5p4s5q4m5r5y5s4m5t4m5u3x5v2l5w1w5x2l5y3u5z3r6k2l6l3r6m3x6n3r6o3x6p3r6q2l6r3x6s3x6t1w6u1w6v3r6w1w6x5t6y3x6z3x7k3x7l3x7m2r7n3r7o2l7p3x7q3r7r4y7s3r7t3r7u3m7v2r7w1w7x2r7y3u202l3rcl4sal2lam3ran3rao3rap3rar3ras2lat4tau2pav3raw3uay4taz2lbk2sbl3u'fof'6obo2lbp3xbq3rbr1wbs2lbu2obv3rbz3xck4s202k3rcm4scn4sco4scp4scq6ocr4scs4mct4mcu4mcv4mcw1w2m2zcy1wcz1wdl4sdm4ydn4ydo4ydp4ydq4yds4ydt4sdu4sdv4sdw4sdz3xek3rel3rem3ren3reo3rep3req5ter3res3ret3reu3rev3rew1wex1wey1wez1wfl3xfm3xfn3xfo3xfp3xfq3xfr3ufs3xft3xfu3xfv3xfw3xfz3r203k6o212m6o2dw2l2cq2l3t3r3u2l17s4m19m3r}'kerning'{cl{4qs5ku5ot5qs17sv5tv}201t{2ww4wy2yw}201w{2ks}201x{2ww4wy2yw}2k{201ts201xs}2w{7qs4qu5kw5os5qw5rs17su5tu7tsfzs}2x{5ow5qs}2y{7qs4qu5kw5os5qw5rs17su5tu7tsfzs}'fof'-6o7p{17su5tu5ot}ck{4qs5ku5ot5qs17sv5tv}4l{4qs5ku5ot5qs17sv5tv}cm{4qs5ku5ot5qs17sv5tv}cn{4qs5ku5ot5qs17sv5tv}co{4qs5ku5ot5qs17sv5tv}cp{4qs5ku5ot5qs17sv5tv}6l{17st5tt5os}17s{2kwclvcmvcnvcovcpv4lv4wwckv}5o{2kucltcmtcntcotcpt4lt4wtckt}5q{2ksclscmscnscoscps4ls4wvcks}5r{2ks4ws}5t{2kwclvcmvcnvcovcpv4lv4wwckv}eo{17st5tt5os}fu{17su5tu5ot}6p{17ss5ts}ek{17st5tt5os}el{17st5tt5os}em{17st5tt5os}en{17st5tt5os}6o{201ts}ep{17st5tt5os}es{17ss5ts}et{17ss5ts}eu{17ss5ts}ev{17ss5ts}6z{17su5tu5os5qt}fm{17su5tu5os5qt}fn{17su5tu5os5qt}fo{17su5tu5os5qt}fp{17su5tu5os5qt}fq{17su5tu5os5qt}fs{17su5tu5os5qt}ft{17su5tu5ot}7m{5os}fv{17su5tu5ot}fw{17su5tu5ot}}}"),Courier:th("{'widths'{k3w'fof'6o}'kerning'{'fof'-6o}}"),"Courier-BoldOblique":th("{'widths'{k3w'fof'6o}'kerning'{'fof'-6o}}"),"Times-Bold":th("{'widths'{k3q2q5ncx2r201n3m201o6o201s2l201t2l201u2l201w3m201x3m201y3m2k1t2l2l202m2n2n3m2o3m2p6o202q6o2r1w2s2l2t2l2u3m2v3t2w1t2x2l2y1t2z1w3k3m3l3m3m3m3n3m3o3m3p3m3q3m3r3m3s3m203t2l203u2l3v2l3w3t3x3t3y3t3z3m4k5x4l4s4m4m4n4s4o4s4p4m4q3x4r4y4s4y4t2r4u3m4v4y4w4m4x5y4y4s4z4y5k3x5l4y5m4s5n3r5o4m5p4s5q4s5r6o5s4s5t4s5u4m5v2l5w1w5x2l5y3u5z3m6k2l6l3m6m3r6n2w6o3r6p2w6q2l6r3m6s3r6t1w6u2l6v3r6w1w6x5n6y3r6z3m7k3r7l3r7m2w7n2r7o2l7p3r7q3m7r4s7s3m7t3m7u2w7v2r7w1q7x2r7y3o202l3mcl4sal2lam3man3mao3map3mar3mas2lat4uau1yav3maw3tay4uaz2lbk2sbl3t'fof'6obo2lbp3rbr1tbs2lbu2lbv3mbz3mck4s202k3mcm4scn4sco4scp4scq6ocr4scs4mct4mcu4mcv4mcw2r2m3rcy2rcz2rdl4sdm4ydn4ydo4ydp4ydq4yds4ydt4sdu4sdv4sdw4sdz3rek3mel3mem3men3meo3mep3meq4ser2wes2wet2weu2wev2wew1wex1wey1wez1wfl3rfm3mfn3mfo3mfp3mfq3mfr3tfs3mft3rfu3rfv3rfw3rfz3m203k6o212m6o2dw2l2cq2l3t3m3u2l17s4s19m3m}'kerning'{cl{4qt5ks5ot5qy5rw17sv5tv}201t{cks4lscmscnscoscpscls4wv}2k{201ts}2w{4qu5ku7mu5os5qx5ru17su5tu}2x{17su5tu5ou5qs}2y{4qv5kv7mu5ot5qz5ru17su5tu}'fof'-6o7t{cksclscmscnscoscps4ls}3u{17su5tu5os5qu}3v{17su5tu5os5qu}fu{17su5tu5ou5qu}7p{17su5tu5ou5qu}ck{4qt5ks5ot5qy5rw17sv5tv}4l{4qt5ks5ot5qy5rw17sv5tv}cm{4qt5ks5ot5qy5rw17sv5tv}cn{4qt5ks5ot5qy5rw17sv5tv}co{4qt5ks5ot5qy5rw17sv5tv}cp{4qt5ks5ot5qy5rw17sv5tv}6l{17st5tt5ou5qu}17s{ckuclucmucnucoucpu4lu4wu}5o{ckuclucmucnucoucpu4lu4wu}5q{ckzclzcmzcnzcozcpz4lz4wu}5r{ckxclxcmxcnxcoxcpx4lx4wu}5t{ckuclucmucnucoucpu4lu4wu}7q{ckuclucmucnucoucpu4lu}6p{17sw5tw5ou5qu}ek{17st5tt5qu}el{17st5tt5ou5qu}em{17st5tt5qu}en{17st5tt5qu}eo{17st5tt5qu}ep{17st5tt5ou5qu}es{17ss5ts5qu}et{17sw5tw5ou5qu}eu{17sw5tw5ou5qu}ev{17ss5ts5qu}6z{17sw5tw5ou5qu5rs}fm{17sw5tw5ou5qu5rs}fn{17sw5tw5ou5qu5rs}fo{17sw5tw5ou5qu5rs}fp{17sw5tw5ou5qu5rs}fq{17sw5tw5ou5qu5rs}7r{cktcltcmtcntcotcpt4lt5os}fs{17sw5tw5ou5qu5rs}ft{17su5tu5ou5qu}7m{5os}fv{17su5tu5ou5qu}fw{17su5tu5ou5qu}fz{cksclscmscnscoscps4ls}}}"),Symbol:th("{'widths'{k3uaw4r19m3m2k1t2l2l202m2y2n3m2p5n202q6o3k3m2s2l2t2l2v3r2w1t3m3m2y1t2z1wbk2sbl3r'fof'6o3n3m3o3m3p3m3q3m3r3m3s3m3t3m3u1w3v1w3w3r3x3r3y3r3z2wbp3t3l3m5v2l5x2l5z3m2q4yfr3r7v3k7w1o7x3k}'kerning'{'fof'-6o}}"),Helvetica:th("{'widths'{k3p2q4mcx1w201n3r201o6o201s1q201t1q201u1q201w2l201x2l201y2l2k1w2l1w202m2n2n3r2o3r2p5t202q6o2r1n2s2l2t2l2u2r2v3u2w1w2x2l2y1w2z1w3k3r3l3r3m3r3n3r3o3r3p3r3q3r3r3r3s3r203t2l203u2l3v1w3w3u3x3u3y3u3z3r4k6p4l4m4m4m4n4s4o4s4p4m4q3x4r4y4s4s4t1w4u3m4v4m4w3r4x5n4y4s4z4y5k4m5l4y5m4s5n4m5o3x5p4s5q4m5r5y5s4m5t4m5u3x5v1w5w1w5x1w5y2z5z3r6k2l6l3r6m3r6n3m6o3r6p3r6q1w6r3r6s3r6t1q6u1q6v3m6w1q6x5n6y3r6z3r7k3r7l3r7m2l7n3m7o1w7p3r7q3m7r4s7s3m7t3m7u3m7v2l7w1u7x2l7y3u202l3rcl4mal2lam3ran3rao3rap3rar3ras2lat4tau2pav3raw3uay4taz2lbk2sbl3u'fof'6obo2lbp3rbr1wbs2lbu2obv3rbz3xck4m202k3rcm4mcn4mco4mcp4mcq6ocr4scs4mct4mcu4mcv4mcw1w2m2ncy1wcz1wdl4sdm4ydn4ydo4ydp4ydq4yds4ydt4sdu4sdv4sdw4sdz3xek3rel3rem3ren3reo3rep3req5ter3mes3ret3reu3rev3rew1wex1wey1wez1wfl3rfm3rfn3rfo3rfp3rfq3rfr3ufs3xft3rfu3rfv3rfw3rfz3m203k6o212m6o2dw2l2cq2l3t3r3u1w17s4m19m3r}'kerning'{5q{4wv}cl{4qs5kw5ow5qs17sv5tv}201t{2wu4w1k2yu}201x{2wu4wy2yu}17s{2ktclucmucnu4otcpu4lu4wycoucku}2w{7qs4qz5k1m17sy5ow5qx5rsfsu5ty7tufzu}2x{17sy5ty5oy5qs}2y{7qs4qz5k1m17sy5ow5qx5rsfsu5ty7tufzu}'fof'-6o7p{17sv5tv5ow}ck{4qs5kw5ow5qs17sv5tv}4l{4qs5kw5ow5qs17sv5tv}cm{4qs5kw5ow5qs17sv5tv}cn{4qs5kw5ow5qs17sv5tv}co{4qs5kw5ow5qs17sv5tv}cp{4qs5kw5ow5qs17sv5tv}6l{17sy5ty5ow}do{17st5tt}4z{17st5tt}7s{fst}dm{17st5tt}dn{17st5tt}5o{ckwclwcmwcnwcowcpw4lw4wv}dp{17st5tt}dq{17st5tt}7t{5ow}ds{17st5tt}5t{2ktclucmucnu4otcpu4lu4wycoucku}fu{17sv5tv5ow}6p{17sy5ty5ow5qs}ek{17sy5ty5ow}el{17sy5ty5ow}em{17sy5ty5ow}en{5ty}eo{17sy5ty5ow}ep{17sy5ty5ow}es{17sy5ty5qs}et{17sy5ty5ow5qs}eu{17sy5ty5ow5qs}ev{17sy5ty5ow5qs}6z{17sy5ty5ow5qs}fm{17sy5ty5ow5qs}fn{17sy5ty5ow5qs}fo{17sy5ty5ow5qs}fp{17sy5ty5qs}fq{17sy5ty5ow5qs}7r{5ow}fs{17sy5ty5ow5qs}ft{17sv5tv5ow}7m{5ow}fv{17sv5tv5ow}fw{17sv5tv5ow}}}"),"Helvetica-BoldOblique":th("{'widths'{k3s2q4scx1w201n3r201o6o201s1w201t1w201u1w201w3m201x3m201y3m2k1w2l2l202m2n2n3r2o3r2p5t202q6o2r1s2s2l2t2l2u2r2v3u2w1w2x2l2y1w2z1w3k3r3l3r3m3r3n3r3o3r3p3r3q3r3r3r3s3r203t2l203u2l3v2l3w3u3x3u3y3u3z3x4k6l4l4s4m4s4n4s4o4s4p4m4q3x4r4y4s4s4t1w4u3r4v4s4w3x4x5n4y4s4z4y5k4m5l4y5m4s5n4m5o3x5p4s5q4m5r5y5s4m5t4m5u3x5v2l5w1w5x2l5y3u5z3r6k2l6l3r6m3x6n3r6o3x6p3r6q2l6r3x6s3x6t1w6u1w6v3r6w1w6x5t6y3x6z3x7k3x7l3x7m2r7n3r7o2l7p3x7q3r7r4y7s3r7t3r7u3m7v2r7w1w7x2r7y3u202l3rcl4sal2lam3ran3rao3rap3rar3ras2lat4tau2pav3raw3uay4taz2lbk2sbl3u'fof'6obo2lbp3xbq3rbr1wbs2lbu2obv3rbz3xck4s202k3rcm4scn4sco4scp4scq6ocr4scs4mct4mcu4mcv4mcw1w2m2zcy1wcz1wdl4sdm4ydn4ydo4ydp4ydq4yds4ydt4sdu4sdv4sdw4sdz3xek3rel3rem3ren3reo3rep3req5ter3res3ret3reu3rev3rew1wex1wey1wez1wfl3xfm3xfn3xfo3xfp3xfq3xfr3ufs3xft3xfu3xfv3xfw3xfz3r203k6o212m6o2dw2l2cq2l3t3r3u2l17s4m19m3r}'kerning'{cl{4qs5ku5ot5qs17sv5tv}201t{2ww4wy2yw}201w{2ks}201x{2ww4wy2yw}2k{201ts201xs}2w{7qs4qu5kw5os5qw5rs17su5tu7tsfzs}2x{5ow5qs}2y{7qs4qu5kw5os5qw5rs17su5tu7tsfzs}'fof'-6o7p{17su5tu5ot}ck{4qs5ku5ot5qs17sv5tv}4l{4qs5ku5ot5qs17sv5tv}cm{4qs5ku5ot5qs17sv5tv}cn{4qs5ku5ot5qs17sv5tv}co{4qs5ku5ot5qs17sv5tv}cp{4qs5ku5ot5qs17sv5tv}6l{17st5tt5os}17s{2kwclvcmvcnvcovcpv4lv4wwckv}5o{2kucltcmtcntcotcpt4lt4wtckt}5q{2ksclscmscnscoscps4ls4wvcks}5r{2ks4ws}5t{2kwclvcmvcnvcovcpv4lv4wwckv}eo{17st5tt5os}fu{17su5tu5ot}6p{17ss5ts}ek{17st5tt5os}el{17st5tt5os}em{17st5tt5os}en{17st5tt5os}6o{201ts}ep{17st5tt5os}es{17ss5ts}et{17ss5ts}eu{17ss5ts}ev{17ss5ts}6z{17su5tu5os5qt}fm{17su5tu5os5qt}fn{17su5tu5os5qt}fo{17su5tu5os5qt}fp{17su5tu5os5qt}fq{17su5tu5os5qt}fs{17su5tu5os5qt}ft{17su5tu5ot}7m{5os}fv{17su5tu5ot}fw{17su5tu5ot}}}"),ZapfDingbats:th("{'widths'{k4u2k1w'fof'6o}'kerning'{'fof'-6o}}"),"Courier-Bold":th("{'widths'{k3w'fof'6o}'kerning'{'fof'-6o}}"),"Times-Italic":th("{'widths'{k3n2q4ycx2l201n3m201o5t201s2l201t2l201u2l201w3r201x3r201y3r2k1t2l2l202m2n2n3m2o3m2p5n202q5t2r1p2s2l2t2l2u3m2v4n2w1t2x2l2y1t2z1w3k3m3l3m3m3m3n3m3o3m3p3m3q3m3r3m3s3m203t2l203u2l3v2l3w4n3x4n3y4n3z3m4k5w4l3x4m3x4n4m4o4s4p3x4q3x4r4s4s4s4t2l4u2w4v4m4w3r4x5n4y4m4z4s5k3x5l4s5m3x5n3m5o3r5p4s5q3x5r5n5s3x5t3r5u3r5v2r5w1w5x2r5y2u5z3m6k2l6l3m6m3m6n2w6o3m6p2w6q1w6r3m6s3m6t1w6u1w6v2w6w1w6x4s6y3m6z3m7k3m7l3m7m2r7n2r7o1w7p3m7q2w7r4m7s2w7t2w7u2r7v2s7w1v7x2s7y3q202l3mcl3xal2ram3man3mao3map3mar3mas2lat4wau1vav3maw4nay4waz2lbk2sbl4n'fof'6obo2lbp3mbq3obr1tbs2lbu1zbv3mbz3mck3x202k3mcm3xcn3xco3xcp3xcq5tcr4mcs3xct3xcu3xcv3xcw2l2m2ucy2lcz2ldl4mdm4sdn4sdo4sdp4sdq4sds4sdt4sdu4sdv4sdw4sdz3mek3mel3mem3men3meo3mep3meq4mer2wes2wet2weu2wev2wew1wex1wey1wez1wfl3mfm3mfn3mfo3mfp3mfq3mfr4nfs3mft3mfu3mfv3mfw3mfz2w203k6o212m6m2dw2l2cq2l3t3m3u2l17s3r19m3m}'kerning'{cl{5kt4qw}201s{201sw}201t{201tw2wy2yy6q-t}201x{2wy2yy}2k{201tw}2w{7qs4qy7rs5ky7mw5os5qx5ru17su5tu}2x{17ss5ts5os}2y{7qs4qy7rs5ky7mw5os5qx5ru17su5tu}'fof'-6o6t{17ss5ts5qs}7t{5os}3v{5qs}7p{17su5tu5qs}ck{5kt4qw}4l{5kt4qw}cm{5kt4qw}cn{5kt4qw}co{5kt4qw}cp{5kt4qw}6l{4qs5ks5ou5qw5ru17su5tu}17s{2ks}5q{ckvclvcmvcnvcovcpv4lv}5r{ckuclucmucnucoucpu4lu}5t{2ks}6p{4qs5ks5ou5qw5ru17su5tu}ek{4qs5ks5ou5qw5ru17su5tu}el{4qs5ks5ou5qw5ru17su5tu}em{4qs5ks5ou5qw5ru17su5tu}en{4qs5ks5ou5qw5ru17su5tu}eo{4qs5ks5ou5qw5ru17su5tu}ep{4qs5ks5ou5qw5ru17su5tu}es{5ks5qs4qs}et{4qs5ks5ou5qw5ru17su5tu}eu{4qs5ks5qw5ru17su5tu}ev{5ks5qs4qs}ex{17ss5ts5qs}6z{4qv5ks5ou5qw5ru17su5tu}fm{4qv5ks5ou5qw5ru17su5tu}fn{4qv5ks5ou5qw5ru17su5tu}fo{4qv5ks5ou5qw5ru17su5tu}fp{4qv5ks5ou5qw5ru17su5tu}fq{4qv5ks5ou5qw5ru17su5tu}7r{5os}fs{4qv5ks5ou5qw5ru17su5tu}ft{17su5tu5qs}fu{17su5tu5qs}fv{17su5tu5qs}fw{17su5tu5qs}}}"),"Times-Roman":th("{'widths'{k3n2q4ycx2l201n3m201o6o201s2l201t2l201u2l201w2w201x2w201y2w2k1t2l2l202m2n2n3m2o3m2p5n202q6o2r1m2s2l2t2l2u3m2v3s2w1t2x2l2y1t2z1w3k3m3l3m3m3m3n3m3o3m3p3m3q3m3r3m3s3m203t2l203u2l3v1w3w3s3x3s3y3s3z2w4k5w4l4s4m4m4n4m4o4s4p3x4q3r4r4s4s4s4t2l4u2r4v4s4w3x4x5t4y4s4z4s5k3r5l4s5m4m5n3r5o3x5p4s5q4s5r5y5s4s5t4s5u3x5v2l5w1w5x2l5y2z5z3m6k2l6l2w6m3m6n2w6o3m6p2w6q2l6r3m6s3m6t1w6u1w6v3m6w1w6x4y6y3m6z3m7k3m7l3m7m2l7n2r7o1w7p3m7q3m7r4s7s3m7t3m7u2w7v3k7w1o7x3k7y3q202l3mcl4sal2lam3man3mao3map3mar3mas2lat4wau1vav3maw3say4waz2lbk2sbl3s'fof'6obo2lbp3mbq2xbr1tbs2lbu1zbv3mbz2wck4s202k3mcm4scn4sco4scp4scq5tcr4mcs3xct3xcu3xcv3xcw2l2m2tcy2lcz2ldl4sdm4sdn4sdo4sdp4sdq4sds4sdt4sdu4sdv4sdw4sdz3mek2wel2wem2wen2weo2wep2weq4mer2wes2wet2weu2wev2wew1wex1wey1wez1wfl3mfm3mfn3mfo3mfp3mfq3mfr3sfs3mft3mfu3mfv3mfw3mfz3m203k6o212m6m2dw2l2cq2l3t3m3u1w17s4s19m3m}'kerning'{cl{4qs5ku17sw5ou5qy5rw201ss5tw201ws}201s{201ss}201t{ckw4lwcmwcnwcowcpwclw4wu201ts}2k{201ts}2w{4qs5kw5os5qx5ru17sx5tx}2x{17sw5tw5ou5qu}2y{4qs5kw5os5qx5ru17sx5tx}'fof'-6o7t{ckuclucmucnucoucpu4lu5os5rs}3u{17su5tu5qs}3v{17su5tu5qs}7p{17sw5tw5qs}ck{4qs5ku17sw5ou5qy5rw201ss5tw201ws}4l{4qs5ku17sw5ou5qy5rw201ss5tw201ws}cm{4qs5ku17sw5ou5qy5rw201ss5tw201ws}cn{4qs5ku17sw5ou5qy5rw201ss5tw201ws}co{4qs5ku17sw5ou5qy5rw201ss5tw201ws}cp{4qs5ku17sw5ou5qy5rw201ss5tw201ws}6l{17su5tu5os5qw5rs}17s{2ktclvcmvcnvcovcpv4lv4wuckv}5o{ckwclwcmwcnwcowcpw4lw4wu}5q{ckyclycmycnycoycpy4ly4wu5ms}5r{cktcltcmtcntcotcpt4lt4ws}5t{2ktclvcmvcnvcovcpv4lv4wuckv}7q{cksclscmscnscoscps4ls}6p{17su5tu5qw5rs}ek{5qs5rs}el{17su5tu5os5qw5rs}em{17su5tu5os5qs5rs}en{17su5qs5rs}eo{5qs5rs}ep{17su5tu5os5qw5rs}es{5qs}et{17su5tu5qw5rs}eu{17su5tu5qs5rs}ev{5qs}6z{17sv5tv5os5qx5rs}fm{5os5qt5rs}fn{17sv5tv5os5qx5rs}fo{17sv5tv5os5qx5rs}fp{5os5qt5rs}fq{5os5qt5rs}7r{ckuclucmucnucoucpu4lu5os}fs{17sv5tv5os5qx5rs}ft{17ss5ts5qs}fu{17sw5tw5qs}fv{17sw5tw5qs}fw{17ss5ts5qs}fz{ckuclucmucnucoucpu4lu5os5rs}}}"),"Helvetica-Oblique":th("{'widths'{k3p2q4mcx1w201n3r201o6o201s1q201t1q201u1q201w2l201x2l201y2l2k1w2l1w202m2n2n3r2o3r2p5t202q6o2r1n2s2l2t2l2u2r2v3u2w1w2x2l2y1w2z1w3k3r3l3r3m3r3n3r3o3r3p3r3q3r3r3r3s3r203t2l203u2l3v1w3w3u3x3u3y3u3z3r4k6p4l4m4m4m4n4s4o4s4p4m4q3x4r4y4s4s4t1w4u3m4v4m4w3r4x5n4y4s4z4y5k4m5l4y5m4s5n4m5o3x5p4s5q4m5r5y5s4m5t4m5u3x5v1w5w1w5x1w5y2z5z3r6k2l6l3r6m3r6n3m6o3r6p3r6q1w6r3r6s3r6t1q6u1q6v3m6w1q6x5n6y3r6z3r7k3r7l3r7m2l7n3m7o1w7p3r7q3m7r4s7s3m7t3m7u3m7v2l7w1u7x2l7y3u202l3rcl4mal2lam3ran3rao3rap3rar3ras2lat4tau2pav3raw3uay4taz2lbk2sbl3u'fof'6obo2lbp3rbr1wbs2lbu2obv3rbz3xck4m202k3rcm4mcn4mco4mcp4mcq6ocr4scs4mct4mcu4mcv4mcw1w2m2ncy1wcz1wdl4sdm4ydn4ydo4ydp4ydq4yds4ydt4sdu4sdv4sdw4sdz3xek3rel3rem3ren3reo3rep3req5ter3mes3ret3reu3rev3rew1wex1wey1wez1wfl3rfm3rfn3rfo3rfp3rfq3rfr3ufs3xft3rfu3rfv3rfw3rfz3m203k6o212m6o2dw2l2cq2l3t3r3u1w17s4m19m3r}'kerning'{5q{4wv}cl{4qs5kw5ow5qs17sv5tv}201t{2wu4w1k2yu}201x{2wu4wy2yu}17s{2ktclucmucnu4otcpu4lu4wycoucku}2w{7qs4qz5k1m17sy5ow5qx5rsfsu5ty7tufzu}2x{17sy5ty5oy5qs}2y{7qs4qz5k1m17sy5ow5qx5rsfsu5ty7tufzu}'fof'-6o7p{17sv5tv5ow}ck{4qs5kw5ow5qs17sv5tv}4l{4qs5kw5ow5qs17sv5tv}cm{4qs5kw5ow5qs17sv5tv}cn{4qs5kw5ow5qs17sv5tv}co{4qs5kw5ow5qs17sv5tv}cp{4qs5kw5ow5qs17sv5tv}6l{17sy5ty5ow}do{17st5tt}4z{17st5tt}7s{fst}dm{17st5tt}dn{17st5tt}5o{ckwclwcmwcnwcowcpw4lw4wv}dp{17st5tt}dq{17st5tt}7t{5ow}ds{17st5tt}5t{2ktclucmucnu4otcpu4lu4wycoucku}fu{17sv5tv5ow}6p{17sy5ty5ow5qs}ek{17sy5ty5ow}el{17sy5ty5ow}em{17sy5ty5ow}en{5ty}eo{17sy5ty5ow}ep{17sy5ty5ow}es{17sy5ty5qs}et{17sy5ty5ow5qs}eu{17sy5ty5ow5qs}ev{17sy5ty5ow5qs}6z{17sy5ty5ow5qs}fm{17sy5ty5ow5qs}fn{17sy5ty5ow5qs}fo{17sy5ty5ow5qs}fp{17sy5ty5qs}fq{17sy5ty5ow5qs}7r{5ow}fs{17sy5ty5ow5qs}ft{17sv5tv5ow}7m{5ow}fv{17sv5tv5ow}fw{17sv5tv5ow}}}")}};tn.events.push(["addFont",function(tn){var ti=tn.font,ta=td.Unicode[ti.postScriptName];ta&&(ti.metadata.Unicode={},ti.metadata.Unicode.widths=ta.widths,ti.metadata.Unicode.kerning=ta.kerning);var to=tf.Unicode[ti.postScriptName];to&&(ti.metadata.Unicode.encoding=to,ti.encoding=to.codePages[0])}])}(E.API),/**
 * @license
 * Licensed under the MIT License.
 * http://opensource.org/licenses/mit-license
 */function(tn){var e=function(tn){for(var ti=tn.length,ta=new Uint8Array(ti),to=0;to<ti;to++)ta[to]=tn.charCodeAt(to);return ta};tn.API.events.push(["addFont",function(ti){var ta,to=void 0,ts=ti.font,tc=ti.instance;if(!ts.isStandardFont){if(void 0===tc)throw Error("Font does not exist in vFS, import fonts or remove declaration doc.addFont('"+ts.postScriptName+"').");if("string"!=typeof(to=!1===tc.existsFileInVFS(ts.postScriptName)?tc.loadFile(ts.postScriptName):tc.getFileFromVFS(ts.postScriptName)))throw Error("Font is not stored as string-data in vFS, import fonts or remove declaration doc.addFont('"+ts.postScriptName+"').");ta=to,ta=/^\x00\x01\x00\x00/.test(ta)?e(ta):e(th(ta)),ts.metadata=tn.API.TTFFont.open(ta),ts.metadata.Unicode=ts.metadata.Unicode||{encoding:{},kerning:{},widths:[]},ts.metadata.glyIdsUsed=[0]}}])}(E),E.API.addSvgAsImage=function(tn,ti,to,ts,th,tl,tf,td){if(isNaN(ti)||isNaN(to))throw tu.error("jsPDF.addSvgAsImage: Invalid coordinates",arguments),Error("Invalid coordinates passed to jsPDF.addSvgAsImage");if(isNaN(ts)||isNaN(th))throw tu.error("jsPDF.addSvgAsImage: Invalid measurements",arguments),Error("Invalid measurements (width and/or height) passed to jsPDF.addSvgAsImage");var tp=document.createElement("canvas");tp.width=ts,tp.height=th;var tg=tp.getContext("2d");tg.fillStyle="#fff",tg.fillRect(0,0,tp.width,tp.height);var tm={ignoreMouse:!0,ignoreAnimation:!0,ignoreDimensions:!0},tv=this;return(tc.canvg?Promise.resolve(tc.canvg):Promise.all([ta.e(6350),ta.e(636)]).then(ta.bind(ta,34668))).catch(function(tn){return Promise.reject(Error("Could not load canvg: "+tn))}).then(function(tn){return tn.default?tn.default:tn}).then(function(ti){return ti.fromString(tg,tn,tm)},function(){return Promise.reject(Error("Could not load canvg."))}).then(function(tn){return tn.render(tm)}).then(function(){tv.addImage(tp.toDataURL("image/jpeg",1),ti,to,ts,th,tf,td)})},E.API.putTotalPages=function(tn){var ti,ta=0;15>parseInt(this.internal.getFont().id.substr(1),10)?(ti=RegExp(tn,"g"),ta=this.internal.getNumberOfPages()):(ti=RegExp(this.pdfEscape16(tn,this.internal.getFont()),"g"),ta=this.pdfEscape16(this.internal.getNumberOfPages()+"",this.internal.getFont()));for(var to=1;to<=this.internal.getNumberOfPages();to++)for(var ts=0;ts<this.internal.pages[to].length;ts++)this.internal.pages[to][ts]=this.internal.pages[to][ts].replace(ti,ta);return this},E.API.viewerPreferences=function(tn,ti){tn=tn||{},ti=ti||!1;var ta,ts,tc,tu,th={HideToolbar:{defaultValue:!1,value:!1,type:"boolean",explicitSet:!1,valueSet:[!0,!1],pdfVersion:1.3},HideMenubar:{defaultValue:!1,value:!1,type:"boolean",explicitSet:!1,valueSet:[!0,!1],pdfVersion:1.3},HideWindowUI:{defaultValue:!1,value:!1,type:"boolean",explicitSet:!1,valueSet:[!0,!1],pdfVersion:1.3},FitWindow:{defaultValue:!1,value:!1,type:"boolean",explicitSet:!1,valueSet:[!0,!1],pdfVersion:1.3},CenterWindow:{defaultValue:!1,value:!1,type:"boolean",explicitSet:!1,valueSet:[!0,!1],pdfVersion:1.3},DisplayDocTitle:{defaultValue:!1,value:!1,type:"boolean",explicitSet:!1,valueSet:[!0,!1],pdfVersion:1.4},NonFullScreenPageMode:{defaultValue:"UseNone",value:"UseNone",type:"name",explicitSet:!1,valueSet:["UseNone","UseOutlines","UseThumbs","UseOC"],pdfVersion:1.3},Direction:{defaultValue:"L2R",value:"L2R",type:"name",explicitSet:!1,valueSet:["L2R","R2L"],pdfVersion:1.3},ViewArea:{defaultValue:"CropBox",value:"CropBox",type:"name",explicitSet:!1,valueSet:["MediaBox","CropBox","TrimBox","BleedBox","ArtBox"],pdfVersion:1.4},ViewClip:{defaultValue:"CropBox",value:"CropBox",type:"name",explicitSet:!1,valueSet:["MediaBox","CropBox","TrimBox","BleedBox","ArtBox"],pdfVersion:1.4},PrintArea:{defaultValue:"CropBox",value:"CropBox",type:"name",explicitSet:!1,valueSet:["MediaBox","CropBox","TrimBox","BleedBox","ArtBox"],pdfVersion:1.4},PrintClip:{defaultValue:"CropBox",value:"CropBox",type:"name",explicitSet:!1,valueSet:["MediaBox","CropBox","TrimBox","BleedBox","ArtBox"],pdfVersion:1.4},PrintScaling:{defaultValue:"AppDefault",value:"AppDefault",type:"name",explicitSet:!1,valueSet:["AppDefault","None"],pdfVersion:1.6},Duplex:{defaultValue:"",value:"none",type:"name",explicitSet:!1,valueSet:["Simplex","DuplexFlipShortEdge","DuplexFlipLongEdge","none"],pdfVersion:1.7},PickTrayByPDFSize:{defaultValue:!1,value:!1,type:"boolean",explicitSet:!1,valueSet:[!0,!1],pdfVersion:1.7},PrintPageRange:{defaultValue:"",value:"",type:"array",explicitSet:!1,valueSet:null,pdfVersion:1.7},NumCopies:{defaultValue:1,value:1,type:"integer",explicitSet:!1,valueSet:null,pdfVersion:1.7}},tl=Object.keys(th),tf=[],td=0,tp=0,tg=0;function d(tn,ti){var ta,to=!1;for(ta=0;ta<tn.length;ta+=1)tn[ta]===ti&&(to=!0);return to}if(void 0===this.internal.viewerpreferences&&(this.internal.viewerpreferences={},this.internal.viewerpreferences.configuration=JSON.parse(JSON.stringify(th)),this.internal.viewerpreferences.isSubscribed=!1),ta=this.internal.viewerpreferences.configuration,"reset"===tn||!0===ti){var tm=tl.length;for(tg=0;tg<tm;tg+=1)ta[tl[tg]].value=ta[tl[tg]].defaultValue,ta[tl[tg]].explicitSet=!1}if("object"===(0,to.Z)(tn)){for(tc in tn)if(tu=tn[tc],d(tl,tc)&&void 0!==tu){if("boolean"===ta[tc].type&&"boolean"==typeof tu)ta[tc].value=tu;else if("name"===ta[tc].type&&d(ta[tc].valueSet,tu))ta[tc].value=tu;else if("integer"===ta[tc].type&&Number.isInteger(tu))ta[tc].value=tu;else if("array"===ta[tc].type){for(td=0;td<tu.length;td+=1)if(ts=!0,1===tu[td].length&&"number"==typeof tu[td][0])tf.push(String(tu[td]-1));else if(tu[td].length>1){for(tp=0;tp<tu[td].length;tp+=1)"number"!=typeof tu[td][tp]&&(ts=!1);!0===ts&&tf.push([tu[td][0]-1,tu[td][1]-1].join(" "))}ta[tc].value="["+tf.join(" ")+"]"}else ta[tc].value=ta[tc].defaultValue;ta[tc].explicitSet=!0}}return!1===this.internal.viewerpreferences.isSubscribed&&(this.internal.events.subscribe("putCatalog",function(){var tn,ti=[];for(tn in ta)!0===ta[tn].explicitSet&&("name"===ta[tn].type?ti.push("/"+tn+" /"+ta[tn].value):ti.push("/"+tn+" "+ta[tn].value));0!==ti.length&&this.internal.write("/ViewerPreferences\n<<\n"+ti.join("\n")+"\n>>")}),this.internal.viewerpreferences.isSubscribed=!0),this.internal.viewerpreferences.configuration=ta,this},eX=E.API,eZ=function(){var tn='<rdf:RDF xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#"><rdf:Description rdf:about="" xmlns:jspdf="'+this.internal.__metadata__.namespaceuri+'"><jspdf:metadata>',ti=unescape(encodeURIComponent('<x:xmpmeta xmlns:x="adobe:ns:meta/">')),ta=unescape(encodeURIComponent(tn)),to=unescape(encodeURIComponent(this.internal.__metadata__.metadata)),ts=unescape(encodeURIComponent("</jspdf:metadata></rdf:Description></rdf:RDF>")),tc=unescape(encodeURIComponent("</x:xmpmeta>")),tu=ta.length+to.length+ts.length+ti.length+tc.length;this.internal.__metadata__.metadata_object_number=this.internal.newObject(),this.internal.write("<< /Type /Metadata /Subtype /XML /Length "+tu+" >>"),this.internal.write("stream"),this.internal.write(ti+ta+to+ts+tc),this.internal.write("endstream"),this.internal.write("endobj")},eK=function(){this.internal.__metadata__.metadata_object_number&&this.internal.write("/Metadata "+this.internal.__metadata__.metadata_object_number+" 0 R")},eX.addMetadata=function(tn,ti){return void 0===this.internal.__metadata__&&(this.internal.__metadata__={metadata:tn,namespaceuri:ti||"http://jspdf.default.namespaceuri/"},this.internal.events.subscribe("putCatalog",eK),this.internal.events.subscribe("postPutResources",eZ)),this},eQ=(e$=E.API).pdfEscape16=function(tn,ti){for(var ta,to=ti.metadata.Unicode.widths,ts=["","0","00","000","0000"],tc=[""],tu=0,th=tn.length;tu<th&&(ta=ti.metadata.characterToGlyph(tn.charCodeAt(tu)),ti.metadata.glyIdsUsed.push(ta),ti.metadata.toUnicode[ta]=tn.charCodeAt(tu),-1==to.indexOf(ta)&&(to.push(ta),to.push([parseInt(ti.metadata.widthOfGlyph(ta),10)])),"0"!=ta);++tu)tc.push(ts[4-(ta=ta.toString(16)).length],ta);return tc.join("")},e1=function(tn){var ti,ta,to,ts,tc,tu,th;for(tc="/CIDInit /ProcSet findresource begin\n12 dict begin\nbegincmap\n/CIDSystemInfo <<\n  /Registry (Adobe)\n  /Ordering (UCS)\n  /Supplement 0\n>> def\n/CMapName /Adobe-Identity-UCS def\n/CMapType 2 def\n1 begincodespacerange\n<0000><ffff>\nendcodespacerange",to=[],tu=0,th=(ta=Object.keys(tn).sort(function(tn,ti){return tn-ti})).length;tu<th;tu++)ti=ta[tu],to.length>=100&&(tc+="\n"+to.length+" beginbfchar\n"+to.join("\n")+"\nendbfchar",to=[]),void 0!==tn[ti]&&null!==tn[ti]&&"function"==typeof tn[ti].toString&&(ts=("0000"+tn[ti].toString(16)).slice(-4),ti=("0000"+(+ti).toString(16)).slice(-4),to.push("<"+ti+"><"+ts+">"));return to.length&&(tc+="\n"+to.length+" beginbfchar\n"+to.join("\n")+"\nendbfchar\n"),tc+="endcmap\nCMapName currentdict /CMap defineresource pop\nend\nend"},e$.events.push(["putFont",function(tn){!function(tn){var ti=tn.font,ta=tn.out,to=tn.newObject,ts=tn.putStream;if(ti.metadata instanceof E.API.TTFFont&&"Identity-H"===ti.encoding){for(var tc=ti.metadata.Unicode.widths,tu=ti.metadata.subset.encode(ti.metadata.glyIdsUsed,1),th="",tl=0;tl<tu.length;tl++)th+=String.fromCharCode(tu[tl]);var tf=to();ts({data:th,addLength1:!0,objectId:tf}),ta("endobj");var td=to();ts({data:e1(ti.metadata.toUnicode),addLength1:!0,objectId:td}),ta("endobj");var tp=to();ta("<<"),ta("/Type /FontDescriptor"),ta("/FontName /"+F(ti.fontName)),ta("/FontFile2 "+tf+" 0 R"),ta("/FontBBox "+E.API.PDFObject.convert(ti.metadata.bbox)),ta("/Flags "+ti.metadata.flags),ta("/StemV "+ti.metadata.stemV),ta("/ItalicAngle "+ti.metadata.italicAngle),ta("/Ascent "+ti.metadata.ascender),ta("/Descent "+ti.metadata.decender),ta("/CapHeight "+ti.metadata.capHeight),ta(">>"),ta("endobj");var tg=to();ta("<<"),ta("/Type /Font"),ta("/BaseFont /"+F(ti.fontName)),ta("/FontDescriptor "+tp+" 0 R"),ta("/W "+E.API.PDFObject.convert(tc)),ta("/CIDToGIDMap /Identity"),ta("/DW 1000"),ta("/Subtype /CIDFontType2"),ta("/CIDSystemInfo"),ta("<<"),ta("/Supplement 0"),ta("/Registry (Adobe)"),ta("/Ordering ("+ti.encoding+")"),ta(">>"),ta(">>"),ta("endobj"),ti.objectNumber=to(),ta("<<"),ta("/Type /Font"),ta("/Subtype /Type0"),ta("/ToUnicode "+td+" 0 R"),ta("/BaseFont /"+F(ti.fontName)),ta("/Encoding /"+ti.encoding),ta("/DescendantFonts ["+tg+" 0 R]"),ta(">>"),ta("endobj"),ti.isAlreadyPutted=!0}}(tn)}]),e$.events.push(["putFont",function(tn){!function(tn){var ti=tn.font,ta=tn.out,to=tn.newObject,ts=tn.putStream;if(ti.metadata instanceof E.API.TTFFont&&"WinAnsiEncoding"===ti.encoding){for(var tc=ti.metadata.rawData,tu="",th=0;th<tc.length;th++)tu+=String.fromCharCode(tc[th]);var tl=to();ts({data:tu,addLength1:!0,objectId:tl}),ta("endobj");var tf=to();ts({data:e1(ti.metadata.toUnicode),addLength1:!0,objectId:tf}),ta("endobj");var td=to();ta("<<"),ta("/Descent "+ti.metadata.decender),ta("/CapHeight "+ti.metadata.capHeight),ta("/StemV "+ti.metadata.stemV),ta("/Type /FontDescriptor"),ta("/FontFile2 "+tl+" 0 R"),ta("/Flags 96"),ta("/FontBBox "+E.API.PDFObject.convert(ti.metadata.bbox)),ta("/FontName /"+F(ti.fontName)),ta("/ItalicAngle "+ti.metadata.italicAngle),ta("/Ascent "+ti.metadata.ascender),ta(">>"),ta("endobj"),ti.objectNumber=to();for(var tp=0;tp<ti.metadata.hmtx.widths.length;tp++)ti.metadata.hmtx.widths[tp]=parseInt(ti.metadata.hmtx.widths[tp]*(1e3/ti.metadata.head.unitsPerEm));ta("<</Subtype/TrueType/Type/Font/ToUnicode "+tf+" 0 R/BaseFont/"+F(ti.fontName)+"/FontDescriptor "+td+" 0 R/Encoding/"+ti.encoding+" /FirstChar 29 /LastChar 255 /Widths "+E.API.PDFObject.convert(ti.metadata.hmtx.widths)+">>"),ta("endobj"),ti.isAlreadyPutted=!0}}(tn)}]),e2=function(tn){var ti,ta=tn.text||"",to=tn.x,ts=tn.y,tc=tn.options||{},tu=tn.mutex||{},th=tu.pdfEscape,tl=tu.activeFontKey,tf=tu.fonts,td=tl,tp="",tg=0,tm="",tv=tf[td].encoding;if("Identity-H"!==tf[td].encoding)return{text:ta,x:to,y:ts,options:tc,mutex:tu};for(tm=ta,td=tl,Array.isArray(ta)&&(tm=ta[0]),tg=0;tg<tm.length;tg+=1)tf[td].metadata.hasOwnProperty("cmap")&&(ti=tf[td].metadata.cmap.unicode.codeMap[tm[tg].charCodeAt(0)]),ti||256>tm[tg].charCodeAt(0)&&tf[td].metadata.hasOwnProperty("Unicode")?tp+=tm[tg]:tp+="";var tb="";return 14>parseInt(td.slice(1))||"WinAnsiEncoding"===tv?tb=th(tp,td).split("").map(function(tn){return tn.charCodeAt(0).toString(16)}).join(""):"Identity-H"===tv&&(tb=eQ(tp,tf[td])),tu.isHex=!0,{text:tb,x:to,y:ts,options:tc,mutex:tu}},e$.events.push(["postProcessText",function(tn){var ti=tn.text||"",ta=[],to={text:ti,x:tn.x,y:tn.y,options:tn.options,mutex:tn.mutex};if(Array.isArray(ti)){var ts=0;for(ts=0;ts<ti.length;ts+=1)Array.isArray(ti[ts])&&3===ti[ts].length?ta.push([e2(Object.assign({},to,{text:ti[ts][0]})).text,ti[ts][1],ti[ts][2]]):ta.push(e2(Object.assign({},to,{text:ti[ts]})).text);tn.text=ta}else tn.text=e2(Object.assign({},to,{text:ti})).text}]),e5=E.API,e0=function(){return void 0===this.internal.vFS&&(this.internal.vFS={}),!0},e5.existsFileInVFS=function(tn){return e0.call(this),void 0!==this.internal.vFS[tn]},e5.addFileToVFS=function(tn,ti){return e0.call(this),this.internal.vFS[tn]=ti,this},e5.getFileFromVFS=function(tn){return e0.call(this),void 0!==this.internal.vFS[tn]?this.internal.vFS[tn]:null},/**
 * @license
 * Unicode Bidi Engine based on the work of Alex Shensis (@asthensis)
 * MIT License
 */function(tn){tn.__bidiEngine__=tn.prototype.__bidiEngine__=function(tn){var ta,to,ts,tc,tu,th,tl,tf=[[0,3,0,1,0,0,0],[0,3,0,1,2,2,0],[0,3,0,17,2,0,1],[0,3,5,5,4,1,0],[0,3,21,21,4,0,1],[0,3,5,5,4,2,0]],td=[[2,0,1,1,0,1,0],[2,0,1,1,0,2,0],[2,0,2,1,3,2,0],[2,0,2,33,3,1,1]],tp={L:0,R:1,EN:2,AN:3,N:4,B:5,S:6},tg={0:0,5:1,6:2,7:3,32:4,251:5,254:6,255:7},tm=["(",")","(","<",">","<","[","]","[","{","}","{","\xab","\xbb","\xab","‹","›","‹","⁅","⁆","⁅","⁽","⁾","⁽","₍","₎","₍","≤","≥","≤","〈","〉","〈","﹙","﹚","﹙","﹛","﹜","﹛","﹝","﹞","﹝","﹤","﹥","﹤"],tv=new RegExp(/^([1-4|9]|1[0-9]|2[0-9]|3[0168]|4[04589]|5[012]|7[78]|159|16[0-9]|17[0-2]|21[569]|22[03489]|250)$/),tb=!1,ty=0;this.__bidiEngine__={};var b=function(tn){var ta=tn.charCodeAt(),to=ta>>8,ts=tg[to];return void 0!==ts?ti[256*ts+(255&ta)]:252===to||253===to?"AL":tv.test(to)?"L":8===to?"R":"N"},y=function(tn){for(var ti,ta=0;ta<tn.length&&"L"!==(ti=b(tn.charAt(ta)));ta++)if("R"===ti)return!0;return!1},w=function(tn,ti,tu,th){var tl,tf,td,tp,tg=ti[th];switch(tg){case"L":case"R":case"LRE":case"RLE":case"LRO":case"RLO":case"PDF":tb=!1;break;case"N":case"AN":break;case"EN":tb&&(tg="AN");break;case"AL":tb=!0,tg="R";break;case"WS":case"BN":tg="N";break;case"CS":th<1||th+1>=ti.length||"EN"!==(tl=tu[th-1])&&"AN"!==tl||"EN"!==(tf=ti[th+1])&&"AN"!==tf?tg="N":tb&&(tf="AN"),tg=tf===tl?tf:"N";break;case"ES":tg="EN"===(tl=th>0?tu[th-1]:"B")&&th+1<ti.length&&"EN"===ti[th+1]?"EN":"N";break;case"ET":if(th>0&&"EN"===tu[th-1]){tg="EN";break}if(tb){tg="N";break}for(td=th+1,tp=ti.length;td<tp&&"ET"===ti[td];)td++;tg=td<tp&&"EN"===ti[td]?"EN":"N";break;case"NSM":if(ts&&!tc){for(tp=ti.length,td=th+1;td<tp&&"NSM"===ti[td];)td++;if(td<tp){var tm=tn[th];if(tl=ti[td],(tm>=1425&&tm<=2303||64286===tm)&&("R"===tl||"AL"===tl)){tg="R";break}}}tg=th<1||"B"===(tl=ti[th-1])?"N":tu[th-1];break;case"B":tb=!1,ta=!0,tg=ty;break;case"S":to=!0,tg="N"}return tg},N=function(tn,ti,ta){var to=tn.split("");return ta&&L(to,ta,{hiLevel:ty}),to.reverse(),ti&&ti.reverse(),to.join("")},L=function(tn,ti,ts){var tc,tu,th,tl,tg,tm=-1,tv=tn.length,tw=0,tN=[],tL=ty?td:tf,tx=[];for(tb=!1,ta=!1,to=!1,tu=0;tu<tv;tu++)tx[tu]=b(tn[tu]);for(th=0;th<tv;th++){if(tg=tw,tN[th]=w(tn,tx,tN,th),tc=240&(tw=tL[tg][tp[tN[th]]]),tw&=15,ti[th]=tl=tL[tw][5],tc>0){if(16===tc){for(tu=tm;tu<th;tu++)ti[tu]=1;tm=-1}else tm=-1}if(tL[tw][6])-1===tm&&(tm=th);else if(tm>-1){for(tu=tm;tu<th;tu++)ti[tu]=tl;tm=-1}"B"===tx[th]&&(ti[th]=0),ts.hiLevel|=tl}to&&function(tn,ti,ta){for(var to=0;to<ta;to++)if("S"===tn[to]){ti[to]=ty;for(var ts=to-1;ts>=0&&"WS"===tn[ts];ts--)ti[ts]=ty}}(tx,ti,tv)},A=function(tn,ti,to,ts,tc){if(!(tc.hiLevel<tn)){if(1===tn&&1===ty&&!ta)return ti.reverse(),void(to&&to.reverse());for(var tu,th,tl,tf,td=ti.length,tp=0;tp<td;){if(ts[tp]>=tn){for(tl=tp+1;tl<td&&ts[tl]>=tn;)tl++;for(tf=tp,th=tl-1;tf<th;tf++,th--)tu=ti[tf],ti[tf]=ti[th],ti[th]=tu,to&&(tu=to[tf],to[tf]=to[th],to[th]=tu);tp=tl}tp++}}},x=function(tn,ti,ta){var to=tn.split(""),ts={hiLevel:ty};return ta||(ta=[]),L(to,ta,ts),function(tn,ti,ta){if(0!==ta.hiLevel&&tl)for(var to,ts=0;ts<tn.length;ts++)1===ti[ts]&&(to=tm.indexOf(tn[ts]))>=0&&(tn[ts]=tm[to+1])}(to,ta,ts),A(2,to,ti,ta,ts),A(1,to,ti,ta,ts),to.join("")};return this.__bidiEngine__.doBidiReorder=function(tn,ti,ta){if(function(tn,ti){if(ti)for(var ta=0;ta<tn.length;ta++)ti[ta]=ta;void 0===tc&&(tc=y(tn)),void 0===th&&(th=y(tn))}(tn,ti),ts||!tu||th){if(ts&&tu&&tc^th)ty=tc?1:0,tn=N(tn,ti,ta);else if(!ts&&tu&&th)ty=tc?1:0,tn=N(tn=x(tn,ti,ta),ti);else if(!ts||tc||tu||th){if(ts&&!tu&&tc^th)tn=N(tn,ti),tc?(ty=0,tn=x(tn,ti,ta)):(ty=1,tn=N(tn=x(tn,ti,ta),ti));else if(ts&&tc&&!tu&&th)ty=1,tn=N(tn=x(tn,ti,ta),ti);else if(!ts&&!tu&&tc^th){var to=tl;tc?(ty=1,tn=x(tn,ti,ta),ty=0,tl=!1,tn=x(tn,ti,ta),tl=to):(ty=0,tn=N(tn=x(tn,ti,ta),ti),ty=1,tl=!1,tn=x(tn,ti,ta),tl=to,tn=N(tn,ti))}}else ty=0,tn=x(tn,ti,ta)}else ty=tc?1:0,tn=x(tn,ti,ta);return tn},this.__bidiEngine__.setOptions=function(tn){tn&&(ts=tn.isInputVisual,tu=tn.isOutputVisual,tc=tn.isInputRtl,th=tn.isOutputRtl,tl=tn.isSymmetricSwapping)},this.__bidiEngine__.setOptions(tn),this.__bidiEngine__};var ti=["BN","BN","BN","BN","BN","BN","BN","BN","BN","S","B","S","WS","B","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","B","B","B","S","WS","N","N","ET","ET","ET","N","N","N","N","N","ES","CS","ES","CS","CS","EN","EN","EN","EN","EN","EN","EN","EN","EN","EN","CS","N","N","N","N","N","N","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","N","N","N","N","N","N","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","N","N","N","N","BN","BN","BN","BN","BN","BN","B","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","CS","N","ET","ET","ET","ET","N","N","N","N","L","N","N","BN","N","N","ET","ET","EN","EN","N","L","N","N","N","EN","L","N","N","N","N","N","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","N","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","N","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","N","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","N","N","L","L","L","L","L","L","L","N","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","N","L","N","N","N","N","N","ET","N","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","R","NSM","R","NSM","NSM","R","NSM","NSM","R","NSM","N","N","N","N","N","N","N","N","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","N","N","N","N","N","R","R","R","R","R","N","N","N","N","N","N","N","N","N","N","N","AN","AN","AN","AN","AN","AN","N","N","AL","ET","ET","AL","CS","AL","N","N","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","AL","AL","N","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","AN","AN","AN","AN","AN","AN","AN","AN","AN","AN","ET","AN","AN","AL","AL","AL","NSM","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","NSM","NSM","NSM","NSM","NSM","NSM","NSM","AN","N","NSM","NSM","NSM","NSM","NSM","NSM","AL","AL","NSM","NSM","N","NSM","NSM","NSM","NSM","AL","AL","EN","EN","EN","EN","EN","EN","EN","EN","EN","EN","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","N","AL","AL","NSM","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","N","N","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","AL","N","N","N","N","N","N","N","N","N","N","N","N","N","N","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","R","R","N","N","N","N","R","N","N","N","N","N","WS","WS","WS","WS","WS","WS","WS","WS","WS","WS","WS","BN","BN","BN","L","R","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","WS","B","LRE","RLE","PDF","LRO","RLO","CS","ET","ET","ET","ET","ET","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","CS","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","WS","BN","BN","BN","BN","BN","N","LRI","RLI","FSI","PDI","BN","BN","BN","BN","BN","BN","EN","L","N","N","EN","EN","EN","EN","EN","EN","ES","ES","N","N","N","L","EN","EN","EN","EN","EN","EN","EN","EN","EN","EN","ES","ES","N","N","N","N","L","L","L","L","L","L","L","L","L","L","L","L","L","N","N","N","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","L","L","L","L","L","L","L","N","N","N","N","N","N","N","N","N","N","N","N","L","L","L","L","L","N","N","N","N","N","R","NSM","R","R","R","R","R","R","R","R","R","R","ES","R","R","R","R","R","R","R","R","R","R","R","R","R","N","R","R","R","R","R","N","R","N","R","R","N","R","R","N","R","R","R","R","R","R","R","R","R","R","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","CS","N","CS","N","N","CS","N","N","N","N","N","N","N","N","N","ET","N","N","ES","ES","N","N","N","N","N","ET","ET","N","N","N","N","N","AL","AL","AL","AL","AL","N","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","N","N","BN","N","N","N","ET","ET","ET","N","N","N","N","N","ES","CS","ES","CS","CS","EN","EN","EN","EN","EN","EN","EN","EN","EN","EN","CS","N","N","N","N","N","N","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","N","N","N","N","N","N","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","N","N","N","N","N","N","N","N","N","N","N","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","N","N","N","L","L","L","L","L","L","N","N","L","L","L","L","L","L","N","N","L","L","L","L","L","L","N","N","L","L","L","N","N","N","ET","ET","N","N","N","ET","ET","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N"],ta=new tn.__bidiEngine__({isInputVisual:!0});tn.API.events.push(["postProcessText",function(tn){var ti=tn.text,to=(tn.x,tn.y,tn.options||{}),ts=(tn.mutex,to.lang,[]);if(to.isInputVisual="boolean"!=typeof to.isInputVisual||to.isInputVisual,ta.setOptions(to),"[object Array]"===Object.prototype.toString.call(ti)){var tc=0;for(ts=[],tc=0;tc<ti.length;tc+=1)"[object Array]"===Object.prototype.toString.call(ti[tc])?ts.push([ta.doBidiReorder(ti[tc][0]),ti[tc][1],ti[tc][2]]):ts.push([ta.doBidiReorder(ti[tc])]);tn.text=ts}else tn.text=ta.doBidiReorder(ti);ta.setOptions({isInputVisual:!0})}])}(E),E.API.TTFFont=function(){function t(tn){var ti;if(this.rawData=tn,ti=this.contents=new e4(tn),this.contents.pos=4,"ttcf"===ti.readString(4))throw Error("TTCF not supported.");ti.pos=0,this.parse(),this.subset=new rg(this),this.registerTTF()}return t.open=function(tn){return new t(tn)},t.prototype.parse=function(){return this.directory=new e6(this.contents),this.head=new e7(this),this.name=new rs(this),this.cmap=new rn(this),this.toUnicode={},this.hhea=new ri(this),this.maxp=new rc(this),this.hmtx=new ru(this),this.post=new ro(this),this.os2=new ra(this),this.loca=new rp(this),this.glyf=new rl(this),this.ascender=this.os2.exists&&this.os2.ascender||this.hhea.ascender,this.decender=this.os2.exists&&this.os2.decender||this.hhea.decender,this.lineGap=this.os2.exists&&this.os2.lineGap||this.hhea.lineGap,this.bbox=[this.head.xMin,this.head.yMin,this.head.xMax,this.head.yMax]},t.prototype.registerTTF=function(){var tn,ti,ta,to,ts;if(this.scaleFactor=1e3/this.head.unitsPerEm,this.bbox=(function(){var ti,ta,to,ts;for(ts=[],ti=0,ta=(to=this.bbox).length;ti<ta;ti++)tn=to[ti],ts.push(Math.round(tn*this.scaleFactor));return ts}).call(this),this.stemV=0,this.post.exists?(ta=255&(to=this.post.italic_angle),0!=(32768&(ti=to>>16))&&(ti=-(1+(65535^ti))),this.italicAngle=+(ti+"."+ta)):this.italicAngle=0,this.ascender=Math.round(this.ascender*this.scaleFactor),this.decender=Math.round(this.decender*this.scaleFactor),this.lineGap=Math.round(this.lineGap*this.scaleFactor),this.capHeight=this.os2.exists&&this.os2.capHeight||this.ascender,this.xHeight=this.os2.exists&&this.os2.xHeight||0,this.familyClass=(this.os2.exists&&this.os2.familyClass||0)>>8,this.isSerif=1===(ts=this.familyClass)||2===ts||3===ts||4===ts||5===ts||7===ts,this.isScript=10===this.familyClass,this.flags=0,this.post.isFixedPitch&&(this.flags|=1),this.isSerif&&(this.flags|=2),this.isScript&&(this.flags|=8),0!==this.italicAngle&&(this.flags|=64),this.flags|=32,!this.cmap.unicode)throw Error("No unicode cmap for font")},t.prototype.characterToGlyph=function(tn){var ti;return(null!=(ti=this.cmap.unicode)?ti.codeMap[tn]:void 0)||0},t.prototype.widthOfGlyph=function(tn){var ti;return ti=1e3/this.head.unitsPerEm,this.hmtx.forGlyph(tn).advance*ti},t.prototype.widthOfString=function(tn,ti,ta){var to,ts,tc,tu;for(tc=0,ts=0,tu=(tn=""+tn).length;0<=tu?ts<tu:ts>tu;ts=0<=tu?++ts:--ts)to=tn.charCodeAt(ts),tc+=this.widthOfGlyph(this.characterToGlyph(to))+ta*(1e3/ti)||0;return tc*(ti/1e3)},t.prototype.lineHeight=function(tn,ti){var ta;return null==ti&&(ti=!1),ta=ti?this.lineGap:0,(this.ascender+ta-this.decender)/1e3*tn},t}();var eB,eM,eq,eD,eR,eT,eU,ez,eV,eH,eW,eG,eJ,eY,eX,eZ,eK,e$,eQ,e1,e2,e5,e0,e3,e4=function(){function t(tn){this.data=null!=tn?tn:[],this.pos=0,this.length=this.data.length}return t.prototype.readByte=function(){return this.data[this.pos++]},t.prototype.writeByte=function(tn){return this.data[this.pos++]=tn},t.prototype.readUInt32=function(){return 16777216*this.readByte()+(this.readByte()<<16)+(this.readByte()<<8)+this.readByte()},t.prototype.writeUInt32=function(tn){return this.writeByte(tn>>>24&255),this.writeByte(tn>>16&255),this.writeByte(tn>>8&255),this.writeByte(255&tn)},t.prototype.readInt32=function(){var tn;return(tn=this.readUInt32())>=2147483648?tn-4294967296:tn},t.prototype.writeInt32=function(tn){return tn<0&&(tn+=4294967296),this.writeUInt32(tn)},t.prototype.readUInt16=function(){return this.readByte()<<8|this.readByte()},t.prototype.writeUInt16=function(tn){return this.writeByte(tn>>8&255),this.writeByte(255&tn)},t.prototype.readInt16=function(){var tn;return(tn=this.readUInt16())>=32768?tn-65536:tn},t.prototype.writeInt16=function(tn){return tn<0&&(tn+=65536),this.writeUInt16(tn)},t.prototype.readString=function(tn){var ti,ta;for(ta=[],ti=0;0<=tn?ti<tn:ti>tn;ti=0<=tn?++ti:--ti)ta[ti]=String.fromCharCode(this.readByte());return ta.join("")},t.prototype.writeString=function(tn){var ti,ta,to;for(to=[],ti=0,ta=tn.length;0<=ta?ti<ta:ti>ta;ti=0<=ta?++ti:--ti)to.push(this.writeByte(tn.charCodeAt(ti)));return to},t.prototype.readShort=function(){return this.readInt16()},t.prototype.writeShort=function(tn){return this.writeInt16(tn)},t.prototype.readLongLong=function(){var tn,ti,ta,to,ts,tc,tu,th;return tn=this.readByte(),ti=this.readByte(),ta=this.readByte(),to=this.readByte(),ts=this.readByte(),tc=this.readByte(),tu=this.readByte(),th=this.readByte(),128&tn?-1*(72057594037927940*(255^tn)+281474976710656*(255^ti)+1099511627776*(255^ta)+4294967296*(255^to)+16777216*(255^ts)+65536*(255^tc)+256*(255^tu)+(255^th)+1):72057594037927940*tn+281474976710656*ti+1099511627776*ta+4294967296*to+16777216*ts+65536*tc+256*tu+th},t.prototype.writeLongLong=function(tn){var ti,ta;return ti=Math.floor(tn/4294967296),ta=**********&tn,this.writeByte(ti>>24&255),this.writeByte(ti>>16&255),this.writeByte(ti>>8&255),this.writeByte(255&ti),this.writeByte(ta>>24&255),this.writeByte(ta>>16&255),this.writeByte(ta>>8&255),this.writeByte(255&ta)},t.prototype.readInt=function(){return this.readInt32()},t.prototype.writeInt=function(tn){return this.writeInt32(tn)},t.prototype.read=function(tn){var ti,ta;for(ti=[],ta=0;0<=tn?ta<tn:ta>tn;ta=0<=tn?++ta:--ta)ti.push(this.readByte());return ti},t.prototype.write=function(tn){var ti,ta,to,ts;for(ts=[],ta=0,to=tn.length;ta<to;ta++)ti=tn[ta],ts.push(this.writeByte(ti));return ts},t}(),e6=function(){var tn;function e(tn){var ti,ta,to;for(this.scalarType=tn.readInt(),this.tableCount=tn.readShort(),this.searchRange=tn.readShort(),this.entrySelector=tn.readShort(),this.rangeShift=tn.readShort(),this.tables={},ta=0,to=this.tableCount;0<=to?ta<to:ta>to;ta=0<=to?++ta:--ta)ti={tag:tn.readString(4),checksum:tn.readInt(),offset:tn.readInt(),length:tn.readInt()},this.tables[ti.tag]=ti}return e.prototype.encode=function(ti){var ta,to,ts,tc,tu,th,tl,tf,td,tp,tg,tm,tv;for(tv in tc=Math.floor((td=16*Math.floor(Math.log(tg=Object.keys(ti).length)/(th=Math.log(2))))/th),tf=16*tg-td,(to=new e4).writeInt(this.scalarType),to.writeShort(tg),to.writeShort(td),to.writeShort(tc),to.writeShort(tf),ts=16*tg,tl=to.pos+ts,tu=null,tm=[],ti)for(tp=ti[tv],to.writeString(tv),to.writeInt(tn(tp)),to.writeInt(tl),to.writeInt(tp.length),tm=tm.concat(tp),"head"===tv&&(tu=tl),tl+=tp.length;tl%4;)tm.push(0),tl++;return to.write(tm),ta=2981146554-tn(to.data),to.pos=tu+8,to.writeUInt32(ta),to.data},tn=function(tn){var ti,ta,to,ts;for(tn=rh.call(tn);tn.length%4;)tn.push(0);for(to=new e4(tn),ta=0,ti=0,ts=tn.length;ti<ts;ti=ti+=4)ta+=to.readUInt32();return **********&ta},e}(),e8={}.hasOwnProperty,oe=function(tn,ti){for(var ta in ti)e8.call(ti,ta)&&(tn[ta]=ti[ta]);function n(){this.constructor=tn}return n.prototype=ti.prototype,tn.prototype=new n,tn.__super__=ti.prototype,tn};e3=function(){function t(tn){var ti;this.file=tn,ti=this.file.directory.tables[this.tag],this.exists=!!ti,ti&&(this.offset=ti.offset,this.length=ti.length,this.parse(this.file.contents))}return t.prototype.parse=function(){},t.prototype.encode=function(){},t.prototype.raw=function(){return this.exists?(this.file.contents.pos=this.offset,this.file.contents.read(this.length)):null},t}();var e7=function(tn){function e(){return e.__super__.constructor.apply(this,arguments)}return oe(e,e3),e.prototype.tag="head",e.prototype.parse=function(tn){return tn.pos=this.offset,this.version=tn.readInt(),this.revision=tn.readInt(),this.checkSumAdjustment=tn.readInt(),this.magicNumber=tn.readInt(),this.flags=tn.readShort(),this.unitsPerEm=tn.readShort(),this.created=tn.readLongLong(),this.modified=tn.readLongLong(),this.xMin=tn.readShort(),this.yMin=tn.readShort(),this.xMax=tn.readShort(),this.yMax=tn.readShort(),this.macStyle=tn.readShort(),this.lowestRecPPEM=tn.readShort(),this.fontDirectionHint=tn.readShort(),this.indexToLocFormat=tn.readShort(),this.glyphDataFormat=tn.readShort()},e.prototype.encode=function(tn){var ti;return(ti=new e4).writeInt(this.version),ti.writeInt(this.revision),ti.writeInt(this.checkSumAdjustment),ti.writeInt(this.magicNumber),ti.writeShort(this.flags),ti.writeShort(this.unitsPerEm),ti.writeLongLong(this.created),ti.writeLongLong(this.modified),ti.writeShort(this.xMin),ti.writeShort(this.yMin),ti.writeShort(this.xMax),ti.writeShort(this.yMax),ti.writeShort(this.macStyle),ti.writeShort(this.lowestRecPPEM),ti.writeShort(this.fontDirectionHint),ti.writeShort(tn),ti.writeShort(this.glyphDataFormat),ti.data},e}(),e9=function(){function t(tn,ti){var ta,to,ts,tc,tu,th,tl,tf,td,tp,tg,tm,tv,tb,ty,tw;switch(this.platformID=tn.readUInt16(),this.encodingID=tn.readShort(),this.offset=ti+tn.readInt(),td=tn.pos,tn.pos=this.offset,this.format=tn.readUInt16(),this.length=tn.readUInt16(),this.language=tn.readUInt16(),this.isUnicode=3===this.platformID&&1===this.encodingID&&4===this.format||0===this.platformID&&4===this.format,this.codeMap={},this.format){case 0:for(th=0;th<256;++th)this.codeMap[th]=tn.readByte();break;case 4:for(tp=tn.readUInt16()/2,tn.pos+=6,ts=function(){var ti,ta;for(ta=[],th=ti=0;0<=tp?ti<tp:ti>tp;th=0<=tp?++ti:--ti)ta.push(tn.readUInt16());return ta}(),tn.pos+=2,tm=function(){var ti,ta;for(ta=[],th=ti=0;0<=tp?ti<tp:ti>tp;th=0<=tp?++ti:--ti)ta.push(tn.readUInt16());return ta}(),tl=function(){var ti,ta;for(ta=[],th=ti=0;0<=tp?ti<tp:ti>tp;th=0<=tp?++ti:--ti)ta.push(tn.readUInt16());return ta}(),tf=function(){var ti,ta;for(ta=[],th=ti=0;0<=tp?ti<tp:ti>tp;th=0<=tp?++ti:--ti)ta.push(tn.readUInt16());return ta}(),to=(this.length-tn.pos+this.offset)/2,tu=function(){var ti,ta;for(ta=[],th=ti=0;0<=to?ti<to:ti>to;th=0<=to?++ti:--ti)ta.push(tn.readUInt16());return ta}(),th=tb=0,tw=ts.length;tb<tw;th=++tb)for(tv=ts[th],ta=ty=tg=tm[th];tg<=tv?ty<=tv:ty>=tv;ta=tg<=tv?++ty:--ty)0===tf[th]?tc=ta+tl[th]:0!==(tc=tu[tf[th]/2+(ta-tg)-(tp-th)]||0)&&(tc+=tl[th]),this.codeMap[ta]=65535&tc}tn.pos=td}return t.encode=function(tn,ti){var ta,to,ts,tc,tu,th,tl,tf,td,tp,tg,tm,tv,tb,ty,tw,tN,tL,tx,tA,tS,t_,tP,tk,tF,tI,tC,tj,tO,tE,tB,tM,tq,tD,tR,tT,tU,tz,tV,tH,tW,tG,tJ,tY,tX,tZ;switch(tj=new e4,tc=Object.keys(tn).sort(function(tn,ti){return tn-ti}),ti){case"macroman":for(tv=0,tb=function(){var tn=[];for(tm=0;tm<256;++tm)tn.push(0);return tn}(),tw={0:0},ts={},tO=0,tq=tc.length;tO<tq;tO++)null==tw[tJ=tn[to=tc[tO]]]&&(tw[tJ]=++tv),ts[to]={old:tn[to],new:tw[tn[to]]},tb[to]=tw[tn[to]];return tj.writeUInt16(1),tj.writeUInt16(0),tj.writeUInt32(12),tj.writeUInt16(0),tj.writeUInt16(262),tj.writeUInt16(0),tj.write(tb),{charMap:ts,subtable:tj.data,maxGlyphID:tv+1};case"unicode":for(tI=[],td=[],tN=0,tw={},ta={},ty=tl=null,tE=0,tD=tc.length;tE<tD;tE++)null==tw[tx=tn[to=tc[tE]]]&&(tw[tx]=++tN),ta[to]={old:tx,new:tw[tx]},tu=tw[tx]-to,null!=ty&&tu===tl||(ty&&td.push(ty),tI.push(to),tl=tu),ty=to;for(ty&&td.push(ty),td.push(65535),tI.push(65535),tk=2*(tP=tI.length),tp=Math.log((t_=2*Math.pow(Math.log(tP)/Math.LN2,2))/2)/Math.LN2,tS=2*tP-t_,th=[],tA=[],tg=[],tm=tB=0,tR=tI.length;tB<tR;tm=++tB){if(tF=tI[tm],tf=td[tm],65535===tF){th.push(0),tA.push(0);break}if(tF-(tC=ta[tF].new)>=32768)for(th.push(0),tA.push(2*(tg.length+tP-tm)),to=tM=tF;tF<=tf?tM<=tf:tM>=tf;to=tF<=tf?++tM:--tM)tg.push(ta[to].new);else th.push(tC-tF),tA.push(0)}for(tj.writeUInt16(3),tj.writeUInt16(1),tj.writeUInt32(12),tj.writeUInt16(4),tj.writeUInt16(16+8*tP+2*tg.length),tj.writeUInt16(0),tj.writeUInt16(tk),tj.writeUInt16(t_),tj.writeUInt16(tp),tj.writeUInt16(tS),tW=0,tT=td.length;tW<tT;tW++)to=td[tW],tj.writeUInt16(to);for(tj.writeUInt16(0),tG=0,tU=tI.length;tG<tU;tG++)to=tI[tG],tj.writeUInt16(to);for(tY=0,tz=th.length;tY<tz;tY++)tu=th[tY],tj.writeUInt16(tu);for(tX=0,tV=tA.length;tX<tV;tX++)tL=tA[tX],tj.writeUInt16(tL);for(tZ=0,tH=tg.length;tZ<tH;tZ++)tv=tg[tZ],tj.writeUInt16(tv);return{charMap:ta,subtable:tj.data,maxGlyphID:tN+1}}},t}(),rn=function(tn){function e(){return e.__super__.constructor.apply(this,arguments)}return oe(e,e3),e.prototype.tag="cmap",e.prototype.parse=function(tn){var ti,ta,to;for(tn.pos=this.offset,this.version=tn.readUInt16(),to=tn.readUInt16(),this.tables=[],this.unicode=null,ta=0;0<=to?ta<to:ta>to;ta=0<=to?++ta:--ta)ti=new e9(tn,this.offset),this.tables.push(ti),ti.isUnicode&&null==this.unicode&&(this.unicode=ti);return!0},e.encode=function(tn,ti){var ta,to;return null==ti&&(ti="macroman"),ta=e9.encode(tn,ti),(to=new e4).writeUInt16(0),to.writeUInt16(1),ta.table=to.data.concat(ta.subtable),ta},e}(),ri=function(tn){function e(){return e.__super__.constructor.apply(this,arguments)}return oe(e,e3),e.prototype.tag="hhea",e.prototype.parse=function(tn){return tn.pos=this.offset,this.version=tn.readInt(),this.ascender=tn.readShort(),this.decender=tn.readShort(),this.lineGap=tn.readShort(),this.advanceWidthMax=tn.readShort(),this.minLeftSideBearing=tn.readShort(),this.minRightSideBearing=tn.readShort(),this.xMaxExtent=tn.readShort(),this.caretSlopeRise=tn.readShort(),this.caretSlopeRun=tn.readShort(),this.caretOffset=tn.readShort(),tn.pos+=8,this.metricDataFormat=tn.readShort(),this.numberOfMetrics=tn.readUInt16()},e}(),ra=function(tn){function e(){return e.__super__.constructor.apply(this,arguments)}return oe(e,e3),e.prototype.tag="OS/2",e.prototype.parse=function(tn){if(tn.pos=this.offset,this.version=tn.readUInt16(),this.averageCharWidth=tn.readShort(),this.weightClass=tn.readUInt16(),this.widthClass=tn.readUInt16(),this.type=tn.readShort(),this.ySubscriptXSize=tn.readShort(),this.ySubscriptYSize=tn.readShort(),this.ySubscriptXOffset=tn.readShort(),this.ySubscriptYOffset=tn.readShort(),this.ySuperscriptXSize=tn.readShort(),this.ySuperscriptYSize=tn.readShort(),this.ySuperscriptXOffset=tn.readShort(),this.ySuperscriptYOffset=tn.readShort(),this.yStrikeoutSize=tn.readShort(),this.yStrikeoutPosition=tn.readShort(),this.familyClass=tn.readShort(),this.panose=function(){var ti,ta;for(ta=[],ti=0;ti<10;++ti)ta.push(tn.readByte());return ta}(),this.charRange=function(){var ti,ta;for(ta=[],ti=0;ti<4;++ti)ta.push(tn.readInt());return ta}(),this.vendorID=tn.readString(4),this.selection=tn.readShort(),this.firstCharIndex=tn.readShort(),this.lastCharIndex=tn.readShort(),this.version>0&&(this.ascent=tn.readShort(),this.descent=tn.readShort(),this.lineGap=tn.readShort(),this.winAscent=tn.readShort(),this.winDescent=tn.readShort(),this.codePageRange=function(){var ti,ta;for(ta=[],ti=0;ti<2;ti=++ti)ta.push(tn.readInt());return ta}(),this.version>1))return this.xHeight=tn.readShort(),this.capHeight=tn.readShort(),this.defaultChar=tn.readShort(),this.breakChar=tn.readShort(),this.maxContext=tn.readShort()},e}(),ro=function(tn){function e(){return e.__super__.constructor.apply(this,arguments)}return oe(e,e3),e.prototype.tag="post",e.prototype.parse=function(tn){var ti,ta,to,ts;switch(tn.pos=this.offset,this.format=tn.readInt(),this.italicAngle=tn.readInt(),this.underlinePosition=tn.readShort(),this.underlineThickness=tn.readShort(),this.isFixedPitch=tn.readInt(),this.minMemType42=tn.readInt(),this.maxMemType42=tn.readInt(),this.minMemType1=tn.readInt(),this.maxMemType1=tn.readInt(),this.format){case 65536:case 196608:break;case 131072:for(ta=tn.readUInt16(),this.glyphNameIndex=[],ts=0;0<=ta?ts<ta:ts>ta;ts=0<=ta?++ts:--ts)this.glyphNameIndex.push(tn.readUInt16());for(this.names=[],to=[];tn.pos<this.offset+this.length;)ti=tn.readByte(),to.push(this.names.push(tn.readString(ti)));return to;case 151552:return ta=tn.readUInt16(),this.offsets=tn.read(ta);case 262144:return this.map=(function(){var ti,ta,to;for(to=[],ts=ti=0,ta=this.file.maxp.numGlyphs;0<=ta?ti<ta:ti>ta;ts=0<=ta?++ti:--ti)to.push(tn.readUInt32());return to}).call(this)}},e}(),de=function(tn,ti){this.raw=tn,this.length=tn.length,this.platformID=ti.platformID,this.encodingID=ti.encodingID,this.languageID=ti.languageID},rs=function(tn){function e(){return e.__super__.constructor.apply(this,arguments)}return oe(e,e3),e.prototype.tag="name",e.prototype.parse=function(tn){var ti,ta,to,ts,tc,tu,th,tl,tf,td,tp;for(tn.pos=this.offset,tn.readShort(),ti=tn.readShort(),tu=tn.readShort(),ta=[],ts=0;0<=ti?ts<ti:ts>ti;ts=0<=ti?++ts:--ts)ta.push({platformID:tn.readShort(),encodingID:tn.readShort(),languageID:tn.readShort(),nameID:tn.readShort(),length:tn.readShort(),offset:this.offset+tu+tn.readShort()});for(th={},ts=tf=0,td=ta.length;tf<td;ts=++tf)to=ta[ts],tn.pos=to.offset,tl=tn.readString(to.length),tc=new de(tl,to),null==th[tp=to.nameID]&&(th[tp]=[]),th[to.nameID].push(tc);this.strings=th,this.copyright=th[0],this.fontFamily=th[1],this.fontSubfamily=th[2],this.uniqueSubfamily=th[3],this.fontName=th[4],this.version=th[5];try{this.postscriptName=th[6][0].raw.replace(/[\x00-\x19\x80-\xff]/g,"")}catch(tn){this.postscriptName=th[4][0].raw.replace(/[\x00-\x19\x80-\xff]/g,"")}return this.trademark=th[7],this.manufacturer=th[8],this.designer=th[9],this.description=th[10],this.vendorUrl=th[11],this.designerUrl=th[12],this.license=th[13],this.licenseUrl=th[14],this.preferredFamily=th[15],this.preferredSubfamily=th[17],this.compatibleFull=th[18],this.sampleText=th[19]},e}(),rc=function(tn){function e(){return e.__super__.constructor.apply(this,arguments)}return oe(e,e3),e.prototype.tag="maxp",e.prototype.parse=function(tn){return tn.pos=this.offset,this.version=tn.readInt(),this.numGlyphs=tn.readUInt16(),this.maxPoints=tn.readUInt16(),this.maxContours=tn.readUInt16(),this.maxCompositePoints=tn.readUInt16(),this.maxComponentContours=tn.readUInt16(),this.maxZones=tn.readUInt16(),this.maxTwilightPoints=tn.readUInt16(),this.maxStorage=tn.readUInt16(),this.maxFunctionDefs=tn.readUInt16(),this.maxInstructionDefs=tn.readUInt16(),this.maxStackElements=tn.readUInt16(),this.maxSizeOfInstructions=tn.readUInt16(),this.maxComponentElements=tn.readUInt16(),this.maxComponentDepth=tn.readUInt16()},e}(),ru=function(tn){function e(){return e.__super__.constructor.apply(this,arguments)}return oe(e,e3),e.prototype.tag="hmtx",e.prototype.parse=function(tn){var ti,ta,to,ts,tc,tu,th;for(tn.pos=this.offset,this.metrics=[],ti=0,tu=this.file.hhea.numberOfMetrics;0<=tu?ti<tu:ti>tu;ti=0<=tu?++ti:--ti)this.metrics.push({advance:tn.readUInt16(),lsb:tn.readInt16()});for(to=this.file.maxp.numGlyphs-this.file.hhea.numberOfMetrics,this.leftSideBearings=function(){var ta,ts;for(ts=[],ti=ta=0;0<=to?ta<to:ta>to;ti=0<=to?++ta:--ta)ts.push(tn.readInt16());return ts}(),this.widths=(function(){var tn,ti,ta,to;for(to=[],tn=0,ti=(ta=this.metrics).length;tn<ti;tn++)ts=ta[tn],to.push(ts.advance);return to}).call(this),ta=this.widths[this.widths.length-1],th=[],ti=tc=0;0<=to?tc<to:tc>to;ti=0<=to?++tc:--tc)th.push(this.widths.push(ta));return th},e.prototype.forGlyph=function(tn){return tn in this.metrics?this.metrics[tn]:{advance:this.metrics[this.metrics.length-1].advance,lsb:this.leftSideBearings[tn-this.metrics.length]}},e}(),rh=[].slice,rl=function(tn){function e(){return e.__super__.constructor.apply(this,arguments)}return oe(e,e3),e.prototype.tag="glyf",e.prototype.parse=function(){return this.cache={}},e.prototype.glyphFor=function(tn){var ti,ta,to,ts,tc,tu,th,tl,tf,td;return tn in this.cache?this.cache[tn]:(ts=this.file.loca,ti=this.file.contents,ta=ts.indexOf(tn),0===(to=ts.lengthOf(tn))?this.cache[tn]=null:(ti.pos=this.offset+ta,tc=(tu=new e4(ti.read(to))).readShort(),tl=tu.readShort(),td=tu.readShort(),th=tu.readShort(),tf=tu.readShort(),this.cache[tn]=-1===tc?new rd(tu,tl,td,th,tf):new rf(tu,tc,tl,td,th,tf),this.cache[tn]))},e.prototype.encode=function(tn,ti,ta){var to,ts,tc,tu,th;for(tc=[],ts=[],tu=0,th=ti.length;tu<th;tu++)to=tn[ti[tu]],ts.push(tc.length),to&&(tc=tc.concat(to.encode(ta)));return ts.push(tc.length),{table:tc,offsets:ts}},e}(),rf=function(){function t(tn,ti,ta,to,ts,tc){this.raw=tn,this.numberOfContours=ti,this.xMin=ta,this.yMin=to,this.xMax=ts,this.yMax=tc,this.compound=!1}return t.prototype.encode=function(){return this.raw.data},t}(),rd=function(){function t(tn,ti,ta,to,ts){var tc,tu;for(this.raw=tn,this.xMin=ti,this.yMin=ta,this.xMax=to,this.yMax=ts,this.compound=!0,this.glyphIDs=[],this.glyphOffsets=[],tc=this.raw;tu=tc.readShort(),this.glyphOffsets.push(tc.pos),this.glyphIDs.push(tc.readUInt16()),32&tu;)tc.pos+=1&tu?4:2,128&tu?tc.pos+=8:64&tu?tc.pos+=4:8&tu&&(tc.pos+=2)}return t.prototype.encode=function(){var tn,ti,ta;for(ti=new e4(rh.call(this.raw.data)),tn=0,ta=this.glyphIDs.length;tn<ta;++tn)ti.pos=this.glyphOffsets[tn];return ti.data},t}(),rp=function(tn){function e(){return e.__super__.constructor.apply(this,arguments)}return oe(e,e3),e.prototype.tag="loca",e.prototype.parse=function(tn){var ti,ta;return tn.pos=this.offset,ti=this.file.head.indexToLocFormat,this.offsets=0===ti?(function(){var ti,to;for(to=[],ta=0,ti=this.length;ta<ti;ta+=2)to.push(2*tn.readUInt16());return to}).call(this):(function(){var ti,to;for(to=[],ta=0,ti=this.length;ta<ti;ta+=4)to.push(tn.readUInt32());return to}).call(this)},e.prototype.indexOf=function(tn){return this.offsets[tn]},e.prototype.lengthOf=function(tn){return this.offsets[tn+1]-this.offsets[tn]},e.prototype.encode=function(tn,ti){for(var ta=new Uint32Array(this.offsets.length),to=0,ts=0,tc=0;tc<ta.length;++tc)if(ta[tc]=to,ts<ti.length&&ti[ts]==tc){++ts,ta[tc]=to;var tu=this.offsets[tc],th=this.offsets[tc+1]-tu;th>0&&(to+=th)}for(var tl=Array(4*ta.length),tf=0;tf<ta.length;++tf)tl[4*tf+3]=255&ta[tf],tl[4*tf+2]=(65280&ta[tf])>>8,tl[4*tf+1]=(16711680&ta[tf])>>16,tl[4*tf]=(**********&ta[tf])>>24;return tl},e}(),rg=function(){function t(tn){this.font=tn,this.subset={},this.unicodes={},this.next=33}return t.prototype.generateCmap=function(){var tn,ti,ta,to,ts;for(ti in to=this.font.cmap.tables[0].codeMap,tn={},ts=this.subset)ta=ts[ti],tn[ti]=to[ta];return tn},t.prototype.glyphsFor=function(tn){var ti,ta,to,ts,tc,tu,th;for(to={},tc=0,tu=tn.length;tc<tu;tc++)to[ts=tn[tc]]=this.font.glyf.glyphFor(ts);for(ts in ti=[],to)(null!=(ta=to[ts])?ta.compound:void 0)&&ti.push.apply(ti,ta.glyphIDs);if(ti.length>0)for(ts in th=this.glyphsFor(ti))ta=th[ts],to[ts]=ta;return to},t.prototype.encode=function(tn,ti){var ta,to,ts,tc,tu,th,tl,tf,td,tp,tg,tm,tv,tb,ty;for(to in ta=rn.encode(this.generateCmap(),"unicode"),tc=this.glyphsFor(tn),tg={0:0},ty=ta.charMap)tg[(th=ty[to]).old]=th.new;for(tm in tp=ta.maxGlyphID,tc)tm in tg||(tg[tm]=tp++);return td=Object.keys(tf=function(tn){var ti,ta;for(ti in ta={},tn)ta[tn[ti]]=ti;return ta}(tg)).sort(function(tn,ti){return tn-ti}),tv=function(){var tn,ti,ta;for(ta=[],tn=0,ti=td.length;tn<ti;tn++)tu=td[tn],ta.push(tf[tu]);return ta}(),ts=this.font.glyf.encode(tc,tv,tg),tl=this.font.loca.encode(ts.offsets,tv),tb={cmap:this.font.cmap.raw(),glyf:ts.table,loca:tl,hmtx:this.font.hmtx.raw(),hhea:this.font.hhea.raw(),maxp:this.font.maxp.raw(),post:this.font.post.raw(),name:this.font.name.raw(),head:this.font.head.encode(ti)},this.font.os2.exists&&(tb["OS/2"]=this.font.os2.raw()),this.font.directory.encode(tb)},t}();E.API.PDFObject=function(){var tn;function e(){}return tn=function(tn,ti){return(Array(ti+1).join("0")+tn).slice(-ti)},e.convert=function(ti){var ta,to,ts,tc;if(Array.isArray(ti))return"["+(function(){var tn,to,ts;for(ts=[],tn=0,to=ti.length;tn<to;tn++)ta=ti[tn],ts.push(e.convert(ta));return ts})().join(" ")+"]";if("string"==typeof ti)return"/"+ti;if(null!=ti?ti.isString:void 0)return"("+ti+")";if(ti instanceof Date)return"(D:"+tn(ti.getUTCFullYear(),4)+tn(ti.getUTCMonth(),2)+tn(ti.getUTCDate(),2)+tn(ti.getUTCHours(),2)+tn(ti.getUTCMinutes(),2)+tn(ti.getUTCSeconds(),2)+"Z)";if("[object Object]"===({}).toString.call(ti)){for(to in ts=["<<"],ti)tc=ti[to],ts.push("/"+to+" "+e.convert(tc));return ts.push(">>"),ts.join("\n")}return""+ti},e}(),ti.default=E}}]);