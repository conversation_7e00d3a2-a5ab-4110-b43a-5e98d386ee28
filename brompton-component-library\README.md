# Build and install locally

1. Download dependencies:
```bash
yarn install
```

2. Build library:
```bash
yarn build
```

3. Install locally:
```bash
yarn yalc publish
```

# Deployment

This library is packaged as a docker image to later be used as a base image.
It currently is the easiest way I found to make the library available without depending on a registry.

1. Update `package.json` version.

2. Build docker image for destination platform:
```bash
docker build --platform linux/amd64 . -t be-component-library:<VERSION>
```
