import * as React from 'react';
import { Global } from '@emotion/react';
import Box from '@mui/material/Box';

export interface DemoCanvasWidgetProps {
  color?: string;
  background?: string;
  children?: React.ReactNode;
}

export const DemoCanvasWidget: React.FC<DemoCanvasWidgetProps> = ({
  color = 'rgba(255,255,255, 0.05)',
  background = 'rgb(60, 60, 60)',
  children,
}) => {
  return (
    <>
      <Global
        styles={{
          html: { height: '100%' },
          body: { height: '100%' },
          '#root': { height: '100%' },
        }}
      />
      <Box
        sx={{
          height: '100%',
          '--tw-bg-opacity': 1,
          backgroundColor: `rgb(248 250 252 / var(--tw-bg-opacity))`,
          backgroundSize: '50px 50px',
          display: 'flex',
          '& > *': {
            height: '100%',
            minHeight: '100%',
            width: '100%',
          },
          backgroundImage: `linear-gradient(
            0deg,
            transparent 24%,
            ${color} 25%,
            ${color} 26%,
            transparent 27%,
            transparent 74%,
            ${color} 75%,
            ${color} 76%,
            transparent 77%,
            transparent
          ),
          linear-gradient(
            90deg,
            transparent 24%,
            ${color} 25%,
            ${color} 26%,
            transparent 27%,
            transparent 74%,
            ${color} 75%,
            ${color} 76%,
            transparent 77%,
            transparent
          )`,
        }}
      >
        {children}
      </Box>
    </>
  );
};
