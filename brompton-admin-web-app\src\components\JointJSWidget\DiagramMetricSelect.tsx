import { Box, FormControl, MenuItem, Select, SelectChangeEvent } from '@mui/material';
import { useSelector } from 'react-redux';
import { getMetricsIdToName } from '~/redux/selectors/dashboardSelectors';
import { elementVariable } from '~/types/diagram';

type DiagramMetricSelectProps = {
  variables: elementVariable;
  onVariablesChange: (updatedVariable: elementVariable) => void; // New Prop
};
const DiagramMetricSelect = ({ variables, onVariablesChange }: DiagramMetricSelectProps) => {
  const metricsIdToName = useSelector(getMetricsIdToName);
  const handleMeasureChange = (event: SelectChangeEvent<string>) => {
    const selectedDbMeasureId = event.target.value as string;
    onVariablesChange({
      ...variables,
      measurementId: selectedDbMeasureId,
    });
  };
  return (
    <Box sx={{ display: 'flex', gap: 2, p: 2 }}>
      <FormControl fullWidth>
        <Select
          sx={{
            width: '100%',
            p: 0.3,
            '& fieldset': {
              '& legend': {
                maxWidth: '100%',
                height: 'auto',
                '& span': {
                  opacity: 1,
                },
              },
            },
          }}
          value={variables.measurementId}
          onChange={handleMeasureChange}
          label={'Select Metric : ' + variables.label}
        >
          {Object.keys(metricsIdToName).map((dbMeasureId) => {
            return (
              <MenuItem key={dbMeasureId} value={dbMeasureId}>
                {metricsIdToName[dbMeasureId]}
              </MenuItem>
            );
          })}
        </Select>
      </FormControl>
    </Box>
  );
};

export default DiagramMetricSelect;
