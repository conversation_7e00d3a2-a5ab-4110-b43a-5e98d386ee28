const { test, expect, request } = require('@playwright/test');

test.describe('API Test Suite', () => {
  test('DELETE /customers/1/assets/2 deletes asset successfully', async ({ request }) => {
    // Set headers
    const headers = {
      'BE-CsrfToken': '5gHRkFPgKj2hv6HDPB0K0m2w5pHfWqM1jH/5r4zMH0Y=',
      Cookie:
        'BE-AccessToken=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VybmFtZSI6InRlc3QiLCJzdWIiOjIsInJvbGVzIjp7IkFETUlOIjpbMTI4LDgsMTIxLDExOSwxMDYsMTE4LDEwOCw4NiwxMTEsMTEyLDg1LDEwNyw4NCwxMTMsMTE0LDExNywxMjAsMTI3LDEyMywxMjYsMTI0LDEwOSwxMjUsMTIyLDExMCw5LDgyXSwiVVNFUiI6WzEyOCw4LDEyMSwxMTksMTA2LDExOCwxMDgsODYsMTExLDExMiw4NSwxMDcsODQsMTEzLDExNCwxMTcsMTIwLDEyNywxMjMsMTI2LDEyNCwxMDksMTI1LDEyMiwxMTAsOSw4Ml0sIlBPV0VSX1VTRVIiOlsxMjgsOCwxMjEsMTE5LDEwNiwxMTgsMTA4LDg2LDExMSwxMTIsODUsMTA3LDg0LDExMywxMTQsMTE3LDEyMCwxMjcsMTIzLDEyNiwxMjQsMTA5LDEyNSwxMjIsMTEwLDksODJdfSwiaWF0IjoxNzMxNTY1NTY4LCJleHAiOjE3MzE1NzI3Njh9.KkzlwJgDT6lwkXbFIX2353So18xvK11Frsc5MVJsR5I; BE-CSRFToken=5gHRkFPgKj2hv6HDPB0K0m2w5pHfWqM1jH%2F5r4zMH0Y%3D',
    };

    // Make DELETE request
    const response = await request.delete('https://test.brompton.ai/api/v0/customers/1/assets/2', {
      headers: headers,
    });

    // Log response details for debugging
    console.log(`Status: ${response.status()}`);
    console.log(`Response Body: ${await response.text()}`);

    // Check response status
    expect(response.status()).toBe(204); // Assuming 204 No Content indicates successful deletion

    // Optional verification
    const verifyResponse = await request.get(
      'https://test.brompton.ai/api/v0/customers/1/assets/2',
      {
        headers: headers,
      },
    );
    expect(verifyResponse.status()).toBe(404); // Assuming 404 Not Found indicates resource was deleted
  });
});
