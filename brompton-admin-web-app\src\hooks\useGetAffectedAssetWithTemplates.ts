import { ThunkDispatch } from '@reduxjs/toolkit';
import { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { assetsApi } from '~/redux/api/assetsApi';
import { getCustomerId } from '~/redux/selectors/customerSelectors';
import { RootState } from '~/redux/store';
import { AssetDo } from '~/types/asset';

type AffectedAsset = {
  asset: AssetDo;
  path: string;
};

const useGetAffectedAssetWithTemplates = ({ assets }: { assets: AssetDo[] }) => {
  const [affectedAssets, setAffectedAssets] = useState<AffectedAsset[]>([]);
  const dispatch = useDispatch<ThunkDispatch<RootState, any, any>>();
  const customerId = useSelector(getCustomerId);

  useEffect(() => {
    if (assets && assets.length > 0) {
      const fetchData = async () => {
        const affectedPaths: AffectedAsset[] = [];
        for (const asset of assets.filter((asset) => asset.customer_id !== null)) {
          const assetsPath = [];
          if (asset.parent_ids && asset.parent_ids.length > 0) {
            let parentIDs = asset.parent_ids.filter((id) => id !== undefined) as number[];
            while (parentIDs.length > 0) {
              for (const parentId of parentIDs) {
                const { data, isError } = await dispatch(
                  assetsApi.endpoints.getAssetById.initiate({
                    customerId: asset.customer_id!,
                    assetId: parentId.toString(),
                  }),
                );
                if (!data || isError) {
                  parentIDs = [];
                  break;
                }
                parentIDs = data.parent_ids.filter((id) => id !== undefined) as number[];
                assetsPath.push(data);
              }
            }
          }
          const path = [...assetsPath.reverse().map((a) => a.tag), asset.tag].join('\\');
          affectedPaths.push({ asset, path });
        }
        setAffectedAssets(affectedPaths);
      };

      fetchData();
    }
  }, [assets, dispatch]);

  return affectedAssets;
};

export default useGetAffectedAssetWithTemplates;
