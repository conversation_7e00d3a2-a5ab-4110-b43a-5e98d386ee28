import React from 'react';

import { ComponentStory, ComponentMeta } from '@storybook/react';

import { AssetNode, AssetsNodeProps } from 'components/AssetsTree/AssetNode';
import {
  TreeItem,
  TreeItemContentProps,
  TreeView,
  useTreeItem,
} from '@mui/lab';
import {
  ASSET_DEFAULT_ICON,
  COMPANY_DEFAULT_ICON,
  MEASUREMENT_DEFAULT_ICON,
  METRIC_DEFAULT_ICON,
} from './testing/mocks';

export default {
  title: 'AssetNode',
  component: AssetNode,
  decorators: [
    (Story) => (
      <TreeView>
        <Story />
      </TreeView>
    ),
  ],
  argTypes: {
    onExpandToggle: { action: 'onExpandToggle' },
  },
} as ComponentMeta<typeof AssetNode>;

const MockAssetNodeContainer = (args: AssetsNodeProps) =>
  React.forwardRef(function MockAssetNodeContainer(
    props: TreeItemContentProps,
    ref
  ) {
    const { nodeId } = props;

    const treeItemContext = useTreeItem(nodeId);

    return <AssetNode ref={ref} {...props} {...treeItemContext} {...args} />;
  });

const Template: ComponentStory<typeof AssetNode> = (args) => (
  <TreeItem ContentComponent={MockAssetNodeContainer(args)} nodeId="1" />
);

export const AssetItemNode = Template.bind({});

AssetItemNode.args = {
  label: 'California',
  expanded: false,
  selected: false,
  nodeConfig: {
    icon: ASSET_DEFAULT_ICON,
    contextMenuActions: [],
    expandable: true,
    selectable: true,
  },
  checkbox: true,
};

export const NoCheckboxAssetItemNode = Template.bind({});

NoCheckboxAssetItemNode.args = {
  ...AssetItemNode.args,
  checkbox: false,
};

export const ExpandedAssetNode = Template.bind({});

ExpandedAssetNode.args = {
  ...AssetItemNode.args,
  expanded: true,
};

export const MetricNode = Template.bind({});

MetricNode.args = {
  label: 'Line Voltage',
  expanded: false,
  selected: false,
  nodeConfig: {
    icon: METRIC_DEFAULT_ICON,
    contextMenuActions: [],
    expandable: false,
    selectable: true,
  },
  checkbox: true,
};

export const CheckedMetricNode = Template.bind({});

CheckedMetricNode.args = {
  ...MetricNode.args,
  selected: true,
};

export const MeasurementNode = Template.bind({});

MeasurementNode.args = {
  label: 'Power',
  expanded: false,
  selected: false,
  nodeConfig: {
    icon: MEASUREMENT_DEFAULT_ICON,
    contextMenuActions: [],
    expandable: true,
    selectable: true,
  },
  checkbox: true,
};

export const CompanyNode = Template.bind({});

CompanyNode.args = {
  label: 'Stark Industries',
  expanded: false,
  selected: false,
  nodeConfig: {
    icon: COMPANY_DEFAULT_ICON,
    contextMenuActions: [],
    expandable: true,
    selectable: false,
  },
  checkbox: true,
};
