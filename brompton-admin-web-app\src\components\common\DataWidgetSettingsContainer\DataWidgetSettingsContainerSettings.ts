import { Widgets } from '~/types/widgets';

export type DataWidgetSettingsContainerProps<T extends Widgets> = {
  settings: T;
  setSettings: (value: ((prevState: T) => T) | T) => void;
  children?: React.ReactNode;
  dataTabChildren?: React.ReactNode;
  feelTabChidren?: React.ReactNode;
  timeContextChildren?: React.ReactNode;
  titleSettingsElement?: React.ReactNode;
  exculdedSettings?: {
    [key: string]: boolean;
  };
  hideSettings?: {
    [key: string]: boolean;
  };
};

export type WidgetTimeContextProps<T extends Widgets> = {
  showTimeRangeError: boolean;
  settings: T;
  setSettings: (value: ((prevState: T) => T) | T) => void;
  children?: React.ReactNode;
  disabledRealTime?: boolean;
  disabledAssetTz?: boolean;
  exculdedSettings?: {
    [key: string]: boolean;
  };
  hideSettings?: {
    [key: string]: boolean;
  };
};

export type WidgetDataSettingsTabContainerProps<T extends Widgets> = {
  settings: T;
  setSettings: (value: ((prevState: T) => T) | T) => void;
  children?: React.ReactNode;
  hideSettings?: {
    [key: string]: boolean;
  };
};

export type WidgetLookFeelContainerProps<T extends Widgets> = {
  settings: T;
  setSettings: (value: ((prevState: T) => T) | T) => void;
  hideSettings?: {
    [key: string]: boolean;
  };
  children?: React.ReactNode;
};
