const { test, expect, request } = require('@playwright/test');

test.describe('API Test Suite', () => {
  test('GET /customers/82/assets retrieves assets based on parentIds', async ({ request }) => {
    // Set headers
    const headers = {
      'BE-CsrfToken': 'PN9+SuuxqF2eNLUqoPsNrwv/V4/MrCcE20jjfALv4bE=',
      Cookie:
        'BE-AccessToken=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VybmFtZSI6InRlc3QiLCJzdWIiOjIsInJvbGVzIjp7IkFETUlOIjpbMTI4LDgsMTIxLDExOSwxMDYsMTE4LDEwOCw4NiwxMTEsMTEyLDg1LDEwNyw4NCwxMTMsMTE0LDExNywxMjAsMTI3LDEyMywxMjYsMTI0LDEwOSwxMjUsMTIyLDExMCw5LDgyXSwiVVNFUiI6WzEyOCw4LDEyMSwxMTksMTA2LDExOCwxMDgsODYsMTExLDExMiw4NSwxMDcsODQsMTEzLDExNCwxMTcsMTIwLDEyNywxMjMsMTI2LDEyNCwxMDksMTI1LDEyMiwxMTAsOSw4Ml0sIlBPV0VSX1VTRVIiOlsxMjgsOCwxMjEsMTE5LDEwNiwxMTgsMTA4LDg2LDExMSwxMTIsODUsMTA3LDg0LDExMywxMTQsMTE3LDEyMCwxMjcsMTIzLDEyNiwxMjQsMTA5LDEyNSwxMjIsMTEwLDksODJdfSwiaWF0IjoxNzMxNTY2Mjc0LCJleHAiOjE3MzE1NzM0NzR9.JfJdeaXkx2P1Jf-80lWthylRy2QJPFlPgrahWj3EgQ8; BE-CSRFToken=PN9%2BSuuxqF2eNLUqoPsNrwv%2FV4%2FMrCcE20jjfALv4bE%3D',
    };

    // Make GET request
    const response = await request.get(
      'https://test.brompton.ai/api/v0/customers/82/assets?parentIds=-1,318&=',
      {
        headers: headers,
      },
    );

    // Check response status
    expect(response.status()).toBe(200);

    // Verify response body if needed
    const responseBody = await response.json();
    console.log(responseBody);

    // Perform assertions on the response data
    expect(responseBody).toHaveProperty('items'); // Check that 'items' exists
    expect(responseBody.items).toBeInstanceOf(Array); // Check if 'items' is an array
    expect(responseBody.total).toBe(5); // Verify the total count if known
  });
});
