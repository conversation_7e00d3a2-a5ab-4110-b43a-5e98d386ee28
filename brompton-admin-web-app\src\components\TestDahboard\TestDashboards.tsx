import { Box } from '@mui/material';
import { useRouter } from 'next/router';
import { useState } from 'react';
import ChartsContainer from './ChartsContainer';
import SearchForm from './SearchForm';
import { useCustomTimeInterval } from '~/hooks/useCustomTimeInterval';
import { TimeRangeOptions } from '~/types/dashboard';
import { getPreviousDate } from '~/utils/utils';

const TestDashboards = () => {
  const router = useRouter();
  const [selectedCustomer, setSelectedCustomer] = useState<string | null>(null);
  const [selectedAsset, setSelectedAsset] = useState<string | null>(null);
  const [selectedMeasure, setSelectedMeasure] = useState<string[]>([]);
  const [samplePeriod, setSamplePeriod] = useState<number>(1);
  const [aggregation, setAggregation] = useState<number>(1);
  const [refreshInterval, setRefreshTimeInterval] = useState<number>(-1);
  const [selectionTimeRange, setSelectionTimeRange] = useState<number>(0);
  const [rangeSlider, setRangeSlider] = useState<boolean>(true);
  const [chartType, setChartType] = useState<'scatter' | 'bar'>('scatter');
  const today = new Date();
  const [selectedMeasuresByAssetId, setSelectedMeasuresByAssetId] = useState<
    Record<string, string[]>
  >({});
  const [startDate, setStartDate] = useState<Date>(
    new Date(
      today.getFullYear(),
      today.getMonth(),
      today.getDate() - 7,
      today.getHours(),
      today.getMinutes(),
      today.getSeconds(),
    ),
  );
  const [endDate, setEndDate] = useState<Date>(new Date());

  const searchSubmit = (
    selectedMeasuresByAssetId: Record<string, string[]>,
    selectedMeasures: string[],
    customerId: string,
    assetId: string,
    startDate: Date,
    endDate: Date,
    chartType: 'scatter' | 'bar',
    aggregation: number,
    samplePeriod: number,
    refreshInterval: number,
    selectionTimeRange: number,
    rangeSlider: boolean,
  ) => {
    setSelectedMeasuresByAssetId(selectedMeasuresByAssetId);
    setSelectedCustomer(customerId);
    setSelectedMeasure(selectedMeasures);
    setSelectedAsset(assetId);
    setStartDate(startDate);
    setEndDate(endDate);
    setChartType(chartType);
    setAggregation(aggregation);
    setSamplePeriod(samplePeriod);
    setRefreshTimeInterval(refreshInterval);
    setSelectionTimeRange(selectionTimeRange);
    setRangeSlider(rangeSlider);
  };

  const colors = ['blue', 'red', 'black', 'green'];
  useCustomTimeInterval(() => {
    const minutes = TimeRangeOptions[selectionTimeRange].serverValue;
    const end = new Date().getTime();
    const startDate = getPreviousDate(minutes);
    setStartDate(new Date(startDate));
    setEndDate(new Date(end));
  }, refreshInterval);
  return (
    <Box sx={router.pathname === '/dynamic-chart' ? { p: 0, pt: 0 } : undefined}>
      <SearchForm searchSubmit={searchSubmit} start={startDate} end={endDate} />
      {Object.keys(selectedMeasuresByAssetId).map((assetId, i) => {
        const color = colors[i % colors.length];
        return (
          <>
            {selectedMeasuresByAssetId[assetId].map((measure, index) => (
              <ChartsContainer
                key={i + '_' + index}
                index={index}
                assetId={assetId}
                customerId={selectedCustomer ?? ''}
                endDate={endDate}
                startDate={startDate}
                measure={measure}
                selectedMeasure={selectedMeasure}
                assetToMeasureList={selectedMeasuresByAssetId}
                color={color as 'blue' | 'red' | 'black' | 'green'}
                aggregation={aggregation}
                samplePeriod={samplePeriod}
                chartType={chartType}
                rangeSlider={rangeSlider}
              />
            ))}
          </>
        );
      })}
    </Box>
  );
};

export default TestDashboards;
