import { test, expect } from '@playwright/test';
test('templateinstance', async ({ page }) => {
  // opening the URL
  await page.goto('https://test.pivotol.ai/login'); // 60 seconds
  // Go to the username  and password
  await page.getByLabel('Username *').click();
  await page.getByLabel('Username *').fill('normaltest');
  await page.getByLabel('Password *').click();
  await page.getByLabel('Password *').fill('password123');
  // click on Login Button
  await page.click('#__next > div > div > form > div > button');
  await page.waitForTimeout(3000);
  // click on new dashboard
  await page.getByText('Add Dashboard').click({ timeout: 60000 });
  await page.waitForTimeout(6000);
  // click on asset section
  await page.getByText('Assets').click();
  await page.waitForTimeout(6000);
  // click on add asset template
  await page.getByRole('link', { name: 'Add Asset template instance', exact: true }).click();
  await page.waitForTimeout(6000);
  await page.getByLabel('Asset type').click();
  await page.getByRole('option', { name: 'Renewable > Battery Bank (24)', exact: true }).click();
  //await page.getByTestId('ArrowDropDownIcon').nth(17).waitFor({ state: 'visible', timeout: 2000 });
  await page.waitForTimeout(6000);
  await page.click('//*[@id="__next"]/div/div[2]/div[2]/div[2]/div/div[1]/div[25]/div/div/button');
  await page.waitForTimeout(3000);
  //asset template instance
  await page.getByLabel('Unit Of Group *').click();
  await page.getByRole('option', { name: 'uk', exact: true }).click();
  await page.getByLabel('Prefix *').click();
  await page.getByLabel('Prefix *').fill('testsp');
  await page.getByLabel('Customer *').click();
  await page.getByRole('option', { name: 'Customer', exact: true }).click();
  await page.getByLabel('Tag *').click();
  await page.getByLabel('Tag *').fill('Atest');
  await page.getByLabel('Asset type *').click();
  await page.getByRole('option', { name: 'Renewable > Battery Bank (24)', exact: true }).click();
  await page.getByLabel('Parent Assets').click();
  await page.getByRole('option', { name: 'Asset_Template', exact: true }).click();
  await page.getByLabel('Select a time zone').click();
  await page.getByLabel('Select a time zone').fill('across');
  await page.getByLabel('Latitude').click();
  await page.getByLabel('Latitude').fill('0');
  await page.getByLabel('Longitude').click();
  await page.getByLabel('Longitude').fill('0');
  await page.getByLabel('Description').click();
  await page.getByLabel('Description').fill('tests');
  await page.click('//*[@id="__next"]/div/div[2]/div[2]/div[2]/div/div[2]/button[2]');
  await page.waitForTimeout(3000);
  await page.click('//*[@id="__next"]/div/div[2]/div[2]/div[2]/div/div[2]/button[2]');
  await page.waitForTimeout(3000);

  await page.close();
});
