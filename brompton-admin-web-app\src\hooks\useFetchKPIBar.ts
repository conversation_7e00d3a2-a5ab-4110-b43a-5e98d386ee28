import { ThunkDispatch } from '@reduxjs/toolkit';
import { useRouter } from 'next/router';
import { Data, Layout } from 'plotly.js';
import { useCallback, useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { measuresApi } from '~/redux/api/measuresApi';
import { timeseriesApi } from '~/redux/api/timeseriesApi';
import { getCustomerId } from '~/redux/selectors/customerSelectors';
import {
  getAssetTz,
  getGlobalSamplePeriod,
  getGlobalTimeRangeType,
} from '~/redux/selectors/topPanleSelectors';
import { getDbMeasureIdToAssetIdMap } from '~/redux/selectors/treeSelectors';
import { getThousandSeparator } from '~/redux/selectors/userPreferences';
import { getGlobalEndDate, getGlobalStartDate } from '~/redux/selectors/widgetSelectors';
import { RootState } from '~/redux/store';
import { AssetMeasurementDetails } from '~/types/measures';
import { SingleScatterTimeSeriesData } from '~/types/timeseries';
import { KPIBarChart } from '~/types/widgets';
import {
  aggGenerator,
  formatChartDate,
  formatChartDateToAssetTz,
  formatMetricLabel,
  formatNumber,
  roundNumber,
} from '~/utils/utils';

type MeasuresData = {
  tsData: SingleScatterTimeSeriesData;
  measureData: AssetMeasurementDetails;
};

const transformBarChartData = ({
  settings,
  data,
  useAssetTz,
  unitOfMeasure,
  thousandSeparator,
}: {
  settings: KPIBarChart;
  data: MeasuresData[];
  useAssetTz: boolean;
  unitOfMeasure: string;
  thousandSeparator: boolean;
}): {
  layout: Partial<Layout>;
  data: Data[]; // Replace 'Data[]' with the appropriate type or 'any[]' if the type is unknown
  percentageChange: string;
  avg: string;
} => {
  const traces: Data[] = [];
  const layout: Partial<Layout> = {
    showlegend: false,
    // title: data.map((res) => formatMetricLabel(res?.measureData?.tag || '')).join(' Vs. '),
    annotations: [],
    barmode: 'group',
    yaxis: {
      // title: 'Unit Values',
      side: 'left',
    },
    yaxis2: {
      // title: 'Unit Values',
      side: 'right',
      overlaying: 'y',
    },
  };
  if (settings.selectedSamplePeriod === 'M' || settings.selectedSamplePeriod === 'W') {
    layout.xaxis = {
      type: 'date',
      tickformat: '%b %d, %Y',
      dtick: 86400000,
    };
  }
  const filteredResults = data.filter((res) => res?.tsData['ts,val']?.length > 0);
  if (filteredResults.length === 0) {
    return {
      layout: {},
      data: [],
      percentageChange: 'N/A',
      avg: '-',
    };
  }
  const filteredResult = filteredResults[0];
  const tempTotal = filteredResult.tsData['ts,val']
    .map((item) => item[1])
    .reduce((acc, curr) => acc + curr, 0);
  const avg = tempTotal / filteredResult.tsData['ts,val'].length;
  let percentageChange: string;
  if (filteredResults.length < 2) {
    percentageChange = 'N/A';
  } else {
    const filteredResult2 = filteredResults[1] ?? {};
    const tempTotal2 = filteredResult2.tsData['ts,val']
      .map((item) => item[1])
      .reduce((acc, curr) => acc + curr, 0);
    const avg2 = tempTotal2 / filteredResult2.tsData['ts,val'].length;
    const change = avg - avg2;
    const percentage = (change / avg2) * 100;
    percentageChange = `${percentage.toFixed(2)}`;
  }
  const title = formatMetricLabel(filteredResult.measureData.tag || '');
  const allData = filteredResults.map((res) => res.tsData['ts,val']);
  const current = [...allData[0]].sort((a, b) => a[0] - b[0]);
  const x = current.map(([timestamp]) =>
    useAssetTz
      ? formatChartDateToAssetTz(new Date(timestamp))
      : formatChartDate(new Date(timestamp)),
  );
  const trace: Data = {
    x,
    y: current.map((item) => item[1]),
    type: 'bar',
    name: title,
    yaxis: 'y',
    ...(settings.overrideBarColor
      ? {
          marker: {
            color: settings.barColor,
          },
        }
      : undefined),
    customdata: x,
    hovertemplate: `${title}: %{y} ${unitOfMeasure}<br>Time: %{customdata}<extra></extra>'`,
  };
  if (settings.showPrevious && allData.length > 1) {
    const last = [...allData[1]].sort((a, b) => a[0] - b[0]);
    const newlast = last.map((item: [number, number], index: number) => {
      if (current[index]?.[1]) {
        return [current[index]?.[0], item[1]];
      }
      return item;
    });
    const custom = last.map(([timestamp]) =>
      useAssetTz
        ? formatChartDateToAssetTz(new Date(timestamp))
        : formatChartDate(new Date(timestamp)),
    );
    traces.push({
      x,
      y: newlast
        .map((item) => item[1])
        .map((value) => (thousandSeparator ? formatNumber(value) : roundNumber(value))),
      type: 'bar',
      name: title,
      yaxis: 'y',
      customdata: custom,
      hovertemplate: `${title}: %{y} ${unitOfMeasure}<br>Time: %{customdata}<extra></extra>'`,
    });
  }
  traces.push(trace);

  return {
    layout: layout,
    data: traces,
    percentageChange: percentageChange,
    avg: avg?.toString(),
  };
};
export const useFetchKPIBar = ({ settings }: { settings: KPIBarChart }) => {
  const dispatch = useDispatch<ThunkDispatch<RootState, any, any>>();
  const router = useRouter();
  const [selectedTitles, setSelectedTitles] = useState<string[]>([]);
  const thousandSeparator = useSelector(getThousandSeparator);
  useEffect(() => {
    if (
      settings.mode === 'dashboard' &&
      settings.assetMeasure?.assetId !== '' &&
      settings.assetMeasure?.measureId.some((measure) => measure !== '')
    ) {
      setSelectedTitles(settings.assetMeasure.measureId);
    }
    if (settings.mode === 'template') {
      if (settings.selectedDbMeasureId !== '') {
        setSelectedTitles([settings.selectedDbMeasureId]);
      } else {
        setSelectedTitles([]);
      }
    }
  }, [settings.assetMeasure, settings.selectedDbMeasureId, settings.mode]);
  const [resultData, setResultData] = useState<{
    data: MeasuresData[] | undefined;
    isLoading: boolean;
    isError: boolean;
    layout: Partial<Layout>;
    unitOfMeasure: string;
    chartData: Data[]; // Replace 'Data[]' with the appropriate type or 'any[]' if the type is unknown
    percentageChange: string;
    avg: string;
    dates: {
      start: number;
      end: number;
    }[];
  }>({
    data: undefined,
    isLoading: false,
    isError: false,
    layout: {},
    unitOfMeasure: '',
    chartData: [],
    avg: '-',
    percentageChange: '0',
    dates: [],
  });

  const customerId = useSelector(getCustomerId);
  const globalStartDate = useSelector(getGlobalStartDate);
  const globalEndDate = useSelector(getGlobalEndDate);
  const globalTimeRange = useSelector(getGlobalTimeRangeType);
  const globalSamplePeriod = useSelector(getGlobalSamplePeriod);
  const assetTz = useSelector(getAssetTz);
  const {
    startDate: selectedStartDate,
    endDate: selectedEndDate,
    aggBy: selectedAggBy,
    samplePeriod: selectedSamplePeriod,
    timeRange: selectedTimeRange,
    globalSamplePeriod: isGlobalSamplePeriodOverridden,
    overrideGlobalSettings: isGlobalTimeRangeSettingsOverridden,
    overrideAssetTz: isOverrideAssetTz,
    overrideAssetTzValue,
  } = settings;

  const [startDate, setStartDate] = useState(globalStartDate);
  const [endDate, setEndDate] = useState(globalEndDate);
  const [aggBy, setAggBy] = useState(selectedAggBy);
  const [samplePeriod, setSamplePeriod] = useState(globalSamplePeriod);
  const [timeRange, setTimeRange] = useState(globalTimeRange);
  const [assetTzOverride, setAssetTzOverride] = useState(isOverrideAssetTz);
  const [assetTzOverrideValue, setAssetTzOverrideValue] = useState(overrideAssetTzValue);
  const dbMeasureIdToAssetIdMap = useSelector(getDbMeasureIdToAssetIdMap);
  const useAssetTz = useSelector(getAssetTz);

  useEffect(() => {
    setAggBy(selectedAggBy);
    if (isGlobalTimeRangeSettingsOverridden) {
      setStartDate(selectedStartDate);
      setEndDate(selectedEndDate);
      setTimeRange(selectedTimeRange);
    } else {
      setStartDate(globalStartDate);
      setEndDate(globalEndDate);
      setTimeRange(globalTimeRange);
    }

    if (isGlobalSamplePeriodOverridden) {
      setSamplePeriod(selectedSamplePeriod);
    } else {
      setSamplePeriod(globalSamplePeriod);
    }
    if (isOverrideAssetTz) {
      setAssetTzOverride(true);
      setAssetTzOverrideValue(overrideAssetTzValue);
    } else {
      setAssetTzOverride(false);
    }
  }, [
    selectedStartDate,
    selectedEndDate,
    selectedAggBy,
    selectedSamplePeriod,
    selectedTimeRange,
    globalStartDate,
    globalEndDate,
    globalTimeRange,
    isGlobalTimeRangeSettingsOverridden,
    isOverrideAssetTz,
    overrideAssetTzValue,
  ]);

  const fetchMeasureData = useCallback(
    async (measureId: string) => {
      // if (!dbMeasureIdToAssetIdMap[measureId]) {
      //   throw new Error(`No assetId found for ${measureId}`);
      // }
      if (!measureId) {
        throw new Error(`Error invalid measureId: ${measureId}`);
      }
      const { data: measureData, isSuccess: isMeasureDataSuccess } = await dispatch(
        measuresApi.endpoints?.getMeasurementById.initiate({
          customerId,
          assetId: dbMeasureIdToAssetIdMap[measureId],
          measId: measureId,
        }),
      );
      if (!isMeasureDataSuccess || !measureData) {
        throw new Error(`Error fetching measure data for measureId: ${measureId}`);
      }

      return measureData;
    },
    [customerId, dbMeasureIdToAssetIdMap, dispatch],
  );

  const fetchMeasureDataByAsset = useCallback(
    async (assetId: string, measureId: string) => {
      if (!assetId || assetId === '') {
        throw new Error(`No assetId found for ${measureId}`);
      }
      if (!measureId) {
        throw new Error(`Error invalid measureId: ${measureId}`);
      }
      const { data: measureData, isSuccess: isMeasureDataSuccess } = await dispatch(
        measuresApi.endpoints?.getMeasurementById.initiate({
          customerId,
          assetId: assetId,
          measId: measureId,
        }),
      );
      if (!isMeasureDataSuccess || !measureData) {
        throw new Error(`Error fetching measure data for measureId: ${measureId}`);
      }

      return measureData;
    },
    [customerId, dbMeasureIdToAssetIdMap, dispatch, selectedTitles],
  );
  const fetchUnitOfMeasure = useCallback(
    async (typeId: number) => {
      const { data: unitOfMeasure, isSuccess: isUnitOfMeasureSuccess } = await dispatch(
        measuresApi.endpoints?.getUnitsOfMeasure.initiate({ measurementTypeId: typeId }),
      );
      if (!isUnitOfMeasureSuccess || !unitOfMeasure) {
        throw new Error(`Error fetching unit of measure for typeId: ${typeId}`);
      }
      return unitOfMeasure;
    },
    [dispatch],
  );

  const fetchTimeseriesData = useCallback(
    async (
      tsDbMeasureIds: number[],
      agg: {
        meas_id: number;
        start: number;
        end: number;
        agg: string;
        agg_period: string;
        use_asset_tz: boolean;
        browser_tz: boolean;
      },
    ): Promise<{ error: boolean; tsData: Record<number, SingleScatterTimeSeriesData> }> => {
      const { data: tsData, isSuccess: isTsSuccess } = await dispatch(
        timeseriesApi.endpoints.getMultiMeasurementSeries.initiate({
          customerId,
          ...agg,
          measId: tsDbMeasureIds.filter(Boolean).sort().join(','),
          timeRangeType: timeRange,
          assetTz: isOverrideAssetTz ? assetTzOverrideValue : assetTz,
        }),
      );
      if (!isTsSuccess || !tsData) {
        return {
          error: true,
          tsData: {},
        };
      }
      return { error: false, tsData };
    },
    [
      dispatch,
      customerId,
      timeRange,
      isOverrideAssetTz,
      assetTzOverrideValue,
      settings.selectedDbMeasureId,
      settings.selectedSamplePeriod,
      selectedTitles,
      settings.showPrevious,
      assetTz,
    ],
  );
  useEffect(() => {
    const fetchMeasuresData = async () => {
      setResultData({
        ...resultData,
        isLoading: true,
      });
      try {
        const allResults = await Promise.all(
          settings.assetMeasure.measureId
            .filter((measure) => measure !== '' && measure !== null)
            .map(async (measureId) => {
              const measureData = await fetchMeasureDataByAsset(
                settings.assetMeasure.assetId,
                measureId,
              );
              const unitOfMeasures = await fetchUnitOfMeasure(measureData.typeId);
              const uom =
                unitOfMeasures.find((unit) => unit.id === measureData.unitOfMeasureId)?.name ?? '';
              const aggs = aggGenerator(
                settings,
                measureData,
                aggBy,
                assetTzOverride,
                assetTzOverrideValue,
              );
              const response = await Promise.all(
                aggs.map(async (agg) => {
                  const { error, tsData } = await fetchTimeseriesData(
                    [measureData.measurementId],
                    agg,
                  );
                  if (error) {
                    return {
                      isLoading: false,
                      isError: true,
                      error: 'Error fetching timeseries data',
                      tsData: {} as SingleScatterTimeSeriesData,
                      measureData,
                    };
                  }
                  return {
                    isLoading: false,
                    isError: false,
                    error: '',
                    tsData: tsData[measureData.measurementId],
                    measureData,
                  };
                }),
              );
              return {
                response,
                uom,
                aggs,
              };
            }),
        );

        const results = allResults.flatMap((result) => result.response);
        const uom = allResults[0]?.uom ?? '';
        const aggs = allResults.flatMap((result) => result.aggs);

        setResultData({
          ...resultData,
          data: results.map((res) => ({
            tsData: res.tsData,
            measureData: res.measureData,
          })),
          unitOfMeasure: uom,
          dates: aggs.map((agg) => ({
            start: agg.start,
            end: agg.end,
          })),
        });
      } catch (e) {
        setResultData({
          data: undefined,
          isLoading: false,
          isError: true,
          layout: {},
          unitOfMeasure: '',
          avg: '-',
          chartData: [],
          percentageChange: '-',
          dates: [],
        });
      }
    };

    if (selectedTitles.length > 0) fetchMeasuresData();
  }, [
    customerId,
    dbMeasureIdToAssetIdMap,
    dispatch,
    fetchMeasureData,
    fetchTimeseriesData,
    fetchUnitOfMeasure,
    settings.selectedSamplePeriod,
    settings.selectedDbMeasureId,
    settings.showPrevious,
    timeRange,
    assetTz,
    assetTzOverride,
    assetTzOverrideValue,
    selectedTitles,
  ]);

  useEffect(() => {
    if (resultData && resultData.data && resultData.data.length > 0) {
      const data = transformBarChartData({
        settings,
        data: resultData.data,
        useAssetTz,
        unitOfMeasure: resultData.unitOfMeasure,
        thousandSeparator,
      });
      setResultData({
        ...resultData,
        avg: data.avg?.toString(),
        percentageChange: data.percentageChange,
        layout: data.layout,
        chartData: data.data,
        isLoading: false,
      });
    }
  }, [
    resultData.data,
    customerId,
    dbMeasureIdToAssetIdMap,
    dispatch,
    fetchMeasureData,
    fetchTimeseriesData,
    fetchUnitOfMeasure,
    settings.selectedSamplePeriod,
    settings.selectedDbMeasureId,
    timeRange,
    assetTz,
    assetTzOverride,
    assetTzOverrideValue,
    settings.overrideBarColor,
    settings.barColor,
    settings.showPrevious,
    thousandSeparator,
  ]);

  useEffect(() => {
    if (router.pathname === '/dashboard-template' && settings.selectedDbMeasureId !== '') {
      const dummyChartData: Data[] = [
        {
          x: ['Jan', 'Feb', 'Mar', 'Apr', 'May'],
          y: [
            Math.random() * 100,
            Math.random() * 100,
            Math.random() * 100,
            Math.random() * 100,
            Math.random() * 100,
          ],
          type: 'bar',
          name: 'Dummy Data',
        },
      ];

      const dummyDates = [
        { start: Date.now() - 30 * 24 * 60 * 60 * 1000, end: Date.now() }, // Last 30 days
      ];

      const dummyLayout: Partial<Layout> = {
        title: 'Dummy KPI Bar Chart',
        xaxis: { title: 'Months' },
        yaxis: { title: 'Values' },
      };

      setResultData({
        data: undefined,
        isLoading: false,
        isError: false,
        layout: dummyLayout,
        unitOfMeasure: '%',
        chartData: dummyChartData,
        avg: (Math.random() * 100)?.toFixed(2),
        percentageChange: (Math.random() * 10 - 5).toFixed(2), // Random percentage change (-5% to 5%)
        dates: dummyDates,
      });
    }
  }, [router.pathname, settings.selectedDbMeasureId]);

  return resultData;
};
