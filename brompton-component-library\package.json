{"name": "brompton-component-library", "version": "1.1.0", "description": "Component library for Brompton Energy products", "module": "dist/esm/index.js", "main": "dist/cjs/index.js", "types": "dist/index.d.ts", "files": ["dist/*"], "engines": {"node": "16.15.0"}, "scripts": {"typecheck": "tsc --noEmit", "test": "jest", "storybook": "start-storybook -p 6006", "snapshot": "jest --config ./jest.config.snap.ts", "build": "rollup -c", "lint": "eslint --cache --ignore-path ./.gitignore --ext .js,.jsx,.ts,.tsx .", "lint:fix": "yarn lint --fix"}, "author": "<PERSON><PERSON>", "license": "ISC", "devDependencies": {"@mui/icons-material": "^5.10.15", "@mui/lab": "^5.0.0-alpha.124", "@mui/material": "^5.11.15", "@rollup/plugin-commonjs": "^24.0.1", "@rollup/plugin-node-resolve": "^15.0.1", "@rollup/plugin-typescript": "^11.0.0", "@storybook/addon-actions": "^6.5.13", "@storybook/addon-essentials": "^6.5.13", "@storybook/addon-interactions": "^6.5.13", "@storybook/addon-links": "^6.5.13", "@storybook/addon-storyshots": "^6.5.14", "@storybook/addon-storyshots-puppeteer": "^6.5.14", "@storybook/builder-webpack5": "^6.5.13", "@storybook/manager-webpack5": "^6.5.13", "@storybook/react": "^6.5.13", "@storybook/testing-library": "^0.0.13", "@storybook/testing-react": "^1.3.0", "@swc/core": "^1.3.44", "@swc/jest": "^0.2.24", "@testing-library/dom": "^9.2.0", "@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^14.0.0", "@testing-library/user-event": "^14.4.3", "@types/jest": "^29.5.0", "@types/node": "^18.15.11", "@types/react": "^18.0.31", "@typescript-eslint/eslint-plugin": "^5.57.0", "@typescript-eslint/parser": "^5.57.0", "eslint": "^8.37.0", "eslint-config-prettier": "^8.8.0", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-react": "^7.32.2", "eslint-plugin-react-hooks": "^4.6.0", "identity-obj-proxy": "^3.0.0", "jest": "^29.5.0", "jest-environment-jsdom": "^29.5.0", "prettier": "^2.8.7", "puppeteer": "^3.3.0", "react": "^18.2.0", "react-dom": "^18.2.0", "rollup": "^3.26.1", "rollup-plugin-dts": "^5.3.1", "rollup-plugin-peer-deps-external": "^2.2.4", "rollup-plugin-postcss": "^4.0.2", "rollup-plugin-terser": "^7.0.2", "ts-jest": "^29.0.5", "ts-loader": "^9.4.2", "ts-node": "^10.9.1", "tsconfig-paths-webpack-plugin": "^4.0.1", "tslib": "^2.5.0", "typescript": "^4.9.5", "yalc": "^1.0.0-pre.53"}, "dependencies": {"@emotion/css": "^11.1.3", "@emotion/react": "^11.10.5", "@emotion/styled": "^11.10.5", "@fontsource/roboto": "^4.5.8", "react-inlinesvg": "^3.0.1"}, "peerDependencies": {"react": "^18.2.0", "@mui/icons-material": "^5.10.15", "@mui/lab": "^5.0.0-alpha.124", "@mui/material": "^5.11.15"}}