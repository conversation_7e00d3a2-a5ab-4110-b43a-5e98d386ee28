import { Box, IconButton, Typography } from '@mui/material';
import CommonWidgetContainer from '~/components/common/CommonWidgetContainer';
import { Weather } from '~/types/widgets';
import ThunderstormIcon from '@mui/icons-material/Thunderstorm';
import WaterDropIcon from '@mui/icons-material/WaterDrop';
import AirIcon from '@mui/icons-material/Air';
import SouthEastIcon from '@mui/icons-material/SouthEast';
import LightModeIcon from '@mui/icons-material/LightMode';
import ExploreIcon from '@mui/icons-material/Explore';
import WeatherWidgetDialog from './WeatherWidgetDialog';
import { useState } from 'react';
import { useFetchWeather } from '~/hooks/useFetchWeather';
import Loader from '~/components/common/Loader';
import { NoDataFound } from '~/components/common/NoDataFound';
type NewWeatherContainerProps = {
  id: string;
  settings: Weather;
};

const NewWeatherContainer = ({ id, settings }: NewWeatherContainerProps) => {
  const [opensettings, setOpensettings] = useState(false);
  const { weatherData, isLoading, isError } = useFetchWeather({ setting: settings });
  return (
    <CommonWidgetContainer
      id={id}
      settings={settings}
      widgetType="Weather"
      widgetName="Weather"
      customSettingsDialogOpen={opensettings}
      setCustomSettingsDialogOpen={setOpensettings}
      customSettingsDialog={
        <>
          <WeatherWidgetDialog
            id={id}
            settings={settings}
            open={opensettings}
            onClose={() => setOpensettings(false)}
          />
        </>
      }
      widgetContent={
        <Box
          sx={{
            height: '100%',
            width: '100%',
          }}
        >
          {settings.stationId === '' ? (
            <Box
              sx={{
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
                width: '100%',
                height: '100%',
              }}
            >
              <Typography>Enter Station ID</Typography>
            </Box>
          ) : (
            <>
              {isLoading ? (
                <Loader />
              ) : (
                <>
                  {isError ? (
                    <Box
                      sx={{
                        display: 'flex',
                        justifyContent: 'center',
                        height: '100%',
                        alignItems: 'center',
                      }}
                    >
                      <Typography>Error while fetching data</Typography>
                    </Box>
                  ) : (
                    <>
                      {weatherData ? (
                        <>
                          <Box
                            sx={{
                              display: 'flex',
                              justifyContent: 'space-around',
                            }}
                          >
                            <Typography variant="h1">
                              {weatherData.air_temperature} °{weatherData.station_units.units_temp}
                            </Typography>
                            <IconButton color="inherit" disableRipple>
                              <ThunderstormIcon sx={{ fontSize: '6rem' }} />
                            </IconButton>
                          </Box>
                          <Box
                            sx={{
                              display: 'flex',
                              justifyContent: 'space-around',
                            }}
                          >
                            <IconButton color="inherit" disableRipple>
                              <Typography>
                                Feels Like {weatherData.feels_like} °
                                {weatherData.station_units.units_temp}
                              </Typography>
                            </IconButton>
                            <IconButton color="inherit" disableRipple>
                              <Typography>{weatherData.conditions}</Typography>
                            </IconButton>
                          </Box>

                          <Box
                            sx={{
                              display: 'flex',
                              justifyContent: 'space-around',
                            }}
                          >
                            <Box>
                              <IconButton color="inherit" disableRipple>
                                <WaterDropIcon />
                                <Typography>{weatherData.relative_humidity}% Humidity</Typography>
                              </IconButton>
                            </Box>
                            <Box>
                              <IconButton color="inherit" disableRipple>
                                <AirIcon />
                                <Typography>
                                  {weatherData.wind_avg} {weatherData.station_units.units_wind}
                                </Typography>
                              </IconButton>
                              <IconButton color="inherit" disableRipple>
                                <ExploreIcon />
                                <Typography>
                                  {weatherData.wind_direction} {weatherData.wind_direction_cardinal}
                                </Typography>
                              </IconButton>
                            </Box>
                          </Box>
                          <Box
                            sx={{
                              display: 'flex',
                              justifyContent: 'space-around',
                            }}
                          >
                            <Box>
                              <IconButton color="inherit" disableRipple>
                                <SouthEastIcon />
                                <Typography>
                                  {weatherData.sea_level_pressure}{' '}
                                  {weatherData.station_units.units_pressure}
                                </Typography>
                              </IconButton>
                            </Box>
                            <Box>
                              <IconButton color="inherit" disableRipple>
                                <LightModeIcon />
                                <Typography>{weatherData.uv} UV</Typography>
                              </IconButton>
                            </Box>
                          </Box>
                        </>
                      ) : (
                        <NoDataFound message="for the selected station" />
                      )}
                    </>
                  )}
                </>
              )}
            </>
          )}
        </Box>
      }
    />
  );
};
export default NewWeatherContainer;
