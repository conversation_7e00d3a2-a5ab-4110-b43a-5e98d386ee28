const { test, expect } = require('@playwright/test');

test.describe('API Test Suite - Create/Update Dashboard', () => {
  test('POST /customers/84/dashboards - should create or update a dashboard successfully', async ({
    request,
  }) => {
    // Set headers
    const headers = {
      'BE-CsrfToken': 'ecIB5u1MR1fD25E7j4dBex5/SPR/FTfnU4LlvChy3jU=',
      'Content-Type': 'application/json',
      Cookie:
        'BE-AccessToken=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VybmFtZSI6InRlc3QiLCJzdWIiOjIsInJvbGVzIjp7IkFETUlOIjpbMTI4LDgsMTIxLDEwNiwxMTgsODYsMTExLDg1LDEwNyw4NCwxMTMsMTE0LDExNywxMjAsMTI2LDExOSwxMTIsMTI3LDEyMywxMjQsMTA4LDEwOSwxMjUsMTIyLDExMCw5LDgyXSwiVVNFUiI6WzEyOCw4LDEyMSwxMDYsMTE4LDg2LDExMSw4NSwxMDcsODQsMTEzLDExNCwxMTcsMTIwLDEyNiwxMTksMTEyLDEyNywxMjMsMTI0LDEwOCwxMDksMTI1LDEyMiwxMTAsOSw4Ml0sIlBPV0VSX1VTRVIiOlsxMjgsOCwxMjEsMTA2LDExOCw4NiwxMTEsODUsMTA3LDg0LDExMywxMTQsMTE3LDEyMCwxMjYsMTE5LDExMiwxMjcsMTIzLDEyNCwxMDgsMTA5LDEyNSwxMjIsMTEwLDksODJdfSwiaWF0IjoxNzMxNTgzNDc5LCJleHAiOjE3MzE1OTA2Nzl9.sBcwQNlxiVLrd2L6v45QjwPVERaIWSwzsBUINTZdfsU; BE-CSRFToken=ecIB5u1MR1fD25E7j4dBex5%2FSPR%2FFTfnU4LlvChy3jU%3D',
    };

    // Define request body
    const body = {
      title: 'Overview',
      data: '{"currentDashboardId":89,"dashboardTitle":"Overview", ...}', // Replace with the actual data if necessary
    };

    // Make POST request
    const response = await request.post('https://test.brompton.ai/apiv0/customers/84/dashboards', {
      headers: headers,
      data: body,
    });

    // Log response status and body for debugging
    console.log(`Status: ${response.status()}`);
    const responseBody = await response.text();
    console.log(`Response Body: ${responseBody}`);

    // Check response status
    expect(response.status()).toBe(200); // Adjust based on expected status

    // Verify response body if needed
    const jsonResponse = JSON.parse(responseBody);

    // Perform assertions on the response data (customize based on actual API response structure)
    expect(jsonResponse).toHaveProperty('id'); // Assuming response contains an 'id'
    expect(jsonResponse.title).toBe('Overview'); // Check that the title matches
  });
});
