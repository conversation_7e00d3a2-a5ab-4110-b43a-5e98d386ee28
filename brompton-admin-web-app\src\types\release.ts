/**
 * With semantic versioning, the version number x.y.z is divided into:
 *
 * x - Major version
 * y - Minor version
 * z - Patch version
 * In this format:
 *
 * Major Version (x) changes indicate significant updates that might include incompatible API changes.
 * Minor Version (y) changes typically add functionality in a backward-compatible manner.
 * Patch Version (z) changes are usually for backward-compatible bug fixes.
 */
export interface Release {
  version: string; // Version in the format "x.y.z"
  changes: Change[];
  updated_at?: string; // Date of release
}

export interface Change {
  description: string[]; // List of changes introduced by the PR
}
