import { useEffect, useMemo } from 'react';
import { useSelector } from 'react-redux';
import {
  useGetAggregationPeriodsQuery,
  useGetAlertAnalyticsDrillDownByDateQuery,
  useGetAlertAnalyticsQuery,
  useGetThresholdTypesQuery,
} from '~/redux/api/alertApi';
import { useGetAllAssetQuery, useGetAllBackOfficeAssetTypesQuery } from '~/redux/api/assetsApi';
import { useGetAllMeasurementsQuery, useGetAllMeasureTypesQuery } from '~/redux/api/measuresApi';
import { getActiveCustomer } from '~/redux/selectors/customerSelectors';
import { assetsPathMapper, formatMetricLabel } from '~/utils/utils';

type AssetOption = {
  label: string;
  id: number;
};
type MeasureOption = {
  label: string;
  measurement_id: number;
};
type useAlertAnalyticsProps = {
  interval: string;
  selectedDay: string | null;
  selectedAsset: AssetOption | null;
  selectedMeasure: MeasureOption | null;
  showErrorAlert: (message: string) => void;
};
const useAlertAnalytics = ({
  interval,
  selectedDay,
  selectedAsset,
  selectedMeasure,
  showErrorAlert,
}: useAlertAnalyticsProps) => {
  const activeCustomer = useSelector(getActiveCustomer);
  const { data: aggPeriods } = useGetAggregationPeriodsQuery();
  const { data: thresholdTypes } = useGetThresholdTypesQuery();
  const { data: measurementTypes } = useGetAllMeasureTypesQuery();
  const { data: assetTypeListData } = useGetAllBackOfficeAssetTypesQuery();
  const {
    data: assetData,
    isLoading: isAssetLoading,
    isFetching: isAssetReloading,
    error: assetError,
  } = useGetAllAssetQuery(
    { customerId: Number(activeCustomer?.id), parentIds: [] },
    {
      skip: !activeCustomer?.id,
      refetchOnMountOrArgChange: true,
    },
  );
  const assetTypesWithPath = useMemo(() => assetsPathMapper(assetData ?? []), [assetData]);

  const { isFetching: isDrilledFetching, data: alertAnalyticsDrill } =
    useGetAlertAnalyticsDrillDownByDateQuery(
      {
        interval,
        assetId: selectedAsset?.id,
        measureId: selectedMeasure?.measurement_id,
        date: selectedDay?.toString() ?? '',
      },
      { refetchOnMountOrArgChange: false, skip: !selectedDay || selectedDay === null },
    );

  const {
    data: measurementData,
    isLoading: isMeasurementLoading,
    isFetching: isMeasurementFetching,
    error: measurementError,
  } = useGetAllMeasurementsQuery(
    { customerId: Number(activeCustomer?.id), assetId: selectedAsset ? selectedAsset.id : 0 },
    {
      skip: !selectedAsset || selectedAsset.id === 0,
      refetchOnMountOrArgChange: true,
    },
  );

  const {
    isFetching,
    data: alertAnalytics,
    isError,
    refetch,
  } = useGetAlertAnalyticsQuery(
    { interval, assetId: selectedAsset?.id, measureId: selectedMeasure?.measurement_id },
    { refetchOnMountOrArgChange: false },
  );
  useEffect(() => {
    refetch();
  }, [interval, selectedAsset, selectedMeasure, refetch]);
  useEffect(() => {
    if (assetError) {
      showErrorAlert('Error loading assets.');
    }
    if (measurementError) {
      showErrorAlert('Error loading measures.');
    }
  }, [assetError, measurementError]);

  const measurementOptions: MeasureOption[] = useMemo(() => {
    if (!measurementData) return [];
    return measurementData.map((measure) => ({
      label: formatMetricLabel(measure.tag),
      measurement_id: measure.measurementId,
    }));
  }, [measurementData]);

  const drilldownDataInfo = useMemo(() => {
    if (!alertAnalyticsDrill || !Array.isArray(alertAnalyticsDrill)) {
      return {
        labels: [],
        counts: [],
        durations: [],
        timeDurations: [],
      };
    }
    const labels = alertAnalyticsDrill.map((entry) => `Alert ${entry.alert_id}`);
    const counts = alertAnalyticsDrill.map((entry) => parseInt(entry.excursion_count, 10));
    const durations = alertAnalyticsDrill.map((entry) => parseFloat(entry.total_duration_seconds));
    const timeDurations = alertAnalyticsDrill.map((entry) => ({
      label: `Alert ${entry.alert_id}`,
      value: parseFloat(entry.total_duration_seconds) / 3600,
    }));

    return { labels, counts, durations, timeDurations };
  }, [alertAnalyticsDrill]);
  return {
    aggPeriods,
    thresholdTypes,
    measurementTypes,
    assetTypeListData,
    isAssetLoading,
    isAssetReloading,
    assetTypesWithPath,
    isDrilledFetching,
    alertAnalyticsDrill,
    measurementData,
    isMeasurementLoading,
    isMeasurementFetching,
    isFetching,
    alertAnalytics,
    isError,
    measurementOptions,
    drilldownDataInfo,
  };
};

export default useAlertAnalytics;
