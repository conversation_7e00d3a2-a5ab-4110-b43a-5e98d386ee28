import {
  Box,
  Checkbox,
  FormControlLabel,
  FormGroup,
  Menu,
  MenuItem,
  styled,
  Typography,
} from '@mui/material';
import ChevronRightIcon from '@mui/icons-material/ChevronRight';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import React, { ReactNode, useState } from 'react';
import clsx from 'clsx';
import { TreeItemContentProps } from '@mui/lab';

export type MenuAction = {
  label: string;
  onClick: (nodeId: string) => unknown;
};

export type NodeConfig = {
  icon: ReactNode;
  contextMenuActions: MenuAction[];
  selectable: boolean;
  expandable: boolean;
};

export type AssetsNodeProps = {
  disabled: boolean;
  expanded: boolean;
  selected: boolean;
  focused: boolean;
  onExpandToggle: (nodeId: string) => void;
  handleExpansion: (event: React.SyntheticEvent) => void;
  handleSelection: (event: React.SyntheticEvent) => void;
  preventSelection: (event: React.SyntheticEvent) => void;
  nodeConfig: NodeConfig;
  checkbox: boolean;
} & TreeItemContentProps;

const AssetRow = styled('div')(({ theme }) => ({
  minHeight: theme.spacing(5.25),
}));

const ToggleButton = styled('div')(({ theme }) => ({
  '&.MuiTreeItem-iconContainer': {
    width: theme.spacing(4),
    marginRight: 0,
  },
}));

export const AssetNode = React.forwardRef(function AssetNode(
  props: AssetsNodeProps,
  ref
) {
  const {
    classes,
    className,
    label,
    disabled,
    expanded,
    selected,
    focused,
    onExpandToggle,
    handleSelection,
    nodeId,
    nodeConfig,
    checkbox,
  } = props;

  const expandIcon = expanded ? <ExpandMoreIcon /> : <ChevronRightIcon />;

  const isSelectable = nodeConfig.selectable;
  const isExpandable = nodeConfig.expandable;

  const labelOnClick =
    isSelectable && !checkbox ? { onClick: handleSelection } : undefined;

  const labelComponent = (
    <Box
      sx={{ display: 'flex', alignItems: 'center', flexGrow: 1 }}
      {...labelOnClick}
      onContextMenu={(e) => {
        if (nodeConfig.contextMenuActions.length > 0) {
          e.preventDefault();
          handleActionsMenuClick(e);
        }
      }}
    >
      {nodeConfig.icon}
      <Typography>{label}</Typography>
    </Box>
  );

  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);

  const handleActionsMenuClick = (event: React.MouseEvent<HTMLDivElement>) => {
    setAnchorEl(event.currentTarget);
  };
  const handleActionsMenuClose = () => {
    setAnchorEl(null);
  };

  return (
    <>
      <AssetRow
        className={clsx(className, classes.root, {
          [classes.expanded]: expanded,
          [classes.selected]: selected,
          [classes.focused]: focused,
          [classes.disabled]: disabled,
        })}
        ref={ref as React.Ref<HTMLDivElement>}
      >
        <>
          {isExpandable && (
            <ToggleButton
              role="button"
              aria-expanded={expanded}
              className={clsx(classes.iconContainer)}
              onClick={() => onExpandToggle(nodeId)}
            >
              {expandIcon}
            </ToggleButton>
          )}
          {isSelectable && checkbox ? (
            <FormGroup>
              <FormControlLabel
                control={
                  <Checkbox
                    checked={selected}
                    onClick={handleSelection}
                    sx={{ mr: 0.75, p: 0 }}
                  />
                }
                label={labelComponent}
                sx={{ ml: 0, p: 0 }}
              />
            </FormGroup>
          ) : (
            labelComponent
          )}
        </>
      </AssetRow>

      <Menu
        id="basic-menu"
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleActionsMenuClose}
        MenuListProps={{
          'aria-labelledby': 'basic-button',
        }}
      >
        {nodeConfig.contextMenuActions.map((menuAction) => (
          <MenuItem
            key={menuAction.label}
            onClick={() => menuAction.onClick(nodeId)}
          >
            {menuAction.label}
          </MenuItem>
        ))}
      </Menu>
    </>
  );
});
