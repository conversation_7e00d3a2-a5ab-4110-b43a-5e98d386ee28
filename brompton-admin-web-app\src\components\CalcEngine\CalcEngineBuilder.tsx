import { useState } from 'react';
import { calc_engine_template } from '~/types/calc_engine';
import { Customer } from '~/types/customers';
import ExpressionBuilder from './ExpressionBuilder';
import ExpressionTemplateSelector from './ExpressionTemplateSelector';
type CalcEngineBuilderProps = {
  templateId?: number;
};
const CalcEngineBuilder = ({ templateId }: CalcEngineBuilderProps) => {
  const [expressionTemplate, setExpressionTemplate] = useState<calc_engine_template | null>(null);
  const [step, setStep] = useState<number>(0);
  const [customer, setCustomer] = useState<Customer | null>(null);
  return (
    <>
      {step === 0 && (
        <ExpressionTemplateSelector
          expressionTemplate={expressionTemplate}
          setExpressionTemplate={setExpressionTemplate}
          setStep={setStep}
          customer={customer}
          setCustomer={setCustomer}
          templateId={templateId ?? 0}
        />
      )}
      {step === 1 && (
        <ExpressionBuilder
          expressionTemplate={expressionTemplate ?? null}
          setStep={setStep}
          customer={customer}
        />
      )}
    </>
  );
};

export default CalcEngineBuilder;
