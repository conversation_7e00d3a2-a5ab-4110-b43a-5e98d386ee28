import { AssetType, AssetTypeOption } from '~/types/asset';

const buildPath = (assetTypesMap: Map<number, AssetType>, assetType: AssetType): string => {
  if (assetType.parentType === null) {
    return assetType.name;
  } else {
    const parentAssetType = assetTypesMap.get(assetType.parentType);

    if (!parentAssetType) {
      throw new Error('Asset parent type not found');
    }

    return `${buildPath(assetTypesMap, parentAssetType)} > ${assetType.name} (${
      assetType.asset_template_count
    })`;
  }
};

export const assetTypePathMapper = (assetTypes: AssetType[]): AssetTypeOption[] => {
  const assetTypesMap: Map<number, AssetType> = new Map();

  assetTypes.forEach((assetType) => assetTypesMap.set(assetType.id, assetType));

  return assetTypes.map((assetType) => ({
    value: assetType.id,
    label: buildPath(assetTypesMap, assetType),
  }));
};

export const assetTypePathMapperFilterTemplates = (assetTypes: AssetType[]): AssetTypeOption[] => {
  const assetTypesMap: Map<number, AssetType> = new Map();

  assetTypes.forEach((assetType) => assetTypesMap.set(assetType.id, assetType));

  return assetTypes
    .filter((assetType) => assetType.asset_template_count > 0)
    .map((assetType) => ({
      value: assetType.id,
      label: buildPath(assetTypesMap, assetType),
    }));
};
