import {
  Box,
  FormControlLabel,
  FormGroup,
  FormLabel,
  Grid,
  Radio,
  RadioGroup,
  TextField,
} from '@mui/material';
import React, { ChangeEvent, useEffect, useState } from 'react';
import { useSelector } from 'react-redux';
import { useGetCustomerImagesQuery } from '~/redux/api/customersApi';
import { getActiveCustomer } from '~/redux/selectors/customerSelectors';
import { getMetricsIdToName } from '~/redux/selectors/dashboardSelectors';
import { getDbMeasureIdToName } from '~/redux/selectors/treeSelectors';
import { ImageWidget } from '~/types/widgets';
import DataWidgetSettingsContainer from '../common/DataWidgetSettingsContainer/DataWidgetSettingsContainer';
import Loader from '../common/Loader';
import ImageUpload from './ImageUpload';
import ImageWidgetLink from './ImageWidgetLink';

export const images: string[] = [
  '/icons/flow-computer.svg',
  '/icons/solar_panel.gif',
  '/icons/Voltage_Optimizer.svg',
  '/icons/VerticalWidget.svg',
  '/icons/flow-meter.svg',
  '/icons/gas-meter-shaded.svg',
  '/icons/diamond.svg',
  '/icons/power-pole.svg',
  '/icons/water-meter-shaded.svg',
  '/icons/solar-panel-front.svg',
  '/icons/solar-2.svg',
  '/icons/SLD_Temple.svg',
  '/icons/SLD_Shree_Temple_2.svg',
  '/icons/leaf.svg',
  '/icons/cloud.png',
  '/icons/co2.svg',
  '/icons/electric_tower.svg',
  '/icons/natural-gas.svg',
  '/icons/solar-with-sun.svg',
  '/icons/water-faucet.svg',
  '/icons/white.svg',
  '/icons/cloudy.svg',
  '/icons/partly-cloudy.svg',
  '/icons/tac-simple-sld.svg',
  '/icons/temple-simple-sld.svg',
  '/icons/toku.svg',
  '/icons/toku-vertical.svg',
];
interface ImageWidgetDialogProps {
  settings: ImageWidget;
  handleSettingsChange: React.Dispatch<React.SetStateAction<ImageWidget>>;
}
export const ImageWidgetDialog = ({
  settings,
  handleSettingsChange: setSettings,
}: ImageWidgetDialogProps) => {
  const activeCustomer = useSelector(getActiveCustomer);
  const selectedDbMeasureIdToName = useSelector(getDbMeasureIdToName);
  const metricsIdToName = useSelector(getMetricsIdToName);
  const [selectedMeasures, setSelectedMeasures] = useState<string[]>([]);

  const {
    data: customerImages,
    refetch,
    isFetching: imagesLoading,
  } = useGetCustomerImagesQuery(
    {
      id: activeCustomer?.id ?? 0,
    },
    {
      refetchOnMountOrArgChange: true,
      skip: activeCustomer == null || activeCustomer?.id === 0,
    },
  );

  const handleIsEditableChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSettings((prevState) => ({
      ...prevState,
      isEditable: event.target.checked,
    }));
  };

  const handleChangeMeasure = (updatedTitles: string[]) => {
    setSettings((prevState) => {
      const { selectedTitles, measureIdToImageTextDetails, ...rest } = prevState;
      const removedTitles = selectedTitles.filter((title) => !updatedTitles.includes(title));
      const addedTitles = updatedTitles.filter((title) => !selectedTitles.includes(title));
      const newMeasureIdToImageTextDetails = { ...measureIdToImageTextDetails };
      removedTitles.forEach((title) => {
        delete newMeasureIdToImageTextDetails[title];
      });
      const titles: Record<string, string> = {};
      addedTitles.forEach((title) => {
        newMeasureIdToImageTextDetails[title] = {
          label: selectedDbMeasureIdToName[title],
          unit: '',
          id: title,
          positionX: 100,
          positionY: 100,
          value: '',
          dashboard: null,
          openDashboardInNewTab: false,
          dashboardOrTemplate: 'template',
          assetOrAssetType: null,
        };
      });
      if (rest.mode === 'template') {
        updatedTitles.forEach((title) => {
          titles[title] = metricsIdToName[title];
        });
      }
      return {
        ...rest,
        selectedTitles: updatedTitles,
        ...(rest.mode === 'template'
          ? {
              dbMeasureIdToName: titles,
            }
          : {}),
        measureIdToImageTextDetails: newMeasureIdToImageTextDetails,
      };
    });
  };
  const handleImageChange = (event: React.ChangeEvent<HTMLInputElement>, value: string) => {
    setSettings((prevState) => ({
      ...prevState,
      image: event.target.value as ImageWidget['image'],
    }));
  };
  const handleMarginChange = (e: ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    setSettings((prevState) => {
      return {
        ...prevState,
        margin: {
          ...prevState.margin,
          [e.target.name]: Number(e.target.value),
        },
      };
    });
  };
  useEffect(() => {
    if (settings.mode === 'dashboard') {
      const hasValidAssetMeasure = settings.assetMeasure.some(
        (assetMeas) =>
          assetMeas.assetId.trim() !== '' &&
          assetMeas.measureId.some((measure) => measure.trim() !== ''),
      );
      if (hasValidAssetMeasure) {
        const titles = settings.assetMeasure
          .flatMap((assetMeas) => assetMeas.measureId)
          .filter((measure) => measure.trim() !== '');
        setSelectedMeasures(titles);
      } else {
        setSelectedMeasures([]);
      }
    }
    if (settings.mode === 'template') {
      if (settings.selectedTitles.length > 0) {
        setSelectedMeasures(settings.selectedTitles);
      } else {
        setSelectedMeasures([]);
      }
    }
  }, [settings.assetMeasure, settings.selectedTitles, settings.mode]);
  return (
    <DataWidgetSettingsContainer
      settings={settings}
      setSettings={setSettings}
      hideSettings={{
        timeContext: true,
        measurements: true,
      }}
      exculdedSettings={{
        aggBy: true,
        samplePeriod: true,
        globalSamplePeriod: true,
        timeRange: true,
      }}
      dataTabChildren={
        <>
          {/* {settings.mode === 'dashboard' ? (
            <MultiMeasureSelectionMenu
              mode={settings.mode}
              settings={settings}
              setSettings={setSettings as setMultiMeasureWidgetSettings}
            />
          ) : (
            <MultiMeasureSelection
              mode={settings.mode}
              handleChangeMeasure={handleChangeMeasure}
              selectedMeasureNames={settings.selectedTitles}
            />
          )} */}
          <ImageWidgetLink settings={settings} setSettings={setSettings} />
        </>
      }
      feelTabChidren={
        <>
          {/* <FormControl component="fieldset">
            <FormGroup>
              <FormControlLabel
                control={
                  <Checkbox
                    checked={settings.isEditable}
                    onChange={handleIsEditableChange}
                    name="isEditable"
                  />
                }
                label="Editable Mode"
              />
            </FormGroup>
          </FormControl> */}
          <Box mt={2} mb={2}>
            <ImageUpload
              onImageUpload={(imageUrl) =>
                setSettings((prev) => ({ ...prev, uploadedImage: imageUrl }))
              }
              refetch={() => {
                refetch();
              }}
              setSettings={setSettings}
            />
          </Box>

          <FormGroup>
            <FormLabel>Select Image</FormLabel>
            <RadioGroup
              sx={{ mt: 1 }}
              aria-labelledby="demo-controlled-radio-buttons-group"
              name="radio-buttons-group"
              value={settings.image}
              onChange={handleImageChange}
              row
            >
              {images.map((image, i) => (
                <FormControlLabel
                  key={i}
                  value={image}
                  control={<Radio />}
                  label={<img src={image} alt={image} style={{ width: '80px', height: '80px' }} />}
                />
              ))}
              {imagesLoading ? (
                <Loader />
              ) : (
                <>
                  {customerImages?.items?.map((image, i) => (
                    <FormControlLabel
                      key={i}
                      value={image.id}
                      control={<Radio />}
                      label={
                        <img
                          src={image.logo}
                          alt={i.toString()}
                          style={{ width: '80px', height: '80px' }}
                        />
                      }
                    />
                  ))}
                </>
              )}
            </RadioGroup>
          </FormGroup>
          <Grid container mb={2} spacing={1}>
            <Grid item xs={6}>
              <FormGroup>
                <TextField
                  name="l"
                  type="number"
                  onChange={handleMarginChange}
                  value={settings.margin?.l}
                  label="Margin Left"
                  variant="outlined"
                  margin="normal"
                  fullWidth
                />
              </FormGroup>
            </Grid>
            <Grid item xs={6}>
              <FormGroup>
                <TextField
                  name="t"
                  type="number"
                  onChange={handleMarginChange}
                  value={settings.margin?.t}
                  label="Margin Top"
                  variant="outlined"
                  margin="normal"
                  fullWidth
                />
              </FormGroup>
            </Grid>
            <Grid item xs={6}>
              <FormGroup>
                <TextField
                  name="r"
                  type="number"
                  onChange={handleMarginChange}
                  value={settings.margin?.r}
                  label="Margin Right"
                  variant="outlined"
                  margin="normal"
                  fullWidth
                />
              </FormGroup>
            </Grid>
            <Grid item xs={6}>
              <FormGroup>
                <TextField
                  name="b"
                  type="number"
                  onChange={handleMarginChange}
                  value={settings.margin?.b}
                  label="Margin Bottom"
                  variant="outlined"
                  margin="normal"
                  fullWidth
                />
              </FormGroup>
            </Grid>
          </Grid>
          {/* <CustomWidgetMeasureSettingsSelector
            selectedMeasureNames={selectedMeasures}
            selectedDbMeasureIdToName={settings.dbMeasureIdToName}
            settings={settings}
            setSettings={setSettings}
          />
          <ImageWidgetFonts settings={settings} setSettings={setSettings} /> */}
        </>
      }
    />
  );
};
