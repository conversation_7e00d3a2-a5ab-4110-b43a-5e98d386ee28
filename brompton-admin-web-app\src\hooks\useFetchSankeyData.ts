import { ThunkDispatch } from '@reduxjs/toolkit';
import { useRouter } from 'next/router';
import { Data, Layout } from 'plotly.js';
import { SankeyColor } from 'plotly.js/lib/traces/sankey';
import { useCallback, useEffect, useRef, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Connection } from '~/components/ChartSettings/SankeyChartSettings';
import { UnitOfMeasure } from '~/measurements/domain/types';
import { measuresApi } from '~/redux/api/measuresApi';
import { timeseriesApi } from '~/redux/api/timeseriesApi';
import { getCustomerId } from '~/redux/selectors/customerSelectors';
import { getMetricsIdToName } from '~/redux/selectors/dashboardSelectors';
import {
  getAssetTz,
  getGlobalSamplePeriod,
  getGlobalTimeRangeType,
} from '~/redux/selectors/topPanleSelectors';
import { getDbMeasureIdToAssetIdMap } from '~/redux/selectors/treeSelectors';
import { getGlobalEndDate, getGlobalStartDate } from '~/redux/selectors/widgetSelectors';
import { RootState } from '~/redux/store';
import { AggByOptions, SamplePeriodOptions } from '~/types/dashboard';
import { AssetMeasurementDetails, measurementsUnitsDTO } from '~/types/measures';
import {
  AssetMeasurementDetailsWithLastFetchAndSucess,
  AssetMeasurementDetailsWithLastFetchTime,
  SingleScatterTimeSeriesData,
} from '~/types/timeseries';
import { SankeyChartWidget } from '~/types/widgets';

type useFetchSankeyDataProps = {
  settings: SankeyChartWidget;
};
type MeasuresData = {
  isLoading: boolean;
  isError: boolean;
  error?: string;
  tsData: SingleScatterTimeSeriesData;
  measureData: AssetMeasurementDetails;
  unitOfMeasures: UnitOfMeasure[];
  lastFetchTime?: number;
  partialFailed?: boolean;
  partial_full_error?: 'partial' | 'full';
};
type TrendResult = {
  isError: boolean;
  error?: string;
  lastFetchTime?: number;
  tsData: SingleScatterTimeSeriesData;
  measureData: AssetMeasurementDetails;
  unitOfMeasures: UnitOfMeasure[];
};

interface Label {
  sourceFrom: string;
  sourceName: string;
  sourceLabel: string;
  values: number;
}

function topologicalSort(labels: Label[], connections: Connection[]): string[] {
  const labelIndexMap = new Map<string, number>();
  labels.forEach((label, index) => labelIndexMap.set(label.sourceName, index));

  const indegree = new Array(labels.length).fill(0);
  const graph = new Map<number, number[]>();

  // Build graph and compute indegrees using string identifiers
  connections.forEach(({ source, destination }) => {
    const srcIndex = labelIndexMap.get(source)!;
    const destIndex = labelIndexMap.get(destination)!;
    if (!graph.has(srcIndex)) {
      graph.set(srcIndex, []);
    }
    graph.get(srcIndex)!.push(destIndex);
    indegree[destIndex]++;
  });

  // Find all nodes with no incoming edges
  const queue: number[] = [];
  indegree.forEach((count, index) => {
    if (count === 0) {
      queue.push(index);
    }
  });

  const sortedOrder: string[] = [];
  while (queue.length > 0) {
    const node = queue.shift()!;
    sortedOrder.push(labels[node].sourceName);
    if (graph.has(node)) {
      graph.get(node)!.forEach((neighbor) => {
        indegree[neighbor]--;
        if (indegree[neighbor] === 0) {
          queue.push(neighbor);
        }
      });
    }
  }

  return sortedOrder;
}

function calculateValues(labels: Label[], connections: Connection[], sortedOrder: string[]) {
  const labelIndexMap = new Map<string, number>();
  labels.forEach((label, index) => labelIndexMap.set(label.sourceName, index));
  sortedOrder.forEach((name) => {
    const index = labelIndexMap.get(name)!;
    const label = labels[index];
    if (label.sourceFrom === 'Calculated') {
      const sources = connections.filter((conn) => Number(conn.destination) === index);
      label.values = sources.reduce((acc, conn) => acc + labels[Number(conn.source)]?.values, 0);
    }
  });
}
function createSankeyData(
  chartResults: TrendResult[],
  settings: SankeyChartWidget,
  isDashboardTemplate: boolean,
): { data: Data[]; removedResults: AssetMeasurementDetailsWithLastFetchTime[] } {
  const measureDetails: Record<number, number> = {};
  const removedResults: TrendResult[] = chartResults.filter(
    (result) => !result.tsData || result.isError || result.error,
  );
  chartResults.map((res) => {
    measureDetails[res.measureData?.id] = res.tsData?.['ts,val']?.slice(-1)[0]?.[1] ?? 0;
  });
  const labels = settings.Label.map((label) => ({
    ...label,
    values: measureDetails[Number(label.sourceName)] ?? 0,
  }));
  const outgoingMap: Record<number, Connection[]> = {};
  settings.connections.forEach((con) => {
    const src = Number(con.source);
    if (!outgoingMap[src]) outgoingMap[src] = [];
    outgoingMap[src].push(con);
  });
  const sortedOrder = topologicalSort(labels, settings.connections);
  calculateValues(labels, settings.connections, sortedOrder);

  // Generate correct values for each connection
  const targetConnections = settings.connections.map((con) => {
    const srcIndex = Number(con.source);
    const labelValue = labels[srcIndex]?.values ?? 0;
    const value = isDashboardTemplate ? Math.random() : labelValue; // Even if there are multiple, you're not splitting

    return {
      ...con,
      value,
    };
  });
  const data: Data[] = [
    {
      type: 'sankey',
      orientation: 'h',
      node: {
        pad: 15,
        thickness: 30,
        line: {
          color: 'black',
          width: 0.5,
        },
        label: [...settings.Label.map((label) => label.sourceLabel)],
      },
      link: {
        customdata: settings.connections.map((connection) => {
          const sourceIndex = Number(connection.source);
          const sourceLabel = settings.Label[sourceIndex];

          return JSON.stringify({
            sourceName: sourceLabel?.sourceName ?? '',
            linkedDashboard: sourceLabel?.linkedDashboard ?? null,
            asset: sourceLabel.sourceAssetMeasure?.assetId ?? '',
          });
        }),
        source: settings.connections.map((connection) => Number(connection.source)),
        target: settings.connections.map((connection) => Number(connection.destination)),
        value: [...targetConnections.map((con) => con.value)],
        color: settings.showColor
          ? (settings.connections.map((connection) =>
              connection.color ? connection.color : '#DEDEDE',
            ) as SankeyColor[])
          : undefined,
      },
    },
  ];
  return {
    data,
    removedResults: removedResults.map((res) => ({
      ...res.measureData,
      partialFailed: removedResults.length !== chartResults.length,
      lastFetchTime: Date.now(),
    })),
  };
}
export const useFetchSankeyData = ({ settings }: useFetchSankeyDataProps) => {
  const [successAndFailedMeasurements, setSuccessAndFailedMeasurements] = useState<
    AssetMeasurementDetailsWithLastFetchAndSucess[]
  >([]);
  const [state, setState] = useState<{
    data: undefined | MeasuresData[];
    isLoading: boolean;
    isError: boolean;
  }>({
    data: undefined,
    isLoading: true,
    isError: false,
  });

  const [chartData, setChartData] = useState<{
    data: Data[];
    removedResults: AssetMeasurementDetailsWithLastFetchTime[];
  }>({
    data: [],
    removedResults: [],
  });
  const dbMeasureIdToAssetIdMap = useSelector(getDbMeasureIdToAssetIdMap);
  const dispatch = useDispatch<ThunkDispatch<RootState, any, any>>();
  const customerId = useSelector(getCustomerId);
  const router = useRouter();
  const metricToName = useSelector(getMetricsIdToName);
  const globalStartDate = useSelector(getGlobalStartDate);
  const globalEndDate = useSelector(getGlobalEndDate);
  const globalTimeRange = useSelector(getGlobalTimeRangeType);
  const globalSamplePeriod = useSelector(getGlobalSamplePeriod);
  const assetTz = useSelector(getAssetTz);
  const {
    startDate: selectedStartDate,
    endDate: selectedEndDate,
    aggBy: selectedAggBy,
    samplePeriod: selectedSamplePeriod,
    timeRange: selectedTimeRange,
    globalSamplePeriod: isGlobalSamplePeriodOverridden,
    overrideGlobalSettings: isGlobalTimeRangeSettingsOverridden,
    overrideAssetTz: isOverrideAssetTz,
    overrideAssetTzValue,
    isRealTime,
    refreshInterval,
    retainPeriod,
  } = settings;
  const [startDate, setStartDate] = useState(globalStartDate);
  const [endDate, setEndDate] = useState(globalEndDate);
  const [aggBy, setAggBy] = useState(selectedAggBy);
  const [samplePeriod, setSamplePeriod] = useState(globalSamplePeriod);
  const [timeRange, setTimeRange] = useState(globalTimeRange);
  const prevResultsRef = useRef<TrendResult[]>([]);
  const [chartResults, setChartResults] = useState<TrendResult[] | undefined>(undefined);
  const [labelWithAssetMeasure, setLabelWithAssetMeasure] = useState<
    Array<{ assetId: string; measureId: string }>
  >([]);

  useEffect(() => {
    if (settings.Label.length > 0) {
      const validLabels = settings.Label.filter(
        (label) => label.sourceFrom === 'Default' && label.sourceAssetMeasure,
      )
        .filter((label) => {
          const assetId = label.sourceAssetMeasure!.assetId.trim();
          const measureId = label.sourceAssetMeasure!.measureId.trim();
          return assetId !== '' && measureId !== '';
        })
        .map((label) => ({
          assetId: label.sourceAssetMeasure!.assetId,
          measureId: label.sourceAssetMeasure!.measureId,
        }));
      setLabelWithAssetMeasure(validLabels);
    }
  }, [settings, settings.Label]);
  const [allDataFetched, setAllDataFetched] = useState({
    chartData: [] as Data[],
    removedResults: [] as AssetMeasurementDetailsWithLastFetchTime[],
    isLoading: true,
    isError: false,
    layoutData: {
      showlegend: true,
      title: 'Basic Sankey',
      font: {
        size: 10,
      },
    } as Partial<Layout>,
  });
  const [assetTzOverride, setAssetTzOverride] = useState(isOverrideAssetTz);
  const [assetTzOverrideValue, setAssetTzOverrideValue] = useState(overrideAssetTzValue);

  useEffect(() => {
    setAggBy(selectedAggBy);

    if (isGlobalTimeRangeSettingsOverridden) {
      setStartDate(selectedStartDate);
      setEndDate(selectedEndDate);
      setTimeRange(selectedTimeRange);
    } else {
      setStartDate(globalStartDate);
      setEndDate(globalEndDate);
      setTimeRange(globalTimeRange);
    }

    if (isGlobalSamplePeriodOverridden) {
      setSamplePeriod(selectedSamplePeriod);
    } else {
      setSamplePeriod(globalSamplePeriod);
    }
    if (isOverrideAssetTz) {
      setAssetTzOverride(true);
      setAssetTzOverrideValue(overrideAssetTzValue);
    } else {
      setAssetTzOverride(false);
    }
  }, [
    globalEndDate,
    globalSamplePeriod,
    globalStartDate,
    globalTimeRange,
    isGlobalSamplePeriodOverridden,
    isGlobalTimeRangeSettingsOverridden,
    selectedAggBy,
    selectedEndDate,
    selectedSamplePeriod,
    selectedStartDate,
    selectedTimeRange,
    isOverrideAssetTz,
    overrideAssetTzValue,
    aggBy,
  ]);
  const retainPeriodRef = useRef(retainPeriod);

  const fetchMeasureData = useCallback(
    async (assetId: string, measureId: string) => {
      if (!assetId || assetId === '') {
        throw new Error(`Error invalid assetId for: ${measureId}`);
      }
      if (!measureId) {
        throw new Error(`Error invalid measureId: ${measureId}`);
      }
      const { data: measureData, isSuccess: isMeasureDataSuccess } = await dispatch(
        measuresApi.endpoints?.getMeasurementById.initiate(
          {
            customerId,
            assetId: dbMeasureIdToAssetIdMap[measureId],
            measId: measureId,
          },
          { forceRefetch: true },
        ),
      );

      if (!isMeasureDataSuccess || !measureData) {
        throw new Error(`Error fetching measure data for measureId: ${measureId}`);
      }
      return measureData;
    },
    [customerId, dbMeasureIdToAssetIdMap, dispatch],
  );
  const fetchAllAssetMeasures = useCallback(
    async (
      customerId: number,
      assetsWithMeasures: {
        asset_id: number;
        measurement_ids: number[];
      }[],
    ): Promise<measurementsUnitsDTO> => {
      if (!assetsWithMeasures || assetsWithMeasures.length === 0) {
        return {
          items: [],
          total: 0,
        };
      }

      const result = await dispatch(
        measuresApi.endpoints.getMeasuresWithAssetMeasures.initiate({
          customerId,
          data: assetsWithMeasures,
        }),
      );
      const { error, isError, data } = result;
      if (isError || error || !data) {
        console.error('Error fetching measures with asset measures:', error);
        return {
          items: [],
          total: 0,
        };
      }
      return data as measurementsUnitsDTO;
    },
    [customerId, dispatch],
  );
  const fetchUnitOfMeasure = useCallback(
    async (assetMeasurementTypeId: number) => {
      const { data: unitOfMeasures, isSuccess: isUnitOfMeasureSuccess } = await dispatch(
        measuresApi.endpoints?.getUnitsOfMeasure.initiate({
          measurementTypeId: assetMeasurementTypeId,
        }),
      );

      if (!isUnitOfMeasureSuccess || !unitOfMeasures) {
        throw new Error('Error fetching unit of measure data');
      }

      return unitOfMeasures;
    },
    [dispatch],
  ); // include the dispatch function in the dependency array

  const fetchTimeseriesData = useCallback(
    async (
      tsDbMeasureIds: number[],
    ): Promise<{ error: boolean; tsData: Record<number, SingleScatterTimeSeriesData> }> => {
      if (tsDbMeasureIds.length === 0) {
        return { error: true, tsData: {} };
      }
      const { data: tsData, isSuccess: isTsSuccess } = await dispatch(
        timeseriesApi.endpoints.getMultiMeasurementSeries.initiate({
          customerId,
          measId: tsDbMeasureIds.filter(Boolean).sort().join(','),
          start: startDate,
          end: endDate,
          agg: AggByOptions[aggBy].serverValue,
          agg_period: isGlobalSamplePeriodOverridden
            ? SamplePeriodOptions[selectedSamplePeriod].serverValue
            : SamplePeriodOptions[globalSamplePeriod].serverValue,
          timeRangeType: timeRange,
          assetTz: isOverrideAssetTz ? assetTzOverrideValue : assetTz,
        }),
      );

      if (!isTsSuccess || !tsData) {
        return {
          error: true,
          tsData: {},
        };
      }

      return { error: false, tsData };
    },
    [
      dispatch,
      customerId,
      startDate,
      endDate,
      aggBy,
      isGlobalSamplePeriodOverridden,
      selectedSamplePeriod,
      globalSamplePeriod,
      timeRange,
      isOverrideAssetTz,
      assetTzOverrideValue,
      assetTz,
    ],
  );

  const fetchTimeseriesDataWithAgg = useCallback(
    async (
      tsDbMeasureIds: number[],
      aggByOverride: number,
    ): Promise<{ error: boolean; tsData: Record<number, SingleScatterTimeSeriesData> }> => {
      try {
        const tsData = await dispatch(
          timeseriesApi.endpoints.getMultiMeasurementSeries.initiate({
            customerId,
            measId: tsDbMeasureIds.filter(Boolean).sort().join(','),
            start: startDate,
            end: endDate,
            agg: AggByOptions[aggByOverride].serverValue,
            agg_period: isGlobalSamplePeriodOverridden
              ? SamplePeriodOptions[selectedSamplePeriod].serverValue
              : SamplePeriodOptions[globalSamplePeriod].serverValue,
            timeRangeType: timeRange,
            assetTz: isOverrideAssetTz ? assetTzOverrideValue : assetTz,
          }),
        ).unwrap(); // ✅ returns raw tsData

        return { error: false, tsData };
      } catch (err) {
        console.error(`Failed to fetch TS data for aggBy=${aggByOverride}`, err);
        return {
          error: true,
          tsData: {},
        };
      }
    },
    [
      dispatch,
      customerId,
      startDate,
      endDate,
      isGlobalSamplePeriodOverridden,
      selectedSamplePeriod,
      globalSamplePeriod,
      timeRange,
      isOverrideAssetTz,
      assetTzOverrideValue,
      assetTz,
    ],
  );

  const generateRandomData = (
    numPoints: number,
    startTime: number,
    interval: number,
    minValue: number,
    maxValue: number,
  ): [number, number][] => {
    const data: [number, number][] = [];
    for (let i = 0; i < numPoints; i++) {
      const timestamp = startTime + i * interval;
      const value = Math.random() * (maxValue - minValue) + minValue;
      data.push([timestamp, value]);
    }
    return data;
  };
  const groupAssetMeasures = useCallback(
    (assetMeasures: Array<{ assetId: string; measureId: string }>) => {
      return assetMeasures.reduce<Record<string, number[]>>((acc, cur) => {
        const asset = Number(cur.assetId);
        const measures = Array.isArray(cur.measureId) ? cur.measureId : [cur.measureId]; // ensure array

        if (!acc[asset]) {
          acc[asset] = [];
        }

        // Push all measure IDs converted to numbers
        measures.forEach((m) => {
          acc[asset].push(Number(m));
        });

        return acc;
      }, {});
    },
    [
      customerId, // Ensure customerId is included in the dependencies
    ],
  );
  useEffect(() => {
    const fetchMeasuresData = async () => {
      setState({
        data: undefined,
        isError: false,
        isLoading: true,
      });

      const measureDataFetchersWithAssetId = groupAssetMeasures(
        labelWithAssetMeasure.map((item) => ({
          assetId: item.assetId,
          measureId: item.measureId,
        })),
      );
      const allMeasures = await fetchAllAssetMeasures(
        customerId,
        Object.entries(measureDataFetchersWithAssetId).map(([asset_id, measurement_ids]) => {
          return {
            asset_id: Number(asset_id),
            measurement_ids,
          };
        }),
      );
      const allPromisesData: MeasuresData[] = await Promise.all(
        allMeasures.items.map(async (measureData) => {
          return {
            isLoading: false,
            isError: false,
            error: '',
            lastFetchTime: Date.now(),
            measureData: {
              ...measureData,
              id: measureData.id,
              measurementId: measureData.measurement_id, // FIXED: use correct property name
              metricId: measureData.metricId ?? null, // FIXED: use correct property name
              tag: measureData.tag ?? '', // FIXED: use correct property name
            },
            unitOfMeasures: measureData.unitOfMeasure ? [measureData.unitOfMeasure] : [],
            tsData: {
              tag: measureData.id,
              period: '',
              'ts,val': [],
              tag_meta: { uom: measureData.unitOfMeasure?.name ?? '' },
            },
          } as MeasuresData;
        }),
      );
      // 3) If there's nothing to fetch, we can early return
      if (allMeasures.total === 0) {
        setState({ data: undefined, isLoading: false, isError: false });
        return;
      }

      // 4) Fetch all measure data in parallel
      const results = allPromisesData;

      const aggByMeasureMap = new Map<number, number[]>();

      results.forEach((result) => {
        const label = settings.Label.find(
          (l) =>
            l.sourceAssetMeasure?.measureId === result.measureData?.id.toString() ||
            l.sourceName === result.measureData?.id.toString(),
        );

        const aggValue = label?.aggBy ?? 1;
        if (!aggByMeasureMap.has(aggValue)) {
          aggByMeasureMap.set(aggValue, []);
        }

        aggByMeasureMap.get(aggValue)!.push(result.measureData?.measurementId);
      });
      const allTSFetchers = Array.from(aggByMeasureMap.entries()).map(async ([aggKey, measIds]) => {
        const { error, tsData } = await fetchTimeseriesDataWithAgg(measIds, aggKey);
        return { error, tsData };
      });

      const tsFetchResults = await Promise.all(allTSFetchers);

      let combinedTSData: Record<number, SingleScatterTimeSeriesData> = {};
      let foundError = false;

      for (const result of tsFetchResults) {
        if (result.error) {
          foundError = true;
          continue;
        }

        combinedTSData = {
          ...combinedTSData,
          ...result.tsData,
        };
      }

      if (foundError) {
        setState({ data: undefined, isLoading: false, isError: true });
        return;
      }

      // 6) Attach each measure’s timeseries to its result
      results.forEach((result) => {
        const measId = result.measureData?.measurementId;
        if (
          !measId ||
          !combinedTSData[measId] ||
          combinedTSData[measId]?.error ||
          combinedTSData[measId]?.['ts,val'] === undefined ||
          result.isError
        ) {
          result.isLoading = false;
          result.isError = true;
          result.lastFetchTime = undefined;
          result.error = combinedTSData[measId]?.error ?? 'Error found';
        } else if (measId) {
          result.isLoading = false;
          result.lastFetchTime = Date.now();
          result.tsData = combinedTSData[measId];
        }
      });

      // 7) Finally, store in state
      setState({ data: results, isLoading: false, isError: false });

      if (results.length > 0) {
        const updated: AssetMeasurementDetailsWithLastFetchAndSucess[] = [];
        // setSuccessAndFailedMeasurements
        const seenIds = new Set();
        const finalVals: AssetMeasurementDetailsWithLastFetchAndSucess[] = [];
        results.forEach((res) => {
          if (!res.isError && res.tsData) {
            updated.push({
              ...res.measureData,
              lastFetchTime: res.lastFetchTime,
              isSuccess: !res.isError,
            });
          } else {
            const existing = successAndFailedMeasurements.find(
              (r) => r.measurementId === res?.measureData?.measurementId,
            );

            if (existing) {
              updated.push({
                ...existing,
                lastFetchTime: existing.lastFetchTime,
                isSuccess: !res.isError,
              });
            } else {
              updated.push({
                ...res.measureData,
                lastFetchTime: res.lastFetchTime,
                isSuccess: !res.isError,
              }); // fallback to current failed result
            }
          }
        });
        updated.forEach((update) => {
          if (!seenIds.has(update.measurementId)) {
            seenIds.add(update.measurementId);
            finalVals.push(update);
          }
        });
        setSuccessAndFailedMeasurements(finalVals);
      }
    };
    if (!isRealTime && router.pathname !== '/dashboard-template') fetchMeasuresData();
    if (!isRealTime && router.pathname === '/dashboard-template' && settings.Label.length > 0) {
      setState({
        data: settings.Label.filter((label) => label.sourceFrom === 'Default').map((title) => ({
          isLoading: false,
          isError: false,
          error: '',
          lastFetchTime: Date.now(),
          measureData: {
            id: 11284,
            tag: metricToName[title.sourceName] ?? '',
            dataTypeId: 2,
            measurementId: 18578,
            typeId: 23,
            description: 'PHASEB:VOLTAGE',
            locationId: null,
            unitOfMeasureId: 70,
            meterFactor: null,
            datasourceId: null,
            valueTypeId: 1,
          },
          unitOfMeasures: [
            {
              id: 115,
              name: '%',
            },
            {
              id: 69,
              name: 'mV',
            },
            {
              id: 70,
              name: 'volts',
            },
          ],
          tsData: {
            tag: 18578,
            period: '1min',
            'ts,val': generateRandomData(24, 1727626648247, 60000, 20, 100),
            error: '',
            tag_meta: {
              uom: 'volts',
            },
          },
        })),
        isLoading: false,
        isError: false,
      });
    }
  }, [
    labelWithAssetMeasure,
    fetchMeasureData,
    fetchTimeseriesData,
    fetchUnitOfMeasure,
    customerId,
    assetTz,
    globalStartDate,
    globalEndDate,
    globalSamplePeriod,
    globalTimeRange,
    aggBy,
    dbMeasureIdToAssetIdMap,
    dispatch,
    endDate,
    samplePeriod,
    startDate,
    timeRange,
    assetTzOverride,
    assetTzOverrideValue,
    settings,
    settings.Label,
    isRealTime,
  ]);

  const applyRetention = (
    tsData: Record<number, SingleScatterTimeSeriesData>,
    retentionMinutes: number,
  ): Record<number, SingleScatterTimeSeriesData> => {
    // const retentionTime = Date.now() - retentionMinutes * 60000; // Calculate retention boundary
    const browserOffsetMinutes = new Date().getTimezoneOffset(); // Get browser's timezone offset in minutes
    const browserOffsetMs = browserOffsetMinutes * 60000; // Convert to milliseconds
    const retentionTime = Date.now() - retentionMinutes * 60000 - browserOffsetMs; // Adjusted retention boundary
    return Object.fromEntries(
      Object.entries(tsData)
        .filter(([, series]) => series.error === undefined) // Filter out errored series
        .map(([key, series]) => {
          const filteredPoints = series['ts,val'].filter(
            ([timestamp]) => timestamp >= retentionTime,
          );
          return [
            key,
            {
              ...series,
              'ts,val': filteredPoints,
            },
          ];
        }),
    );
  };
  const fetchHistoryTimeseriesData = useCallback(
    async (
      tsDbMeasureIds: number[],
    ): Promise<{ error: boolean; tsData: Record<number, SingleScatterTimeSeriesData> }> => {
      if (tsDbMeasureIds.length === 0) {
        return { error: true, tsData: {} };
      }
      const endDate = new Date();
      const startDate = new Date(endDate.getTime() - retainPeriodRef.current * 60000);

      const { data: tsData, isSuccess: isTsSuccess } = await dispatch(
        timeseriesApi.endpoints.getMultipleHistoryMeasurementSeries.initiate({
          customerId,
          measId: tsDbMeasureIds.filter(Boolean).sort().join(','),
          start: startDate.getTime(),
          end: endDate.getTime(),
          assetTz: false,
        }),
      );

      if (!isTsSuccess || !tsData) {
        return { error: true, tsData: {} };
      }

      // Apply retention logic
      const filteredTsData = applyRetention(tsData, retainPeriodRef.current);

      return { error: false, tsData: filteredTsData };
    },
    [dispatch, customerId],
  );

  useEffect(() => {
    if (isRealTime) {
      // const intervalId = setInterval(() => {
      const fetchRealTimeData = async () => {
        const measureDataFetchersWithAssetId = groupAssetMeasures(
          labelWithAssetMeasure
            .filter((item) => item.measureId && item.measureId.trim() !== '')
            .map((item) => ({
              assetId: item.assetId,
              measureId: item.measureId,
            })),
        );
        const allMeasures = await fetchAllAssetMeasures(
          customerId,
          Object.entries(measureDataFetchersWithAssetId).map(([asset_id, measurement_ids]) => {
            return {
              asset_id: Number(asset_id),
              measurement_ids,
            };
          }),
        );
        const allPromisesData: MeasuresData[] = await Promise.all(
          allMeasures.items.map(async (measureData) => {
            return {
              isLoading: false,
              isError: false,
              error: '',
              lastFetchTime: Date.now(),
              measureData: {
                ...measureData,
                id: measureData.id,
                measurementId: measureData.measurement_id, // FIXED: use correct property name
                metricId: measureData.metricId ?? null, // FIXED: use correct property name
                tag: measureData.tag ?? '', // FIXED: use correct property name
              },
              unitOfMeasures: measureData.unitOfMeasure ? [measureData.unitOfMeasure] : [],
              tsData: {
                tag: measureData.id,
                period: '',
                'ts,val': [],
                tag_meta: { uom: measureData.unitOfMeasure?.name ?? '' },
              },
            } as MeasuresData;
          }),
        );
        const tsMeasureIds =
          allPromisesData?.map((d) => d.measureData.measurementId).filter(Boolean) || [];
        const { error, tsData } = await fetchTimeseriesData(tsMeasureIds.filter(Boolean));
        if (error) {
          setState({ data: undefined, isLoading: false, isError: true });
          return;
        }
        allPromisesData.forEach((result) => {
          const seriesData = tsData[result.measureData?.measurementId];
          if (seriesData?.error || seriesData?.['ts,val'] === undefined) {
            result.isLoading = false;
            result.isError = true;
            result.error = seriesData?.error ?? 'No data available';
          } else {
            result.isLoading = false;
            result.isError = false;
            result.tsData = seriesData;
            result.lastFetchTime = Date.now();
          }
        });
        if (!error) {
          setState((prevState) => ({
            ...prevState,
            data: allPromisesData,
            isLoading: false,
          }));
        }
      };

      fetchRealTimeData();
    }
  }, [isRealTime, refreshInterval, fetchTimeseriesData, labelWithAssetMeasure, groupAssetMeasures]);

  useEffect(() => {
    if (!isRealTime) {
      return;
    }
    const intervalId = setInterval(() => {
      const fetcHistoryMeasuresData = async () => {
        setState({ data: undefined, isLoading: false, isError: false });
        const measureDataFetchersWithAssetId = groupAssetMeasures(
          labelWithAssetMeasure
            .filter((item) => item.measureId && item.measureId.trim() !== '')
            .map((item) => ({
              assetId: item.assetId,
              measureId: item.measureId,
            })),
        );
        const allMeasures = await fetchAllAssetMeasures(
          customerId,
          Object.entries(measureDataFetchersWithAssetId).map(([asset_id, measurement_ids]) => {
            return {
              asset_id: Number(asset_id),
              measurement_ids,
            };
          }),
        );
        const allPromisesData: MeasuresData[] = await Promise.all(
          allMeasures.items.map(async (measureData) => {
            return {
              isLoading: false,
              isError: false,
              error: '',
              lastFetchTime: Date.now(),
              measureData: {
                ...measureData,
                id: measureData.id,
                measurementId: measureData.measurement_id, // FIXED: use correct property name
                metricId: measureData.metricId ?? null, // FIXED: use correct property name
                tag: measureData.tag ?? '', // FIXED: use correct property name
              },
              unitOfMeasures: measureData.unitOfMeasure ? [measureData.unitOfMeasure] : [],
              tsData: {
                tag: measureData.id,
                period: '',
                'ts,val': [],
                tag_meta: { uom: measureData.unitOfMeasure?.name ?? '' },
              },
            } as MeasuresData;
          }),
        );
        const results = allPromisesData;
        const tsMeasureIds = results.map((result) => result.measureData?.measurementId);
        const { error, tsData } = await fetchHistoryTimeseriesData(tsMeasureIds.filter(Boolean));
        if (error) {
          setState({ data: undefined, isLoading: false, isError: true });
          return;
        }
        results.forEach((result) => {
          const seriesData = tsData[result.measureData?.measurementId];
          if (seriesData?.error || seriesData?.['ts,val'] === undefined) {
            result.isLoading = false;
            result.isError = true;
            result.error = seriesData?.error ?? 'No data available';
          } else {
            result.isLoading = false;
            result.isError = false;
            result.tsData = seriesData;
            result.lastFetchTime = Date.now();
          }
        });
        const updated: AssetMeasurementDetailsWithLastFetchAndSucess[] = [];
        results.forEach((res) => {
          if (!res.isError && res.tsData) {
            updated.push({
              ...res.measureData,
              lastFetchTime: res.lastFetchTime,
              isSuccess: !res.isError,
            });
          } else {
            const existing = successAndFailedMeasurements.find(
              (r) => r.measurementId === res?.measureData?.measurementId,
            );

            if (existing) {
              updated.push({
                ...existing,
                lastFetchTime: existing.lastFetchTime,
                isSuccess: !res.isError,
              });
            } else {
              updated.push({
                ...res.measureData,
                lastFetchTime: res.lastFetchTime,
                isSuccess: !res.isError,
              });
            }
          }
        });
        const seenIds = new Set();
        const finalVals: AssetMeasurementDetailsWithLastFetchAndSucess[] = [];
        updated.forEach((update) => {
          if (!seenIds.has(update.measurementId)) {
            seenIds.add(update.measurementId);
            finalVals.push(update);
          }
        });
        setSuccessAndFailedMeasurements([...finalVals]);
        setState({
          ...state,
          data: results,
          isLoading: false,
          isError: false,
        });
      };
      if (labelWithAssetMeasure.length > 0) fetcHistoryMeasuresData();
    }, refreshInterval * 1000);

    return () => clearInterval(intervalId);
  }, [dispatch, isRealTime, refreshInterval, customerId, labelWithAssetMeasure]);
  useEffect(() => {
    if (state.isLoading) {
      setChartResults(undefined);
      setAllDataFetched((prev) => {
        return {
          ...prev,
          isLoading: true,
          isError: false,
        };
      });
    } else if (state.data) {
      const updated: TrendResult[] = [];
      (state.data || []).forEach((result) => {
        if (!result.isError && result.tsData) {
          updated.push(result);
        } else {
          const existing = prevResultsRef.current.find(
            (r) => r.measureData.measurementId === result?.measureData?.measurementId,
          );
          if (existing) {
            updated.push({
              ...existing,
              lastFetchTime: result.lastFetchTime ?? undefined,
              isError: result.isError,
              error: result.error,
              tsData: {
                ...existing.tsData,
                error: result.error,
              },
            });
          } else {
            updated.push(result);
          }
        }
      });
      prevResultsRef.current = updated; // Keep latest working state
      // if (updated.length > 0) {
      //   // setSuccessAndFailedMeasurements
      //   const seenIds = new Set();
      //   const finalVals: AssetMeasurementDetailsWithLastFetchAndSucess[] = [];
      //   updated.forEach((update) => {
      //     if (!seenIds.has(update.measureData.measurementId)) {
      //       seenIds.add(update.measureData.measurementId);
      //       finalVals.push({
      //         ...update.measureData,
      //         isSuccess: !update.isError && !!update.tsData,
      //         lastFetchTime: update.lastFetchTime,
      //       });
      //     }
      //   });
      //   setSuccessAndFailedMeasurements(finalVals);
      // }
      setChartResults(updated as TrendResult[]);
    } else {
      setChartResults(undefined);
      setAllDataFetched((prev) => {
        return {
          ...prev,
          isError: true,
          isLoading: false,
        };
      });
    }
  }, [state.isLoading, state.data]);
  useEffect(() => {
    if (chartResults) {
      const chart = createSankeyData(
        chartResults,
        settings,
        router.pathname === '/dashboard-template',
      );
      setChartData(chart);
    }
    setAllDataFetched({
      ...allDataFetched,
      layoutData: {
        ...allDataFetched.layoutData,
        title: {
          text: settings.title.isVisible ? settings.title.value : undefined,
          font: settings.title.isVisible
            ? {
                size: settings.title.fontSize,
                color: settings.title.color,
              }
            : undefined,
        },
      },
    });
  }, [
    chartResults,
    settings.Label,
    settings.connections,
    settings.showColor,
    settings.title.isVisible,
    settings.title.value,
    settings.title.fontSize,
    settings.title.color,
  ]);
  return {
    data: chartData.data,
    removedResults: chartData.removedResults,
    layout: allDataFetched.layoutData,
    loading: state.isLoading,
    successAndFailedMeasurements,
  };
};
