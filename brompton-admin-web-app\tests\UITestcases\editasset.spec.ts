import { test, expect } from '@playwright/test';
test('editasset', async ({ page }) => {
  // opening the URL
  await page.goto('https://test.pivotol.ai/login'); // 60 seconds
  // Go to the username  and password
  await page.getByLabel('Username *').click();
  await page.getByLabel('Username *').fill('normaltest');
  await page.getByLabel('Password *').click();
  await page.getByLabel('Password *').fill('password123');
  // click on Login Button
  await page.click('#__next > div > div > form > div > button');
  await page.waitForTimeout(3000);
  // click on new dashboard
  await page.getByText('Add Dashboard').click({ timeout: 80000 });
  // select customer
  await page.getByLabel('Select Customer').click();
  //await page.getByRole('combobox', { name: 'Customer' }).click();
  await page.getByRole('option', { name: 'Customer', exact: true }).click();
  await page.getByTestId('ArrowDropDownIcon').nth(2).waitFor({ state: 'visible', timeout: 60000 });
  //await page.getByRole('option', { name: 'Customer', exact: true }).click();
  await page.waitForTimeout(8000);
  //click on asset
  const image = page.getByText('test');
  await image.click({ button: 'right' });
  await page.waitForTimeout(5000);
  // click on edit option from dropdown
  const image1 = page.locator('//*[@id="basic-menu"]/div[3]/ul/li[1]');
  await image1.click();
  await page.waitForTimeout(6000);
  //edit asset
  await page.getByLabel('Tag *').click();
  await page.getByLabel('Tag *').fill('test');
  await page.getByLabel('Select an asset type').click();
  await page.getByRole('option', { name: 'Power > AC Buss (0)', exact: true }).click();
  await page.getByTestId('ArrowDropDownIcon').nth(2).waitFor({ state: 'visible', timeout: 2000 });
  await page.getByLabel('Description').click();
  await page.getByLabel('Description').fill('testassets');
  await page.getByLabel('Select a time zone').click();
  await page.getByLabel('Select a time zone').fill('No options');
  await page.getByLabel('Latitude').click();
  await page.getByLabel('Latitude').fill('0');
  await page.getByLabel('Longitude').click();
  await page.getByLabel('Longitude').fill('0');
  // click on submit button
  await page.getByText('Submit').click();
  await page.waitForTimeout(6000);
  await page.close();
});
