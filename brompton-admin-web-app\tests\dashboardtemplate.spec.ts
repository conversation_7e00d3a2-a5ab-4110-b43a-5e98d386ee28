import { test, expect } from '@playwright/test';

test('dashboardtemplate', async ({ page }) => {
  // Open the URL
  await page.goto('https://test.brompton.ai/login');

  // Fill in Username and Password
  await page.getByLabel('Username *').fill('normaltest');
  await page.getByLabel('Password *').fill('password123');

  // Click on Login Button
  await page.click('#__next > div > div > form > div > button');
  await page.waitForTimeout(3000); // Wait for the login to process

  // Click on Add Dashboard
  await page.getByText('Add Dashboard').click({ timeout: 80000 });

  // Open the user context menu
  await page.locator('//*[@id="__next"]/div/div[1]/div/div[2]/button/div').click();
  await page.waitForTimeout(3000);

  // Select Dashboard Template
  await page.locator('//html/body/div[2]/div[3]/li[4]/div[2]/span').click();
  await page.waitForTimeout(3000);

  // Click on the Proceed button
  await page.getByRole('button', { name: 'PROCEED' }).click();

  // using explicit wait Now you can interact with the asset type element safely
  await page.waitForSelector('input[aria-autocomplete="list"]', {
    state: 'visible',
    timeout: 10000,
  });

  // Interact with Asset Type autocomplete
  const assetType = page.locator('input[aria-autocomplete="list"]').first();
  await assetType.click();
  await assetType.fill('Meter > BTU');
  await page.waitForTimeout(1000); // Wait for options to appear
  await page.getByRole('option', { name: 'Meter > BTU (13)' }).click();

  // Interact with Asset Template autocomplete
  const assetTemplate = page.locator('input[aria-autocomplete="list"]').nth(1);
  await assetTemplate.click();
  await assetTemplate.fill('sm1 - sm');
  await page.waitForTimeout(1000); // Wait for options to appear
  await page.getByRole('option', { name: 'sm1 - sm' }).click();

  // Click on Widgets icon to open widgets menu
  await page.locator('#widgets-icon').click();
  await page.waitForTimeout(2000);

  // Drag and drop the table widget into the layout area
  await page.locator('#table').dragTo(page.locator('.react-grid-layout.layout'));
  await page.waitForTimeout(2000);

  await page.locator('#widgets-icon').click();
  await page.waitForTimeout(2000);
  // First, locate the parent element you need to hover over to reveal the icon
  const parentElement = page.locator('.MuiBox-root.css-1j35o1p'); // Adjust selector if necessary

  // Hover over the parent element to make the icon visible
  await parentElement.hover();

  // Now, locate the 'MoreVertIcon' options button, which should be visible after hovering
  const optionsIcon = page.locator('[data-testid="MoreVertIcon"]');

  // Wait for the icon to become visible and then click it
  await optionsIcon.waitFor({ state: 'visible', timeout: 120000 });
  await optionsIcon.click();

  //await page.waitForTimeout(4000);
  await page.click('//*[@id="widget-settings-menu-1"]/div[3]/ul/li[4]');
  await page.waitForTimeout(6000);

  // Select Measure
  await page
    .locator('xpath=/html/body/div[2]/div[3]/div/div[1]/fieldset/div[2]/div[2]/div/div/div/div/div')
    .click();

  await page.waitForTimeout(1000); // Wait for options to appear
  await page.getByRole('option', { name: 'MassFlow' }).click();
  await page.keyboard.press('Tab');
  await page.waitForTimeout(3000);
  await page.getByRole('button', { name: 'UPDATE' }).click();
  //await page.locator('/html/body/div[2]/div[3]/div/div[2]/button[2]').click();
  // Save the dashboard
  await page.click('//*[@id="__next"]/div/div[2]/div[2]/div[1]/div/div[5]/button');
  await page.getByLabel('Dashboard Title *').click();
  await page.getByLabel('Dashboard Title *').fill('AutoTDashSP');
  await page.getByRole('button', { name: 'SAVE' }).click();

  // Wait for final actions and close the page
  await page.waitForTimeout(6000);
  await page.close();
});
