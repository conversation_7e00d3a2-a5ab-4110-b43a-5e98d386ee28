import React, { useEffect, useState } from 'react';
import { AlertMessage } from '~/shared/forms/types';
import { useCreateScopedUserMutation } from '~/redux/api/usersApi';
import { useFetchCustomersByAdminRole } from '~/hooks/useFetchCustomersByAdminRole';
import { Container, Typography } from '@mui/material';
import NewUserForm from '~/components/user/NewUserForm';
import { CustomError } from '~/errors/CustomerErrorResponse';
import HomeButton from '~/components/common/Home/HomeButton';

export default function CreateNewUser() {
  const [alertMessage, setAlertMessage] = useState<AlertMessage | undefined>(undefined);
  const [createScopedUser, { data, isSuccess, error, isError, isLoading }] =
    useCreateScopedUserMutation();
  const customers = useFetchCustomersByAdminRole();
  const [customerItemList, setCustomerItemList] = useState<{ id: number; label: string }[]>();

  useEffect(() => {
    const customerList = [];
    for (const customer of customers) {
      customerList.push({ id: customer.id, label: customer.name });
    }
    setCustomerItemList(customerList);
  }, [customers]);

  useEffect(() => {
    if (isSuccess && data) {
      setAlertMessage({
        message: `User created successfully!`,
        severity: 'success',
      });
    }

    if (isError && error) {
      const err = error as CustomError;
      setAlertMessage({ message: err.data.message ?? 'Server error', severity: 'error' });
    }
  }, [data, error, isError, isSuccess, isLoading]);

  return (
    <Container maxWidth="md" sx={{ pt: 2 }}>
      <HomeButton />
      <Typography variant="h4">Create New User</Typography>

      {customerItemList && (
        <NewUserForm
          customerItemList={customerItemList}
          loading={isLoading}
          alertMessage={alertMessage}
          onValidSubmit={createScopedUser}
        />
      )}
    </Container>
  );
}
