import { Data } from 'plotly.js';
import Chart from '~/components/Chart';
import { useFetchSankeyData } from '~/hooks/useFetchSankeyData';
import { SankeyChartWidget } from '~/types/widgets';

type SankeyChartWidgetProps = {
  id: string;
  settings: SankeyChartWidget;
};
const SankeyChartWidgetContainer = ({ id, settings }: SankeyChartWidgetProps) => {
  const { data, layout, loading, removedResults, successAndFailedMeasurements } =
    useFetchSankeyData({ settings });
  return (
    <>
      <Chart
        id={id}
        chartType="sankey"
        settings={settings}
        data={data as Data[]}
        layout={layout}
        isLoading={loading}
        showSettings={true}
        removedResults={removedResults}
        successAndFailedMeasurements={successAndFailedMeasurements}
      />
    </>
  );
};
export default SankeyChartWidgetContainer;
