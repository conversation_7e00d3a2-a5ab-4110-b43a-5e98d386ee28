import AddIcon from '@mui/icons-material/Add';
import CancelIcon from '@mui/icons-material/Cancel';
import CloseIcon from '@mui/icons-material/Close';
import DeleteSharpIcon from '@mui/icons-material/DeleteSharp';
import EditIcon from '@mui/icons-material/Edit';
import InfoOutlineIcon from '@mui/icons-material/InfoOutlined';
import InsertChartSharpIcon from '@mui/icons-material/InsertChartSharp';
import {
  Box,
  Button,
  Chip,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  Drawer,
  FormControl,
  IconButton,
  MenuItem,
  Select,
  TextField,
  Tooltip,
  Typography,
} from '@mui/material';
import { DataGrid, GridColDef, GridFilterModel, GridToolbarQuickFilter } from '@mui/x-data-grid';
import { useRouter } from 'next/router';
import { Dispatch, SetStateAction, useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import useGetAssetTypesWithPath from '~/hooks/useGetAssetTypesWithPath';
import { Role, useRolePermission } from '~/hooks/useRolePermission';
import EditMeasurementContainer from '~/layout/containers/EditMeasurementContainer';
import {
  useGetAllBackOfficeAssetTypesMetricsQuery,
  useGetAssetByIdQuery,
} from '~/redux/api/assetsApi';
import {
  useDeleteMeasureMutation,
  useGetAllDatasourcesQuery,
  useGetAllDataTypesQuery,
  useGetAllLocationsQuery,
  useGetAllMeasurementsByCustomerQuery,
  useGetAllMeasureTypesQuery,
  useGetAllUnitsOfMeasureQuery,
  useGetAllValueTypesQuery,
  useGetMeasurementByIdQuery,
} from '~/redux/api/measuresApi';
import { useGetMultipleLastReadingsMeasurementSeriesQuery } from '~/redux/api/timeseriesApi';
import { getActiveCustomer } from '~/redux/selectors/customerSelectors';
import { getDateTimeFormat, getThousandSeparator } from '~/redux/selectors/userPreferences';
import { dashboardSlice } from '~/redux/slices/dashboardSlice';
import { AlertSnackbar } from '~/shared/snackbars/components/AlertSnackbar';
import { useSnackbar } from '~/shared/snackbars/snackbar-hooks';
import { Asset, AssetDo } from '~/types/asset';
import { extendedMeasurementsDTO } from '~/types/measures';
import { formatDate, formatMetricTag, formatNumber, roundNumber } from '~/utils/utils';
import TestDashboards from '../TestDahboard/TestDashboards';
import CustomNoRowsOverlay from '../common/NoRowsOverlay';
import { MeasureDetails } from '../dashboard/MeasureDetails';
type FilterItem = {
  field: string;
  operator: string;
  value: string;
};
function QuickSearchToolbar({
  setShowFilter,
  filteredData,
}: {
  setShowFilter: Dispatch<SetStateAction<boolean>>;
  filteredData?: React.ReactNode;
}) {
  return (
    <Box>
      <Box
        sx={{
          p: 0.5,
          pb: 0,
          display: 'flex',
          gap: 2,
          mb: 2,
        }}
      >
        <Button
          variant="outlined"
          startIcon={<AddIcon />}
          sx={{
            height: 50,
          }}
          onClick={() => {
            setShowFilter(true);
          }}
        >
          Filter
        </Button>
        <GridToolbarQuickFilter
          variant="outlined"
          InputProps={{
            sx: {
              width: 320,
              borderRadius: '10px',
              border: '1px solid #E0E0E0',
              '& .MuiInputBase-input': {
                px: 1,
                py: 1.5,
              },
            },
          }}
        />
      </Box>
      {filteredData ? filteredData : null}
    </Box>
  );
}
const CustomerMeasurementBrowser = () => {
  const { hasDashboardPermission } = useRolePermission();
  const dateTimeFormat = useSelector(getDateTimeFormat);
  const router = useRouter();
  const url = new URL(window.location.href);
  const [page, setPage] = useState(0);
  const [pageSize, setPageSize] = useState(10);
  const [filterModel, setFilterModel] = useState<any>(null);
  const [filteredRows, setFilteredRows] = useState<extendedMeasurementsDTO['items']>([]);
  const [assetPathFilter, setAssetPathFilter] = useState('');
  const [measurementTagFilter, setMeasurementTagFilter] = useState('');
  const [unitsFilter, setUnitsFilter] = useState('');
  const [datasourceFilter, setDatasourceFilter] = useState('');
  const thousandSeparator = useSelector(getThousandSeparator);
  const [paginatedIds, setPaginatedIds] = useState<number[]>([]);
  const [openTrendModal, setOpenTrendModal] = useState<boolean>(false);
  const activeCustomer = useSelector(getActiveCustomer);
  const { data: datasourceList } = useGetAllDatasourcesQuery({ extra: true });
  const [sortModel, setSortModel] = useState<any[]>([]);
  const [measurementsData, setMeasurementsData] = useState<extendedMeasurementsDTO['items']>([]);
  const [isDrawerOpen, setIsDrawerOpen] = useState(false);
  const [selectedAssetType, setSelectedAssetType] = useState('');
  const [selectedMeasurementType, setSelectedMeasurementType] = useState('');
  const [isFiltered, setIsFiltered] = useState(false);
  const { assetTypesWithPath, assetTypeListData: assetTypes } = useGetAssetTypesWithPath();
  const [snackbarState, showSuccessAlert, showErrorAlert] = useSnackbar();
  const dispatch = useDispatch();
  const [editAssetMeasurementIds, setEditAssetMeasurementIds] = useState<{
    assetId: string;
    measurementId: string;
    open: boolean;
    editOrDetail: 'edit' | 'detail' | 'delete' | undefined;
    metricId?: number;
    deleteConfirmation?: boolean;
  }>({
    assetId: '',
    measurementId: '',
    open: false,
    editOrDetail: undefined,
    deleteConfirmation: undefined,
  });
  const { data: asset } = useGetAssetByIdQuery(
    { customerId: activeCustomer?.id ?? 0, assetId: editAssetMeasurementIds.assetId },
    {
      skip: !activeCustomer?.id || editAssetMeasurementIds.assetId === '', // In RTK Query, the option is `skip` instead of `enabled`
    },
  );
  const {
    data: measurement,
    isLoading: isAssetDataLoading,
    isError: isMeasurementError,
    error: measurementError,
  } = useGetMeasurementByIdQuery(
    {
      customerId: activeCustomer?.id ?? 0,
      assetId: editAssetMeasurementIds.assetId,
      measId: editAssetMeasurementIds.measurementId,
    },
    {
      skip: !activeCustomer?.id, // In RTK Query, the option is `skip` instead of `enabled`
    },
  );
  const { data: valueTypeList } = useGetAllValueTypesQuery();
  const { data: unitsOfMeasure, isSuccess: success } = useGetAllUnitsOfMeasureQuery(
    {
      measurementTypeId: editAssetMeasurementIds.metricId ?? 0,
    },
    {
      skip: !editAssetMeasurementIds.metricId,
      refetchOnMountOrArgChange: true,
    },
  );
  const { data: metrics } = useGetAllBackOfficeAssetTypesMetricsQuery(
    {
      assetId: (asset as AssetDo)?.type_id.toString(),
    },
    {
      skip: asset === undefined || (asset as AssetDo)?.type_id === undefined,
      refetchOnMountOrArgChange: true,
    },
  );
  const { data: dataTypeList } = useGetAllDataTypesQuery();
  const { data: locationList } = useGetAllLocationsQuery();

  // Fetch measurement data
  const { data: measurementsList, isFetching: fetchingMeasures } =
    useGetAllMeasurementsByCustomerQuery(
      { customerId: activeCustomer?.id ?? 0, lastreadings: true },
      {
        skip: !activeCustomer || activeCustomer.id === 0,
        refetchOnMountOrArgChange: true,
      },
    );
  const { data: measurementTypeList } = useGetAllMeasureTypesQuery();

  const { data: latestReadings, isFetching: fetchingLastRecords } =
    useGetMultipleLastReadingsMeasurementSeriesQuery(
      {
        measId: paginatedIds.join(','),
        customerId: activeCustomer?.id || 0,
      },
      {
        skip: !activeCustomer || activeCustomer.id === 0 || paginatedIds.length === 0,
        refetchOnMountOrArgChange: true,
      },
    );
  const [deleteMeasure, { isError, isSuccess, error, isLoading }] = useDeleteMeasureMutation();
  useEffect(() => {
    if (isSuccess) {
      showSuccessAlert('Measure deleted successfully');
    } else if (isError) {
      showErrorAlert('Error deleting measure');
    }
  }, [isSuccess, isError]);
  useEffect(() => {
    const source = filteredRows.length ? filteredRows : measurementsList?.items || [];

    const ids = source.slice(page * pageSize, page * pageSize + pageSize).map((item) => item.id);

    setPaginatedIds(ids);
  }, [page, pageSize, filteredRows, measurementsList]);
  useEffect(() => {
    if (measurementsList) {
      setMeasurementsData(measurementsList.items);
    }
  }, [measurementsList]);
  useEffect(() => {
    if (latestReadings && measurementsList?.items) {
      const enrichedMeasurements = measurementsList.items.map((measurement) => {
        if (!paginatedIds.includes(measurement.id)) return measurement;
        const reading = latestReadings[measurement.id];
        const readingValue = Number(reading?.['ts,val']?.[0]?.[1]);
        return {
          ...measurement,
          latestReading:
            reading?.error === undefined && !Number.isNaN(readingValue) ? readingValue : null,
          latestReadingTime:
            reading?.error === undefined ? reading?.['ts,val']?.[0]?.[0] ?? null : null,
        };
      });
      const sortedMeasurements = applySorting(enrichedMeasurements, sortModel);
      setMeasurementsData(sortedMeasurements);
    }
  }, [latestReadings, measurementsList, paginatedIds, sortModel]);

  const columns: GridColDef[] = [
    {
      field: 'asset_path',
      headerName: 'Asset Path',
      flex: 1,
      renderCell(params) {
        return params.value?.split(':').join('/');
      },
    },
    {
      field: 'a_type',
      headerName: 'Asset type',
      flex: 1,
      renderCell(params) {
        return assetTypesWithPath?.find((type) => type.value === params.value)?.label || 'N/A';
      },
    },
    {
      field: 'tag',
      headerName: 'Measurement',
      flex: 1,
      renderCell(params) {
        return formatMetricTag(params.value);
      },
    },
    {
      field: 'latestReading',
      headerName: 'Last Value',
      flex: 1,
      renderCell: (params) => {
        if (fetchingLastRecords) return 'Loading...';
        return params.row?.latestReading !== null && !Number.isNaN(params.row?.latestReading)
          ? thousandSeparator
            ? formatNumber(params.row.latestReading)
            : roundNumber(params.row.latestReading)
          : 'N/A';
      },
    },
    {
      field: 'latestReadingTime',
      headerName: 'Recorded timestamp (UTC)',
      flex: 1,
      renderCell: (params) => {
        const date = new Date(params.value);
        if (fetchingLastRecords) return 'Loading...';
        return params.row?.latestReadingTime !== null && !isNaN(date.getTime())
          ? formatDate(date, dateTimeFormat)
          : 'N/A';
      },
    },
    {
      field: 'id',
      headerName: 'Action',
      flex: 1,
      headerAlign: 'center',
      align: 'center',
      renderCell: (params) => {
        const endDate = new Date();
        const startDate = new Date(endDate.getTime() - 5 * 60 * 60 * 1000); // 5 hours in milliseconds
        return (
          <Box sx={{ display: 'flex', gap: 1 }}>
            <Tooltip title="Details">
              <IconButton
                onClick={() => {
                  const assetId = params.row.parent_asset;
                  const measurementId = params.row.asset_measurement_id;
                  dispatch(dashboardSlice.actions.selectMainPanel('MeasureDetails'));
                  dispatch(
                    dashboardSlice.actions.setCurrentSelectedNodeId(
                      'm:' + assetId + ':' + measurementId,
                    ),
                  );
                  dispatch(
                    dashboardSlice.actions.setSelectedViewMeasureId(
                      'm:' + assetId + ':' + measurementId,
                    ),
                  );
                  setEditAssetMeasurementIds({
                    assetId: assetId.toString(),
                    measurementId: measurementId.toString(),
                    open: true,
                    editOrDetail: 'detail',
                    metricId: params.row.m_type,
                  });
                }}
                size="small"
                color="info"
              >
                <InfoOutlineIcon fontSize="small" />
              </IconButton>
            </Tooltip>
            {hasDashboardPermission('measurement.delete', Role.POWER_USER) && (
              <Tooltip title="Edit">
                <IconButton
                  onClick={() => {
                    const assetId = params.row.parent_asset;
                    const measurementId = params.row.asset_measurement_id;
                    dispatch(dashboardSlice.actions.selectMainPanel('editMeasure'));
                    dispatch(
                      dashboardSlice.actions.setCurrentSelectedNodeId(
                        'm:' + assetId + ':' + measurementId,
                      ),
                    );
                    dispatch(
                      dashboardSlice.actions.setSelectedViewMeasureId(
                        'm:' + assetId + ':' + measurementId,
                      ),
                    );
                    setEditAssetMeasurementIds({
                      assetId: assetId.toString(),
                      measurementId: measurementId.toString(),
                      open: true,
                      editOrDetail: 'edit',
                      deleteConfirmation: undefined,
                    });
                  }}
                  size="small"
                  color="primary"
                >
                  <EditIcon fontSize="small" />
                </IconButton>
              </Tooltip>
            )}
            {hasDashboardPermission('measurement.delete', Role.POWER_USER) && (
              <Tooltip title="Delete">
                <IconButton
                  onClick={() => {
                    // deleteMeasure({
                    //   customerId: activeCustomer?.id ?? 0,
                    //   assetId: params.row.parent_asset.toString(),
                    //   measId: params.row.asset_measurement_id?.toString(),
                    // });
                    const assetId = params.row.parent_asset;
                    const measurementId = params.row.asset_measurement_id;
                    setEditAssetMeasurementIds({
                      assetId: assetId.toString(),
                      measurementId: measurementId.toString(),
                      open: true,
                      editOrDetail: 'delete',
                      deleteConfirmation: undefined,
                    });
                  }}
                  size="small"
                  color="error"
                >
                  <DeleteSharpIcon />
                </IconButton>
              </Tooltip>
            )}
            <Tooltip
              title="Trend"
              onClick={() => {
                updateUrlParams(
                  params.row.parent_asset.toString(),
                  params.row.asset_measurement_id?.toString(),
                  startDate.getTime(),
                  endDate.getTime(),
                );
              }}
            >
              <IconButton>
                <InsertChartSharpIcon />
              </IconButton>
            </Tooltip>
          </Box>
        );
      },
    },
  ];

  const updateUrlParams = (
    assetId: string,
    measurementId: string,
    startDate: number,
    endDate: number,
  ) => {
    setOpenTrendModal(true);
    url.searchParams.set('asset_id', assetId);
    url.searchParams.set('measurement_id', measurementId);
    url.searchParams.set('start_date', startDate.toString());
    url.searchParams.set('end_date', endDate.toString());
    url.searchParams.set('measurement_trend', 'true');
    router.push(url, undefined, { shallow: true });
  };

  const deleteUrlParams = () => {
    const { pathname, searchParams } = url;

    const paramsToRemove = [
      'asset_id',
      'measurement_id',
      'start_date',
      'end_date',
      'measurement_trend',
    ];

    // Remove only the specified parameters
    paramsToRemove.forEach((param) => searchParams.delete(param));

    // Construct updated query parameters
    const updatedQuery = Object.fromEntries(searchParams.entries());

    // Update URL with the remaining query parameters
    router.push({ pathname, query: updatedQuery }, undefined, { shallow: true });
  };

  const applySorting = (
    data: extendedMeasurementsDTO['items'],
    model: typeof sortModel,
  ): extendedMeasurementsDTO['items'] => {
    if (!model || model.length === 0) return data;

    const { field, sort } = model[0];

    return [...data].sort((a, b) => {
      const aVal = (a as any)[field];
      const bVal = (b as any)[field];

      if (aVal == null) return 1;
      if (bVal == null) return -1;

      if (typeof aVal === 'string') {
        return sort === 'asc' ? aVal.localeCompare(bVal) : bVal.localeCompare(aVal);
      }

      return sort === 'asc' ? aVal - bVal : bVal - aVal;
    });
  };
  useEffect(() => {
    const allCleared =
      selectedAssetType === '' &&
      selectedMeasurementType === '' &&
      assetPathFilter === '' &&
      measurementTagFilter === '' &&
      unitsFilter === '' &&
      datasourceFilter === '';

    if (allCleared) {
      setFilteredRows([]);
      setPaginatedIds(measurementsList?.items?.slice(0, pageSize).map((i) => i.id) ?? []);
      setIsFiltered(false);
      setPage(0);
    }
  }, [
    selectedAssetType,
    selectedMeasurementType,
    assetPathFilter,
    measurementTagFilter,
    unitsFilter,
    datasourceFilter,
    measurementsList,
    pageSize,
  ]);
  const handleChipDeleteAndFilterUpdate = (filterType: string) => {
    // Prepare a copy of the next filter state
    const nextFilters = {
      assetType: selectedAssetType,
      measurementType: selectedMeasurementType,
      assetPath: assetPathFilter,
      measurementTag: measurementTagFilter,
      units: unitsFilter,
      datasource: datasourceFilter,
    };

    // Mutate the relevant filter based on type
    switch (filterType) {
      case 'assetType':
        setSelectedAssetType('');
        nextFilters.assetType = '';
        break;
      case 'measurementType':
        setSelectedMeasurementType('');
        nextFilters.measurementType = '';
        break;
      case 'assetPath':
        setAssetPathFilter('');
        nextFilters.assetPath = '';
        break;
      case 'measurementTag':
        setMeasurementTagFilter('');
        nextFilters.measurementTag = '';
        break;
      case 'units':
        setUnitsFilter('');
        nextFilters.units = '';
        break;
      case 'datasource':
        setDatasourceFilter('');
        nextFilters.datasource = '';
        break;
    }

    // Apply filter immediately using `nextFilters`
    const allRows = measurementsList?.items || [];

    const filtered = allRows.filter((row) => {
      const matchesAssetType =
        nextFilters.assetType !== '' ? row?.a_type?.toString() === nextFilters.assetType : true;

      const matchesMeasurementType =
        nextFilters.measurementType !== ''
          ? row?.m_type?.toString() === nextFilters.measurementType
          : true;

      const matchesAssetPath =
        nextFilters.assetPath !== ''
          ? row.asset_path
              .split(':')
              .join('/')
              ?.toLowerCase()
              .includes(nextFilters.assetPath.toLowerCase())
          : true;

      const matchesTag =
        nextFilters.measurementTag !== ''
          ? row.tag?.toLowerCase().includes(nextFilters.measurementTag.toLowerCase())
          : true;

      const matchesUnits =
        nextFilters.units !== ''
          ? row.units?.toLowerCase().includes(nextFilters.units.toLowerCase())
          : true;

      const matchesDatasource =
        nextFilters.datasource !== '' ? String(row?.datasource) === nextFilters.datasource : true;

      return (
        matchesAssetType &&
        matchesMeasurementType &&
        matchesAssetPath &&
        matchesTag &&
        matchesUnits &&
        matchesDatasource
      );
    });

    const sorted = applySorting(filtered, sortModel);
    setMeasurementsData(sorted);
    setFilteredRows(filtered);
    setPaginatedIds(sorted.slice(0, pageSize).map((item) => item.id));

    const isAnyFilterApplied =
      nextFilters.assetType ||
      nextFilters.measurementType ||
      nextFilters.assetPath ||
      nextFilters.measurementTag ||
      nextFilters.units ||
      nextFilters.datasource;

    setIsFiltered(!!isAnyFilterApplied);
  };

  const ToolbarComponent = () => (
    <QuickSearchToolbar
      setShowFilter={() => {
        setIsDrawerOpen(true);
      }}
      filteredData={
        <>
          {isFiltered ? (
            <>
              {(selectedAssetType ||
                selectedMeasurementType ||
                assetPathFilter ||
                measurementTagFilter ||
                unitsFilter ||
                datasourceFilter) && (
                <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 2 }}>
                  {selectedAssetType !== '' && (
                    <Chip
                      label={`Asset Type: ${
                        assetTypesWithPath?.find((type) => type.value === Number(selectedAssetType))
                          ?.label
                      }`}
                      onDelete={() => {
                        handleChipDeleteAndFilterUpdate('assetType');
                      }}
                    />
                  )}
                  {selectedMeasurementType !== '' && (
                    <Chip
                      label={`Measurement Type: ${
                        measurementTypeList?.find(
                          (type) => type.id === Number(selectedMeasurementType),
                        )?.name
                      }`}
                      onDelete={() => {
                        handleChipDeleteAndFilterUpdate('measurementType');
                      }}
                    />
                  )}
                  {assetPathFilter !== '' && (
                    <Chip
                      label={`Asset Path: ${assetPathFilter}`}
                      onDelete={() => handleChipDeleteAndFilterUpdate('assetPath')}
                    />
                  )}
                  {measurementTagFilter !== '' && (
                    <Chip
                      label={`Measurement Tag: ${measurementTagFilter}`}
                      onDelete={() => handleChipDeleteAndFilterUpdate('measurementTag')}
                    />
                  )}
                  {unitsFilter !== '' && (
                    <Chip
                      label={`Units: ${unitsFilter}`}
                      onDelete={() => handleChipDeleteAndFilterUpdate('units')}
                    />
                  )}
                  {datasourceFilter !== '' && (
                    <Chip
                      label={`DataSource: ${
                        datasourceList?.items?.find((ds) => ds.id === Number(datasourceFilter))
                          ?.name
                      }`}
                      onDelete={() => handleChipDeleteAndFilterUpdate('datasource')}
                    />
                  )}
                </Box>
              )}
            </>
          ) : null}
        </>
      }
    />
  );
  ToolbarComponent.displayName = 'ToolbarComponent';
  const applyDrawerFilters = () => {
    const allRows = measurementsList?.items || [];

    const filtered = allRows.filter((row) => {
      const matchesAssetType =
        selectedAssetType !== '' ? row?.a_type?.toString() === selectedAssetType : true;

      const matchesMeasurementType =
        selectedMeasurementType !== '' ? row?.m_type?.toString() === selectedMeasurementType : true;
      const matchesAssetPath =
        assetPathFilter !== ''
          ? row.asset_path
              .split(':')
              .join('/')
              ?.toLowerCase()
              .includes(assetPathFilter.toLowerCase())
          : true;

      const matchesTag =
        measurementTagFilter !== ''
          ? row.tag?.toLowerCase().includes(measurementTagFilter.toLowerCase())
          : true;

      const matchesUnits =
        unitsFilter !== '' ? row.units?.toLowerCase().includes(unitsFilter.toLowerCase()) : true;

      const matchesDatasource =
        datasourceFilter !== '' ? String(row?.datasource) === datasourceFilter : true;
      return (
        matchesAssetType &&
        matchesMeasurementType &&
        matchesAssetPath &&
        matchesTag &&
        matchesUnits &&
        matchesDatasource
      );
    });

    const sorted = applySorting(filtered, sortModel);
    setMeasurementsData(sorted);
    setFilteredRows(filtered);
    setPaginatedIds(sorted.slice(0, pageSize).map((item) => item.id));
    setIsDrawerOpen(false);
    setIsFiltered(true);
  };

  const clearDrawerFilters = () => {
    setSelectedAssetType('');
    setSelectedMeasurementType('');
    setAssetPathFilter('');
    setMeasurementTagFilter('');
    setUnitsFilter('');
    setDatasourceFilter('');
    setFilteredRows([]);
    setPaginatedIds(measurementsList?.items?.slice(0, pageSize).map((i) => i.id) ?? []);
    setIsDrawerOpen(false);
    setIsFiltered(false);
    setPage(0);
  };
  return (
    <Box sx={{ height: 'calc(100vh - 75px)', width: '100%', mt: 2 }}>
      <AlertSnackbar {...snackbarState} />
      <Dialog
        open={editAssetMeasurementIds.open && editAssetMeasurementIds.editOrDetail === 'delete'}
        onClose={() =>
          setEditAssetMeasurementIds({
            assetId: '',
            editOrDetail: undefined,
            measurementId: '',
            open: false,
            metricId: undefined,
            deleteConfirmation: undefined,
          })
        }
      >
        <DialogTitle>Are you sure to delete this measurement?</DialogTitle>
        <DialogActions>
          <Button
            onClick={() =>
              setEditAssetMeasurementIds({
                assetId: '',
                editOrDetail: undefined,
                measurementId: '',
                open: false,
                metricId: undefined,
                deleteConfirmation: false,
              })
            }
            variant="outlined"
            startIcon={<CancelIcon />}
          >
            Cancel
          </Button>
          <Button
            onClick={async () => {
              await deleteMeasure({
                customerId: activeCustomer?.id ?? 0,
                assetId: editAssetMeasurementIds.assetId,
                measId: editAssetMeasurementIds.measurementId,
              });
              setEditAssetMeasurementIds({
                assetId: '',
                editOrDetail: undefined,
                measurementId: '',
                open: false,
                metricId: undefined,
                deleteConfirmation: false,
              });
            }}
            startIcon={<DeleteSharpIcon />}
            variant="contained"
            color="error"
          >
            {isLoading ? 'Deleting...' : 'Delete'}
          </Button>
        </DialogActions>
      </Dialog>
      <Dialog
        fullWidth
        sx={{
          width: '100%',
          maxWidth: '100%',
          minWidth: '600px', // Set a minimum width
          minHeight: '400px', // Set a minimum height
          '& .MuiDialog-paper': {
            // Target the inner Dialog content
            minWidth: '90%',
            minHeight: '85%',
          },
        }}
        open={
          editAssetMeasurementIds.open &&
          (editAssetMeasurementIds.editOrDetail === 'edit' ||
            editAssetMeasurementIds.editOrDetail === 'detail')
        }
        onClose={() => {
          setEditAssetMeasurementIds({
            assetId: '',
            measurementId: '',
            open: false,
            editOrDetail: undefined,
            deleteConfirmation: undefined,
          });
        }}
      >
        <DialogTitle sx={{ m: 0, p: 2 }}>
          <Typography variant="h4">
            {editAssetMeasurementIds.editOrDetail === 'edit'
              ? 'Edit Measurement'
              : 'Measurement Details'}
          </Typography>
          <IconButton
            aria-label="close"
            onClick={() =>
              setEditAssetMeasurementIds({
                assetId: '',
                measurementId: '',
                open: false,
                editOrDetail: undefined,
                deleteConfirmation: undefined,
              })
            }
            sx={{
              position: 'absolute',
              right: 8,
              top: 8,
              color: (theme) => theme.palette.grey[500],
            }}
          >
            <CloseIcon />
          </IconButton>
        </DialogTitle>
        <DialogContent>
          {editAssetMeasurementIds.editOrDetail === 'edit' && activeCustomer && asset && (
            <EditMeasurementContainer customer={activeCustomer} parentAsset={asset as Asset} />
          )}
          {editAssetMeasurementIds.editOrDetail === 'detail' && activeCustomer && asset && (
            <MeasureDetails
              measure={measurement}
              asset={asset}
              metrics={metrics?.items ?? []}
              valueTypeList={valueTypeList ?? []}
              dataSource={datasourceList?.items ?? []}
              unitsOfMeasure={unitsOfMeasure ?? []}
              measureTypeOptions={measurementTypeList ?? []}
              locationList={locationList?.items ?? []}
              dataTypeList={dataTypeList ?? []}
              hideOptions
              onEdit={() => {
                dispatch(dashboardSlice.actions.selectMainPanel('editMeasure'));
              }}
              onDelete={() => {
                dispatch(dashboardSlice.actions.selectMainPanel('editMeasure'));
              }}
            />
          )}
        </DialogContent>
      </Dialog>
      <DataGrid
        sx={{
          '& .MuiDataGrid-columnHeader': {
            background: '#F9FAFB',
          },
          '.MuiDataGrid-overlayWrapper': {
            height: '270px',
          },
          '--DataGrid-overlayHeight': '270px',
        }}
        loading={fetchingMeasures}
        rows={isFiltered ? filteredRows : measurementsData || []}
        columns={columns}
        onFilterModelChange={(model: GridFilterModel) => {
          setFilterModel(model);
          setPage(0); // Reset to first page on filter

          const allRows = measurementsList?.items || [];

          const quickFilterValues = model.quickFilterValues || [];

          const filtered = allRows.filter((row) => {
            const rowRecord = row as Record<string, any>;

            // Match all column filter items
            const columnFilterMatch = (model.items as FilterItem[]).every((filterItem) => {
              const value = rowRecord[filterItem.field]?.toString().toLowerCase() ?? '';
              const searchValue = filterItem.value?.toString().toLowerCase() ?? '';
              if (!value || !searchValue) return true;
              return value.includes(searchValue);
            });

            // Match any quick filter values
            const quickFilterMatch =
              quickFilterValues.length === 0 ||
              quickFilterValues.some((searchVal) => {
                return Object.values(rowRecord).some((fieldVal) =>
                  fieldVal?.toString().toLowerCase().includes(searchVal.toLowerCase()),
                );
              });

            return columnFilterMatch && quickFilterMatch;
          });

          setFilteredRows(filtered);

          const paginated = filtered.slice(0, pageSize).map((item) => item.id);
          setPaginatedIds(paginated);
        }}
        density="comfortable"
        autoPageSize
        disableRowSelectionOnClick
        onPaginationModelChange={(newModel) => {
          setPageSize(newModel.pageSize);
          setPage(newModel.page);

          const source = filteredRows.length ? filteredRows : measurementsList?.items || [];

          const ids = source
            .slice(
              newModel.page * newModel.pageSize,
              newModel.page * newModel.pageSize + newModel.pageSize,
            )
            .map((item) => item.id);

          setPaginatedIds(ids);
        }}
        slotProps={{
          toolbar: {
            showQuickFilter: true,
            printOptions: { disableToolbarButton: true },
            csvOptions: { disableToolbarButton: true },
          },
        }}
        onSortModelChange={(model) => {
          setSortModel(model);
          const sorted = applySorting(measurementsData, model);
          setMeasurementsData(sorted);
        }}
        slots={{
          toolbar: ToolbarComponent,
          noRowsOverlay: CustomNoRowsOverlay,
        }}
        ignoreDiacritics
        getRowId={(row) => row.id} // Ensure each row has a unique ID
      />
      <Drawer anchor="right" open={isDrawerOpen} onClose={() => setIsDrawerOpen(false)}>
        <Box sx={{ width: 600, p: 2 }}>
          <Typography variant="h6" gutterBottom>
            Filters
          </Typography>

          <FormControl fullWidth sx={{ mb: 2 }}>
            <Typography variant="subtitle2">Asset Path</Typography>
            <TextField
              placeholder="Enter asset path"
              value={assetPathFilter}
              onChange={(e) => setAssetPathFilter(e.target.value)}
              variant="outlined"
            />
          </FormControl>

          <FormControl fullWidth sx={{ mb: 2 }}>
            <Typography variant="subtitle2">Measurement Tag</Typography>
            <TextField
              placeholder="Enter tag"
              value={measurementTagFilter}
              onChange={(e) => setMeasurementTagFilter(e.target.value)}
              variant="outlined"
            />
          </FormControl>

          {/* <FormControl fullWidth sx={{ mb: 2 }}>
            <Typography variant="subtitle2">Units</Typography>
            <TextField
              placeholder="Enter units"
              value={unitsFilter}
              onChange={(e) => setUnitsFilter(e.target.value)}
              variant="outlined"
            />
          </FormControl> */}

          <FormControl fullWidth sx={{ mb: 2 }}>
            <Typography variant="subtitle2">Asset Type</Typography>
            <Select
              value={selectedAssetType}
              onChange={(e) => setSelectedAssetType(String(e.target.value))}
              displayEmpty
            >
              <MenuItem value="">All</MenuItem>
              {assetTypesWithPath?.map((type, index) => {
                return (
                  <MenuItem key={index} value={type.value}>
                    {type.label}
                  </MenuItem>
                );
              })}
            </Select>
          </FormControl>

          <FormControl fullWidth>
            <Typography variant="subtitle2">Measurement Type</Typography>
            <Select
              value={selectedMeasurementType}
              onChange={(e) => setSelectedMeasurementType(String(e.target.value))}
              displayEmpty
            >
              <MenuItem value="">All</MenuItem>
              {measurementTypeList?.map((type, index) => (
                <MenuItem key={index} value={type.id}>
                  {type.name}
                </MenuItem>
              ))}
            </Select>
          </FormControl>

          <FormControl fullWidth sx={{ mt: 2 }}>
            <Typography variant="subtitle2">DataSource</Typography>
            <Select
              value={datasourceFilter}
              onChange={(e) => setDatasourceFilter(String(e.target.value))}
              displayEmpty
            >
              <MenuItem value="">All</MenuItem>
              {datasourceList?.items?.map((ds) => (
                <MenuItem key={ds.id} value={ds.id}>
                  {ds.name}
                </MenuItem>
              ))}
            </Select>
          </FormControl>

          <Box mt={3} sx={{ display: 'flex', gap: 1 }}>
            <Button fullWidth variant="contained" onClick={applyDrawerFilters}>
              Apply
            </Button>
            <Button fullWidth variant="outlined" onClick={clearDrawerFilters}>
              Clear Filters
            </Button>
          </Box>
        </Box>
      </Drawer>

      {openTrendModal && (
        <Dialog
          fullWidth
          sx={{
            width: '100%',
            maxWidth: '100%',
            minWidth: '600px', // Set a minimum width
            minHeight: '400px', // Set a minimum height
            '& .MuiDialog-paper': {
              // Target the inner Dialog content
              minWidth: '90%',
              minHeight: '85%',
            },
          }}
          open={openTrendModal}
          onClose={() => {
            deleteUrlParams();
            setOpenTrendModal(false);
          }}
        >
          <DialogTitle id="customized-dialog-title">Measurement Trend</DialogTitle>
          <IconButton
            aria-label="close"
            onClick={() => {
              deleteUrlParams();
              setOpenTrendModal(false);
            }}
            sx={(theme) => ({
              position: 'absolute',
              right: 8,
              top: 8,
              color: theme.palette.grey[500],
            })}
          >
            <CloseIcon />
          </IconButton>
          <DialogContent dividers>
            <TestDashboards />
          </DialogContent>
        </Dialog>
      )}
    </Box>
  );
};

export default CustomerMeasurementBrowser;
