import initStoryshots from '@storybook/addon-storyshots';
import { imageSnapshot } from '@storybook/addon-storyshots-puppeteer';

const WAIT_DELAY_MS = 0;

initStoryshots({
  suite: 'Snapshot Visual tests',
  test: imageSnapshot({
    beforeScreenshot: () =>
      new Promise<void>((resolve) =>
        setTimeout(() => {
          resolve();
        }, WAIT_DELAY_MS)
      ),
  }),
  storyNameRegex: 'Three Level Tree',
});
