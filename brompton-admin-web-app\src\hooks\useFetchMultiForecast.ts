import { ThunkDispatch } from '@reduxjs/toolkit';
import { useCallback, useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { UnitOfMeasure } from '~/measurements/domain/types';
import { measuresApi } from '~/redux/api/measuresApi';
import { timeseriesApi } from '~/redux/api/timeseriesApi';
import { getCustomerId } from '~/redux/selectors/customerSelectors';
import { AggByOptions } from '~/types/dashboard';
import { AssetMeasurementDetails } from '~/types/measures';
import { SingleScatterTimeSeriesData } from '~/types/timeseries';
import { Forecast, MultiPlotWidget } from '~/types/widgets';

type useFetchMultiForecastProps = {
  subplots: MultiPlotWidget['subplots'];
};
type MeasuresData = {
  isLoading: boolean;
  isError: boolean;
  error: string;
  tsData: SingleScatterTimeSeriesData;
  measureData: AssetMeasurementDetails;
  unitOfMeasures: UnitOfMeasure[];
};

const useFetchMultiForecast = ({ subplots }: useFetchMultiForecastProps) => {
  const dispatch = useDispatch<ThunkDispatch<any, any, any>>();
  const customerId = useSelector(getCustomerId);

  const [forecastAssetsMeasueres, setForecastAssetsMeasures] = useState<
    { assetId: string; measureIds: string[] }[]
  >([]);
  const [state, setState] = useState<{
    data: undefined | MeasuresData[];
    isLoading: boolean;
    isError: boolean;
    forcastedData: undefined | MeasuresData[];
  }>({
    data: undefined,
    isLoading: true,
    isError: false,
    forcastedData: undefined,
  });

  useEffect(() => {
    if (!subplots) return;

    const assetToMeasuresMap: Record<string, Set<string>> = {};

    subplots.forEach((subplot) => {
      subplot.assetMeasures.forEach((am) => {
        if (am.ForecastSettings?.showForecast) {
          if (!assetToMeasuresMap[am.assetId]) {
            assetToMeasuresMap[am.assetId] = new Set();
          }
          am.measureId.forEach((mId) => {
            assetToMeasuresMap[am.assetId].add(mId);
          });
        }
      });
    });

    const assetWithMeasurements = Object.entries(assetToMeasuresMap).map(
      ([assetId, measureSet]) => ({
        assetId,
        measureIds: Array.from(measureSet),
      }),
    );

    setForecastAssetsMeasures(assetWithMeasurements);
  }, [subplots]);

  const fetchForecastData = useCallback(
    async (
      forecastMeasureId: string,
      period: string,
      aggBy: number,
    ): Promise<{
      error: boolean;
      tsData: Record<number, SingleScatterTimeSeriesData>;
    }> => {
      const { data: tsData, isSuccess: isTsSuccess } = await dispatch(
        timeseriesApi.endpoints.getForecastMeasurementSeries.initiate({
          customerId,
          measId: forecastMeasureId,
          assetTz: true,
          forecast: period,
          agg: AggByOptions[aggBy].serverValue,
        }),
      );
      if (!isTsSuccess || !tsData) {
        return { error: false, tsData: {} };
      }
      return {
        error: false,
        tsData,
      };
    },
    [dispatch, customerId],
  );

  const fetchMeasureDataByAsset = useCallback(
    async (assetId: string, measureId: string) => {
      const { data: measureData, isSuccess } = await dispatch(
        measuresApi.endpoints.getMeasurementById.initiate({
          customerId,
          assetId,
          measId: measureId,
        }),
      );

      if (!isSuccess || !measureData) {
        throw new Error(`Error fetching measure data for measureId: ${measureId}`);
      }

      return measureData;
    },
    [dispatch, customerId],
  );

  const fetchUnitOfMeasure = useCallback(
    async (assetMeasurementTypeId: number) => {
      const { data, isSuccess } = await dispatch(
        measuresApi.endpoints.getUnitsOfMeasure.initiate({
          measurementTypeId: assetMeasurementTypeId,
        }),
      );

      if (!isSuccess || !data) {
        throw new Error('Error fetching unit of measure data');
      }

      return data;
    },
    [dispatch],
  );

  useEffect(() => {
    const getForecastedData = async () => {
      try {
        const filteredAssetMeasures = forecastAssetsMeasueres.filter(
          (am) => am.assetId.trim() !== '' && am.measureIds.some((mId) => mId.trim() !== ''),
        );

        const allPromises: Promise<MeasuresData>[] = filteredAssetMeasures.flatMap((am) =>
          am.measureIds.map(async (measureId) => {
            try {
              const measureData = await fetchMeasureDataByAsset(am.assetId, measureId);
              const unitOfMeasures = await fetchUnitOfMeasure(measureData.typeId);
              return {
                isLoading: false,
                isError: false,
                error: '',
                measureData,
                unitOfMeasures,
              } as MeasuresData;
            } catch (error: any) {
              console.error(
                `Error fetching data for assetId: ${am.assetId}, measureId: ${measureId}:`,
                error,
              );
              return {
                isLoading: false,
                isError: true,
                error,
              } as MeasuresData;
            }
          }),
        );

        const results = await Promise.all(allPromises);

        const measureWithSettings = results
          .map((result, index) => {
            const measureData = result.measureData;
            if (!measureData) return null;

            const flatMapped = filteredAssetMeasures.flatMap((am) =>
              am.measureIds.map((mid) => ({
                assetId: am.assetId,
                measureId: mid,
              })),
            );

            const matched = flatMapped[index];
            if (!matched) return null;

            let forecastSettings;
            for (const subplot of subplots) {
              const match = subplot.assetMeasures.find(
                (am) => am.assetId === matched.assetId && am.measureId.includes(matched.measureId),
              );
              if (match) {
                forecastSettings = match.ForecastSettings;
                break;
              }
            }

            return {
              measureId: measureData.measurementId,
              measureData,
              unitOfMeasures: result.unitOfMeasures,
              forecastSettings,
            };
          })
          .filter(Boolean) as {
          measureId: number;
          measureData: AssetMeasurementDetails;
          unitOfMeasures: UnitOfMeasure[];
          forecastSettings: Forecast;
        }[];

        const forecastDataResults = await Promise.all(
          measureWithSettings.map(
            async ({ measureId, measureData, unitOfMeasures, forecastSettings }) => {
              const period = forecastSettings?.period ?? 'eom';
              const aggBy = 2;
              const { tsData } = await fetchForecastData(measureId.toString(), period, aggBy);

              return {
                isLoading: false,
                isError: false,
                error: '',
                tsData,
                measureData,
                unitOfMeasures,
              } as MeasuresData;
            },
          ),
        );

        setState((prev) => ({
          ...prev,
          isLoading: false,
          forcastedData: forecastDataResults,
        }));
      } catch (e) {
        console.error(e);
        setState((prev) => ({
          ...prev,
          isLoading: false,
          isError: true,
          forcastedData: [],
        }));
      }
    };

    if (forecastAssetsMeasueres.length > 0) {
      getForecastedData();
    }
  }, [dispatch, customerId, forecastAssetsMeasueres, subplots]);

  return {
    forecastAssetsMeasueres,
    forcastedData: state.forcastedData,
    isLoading: state.isLoading,
  };
};

export default useFetchMultiForecast;
