import { Checkbox, FormControlLabel, FormGroup, TextField } from '@mui/material';
import { KPIWidgets, setKPIWidgetSettings } from '~/types/widgets';

type PrefixSuffixContanerProps = {
  settings: KPIWidgets;
  handleSettingsChange: setKPIWidgetSettings;
};
const PrefixSuffixContaner = ({ settings, handleSettingsChange }: PrefixSuffixContanerProps) => {
  const handleSuffixVisibility = (event: React.ChangeEvent<HTMLInputElement>, checked: boolean) => {
    handleSettingsChange({
      ...settings,
      suffix: { ...settings.suffix, isVisible: checked },
    });
  };
  const handleSuffixChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    handleSettingsChange({
      ...settings,
      suffix: { ...settings.suffix, value: event.target.value },
    });
  };

  const handlePrefixChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    handleSettingsChange({
      ...settings,
      prefix: { ...settings.prefix, value: event.target.value },
    });
  };
  const handlePrefixVisibility = (event: React.ChangeEvent<HTMLInputElement>, checked: boolean) => {
    handleSettingsChange({
      ...settings,
      prefix: { ...settings.prefix, isVisible: checked },
    });
  };
  return (
    <>
      <FormGroup>
        <FormControlLabel
          sx={{
            width: 'fit-content',
          }}
          control={
            <Checkbox
              sx={{
                p: 2,
              }}
              checked={settings.prefix?.isVisible}
              onChange={handlePrefixVisibility}
              name="prefix"
            />
          }
          label="Add Prefix?"
        />
      </FormGroup>
      {settings.prefix?.isVisible ? (
        <FormGroup sx={{ p: 2, pb: 0, pt: 0 }}>
          <TextField
            name="value"
            onChange={handlePrefixChange}
            value={settings.prefix?.value}
            label="Prefix"
            variant="outlined"
            margin="normal"
            fullWidth
          />
        </FormGroup>
      ) : null}
      <FormGroup>
        <FormControlLabel
          sx={{
            width: 'fit-content',
          }}
          control={
            <Checkbox
              sx={{
                p: 2,
              }}
              checked={settings.suffix?.isVisible}
              onChange={handleSuffixVisibility}
              name="suffix"
            />
          }
          label="Add Suffix?"
        />
      </FormGroup>
      {settings.suffix?.isVisible ? (
        <FormGroup sx={{ p: 2, pb: 0, pt: 0 }}>
          <TextField
            name="suffix.value"
            onChange={handleSuffixChange}
            value={settings.suffix?.value}
            label="Suffix"
            variant="outlined"
            margin="normal"
            fullWidth
          />
        </FormGroup>
      ) : null}
    </>
  );
};

export default PrefixSuffixContaner;
