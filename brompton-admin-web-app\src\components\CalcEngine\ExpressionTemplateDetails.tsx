import { Box, Typography } from '@mui/material';
import { calc_engine_template } from '~/types/calc_engine';

type ExpressionTemplateDetailsProps = {
  selectedTemplate: calc_engine_template | null;
};
const ExpressionTemplateDetails = ({ selectedTemplate }: ExpressionTemplateDetailsProps) => {
  return (
    <>
      {selectedTemplate && (
        <Box mt={3}>
          <Typography variant="body1">
            <Box component="span" fontWeight="bold">
              Expression Template :{' '}
            </Box>
            {selectedTemplate?.name}
          </Typography>
          <Typography variant="body1">
            <Box component="span" fontWeight="bold">
              Description :{' '}
            </Box>
            {selectedTemplate?.description}
          </Typography>
          <Typography variant="body1">
            <Box component="span" fontWeight="bold">
              Expression :{''}
            </Box>
            {selectedTemplate?.expression}
          </Typography>
        </Box>
      )}
    </>
  );
};

export default ExpressionTemplateDetails;
