import {
  Autocomplete,
  CircularProgress,
  FormControl,
  TableCell,
  TableRow,
  TextField,
} from '@mui/material';
import { useGetAllMeasurementsQuery } from '~/redux/api/measuresApi';
import { formatMetricLabel } from '~/utils/utils';

type AssetOption = {
  label: string;
  id: number;
};

type MeasurementOption = {
  id: string;
  name: string;
  fullName?: string;
};

interface AssetMeasureRowProps {
  idx: number;
  row: { fileAsset: string; fileMeasure: string };
  assetId: string;
  measurementId: string;
  assetOptions: AssetOption[];
  isAssetLoading: boolean;
  customerId: number;
  handleAssetChange: (key: string, event: any, newValue: AssetOption | null) => void;
  handleMeasurementChange: (key: string, event: any, newValue: MeasurementOption | null) => void;
}

const AssetMeasureRow: React.FC<AssetMeasureRowProps> = ({
  idx,
  row,
  assetId,
  measurementId,
  assetOptions,
  isAssetLoading,
  customerId,
  handleAssetChange,
  handleMeasurementChange,
}) => {
  const key = `${row.fileAsset}||${row.fileMeasure}`;

  // Fetch measurements for this specific row's selected asset
  const { data: measurementsData, isLoading: isMeasurementsLoading } = useGetAllMeasurementsQuery(
    {
      customerId,
      assetId: assetId ? Number(assetId) : 0,
    },
    {
      skip: !customerId || !assetId,
      refetchOnMountOrArgChange: true,
    },
  );

  const measurementOptions = measurementsData
    ? measurementsData.map((m) => ({ id: String(m.measurementId), name: formatMetricLabel(m.tag) }))
    : [];
  const selectedAsset = assetOptions.find((asset) => String(asset.id) === assetId) || null;
  const selectedMeasurement =
    measurementOptions.find((m) => String(m.id) === measurementId) || null;

  return (
    <TableRow key={idx}>
      <TableCell>{row.fileAsset}</TableCell>
      <TableCell>{row.fileMeasure}</TableCell>
      <TableCell>
        <FormControl fullWidth>
          <Autocomplete
            fullWidth
            id={`asset-autocomplete-${idx}`}
            loading={isAssetLoading}
            options={assetOptions}
            getOptionLabel={(option) => option.label}
            onChange={(event, newValue) => handleAssetChange(key, event, newValue)}
            value={selectedAsset}
            renderInput={(params) => (
              <TextField
                {...params}
                label="Asset"
                variant="outlined"
                InputProps={{
                  ...params.InputProps,
                  endAdornment: (
                    <>
                      {isAssetLoading ? <CircularProgress color="inherit" size={20} /> : null}
                      {params.InputProps.endAdornment}
                    </>
                  ),
                }}
              />
            )}
          />
        </FormControl>
      </TableCell>
      <TableCell>
        <FormControl fullWidth>
          <Autocomplete
            fullWidth
            id={`measurement-autocomplete-${idx}`}
            loading={isMeasurementsLoading}
            options={measurementOptions}
            getOptionLabel={(option) => option.name}
            onChange={(event, newValue) => handleMeasurementChange(key, event, newValue)}
            value={selectedMeasurement}
            disabled={!assetId}
            renderInput={(params) => (
              <TextField
                {...params}
                label="Measurement"
                variant="outlined"
                InputProps={{
                  ...params.InputProps,
                  endAdornment: (
                    <>
                      {isMeasurementsLoading ? (
                        <CircularProgress color="inherit" size={20} />
                      ) : null}
                      {params.InputProps.endAdornment}
                    </>
                  ),
                }}
              />
            )}
          />
        </FormControl>
      </TableCell>
    </TableRow>
  );
};

export default AssetMeasureRow;
