import * as yup from 'yup';
export type calc_engine_template = {
  id: number;
  name: string;
  expression: string;
  description: string;
  dataType: number;
};
export type calc_engine_template_collection = {
  items: calc_engine_template[];
  total: number;
};

export type calc_engine_template_DTO = calc_engine_template_collection;

export type calc_engine_template_instance = {
  id?: number;
  name: string;
  expression: string;
  description: string;
  dataType: number;
};

export type calc_Instance = {
  id: number;
  ispersisted: boolean;
  pollPeriod: number;
};

export type measure_calc_engine = {
  calcTemplate: {
    id: number;
    name: string;
    expression: string;
    description: string;
    dataType: {
      id: number;
      name: string;
    };
  };
  calcInstance: calc_Instance;
  calcInputs: input[];
};
export type DataTypes = {
  id: number;
  name: string;
};

export type DataTypesCollection = {
  items: DataTypes[];
  total: number;
};

export type poll_period = {
  id: number;
  value: string;
};

export type poll_period_collection = {
  items: poll_period[];
  total: number;
};
export type input = {
  id?: string | number;
  inputId?: number;
  inputLabel: string;
  constantType: string;
  constantValue?: string;
  measurementId?: number;
  measurement?: string;
  constantNumber?: number;
  constantString?: string;
  comments?: string;
  comment?: string;
};
export type create_calc_engine_instance = {
  id?: number;
  templateId: number;
  customerId: number;
  outputMesurementId: number;
  ispersisted: boolean;
  pollPeriod?: number;
  inputs: input[];
  iswriteback?: boolean;
};

export const calculationMeasurementSchema = yup.object({
  expression_template_id: yup.number().required('Expression template is required.').nullable(),
  is_persisted: yup.boolean().required('Persistence flag is required.').default(false),
  writeback: yup.boolean().nullable().default(false),
  // .when('is_persisted', {
  //   is: true,
  //   then: (schema) => schema.required('Writeback is required when persisted.'),
  //   otherwise: (schema) => schema.notRequired(),
  // }),

  poll_period: yup
    .object({
      id: yup.number().required(),
      value: yup.mixed().required(),
      label: yup.string().nullable(),
    })
    .nullable()
    .when('is_persisted', {
      is: true,
      then: (schema) => schema.required('Poll period is required when persisted.'),
      otherwise: (schema) => {
        return schema.nullable().notRequired();
      },
    }),

  variable_inputs: yup
    .array()
    .of(
      yup.object({
        variable: yup.string().required('Variable name is required.'),
        type: yup
          .string()
          .oneOf(['measurement', 'constant'])
          .required('Variable type is required.'),
        metric_id: yup
          .number()
          .nullable()
          .when('type', {
            is: 'measurement',
            then: (schema) => schema.required('Metric is required for measurement input.'),
            otherwise: (schema) => schema.notRequired().nullable(),
          }),

        constant_value: yup
          .mixed()
          .nullable()
          .when('type', {
            is: 'constant',
            then: (schema) => schema.required('Constant value is required.'),
            otherwise: (schema) => schema.notRequired().nullable(),
          }),

        comment: yup.string().nullable(),
      }),
    )
    .required('Variable inputs are required.')
    .min(1, 'At least one variable input is required.'),
});

export type CalculationMeasurementSchema = yup.InferType<typeof calculationMeasurementSchema>;
export type CalculationMeasurementSchemaData = CalculationMeasurementSchema;

export const assetTocalculationMeasurementSchema = yup.object({
  expression_template_id: yup.number().required('Expression template is required.').nullable(),
  is_persisted: yup.boolean().required('Persistence flag is required.').default(false),
  writeback: yup.boolean().nullable().default(false),
  // .when('is_persisted', {
  //   is: true,
  //   then: (schema) => schema.required('Writeback is required when persisted.'),
  //   otherwise: (schema) => schema.notRequired(),
  // }),

  poll_period: yup
    .object({
      id: yup.number().required(),
      value: yup.mixed().required(),
      label: yup.string().nullable(),
    })
    .nullable()
    .when('is_persisted', {
      is: true,
      then: (schema) => schema.required('Poll period is required when persisted.'),
      otherwise: (schema) => {
        return schema.nullable().notRequired();
      },
    }),

  variable_inputs: yup
    .array()
    .of(
      yup.object({
        variable: yup.string().required('Variable name is required.'),
        type: yup
          .string()
          .oneOf(['measurement', 'constant'])
          .required('Variable type is required.'),
        metric_id: yup
          .string()
          .nullable()
          .when('type', {
            is: 'measurement',
            then: (schema) => schema.required('Metric is required for measurement input.'),
            otherwise: (schema) => schema.notRequired().nullable(),
          }),

        constant_value: yup
          .mixed()
          .nullable()
          .when('type', {
            is: 'constant',
            then: (schema) => schema.required('Constant value is required.'),
            otherwise: (schema) => schema.notRequired().nullable(),
          }),

        comment: yup.string().nullable(),
      }),
    )
    .required('Variable inputs are required.')
    .min(1, 'At least one variable input is required.'),
});

export type assetToCalculationMeasurementSchema = yup.InferType<
  typeof assetTocalculationMeasurementSchema
>;
export type assetToCalculationMeasurementSchemaData = assetToCalculationMeasurementSchema;
export type Variable = {
  metric_id?: number | null | undefined;
  constant_value?: any | null | undefined;
  comment?: string | null | undefined;
  type: 'measurement' | 'constant' | undefined;
  variable: string;
};
