import {
  Box,
  FormControl,
  FormGroup,
  FormLabel,
  MenuItem,
  Select,
  SelectChangeEvent,
  TextField,
} from '@mui/material';
import { StatsWidget } from '~/types/widgets';
import { fontWeights } from '~/utils/utils';

type StatsConfigSettingsProps = {
  key: string;
  statsWidgetSettings: StatsWidget;
  setStatsWidgetSettings: (value: React.SetStateAction<StatsWidget>) => void;
};
const StatsCOnfigSettings = ({
  key,
  statsWidgetSettings,
  setStatsWidgetSettings,
}: StatsConfigSettingsProps) => {
  const onHandleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const [key, subKey] = event.target.name.split('.');
    setStatsWidgetSettings({
      ...statsWidgetSettings,
      [key]: {
        // ...statsWidgetSettings[key],
        [subKey]: subKey === 'fontSize' ? Number(event.target.value) : event.target.value,
      },
    });
  };
  const onFontWeightChange = (event: SelectChangeEvent<string>, child: React.ReactNode) => {
    const [key, subKey] = event.target.name.split('.');
    setStatsWidgetSettings({
      ...statsWidgetSettings,
      [key]: {
        // ...statsWidgetSettings[key],
        [subKey]: event.target.value,
      },
    });
  };
  return (
    <>
      <FormGroup sx={{ p: 2, pt: 0, pb: 0 }}>
        <TextField
          name={`${key}.fontSize`}
          type="number"
          onChange={onHandleChange}
          defaultValue={12}
          // eslint-disable-next-line @typescript-eslint/ban-ts-comment
          // @ts-ignore
          value={statsWidgetSettings[`${key}`]?.fontSize}
          label="Font Size"
          variant="outlined"
          margin="normal"
          fullWidth
        />
      </FormGroup>
      <FormGroup sx={{ p: 2, pt: 0, pb: 0 }}>
        <TextField
          name={`${key}.fontColor`}
          type="color"
          label="Font Color"
          onChange={onHandleChange}
          // eslint-disable-next-line @typescript-eslint/ban-ts-comment
          // @ts-ignore
          value={statsWidgetSettings[`${key}`]?.fontColor}
          variant="outlined"
          margin="normal"
          fullWidth
        />
      </FormGroup>
      <FormControl sx={{ mb: 2, p: 2, pb: 0 }}>
        <Box display="flex" alignItems="center" width="100%" gap={1}>
          <FormLabel id="y-select-lable">Font Weight&nbsp;&nbsp;</FormLabel>
          <Select
            labelId="y-select-lable"
            id="tabel-series"
            name={`${key}.fontWeight`}
            defaultValue="bolder"
            // eslint-disable-next-line @typescript-eslint/ban-ts-comment
            // @ts-ignore
            value={statsWidgetSettings[`${key}`]?.fontWeight}
            onChange={onFontWeightChange}
          >
            {fontWeights.map((fonts: string) => {
              return (
                <MenuItem key={fonts} value={fonts}>
                  {fonts}
                </MenuItem>
              );
            })}
          </Select>
        </Box>
      </FormControl>
    </>
  );
};
export default StatsCOnfigSettings;
