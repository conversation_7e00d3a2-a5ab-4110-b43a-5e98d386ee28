import AddIcon from '@mui/icons-material/Add';
import DeleteIcon from '@mui/icons-material/Delete';
import { Box, Button, Typography } from '@mui/material';
import React from 'react';
import { MultiPlotWidget } from '~/types/widgets';
import AssetMeasureConfig from './AssetMeasureConfig';

type Props = {
  settings: MultiPlotWidget;
  subplot: MultiPlotWidget['subplots'][number];
  subplotIndex: number;
  handleSettingsChange: (
    value: MultiPlotWidget | ((prev: MultiPlotWidget) => MultiPlotWidget),
  ) => void;
};

const SubplotSettingsCard: React.FC<Props> = ({
  subplot,
  subplotIndex,
  handleSettingsChange,
  settings,
}) => {
  const addAssetMeasure = () => {
    handleSettingsChange((prev) => ({
      ...prev,
      subplots: prev.subplots.map((sp, idx) =>
        idx === subplotIndex
          ? {
              ...sp,
              assetMeasures: [
                ...sp.assetMeasures,
                {
                  id: sp.assetMeasures.length + 1,
                  assetId: '',
                  measureId: [],
                  chartType: 'bar',
                  aggBy: 1,
                  showAvgLine: false,
                  showMinLine: false,
                  showMaxLine: false,
                  thresholdColor: '',
                  thresholdValue: 0,
                  thresholdName: '',
                  thresholdStyle: 'solid',
                  showThresholdLine: false,
                  chartColor: '#000000',
                  showRangeSlider: false,
                  overrideChartColor: false,
                  ForecastSettings: {
                    showForecast: false,
                    period: '24hr',
                    forecastColor: '#000000',
                    showMean: false,
                    meanName: '',
                    meanColor: '',
                    meanStyle: 'solid',
                  },
                  selectedDbMeasureId: '',
                },
              ],
            }
          : sp,
      ),
    }));
  };

  const removeSubplot = () => {
    const subplotIdToRemove = subplot.id;
    handleSettingsChange((prev) => ({
      ...prev,
      subplots: prev.subplots.filter((sp) => sp.id !== subplotIdToRemove),
    }));
  };

  return (
    <Box key={subplot.id} mb={1}>
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          gap: 2,
          mb: 1,
        }}
      >
        {/* Subplot Heading + Action Buttons */}
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            flexWrap: 'wrap',
            gap: 2,
          }}
        >
          <Typography sx={{ fontWeight: 600, fontSize: '1.1rem' }}>
            Subplot {subplotIndex + 1}
          </Typography>

          <Box display="flex" gap={2} flexWrap="wrap">
            <Button
              startIcon={<AddIcon />}
              color="info"
              variant="contained"
              onClick={addAssetMeasure}
            >
              Add Asset Measure
            </Button>
            <Button
              startIcon={<DeleteIcon />}
              color="error"
              variant="contained"
              onClick={removeSubplot}
            >
              Delete Subplot
            </Button>
          </Box>
        </Box>
      </Box>
      {subplot.assetMeasures.map((measure, measureIndex) => (
        <AssetMeasureConfig
          key={measure.id}
          subplotIndex={subplotIndex}
          measureIndex={measureIndex}
          measure={measure}
          handleSettingsChange={handleSettingsChange}
          dbMeasureIdToName={settings.dbMeasureIdToName ?? {}}
          settings={settings}
          assetMeasureLength={subplot.assetMeasures.length}
        />
      ))}
    </Box>
  );
};

export default SubplotSettingsCard;
