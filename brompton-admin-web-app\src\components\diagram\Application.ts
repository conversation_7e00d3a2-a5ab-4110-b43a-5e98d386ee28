import * as SRD from '@projectstorm/react-diagrams';
import { CustomNodeFactory } from './/model/CustomNodeFactory';
import { AdvancedLinkFactory } from './Animation';
import { CustomPortFactory } from './model/CustomPortFactory';
import { CustomPortModel } from './model/CustomPortModel';
import { CustomType } from './model/CustomType';

class Application {
  protected activeModel: SRD.DiagramModel;
  protected diagramEngine: SRD.DiagramEngine;

  constructor() {
    this.diagramEngine = SRD.default();
    this.activeModel = new SRD.DiagramModel();
    this.newModel();
  }

  public newModel() {
    this.activeModel = new SRD.DiagramModel();
    this.diagramEngine.setModel(this.activeModel);

    const customTypes: CustomType[] = ['building', 'solarPanel', 'battery', 'grid', 'CHP'];

    customTypes.map((type) => {
      this.diagramEngine
        .getPortFactories()
        .registerFactory(new CustomPortFactory(type, (config) => new CustomPortModel(type, type)));
      this.diagramEngine.getNodeFactories().registerFactory(new CustomNodeFactory(type));
      this.diagramEngine.getLinkFactories().registerFactory(new AdvancedLinkFactory());
    });
  }

  public getActiveDiagram(): SRD.DiagramModel {
    return this.activeModel;
  }

  public getDiagramEngine(): SRD.DiagramEngine {
    return this.diagramEngine;
  }
}

export default Application;
