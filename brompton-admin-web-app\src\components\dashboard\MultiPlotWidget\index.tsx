import ArticleIcon from '@mui/icons-material/Article';
import PictureAsPdfIcon from '@mui/icons-material/PictureAsPdf';
import { Box, CircularProgress, Typography } from '@mui/material';
import html2canvas from 'html2canvas';
import jsPDF from 'jspdf';
import dynamic from 'next/dynamic';
import { Data, Datum, PlotData, ScatterData } from 'plotly.js';
import { ReactNode, useRef } from 'react';
import { useSelector } from 'react-redux';
import * as XLSX from 'xlsx';
import CommonWidgetContainer from '~/components/common/CommonWidgetContainer';
import { useFetchMultiPlotData } from '~/hooks/useFetchMultiPlotData';
import { useMultiPlotWidgetLogic } from '~/hooks/useMultiPlotWidgetLogic';
import { getMetricsIdToName } from '~/redux/selectors/dashboardSelectors';
import { MultiPlotWidget } from '~/types/widgets';
import MultiPlotWidgetSettingsDialog from './MultiPlotWidgetSettingsDialog';

const Plot = dynamic(() => import('react-plotly.js'), { ssr: false });

type MultiPlotWidgetProps = {
  id: string;
  settings: MultiPlotWidget;
};

const MultiPlotWidgetContainer = ({ id, settings }: MultiPlotWidgetProps) => {
  const containerRef = useRef(null);
  const metricsIdToName = useSelector(getMetricsIdToName);
  const { data: subplotData, isLoading } = useFetchMultiPlotData(settings, metricsIdToName);
  const { layoutConfig, allTraces } = useMultiPlotWidgetLogic(settings, subplotData);
  const exportToPDF = async () => {
    if (containerRef.current) {
      const canvas = await html2canvas(containerRef.current, {
        scale: 3,
      });
      const imgData = canvas.toDataURL('image/png');
      const pdf = new jsPDF({
        orientation: 'landscape',
        unit: 'mm',
        format: 'a4',
      });
      const imgProps = pdf.getImageProperties(imgData);
      const pdfWidth = pdf.internal.pageSize.getWidth();
      const pdfHeight = (imgProps.height * pdfWidth) / imgProps.width;

      pdf.addImage(imgData, 'PNG', 0, 0, pdfWidth, pdfHeight);
      pdf.save('chart.pdf');
    }
  };
  const createXLSXFile = (): void => {
    const data = allTraces as Data[];
    const workbook = XLSX.utils.book_new();
    data.forEach((dataItem1, index) => {
      // Convert each Data object to a worksheet
      const worksheetData = [];
      const dataItem = dataItem1 as PlotData | ScatterData;
      let name = '';
      for (let i = 0; i < dataItem.x.length; i++) {
        const rowData: {
          Time: string;
          [y: string]: Datum | Datum[];
        } = {
          Time: new Date(dataItem.x[i] as string).toISOString().slice(0, 19).replace('T', ' '),
          [`${dataItem.name}`]: dataItem.y[i],
        };
        name = dataItem.name;
        worksheetData.push(rowData);
      }
      const worksheet = XLSX.utils.json_to_sheet(worksheetData);
      const safeName = name.replace(/[:\\/?*\[\]]/g, '_');
      const sheetName = `Plot ${(dataItem as any).exportId ?? ''}_${safeName}`.slice(0, 31);
      XLSX.utils.book_append_sheet(workbook, worksheet, sheetName);
    });
    const now = new Date().toISOString().slice(0, 19).replace(/T/, '_').replace(/:/g, '-');
    XLSX.writeFile(workbook, `ChartData_${now}.xlsx`);
  };
  const options = [
    {
      method: exportToPDF,
      content: (
        <>
          <PictureAsPdfIcon style={{ marginRight: '5px' }} />
          Export To PDF
        </>
      ),
    },
    {
      method: createXLSXFile,
      content: (
        <>
          <ArticleIcon style={{ marginRight: '5px' }} />
          Export To Excel
        </>
      ),
    },
  ];
  return (
    <CommonWidgetContainer
      id={id}
      settings={settings}
      widgetName="multi-plot"
      widgetType="multi-plot"
      options={
        options.filter((option) => option !== null) as {
          method: () => void | Promise<void>;
          content: ReactNode;
        }[]
      }
      widgetContent={
        <>
          <Box
            ref={containerRef}
            sx={{
              display: 'flex',
              height: '100%',
              flexDirection: 'column',
              alignItems: 'center',
            }}
          >
            {/* Loader display when loading */}
            {isLoading ? (
              <Box
                display="flex"
                alignItems="center"
                justifyContent="center"
                height="100%"
                minHeight="300px"
              >
                <CircularProgress />
              </Box>
            ) : allTraces.length > 0 ? (
              <Plot
                data={allTraces}
                layout={layoutConfig}
                config={{
                  responsive: true,
                  displaylogo: false,
                  displayModeBar: false,
                }}
                style={{ width: '100%', height: '100%' }}
              />
            ) : (
              <Typography
                variant="body1"
                component="div"
                sx={{
                  justifyContent: 'center',
                  height: '100%',
                  display: 'flex',
                  alignItems: 'center',
                }}
              >
                No Charts to Display. Please add chart(s).
              </Typography>
            )}
          </Box>
        </>
      }
      settingsDialog={MultiPlotWidgetSettingsDialog}
    />
  );
};

export default MultiPlotWidgetContainer;
