import { useEffect, useState } from 'react';
import { AlertMessage } from '~/shared/forms/types';
import { useCreateCustomerMutation } from '~/redux/api/customersApi';
import { Container, Typography } from '@mui/material';
import NewCustomerForm from '~/components/customer/NewCustomerForm';
import { CustomError } from '~/errors/CustomerErrorResponse';
import HomeButton from '~/components/common/Home/HomeButton';

export default function NewCustomerPage() {
  const [alertMessage, setAlertMessage] = useState<AlertMessage | undefined>(undefined);

  const [createCustomer, { data: customer, isLoading, isSuccess, error, isError }] =
    useCreateCustomerMutation();

  useEffect(() => {
    if (isError && error) {
      const err = error as CustomError;
      setAlertMessage({ message: err.data.message ?? 'Server error', severity: 'error' });
    }
    if (isSuccess && customer) {
      setAlertMessage({
        message: `Customer "${customer.nameId}" created successfully!`,
        severity: 'success',
      });
    }
  }, [isSuccess, isError, error, customer]);

  return (
    <Container maxWidth="md" sx={{ pt: 2 }}>
      <HomeButton />
      <Typography variant="h4">Create new customer</Typography>
      <NewCustomerForm
        loading={isLoading}
        alertMessage={alertMessage}
        onValidSubmit={createCustomer}
      />
    </Container>
  );
}
