import {
  DefaultLinkModel,
  DefaultPortModel,
  LinkModel,
  PortModelAlignment,
} from '@projectstorm/react-diagrams';
import { CustomType } from './CustomType';
import { AdvancedLinkModel } from '../Animation';

export class CustomPortModel extends DefaultPortModel {
  constructor(portModelType: CustomType, name: string) {
    super({
      type: portModelType,
      name: portModelType,
    });
  }

  createLinkModel(): LinkModel {
    return new AdvancedLinkModel();
  }

  canLinkToPort(port: CustomPortModel): any {
    if (this.getType() == 'CHP') {
      return port.options.type === 'building' || port.options.type === 'solarPanel';
    } else if (this.getType() == 'building') {
      return (
        port.options.type === 'CHP' ||
        port.options.type === 'grid' ||
        port.options.type === 'battery'
      );
    } else if (this.getType() == 'grid') {
      return port.options.type === 'building' || port.options.type === 'solarPanel';
    } else if (this.getType() == 'solarPanel') {
      return (
        port.options.type === 'grid' ||
        port.options.type === 'battery' ||
        port.options.type === 'CHP'
      );
    } else if (this.getType() == 'battery') {
      return port.options.type === 'building' || port.options.type === 'solarPanel';
    }
  }
}
