import { GridColDef } from '@mui/x-data-grid';
import { useRouter } from 'next/router';
import { useEffect, useRef, useState } from 'react';
import { useSelector } from 'react-redux';
import { UnitOfMeasure } from '~/measurements/domain/types';
import { getAssetTz } from '~/redux/selectors/topPanleSelectors';
import { getDateTimeFormat, getThousandSeparator } from '~/redux/selectors/userPreferences';
import { AssetMeasurementDetails } from '~/types/measures';
import {
  AssetMeasurementDetailsWithLastFetchTime,
  SingleScatterTimeSeriesData,
} from '~/types/timeseries';
import { TableWidget } from '~/types/widgets';
import { formatDate, formatNumber, roundNumber } from '~/utils/utils';
import { useGetMeasuresTsData } from './useGetMeasuresTsData';

type TrendResult = {
  isError: boolean;
  error?: string;
  tsData: SingleScatterTimeSeriesData;
  measureData: AssetMeasurementDetails;
  unitOfMeasures: UnitOfMeasure[];
  lastFetchTime: number;
};

function transformTableData(
  results: TrendResult[],
  selectedTitles: string[],
  dateFormats: string,
  useAssetTz: boolean,
  isDashboardTemplate: boolean,
  thousandSeparator: boolean,
): {
  rowHeader: GridColDef[];
  rows: any[];
  removedResults: AssetMeasurementDetailsWithLastFetchTime[];
} {
  const rowHeader: GridColDef[] = [
    {
      field: 'timeData',
      headerName: 'Time',
      width: 200,
      resizable: true,
      headerAlign: 'left',
      align: 'left',
    },
  ];
  const rows: any[] = [];
  const removedResults: TrendResult[] =
    results.filter(
      (result) => !result || !result.tsData || result.isError || result.error || !result.tsData,
    ) ?? [];

  results
    .filter((result) => result && result.tsData)
    .forEach(function (value) {
      const { measureData, tsData, unitOfMeasures } = value;
      if (isDashboardTemplate) {
        rowHeader.push({
          field: measureData.id.toString(),
          headerName: measureData?.tag.replace(/\\/g, ' > '),
          width: 250,
          resizable: true,
          headerAlign: 'left',
          align: 'left',
        });
        const data = tsData['ts,val'] || [];
        data.forEach(function ([time, val], ind) {
          if (rows.length === ind) {
            rows.push({
              id: ind,
            });
          }
          rows[ind]['timeData'] = formatDate(
            new Date(new Date(time).toLocaleString('en-US')),
            dateFormats,
          );
          rows[ind][measureData.id.toString()] = thousandSeparator
            ? formatNumber(val)
            : roundNumber(val);
        });
      }

      if (selectedTitles.includes(measureData.id.toString())) {
        const unit =
          unitOfMeasures?.find((data) => data.id === measureData.unitOfMeasureId) || null;
        rowHeader.push({
          field: measureData.id.toString(),
          headerName: unit?.name
            ? `${measureData?.tag.replace(/\\/g, ' > ')} (${unit.name})`
            : measureData?.tag.replace(/\\/g, ' > '),
          width: 250,
          resizable: true,
          headerAlign: 'left',
          align: 'left',
        });
        const data = tsData['ts,val'] || [];
        data.forEach(function ([time, val], ind) {
          if (rows.length === ind) {
            rows.push({
              id: ind,
            });
          }
          rows[ind]['timeData'] = formatDate(
            new Date(new Date(time).toLocaleString('en-US')),
            dateFormats,
          );
          rows[ind][measureData.id.toString()] = thousandSeparator
            ? formatNumber(val)
            : roundNumber(val);
        });
      }
    });
  return {
    rowHeader,
    rows,
    removedResults: removedResults.map((res) => {
      return {
        ...res.measureData,
        lastFetchTime: res.lastFetchTime,
        partialFailed: removedResults.length !== results.length,
      };
    }),
  };
}
export function useFetchTableData(widgetId: string, state: TableWidget) {
  const selectedSamplePeriod = state.samplePeriod;
  const prevResultsRef = useRef<TrendResult[]>([]);

  const selectedAggBy = state.aggBy;
  const selectedTitles = state.selectedTitles;
  const dateFormats = useSelector(getDateTimeFormat);
  const thousandSeparator = useSelector(getThousandSeparator);

  const useAssetTz = useSelector(getAssetTz);
  const router = useRouter();
  const [selectedMeasures, setSelectedMesure] = useState<string[]>([]);
  const [allDataFetched, setAllDataFetched] = useState<{
    rowHeader: GridColDef[];
    rows: any[];
    removedResults: AssetMeasurementDetailsWithLastFetchTime[];
    isLoading: boolean;
    isError: boolean;
  }>({
    rowHeader: [],
    rows: [],
    removedResults: [],
    isLoading: false,
    isError: false,
  });
  const [tableResults, setTableResults] = useState<TrendResult[] | undefined>(undefined);
  useEffect(() => {
    if (state.mode === 'dashboard') {
      const hasValidAssetMeasure = state.assetMeasure?.some(
        (assetMeas) =>
          assetMeas.assetId.trim() !== '' &&
          assetMeas.measureId.some((measure) => measure.trim() !== ''),
      );

      if (hasValidAssetMeasure) {
        const titles = state.assetMeasure
          .flatMap((assetMeas) => assetMeas.measureId)
          .filter((measure) => measure.trim() !== '');

        setSelectedMesure(titles);
      } else {
        setSelectedMesure([]);
      }
    }
    if (state.mode === 'template') {
      const selectedMesures = selectedTitles.map((title) => title);
      setSelectedMesure([...selectedMesures]);
    }
  }, [state.assetMeasure, selectedTitles]);
  const {
    data: measureData,
    isLoading: isMeasureDataLoading,
    successAndFailedMeasurements,
    isError,
  } = useGetMeasuresTsData({
    selectedTitles: selectedMeasures,
    dataFetchSettings: state,
    assetMeasure: state.assetMeasure,
  });
  useEffect(() => {
    if (isMeasureDataLoading) {
      setTableResults(undefined);
      setAllDataFetched((prev) => {
        return {
          ...prev,
          isLoading: true,
          isError: false,
        };
      });
    } else if (measureData) {
      // const filteredList = measureData;
      // setTableResults(filteredList as TrendResult[]);
      const updated: TrendResult[] = [];
      (measureData || []).forEach((result) => {
        if (!result.isError && result.tsData) {
          updated.push(result);
        } else {
          const existing = prevResultsRef.current.find(
            (r) => r.measureData.measurementId === result?.measureData?.measurementId,
          );
          if (existing) {
            updated.push({
              ...existing,
              lastFetchTime: result.lastFetchTime,
              isError: result.isError,
              error: result.error,
              tsData: {
                ...existing.tsData,
                error: result.error,
              },
            });
          } else {
            updated.push(result);
          }
        }
      });
      prevResultsRef.current = updated; // Keep latest working state
      setTableResults(updated as TrendResult[]);
    } else if (isError) {
      setTableResults(undefined);
      setAllDataFetched((prev) => {
        return {
          ...prev,
          isError: true,
          isLoading: false,
        };
      });
    }
  }, [isMeasureDataLoading, measureData, isError]);

  useEffect(() => {
    if (tableResults) {
      const selectedMeasureIds = selectedMeasures.length === 0 ? [] : selectedMeasures;
      const tableData = transformTableData(
        tableResults,
        selectedMeasureIds.length > 0 ? [...selectedMeasureIds] : [],
        dateFormats,
        useAssetTz,
        router.pathname === '/dashboard-template',
        thousandSeparator,
      );
      setAllDataFetched({
        ...tableData,
        isLoading: false,
        isError: false,
      });
    }
  }, [
    tableResults,
    state.title.value,
    state.title.isVisible,
    state.title.color,
    selectedMeasures,
    selectedSamplePeriod,
    selectedAggBy,
    state.overrideGlobalSettings,
    state.startDate,
    state.endDate,
    state.timeRange,
    state.globalSamplePeriod,
    dateFormats,
    router.pathname,
    thousandSeparator,
  ]);
  return { ...allDataFetched, successAndFailedMeasurements };
}
