import { yupResolver } from '@hookform/resolvers/yup';
import { Alert, Box, Button, TextField, Typography } from '@mui/material';
import Link from 'next/link';
import { useEffect, useState } from 'react';
import { SubmitHandler, useForm } from 'react-hook-form';
import { CustomError } from '~/errors/CustomerErrorResponse';
import { useForgotPassowrdMutation } from '~/redux/api/authApi';
import { AlertMessage } from '~/shared/forms/types';
import { forgotPassowrdSchema } from '~/types/users';

interface ForgotPasswordInputs {
  userNameOrEmail: string;
}

const ForgotPasswordForm = () => {
  const [alertMessage, setAlertMessage] = useState<AlertMessage | undefined>(undefined);
  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<ForgotPasswordInputs>({
    resolver: yupResolver(forgotPassowrdSchema),
  });
  const [sendForgotPasswordRequest, { isError, error, isLoading, isSuccess }] =
    useForgotPassowrdMutation();

  useEffect(() => {
    if (error && isError) {
      const err = error as CustomError;
      setAlertMessage({ message: err.data.message ?? 'Server error', severity: 'error' });
    }
    if (isSuccess) {
      setAlertMessage({
        message: `Reset password email sent successfully on registered email address!`,
        severity: 'success',
      });
    }
  }, [error, isError, isSuccess]);

  const onSubmit: SubmitHandler<ForgotPasswordInputs> = (data) => {
    sendForgotPasswordRequest({
      user: data.userNameOrEmail,
    });
  };

  return (
    <Box
      component="form"
      onSubmit={handleSubmit(onSubmit)}
      sx={{
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        gap: 2,
        width: '100%',
        mt: 200,
        maxWidth: 400,
        margin: '0 auto',
      }}
    >
      <Typography variant="h5" component="h1" gutterBottom>
        Forgot Password
      </Typography>

      <TextField
        label="Username or Email"
        variant="outlined"
        fullWidth
        error={!!errors.userNameOrEmail}
        helperText={errors.userNameOrEmail?.message}
        {...register('userNameOrEmail')}
      />

      <Button
        type="submit"
        variant="contained"
        color="primary"
        disabled={isLoading || isSuccess}
        fullWidth
      >
        Submit
      </Button>
      {alertMessage && (
        <Alert severity={alertMessage.severity} sx={{ mt: 3, mb: 3 }}>
          <Typography>{alertMessage.message}</Typography>
        </Alert>
      )}
      <Button
        variant="outlined"
        color="primary"
        LinkComponent={Link}
        href="/login"
        fullWidth
        sx={{ mt: 1 }}
      >
        Go Back to Login page
      </Button>
    </Box>
  );
};

export default ForgotPasswordForm;
