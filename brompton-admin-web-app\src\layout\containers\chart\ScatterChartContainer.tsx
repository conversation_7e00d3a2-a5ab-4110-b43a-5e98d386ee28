import Chart from '~/components/Chart';
import useFetchAnnotationData from '~/hooks/useFetchAnnotationData';
import { useFetchScatterData } from '~/hooks/useFetchScatterData';
import { ScatterChartWidget } from '~/types/widgets';

export type ScatterChartContainerProps = {
  id: string;
  settings: ScatterChartWidget;
};

export function ScatterChartContainer({ id, settings }: ScatterChartContainerProps): JSX.Element {
  const {
    isLoading,
    chartData,
    layoutData,
    removedResults,
    successfulResults,
    successAndFailedMeasurements,
  } = useFetchScatterData(id, settings);
  const { measureIdAnnotation } = useFetchAnnotationData(id, settings);
  return (
    <Chart
      id={id}
      chartType="scatter"
      settings={settings}
      data={chartData}
      layout={layoutData}
      isLoading={isLoading}
      showSettings={true}
      measureIdAnnotation={measureIdAnnotation}
      removedResults={removedResults}
      successfulResults={successfulResults}
      successAndFailedMeasurements={successAndFailedMeasurements}
    />
  );
}
