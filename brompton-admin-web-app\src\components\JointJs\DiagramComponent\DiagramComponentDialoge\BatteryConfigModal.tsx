import {
  Box,
  Modal,
  Typography,
  TextField,
  Button,
  RadioGroup,
  FormControlLabel,
  Radio,
} from '@mui/material';
import React, { FC, useState } from 'react';
import { theme } from '~/pages/_app';

interface IBatteryConfigModal {
  open: boolean;
  onClose: () => void;
  onSave: (
    level: number,
    orientation: 'vertical' | 'horizontal',
    colors: BatteryColors,
    style: 'bar' | 'solid',
  ) => void;
}

interface BatteryColors {
  low: string;
  mid: string;
  high: string;
}

const BatteryConfigModal: FC<IBatteryConfigModal> = ({ open, onClose, onSave }) => {
  const [batteryPercentage, setBatteryPercentage] = useState<string>(''); // Track battery percentage
  const [orientation, setOrientation] = useState<'vertical' | 'horizontal'>('vertical'); // Track orientation
  const [style, setStyle] = useState<'bar' | 'solid'>('bar'); // Track selected style
  const [colors, setColors] = useState<BatteryColors>({
    low: '#ff0000', // Default color for low
    mid: '#ffff00', // Default color for mid
    high: '#009900', // Default color for high
  });

  // Handle changes in the battery percentage input
  const handleInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setBatteryPercentage(event.target.value);
  };

  // Handle orientation radio button changes
  const handleOrientationChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setOrientation(event.target.value as 'vertical' | 'horizontal');
  };

  // Handle style radio button changes
  const handleStyleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setStyle(event.target.value as 'bar' | 'solid');
  };

  // Handle color input changes
  const handleColorChange =
    (level: keyof BatteryColors) => (event: React.ChangeEvent<HTMLInputElement>) => {
      setColors({ ...colors, [level]: event.target.value });
    };

  // Handle save button click
  const handleSave = () => {
    let level = parseFloat(batteryPercentage);

    // Validate level to ensure it's within 0-100
    level = Math.round(level); // Round off to the nearest integer
    if (level < 0 || level > 100) {
      alert('Battery percentage must be between 0 and 100.');
      return;
    }

    // Pass the level, orientation, colors, and style back to the parent component
    setBatteryPercentage('');
    setColors({
      low: '#ff0000',
      mid: '#ffff00',
      high: '#009900',
    });
    onSave(level, orientation, colors, style);
    onClose(); // Close the modal
  };

  return (
    <Modal
      open={open}
      onClose={onClose}
      aria-labelledby="battery-modal-title"
      aria-describedby="battery-modal-description"
    >
      <Box
        style={{
          position: 'absolute',
          top: '50%',
          left: '50%',
          transform: 'translate(-50%, -50%)',
          width: 300,
          backgroundColor: 'white',
          padding: '20px',
          borderRadius: '8px',
          boxShadow: theme.shadows[5],
        }}
      >
        <Typography id="battery-modal-title" variant="h6" component="h2">
          Configure Battery
        </Typography>
        <TextField
          id="battery-percentage-input"
          label="Battery Percentage"
          variant="outlined"
          fullWidth
          value={batteryPercentage}
          onChange={handleInputChange}
          type="text"
          sx={{ mt: 2, mb: 2 }}
        />
        <Typography variant="subtitle1" sx={{ mt: 2 }}>
          Orientation:
        </Typography>
        <RadioGroup value={orientation} onChange={handleOrientationChange} row>
          <FormControlLabel value="vertical" control={<Radio />} label="Vertical" />
          <FormControlLabel value="horizontal" control={<Radio />} label="Horizontal" />
        </RadioGroup>

        <Typography variant="subtitle1" sx={{ mt: 2 }}>
          Fill Style:
        </Typography>
        <RadioGroup value={style} onChange={handleStyleChange} row>
          <FormControlLabel value="solid" control={<Radio />} label="Solid" />
          <FormControlLabel value="bar" control={<Radio />} label="Bar" />
        </RadioGroup>

        <Typography variant="subtitle1" sx={{ mt: 2 }}>
          Colors:
        </Typography>
        <Box sx={{ mt: 2 }}>
          <TextField
            id="low-color-input"
            label="Low Level Color"
            type="color"
            value={colors.low}
            onChange={handleColorChange('low')}
            fullWidth
          />
          <TextField
            id="mid-color-input"
            label="Mid Level Color"
            type="color"
            value={colors.mid}
            onChange={handleColorChange('mid')}
            fullWidth
            sx={{ my: 3 }}
          />
          <TextField
            id="high-color-input"
            label="High Level Color"
            type="color"
            value={colors.high}
            onChange={handleColorChange('high')}
            fullWidth
            sx={{ marginBottom: '10px' }}
          />
        </Box>

        <Button variant="contained" onClick={handleSave} fullWidth sx={{ mt: 2 }}>
          Save
        </Button>
      </Box>
    </Modal>
  );
};

export default BatteryConfigModal;
