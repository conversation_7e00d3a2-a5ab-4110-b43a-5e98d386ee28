  apiVersion: apps/v1
  kind: Deployment
  metadata:
    name: be-frontend-prod
    namespace: admin
    labels:
      app: be-frontend-prod #deployment label
  spec:
    selector:
      matchLabels:
        app: be-frontend-prod # has to match .spec.template.metadata.labels
    replicas: 1 # by default is 1
    minReadySeconds: 10 # by default is 0
    template:
      metadata:
        labels:
          app: be-frontend-prod # has to match .spec.selector.matchLabels
      spec:
        #updateStrategy: rollingUpdate
        volumes:
          - name: db
            persistentVolumeClaim:
              claimName: admin-app-pvc
        terminationGracePeriodSeconds: 10
        imagePullSecrets:
          - name: ecr-registry-helper-secrets
        containers:
          - name: be-frontend-prod
            image: 067172429169.dkr.ecr.us-east-1.amazonaws.com/brompton-energy/frontend:v.1.0.5 #repo/image
            imagePullPolicy: Always
            ports:
              - containerPort: 8080
                name: be-frontend
            volumeMounts:
              - name: db
                mountPath: /data
            env:
              - name: NEXT_PUBLIC_BE_ADMIN_API_URL
                value: "https://api.brompton.ai/v0"
            resources:
              requests:
                memory: "64Mi"  # You can adjust these values based on your application's requirements
                cpu: "250m"
              limits:
                memory: "500Mi"  # You can adjust these values based on your application's requirements
                cpu: "1"
        affinity:
          nodeAffinity:
            requiredDuringSchedulingIgnoredDuringExecution:
              nodeSelectorTerms:
                - matchExpressions:
                    - key: application
                      operator: In
                      values:
                        - fullstack
                    - key: company
                      operator: In
                      values:
                        - brompton-energy