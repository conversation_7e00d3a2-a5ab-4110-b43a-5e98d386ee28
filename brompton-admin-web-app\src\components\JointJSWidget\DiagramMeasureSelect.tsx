import {
  Alert,
  Autocomplete,
  Box,
  CircularProgress,
  FormControl,
  FormLabel,
  MenuItem,
  OutlinedInput,
  Select,
  SelectChangeEvent,
  TextField,
} from '@mui/material';
import { useEffect, useMemo, useState } from 'react';
import { useSelector } from 'react-redux';
import { useGetAllAssetQuery } from '~/redux/api/assetsApi';
import { useGetAllMeasurementsQuery } from '~/redux/api/measuresApi';
import { getCustomerId } from '~/redux/selectors/customerSelectors';
import { AggByOptions } from '~/types/dashboard';
import { elementVariable } from '~/types/diagram';
import { DiagramWidget } from '~/types/widgets';
import { assetsPathMapper, formatMetricLabel } from '~/utils/utils';

type AssetOption = {
  label: string;
  id: number;
};

type MeasureOption = {
  label: string;
  id: string;
};

type DiagramMeasureSelectProps = {
  variables: elementVariable;
  onVariablesChange: (updatedVariable: elementVariable) => void; // New Prop
  showWarningMessage?: boolean;
  showNoneWarning?: boolean;
  settings: DiagramWidget;
};

const DiagramMeasureSelect = ({
  variables,
  showNoneWarning,
  showWarningMessage,
  onVariablesChange,
  settings,
}: DiagramMeasureSelectProps) => {
  const customerId = useSelector(getCustomerId);
  const [selectedAsset, setSelectedAsset] = useState<AssetOption | null>(null);
  const [selectedMeasure, setSelectedMeasure] = useState<MeasureOption | null>(null);
  const {
    data: assetData,
    isLoading: isAssetLoading,
    isFetching: isAssetReloading,
  } = useGetAllAssetQuery(
    { customerId, parentIds: [] },
    {
      skip: !customerId,
      refetchOnMountOrArgChange: true,
    },
  );

  const assetTypesWithPath = useMemo(() => assetsPathMapper(assetData ?? []), [assetData]);

  const { data: measurementData, isLoading: isMeasurementLoading } = useGetAllMeasurementsQuery(
    { customerId, assetId: selectedAsset ? selectedAsset.id : 0 },
    {
      skip: !selectedAsset || selectedAsset.id === 0,
      refetchOnMountOrArgChange: true,
    },
  );

  const measurementOptions: MeasureOption[] = useMemo(() => {
    if (!measurementData) return [];
    return measurementData.map((measure) => ({
      label: formatMetricLabel(measure.tag),
      id: String(measure.id),
    }));
  }, [measurementData]);

  useEffect(() => {
    if (variables) {
      const asset = assetTypesWithPath.find(
        (assetType) => assetType.id.toString() === variables.assetId,
      );
      const measurement = measurementOptions.find(
        (measure) => measure.id.toString() === variables.measurementId,
      );
      if (asset) setSelectedAsset(asset);
      if (measurement) setSelectedMeasure(measurement);
    }
  }, [variables, assetTypesWithPath, measurementOptions]);

  const handleAssetChange = (event: any, newValue: AssetOption | null) => {
    setSelectedAsset(newValue);
    setSelectedMeasure(null);

    onVariablesChange({
      ...variables,
      assetId: newValue ? newValue.id.toString() : '',
      measurementId: '', // Clear measurement when asset changes
    });
  };

  const handleMeasurementChange = (event: any, newValue: MeasureOption | null) => {
    setSelectedMeasure(newValue);

    onVariablesChange({
      ...variables,
      measurementId: newValue ? newValue.id : '',
    });
  };

  // const setOverRideSettings = (event: React.ChangeEvent<HTMLInputElement>, checked: boolean) => {
  //   onVariablesChange({
  //     ...variables,
  //     globalSamplePeriod: checked,
  //   });
  // };

  // const handleSamplePeriodChange = (event: SelectChangeEvent<number>) => {
  //   const index = event.target.value as number;
  //   onVariablesChange({
  //     ...variables,
  //     samplePeriod: SamplePeriodOptions[index].value,
  //   });
  // };
  const handleAggByChange = (event: SelectChangeEvent<number>) => {
    const index = event.target.value as number;
    onVariablesChange({
      ...variables,
      aggBy: AggByOptions[index].value,
    });
  };
  return (
    <>
      <FormLabel sx={{ pl: 2, mt: 1, fontSize: 20 }}>
        Variable : <strong>{variables.label ?? ''}</strong>
      </FormLabel>
      {!settings.isRealTime ? (
        <Box p={2}>
          <FormControl fullWidth>
            <Box display="flex" alignItems="center" width="100%" gap={1}>
              <Select
                fullWidth
                labelId={'aggregate-by-label'}
                id={'aggregate-by-select'}
                label={'Aggregate By' + variables?.aggBy}
                value={variables?.aggBy}
                input={
                  <OutlinedInput
                    label="Aggregate By"
                    sx={{
                      p: 0.2,
                      '& legend': {
                        maxWidth: '100%',
                        height: 'fit-content',
                        '& span': {
                          opacity: 1,
                        },
                      },
                    }}
                  />
                }
                onChange={handleAggByChange}
              >
                {AggByOptions.map((option) => (
                  <MenuItem key={option.value} value={option.value}>
                    {option.label}
                  </MenuItem>
                ))}
              </Select>
            </Box>
            {showWarningMessage ? (
              <Alert variant="outlined" severity="error" sx={{ mt: 2 }}>
                Reset the aggregate option in widget setting where <strong>STD.P</strong> is
                selected.
              </Alert>
            ) : null}
            {showNoneWarning ? (
              <Alert variant="outlined" severity="error" sx={{ mt: 2 }}>
                Time range with more then <strong>6 hours</strong> time period is not allowed
              </Alert>
            ) : null}
          </FormControl>
        </Box>
      ) : null}

      <Box sx={{ display: 'flex', gap: 2, p: 2 }}>
        <FormControl fullWidth>
          <Autocomplete
            fullWidth
            id={`asset-autocomplete`}
            loading={isAssetReloading}
            options={assetTypesWithPath.map((asset) => ({
              label: asset.label,
              id: asset.id,
            }))}
            getOptionLabel={(option) => option.label}
            onChange={handleAssetChange}
            value={selectedAsset}
            renderInput={(params) => (
              <TextField
                {...params}
                label="Select Asset"
                variant="outlined"
                InputProps={{
                  ...params.InputProps,
                  endAdornment: (
                    <>
                      {isAssetLoading ? <CircularProgress color="inherit" size={20} /> : null}
                      {params.InputProps.endAdornment}
                    </>
                  ),
                }}
              />
            )}
          />
        </FormControl>
        {selectedAsset && (
          <FormControl fullWidth>
            <Autocomplete
              fullWidth
              id={`measurement-autocomplete`}
              loading={isMeasurementLoading}
              options={measurementOptions}
              getOptionLabel={(option) => option.label}
              onChange={handleMeasurementChange}
              value={selectedMeasure}
              renderInput={(params) => (
                <TextField
                  {...params}
                  label="Select Measurement"
                  variant="outlined"
                  InputProps={{
                    ...params.InputProps,
                    endAdornment: (
                      <>
                        {isMeasurementLoading ? (
                          <CircularProgress color="inherit" size={20} />
                        ) : null}
                        {params.InputProps.endAdornment}
                      </>
                    ),
                  }}
                />
              )}
            />
          </FormControl>
        )}
      </Box>
      {/* {!settings.isRealTime ? (
        <Box sx={{ pl: 2, pr: 2 }}>
          <FormGroup>
            <FormControlLabel
              control={
                <Checkbox
                  sx={{
                    p: 2,
                  }}
                  checked={variables?.globalSamplePeriod}
                  onChange={setOverRideSettings}
                  name="globalSamplePeriod"
                />
              }
              label="Override Global Sample Period"
            />
          </FormGroup>
          {variables?.globalSamplePeriod ? (
            <FormControl fullWidth>
              <Box display="flex" alignItems="center" width="100%" p={1} gap={1}>
                <Select
                  labelId={'sample-period-label'}
                  id={'sample-period-select'}
                  value={variables?.samplePeriod ?? 2}
                  label="Sample Period"
                  input={
                    <OutlinedInput
                      label="Sample Period"
                      sx={{
                        p: 0.2,
                        '& legend': {
                          maxWidth: '100%',
                          height: 'fit-content',
                          '& span': {
                            opacity: 1,
                          },
                        },
                      }}
                    />
                  }
                  onChange={handleSamplePeriodChange}
                  fullWidth
                >
                  {SamplePeriodOptions.map((option, index) =>
                    index > 0 ? (
                      <MenuItem key={option.value} value={index}>
                        {option.label}
                      </MenuItem>
                    ) : null,
                  )}
                </Select>
              </Box>
            </FormControl>
          ) : null}
        </Box>
      ) : null} */}
    </>
  );
};

export default DiagramMeasureSelect;
