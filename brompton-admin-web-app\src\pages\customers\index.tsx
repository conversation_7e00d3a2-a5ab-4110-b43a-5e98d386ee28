import DeleteIcon from '@mui/icons-material/Delete';
import FlagIcon from '@mui/icons-material/Flag';
import FlagOutlinedIcon from '@mui/icons-material/FlagOutlined';
import ModeEditIcon from '@mui/icons-material/ModeEdit';
import {
  Box,
  Button,
  Card,
  Chip,
  CircularProgress,
  Container,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  IconButton,
  TextField,
  Tooltip,
  Typography,
} from '@mui/material';
import { GridColDef, GridRenderCellParams } from '@mui/x-data-grid';
import Image from 'next/image';
import { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import CustomDialog from '~/components/common/CustomDialog';
import DataTable from '~/components/common/DataTable/DataTable';
import PageName from '~/components/common/PageName/PageName';
import EditCustomerForm from '~/components/customer/EditCustomerForm';
import NewCustomerForm from '~/components/customer/NewCustomerForm';
import { CustomError } from '~/errors/CustomerErrorResponse';
import { useHasAdminAccess } from '~/hooks/useHasAdminAccess';
import { useHasPowerUserAccess } from '~/hooks/useHasPowerUserAccess';
import { useRolePermission } from '~/hooks/useRolePermission';
import {
  useCreateCustomerMutation,
  useDeleteCustomerByIdMutation,
  useEditCustomerByIdMutation,
  useGetCustomersQuery,
} from '~/redux/api/customersApi';
import { useUpdateUserPreferencesMutation } from '~/redux/api/usersApi';
import { getUserPreferences } from '~/redux/selectors/userPreferences';
import { dashboardSlice } from '~/redux/slices/dashboardSlice';
import { AlertMessage } from '~/shared/forms/types';
import { AlertSnackbar } from '~/shared/snackbars/components/AlertSnackbar';
import { useSnackbar } from '~/shared/snackbars/snackbar-hooks';
import { Customer, EditCustomer } from '~/types/customers';

export default function CustomerListPage() {
  const { globalAdmin, admin } = useHasAdminAccess();
  const { hasPermission } = useRolePermission();
  const hasPowerUserAccess = useHasPowerUserAccess();
  const {
    data,
    isSuccess,
    isFetching: isLoading,
    refetch,
  } = useGetCustomersQuery({ is_logo: true });
  const [
    updateCustomer,
    { error: updateError, isError: updateIsError, data: updateData, isSuccess: updateIsSuccess },
  ] = useEditCustomerByIdMutation();
  const dispatch = useDispatch();
  const [customers, setCustomers] = useState<Customer[]>([]);
  const [deleteCusetomer, { isError, data: deleteData }] = useDeleteCustomerByIdMutation();
  const [deleteConfirmationOpen, setDeleteConfirmationOpen] = useState(false);
  const [customerToDelete, setCustomerToDelete] = useState<Customer | null>(null);
  const [favoriteCustomer, setFavoriteCustomer] = useState<Customer | null>(null);
  const [snackbarState, showSuccessAlert, showErrorAlert] = useSnackbar();
  const [createCustomer, setCreateCustomer] = useState<boolean>(false);
  const [filterFields, setFilterFields] = useState<{
    name: string;
    address: string;
    nameId: string;
    showFilter: boolean;
  }>({
    name: '',
    address: '',
    nameId: '',
    showFilter: false,
  });
  const [alertMessage, setAlertMessage] = useState<AlertMessage | undefined>(undefined);
  useEffect(() => {
    if (data) {
      setCustomers(data);
    }
  }, [data]);
  useEffect(() => {
    if (
      filterFields.address === '' &&
      filterFields.name === '' &&
      filterFields.nameId === '' &&
      data
    ) {
      setCustomers(data);
    }
  }, [filterFields, data]);
  const [
    createNewCustomer,
    {
      data: customer,
      isLoading: createLoading,
      isSuccess: isCreateSuccess,
      error,
      isError: createIsError,
    },
  ] = useCreateCustomerMutation();
  const [isEdit, setIsEdit] = useState<{
    isEdit: boolean;
    id: number | null;
  }>({
    isEdit: false,
    id: null,
  });

  const userPreferences = useSelector(getUserPreferences);
  useEffect(() => {
    if (createIsError && error) {
      const err = error as CustomError;
      showErrorAlert(err.data.message ?? 'Server error');
    }
    if (isCreateSuccess && customer) {
      showSuccessAlert(`Customer "${customer.nameId}" created successfully!!`);
      setCreateCustomer(false);
    }
  }, [isCreateSuccess, createIsError, error, customer]);
  const [
    createUserPreference,
    {
      isSuccess: isCreateUserPreferenceSuccess,
      isError: isCreateUserPreferenceError,
      data: createUserPreferenceData,
    },
  ] = useUpdateUserPreferencesMutation();
  const handleDeleteConfirmationOpen = (customer: Customer) => {
    setCustomerToDelete(customer);
    setDeleteConfirmationOpen(true);
  };

  const handleEdit = (id: number) => {
    setIsEdit({
      isEdit: true,
      id: id,
    });
  };

  useEffect(() => {
    if (updateIsSuccess) {
      showSuccessAlert(`customer updated successfully!`);
      setIsEdit({
        id: null,
        isEdit: false,
      });
      refetch();
    }

    if (updateIsError && updateError) {
      showErrorAlert(updateError && 'Error on update customer');
    }
  }, [updateIsError, updateError, updateData, updateIsSuccess]);

  useEffect(() => {
    if (userPreferences.DEFAULT_CUSTOMER && data && data.length) {
      const filterFavortieCustomer = data.filter(
        (customer) => customer.id === Number(userPreferences.DEFAULT_CUSTOMER),
      );
      if (filterFavortieCustomer && filterFavortieCustomer.length >= 1) {
        setFavoriteCustomer(filterFavortieCustomer[0]);
      }
    }
  }, [data, userPreferences]);

  useEffect(() => {
    if (favoriteCustomer && favoriteCustomer.id !== Number(userPreferences.DEFAULT_CUSTOMER)) {
      createUserPreference({
        preferences: {
          DEFAULT_CUSTOMER: (favoriteCustomer.id ?? 0).toString(),
        },
      });
      dispatch(
        dashboardSlice.actions.setUserPreferences({
          ...userPreferences,
          DEFAULT_CUSTOMER: (favoriteCustomer.id ?? 0).toString(),
        }),
      );
    }
  }, [favoriteCustomer]);

  useEffect(() => {
    if (isCreateUserPreferenceSuccess) {
      showSuccessAlert(`Default customer updated successfully!`);
    }

    if (isCreateUserPreferenceError) {
      showErrorAlert('Error on default customer');
    }
  }, [isCreateUserPreferenceSuccess, isCreateUserPreferenceError, createUserPreferenceData]);

  const handleDeleteConfirmationClose = () => {
    setCustomerToDelete(null);
    setDeleteConfirmationOpen(false);
  };

  const handleDeleteCustomer = () => {
    if (customerToDelete) {
      deleteCusetomer(customerToDelete.id);
    }
    refetch();
    handleDeleteConfirmationClose();
  };
  const columns: GridColDef[] = [
    {
      field: 'image',
      headerName: 'Logo',
      width: 150,
      filterable: false,
      renderCell: (params: GridRenderCellParams) =>
        params?.row?.logo ? (
          <Image
            src={params.row.logo}
            alt={params.row.name}
            width={100}
            height={100}
            style={{ height: 'auto' }}
          />
        ) : (
          <Typography>No Logo</Typography>
        ),
    },
    { field: 'name', headerName: 'Customer Name', width: 240 },
    { field: 'address', headerName: 'Customer Address', width: 600 },
    { field: 'nameId', headerName: 'Name Id', width: 250 },
    {
      field: 'actions',
      headerName: 'Actions',
      width: 500,
      renderCell: (params: GridRenderCellParams) => (
        <>
          <Tooltip title="Set as Default Customer" arrow>
            <Button onClick={() => setFavoriteCustomer(params.row)}>
              {favoriteCustomer && params.row.id === favoriteCustomer.id ? (
                <FlagIcon />
              ) : (
                <FlagOutlinedIcon />
              )}
            </Button>
          </Tooltip>
          {globalAdmin || admin || hasPowerUserAccess ? (
            <>
              {hasPermission('customer.update') && (hasPowerUserAccess || globalAdmin || admin) && (
                <Tooltip title="Edit User">
                  <Button onClick={() => handleEdit(params.row.id)}>
                    <ModeEditIcon />
                  </Button>
                </Tooltip>
              )}
              {hasPermission('customer.delete') && (hasPowerUserAccess || globalAdmin || admin) && (
                <Tooltip title="Delete Customer" arrow>
                  <Button onClick={() => handleDeleteConfirmationOpen(params.row)}>
                    <DeleteIcon />
                  </Button>
                </Tooltip>
              )}
            </>
          ) : null}
        </>
      ),
    },
  ];
  return (
    <Box p={4}>
      <AlertSnackbar {...snackbarState} />
      <Box p={2} pb={0} pt={0} sx={{ display: 'flex' }}>
        <Box
          sx={{
            flexGrow: 1,
            display: 'flex',
            alignItems: 'center',
          }}
        >
          <PageName name="Customers" />
        </Box>
      </Box>
      {hasPermission('customer.create') ? (
        <Typography
          ml={2}
          onClick={() => {
            setCreateCustomer(true);
          }}
        >
          <Button variant="contained" color="primary" sx={{ float: 'right', mt: 1, mb: 1 }}>
            Add new Customer
          </Button>
        </Typography>
      ) : null}
      {isLoading && <CircularProgress />}

      {!isLoading && isSuccess && data && (
        <Box sx={{ height: '100%', width: '100%', mt: 3 }}>
          <DataTable
            columns={columns}
            data={customers}
            filteredData={
              <>
                <>
                  {filterFields.showFilter ? (
                    <>
                      {filterFields.name && (
                        <Chip
                          label={`Name: ${filterFields.name}`}
                          onDelete={() => {
                            setFilterFields({ ...filterFields, name: '' });
                            setCustomers(
                              data.filter((customer) => {
                                if (filterFields.address) {
                                  if (
                                    customer.address
                                      .toLowerCase()
                                      .includes(filterFields.address.toLowerCase())
                                  ) {
                                    return true;
                                  }
                                }
                                if (filterFields.nameId) {
                                  if (
                                    customer.nameId
                                      .toLowerCase()
                                      .includes(filterFields.nameId.toLowerCase())
                                  ) {
                                    return true;
                                  }
                                }
                                return false;
                              }),
                            );
                          }}
                        />
                      )}
                      {filterFields.address && (
                        <Chip
                          label={`Address: ${filterFields.address}`}
                          onDelete={() => {
                            setFilterFields({ ...filterFields, address: '' });
                            setCustomers(
                              data.filter((customer) => {
                                if (filterFields.name) {
                                  if (
                                    customer.name
                                      .toLowerCase()
                                      .includes(filterFields.name.toLowerCase())
                                  ) {
                                    return true;
                                  }
                                }
                                if (filterFields.nameId) {
                                  if (
                                    customer.nameId
                                      .toLowerCase()
                                      .includes(filterFields.nameId.toLowerCase())
                                  ) {
                                    return true;
                                  }
                                }
                                return false;
                              }),
                            );
                          }}
                        />
                      )}
                      {filterFields.nameId && (
                        <Chip
                          label={`Name Id: ${filterFields.nameId}`}
                          onDelete={() => {
                            setFilterFields({ ...filterFields, nameId: '' });
                            setCustomers(
                              data.filter((customer) => {
                                if (filterFields.name) {
                                  if (
                                    customer.name
                                      .toLowerCase()
                                      .includes(filterFields.name.toLowerCase())
                                  ) {
                                    return true;
                                  }
                                }
                                if (filterFields.address) {
                                  if (
                                    customer.address
                                      .toLowerCase()
                                      .includes(filterFields.address.toLowerCase())
                                  ) {
                                    return true;
                                  }
                                }
                                return false;
                              }),
                            );
                          }}
                        />
                      )}
                      {filterFields.name || filterFields.address || filterFields.nameId ? (
                        <Chip
                          label={'Clear all'}
                          variant="outlined"
                          sx={{
                            color: 'primary.main',
                            border: 'unset',
                          }}
                          onClick={() => {
                            setFilterFields({
                              name: '',
                              address: '',
                              nameId: '',
                              showFilter: false,
                            });
                            setCustomers(data);
                          }}
                        />
                      ) : null}
                    </>
                  ) : null}
                </>
              </>
            }
            filterOptions={
              <Card sx={{ p: 2 }}>
                <TextField
                  fullWidth
                  label="Name"
                  margin="normal"
                  variant="outlined"
                  value={filterFields.name}
                  onChange={(e) => setFilterFields({ ...filterFields, name: e.target.value })}
                />
                <TextField
                  fullWidth
                  margin="normal"
                  label="Address"
                  variant="outlined"
                  value={filterFields.address}
                  onChange={(e) => setFilterFields({ ...filterFields, address: e.target.value })}
                />
                <TextField
                  fullWidth
                  label="Name Id"
                  margin="normal"
                  variant="outlined"
                  value={filterFields.nameId}
                  onChange={(e) => setFilterFields({ ...filterFields, nameId: e.target.value })}
                />
                <Box sx={{ p: 2 }}>
                  <Button
                    variant="contained"
                    color="primary"
                    onClick={() => {
                      refetch();
                      setFilterFields({ ...filterFields, showFilter: true });
                      setCustomers(
                        data.filter((customer) => {
                          if (filterFields.name) {
                            if (
                              customer.name.toLowerCase().includes(filterFields.name.toLowerCase())
                            ) {
                              return true;
                            }
                          }
                          if (filterFields.address) {
                            if (
                              customer.address
                                .toLowerCase()
                                .includes(filterFields.address.toLowerCase())
                            ) {
                              return true;
                            }
                          }
                          if (filterFields.nameId) {
                            if (
                              customer.nameId
                                .toLowerCase()
                                .includes(filterFields.nameId.toLowerCase())
                            ) {
                              return true;
                            }
                          }
                          return false;
                        }),
                      );
                    }}
                  >
                    Search
                  </Button>
                </Box>
              </Card>
            }
          />
          <Dialog open={deleteConfirmationOpen} onClose={handleDeleteConfirmationClose}>
            <Box textAlign={'center'}>
              <Box>
                <IconButton color="primary">
                  <DeleteIcon />
                </IconButton>
              </Box>
            </Box>
            <DialogTitle>Delete Customer `[{customerToDelete?.name}]`?</DialogTitle>
            <DialogContent>
              <Typography>Are you sure you want to delete this customer?</Typography>
            </DialogContent>
            <DialogActions>
              <Button onClick={handleDeleteConfirmationClose} variant="outlined">
                Cancel
              </Button>
              <Button onClick={handleDeleteCustomer} variant="contained" color="primary">
                Delete
              </Button>
            </DialogActions>
          </Dialog>
        </Box>
      )}

      <CustomDialog
        title="Add Customer"
        content={
          <>
            <NewCustomerForm
              loading={isLoading}
              alertMessage={alertMessage}
              onValidSubmit={createNewCustomer}
              onClose={() => {
                setCreateCustomer(false);
              }}
            />
          </>
        }
        open={createCustomer}
        dialogActions={<></>}
        onClose={() => {
          setCreateCustomer(false);
        }}
      />
      <CustomDialog
        title="Edit Customer"
        content={
          <Container maxWidth="xl">
            <EditCustomerForm
              loading={isLoading}
              customer={
                data?.find((customer) => customer.id === isEdit.id) as unknown as EditCustomer
              }
              onValidSubmit={(customer: EditCustomer) => {
                if (isEdit.id) {
                  updateCustomer({ customerId: isEdit.id, userDetails: customer });
                }
              }}
              onCancel={() => {
                setIsEdit({
                  id: null,
                  isEdit: false,
                });
              }}
            />
          </Container>
        }
        open={isEdit.id !== null && isEdit.isEdit}
        dialogActions={null}
        onClose={() => {
          setIsEdit({
            id: null,
            isEdit: false,
          });
        }}
      />
    </Box>
  );
}
