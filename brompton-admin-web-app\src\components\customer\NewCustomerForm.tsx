import { yupResolver } from '@hookform/resolvers/yup';
import { Alert, Box, Button, TextField } from '@mui/material';
import { Controller, useForm } from 'react-hook-form';
import { AlertMessage } from '~/shared/forms/types';
import { Customer, customerSchema } from '~/types/customers';
import React, { ChangeEvent } from 'react'; // Import React and ChangeEvent

type NewCustomerFormProps = {
  loading: boolean;
  alertMessage: AlertMessage | undefined;
  onValidSubmit: (data: Customer) => Promise<unknown>;
  onClose?: () => void;
};

export default function NewCustomerForm({
  loading,
  alertMessage,
  onValidSubmit,
  onClose,
}: NewCustomerFormProps): JSX.Element {
  const { control, handleSubmit, reset } = useForm<Customer>({
    defaultValues: {
      nameId: '',
      name: '',
      address: '',
      logo: undefined, // Update the initial value of the logo field to an empty object
    },
    resolver: yup<PERSON><PERSON><PERSON>ver(customerSchema),
  });

  return (
    <form
      onSubmit={handleSubmit(async (data) => {
        try {
          if (data.logo) {
            const reader = new FileReader();
            data.logo = await new Promise((resolve, reject) => {
              reader.onloadend = () => {
                resolve(reader.result as string);
              };
              reader.onerror = reject;
              if (data.logo instanceof Blob) {
                reader.readAsDataURL(data.logo);
              }
            });
          }
          await onValidSubmit(data);
          reset();
        } catch (err) {
          // Handle the error here
        }
      })}
      noValidate
    >
      <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
        <Controller
          name="nameId"
          control={control}
          render={({ field: { onChange, onBlur, value }, fieldState }) => (
            <TextField
              error={!!fieldState.error}
              helperText={fieldState.error?.message}
              onChange={onChange}
              onBlur={onBlur}
              value={value}
              label="Name id"
              variant="outlined"
              margin="normal"
              disabled={loading}
              fullWidth
              required
            />
          )}
        />
        <Controller
          name="name"
          control={control}
          render={({ field: { onChange, onBlur, value }, fieldState }) => (
            <TextField
              error={!!fieldState.error}
              helperText={fieldState.error?.message}
              onChange={onChange}
              onBlur={onBlur}
              value={value}
              label="Name"
              variant="outlined"
              margin="normal"
              disabled={loading}
              fullWidth
              required
            />
          )}
        />
        <Controller
          name="address"
          control={control}
          render={({ field: { onChange, onBlur, value }, fieldState }) => (
            <TextField
              error={!!fieldState.error}
              helperText={fieldState.error?.message}
              onChange={onChange}
              onBlur={onBlur}
              value={value}
              label="Address"
              variant="outlined"
              margin="normal"
              disabled={loading}
              fullWidth
              required
            />
          )}
        />
        <Controller
          name="logo"
          control={control}
          render={({ field: { onChange, onBlur }, fieldState }) => (
            <TextField
              type="file"
              error={!!fieldState.error}
              helperText={fieldState.error?.message}
              onChange={(e: ChangeEvent<HTMLInputElement>) => {
                // Update the onChange function to properly handle the file change event
                const file = e.target.files?.[0];
                onChange(file);
              }}
              onBlur={onBlur}
              variant="outlined"
              margin="normal"
              disabled={loading}
              fullWidth
              required
            />
          )}
        />
        <Box display={'flex'} gap={2} width={'100%'}>
          <Button
            sx={{ mt: 2, width: '100%' }}
            variant="outlined"
            onClick={() => {
              if (onClose) {
                onClose();
              }
            }}
          >
            Cancel
          </Button>
          <Button
            type="submit"
            variant="contained"
            size="large"
            sx={{ mt: 2, width: '100%' }}
            disabled={loading}
          >
            Add
          </Button>
        </Box>
        {alertMessage && (
          <Alert severity={alertMessage.severity} sx={{ mt: 3 }}>
            {alertMessage.message}
          </Alert>
        )}
      </Box>
    </form>
  );
}
