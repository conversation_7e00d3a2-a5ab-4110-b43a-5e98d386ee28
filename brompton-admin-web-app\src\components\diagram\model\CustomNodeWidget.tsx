import * as React from 'react';
import { CustomNodeModel } from './CustomNodeModel';
import { DiagramEngine, PortWidget } from '@projectstorm/react-diagrams';
import styled from '@emotion/styled';
import Image from 'next/image';
import buliding from '../assets/images/Bulding.jpeg';
import solar from '../assets/images/solarpanel.jpeg';
import grid from '../assets/images/Img1.jpeg';
import battery from '../assets/images/Img2.jpeg';
import Chamber from '../assets/images/Chamber.svg';

import { CustomType } from './CustomType';
import { makeStyles } from '@material-ui/styles';

const useStyles = makeStyles({
  body: {
    position: 'relative',
    width: 200,
    height: 180,
  },
  container: {
    position: 'relative',
    width: 'fit-content',
    height: 'fit-content',
    zIndex: 10,
    backgroundColor: 'rgba(248, 250, 252)',
  },
  image: {
    pointerEvents: 'none',
  },
  port: {},
});

export interface CustomNodeWidgetProps {
  node: CustomNodeModel;
  engine: DiagramEngine;
  size?: number;
  type: CustomType;
}

// eslint-disable-next-line @typescript-eslint/no-namespace
namespace S {
  export const Port = styled.div`
    position: relative;
    width: 250px;
    height: 230px;
    // background: rgba(0, 0, 0, 0.5);
    border-radius: 8px;
    cursor: pointer;
    bottom: 3px;
    right: -30px;

    &:hover {
      // background: rgba(0, 0, 0, 1);
    }
  `;
}

/**
 * <AUTHOR> Vorster
 */

export const CustomNodeWidget: React.FC<CustomNodeWidgetProps> = (props) => {
  const getPortName = (type: CustomType, node: CustomNodeModel): any => {
    switch (type) {
      case 'building':
        return node.getPort('building');
      case 'solarPanel':
        return node.getPort('solarPanel');
      case 'battery':
        return node.getPort('battery');
      case 'grid':
        return node.getPort('grid');
      case 'CHP':
        return node.getPort('CHP');
      default:
        return null; // or handle default case appropriately
    }
  };

  const classes = useStyles();
  return (
    <div
      style={{
        position: 'relative',
        width: 200,
        height: 180,
      }}
      className={classes.body}
    >
      <div className={classes.container}>
        <div style={{ display: 'flex', justifyContent: 'space-between' }}>
          <div>{props.node.getOptions().name}</div>
          <div>{props.node.getOptions().value}</div>
        </div>
        {props.type === 'solarPanel' && (
          <div className="-z-20">
            <Image src={solar} alt="udshj" className={classes.image}></Image>
          </div>
        )}
        {props.type === 'building' && (
          <div>
            {/* {<div>{buildingValue}</div>} */}
            <Image src={buliding} alt="udshj" className={classes.image}></Image>
          </div>
        )}
        {props.type === 'battery' && (
          <Image src={battery} alt="udshj" className={classes.image}></Image>
        )}
        {props.type === 'grid' && <Image src={grid} alt="udshj" className={classes.image}></Image>}
        {props.type === 'CHP' && <Image src={Chamber} alt="CHP" className={classes.image}></Image>}
      </div>

      <PortWidget
        style={{
          top: -9,
          right: -9,
          position: 'absolute',
        }}
        port={getPortName(props.type, props.node)}
        engine={props.engine}
      >
        <S.Port />
      </PortWidget>
    </div>
  );
};
