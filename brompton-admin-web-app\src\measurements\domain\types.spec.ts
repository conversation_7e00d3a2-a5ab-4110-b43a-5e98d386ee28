import { AssetMeasurementSchema } from './types';

describe('Asset measurement validation', () => {
  test('full asset measurement should be valid', () => {
    const result = AssetMeasurementSchema.validateSync({
      tag: 'Engine/Power',
      description: 'Power of an engine',
      typeId: 43,
      dataTypeId: 8,
      valueTypeId: 11,
      unitOfMeasureId: 9,
      locationId: 33,
      meterFactor: 0,
    });

    expect(result.tag).toBe('Engine/Power');
  });

  test('missing tag should throw an exception', () => {
    expect(() =>
      AssetMeasurementSchema.validateSync({
        description: 'Power of an engine',
        typeId: 43,
        dataTypeId: 8,
        valueTypeId: 11,
        unitOfMeasureId: 9,
        locationId: 33,
        meterFactor: 0,
      }),
    ).toThrowError('Please enter a tag');
  });

  test('NaN meter factor should be transformed to undefined', () => {
    const result = AssetMeasurementSchema.validateSync({
      tag: 'Engine/Power',
      description: 'Power of an engine',
      typeId: 43,
      dataTypeId: 8,
      valueTypeId: 11,
      unitOfMeasureId: 9,
      locationId: 33,
      meterFactor: NaN,
    });

    expect(result.meterFactor).toBeUndefined();
  });
});
