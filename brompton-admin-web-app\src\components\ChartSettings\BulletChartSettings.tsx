import { Box, Checkbox, FormControl, FormControlLabel, Input, Stack } from '@mui/material';
import { ChangeEvent } from 'react';
import { BulletChartWidget, setSingleMeasureWidgetSettings } from '~/types/widgets';
import DataWidgetSettingsContainer from '../common/DataWidgetSettingsContainer/DataWidgetSettingsContainer';
import SingleMeasureSelect from '../common/SingleMeasureSelect';

type BulletChartSettingsProps = {
  bulletChartSettings: BulletChartWidget;
  selectedDbMeasureIdToName: { [key: string]: string };
  handlebulletChartSettingsUpdate: (
    value: ((prevState: BulletChartWidget) => BulletChartWidget) | BulletChartWidget,
  ) => void;
};
const BulletChartSettings = ({
  bulletChartSettings,
  handlebulletChartSettingsUpdate,
  selectedDbMeasureIdToName,
}: BulletChartSettingsProps) => {
  const handleChangeIndicator = (e: ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    handlebulletChartSettingsUpdate((prevState) => {
      return {
        ...prevState,
        [e.target.name]: e.target.value,
      };
    });
  };

  const handleCheckBoxChange = (e: ChangeEvent<HTMLInputElement>) => {
    handlebulletChartSettingsUpdate((prevState) => {
      return {
        ...prevState,
        [e.target.name]: e.target.checked,
      };
    });
  };

  const handleMarginChange = (e: ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    handlebulletChartSettingsUpdate((prevState) => {
      return {
        ...prevState,
        margin: {
          ...prevState.margin,
          [e.target.name]: Number(e.target.value),
        },
      };
    });
  };

  return (
    <>
      <Stack gap={1}>
        <DataWidgetSettingsContainer
          settings={bulletChartSettings}
          setSettings={handlebulletChartSettingsUpdate}
          dataTabChildren={
            <>
              <FormControl fullWidth>
                <Box pt={2}>
                  <SingleMeasureSelect
                    id="Bullet-chart"
                    settings={bulletChartSettings}
                    setSettings={handlebulletChartSettingsUpdate as setSingleMeasureWidgetSettings}
                  />
                </Box>
              </FormControl>
              <FormControl
                fullWidth
                sx={{
                  mt: 2,
                }}
              >
                Min Value
                <Input
                  type="number"
                  fullWidth
                  value={bulletChartSettings.minValue}
                  name="minValue"
                  onChange={handleChangeIndicator}
                />
              </FormControl>
              <FormControl
                fullWidth
                sx={{
                  mt: 2,
                }}
              >
                Max Value
                <Input
                  type="number"
                  fullWidth
                  value={bulletChartSettings.maxValue}
                  name="maxValue"
                  onChange={handleChangeIndicator}
                />
              </FormControl>
              <FormControlLabel
                sx={{
                  p: 2,
                  pt: 0,
                  px: 0,
                  mt: 2,
                  width: '100%',
                }}
                control={
                  <Checkbox
                    value={bulletChartSettings.showThreshHoldValue}
                    name="showThreshHoldValue"
                    checked={bulletChartSettings.showThreshHoldValue}
                    onChange={handleCheckBoxChange}
                  />
                }
                label="Show Threshold"
              />
              {bulletChartSettings.showThreshHoldValue ? (
                <>
                  <FormControl
                    fullWidth
                    sx={{
                      p: 0,
                    }}
                  >
                    Threshold Value
                    <Input
                      type="number"
                      fullWidth
                      value={bulletChartSettings.threshHoldValue}
                      name="threshHoldValue"
                      onChange={handleChangeIndicator}
                    />
                  </FormControl>
                </>
              ) : null}
              <FormControlLabel
                sx={{
                  p: 2,
                  pt: 0,
                  px: 0,
                  width: '100%',
                  mt: 2,
                }}
                control={
                  <Checkbox
                    value={bulletChartSettings.showIndicator1}
                    checked={bulletChartSettings.showIndicator1}
                    name="showIndicator1"
                    onChange={handleCheckBoxChange}
                  />
                }
                label="Show Indicator 1"
              />
              {bulletChartSettings.showIndicator1 ? (
                <>
                  <FormControl fullWidth>
                    Indicator 1 Value
                    <Input
                      type="number"
                      fullWidth
                      value={bulletChartSettings.indicator1Value}
                      name="indicator1Value"
                      onChange={handleChangeIndicator}
                    />
                  </FormControl>

                  <FormControlLabel
                    sx={{
                      p: 2,
                      pt: 0,
                      width: '100%',
                      px: 0,
                      mt: 2,
                    }}
                    control={
                      <Checkbox
                        value={bulletChartSettings.showIndicator2}
                        name="showIndicator2"
                        checked={bulletChartSettings.showIndicator2}
                        onChange={handleCheckBoxChange}
                      />
                    }
                    label="Show Indicator 2"
                  />
                  {bulletChartSettings.showIndicator2 ? (
                    <FormControl fullWidth>
                      Indicator 2 Value
                      <Input
                        type="number"
                        fullWidth
                        value={bulletChartSettings.indicator2Value}
                        name="indicator2Value"
                        onChange={handleChangeIndicator}
                      />
                    </FormControl>
                  ) : null}
                </>
              ) : null}
            </>
          }
          feelTabChidren={
            <>
              <FormControl
                fullWidth
                sx={{
                  mt: 2,
                  p: 2,
                  py: 0,
                }}
              >
                Bar Color
                <Input
                  type="color"
                  fullWidth
                  name="barColor"
                  value={bulletChartSettings.barColor}
                  onChange={handleChangeIndicator}
                />
              </FormControl>
              {bulletChartSettings.showThreshHoldValue ? (
                <>
                  <FormControl
                    fullWidth
                    sx={{
                      p: 2,
                      pt: 0,
                      mt: 1,
                    }}
                  >
                    Threshold Color
                    <Input
                      type="color"
                      fullWidth
                      name="threshHoldColor"
                      value={bulletChartSettings.threshHoldColor}
                      onChange={handleChangeIndicator}
                    />
                  </FormControl>
                </>
              ) : null}
              {bulletChartSettings.showIndicator1 ? (
                <>
                  <FormControl
                    fullWidth
                    sx={{
                      p: 2,
                      pt: 0,
                      mt: 1,
                    }}
                  >
                    Indicator 1 Color
                    <Input
                      type="color"
                      fullWidth
                      value={bulletChartSettings.indicator1Color}
                      name="indicator1Color"
                      onChange={handleChangeIndicator}
                    />
                  </FormControl>
                  {bulletChartSettings.showIndicator2 ? (
                    <FormControl
                      fullWidth
                      sx={{
                        p: 2,
                        pt: 0,
                        mt: 1,
                      }}
                    >
                      Indicator 2 Color
                      <Input
                        type="color"
                        fullWidth
                        value={bulletChartSettings.indicator2Color}
                        name="indicator2Color"
                        onChange={handleChangeIndicator}
                      />
                    </FormControl>
                  ) : null}
                </>
              ) : null}
              <FormControl
                fullWidth
                sx={{
                  p: 2,
                  pt: 0,
                }}
              >
                Margin Bottom
                <Input
                  type="number"
                  fullWidth
                  value={bulletChartSettings.margin.b}
                  name="b"
                  onChange={handleMarginChange}
                />
              </FormControl>
              <FormControl
                fullWidth
                sx={{
                  p: 2,
                  pt: 0,
                }}
              >
                Margin Top
                <Input
                  type="number"
                  fullWidth
                  value={bulletChartSettings.margin.t}
                  name="t"
                  onChange={handleMarginChange}
                />
              </FormControl>
              <FormControl
                fullWidth
                sx={{
                  p: 2,
                  pt: 0,
                }}
              >
                Margin Left
                <Input
                  type="number"
                  fullWidth
                  value={bulletChartSettings.margin.l}
                  name="l"
                  onChange={handleMarginChange}
                />
              </FormControl>
              <FormControl
                fullWidth
                sx={{
                  p: 2,
                  pt: 0,
                }}
              >
                Margin Right
                <Input
                  type="number"
                  fullWidth
                  value={bulletChartSettings.margin.r}
                  name="r"
                  onChange={handleMarginChange}
                />
              </FormControl>
            </>
          }
        />
        {/* <Box component={Card}>
          <DataWidgetContainer
            id={'bullet-chart-settings'}
            settings={bulletChartSettings}
            setSettings={handlebulletChartSettingsUpdate as setSettings}
          >
             <FormControl
              fullWidth
              sx={{
                p: 2,
                pt: 0,
              }}
            >
              Margin Bottom
              <Input
                type="number"
                fullWidth
                value={bulletChartSettings.margin.b}
                name="b"
                onChange={handleMarginChange}
              />
            </FormControl>
            <FormControl
              fullWidth
              sx={{
                p: 2,
                pt: 0,
              }}
            >
              Margin Top
              <Input
                type="number"
                fullWidth
                value={bulletChartSettings.margin.t}
                name="t"
                onChange={handleMarginChange}
              />
            </FormControl>
            <FormControl
              fullWidth
              sx={{
                p: 2,
                pt: 0,
              }}
            >
              Margin Left
              <Input
                type="number"
                fullWidth
                value={bulletChartSettings.margin.l}
                name="l"
                onChange={handleMarginChange}
              />
            </FormControl>
            <FormControl
              fullWidth
              sx={{
                p: 2,
                pt: 0,
              }}
            >
              Margin Right
              <Input
                type="number"
                fullWidth
                value={bulletChartSettings.margin.r}
                name="r"
                onChange={handleMarginChange}
              />
            </FormControl>
          </DataWidgetContainer>
        </Box> */}
      </Stack>
    </>
  );
};

export default BulletChartSettings;
