import { Asset } from '~/types/asset';
import { AssetMeasurement } from '~/measurements/domain/types';
import { Customer } from '~/types/customers';
import { Tree } from '~/components/AssetsTree';

type MeasurementGroup = { type: string; measurements: AssetMeasurement[] };

export type AssetWithMeasurements = Asset & { measurementGroups: MeasurementGroup[] };

const assetToTree = (
  asset: AssetWithMeasurements,
  assetsMap: Map<number, AssetWithMeasurements>,
): Tree =>
  new Tree(
    asset.id.toString(),
    asset.tag,
    'activo',
    asset.childrenIds
      .filter((assetId) => assetsMap.has(assetId))
      .map((assetId) => assetsMap.get(assetId) as AssetWithMeasurements) // Should never be undefined due to previous filter
      .map((asset) => assetToTree(asset, assetsMap))
      .concat(
        asset.measurementGroups.map(
          (measurementGroup) =>
            new Tree(
              `${asset.id}:${measurementGroup.measurements[0].typeId}`,
              measurementGroup.type,
              'medicion',
              measurementGroup.measurements.map(
                (measurement) =>
                  new Tree(
                    `m:${asset.id}:${measurement.id}`,
                    measurement.tag,
                    'metric',
                    [],
                    measurement.datasourceId ?? -1,
                  ),
              ),
              -1,
            ),
        ),
      ),
    -1,
  );

export const mapAssetTree = (customer: Customer, assets: AssetWithMeasurements[]): Tree => {
  const assetsMap: Map<number, AssetWithMeasurements> = new Map();
  assets.forEach((asset) => assetsMap.set(asset.id, asset));

  const rootAssets = assets.filter((asset) => asset.parentIds.length === 0);

  return new Tree(
    '-1',
    customer.name,
    'company',
    rootAssets.map((asset) => assetToTree(asset, assetsMap)),
    -1,
  );
};
