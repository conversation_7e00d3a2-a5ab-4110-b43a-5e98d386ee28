import { useGetCurrentUserDetailsQuery } from '~/redux/api/usersApi';
import { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';
import { getCustomerId } from '~/redux/selectors/customerSelectors';

export const useHasPowerUserAccess = () => {
  const { data, isLoading } = useGetCurrentUserDetailsQuery();
  const activeCustomerId = useSelector(getCustomerId);
  const [hasPowerUserAccess, setHasPowerUserAccess] = useState(false);
  useEffect(() => {
    setHasPowerUserAccess(false);
    if (!isLoading && data) {
      if (data.global_role === 'ADMIN' || data.global_role === 'POWER_USER') {
        setHasPowerUserAccess(true);
      } else {
        const scopedRoles = data.scoped_roles;
        for (const scopedRole of scopedRoles) {
          if (scopedRole.role === 'ADMIN' || scopedRole.role === 'POWER_USER') {
            if (scopedRole.customer_ids.includes(activeCustomerId)) {
              setHasPowerUserAccess(true);
              break;
            }
          }
        }
      }
    }
  }, [activeCustomerId, data, isLoading]);

  return hasPowerUserAccess;
};
