apiVersion: v1
# Indicates this as a service
kind: Service
metadata:
 # Service name
 name: be-frontend-poc
 namespace: admin
 annotations:
    service.beta.kubernetes.io/aws-load-balancer-type: "nlb"
    service.beta.kubernetes.io/aws-load-balancer-ssl-cert: "arn:aws:acm:us-east-1:067172429169:certificate/725eca42-339c-49ac-8b5b-43b38047693c"
    service.beta.kubernetes.io/aws-load-balancer-backend-protocol: "TLS"
    service.beta.kubernetes.io/aws-load-balancer-ssl-ports: "443"
    service.beta.kubernetes.io/aws-load-balancer-ssl-negotiation-policy: ELBSecurityPolicy-TLS13-1-2-2021-06
spec:
 selector:
   # Selector for Pods
    app.kubernetes.io/instance: be-frontend
    app.kubernetes.io/name: be-frontend
 ports:
   # Port Map
 - name: http
   port: 80
   targetPort: 8080
   protocol: TCP
 - name: https
   port: 443
   targetPort: 8080
   protocol: TCP
 type: Load<PERSON><PERSON>cer