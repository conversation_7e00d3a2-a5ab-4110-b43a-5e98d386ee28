# Build

1. Add locally published `brompton-component-library`:

```bash
yarn dlx yalc add brompton-component-library
```

2. Install dependencies:

```bash
yarn install
```
# Test case

1. package `brompton-admin-web-app` 

2. run command `npx playwright test yourfilename.spec.ts --headed`

3. show the logs and error after test cases runs
# Deployment

1. Package `be-component-library` image.

2. Update `package.json` version.

3. Set `NEXT_PUBLIC_BE_ADMIN_API_URL` to BE Admin API production url.

4. Build docker image for destination platform:

```bash
docker build --platform linux/amd64 . -t be-admin-web-app:<VERSION>
```

5. Save docker image as tar:

```bash
docker save be-admin-web-app:<VERSION> -o be-admin-web-app_<VERSION>.tar
```

6. Copy to server:

```bash
scp be-admin-web-app_<VERSION> <USER>@<SERVER_IP>:<DEST_DIR>
```

7. Log into server and load docker image:

```bash
docker load -i be-admin-web-app_<VERSION>.tar
```

# Deployment on AWS

## Useful Links

[Application Environment](https://us-east-1.console.aws.amazon.com/elasticbeanstalk/home?region=us-east-1#/application/versions?applicationName=datalogger-admin-app)
[EC2 Instance](https://us-east-1.console.aws.amazon.com/ec2/home?region=us-east-1#InstanceDetails:instanceId=i-0cdc8054f537f84aa)

## Command for tailing logs

```bash
tail /var/log/eb-engine.log -f
```

```bash
tail /var/log/web.stdout.log -f
```


# Handling Semantic Versioning
With semantic versioning, the version number x.y.z is divided into:

x - Major version
y - Minor version
z - Patch version

## In this format:
Major Version (x) changes indicate significant updates that might include incompatible API changes.
Minor Version (y) changes typically add functionality in a backward-compatible manner.
Patch Version (z) changes are usually for backward-compatible bug fixes.
Updating Release Data
When updating the release data based on PRs, you'll need to determine which part of the version the PR impacts. This can be part of the PR's metadata or determined through some other means. Then, follow these guidelines:

For Major Changes (x): Increment the x and reset y and z to 0. For example, 1.2.3 becomes 2.0.0.
For Minor Changes (y): Increment the y and reset z to 0, while keeping x the same. For example, 1.2.3 becomes 1.3.0.
For Patch Changes (z): Increment the z while keeping x and y the same. For example, 1.2.3 becomes 1.2.4.