import { yupResolver } from '@hookform/resolvers/yup';
import CancelIcon from '@mui/icons-material/Cancel';
import SaveIcon from '@mui/icons-material/Save';
import SaveAsIcon from '@mui/icons-material/SaveAs';
import {
  Alert,
  Button,
  Checkbox,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  FormControlLabel,
  Radio,
  RadioGroup,
  TextField,
} from '@mui/material';
import { useEffect, useState } from 'react';
import { Controller, useForm } from 'react-hook-form';
import { useSelector } from 'react-redux';
import { useHasAdminAccess } from '~/hooks/useHasAdminAccess';
import { useGetDashboardTemplatesQuery } from '~/redux/api/dashboardTemplate';
import { getCurrentAssetType, getDashboardTemplateId } from '~/redux/selectors/dashboardSelectors';
import { dashboardTemplateTitleSchema, DashboarTemplateForm } from '~/types/dashboardList';
import DialogTransition from '../common/DialogTransition';

type DashboardTemplateTitleDialogProps = {
  open: boolean;
  onClose: () => void;
  flow: 'save' | 'update';
  onSave: (data: DashboarTemplateForm, flow: 'save' | 'update') => void;
  initialTitle: string;
};

const DashboardTemplateTitleDialog = ({
  flow,
  initialTitle,
  onClose,
  onSave,
  open,
}: DashboardTemplateTitleDialogProps) => {
  const [action, setAction] = useState<'save' | 'update'>(flow);
  const templateID = useSelector(getDashboardTemplateId);
  const assetType = useSelector(getCurrentAssetType);
  const { globalAdmin } = useHasAdminAccess();
  const { data: dashboardTemplates } = useGetDashboardTemplatesQuery(
    { assetTypeId: assetType },
    { skip: !assetType, refetchOnMountOrArgChange: true },
  );
  const { control, handleSubmit, reset, setValue, getValues } = useForm<DashboarTemplateForm>({
    defaultValues: {
      title: initialTitle,
      save_as_global_dashboard_template: false, // Initialize checkbox field
    },
    resolver: yupResolver(dashboardTemplateTitleSchema),
  });
  useEffect(() => {
    setAction(flow);
  }, [flow]);
  useEffect(() => {
    if (action === 'save') {
      reset({
        title: '',
        save_as_global_dashboard_template: false,
      });
    } else {
      reset({
        title: initialTitle,
        save_as_global_dashboard_template: getValues('save_as_global_dashboard_template'),
      });
    }
  }, [flow, action, reset, initialTitle]);
  useEffect(() => {
    if (flow === 'update' && open) {
      const dashboardTemplate = dashboardTemplates?.items?.find(
        (template) => template.id === templateID,
      );
      if (dashboardTemplate && dashboardTemplate?.customer === null) {
        setValue('save_as_global_dashboard_template', true);
      } else {
        setValue('save_as_global_dashboard_template', false);
      }
    }
    if (action === 'save' && open) {
      setValue('save_as_global_dashboard_template', false);
    }
  }, [dashboardTemplates, open, flow, templateID, action]);

  return (
    <Dialog
      open={open}
      onClose={() => {
        onClose();
        setAction(flow);
        reset({
          title: flow === 'update' ? initialTitle : '',
          description: '',
          save_as_global_dashboard_template: false,
        });
      }}
      TransitionComponent={DialogTransition}
      sx={{
        '& .MuiDialog-container': {
          '& .MuiPaper-root': {
            width: '100%',
            maxWidth: '600px',
            p: 1,
          },
        },
      }}
    >
      <form
        onSubmit={handleSubmit(async (data) => {
          onClose();
          onSave(data, action);
        })}
        noValidate
      >
        <DialogTitle>{!initialTitle ? 'Save Dashboard Template' : 'Update Dashboard'}</DialogTitle>

        <DialogContent>
          {initialTitle && (
            <RadioGroup
              aria-label="action"
              name="action"
              value={action}
              onChange={(e) => setAction(e.target.value as 'save' | 'update')}
            >
              <FormControlLabel value="save" control={<Radio />} label="Save As" />
              <FormControlLabel value="update" control={<Radio />} label="Update" />
            </RadioGroup>
          )}

          <Controller
            name="title"
            control={control}
            render={({ field, fieldState }) => (
              <TextField
                {...field}
                error={!!fieldState.error}
                helperText={fieldState.error?.message}
                label="Dashboard Title"
                variant="outlined"
                margin="normal"
                fullWidth
                required
              />
            )}
          />
          <Controller
            name="save_as_global_dashboard_template"
            control={control}
            render={({ field }) => (
              <FormControlLabel
                control={
                  <Checkbox
                    {...field}
                    disabled={!globalAdmin || action === 'update'}
                    checked={field.value}
                    onChange={(e) => field.onChange(e.target.checked)}
                  />
                }
                label="Save as Global Dashboard Template"
              />
            )}
          />

          {!globalAdmin && action === 'update' && (
            <Alert severity="warning">
              Please log in as a global user to save a global dashboard template.
            </Alert>
          )}
        </DialogContent>

        <DialogActions>
          <Button
            onClick={() => {
              onClose();
              setAction(flow);
              reset({
                title: flow === 'update' ? initialTitle : '',
                description: '',
                save_as_global_dashboard_template: false,
              });
            }}
            variant="outlined"
            startIcon={<CancelIcon />}
          >
            Cancel
          </Button>
          <Button
            type="submit"
            variant="contained"
            color="primary"
            disabled={!globalAdmin && action === 'update'}
            startIcon={!initialTitle || action === 'save' ? <SaveIcon /> : <SaveAsIcon />}
          >
            {!initialTitle || action === 'save' ? 'Save' : 'Update'}
          </Button>
        </DialogActions>
      </form>
    </Dialog>
  );
};

export default DashboardTemplateTitleDialog;
