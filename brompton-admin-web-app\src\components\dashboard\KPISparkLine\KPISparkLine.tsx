import InfoIcon from '@mui/icons-material/Info';
import { Box, Stack, Tooltip, Typography } from '@mui/material';
import dynamic from 'next/dynamic';
import { useSelector } from 'react-redux';
import Loader from '~/components/common/Loader';
import NoMeasureSelected from '~/components/common/NoMeasureSelected';
import { useFetchKPISparkLine } from '~/hooks/useFetchKPISparkLine';
import { getZoomEnabled } from '~/redux/selectors/dashboardSelectors';
import { getThousandSeparator } from '~/redux/selectors/userPreferences';
import { KPISparkline } from '~/types/widgets';
import { formatMetricLabel, formatNumber, hasNoMeasureSelected, roundNumber } from '~/utils/utils';
import CommonWidgetContainer from '../../common/CommonWidgetContainer';
import KPISparkLineDialog from './KPISparkLineDialog';
const Plot = dynamic(() => import('react-plotly.js'), { ssr: false });
type KPISparkLineProps = {
  id: string;
  settings: KPISparkline;
};
const KPISparkLine = ({ id, settings }: KPISparkLineProps) => {
  const {
    chartData,
    layoutData,
    isLoading,
    stats,
    uom,
    removedResults,
    successAndFailedMeasurements,
  } = useFetchKPISparkLine(id, settings);
  const defaultTitle = formatMetricLabel(settings.title.value);
  const noMeasureSelected = hasNoMeasureSelected(settings);
  const enabledZoom = useSelector(getZoomEnabled);
  const thousandSeparator = useSelector(getThousandSeparator);

  return (
    <>
      <CommonWidgetContainer
        id={id}
        successAndFailedMeasurements={successAndFailedMeasurements}
        removedResults={removedResults}
        widgetType="kpi-sparkline"
        widgetName="KPI Sparkline"
        settings={settings}
        settingsDialog={KPISparkLineDialog}
        widgetContent={
          <Box
            sx={{
              display: 'flex',
              height: '100%',
              flexDirection: 'column',
              alignItems: 'center',
            }}
          >
            {noMeasureSelected ? (
              <NoMeasureSelected />
            ) : (
              <>
                {isLoading ? (
                  <Loader />
                ) : (
                  <>
                    <Box sx={{ width: '100%', pl: 2, pt: 2 }}>
                      {settings.title.isVisible && (
                        <Stack direction="row" alignItems="center" gap={1}>
                          <Typography
                            variant="h5"
                            sx={{
                              fontSize: settings.title.isVisible
                                ? settings.title.fontSize + 'px'
                                : undefined,
                              fontWeight: settings.title.isVisible
                                ? settings.title.fontWeight
                                : undefined,
                              color: settings.title.isVisible ? settings.title.color : '#a1a1a1',
                            }}
                            component="div"
                          >
                            {settings.title.isVisible ? settings.title.value : defaultTitle}
                          </Typography>
                          <Tooltip title={settings.tooltip ?? 'Sparkline tooltip'}>
                            <InfoIcon />
                          </Tooltip>
                        </Stack>
                      )}
                      <Typography
                        variant="h4"
                        sx={{
                          fontSize: settings.value?.fontSize + 'px',
                          fontWeight: settings.value?.fontWeight,
                          color: settings.value?.color,
                        }}
                        component="div"
                      >
                        {settings.prefix?.isVisible ? settings.prefix?.value : null}{' '}
                        {thousandSeparator
                          ? formatNumber(Number(stats.lastValue || 0))
                          : roundNumber(Number(stats.lastValue || 0))}
                        {uom}
                        {settings.suffix?.isVisible ? settings.suffix?.value : null}{' '}
                      </Typography>
                    </Box>
                    <Box height={'200%'} width={'100%'}>
                      <Plot
                        data={chartData}
                        useResizeHandler={true}
                        style={{ width: '100%', height: '100%' }}
                        layout={{
                          ...layoutData,
                          xaxis: {
                            ...layoutData.xaxis,
                            position: 0,
                            fixedrange: enabledZoom ? true : undefined,
                          },
                          yaxis: {
                            ...layoutData.yaxis,
                            position: 0,
                            fixedrange: enabledZoom ? true : undefined,
                          },
                          legend: {
                            x: 0, // Position legend at the left
                            y: -0.1, // Position legend slightly below the x-axis
                            xanchor: 'left', // Anchor the legend to the right side of the x position
                            yanchor: 'top', // Anchor the legend to the top side of the y position
                          },
                          autosize: true,
                        }}
                        config={{
                          responsive: true,
                          displaylogo: false,
                          displayModeBar: false, // This will hide the entire mode bar
                          modeBarButtonsToRemove: ['pan2d', 'lasso2d'],
                          watermark: false,
                        }}
                      />
                    </Box>
                  </>
                )}
              </>
            )}
          </Box>
        }
      />
    </>
  );
};
export default KPISparkLine;
