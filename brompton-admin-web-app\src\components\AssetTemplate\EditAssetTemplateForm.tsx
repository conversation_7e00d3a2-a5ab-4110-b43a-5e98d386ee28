import DeleteIcon from '@mui/icons-material/Delete';
import EditIcon from '@mui/icons-material/Edit';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import {
  Accordion,
  AccordionDetails,
  AccordionSummary,
  Alert,
  AlertTitle,
  Autocomplete,
  Box,
  Button,
  Card,
  Checkbox,
  Chip,
  Collapse,
  FormControl,
  FormControlLabel,
  FormHelperText,
  FormLabel,
  Grid,
  IconButton,
  List,
  ListItem,
  ListItemText,
  Paper,
  Radio,
  RadioGroup,
  Stack,
  Step,
  StepLabel,
  Stepper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TextField,
  Typography,
} from '@mui/material';
import { useEffect, useState } from 'react';
import { Controller } from 'react-hook-form';
import useEditAssetTemplateHelper from '~/hooks/useEditAssetTemplateHelper';
import {
  editAssetTemplateForm,
  getAssetTemplateDataWithMetricName,
} from '~/measurements/domain/types'; // Updated import path
import { useUpdateAssetTemplateMultiMutation } from '~/redux/api/assetsApi';
import { AssetTypeOption } from '~/types/asset';
import { calc_engine_template, poll_period } from '~/types/calc_engine';
import { assetTypePathMapperFilterTemplates } from '~/utils/mappers/asset-type-mapper';
import ExpressionTemplateDetails from '../CalcEngine/ExpressionTemplateDetails';

// MUI Stepper schema form
type EditAssetTemplateFormProps = {
  isFetching: boolean;
  assetTemplate: string | undefined;
  data: getAssetTemplateDataWithMetricName | undefined;
};
const MAX_VISIBLE_ASSETS = 5;
const EditAssetTemplateForm = ({ data, assetTemplate, isFetching }: EditAssetTemplateFormProps) => {
  const [activeStep, setActiveStep] = useState(0);
  const [currentMeausreIndex, setCurrentMeasureIndex] = useState<number>(-1);
  const [expanded, setExpanded] = useState(false);
  const [currentCalcMeasureIndex, setCurrentCalcMeasureIndex] = useState<number>(-1);
  const [assetTypesWithPath, setAssetTypesWithPath] = useState<AssetTypeOption[]>([]);
  const [measurementIds, setMeasurementIds] = useState<number[]>([]);
  const [existingMeasurementMetrics, setExistingMeasurementMetrics] = useState<string[]>([]);
  const [updateAssetTemplateMulti, { isError, isLoading, isSuccess, error }] =
    useUpdateAssetTemplateMultiMutation();
  const {
    assetTypeListData,
    assetTypeMetricsListOptions,
    dataTypesListOptions,
    datasourceOptions,
    locationsListOption,
    measurementTypeListOptions,
    valueTypeOptions,
    assets,
    fields,
    append,
    remove,
    update,
    hasDuplicates,
    editAssetTemplate: { control, errors, handleSubmit, setValue, trigger, formValues },
    measurementsEdits: {
      measurementsControl,
      measureClearError,
      measureErrors,
      resetMeasure,
      measurementGetValues,
      measurementSubmit,
      setMeasureValue,
    },
    calculationMeasurement: {
      calcMeasureError,
      calcMeasureGetValues,
      calcMeasureHandleSubmit,
      calcMeasureSetValue,
      calcMeasureWatch,
      calcMeasurementController,
      resetCalcMeasureValues,
      variableFields,
    },
    calcMeasurements,
    setCalcMeasurements,
    expressionTemplates,
    fetchingExpressionTemplates,
    pollPeriods,
    steps,
    calculatedMeasures,
    calculationSource,
  } = useEditAssetTemplateHelper({ data, currentMeausreIndex, activeStep });
  const [expression, setExpression] = useState<calc_engine_template | null>(null);
  const variableInputs = calcMeasureWatch('variable_inputs');
  const expressionTemplate = calcMeasureWatch('expression_template_id');
  useEffect(() => {
    if (!expressionTemplate || !expressionTemplates) {
      setExpression(null);
      calcMeasureSetValue('variable_inputs', []);
      return;
    }
    const expressionTemplateToFind = expressionTemplates.items.find(
      (template) => template.id === expressionTemplate,
    );
    if (!expressionTemplateToFind) {
      setExpression(null);
      calcMeasureSetValue('variable_inputs', []);
      return;
    }
    setExpression(expressionTemplateToFind);
    calcMeasureSetValue('expression_template_id', expressionTemplateToFind.id);

    const variableMatches = expressionTemplateToFind.expression.match(/\$[A-Za-z0-9_]+/g);
    const uniqueVariables = Array.from(new Set(variableMatches ?? []));
    const defaultVariableInputs = uniqueVariables.map((variable) => ({
      variable,
      type: 'measurement' as const,
      metric_id: null,
      constant_value: null,
      comment: '',
    }));

    const currentMetricId = fields[currentCalcMeasureIndex]?.metric_id?.toString();

    const matchedCalc = calcMeasurements.find((calc) => calc.metric_id === currentMetricId);

    const updatedVariableInputs = defaultVariableInputs.map((input) => {
      const existing = matchedCalc?.calcMeasurementData.variable_inputs.find(
        (v) => v.variable === input.variable,
      );
      return {
        ...input,
        type: existing?.type ?? input.type,
        metric_id: existing?.metric_id ?? null,
        constant_value: existing?.constant_value ?? null,
        comment: existing?.comment ?? '',
      };
    });

    // Set form state
    calcMeasureSetValue('variable_inputs', updatedVariableInputs);

    // Update calcMeasurements for this specific metric
    setCalcMeasurements((prev) =>
      prev.map((calc) => {
        if (calc.metric_id === currentMetricId) {
          return {
            ...calc,
            variable: updatedVariableInputs,
            calcMeasurementData: {
              ...calc.calcMeasurementData,
              variable_inputs: updatedVariableInputs,
            },
          };
        }
        return calc;
      }),
    );
  }, [expressionTemplate, expressionTemplates]);
  let hasInvalidVariable = false;
  calcMeasurements.forEach((measure) => {
    if (measure.calcMeasurementData.variable_inputs.length === 0) {
      hasInvalidVariable = true;
    }
    measure.calcMeasurementData.variable_inputs.forEach((varis) => {
      if (varis.type === 'measurement') {
        if (varis.metric_id === null || varis.metric_id === undefined) {
          hasInvalidVariable = true;
        }
      } else if (varis.type === 'constant') {
        if (varis.constant_value === null || varis.constant_value === undefined) {
          hasInvalidVariable = true;
        }
      } else {
        hasInvalidVariable = true; // invalid type
      }
    });
  });
  useEffect(() => {
    if (assetTypeListData) {
      setAssetTypesWithPath(
        assetTypePathMapperFilterTemplates(
          assetTypeListData.map((item) => ({
            ...item,
            name: `${item.name}`,
            id: item.id,
          })),
        ),
      );
    }
  }, [assetTypeListData]);

  useEffect(() => {
    if (data && assetTypesWithPath.length > 0) {
      const selectedAssetType = assetTypesWithPath.find(
        (item) => item.value === data.asset_type_id,
      );
      if (selectedAssetType) {
        setValue('asset_type_id', selectedAssetType.value);
      }
      setValue('manufacturer', data.manufacturer);
      setValue('model_number', data.model_number);
      setValue('save_as_global_asset_template', data.customer ? false : true);
      if (data.measurements && data.measurements.length > 0 && measurementIds.length === 0) {
        const currentMeasurementIds = new Set(measurementIds);
        const measurementsToAppend: {
          description: string | undefined; // description must be string or null, not undefined
          location_id: number | null | undefined;
          datasource_id: number | null | undefined;
          meter_factor: number | null | undefined;
          type_id: number | null;
          data_type_id: number | null;
          value_type_id: number | null;
          metric_id: number | null;
          id: number | undefined | null;
        }[] = [];

        data.measurements.forEach((measurement) => {
          // eslint-disable-next-line @typescript-eslint/ban-ts-comment
          // @ts-ignore
          if (!currentMeasurementIds.has(Number(measurement.id))) {
            // eslint-disable-next-line @typescript-eslint/ban-ts-comment
            // @ts-ignore
            currentMeasurementIds.add(measurement.id);
            // Ensure description is not undefined
            measurementsToAppend.push({
              type_id: measurement.type_id,
              data_type_id: measurement.data_type_id,
              value_type_id: measurement.value_type_id,
              metric_id:
                typeof measurement.metric_id === 'number'
                  ? measurement.metric_id
                  : Number(measurement.metric_id) || null,
              description: measurement.description ?? undefined, // Fallback to null if description is undefined
              location_id: measurement.location_id ?? null, // Fallback to null if location_id is undefined
              datasource_id: measurement.datasource_id ?? null, // Fallback to null if datasource_id is undefined
              meter_factor: measurement.meter_factor ?? null, // Fallback to null if meter_factor is undefined
              id: measurement.id ?? null,
            });
          }
        });

        measurementsToAppend.forEach((measurement) => {
          const isValidMeasurement =
            measurement.type_id === null ||
            measurement.data_type_id === null ||
            measurement.value_type_id === null ||
            measurement.metric_id === null;

          if (!isValidMeasurement) {
            // eslint-disable-next-line @typescript-eslint/ban-ts-comment
            //@ts-ignore
            append(measurement);
          }
        });
        setMeasurementIds(Array.from(currentMeasurementIds));
      }
    }
  }, [data, assetTypesWithPath]);
  useEffect(() => {
    if (data) {
      const measurementIdsSet = new Set(
        data.measurements.map((measurement) => measurement.metric_id?.toString() ?? ''),
      );
      const existingMetrics = data.measurements
        .filter((measurement) => measurementIdsSet.has(measurement.metric_id?.toString() ?? ''))
        .map((measurement) => measurement.metric_id?.toString() ?? '');
      setExistingMeasurementMetrics(existingMetrics);
    }
  }, [data]);
  const handleNext = async () => {
    // Trigger validation on first step before moving to second step
    const isValid = await trigger(['asset_type_id', 'manufacturer', 'model_number']);
    if (isValid) {
      setActiveStep((prevStep) => prevStep + 1);
    }
  };

  const handleBack = () => {
    setActiveStep((prevStep) => prevStep - 1);
  };

  const handleMeasure = measurementSubmit((data) => {
    const metricIdStr = data.metric_id?.toString() ?? '';
    const isCalcMeasurement = data.datasource_id === calculationSource?.id;
    const addCalcMeasurement = () => {
      setCalcMeasurements((prev) => [
        ...prev,
        {
          metric_id: metricIdStr,
          existing: false,
          calcMeasurementData: {
            expression_template_id: null,
            is_persisted: false,
            poll_period: null,
            writeback: false,
            variable_inputs: [],
          },
        },
      ]);
    };
    if (currentMeausreIndex === -1) {
      // If currentMeausreIndex is -1, add a new measurement to the fields array
      append({
        type_id: data.type_id ?? 0,
        location_id: data.location_id ?? 0,
        datasource_id: data.datasource_id ?? 0,
        description: data.description ?? '',
        meter_factor: data.meter_factor ?? 0,
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        //@ts-ignore
        metric_id: data.metric_id ?? 0,
        data_type_id: data.data_type_id ?? 0,
        value_type_id: data.value_type_id ?? 0,
        id: undefined,
      });
      if (isCalcMeasurement) {
        addCalcMeasurement();
      } else {
        setCalcMeasurements((prev) => prev.filter((calc) => calc.metric_id !== metricIdStr));
      }
    } else {
      // Use the update method to replace the specific field at the index
      setValue(`measurements.${currentMeausreIndex}`, {
        type_id: data.type_id ?? 0,
        location_id: data.location_id ?? 0,
        datasource_id: data.datasource_id ?? 0,
        description: data.description ?? '',
        meter_factor: data.meter_factor ?? 0,
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        //@ts-ignore
        metric_id: data.metric_id ?? 0,
        data_type_id: data.data_type_id ?? 0,
        value_type_id: data.value_type_id ?? 0,
        id: data.id ?? undefined,
      });
      if (isCalcMeasurement) {
        const existingCalcMeasurement = calcMeasurements.find(
          (calc) => calc.metric_id === metricIdStr,
        );

        if (existingCalcMeasurement) {
          // Update variable_inputs for existing
          setCalcMeasurements((prev) =>
            prev.map((calc) =>
              calc.metric_id === metricIdStr
                ? {
                    ...calc,
                    calcMeasurementData: {
                      ...calc.calcMeasurementData,
                      variable_inputs: [
                        ...calc.calcMeasurementData.variable_inputs,
                        {
                          variable: '',
                          type: 'measurement',
                          metric_id: null,
                          constant_value: null,
                          comment: '',
                        },
                      ],
                    },
                  }
                : calc,
            ),
          );
        } else {
          addCalcMeasurement();
        }
      } else {
        setCalcMeasurements((prev) => prev.filter((calc) => calc.metric_id !== metricIdStr));
      }
      update(currentMeausreIndex, {
        type_id: data.type_id ?? 0,
        location_id: data.location_id ?? 0,
        datasource_id: data.datasource_id ?? 0,
        description: data.description ?? '',
        meter_factor: data.meter_factor ?? 0,
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        //@ts-ignore
        metric_id: data.metric_id ?? 0,
        data_type_id: data.data_type_id ?? 0,
        value_type_id: data.value_type_id ?? 0,
        id: data.id ?? undefined,
      });
    }

    // Reset the form and index after submitting the form
    resetMeasure();
    setCurrentMeasureIndex(-1);
  });

  return (
    <Box sx={{ width: '100%' }}>
      <Stepper
        activeStep={activeStep}
        alternativeLabel
        sx={{ position: 'sticky', top: 0, backgroundColor: 'white', zIndex: 1000, padding: '10px' }}
      >
        {steps.filter(Boolean).map((label) => (
          <Step key={label}>
            <StepLabel>{label}</StepLabel>
          </Step>
        ))}
      </Stepper>
      {activeStep === 0 && assets.length > 0 && (
        <Box sx={{ width: '100%', mt: 2, mb: 4 }}>
          <Alert
            severity="warning"
            sx={{
              borderRadius: 2,
              p: 2,
              '& .MuiAlert-message': {
                width: '100%',
              },
            }}
          >
            <AlertTitle>Important Notice</AlertTitle>
            <Typography variant="body2" sx={{ mb: 1 }}>
              The following assets will be affected by this update:
            </Typography>

            {/* Show first 5 assets */}
            <List dense>
              {assets.slice(0, MAX_VISIBLE_ASSETS).map((asset, index) => (
                <ListItem key={asset.asset.id || index} sx={{ py: 0, pl: 2 }}>
                  <ListItemText primary={`${index + 1}. ${asset.path}`} />
                </ListItem>
              ))}
            </List>

            {assets.length > MAX_VISIBLE_ASSETS && (
              <Box sx={{ mt: 0 }}>
                <Accordion
                  elevation={0}
                  expanded={expanded}
                  onChange={() => setExpanded(!expanded)}
                  sx={{
                    backgroundColor: 'transparent',
                    border: 'none',
                    '&:before': { display: 'none' },
                  }}
                >
                  <AccordionSummary
                    expandIcon={
                      <IconButton>
                        <ExpandMoreIcon fontSize="medium" />
                      </IconButton>
                    }
                    sx={{
                      minHeight: 'auto',
                      p: 1,
                      borderRadius: 1,
                      bgcolor: expanded ? 'grey.200' : 'grey.100',
                      transition: 'all 0.3s',
                    }}
                  >
                    <Typography variant="body2" sx={{ fontWeight: 500 }}>
                      {expanded ? 'Show Less' : `Show More`}
                    </Typography>
                    <Chip
                      label={`${assets.length - MAX_VISIBLE_ASSETS} more`}
                      size="small"
                      sx={{ ml: 1, bgcolor: 'primary.light', color: 'primary.dark' }}
                    />
                  </AccordionSummary>
                  <AccordionDetails sx={{ p: 1 }}>
                    <Collapse in={expanded}>
                      <List dense>
                        {assets.slice(MAX_VISIBLE_ASSETS).map((asset, index) => (
                          <ListItem
                            key={asset.asset.id || index + MAX_VISIBLE_ASSETS}
                            sx={{ py: 0, pl: 2 }}
                          >
                            <ListItemText
                              primary={`${index + MAX_VISIBLE_ASSETS + 1}. ${asset.path}`}
                            />
                          </ListItem>
                        ))}
                      </List>
                    </Collapse>
                  </AccordionDetails>
                </Accordion>
              </Box>
            )}
          </Alert>
        </Box>
      )}
      <form
        onSubmit={handleSubmit(async (formData: editAssetTemplateForm) => {
          try {
            if (calculatedMeasures.length > 0) {
              await handleNext();
            } else {
              const stringMetrics = fields.filter((measure) => isNaN(Number(measure.metric_id)));
              await updateAssetTemplateMulti({
                assetTypeId: formData.asset_type_id.toString(),
                assetTemplateId: assetTemplate !== undefined ? assetTemplate?.toString() ?? '' : '',
                updateAssetTemplate: {
                  metrics: stringMetrics
                    .map((metric) => metric.metric_id?.toString())
                    .filter((id): id is string => id !== undefined),
                  updateAssetTemplate: {
                    ...formData,
                    measurements:
                      formData?.measurements?.map((measure) => ({
                        type_id: Number(measure.type_id),
                        data_type_id: Number(measure.data_type_id),
                        value_type_id: Number(measure.value_type_id),
                        metric_id: measure?.metric_id ?? undefined,
                        description: measure.description ?? '',
                        location_id: measure.location_id ?? null,
                        datasource_id: measure.datasource_id ?? null,
                        meter_factor: measure.meter_factor ?? null,
                        id: measure.id ?? undefined,
                      })) ?? [],
                  },
                },
              });
            }
          } catch (e) {
            console.log(e);
          }
        })}
      >
        {activeStep === 0 && (
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2, mt: 2 }}>
            <Controller
              name="asset_type_id"
              control={control}
              render={({ field }) => (
                <Autocomplete
                  {...field}
                  disabled={isFetching}
                  options={assetTypesWithPath}
                  getOptionLabel={(option) => option.label || ''}
                  value={
                    assetTypesWithPath.find((assetType) => assetType.value === field.value) ?? null
                  } // Use the full object as the value
                  onChange={(_, value) => {
                    if (value?.value) {
                      setValue('asset_type_id', value?.value ?? null);
                    }
                  }} // Set the selected option as the value
                  onBlur={field.onBlur}
                  renderInput={(params) => (
                    <TextField
                      {...params}
                      label="Asset Type"
                      error={!!errors.asset_type_id}
                      helperText={errors.asset_type_id?.message}
                    />
                  )}
                />
              )}
            />
            <Controller
              name="manufacturer"
              control={control}
              render={({ field }) => (
                <TextField
                  {...field}
                  disabled={isFetching}
                  label="Manufacturer"
                  error={!!errors.manufacturer}
                  onBlur={field.onBlur}
                  helperText={errors.manufacturer?.message}
                  InputLabelProps={{ shrink: true }}
                />
              )}
            />
            <Controller
              name="model_number"
              control={control}
              render={({ field }) => (
                <TextField
                  {...field}
                  label="Model Number"
                  disabled={isFetching}
                  error={!!errors.model_number}
                  onBlur={field.onBlur}
                  helperText={errors.model_number?.message}
                  InputLabelProps={{ shrink: true }}
                />
              )}
            />
            <Controller
              name="save_as_global_asset_template"
              control={control}
              render={({ field }) => (
                <FormControlLabel
                  control={
                    <Checkbox
                      {...field}
                      disabled
                      checked={field.value || false}
                      onChange={(e) => field.onChange(e.target.checked)}
                    />
                  }
                  label="Save as Global Asset Template"
                />
              )}
            />
            <Button onClick={handleNext} variant="contained" disabled={isFetching}>
              Next
            </Button>
          </Box>
        )}

        {activeStep === 1 && (
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
            <Box sx={{ mt: 2, mb: 2, p: 2 }} component={Card}>
              <Typography variant="h6" sx={{ mb: 2 }}>
                {currentMeausreIndex === -1
                  ? 'Add Measurement'
                  : `Edit Measurement #${currentMeausreIndex + 1}`}
              </Typography>
              <form onSubmit={(e) => e.preventDefault()}>
                <Grid container spacing={2}>
                  {/* Type Field */}
                  <Grid item md={4}>
                    <Controller
                      name={`type_id`}
                      control={measurementsControl}
                      render={({ field }) => (
                        <Autocomplete
                          {...field}
                          options={measurementTypeListOptions}
                          getOptionLabel={(option) => option.label || ''}
                          value={
                            measurementTypeListOptions.find(
                              (item) => item.id === field.value?.toString(),
                            ) ?? null
                          }
                          onBlur={field.onBlur}
                          onChange={(_, value) => {
                            if (value?.id) {
                              setMeasureValue('type_id', Number(value?.id ?? null));
                              measureClearError('type_id');
                            }
                          }}
                          renderInput={(params) => (
                            <TextField
                              {...params}
                              label="Type"
                              error={!!measureErrors?.type_id}
                              helperText={measureErrors?.type_id?.message}
                            />
                          )}
                        />
                      )}
                    />
                  </Grid>

                  {/* Data Type Field */}
                  <Grid item md={4}>
                    <Controller
                      name={`data_type_id`}
                      control={measurementsControl}
                      render={({ field }) => (
                        <Autocomplete
                          {...field}
                          options={dataTypesListOptions}
                          getOptionLabel={(option) => option.label || ''}
                          value={
                            dataTypesListOptions.find(
                              (item) => item.id === field.value?.toString(),
                            ) ?? null
                          }
                          onBlur={field.onBlur}
                          onChange={(_, value) => {
                            if (value?.id) {
                              setMeasureValue('data_type_id', Number(value?.id ?? null));
                              measureClearError('data_type_id');
                            }
                          }}
                          renderInput={(params) => (
                            <TextField
                              {...params}
                              label="Data Type"
                              error={!!measureErrors?.data_type_id}
                              helperText={measureErrors?.data_type_id?.message}
                            />
                          )}
                        />
                      )}
                    />
                  </Grid>

                  {/* Value Type Field */}
                  <Grid item md={4}>
                    <Controller
                      name={`value_type_id`}
                      control={measurementsControl}
                      render={({ field }) => (
                        <Autocomplete
                          {...field}
                          options={valueTypeOptions}
                          getOptionLabel={(option) => option.label || ''}
                          value={
                            valueTypeOptions.find((item) => item.id === field.value?.toString()) ??
                            null
                          }
                          onBlur={field.onBlur}
                          onChange={(_, value) => {
                            if (value?.id) {
                              setMeasureValue('value_type_id', Number(value?.id ?? null));
                              measureClearError('value_type_id');
                            }
                          }}
                          renderInput={(params) => (
                            <TextField
                              {...params}
                              label="Value Type"
                              error={!!measureErrors?.value_type_id}
                              helperText={measureErrors?.value_type_id?.message}
                            />
                          )}
                        />
                      )}
                    />
                  </Grid>

                  {/* Metric Field */}
                  <Grid item md={4}>
                    <Controller
                      name="metric_id"
                      control={measurementsControl}
                      render={({ field }) => {
                        const selectedOption = assetTypeMetricsListOptions.find(
                          (option) => String(option.id) === String(field.value),
                        );
                        return (
                          <Autocomplete
                            freeSolo
                            sx={{
                              '.MuiFormControl-root': {
                                mt: 0,
                              },
                              '*': { mt: 0, background: 'transparent' },
                            }}
                            options={assetTypeMetricsListOptions}
                            getOptionLabel={(option) =>
                              typeof option === 'string' ? option : String(option.label)
                            }
                            isOptionEqualToValue={(option, value) =>
                              typeof option === 'string'
                                ? option === value
                                : String(option.id) === String(value.id)
                            }
                            value={
                              selectedOption ||
                              (field.value ? { id: field.value, label: field.value } : null)
                            }
                            onChange={(_, newValue) => {
                              let updatedMetricId: string | null = null;
                              if (typeof newValue === 'string') {
                                field.onChange(newValue);
                              } else if (newValue && typeof newValue.id === 'string') {
                                field.onChange(newValue.id);
                                updatedMetricId = newValue.id;
                              } else {
                                field.onChange(null);
                              }
                              if (updatedMetricId !== null) {
                                setCalcMeasurements((prev) =>
                                  prev.map((entry) => {
                                    const shouldUpdateOutput =
                                      String(entry.metric_id) === String(field.value);

                                    const updatedInputs =
                                      entry.calcMeasurementData.variable_inputs.map((input) => {
                                        if (String(input.metric_id) === String(field.value)) {
                                          return {
                                            ...input,
                                            metric_id: Number(updatedMetricId),
                                          };
                                        }
                                        return input;
                                      });

                                    if (
                                      shouldUpdateOutput ||
                                      updatedInputs.some(
                                        (input, i) =>
                                          input !== entry.calcMeasurementData.variable_inputs[i],
                                      )
                                    ) {
                                      return {
                                        ...entry,
                                        metric_id: shouldUpdateOutput
                                          ? String(updatedMetricId) // ✅ safely convert to string
                                          : entry.metric_id,
                                        calcMeasurementData: {
                                          ...entry.calcMeasurementData,
                                          variable_inputs: updatedInputs,
                                        },
                                      };
                                    }

                                    return entry;
                                  }),
                                );
                              }
                            }}
                            renderInput={(params) => (
                              <TextField
                                {...params}
                                label="Metric"
                                fullWidth
                                value={
                                  selectedOption ||
                                  (field.value ? { id: field.value, label: field.value } : null)
                                }
                                margin="normal"
                                onChange={(event) => {
                                  const newValue = event.target.value as
                                    | string
                                    | {
                                        id: string;
                                        label: string;
                                      };
                                  if (typeof newValue === 'string') {
                                    field.onChange(newValue);
                                  } else if (newValue && typeof newValue.id === 'string') {
                                    field.onChange(newValue.id);
                                  } else {
                                    field.onChange(null);
                                  }
                                }}
                                error={!!measureErrors?.metric_id}
                                helperText={measureErrors?.metric_id?.message || ''}
                                required
                              />
                            )}
                          />
                        );
                      }}
                    />
                  </Grid>

                  {/* Description Field */}
                  <Grid item md={4}>
                    <Controller
                      name={`description`}
                      control={measurementsControl}
                      render={({ field }) => (
                        <TextField
                          {...field}
                          fullWidth
                          label="Description"
                          error={!!measureErrors?.description}
                          helperText={measureErrors?.description?.message}
                        />
                      )}
                    />
                  </Grid>

                  {/* Location Field */}
                  <Grid item md={4}>
                    <Controller
                      name={`location_id`}
                      control={measurementsControl}
                      render={({ field }) => (
                        <Autocomplete
                          {...field}
                          options={locationsListOption}
                          getOptionLabel={(option) => option.label || ''}
                          value={
                            locationsListOption.find(
                              (item) => item.id.toString() === field.value?.toString(),
                            ) ?? null
                          }
                          onBlur={field.onBlur}
                          onChange={(_, value) => {
                            if (value?.id) {
                              setMeasureValue('location_id', Number(value?.id ?? null));
                              measureClearError('location_id');
                            }
                          }}
                          renderInput={(params) => (
                            <TextField
                              {...params}
                              label="Location"
                              error={!!measureErrors?.location_id}
                              helperText={measureErrors?.location_id?.message}
                            />
                          )}
                        />
                      )}
                    />
                  </Grid>

                  {/* DataSource Field */}
                  <Grid item md={4}>
                    <Controller
                      name={`datasource_id`}
                      control={measurementsControl}
                      render={({ field }) => (
                        <Autocomplete
                          {...field}
                          disabled={
                            measurementGetValues('metric_id') != null &&
                            existingMeasurementMetrics.includes(
                              measurementGetValues('metric_id')!.toString(),
                            )
                          }
                          options={datasourceOptions}
                          getOptionLabel={(option) => option.label || ''}
                          value={
                            datasourceOptions.find((item) => item.id === field.value?.toString()) ??
                            null
                          }
                          onBlur={field.onBlur}
                          onChange={(_, value) => {
                            setMeasureValue('datasource_id', Number(value?.id ?? null));
                            measureClearError('datasource_id');
                          }}
                          renderInput={(params) => (
                            <TextField
                              {...params}
                              label="Datasource"
                              error={!!measureErrors?.datasource_id}
                              helperText={measureErrors?.datasource_id?.message}
                            />
                          )}
                        />
                      )}
                    />
                  </Grid>

                  {/* Meter Factor Field */}
                  <Grid item md={4}>
                    <Controller
                      name={`meter_factor`}
                      control={measurementsControl}
                      render={({ field }) => (
                        <TextField
                          {...field}
                          fullWidth
                          label="Meter Factor"
                          error={!!measureErrors?.meter_factor}
                          helperText={measureErrors?.meter_factor?.message}
                          InputLabelProps={{ shrink: true }}
                        />
                      )}
                    />
                  </Grid>
                </Grid>

                <Button type="submit" sx={{ mt: 2 }} variant="contained" onClick={handleMeasure}>
                  {currentMeausreIndex === -1 ? 'Add' : 'Update'}
                </Button>
                <Button
                  type="reset"
                  sx={{ mt: 2, ml: 3 }}
                  variant="outlined"
                  onClick={() => {
                    setCurrentMeasureIndex(-1);
                    resetMeasure();
                  }}
                >
                  Cancel
                </Button>
              </form>
            </Box>
            <Card sx={{ p: 2 }}>
              <TableContainer>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell sx={{ padding: '4px 8px', maxWidth: 50 }}>ID</TableCell>
                      <TableCell sx={{ padding: '4px 8px', width: '200px', minHeight: '80px' }}>
                        Type
                      </TableCell>
                      <TableCell sx={{ padding: '4px 8px', width: '200px', minHeight: '80px' }}>
                        Data Type
                      </TableCell>
                      <TableCell sx={{ padding: '4px 8px', width: '200px', minHeight: '80px' }}>
                        Value Type
                      </TableCell>
                      <TableCell sx={{ padding: '4px 8px', width: '200px', minHeight: '80px' }}>
                        Metric
                      </TableCell>
                      <TableCell sx={{ padding: '4px 8px', width: '200px', minHeight: '80px' }}>
                        Description
                      </TableCell>
                      <TableCell sx={{ padding: '4px 8px', width: '200px', minHeight: '80px' }}>
                        Location
                      </TableCell>
                      <TableCell sx={{ padding: '4px 8px', width: '200px' }}>Datasource</TableCell>
                      <TableCell sx={{ padding: '4px 8px', width: '200px' }}>
                        Meter Factor
                      </TableCell>
                      <TableCell sx={{ padding: '4px 8px', maxWidth: 50 }}>Actions</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {fields.map((field, index) => (
                      <TableRow key={field.id}>
                        <TableCell sx={{ padding: '4px 8px' }}>
                          <Typography variant="body1">{index + 1}</Typography>
                        </TableCell>
                        <TableCell sx={{ padding: '4px 8px' }}>
                          {/* Show the selected value for Type */}
                          <Typography variant="body1">
                            {measurementTypeListOptions.find(
                              (item) => item.id === field.type_id?.toString(),
                            )?.label || 'N/A'}
                          </Typography>
                        </TableCell>
                        <TableCell sx={{ padding: '4px 8px' }}>
                          {/* Show the selected value for Data Type */}
                          <Typography variant="body1">
                            {dataTypesListOptions.find(
                              (item) => item.id === field.data_type_id?.toString(),
                            )?.label || 'N/A'}
                          </Typography>
                        </TableCell>
                        <TableCell sx={{ padding: '4px 8px' }}>
                          {/* Show the selected value for Value Type */}
                          <Typography variant="body1">
                            {valueTypeOptions.find(
                              (item) => item.id === field.value_type_id?.toString(),
                            )?.label || 'N/A'}
                          </Typography>
                        </TableCell>
                        <TableCell sx={{ padding: '4px 8px' }}>
                          <Typography variant="body1">
                            {(() => {
                              const isCustom = isNaN(Number(field.metric_id));
                              const label = isCustom
                                ? field.metric_id || 'N/A'
                                : assetTypeMetricsListOptions.find(
                                    (item) => item.id === field.metric_id?.toString(),
                                  )?.label || 'N/A';

                              return <Typography variant="body1">{String(label)}</Typography>;
                            })()}
                          </Typography>
                        </TableCell>
                        <TableCell sx={{ padding: '4px 8px' }}>
                          {/* Show the Description */}
                          <Typography variant="body1">{field.description || 'N/A'}</Typography>
                        </TableCell>
                        <TableCell sx={{ padding: '4px 8px' }}>
                          {/* Show the Location */}
                          <Typography variant="body1">
                            {locationsListOption.find(
                              (item) => item.id.toString() === field.location_id?.toString(),
                            )?.label || 'N/A'}
                          </Typography>
                        </TableCell>
                        <TableCell sx={{ padding: '4px 8px' }}>
                          {/* Show the Data Source */}
                          <Typography variant="body1">
                            {datasourceOptions.find(
                              (item) => item.id === field.datasource_id?.toString(),
                            )?.label || 'N/A'}
                          </Typography>
                        </TableCell>
                        <TableCell sx={{ padding: '4px 8px' }}>
                          <Typography variant="body1">{field.meter_factor || 'N/A'}</Typography>
                        </TableCell>
                        <TableCell sx={{ padding: '4px 8px' }}>
                          <Box sx={{ width: '100%', display: 'flex' }}>
                            <IconButton
                              color="error"
                              onClick={() => {
                                remove(index);
                                setCalcMeasurements((prev) =>
                                  prev.filter(
                                    (calc) => calc.metric_id !== field.metric_id?.toString(),
                                  ),
                                );
                              }}
                            >
                              <DeleteIcon />
                            </IconButton>
                            <IconButton
                              color="primary"
                              onClick={() => {
                                resetMeasure();
                                setCurrentMeasureIndex(index);
                                setMeasureValue('type_id', field.type_id);
                                setMeasureValue('data_type_id', field.data_type_id);
                                setMeasureValue('value_type_id', field.value_type_id);
                                setMeasureValue('metric_id', field.metric_id);
                                setMeasureValue('description', field.description);
                                setMeasureValue('location_id', field.location_id);
                                setMeasureValue('datasource_id', field.datasource_id);
                                setMeasureValue('meter_factor', field.meter_factor);
                                const measurementID = data?.measurements.at(index)?.id;
                                setMeasureValue('id', measurementID ?? undefined);
                              }}
                            >
                              <EditIcon />
                            </IconButton>
                          </Box>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            </Card>
            {errors.measurements?.message && (
              <Alert severity="error">{errors.measurements?.message}</Alert>
            )}
            {hasDuplicates() ? <Alert severity="error">Metric can not be duplicate</Alert> : null}
            <Box
              sx={{
                display: 'flex',
                justifyContent: 'space-between',
                position: 'sticky',
                bottom: 0,
                backgroundColor: 'white',
                zIndex: 1000,
                padding: '10px',
              }}
            >
              {/*  sx={{ position: 'sticky', top: 0, backgroundColor: 'white', zIndex: 1000, padding: '10px' }} */}
              <Button onClick={handleBack} variant="outlined">
                Back
              </Button>
              <Button
                type="submit"
                variant="contained"
                disabled={isLoading || hasDuplicates() || fields.length === 0}
              >
                {calculatedMeasures.length > 0 ? 'Save & Next' : 'Submit'}
              </Button>
            </Box>
          </Box>
        )}
      </form>
      {activeStep === 2 && (
        <>
          <Typography variant="h6" my={2}>
            Calculated Measurements Mapping
          </Typography>
          <TableContainer component={Paper}>
            <Table sx={{ minWidth: 650 }} aria-label="simple table">
              <TableHead>
                <TableRow>
                  <TableCell align="center">Type </TableCell>
                  <TableCell align="center">Data Type</TableCell>
                  <TableCell align="center">Value Type</TableCell>
                  <TableCell align="center">Metric</TableCell>
                  <TableCell align="center">Location</TableCell>
                  <TableCell align="center">Data Source</TableCell>
                  <TableCell align="center">Description</TableCell>
                  <TableCell align="center">Meter Factor</TableCell>
                  <TableCell align="center">Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {fields.map((row, i) => {
                  if (
                    calculationSource === undefined ||
                    row.datasource_id !== calculationSource?.id
                  )
                    return null;
                  return (
                    <TableRow
                      key={i} // Use unique `id` if available from useFieldArray
                      sx={{ '&:last-child td, &:last-child th': { border: 0 } }}
                    >
                      <TableCell component="th" scope="row" align="center">
                        {measurementTypeListOptions.find(
                          (measure) => measure.id === row.type_id?.toString(),
                        )?.label ?? 'N/A'}
                      </TableCell>
                      <TableCell component="th" scope="row" align="center">
                        {dataTypesListOptions.find(
                          (dataType) => dataType.id === row.data_type_id?.toString(),
                        )?.label ?? 'N/A'}
                      </TableCell>
                      <TableCell component="th" scope="row" align="center">
                        {valueTypeOptions.find(
                          (valueType) => valueType.id === row.value_type_id?.toString(),
                        )?.label ?? 'N/A'}
                      </TableCell>
                      <TableCell component="th" scope="row" align="center">
                        {(() => {
                          const metric = row.metric_id;
                          if (!metric) return null;
                          const metricString = metric.toString();
                          const foundMetric = assetTypeMetricsListOptions.find(
                            (asset) => asset.id === metricString,
                          );
                          return foundMetric ? foundMetric.label : metricString;
                        })()}
                      </TableCell>
                      <TableCell component="th" scope="row" align="center">
                        {locationsListOption.find(
                          (location) => location.id === row.location_id?.toString(),
                        )?.label ?? 'N/A'}
                      </TableCell>
                      <TableCell component="th" scope="row" align="center">
                        {datasourceOptions.find(
                          (source) => source.id === row.datasource_id?.toString(),
                        )?.label ?? 'N/A'}
                      </TableCell>
                      <TableCell component="th" scope="row" align="center">
                        {row.description ?? 'N/A'}
                      </TableCell>
                      <TableCell component="th" scope="row" align="center">
                        {row.meter_factor ?? 'N/A'}
                      </TableCell>
                      <TableCell component="th" scope="row" align="center">
                        <Box
                          display={'flex'}
                          justifyContent={'space-around'}
                          sx={{ cursor: 'pointer' }}
                        >
                          <EditIcon
                            onClick={() => {
                              if (currentCalcMeasureIndex !== i) {
                                setCurrentCalcMeasureIndex(i);
                                const findCalcMeasure = calcMeasurements.find(
                                  (calc) => calc.metric_id === row.metric_id?.toString(),
                                );
                                // console.log(findCalcMeasure, calcMeasurements);
                                if (findCalcMeasure) {
                                  calcMeasureSetValue(
                                    'expression_template_id',
                                    findCalcMeasure.calcMeasurementData.expression_template_id,
                                  );
                                  calcMeasureSetValue(
                                    'variable_inputs',
                                    findCalcMeasure.calcMeasurementData.variable_inputs,
                                  );
                                  calcMeasureSetValue(
                                    'poll_period',
                                    findCalcMeasure.calcMeasurementData.poll_period,
                                  );
                                  calcMeasureSetValue(
                                    'writeback',
                                    findCalcMeasure.calcMeasurementData.writeback,
                                  );
                                  calcMeasureSetValue(
                                    'is_persisted',
                                    findCalcMeasure.calcMeasurementData.is_persisted,
                                  );
                                } else {
                                  calcMeasureSetValue('expression_template_id', null);
                                  calcMeasureSetValue('variable_inputs', []);
                                  calcMeasureSetValue('poll_period', null);
                                  calcMeasureSetValue('writeback', false);
                                  calcMeasureSetValue('is_persisted', false);
                                }
                              } else {
                                setCurrentCalcMeasureIndex(-1);
                              }
                            }}
                          />
                        </Box>
                      </TableCell>
                    </TableRow>
                  );
                })}
              </TableBody>
            </Table>
          </TableContainer>
          {hasInvalidVariable && (
            <Alert color="error">Each variable must have a valid metric or constant value.</Alert>
          )}
          {currentCalcMeasureIndex !== -1 &&
            fields[currentCalcMeasureIndex] &&
            (() => {
              return (
                <form
                  onSubmit={calcMeasureHandleSubmit((data) => {
                    const measuremetToCalcMetric = fields[currentCalcMeasureIndex]?.metric_id;
                    if (measuremetToCalcMetric) {
                      setCalcMeasurements(
                        calcMeasurements.map((calc) => {
                          if (calc.metric_id === measuremetToCalcMetric?.toString()) {
                            return {
                              ...calc,
                              existing: false,
                              calcMeasurementData: {
                                expression_template_id: data.expression_template_id,
                                is_persisted: data.is_persisted,
                                poll_period: data.poll_period,
                                writeback: data.writeback,
                                variable_inputs: data.variable_inputs.map((vars) => ({
                                  ...vars,
                                  metric_id: vars?.metric_id,
                                  constant_value: vars?.constant_value,
                                  comment: vars?.comment,
                                })),
                              },
                            };
                          }
                          return calc;
                        }),
                      );
                      resetCalcMeasureValues();
                      setCurrentCalcMeasureIndex(-1);
                    }
                  })}
                >
                  <Box component={Card} sx={{ mt: 2, p: 3 }}>
                    <Typography variant="subtitle1" gutterBottom>
                      Editing Calculation Measurement: {currentCalcMeasureIndex + 1}
                    </Typography>

                    <Typography variant="body2" sx={{ mb: 2 }}>
                      Metric:{' '}
                      {(() => {
                        const metric = fields[currentCalcMeasureIndex]?.metric_id;
                        if (!metric) return null;
                        const metricString = metric.toString();
                        const foundMetric = assetTypeMetricsListOptions.find(
                          (asset) => asset.id === metricString,
                        );
                        return foundMetric ? foundMetric.label : metricString;
                      })()}
                    </Typography>

                    <Stack spacing={2}>
                      <Controller
                        name="expression_template_id"
                        control={calcMeasurementController}
                        render={({ field }) => {
                          const selectedTemplate =
                            expressionTemplates?.items?.find((item) => item.id === field.value) ??
                            null;
                          return (
                            <Autocomplete
                              fullWidth
                              loading={fetchingExpressionTemplates}
                              disabled={
                                calcMeasurements.find(
                                  (calc) =>
                                    calc.metric_id ===
                                    fields[currentCalcMeasureIndex]?.metric_id?.toString(),
                                )?.existing ?? false
                              }
                              options={expressionTemplates?.items || []}
                              getOptionLabel={(option) => option.name || ''}
                              value={selectedTemplate}
                              onChange={(_, value) => field.onChange(value?.id ?? null)}
                              renderInput={(params) => (
                                <TextField
                                  {...params}
                                  label="Expression Template"
                                  error={!!calcMeasureError.expression_template_id}
                                  helperText={calcMeasureError.expression_template_id?.message}
                                  variant="outlined"
                                />
                              )}
                            />
                          );
                        }}
                      />

                      <ExpressionTemplateDetails selectedTemplate={expression} />
                      <Box sx={{ my: 2 }}>
                        {variableInputs && variableInputs.length > 0 && (
                          <Box>
                            <Typography variant="subtitle1" sx={{ mb: 2 }}>
                              Variable Inputs
                            </Typography>

                            {variableFields.map((item, index) => (
                              <Box
                                key={item.id}
                                sx={{
                                  mb: 3,
                                  p: 2,
                                  border: '1px solid #ccc',
                                  borderRadius: 1,
                                }}
                              >
                                <Typography variant="body2" sx={{ mb: 2 }}>
                                  Variable: <strong>{item.variable}</strong>
                                </Typography>

                                <Grid container spacing={2}>
                                  <Grid item xs={12} md={4}>
                                    <Controller
                                      name={`variable_inputs.${index}.type`}
                                      control={calcMeasurementController}
                                      render={({ field }) => (
                                        <FormControl
                                          component="fieldset"
                                          fullWidth
                                          error={!!calcMeasureError?.variable_inputs?.[index]?.type}
                                          onChange={(event) => {
                                            field.onChange(event);
                                            calcMeasureSetValue(
                                              `variable_inputs.${index}.metric_id`,
                                              null,
                                            );
                                            calcMeasureSetValue(
                                              `variable_inputs.${index}.constant_value`,
                                              null,
                                            );
                                          }}
                                        >
                                          <FormLabel component="legend">Type</FormLabel>
                                          <RadioGroup row {...field}>
                                            <FormControlLabel
                                              value="measurement"
                                              control={<Radio />}
                                              label="Measurement"
                                            />
                                            <FormControlLabel
                                              value="constant"
                                              control={<Radio />}
                                              label="Constant"
                                            />
                                          </RadioGroup>
                                          {calcMeasureError?.variable_inputs?.[index]?.type && (
                                            <FormHelperText>
                                              {calcMeasureError.variable_inputs[
                                                index
                                              ]?.type?.toString()}
                                            </FormHelperText>
                                          )}
                                        </FormControl>
                                      )}
                                    />
                                  </Grid>

                                  {/* METRIC ID or CONSTANT VALUE */}
                                  <Grid item xs={12} md={4}>
                                    {variableInputs[index]?.type === 'measurement' ? (
                                      <Controller
                                        name={`variable_inputs.${index}.metric_id`}
                                        control={calcMeasurementController}
                                        render={({ field }) => {
                                          const options = assetTypeMetricsListOptions.filter(
                                            (metric) =>
                                              !calculatedMeasures.some(
                                                (cm) => cm.metric_id?.toString() === metric.id,
                                              ) &&
                                              fields.some(
                                                (cm) => cm.metric_id?.toString() === metric.id,
                                              ),
                                          );
                                          const selectedOption =
                                            options.find(
                                              (opt) =>
                                                opt.id?.toString() === field.value?.toString(),
                                            ) ?? null;
                                          return (
                                            <Autocomplete
                                              fullWidth
                                              options={options}
                                              getOptionLabel={(option) => option.label}
                                              isOptionEqualToValue={(option, value) =>
                                                option.id === value?.id
                                              }
                                              value={selectedOption}
                                              onChange={(_, newValue) =>
                                                field.onChange(newValue?.id ?? null)
                                              }
                                              renderInput={(params) => (
                                                <TextField
                                                  {...params}
                                                  label="Metric"
                                                  error={
                                                    !!calcMeasureError?.variable_inputs?.[index]
                                                      ?.metric_id
                                                  }
                                                  helperText={
                                                    calcMeasureError?.variable_inputs?.[index]
                                                      ?.metric_id?.message
                                                  }
                                                />
                                              )}
                                            />
                                          );
                                        }}
                                      />
                                    ) : (
                                      <Controller
                                        name={`variable_inputs.${index}.constant_value`}
                                        control={calcMeasurementController}
                                        render={({ field }) => (
                                          <TextField
                                            {...field}
                                            label="Constant Value"
                                            fullWidth
                                            error={
                                              !!calcMeasureError?.variable_inputs?.[index]
                                                ?.constant_value
                                            }
                                            helperText={
                                              calcMeasureError?.variable_inputs?.[index]
                                                ?.constant_value?.message
                                            }
                                          />
                                        )}
                                      />
                                    )}
                                  </Grid>

                                  {/* COMMENT */}
                                  <Grid item xs={12} md={4}>
                                    <Controller
                                      name={`variable_inputs.${index}.comment`}
                                      control={calcMeasurementController}
                                      render={({ field }) => (
                                        <TextField
                                          {...field}
                                          label="Comment"
                                          fullWidth
                                          error={
                                            !!calcMeasureError?.variable_inputs?.[index]?.comment
                                          }
                                          helperText={
                                            calcMeasureError?.variable_inputs?.[index]?.comment
                                              ?.message
                                          }
                                        />
                                      )}
                                    />
                                  </Grid>
                                </Grid>
                              </Box>
                            ))}
                          </Box>
                        )}
                      </Box>

                      <Controller
                        name="is_persisted"
                        control={calcMeasurementController}
                        render={({ field }) => (
                          <FormControlLabel
                            control={
                              <Checkbox {...field} checked={!!field.value} color="primary" />
                            }
                            label="Is Persisted"
                          />
                        )}
                      />
                      {calcMeasureWatch('is_persisted') ? (
                        <>
                          <Controller
                            name="writeback"
                            control={calcMeasurementController}
                            render={({ field }) => (
                              <FormControlLabel
                                control={
                                  <Checkbox {...field} checked={!!field.value} color="primary" />
                                }
                                label="Writeback"
                              />
                            )}
                          />

                          <Controller
                            name="poll_period"
                            control={calcMeasurementController}
                            render={({ field }) => (
                              <Autocomplete<poll_period>
                                fullWidth
                                options={(pollPeriods?.items || []) as poll_period[]}
                                getOptionLabel={(option) => String(option.value)}
                                isOptionEqualToValue={(option, value) => option.id === value?.id}
                                // eslint-disable-next-line @typescript-eslint/ban-ts-comment
                                //@ts-ignore
                                value={field.value ?? null}
                                onChange={(_, value) => field.onChange(value)}
                                renderInput={(params) => (
                                  <TextField
                                    {...params}
                                    label="Poll Period"
                                    error={!!calcMeasureError.poll_period}
                                    helperText={calcMeasureError.poll_period?.message}
                                    variant="outlined"
                                  />
                                )}
                              />
                            )}
                          />
                        </>
                      ) : null}
                    </Stack>
                    <Button type="submit" color="primary" variant="contained" sx={{ mt: 2 }}>
                      Save
                    </Button>
                    <Button
                      type="reset"
                      color="primary"
                      variant="outlined"
                      sx={{ mt: 2, ml: 1.5 }}
                      onClick={() => {
                        resetCalcMeasureValues();
                        setCurrentCalcMeasureIndex(-1);
                      }}
                    >
                      cancel
                    </Button>
                  </Box>
                </form>
              );
            })()}
          <Box
            sx={{
              display: 'flex',
              justifyContent: 'space-between',
              position: 'sticky',
              bottom: 0,
              backgroundColor: 'white',
              zIndex: 1000,
              padding: '10px',
            }}
          >
            <Button onClick={handleBack} variant="outlined">
              Back
            </Button>
            <Button
              onClick={async () => {
                const stringMetrics = fields.filter((measure) => isNaN(Number(measure.metric_id)));
                await updateAssetTemplateMulti({
                  assetTypeId: formValues('asset_type_id').toString(),
                  assetTemplateId:
                    assetTemplate !== undefined ? assetTemplate?.toString() ?? '' : '',

                  updateAssetTemplate: {
                    metrics: stringMetrics
                      .map((metric) => metric.metric_id?.toString())
                      .filter((id): id is string => id !== undefined),
                    expressionInstance: calcMeasurements.map((calc) => ({
                      templateId: calc.calcMeasurementData.expression_template_id ?? null,
                      ispersisted: calc.calcMeasurementData.is_persisted ?? false,
                      pollPeriod: calc.calcMeasurementData.poll_period?.id ?? undefined,
                      metricId: calc.metric_id?.toString() ?? '',
                      iswriteback: calc.calcMeasurementData.writeback ?? false,
                      variables: calc.calcMeasurementData.variable_inputs.map((vars) => ({
                        inputLabel: vars.variable,
                        metric: vars.metric_id?.toString() ?? undefined,
                        constantType: vars.constant_value ? 'number' : undefined,
                        constantValue: vars.constant_value?.toString() ?? undefined, // Ensure constantValue is a string
                        comment: vars.comment ?? undefined,
                      })),
                    })),
                    updateAssetTemplate: {
                      ...formValues(),
                      measurements:
                        formValues('measurements')?.map((measure) => ({
                          type_id: Number(measure.type_id),
                          data_type_id: Number(measure.data_type_id),
                          value_type_id: Number(measure.value_type_id),
                          metric_id: (measure?.metric_id ?? undefined) as
                            | string
                            | number
                            | undefined,
                          description: measure.description ?? '',
                          location_id: measure.location_id ?? null,
                          datasource_id: measure.datasource_id ?? null,
                          meter_factor: measure.meter_factor ?? null,
                          id: measure.id ?? undefined,
                        })) ?? [],
                    },
                  },
                });
              }}
              variant="contained"
              disabled={isLoading || hasInvalidVariable || fields.length === 0}
            >
              Save
            </Button>
          </Box>
        </>
      )}
      {(isError || isSuccess) && (
        <Alert
          severity={isSuccess ? 'success' : 'error'} // Use 'success' if successful, 'error' otherwise
          sx={{ marginTop: 2 }}
        >
          {isSuccess ? 'Asset template updated successfully!' : 'Failed to update asset template.'}
        </Alert>
      )}
    </Box>
  );
};

export default EditAssetTemplateForm;
