import { Box, Button, Stack, Typography } from '@mui/material';
import { useDispatch, useSelector } from 'react-redux';
import useGetAssetToMeasureData from '~/hooks/useGetAssetToMeasureData';
import { getActiveCustomer } from '~/redux/selectors/customerSelectors';
import { getIsUserLoggedIn } from '~/redux/selectors/dashboardSelectors';
import { dashboardSlice } from '~/redux/slices/dashboardSlice';
import { AssetDoDetails } from '~/types/asset';
import AssetToTemplateForm from './AssetToTemplateForm/AssetToTemplateForm';

const AssetToTemplate = () => {
  const dispatch = useDispatch();
  const activeCustomer = useSelector(getActiveCustomer);
  const loggedInuser = useSelector(getIsUserLoggedIn);
  const {
    asset,
    assetTypeListData,
    isAssetDataLoading,
    isAssetLoading,
    fetchingMeasurements,
    measurements,
    assetTypesList,
  } = useGetAssetToMeasureData();
  if (!activeCustomer) return <></>;
  if (isAssetDataLoading || isAssetLoading || fetchingMeasurements) return <>Loading</>;
  const assetTypeOption = assetTypesList.find(
    (assetTypeOption) => assetTypeOption.value === (asset as AssetDoDetails)?.type_id,
  );

  return (
    <Box sx={{ display: 'flex', flexDirection: 'column', pl: 2 }}>
      <Stack direction={'row'}>
        <Box display={'flex'} justifyContent={'space-between'} width={'100%'}>
          <Typography variant="h4">Asset Template from Asset</Typography>
          {!loggedInuser?.scoped_roles?.find((role) => role.role === 'USER')?.role ? (
            <Box>
              <Button
                sx={{ ml: 'auto' }}
                onClick={() => {
                  dispatch(dashboardSlice.actions.selectMainPanel('chart'));
                }}
              >
                Back
              </Button>
            </Box>
          ) : null}
        </Box>
      </Stack>
      {/* <Typography>Asset : {asset?.tag}</Typography> */}
      <Box sx={{ mt: 2 }}>
        <AssetToTemplateForm
          asset={asset as AssetDoDetails}
          assetTypesList={assetTypesList}
          measurements={measurements}
        />
      </Box>
    </Box>
  );
};

export default AssetToTemplate;
