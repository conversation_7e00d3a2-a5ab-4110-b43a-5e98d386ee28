// tests/api.test.js
const { test, expect, request } = require('@playwright/test');

test.describe('API Test Suite', () => {
  test('PATCH /customers/1/assets/2 updates asset description successfully', async ({
    request,
  }) => {
    // Set headers
    const headers = {
      'BE-CsrfToken': 'j+skqGHHzM1cV7xe20cBjhbSYfnYc/OnpsErg9D/akw=',
      'Content-Type': 'application/json',
      Cookie:
        'BE-AccessToken=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VybmFtZSI6InRlc3QiLCJzdWIiOjIsInJvbGVzIjp7IkFETUlOIjpbMTI4LDgsMTIxLDExOSwxMDYsMTE4LDEwOCw4NiwxMTEsMTEyLDg1LDEwNyw4NCwxMTMsMTE0LDExNywxMjAsMTI3LDEyMywxMjYsMTI0LDEwOSwxMjUsMTIyLDExMCw5LDgyXSwiVVNFUiI6WzEyOCw4LDEyMSwxMTksMTA2LDExOCwxMDgsODYsMTExLDExMiw4NSwxMDcsODQsMTEzLDExNCwxMTcsMTIwLDEyNywxMjMsMTI2LDEyNCwxMDksMTI1LDEyMiwxMTAsOSw4Ml0sIlBPV0VSX1VTRVIiOlsxMjgsOCwxMjEsMTE5LDEwNiwxMTgsMTA4LDg2LDExMSwxMTIsODUsMTA3LDg0LDExMywxMTQsMTE3LDEyMCwxMjcsMTIzLDEyNiwxMjQsMTA5LDEyNSwxMjIsMTEwLDksODJdfSwiaWF0IjoxNzMxNTYzNDk4LCJleHAiOjE3MzE1NzA2OTh9.wTXeUK_c5af0anV03rbK7tfOTaCntKwTJtVS89Cv_fI; BE-CSRFToken=j%2BskqGHHzM1cV7xe20cBjhbSYfnYc%2FOnpsErg9D%2Fakw%3D',
    };

    // Define request body
    const body = {
      description: 'dubidubi',
    };

    // Make PATCH request
    const response = await request.patch('https://test.brompton.ai/apiv0/customers/1/assets/2', {
      headers: headers,
      data: body,
    });

    // Check response status
    expect(response.status()).toBe(200);

    // Verify response body if needed
    const responseBody = await response.json();
    console.log(responseBody);

    // Perform assertions to confirm the description was updated
    expect(responseBody).toHaveProperty('description', 'dubidubi');
  });
});
