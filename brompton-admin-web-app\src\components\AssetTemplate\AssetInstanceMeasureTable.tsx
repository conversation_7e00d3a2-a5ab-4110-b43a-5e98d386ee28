import DeleteIcon from '@mui/icons-material/Delete';
import EditIcon from '@mui/icons-material/Edit';
import { Box } from '@mui/material';
import Paper from '@mui/material/Paper';
import Table from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';
import TableCell from '@mui/material/TableCell';
import TableContainer from '@mui/material/TableContainer';
import TableHead from '@mui/material/TableHead';
import TableRow from '@mui/material/TableRow';
import { Dispatch, SetStateAction } from 'react';
import { UseFormSetValue } from 'react-hook-form';
import { measurementSchemaData, measurementSchemaDataDTO } from '~/measurements/domain/types';
import { useGetUnitOMeasureWithMeasureTypeQuery } from '~/redux/api/measuresApi';

type MeasureTableProps = {
  currentMeausreIndex: number;
  isEdit: boolean;
  EditForm: React.ReactNode;
  setCurrentMeasureIndex: Dispatch<SetStateAction<number>>;
  fields: measurementSchemaData[] | measurementSchemaDataDTO[];
  valueTypeOptions: {
    id: string;
    label: string;
  }[];
  datasourceOptions: {
    id: string;
    label: string;
  }[];
  locationsListOption: {
    id: string;
    label: string;
  }[];
  dataTypesListOptions: {
    id: string;
    label: string;
  }[];
  measurementTypeListOptions: {
    id: string;
    label: string;
  }[];
  assetTypeMetricsListOptions: {
    id: string;
    label: string;
  }[];
  remove: (index: number) => void;
  setMeasureValue?: UseFormSetValue<measurementSchemaData | measurementSchemaDataDTO>;
  isInstance?: boolean;
  units_group_id: number;
};
const TableRowHelper = ({
  EditForm,
  currentMeausreIndex,
  isEdit,
  fields,
  valueTypeOptions,
  datasourceOptions,
  locationsListOption,
  measurementTypeListOptions,
  dataTypesListOptions,
  setCurrentMeasureIndex,
  assetTypeMetricsListOptions,
  remove,
  setMeasureValue,
  isInstance,
  units_group_id,
  row,
  i,
}: {
  currentMeausreIndex: number;
  isEdit: boolean;
  EditForm: React.ReactNode;
  setCurrentMeasureIndex: Dispatch<SetStateAction<number>>;
  units_group_id: number;
  fields: measurementSchemaData | measurementSchemaDataDTO;
  i: number;
  setMeasureValue?: UseFormSetValue<measurementSchemaData | measurementSchemaDataDTO>;
  isInstance: boolean;
  valueTypeOptions: {
    id: string;
    label: string;
  }[];
  datasourceOptions: {
    id: string;
    label: string;
  }[];
  locationsListOption: {
    id: string;
    label: string;
  }[];
  dataTypesListOptions: {
    id: string;
    label: string;
  }[];
  measurementTypeListOptions: {
    id: string;
    label: string;
  }[];
  assetTypeMetricsListOptions: {
    id: string;
    label: string;
  }[];
  remove: (index: number) => void;
  row: measurementSchemaData | measurementSchemaDataDTO;
}) => {
  const typeId = measurementTypeListOptions.find(
    (measure) => measure.id === row.type_id?.toString(),
  );
  const { data } = useGetUnitOMeasureWithMeasureTypeQuery(
    {
      measurementType: Number(typeId?.id) ?? 0,
      unitOfGroup: units_group_id,
    },
    {
      skip:
        units_group_id === 0 ||
        typeId === undefined ||
        typeId.id === undefined ||
        units_group_id === undefined,
    },
  );

  return (
    <>
      <TableRow key={i} sx={{ '&:last-child td, &:last-child th': { border: 0 } }}>
        <TableCell component="th" scope="row" align="center">
          {i + 1}
        </TableCell>
        {isInstance ? (
          <TableCell component="th" scope="row" align="center">
            {isInstance && 'tag' in row ? row.tag : 'N/A'}
          </TableCell>
        ) : null}

        <TableCell component="th" scope="row" align="center">
          {measurementTypeListOptions.find((measure) => measure.id === row.type_id?.toString())
            ?.label ?? 'N/A'}
        </TableCell>
        <TableCell component="th" scope="row" align="center">
          {dataTypesListOptions.find((dataType) => dataType.id === row.data_type_id?.toString())
            ?.label ?? 'N/A'}
        </TableCell>
        <TableCell component="th" scope="row" align="center">
          {valueTypeOptions.find((valueType) => valueType.id === row.value_type_id?.toString())
            ?.label ?? 'N/A'}
        </TableCell>
        <TableCell component="th" scope="row" align="center">
          {
            assetTypeMetricsListOptions.find((asset) => asset.id === row.metric_id?.toString())
              ?.label
          }
        </TableCell>
        <TableCell component="th" scope="row" align="center">
          {
            // eslint-disable-next-line @typescript-eslint/ban-ts-comment
            //@ts-ignore
            data?.items.find((unit) => unit.id.toString() === row.unit_of_measure_id?.toString())
              ?.name ?? 'N/A'
          }
        </TableCell>
        {/* <TableCell component="th" scope="row" align="center">
          {row.description ?? 'N/A'}
        </TableCell> */}
        <TableCell component="th" scope="row" align="center">
          {row.meter_factor ?? 'N/A'}
        </TableCell>
        <TableCell component="th" scope="row" align="center">
          <Box display={'flex'} justifyContent={'space-around'}>
            <EditIcon
              onClick={() => {
                setCurrentMeasureIndex(i);
                if (row.type_id !== null) setMeasureValue?.('type_id', row.type_id);
                if (row.data_type_id !== null) setMeasureValue?.('data_type_id', row.data_type_id);
                if (row.value_type_id !== null)
                  setMeasureValue?.('value_type_id', row.value_type_id);
                if (row.metric_id !== null) setMeasureValue?.('metric_id', row.metric_id);
                if (row.description !== null) setMeasureValue?.('description', row.description);
                if (row.description === null) setMeasureValue?.('description', '');
                if (row.location_id !== null) setMeasureValue?.('location_id', row.location_id);
                if (row.datasource_id !== null)
                  setMeasureValue?.('datasource_id', row.datasource_id);
                if (row.datasource_id === null) setMeasureValue?.('datasource_id', undefined);
                if (row.meter_factor !== null) setMeasureValue?.('meter_factor', row.meter_factor);
                if ((row as measurementSchemaDataDTO).unit_of_measure_id !== null) {
                  setMeasureValue?.(
                    'unit_of_measure_id',
                    (row as measurementSchemaDataDTO).unit_of_measure_id,
                  );
                }
                if ((row as measurementSchemaDataDTO).tag !== null)
                  setMeasureValue?.('tag', (row as measurementSchemaDataDTO).tag);
                // if (row.unit_of_measure_id !== null) {
                //   setMeasureValue?.(
                //     'unit_of_measure_id',
                //     (row as measurementSchemaDataDTO).unit_of_measure_id,
                //   );
                // }
                // setMeasureValue?.('tag', (row as measurementSchemaDataDTO).tag);
              }}
              sx={{ cursor: 'pointer' }}
            />
            {!isInstance ? (
              <DeleteIcon color="error" onClick={() => remove(i)} sx={{ cursor: 'pointer' }} />
            ) : null}
          </Box>
        </TableCell>
      </TableRow>

      {isEdit && i === currentMeausreIndex ? (
        <TableRow key={i} sx={{ '&:last-child td, &:last-child th': { border: 0 } }}>
          <TableCell colSpan={10}>
            <Box>{EditForm}</Box>
          </TableCell>
        </TableRow>
      ) : null}
    </>
  );
};
const AssetInstanceMeasureTable = ({
  EditForm,
  isEdit,
  currentMeausreIndex,
  fields,
  valueTypeOptions,
  datasourceOptions,
  locationsListOption,
  measurementTypeListOptions,
  dataTypesListOptions,
  setCurrentMeasureIndex,
  assetTypeMetricsListOptions,
  remove,
  setMeasureValue,
  isInstance,
  units_group_id,
}: MeasureTableProps) => {
  return (
    <>
      <TableContainer component={Paper}>
        <Table sx={{ minWidth: 650 }} aria-label="simple table">
          <TableHead>
            <TableRow>
              <TableCell align="center">Measure No.</TableCell>
              {isInstance ? <TableCell align="center">Tag </TableCell> : null}
              <TableCell align="center">Type </TableCell>
              <TableCell align="center">Data Type</TableCell>
              <TableCell align="center">Value Type</TableCell>
              <TableCell align="center">Metric</TableCell>
              <TableCell align="center">Unit of Measure</TableCell>
              {/* <TableCell align="center">Description</TableCell> */}
              <TableCell align="center">Meter Factor</TableCell>
              <TableCell align="center">Actions</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {fields.map((row, i) => (
              // <TableRow key={i} sx={{ '&:last-child td, &:last-child th': { border: 0 } }}>
              <TableRowHelper
                key={i}
                currentMeausreIndex={currentMeausreIndex}
                EditForm={EditForm}
                isEdit={isEdit}
                assetTypeMetricsListOptions={assetTypeMetricsListOptions}
                dataTypesListOptions={dataTypesListOptions}
                datasourceOptions={datasourceOptions}
                fields={row}
                i={i}
                isInstance={isInstance ?? false}
                locationsListOption={locationsListOption}
                measurementTypeListOptions={measurementTypeListOptions}
                remove={remove}
                row={row}
                setCurrentMeasureIndex={setCurrentMeasureIndex}
                setMeasureValue={setMeasureValue}
                units_group_id={units_group_id}
                valueTypeOptions={valueTypeOptions}
              />
              // </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>
    </>
  );
};

export default AssetInstanceMeasureTable;
