import { fireEvent, render, screen, waitFor } from '@testing-library/react';
import { Provider } from 'react-redux';
import { useHasAdminAccess } from '~/hooks/useHasAdminAccess';
import { useGetDashboardTemplatesQuery } from '~/redux/api/dashboardTemplate';
import { store } from '~/redux/store';
import DashboardTemplateTitleDialog from '../DashboardTemplateTItleDialog';

// Mock hooks
jest.mock('~/hooks/useHasAdminAccess');
jest.mock('~/redux/api/dashboardTemplate');

describe('DashboardTemplateTitleDialog', () => {
  const onClose = jest.fn();
  const onSave = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
    (useHasAdminAccess as jest.Mock).mockReturnValue({ globalAdmin: true });
    (useGetDashboardTemplatesQuery as jest.Mock).mockReturnValue({
      data: { items: [{ id: 1, customer: null }] },
    });
  });

  function renderDialog(props = {}) {
    return render(
      <Provider store={store}>
        <DashboardTemplateTitleDialog
          open={true}
          onClose={onClose}
          onSave={onSave}
          flow="save"
          initialTitle=""
          {...props}
        />
      </Provider>,
    );
  }

  it('renders dialog with Save button and title input', () => {
    renderDialog();
    expect(screen.getByLabelText(/Dashboard Title/i)).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /Save/i })).toBeInTheDocument();
  });

  it('calls onSave with correct data on submit', async () => {
    renderDialog();
    const input = screen.getByLabelText(/Dashboard Title/i);
    fireEvent.change(input, { target: { value: 'My Dashboard' } });
    fireEvent.click(screen.getByRole('button', { name: /Save/i }));

    await waitFor(() => {
      expect(onSave).toHaveBeenCalledWith(
        expect.objectContaining({
          title: 'My Dashboard',
          save_as_global_dashboard_template: false,
        }),
        'save',
      );
    });
  });

  it('calls onClose when Cancel is clicked', () => {
    renderDialog();
    fireEvent.click(screen.getByRole('button', { name: /Cancel/i }));
    expect(onClose).toHaveBeenCalled();
  });

  it('disables checkbox if not global admin', () => {
    (useHasAdminAccess as jest.Mock).mockReturnValue({ globalAdmin: false });
    renderDialog();
    expect(screen.getByLabelText(/Save as Global Dashboard Template/i)).toBeDisabled();
  });

  it('shows warning alert if not global admin and flow is update', () => {
    (useHasAdminAccess as jest.Mock).mockReturnValue({ globalAdmin: false });
    renderDialog({ flow: 'update', initialTitle: 'Existing Dashboard' });
    expect(screen.getByText(/Please log in as a global user/i)).toBeInTheDocument();
  });

  it('shows radio buttons when initialTitle is provided', () => {
    renderDialog({ flow: 'update', initialTitle: 'Existing Dashboard' });

    const radios = screen
      .getAllByLabelText(/Save As/i)
      .filter((el) => el.getAttribute('type') === 'radio');
    expect(radios.length).toBe(1);
    expect(radios[0]).toBeInTheDocument();

    const updateRadios = screen
      .getAllByLabelText(/Update/i)
      .filter((el) => el.getAttribute('type') === 'radio');
    expect(updateRadios.length).toBe(1);
    expect(updateRadios[0]).toBeInTheDocument();
  });

  it('renders Update button and label when action is update', () => {
    renderDialog({ flow: 'update', initialTitle: 'Existing Dashboard' });
    expect(screen.getByRole('button', { name: /Update/i })).toBeInTheDocument();
  });
});
