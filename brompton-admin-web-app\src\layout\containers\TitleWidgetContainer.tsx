import { Box, CircularProgress, Typography } from '@mui/material';
import { TitleWidget } from '~/types/widgets';

import { TitleSettingsDialog } from '~/components/TitleSettings/TitleSettingsDialog';
import CommonWidgetContainer from '~/components/common/CommonWidgetContainer';
import { useFetchTitleData } from '~/hooks/useFetchTitleData';

type TitleWidgetContainerProps = {
  id: string;
  settings: TitleWidget;
};
export const TitleWidgetContainer = ({ id, settings }: TitleWidgetContainerProps) => {
  const {
    data: titleData,
    isLoading: isTitleDataLoading,
    successAndFailedMeasurements,
  } = useFetchTitleData(id, settings);
  return (
    <CommonWidgetContainer
      id={id}
      widgetType="title"
      widgetName="Value"
      settings={settings}
      successAndFailedMeasurements={
        settings.valueMode === 'measurement' ? successAndFailedMeasurements : undefined
      }
      widgetContent={
        <Box
          sx={{
            height: '100%',
            display: 'flex',
            flexDirection: 'column',
            justifyContent: 'center',
          }}
        >
          {settings.title && settings.title.isVisible && (
            <Typography
              textTransform={'capitalize'}
              component={'div'}
              variant="inherit"
              sx={{
                fontSize: settings.title.fontSize + 'px',
                fontWeight: settings.title.fontWeight,
                textAlign: 'center',
                color: settings.title.color,
              }}
            >
              {settings.title.value}
            </Typography>
          )}
          <Typography
            sx={{
              fontSize: (settings.fixedFontSize ?? settings.fontSize) + 'px',
              fontWeight: settings.fixedFontWeight ?? settings.fontWeight,
              textAlign: 'center',
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              color: settings.fixedColor ?? '#000000',
              cursor: settings.dashboard !== null && settings.dashboard?.id ? 'pointer' : undefined,
            }}
          >
            {isTitleDataLoading ? (
              <CircularProgress size={24} />
            ) : settings.valueMode === 'measurement' ? (
              titleData?.value !== undefined ? (
                titleData?.value +
                (settings.showUnit && titleData?.unit ? ` ${titleData.unit}` : '--')
              ) : (
                '--'
              )
            ) : (
              // Default to fixed value if valueMode is undefined or 'fixed'
              settings.fixedValue || ''
            )}
          </Typography>
        </Box>
      }
      settingsDialog={TitleSettingsDialog}
    />
  );
};
