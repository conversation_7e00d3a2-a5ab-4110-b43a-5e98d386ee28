import { ThunkDispatch } from '@reduxjs/toolkit';
import { useDispatch, useSelector } from 'react-redux';
import { assetsApi } from '~/redux/api/assetsApi';
import { useGetMeasuresByCustomersMutation } from '~/redux/api/measuresApi';
import { getActiveCustomer } from '~/redux/selectors/customerSelectors';
import {
  getCurrentDashboardId,
  getCurrentDashboardTitle,
  getSelectedAssetFromWidgets,
  getSelectedMeasurementsFromWidgets,
  selectDashboardState,
} from '~/redux/selectors/dashboardSelectors';
import { RootState } from '~/redux/store';
type topPanelHelperProps = {
  showSuccessAlert: (message: string) => void;
  showErrorAlert: (message: string) => void;
};
const useTopPanelHelper = ({ showSuccessAlert, showErrorAlert }: topPanelHelperProps) => {
  const dispatch = useDispatch<ThunkDispatch<RootState, any, any>>();

  const activeCustomer = useSelector(getActiveCustomer);
  const currentDashboardId = useSelector(getCurrentDashboardId);
  const currentDashboardTitle = useSelector(getCurrentDashboardTitle);
  const dashboardState = useSelector(selectDashboardState);
  const [getMeasuresByCustomer] = useGetMeasuresByCustomersMutation();
  const selectedAssetFromWidgets = useSelector(getSelectedAssetFromWidgets);
  const selectedMeasurementsFromWidgets = useSelector(getSelectedMeasurementsFromWidgets);

  const exportDashboard = async () => {
    try {
      // Fetch assets
      const {
        data: assetsData,
        isSuccess: isAssetDataSuccess,
        isError: isAssetError,
      } = await dispatch(
        assetsApi.endpoints.getMultiAsset.initiate({
          customerId: activeCustomer?.id ?? 0,
          ids: selectedAssetFromWidgets.map(Number),
        }),
      );

      if (isAssetError || !isAssetDataSuccess) {
        showErrorAlert('Error loading asset data');
        return;
      }

      // Fetch measurements
      const measureResponse = await getMeasuresByCustomer({
        customerId: activeCustomer?.id ?? 0,
        measurements: selectedMeasurementsFromWidgets.map(Number),
      });

      if ('error' in measureResponse) {
        showErrorAlert('Error fetching measurement data');
        return;
      }

      const measuresData = measureResponse.data;

      // Construct export data
      const dashboardData = {
        metaInfo: {
          customer: { ...activeCustomer },
        },
        entityInfo: {
          entityType: 'dashboard',
          version: 1,
          entityId: currentDashboardId,
          entityName: currentDashboardTitle,
          entityData: {
            id: currentDashboardId,
            title: currentDashboardTitle,
            data: dashboardState,
          },
        },
        relatedEntities: [
          // Add assets
          ...assetsData?.map((asset) => ({
            entityType: 'asset',
            version: 1,
            entityId: asset.id,
            entityData: {
              ...asset,
            },
          })),
          // Add measurements
          ...measuresData?.items.map((measure) => ({
            entityType: 'measurement',
            version: 1,
            entityId: measure.id,
            entityData: {
              ...measure,
            },
          })),
        ],
      };

      const jsonString = JSON.stringify(dashboardData, null, 2);
      const blob = new Blob([jsonString], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');

      a.href = url;
      a.download = `dashboard_${
        activeCustomer?.name
      }_${currentDashboardTitle}_${new Date().getTime()}.json`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);

      showSuccessAlert('Dashboard export successful');
    } catch (error) {
      console.error('Export error:', error);
      showErrorAlert('Failed to export dashboard');
    }
  };

  return {
    exportDashboard,
  };
};

export default useTopPanelHelper;
