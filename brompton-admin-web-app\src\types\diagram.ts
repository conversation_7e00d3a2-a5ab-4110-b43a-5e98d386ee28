import { dia, shapes } from '@joint/core';
import Battery from '~/components/CreateElement/Battery';
import { DraggableLabel } from '~/components/CreateElement/DraggableLabel';
import Progress from '~/components/CreateElement/Progress';
import { BarChartElement } from '~/components/JointJs/DiagramComponent/BarChartElement';
import Circle from '~/components/JointJs/DiagramComponent/Circle';
import { ConicTank } from '~/components/JointJs/DiagramComponent/ConicalTank';
import { ControlValve } from '~/components/JointJs/DiagramComponent/ControlValve';
import Ellipse from '~/components/JointJs/DiagramComponent/Ellipse';
// import { ExtendedCircle } from '~/components/JointJs/DiagramComponent/ExtendedStandard';
import { HandValve } from '~/components/JointJs/DiagramComponent/HandValve';
import LiquidTank from '~/components/JointJs/DiagramComponent/LiquidTank';
import Pipe from '~/components/JointJs/DiagramComponent/Pipe';
import Pump from '~/components/JointJs/DiagramComponent/Pump';
import Rectangle from '~/components/JointJs/DiagramComponent/Rectangle';
import { StatsChartElement } from '~/components/JointJs/DiagramComponent/StatsChartElement';
import { TrendChartElement } from '~/components/JointJs/DiagramComponent/TrendChartElement';
import { CATEGORY_E } from '~/components/JointJs/Palette/Palette';

export const commonAttrs = {
  size: { width: 120, height: 120 },
  attrs: {
    label: { text: '', fill: 'black' },
  },
};

// Mixin to add range picker functionality
export const cellNamespace = {
  ...shapes,
  Pipe,
  Pump,
  HandValve,
  ControlValve,
  LiquidTank,
  ConicTank,
  DraggableLabel,
  Battery,
  Rectangle,
  Circle,
  Ellipse,
  Progress,
  // ExtendedCircle,
  groupElement: shapes.standard.Rectangle,
  // BarChartElement,
  // StatsChartElement,
  // TrendChartElement,
};

export const shapeMap: Record<string, (title: string) => dia.Element> = {
  pump: (title) => createStandardShape(Pump, title ?? 'Pump', undefined, CATEGORY_E.DOMAIN),
  battery: (title) =>
    createStandardShape(
      Battery,
      title ?? 'Battery',
      {
        height: 170,
        width: 75,
      },
      CATEGORY_E.DOMAIN,
    ),
  handValve: (title) =>
    createStandardShape(HandValve, title ?? 'Hand Valve', undefined, CATEGORY_E.DOMAIN),
  controlValve: (title) =>
    createStandardShape(ControlValve, title ?? 'Control Valve', undefined, CATEGORY_E.DOMAIN),
  liquidTank: () => createStandardShape(LiquidTank, 'Liquid Tank', undefined, CATEGORY_E.DOMAIN),
  conicalTank: () => createStandardShape(ConicTank, 'Conical Tank', undefined, CATEGORY_E.DOMAIN),
  'custom-Reactangle': (title) =>
    createStandardShape(Rectangle, 'Rectangle', undefined, CATEGORY_E.DOMAIN),
  'custom-circle': (title) => createStandardShape(Circle, 'Circle', undefined, CATEGORY_E.DOMAIN),
  'custom-Ellipse': (title) =>
    createStandardShape(Ellipse, 'Ellipse', undefined, CATEGORY_E.DOMAIN),
  label: (title) =>
    createStandardShape(
      shapes.standard.TextBlock,
      title ?? 'Label',
      { width: 150, height: 60 },
      CATEGORY_E.BASIC,
    ),
  circle: (title) =>
    createStandardShape(
      shapes.standard.Circle,
      title ?? 'Circle',
      { width: 120, height: 120 },
      CATEGORY_E.BASIC,
    ),
  image: (title) =>
    createStandardShape(
      shapes.standard.BorderedImage,
      title ?? '',
      { width: 100, height: 60 },
      CATEGORY_E.BASIC,
    ),
  rectangle: (title) =>
    createStandardShape(
      shapes.standard.Rectangle,
      title ?? 'Rectangle',
      { width: 100, height: 60 },
      CATEGORY_E.BASIC,
    ),
  ellipse: (title) =>
    createStandardShape(
      shapes.standard.Ellipse,
      title ?? 'Ellipse',
      { width: 120, height: 80 },
      CATEGORY_E.BASIC,
    ),
  cylinder: (title) =>
    createStandardShape(
      shapes.standard.Cylinder,
      title ?? 'Cylinder',
      { width: 100, height: 120 },
      CATEGORY_E.BASIC,
    ),

  barChart: (title) => BarChartElement(title ?? 'Bar Chart'),
  statsChart: (title) => StatsChartElement(title ?? 'Stats Chart'),
  trendChart: (title) => TrendChartElement(title ?? 'Trend Chart'),
  progress: (title) =>
    createStandardShape(
      Progress,
      title ?? 'Progress',
      {
        height: 150,
        width: 75,
      },
      CATEGORY_E.DOMAIN,
    ),
  compressor: (title) =>
    createStandardShape(
      shapes.standard.BorderedImage,
      title ?? 'Compressor',
      { width: 100, height: 120 },
      CATEGORY_E.GENERAL,
    ),
  flarePlain: (title) =>
    createStandardShape(
      shapes.standard.BorderedImage,
      title ?? 'Flare Plain',
      { width: 100, height: 120 },
      CATEGORY_E.GENERAL,
    ),
  gateValve: (title) =>
    createStandardShape(
      shapes.standard.BorderedImage,
      title ?? 'Gate Valve',
      { width: 100, height: 120 },
      CATEGORY_E.GENERAL,
    ),
  grayTextBox: (title) =>
    createStandardShape(
      shapes.standard.BorderedImage,
      title ?? 'Gray Text Box',
      { width: 120, height: 60 },
      CATEGORY_E.GENERAL,
    ),
  hashHut: (title) =>
    createStandardShape(
      shapes.standard.BorderedImage,
      title ?? 'Hash Hut',
      { width: 100, height: 120 },
      CATEGORY_E.GENERAL,
    ),
  hydraulicMotorValve: (title) =>
    createStandardShape(
      shapes.standard.BorderedImage,
      title ?? 'Hydraulic Motor Valve',
      { width: 100, height: 120 },
      CATEGORY_E.GENERAL,
    ),
  hydraulicMotorValveDark: (title) =>
    createStandardShape(
      shapes.standard.BorderedImage,
      title ?? 'Hydraulic Motor Valve Dark',
      { width: 100, height: 120 },
      CATEGORY_E.GENERAL,
    ),
  hydraulicMotorValveWhite: (title) =>
    createStandardShape(
      shapes.standard.BorderedImage,
      title ?? 'Hydraulic Motor Valve White',
      { width: 100, height: 120 },
      CATEGORY_E.GENERAL,
    ),
  insetFieldBordered: (title) =>
    createStandardShape(
      shapes.standard.BorderedImage,
      title ?? 'Inset Field Bordered',
      { width: 100, height: 120 },
      CATEGORY_E.GENERAL,
    ),
  outsetFieldBordered: (title) =>
    createStandardShape(
      shapes.standard.BorderedImage,
      title ?? 'Outset Field Bordered',
      { width: 100, height: 120 },
      CATEGORY_E.GENERAL,
    ),
  pidArrow: (title) =>
    createStandardShape(
      shapes.standard.BorderedImage,
      title ?? 'Pid Arrow',
      { width: 100, height: 120 },
      CATEGORY_E.GENERAL,
    ),
  pneumaticValve: (title) =>
    createStandardShape(
      shapes.standard.BorderedImage,
      title ?? 'Pneumatic Valve',
      { width: 100, height: 120 },
      CATEGORY_E.GENERAL,
    ),
  scrubber: (title) =>
    createStandardShape(
      shapes.standard.BorderedImage,
      title ?? 'Scrubber',
      { width: 100, height: 120 },
      CATEGORY_E.GENERAL,
    ),
  tank: (title) =>
    createStandardShape(
      shapes.standard.BorderedImage,
      title ?? 'Tank',
      { width: 100, height: 120 },
      CATEGORY_E.GENERAL,
    ),
  vru: (title) =>
    createStandardShape(
      shapes.standard.BorderedImage,
      title ?? 'Vru',
      { width: 100, height: 120 },
      CATEGORY_E.GENERAL,
    ),
  whiteTextBox: (title) =>
    createStandardShape(
      shapes.standard.BorderedImage,
      title ?? 'White Text Box',
      { width: 120, height: 60 },
      CATEGORY_E.GENERAL,
    ),
};

export const createStandardShape = (
  ShapeClass: any,
  label: string,
  customSize?: { width: number; height: number },
  elementType?: string,
) => {
  const size = customSize || commonAttrs.size;
  const shape = new ShapeClass({
    size,
    attrs: {
      label: { ...commonAttrs.attrs.label, text: label },
    },
    elementType,
  });

  // Use type assertion to add rangePicker if applicable
  if ('addRange' in shape && 'getRanges' in shape) {
    (shape as any).rangePicker = [];
  }

  return shape;
};

export type diagram = {
  id: number;
  name: string;
  data: string | null;
};
export type diagramDTO = {
  total: number;
  items: diagram[];
};

export const colorRangeConfigs = {
  low: '#ffa500',
  medium: '#ffff00',
  high: '#008000',
  full: '#ff0000',
};

export const defaultRangeConfigs = {};

export type rangePicker = {
  value: number;
  color: string;
  operator: conditionalOperator;
};

export type elementVariable = {
  variableName: string;
  label: string;
  assetId: string;
  measurementId: string;
  value: string;
  error?: boolean;
  errorMsg?: string;
  uom?: string;
  overrideAssetTz?: boolean;
  overrideAssetTzValue?: boolean;
  globalSamplePeriod?: boolean;
  samplePeriod?: number;
  aggBy?: number;
  overrideGlobalSettings?: boolean;
  isRelativeToGlboalEndTime?: boolean;
  timeRange?: number;
  startDate?: number;
  endDate?: number;
};
export type conditionalOperator = '>' | '>=' | '<' | '<=' | '==' | '!=';

export type colorRule = {
  color?: string;
};

export type borderRule = {
  borderWidth?: number;
  borderStyle?: 'solid' | 'dotted' | 'dashed' | 'none';
  borderColor?: string;
};

export type elementVisibilityRule = {
  show?: boolean;
};

export type titleRule = {
  titleColor?: string;
  fontSize?: number;
};
export type rotationRule = {
  rotate?: boolean;
};

export type formattingRules = colorRule &
  borderRule &
  elementVisibilityRule &
  titleRule &
  rotationRule;

export type conditionalRule = {
  variable: string;
  conditionalOperator: conditionalOperator;
  value: string;
  applicableTo: 'color' | 'border' | 'title' | 'background' | 'rotation';
  // formattingOption: 'background' | 'color' | 'border' | 'hide_show' | 'title';
  rules: formattingRules;
};

export type elementSettings = {
  variables: elementVariable[];
  range: rangePicker[];
  conditionalRule: conditionalRule[];
  variableLabels: dia.Cell.ID[];
};

export const checkConditionalRules = (
  element: dia.Cell<dia.Cell.Attributes, dia.ModelSetOptions>,
): boolean[] => {
  const { variables = [], conditionalRule = [] } = element.get('data') || {};

  if (!variables.length || !conditionalRule.length) {
    return [];
  }

  return conditionalRule.map((rule: conditionalRule) => {
    const matchingVariable = variables[rule.variable];
    if (!matchingVariable) {
      return false;
    }

    const variableValue = matchingVariable.value;
    const ruleValue = rule.value;
    const operator = rule.conditionalOperator;

    switch (operator) {
      case '==':
        return variableValue === ruleValue;
      case '!=':
        return variableValue !== ruleValue;
      case '>':
        return parseFloat(variableValue) > parseFloat(ruleValue);
      case '<':
        return parseFloat(variableValue) < parseFloat(ruleValue);
      case '>=':
        return parseFloat(variableValue) >= parseFloat(ruleValue);
      case '<=':
        return parseFloat(variableValue) <= parseFloat(ruleValue);
      default:
        return false;
    }
  });
};

export type fontWeight =
  | 'normal'
  | 'bold'
  | 'lighter'
  | 'bolder'
  | '100'
  | '200'
  | '300'
  | '400'
  | '500'
  | '600'
  | '700'
  | '800'
  | '900';
export type LabelSettings = {
  fontSize: number;
  fontColor: string;
  fontWeight: fontWeight;
  backgroundColor: string;
  opacity: number;
  isLabel: boolean;
};

export const isGroupWithConicTankAndProgressOnly = (groupElement: dia.Element): boolean => {
  const embeddedCells = groupElement.getEmbeddedCells();
  // Ensure there are exactly two elements in the group
  if (embeddedCells.length !== 2) {
    return false;
  }
  // Check if there is one ConicTank and one Progress
  const hasConicTank = embeddedCells.some((cell) => cell instanceof ConicTank);
  const hasProgress = embeddedCells.some((cell) => cell instanceof Progress);

  return hasConicTank && hasProgress;
};
export const isGroupWithLiquidTankAndProgressOnly = (groupElement: dia.Element): boolean => {
  const embeddedCells = groupElement.getEmbeddedCells();
  if (embeddedCells.length !== 2) {
    return false;
  }
  const hasLiquidTank = embeddedCells.some((cell) => cell instanceof LiquidTank);
  const hasProgress = embeddedCells.some((cell) => cell instanceof Progress);
  return hasLiquidTank && hasProgress;
};
