import React from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { getActiveCustomer } from '~/redux/selectors/customerSelectors';
import NewAssetContainer from '~/layout/containers/NewAssetContainer';
import { useGetAssetByIdQuery } from '~/redux/api/assetsApi';
import { Container } from '@mui/system';
import { getCurrentSelectedAssetId } from '~/redux/selectors/treeSelectors';
import { getIsUserLoggedIn, getMainPanel } from '~/redux/selectors/dashboardSelectors';
import EditAssetContainer from '~/layout/containers/EditAssetContainer';
import { Asset } from '~/types/asset';
import { Box, Button } from '@mui/material';
import { dashboardSlice } from '~/redux/slices/dashboardSlice';

export function MainCreateNewAsset() {
  const activeCustomer = useSelector(getActiveCustomer);
  const assetId = useSelector(getCurrentSelectedAssetId);
  const loggedInuser = useSelector(getIsUserLoggedIn);
  const mainPanel = useSelector(getMainPanel);
  const dispatch = useDispatch();
  const { data: asset } = useGetAssetByIdQuery(
    { customerId: activeCustomer?.id ?? 0, assetId: assetId },
    {
      skip: !activeCustomer?.id || assetId === '-1', // In RTK Query, the option is `skip` instead of `enabled`
    },
  );

  return (
    <>
      {!loggedInuser?.scoped_roles?.find((role) => role.role === 'USER')?.role ? (
        <Box mt={2} sx={{ width: '100%', display: 'flex', justifyContent: 'end' }}>
          <Button
            sx={{ ml: 'auto' }}
            onClick={() => {
              dispatch(dashboardSlice.actions.selectMainPanel('chart'));
            }}
          >
            Back
          </Button>
        </Box>
      ) : null}
      <Container maxWidth={'sm'} component={'div'} style={{ float: 'left' }}>
        {mainPanel === 'newAsset' && activeCustomer && assetId === '-1' && (
          <NewAssetContainer customer={activeCustomer} parentAsset={undefined} />
        )}
        {mainPanel === 'newAsset' && activeCustomer && assetId != '-1' && asset && (
          <NewAssetContainer customer={activeCustomer} parentAsset={asset as Asset} />
        )}
        {mainPanel === 'editAsset' && activeCustomer && assetId !== '-1' && asset && (
          <EditAssetContainer customer={activeCustomer} parentAsset={asset as Asset} />
        )}
      </Container>
    </>
  );
}
