export default {
  dateRangeWrapper: 'rdrDateRangeWrapper',
  calendarWrapper: 'rdrCalendarWrapper',
  dateDisplay: 'rdrDateDisplay',
  dateDisplayItem: 'rdrDateDisplayItem',
  dateDisplayItemActive: 'rdrDateDisplayItemActive',
  monthAndYearWrapper: 'rdrMonthAndYearWrapper',
  monthAndYearPickers: 'rdrMonthAndYearPickers',
  nextPrevButton: 'rdrNextPrevButton',
  month: 'rdrMonth',
  weekDays: 'rdrWeekDays',
  weekDay: 'rdrWeekDay',
  days: 'rdrDays',
  day: 'rdrDay',
  dayNumber: 'rdrDayNumber',
  dayPassive: 'rdrDayPassive',
  dayToday: 'rdrDayToday',
  dayStartOfWeek: 'rdrDayStartOfWeek',
  inRange: 'rdrInRange',
  endEdge: 'rdrEndEdge',
  dayEndOfWeek: 'rdrDayEndOfWeek',
  startEdge: 'rdrStartEdge',
  prevButton: 'rdrPprevButton',
  nextButton: 'rdrNextButton',
  daySelected: 'rdrDaySelected',
  selected: 'rdrSelected',
  dayDisabled: 'rdrDayDisabled',
  months: 'rdrMonths',
  monthPicker: 'rdrMonthPicker',
  yearPicker: 'rdrYearPicker',
  dateDisplayWrapper: 'rdrDateDisplayWrapper',
  dayStartOfMonth: 'rdrDayStartOfMonth',
  dayEndOfMonth: 'rdrDayEndOfMonth',
  definedRangesWrapper: 'rdrDefinedRangesWrapper',
  staticRanges: 'rdrStaticRanges',
  staticRange: 'rdrStaticRange',
  inputRanges: 'rdrInputRanges',
  inputRange: 'rdrInputRange',
  inputRangeInput: 'rdrInputRangeInput',
  dateRangePickerWrapper: 'rdrDateRangePickerWrapper',
  dayStartPreview: 'rdrDayStartPreview',
  dayInPreview: 'rdrDayInPreview',
  dayEndPreview: 'rdrDayEndPreview',
  staticRangeLabel: 'rdrStaticRangeLabel',
  dayHovered: 'rdrDayHovered',
  dayActive: 'rdrDayActive',
  staticRangeSelected: 'rdrStaticRangeSelected',
  monthName: 'rdrMonthName',
  infiniteMonths: 'rdrInfiniteMonths',
  monthsVertical: 'rdrMonthsVertical',
  monthsHorizontal: 'rdrMonthsHorizontal',
};
