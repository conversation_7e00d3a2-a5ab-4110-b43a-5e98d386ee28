import { AssetMeasurement } from '~/measurements/domain/types';
import { AssetWithMeasurements } from './asset-tree-mapper';
import { Asset } from '../../types/asset';

export const normalizeAsset = (
  measurementTypes: Map<number, string>,
  asset: Asset,
  measurements: AssetMeasurement[],
): AssetWithMeasurements => {
  const measurementGroups: Map<string, AssetMeasurement[]> = new Map();

  measurements.forEach((measurement) => {
    const measurementType = measurementTypes.get(measurement.typeId) ?? `N/A`;
    const existingMeasurements = measurementGroups.get(measurementType) ?? [];
    existingMeasurements.push(measurement);
    measurementGroups.set(measurementType, existingMeasurements);
  });

  return {
    ...asset,
    measurementGroups:
      measurementGroups.size > 0
        ? Array.from(measurementGroups.entries(), ([type, measurements]) => ({
            type,
            measurements,
          }))
        : [],
  };
};
