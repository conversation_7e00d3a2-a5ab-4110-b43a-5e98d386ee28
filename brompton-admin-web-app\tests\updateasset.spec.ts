import { test, expect } from '@playwright/test';

test('update asset', async ({ page }) => {
  // Opening the login page
  await page.goto('https://test.pivotol.ai/login');

  // Fill in Username and Password
  await page.getByLabel('Username *').fill('test');
  await page.getByLabel('Password *').fill('Br0mpt0n!0T');

  // Click on Login Button
  await page.click('#__next > div > div > form > div > button');
  await page.waitForTimeout(3000);
  await page.setViewportSize({ width: 1920, height: 1080 });

  // Navigate to Assets -> Manage Assets
  await page.locator('div.MuiListItemText-root span', { hasText: 'Assets' }).click();
  await page.waitForTimeout(3000);
  await page.getByRole('menuitem', { name: 'Manage Assets' }).click();
  await page.waitForTimeout(8000);

  // Right-click on AssetAllFlows
  await page
    .locator('div.MuiBox-root.css-c36nl0:has-text("AssetAllFlows")')
    .click({ button: 'right' });
  await page.waitForTimeout(8000);

  await page.getByRole('menuitem', { name: 'Edit' }).click();
  await page.waitForTimeout(3000);

  // Fill in asset details
  await page.getByLabel('Tag *').fill('AssetAllFlows');

  // ✅ Fix: Click on the correct Asset Type dropdown
  //await page.getByLabel('Asset type').click();

  // Select asset type not editable on edit page
  //await page.getByRole('option', { name: 'Power > AC Buss (0)', exact: true }).click();

  // Ensure dropdown is visible before selecting timezone
  await page.getByTestId('ArrowDropDownIcon').nth(2).waitFor({ state: 'visible', timeout: 2000 });

  await page.getByLabel('Description').fill('TestAssets');

  await page.getByLabel('Select a time zone').click();
  await page.getByRole('option', { name: 'Africa/Abidjan', exact: true }).click();

  // Ensure dropdown is visible before selecting latitude/longitude
  await page.getByTestId('ArrowDropDownIcon').nth(1).waitFor({ state: 'visible', timeout: 2000 });

  await page.getByLabel('Latitude').fill('0');
  await page.getByLabel('Longitude').fill('0');

  // Submit the form
  await page.getByText('Submit').click();
  await page.waitForTimeout(2000);

  // Close the page
  await page.close();
});
