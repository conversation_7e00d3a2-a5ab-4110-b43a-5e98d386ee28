const { test, expect } = require('@playwright/test');

test.describe('API Testing for POST Customer Dashboard', () => {
  test('should successfully create/update dashboard with status 200', async ({ request }) => {
    // Define headers
    const headers = {
      'BE-CsrfToken': 'rvI8VKVKSQdSn7K2r4d8FEWHn8I21sv1el6iDlF8Jlg=',
      'Content-Type': 'application/json',
      Cookie:
        'BE-AccessToken=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VybmFtZSI6InRlc3QiLCJzdWIiOjIsInJvbGVzIjp7IkFETUlOIjpbOCwxMjEsMTE5LDEwNiwxMTgsMTA4LDg2LDExMSwxMTIsODUsMTA3LDg0LDExMywxMTQsMTE3LDEyMCwxMjcsMTIzLDEyNiwxMjQsMTA5LDEyNSwxMjIsMTEwLDksODJdLCJVU0VSIjpbOCwxMjEsMTE5LDEwNiwxMTgsMTA4LDg2LDExMSwxMTIsODUsMTA3LDg0LDExMywxMTQsMTE3LDEyMCwxMjcsMTIzLDEyNiwxMjQsMTA5LDEyNSwxMjIsMTEwLDksODJdLCJQT1dFUl9VU0VSIjpbOCwxMjEsMTE5LDEwNiwxMTgsMTA4LDg2LDExMSwxMTIsODUsMTA3LDg0LDExMywxMTQsMTE3LDEyMCwxMjcsMTIzLDEyNiwxMjQsMTA5LDEyNSwxMjIsMTEwLDksODJdfSwiaWF0IjoxNzMxNTA3NTgyLCJleHAiOjE3MzE1MTQ3ODJ9.GKXEI4ZLoemEAUYsODVNknMuvFhfv2__GgTpVoFC36o; BE-CSRFToken=rvI8VKVKSQdSn7K2r4d8FEWHn8I21sv1el6iDlF8Jlg%3D',
    };

    // Define body payload
    const payload = {
      title: 'Overview',
      data: '{"currentDashboardId":89,"dashboardTitle":"Overview", ...}', // Truncated for readability
    };

    // Send POST request
    const response = await request.post('https://test.brompton.ai/apiv0/customers/84/dashboards', {
      headers,
      data: payload,
    });

    // Validate the response status is 200
    expect(response.status()).toBe(200);

    // Check the response contains expected result (customize based on actual API response structure)
    const responseBody = await response.text();
    console.log('Response:', responseBody);
    expect(responseBody).toContain('expected_content_here'); // Replace with actual expected content
  });
});
