import { ThunkDispatch } from '@reduxjs/toolkit';
import { Data, Layout } from 'plotly.js';
import { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { timeseriesApi } from '~/redux/api/timeseriesApi';
import { getActiveCustomer } from '~/redux/selectors/customerSelectors';
import { RootState } from '~/redux/store';
import { AggByOptions, SamplePeriodOptions } from '~/types/dashboard';
import { SingleScatterTimeSeriesData } from '~/types/timeseries';
import { formatChartDate, formatMetricLabel, formatMetricTag } from '~/utils/utils';
type TrendResult = {
  tsData: SingleScatterTimeSeriesData;
};
type useFetchDynamicChartProps = {
  measurement: number | null;
  startDate: number;
  endDate: number;
  aggBy: number;
  samplePeriod: number;
  chartType: 'scatter' | 'bar';
  unit: string | null;
  tag: string | null;
};
type ChartData = {
  data: Data[];
  layout: Partial<Layout>;
  tsData?: number;
};

const convertDynamicCharts = ({
  chartType,
  result,
  unit,
  tag,
}: {
  unit: string | null;
  tag: string | null;
  result: TrendResult;
  chartType: 'scatter' | 'bar';
}): ChartData => {
  const traces: Data[] = [];
  const layout: Partial<Layout> = {
    showlegend: true,
    // title: numberOfCharts == 1 ? 'Trends' : formatMetricLabel(results[0]?.measureData.tag),

    annotations: [],
    yaxis: {
      // title: 'Unit Values',
      side: 'left',
    },
    yaxis2: {
      // title: 'Unit Values',
      side: 'right',
      overlaying: 'y',
    },
  };
  let chartNumber = 1;
  if (result && result.tsData) {
    const seriesData = result.tsData;

    const values = seriesData['ts,val'];
    if (!values) {
      return {
        data: [],
        layout,
        tsData: 0,
      };
    }
    const x = values.map(([timestamp]) => formatChartDate(new Date(timestamp)));
    const y = values.map(([, value]) => value * 1);

    const unitsOfMeasure = unit;

    const title = formatMetricLabel(tag ?? '');
    traces.push({
      type: chartType,
      x,
      y,
      // marker: { color },
      hovertemplate: `${title}: %{y} ${unitsOfMeasure}<br>Time: %{x}<extra></extra>'`,
      name: `${formatMetricTag(tag ?? '')} (${unitsOfMeasure})`,
      yaxis: 'y2',
      mode: 'lines',
    });
    chartNumber++;
  }
  return {
    data: traces,
    layout,
  };
};
const useFetchDynamicChart = ({
  measurement,
  startDate,
  endDate,
  aggBy,
  samplePeriod,
  chartType,
  unit,
  tag,
}: useFetchDynamicChartProps) => {
  const dispatch = useDispatch<ThunkDispatch<RootState, any, any>>();
  const activeCustomer = useSelector(getActiveCustomer);
  const [chartResults, setChartResults] = useState<TrendResult | undefined>(undefined);
  const [allDataFetched, setAllDataFetched] = useState({
    chartData: [] as Data[],
    tsData: undefined as number | undefined,
    isLoading: true,
    isError: false,
    layoutData: {
      showlegend: true,
      title: 'Chart',
    } as Partial<Layout>,
  });

  useEffect(() => {
    const fetchMeasureData = async () => {
      try {
        setAllDataFetched((prev) => ({
          ...prev,
          isError: false,
          chartData: [],
          tsData: undefined,
          isLoading: true, // Ensure loading state is set before the request
        }));

        const {
          data: tsData,
          isSuccess,
          isError,
        } = await dispatch(
          timeseriesApi.endpoints.getMeasurementSeries.initiate({
            customerId: Number(activeCustomer?.id),
            measId: measurement?.toString() ?? '',
            start: startDate,
            end: endDate,
            agg: AggByOptions[aggBy].serverValue,
            agg_period: SamplePeriodOptions[samplePeriod].serverValue,
            timeRangeType: 1,
            assetTz: false,
          }),
        );

        if (isError) {
          setAllDataFetched((prev) => ({
            ...prev,
            isError: true,
            isLoading: false,
          }));
        } else if (isSuccess && tsData) {
          setChartResults({ tsData } as TrendResult);
        }
      } catch (e) {
        setAllDataFetched((prev) => ({
          ...prev,
          isError: true,
          isLoading: false,
        }));
      }
    };

    if (measurement !== null && endDate > startDate) {
      fetchMeasureData();
    }
  }, [measurement, startDate, endDate, aggBy, samplePeriod, unit]);

  useEffect(() => {
    if (chartResults) {
      const chartData = convertDynamicCharts({
        unit,
        result: chartResults,
        chartType,
        tag,
      });

      if (chartData) {
        setAllDataFetched((prev) => ({
          ...prev,
          chartData: chartData.data || [],
          layoutData: chartData.layout || {},
          tsData: chartData.tsData ?? undefined,
          isLoading: false, // Set loading to false after processing
          isError: false,
        }));
      }
    }
  }, [chartResults, measurement, startDate, endDate, aggBy, samplePeriod, unit, chartType, tag]);

  return { ...allDataFetched };
};

export default useFetchDynamicChart;
