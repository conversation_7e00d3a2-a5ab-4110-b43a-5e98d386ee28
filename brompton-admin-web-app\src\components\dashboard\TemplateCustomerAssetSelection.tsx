import { Autocomplete, Box, Button, TextField } from '@mui/material';
import { SetStateAction, useState } from 'react';
import { useGetAllAssetQuery } from '~/redux/api/assetsApi';
import { useGetCustomersQuery } from '~/redux/api/customersApi';
import { Customer } from '~/types/customers';
import { assetsPathMapper } from '~/utils/utils';
import CustomDialog from '../common/CustomDialog';

type TemplateCustomerAssetSelectionProps = {
  saveTemplateAsDashboard: boolean;
  setSaveTemplateAsDashboard: (value: SetStateAction<boolean>) => void;
};
const TemplateCustomerAssetSelection = ({
  saveTemplateAsDashboard,
  setSaveTemplateAsDashboard,
}: TemplateCustomerAssetSelectionProps) => {
  const [customer, setCustomer] = useState<Customer | null>(null);
  const { data: customerList, isFetching: isCustomerListLoading } = useGetCustomersQuery({
    is_logo: false,
  });
  const [customerAssetList, setCustomerAssetList] = useState<
    { id: string; value: number; label: string }[]
  >([]);
  const { data: customerAssets } = useGetAllAssetQuery(
    {
      customerId: customer?.id ?? 0,
      parentIds: [],
    },
    {
      skip: !customer,
      refetchOnMountOrArgChange: true,
    },
  );
  return (
    <>
      <CustomDialog
        open={saveTemplateAsDashboard}
        content={
          <Box mt={3}>
            <Autocomplete<Customer>
              id="top-panel-customer-list"
              options={customerList ?? []}
              loading={isCustomerListLoading}
              getOptionLabel={(option) => option.name}
              onChange={(event, value) => setCustomer(value)}
              size="small"
              fullWidth
              isOptionEqualToValue={(option, value) => option.id === value.id}
              value={customer ?? null}
              renderInput={(params) => <TextField {...params} label="Select Customer" />}
            />
            <Autocomplete
              sx={{
                mt: 2,
              }}
              fullWidth
              multiple
              size="small"
              options={
                customerAssets
                  ? [
                      ...assetsPathMapper(customerAssets ?? []),
                      ...customerAssets
                        .filter((item) => item.parentIds.length === 0)
                        .map((item) => ({
                          id: item.id.toString(),
                          value: item.id,
                          label: item.tag,
                        })),
                    ]
                  : []
              }
              getOptionLabel={(option) => option.label}
              onChange={(_, value) => {
                setCustomerAssetList(
                  value.map((item) => ({
                    id: item.id.toString(),
                    value: item.value,
                    label: item.label,
                  })),
                );
              }}
              //   onChange={(_, value) => field.onChange(value.map((item) => item.id))}
              renderInput={(params) => <TextField {...params} label="Parent Assets" />}
              isOptionEqualToValue={(option, value) => option.id === value.id}
            />
          </Box>
        }
        dialogActions={
          <>
            <Button
              variant="outlined"
              onClick={() => {
                setSaveTemplateAsDashboard(false);
                setCustomer(null);
                setCustomerAssetList([]);
              }}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              variant="contained"
              color="primary"
              disabled={
                !customer ||
                !customerAssets ||
                customerAssets.length === 0 ||
                !customerAssetList ||
                customerAssetList.length === 0
              }
              //   startIcon={!initialTitle || action === 'save' ? <SaveIcon /> : <SaveAsIcon />}
            >
              Save
            </Button>
          </>
        }
        title="Save Template as Dashboard"
        onClose={() => {
          setSaveTemplateAsDashboard(false);
        }}
      />
    </>
  );
};

export default TemplateCustomerAssetSelection;
