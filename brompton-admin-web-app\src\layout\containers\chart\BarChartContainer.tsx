import Chart from '~/components/Chart';
import useFetchAnnotationData from '~/hooks/useFetchAnnotationData';
import { useFetchBarData } from '~/hooks/useFetchBarData';
import { BarChartWidget } from '~/types/widgets';

type BarChartContainerProps = {
  id: string;
  settings: BarChartWidget;
};

export function BarChartContainer({ id, settings }: BarChartContainerProps): JSX.Element {
  const { isLoading, chartData, layoutData, removedResults, successAndFailedMeasurements } =
    useFetchBarData(id, settings);
  const { measureIdAnnotation } = useFetchAnnotationData(id, settings);
  return (
    <>
      <Chart
        id={id}
        chartType="bar"
        settings={settings}
        data={chartData}
        layout={layoutData}
        isLoading={isLoading}
        showSettings={true}
        successAndFailedMeasurements={successAndFailedMeasurements}
        measureIdAnnotation={measureIdAnnotation}
        removedResults={removedResults}
      />
    </>
  );
}
