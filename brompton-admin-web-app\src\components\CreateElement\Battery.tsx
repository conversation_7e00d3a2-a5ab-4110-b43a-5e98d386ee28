import { dia, util } from '@joint/core';

export default class Battery extends dia.Element {
  constructor(attributes = {}, options = {}) {
    super(attributes, options);
    this.updateMarkup(); // Generate the initial markup
    this.on('change:size', this.updateMarkup.bind(this));
  }

  defaults(): Partial<dia.Element.Attributes> {
    return {
      ...super.defaults,
      type: 'Battery',
      size: { width: 80, height: 160 },
      attrs: {
        body: {
          stroke: 'black',
          strokeWidth: 8,
          width: 'calc(w)',
          height: 'calc(h)',
          x: 0,
          y: 20,
          fill: '#fff',
        },
        head: {
          stroke: 'black',
          strokeWidth: 8,
          width: 40,
          height: 20,
          x: 0, // Initial value, this will be recalculated
          y: 0,
          fill: 'black',
        },
        label: {
          // text: 'Battery',
          refY: 'calc(h + 15)', // Position label outside the bottom of the battery with 5px margin top
          refX: 'calc(w / 2)',
          textAnchor: 'middle',
          textVerticalAnchor: 'middle',
          fontSize: 14,
          fill: 'black',
        },
      },
    };
  }

  updateMarkup() {
    const bodyWidth = this.size().width;
    const bodyHeight = this.size().height;

    // Calculate the x position for the head to be centered at the top of the body
    const headX = (bodyWidth - 40) / 2;

    this.attr({
      body: {
        width: bodyWidth,
        height: bodyHeight - 20, // Adjusted for head position
      },
      head: {
        x: headX,
        y: 0,
        width: 40,
        height: 20,
      },
      label: {
        refX: bodyWidth / 2,
        refY: bodyHeight + 15, // Position label outside the bottom of the battery with 5px margin top
      },
    });

    this.prop('markup', [
      { tagName: 'rect', selector: 'head' },
      { tagName: 'rect', selector: 'body' },
      { tagName: 'text', selector: 'label' },
    ]);
  }

  preinitialize() {
    this.markup = util.svg/* xml */ `
    <rect @selector="body"/>
    <rect @selector="head"/>
    <text @selector="label"/>`;
  }
}
