import { Box, Typography } from '@mui/material';

import { CSSProperties, ReactNode } from 'react';

type NoDataFoundProps = {
  style?: CSSProperties;
  message?: string | ReactNode;
};
export const NoDataFound = ({ style, message }: NoDataFoundProps) => {
  return (
    <Box
      sx={{
        display: 'flex',
        justifyContent: 'center',
        height: '100%',
        alignItems: 'center',
        ...style,
      }}
    >
      <Typography>No data found {message ? message : null} </Typography>
    </Box>
  );
};
