import CancelIcon from '@mui/icons-material/Cancel';
import SaveAsIcon from '@mui/icons-material/SaveAs';
import { Button, Card, FormControlLabel, FormGroup, Radio, RadioGroup } from '@mui/material';
import FormControl from '@mui/material/FormControl';
import { SelectChangeEvent } from '@mui/material/Select';
import { PlotType } from 'plotly.js';
import React, { useEffect, useState } from 'react';
import { BarChartSettings } from '~/components/ChartSettings/BarChartSettings';
import { HeatmapChartSettings } from '~/components/ChartSettings/HeatmapChartSettings';
import { ScatterChartSettings } from '~/components/ChartSettings/ScatterChartSettings';
import {
  DEFAULT_BAR_CHART_SETTINGS,
  DEFAULT_BULLET_CHART_SETTINGS,
  DEFAULT_HEAT_MAP_SETTINGS,
  DEFAULT_INDICATOR_CHART_SETTINGS,
  DEFAULT_SANKY_CHART_SETTINGS,
  DEFAULT_SCATTER_CHART_SETTINGS,
} from '~/redux/slices/dashboardSlice';
import {
  BarChartWidget,
  BulletChartWidget,
  ChartSettings,
  ChartType,
  ChartWidget,
  GaugeChartWidget,
  HeatmapChartWidget,
  SankeyChartWidget,
  ScatterChartWidget,
} from '~/types/widgets';
import CustomDialog from '../common/CustomDialog';
import BulletChartSettings from './BulletChartSettings';
import GaugeChartSettings from './GaugeChartSettings';
import SankeyChartSettings from './SankeyChartSettings';

type ChartSettingDialogProps = {
  open: boolean;
  onCancel: () => unknown;
  onUpdateClick: (
    settings: ChartWidget,
    chartAxisType: {
      xAsisLabel: string;
    },
  ) => unknown;
  selectedDbMeasureIdToName: { [key: string]: string };
  id: string;
  currentChartType: ChartType;
  currentChartSettings: ChartSettings;
};

export const ChartSettingDialog = ({
  open,
  onUpdateClick,
  onCancel,
  selectedDbMeasureIdToName,
  id,
  currentChartType,
  currentChartSettings,
}: ChartSettingDialogProps): JSX.Element => {
  const [chartType, setChartType] = useState<ChartType>(currentChartType);
  const [chartAxis, setChartAxis] = useState<{
    xAsisLabel: string;
  }>({
    xAsisLabel: 'Time',
  });
  const [heatmapChartSettings, setHeatmapChartSettings] =
    useState<HeatmapChartWidget>(DEFAULT_HEAT_MAP_SETTINGS);
  const [barChartSettings, setBarChartSettings] = useState<BarChartWidget>(
    DEFAULT_BAR_CHART_SETTINGS,
  );
  const [scatterChartState, setScatterChartState] = useState<ScatterChartWidget>(
    DEFAULT_SCATTER_CHART_SETTINGS,
  );
  const [gaugeChartState, setGaugeChartState] = useState<GaugeChartWidget>(
    DEFAULT_INDICATOR_CHART_SETTINGS,
  );
  const [bulletChartState, setBulletChartState] = useState<BulletChartWidget>(
    DEFAULT_BULLET_CHART_SETTINGS,
  );
  const [SankeyChartSettingsState, setSankeyChartSettingsState] = useState<SankeyChartWidget>(
    DEFAULT_SANKY_CHART_SETTINGS,
  );
  const [title, setTitle] = useState(currentChartSettings.title);

  useEffect(() => {
    // using currentChartType to make sure that initial state is set correctly only once
    if (currentChartType === 'heatmap') {
      setHeatmapChartSettings(currentChartSettings as HeatmapChartWidget);
    } else if (currentChartType === 'scatter') {
      setScatterChartState(currentChartSettings as ScatterChartWidget);
    } else if (currentChartType === 'bar') {
      setBarChartSettings(currentChartSettings as BarChartWidget);
    } else if (currentChartType === 'indicator') {
      setGaugeChartState(currentChartSettings as GaugeChartWidget);
    } else if (currentChartType === 'bullet') {
      setBulletChartState(currentChartSettings as BulletChartWidget);
    } else if (currentChartType === 'sankey') {
      setSankeyChartSettingsState(currentChartSettings as SankeyChartWidget);
    }
  }, [currentChartType, currentChartSettings, id]);
  const getFirstMeasure = () => {
    if (currentChartType === 'bar' || currentChartType === 'scatter') {
      return 'selectedTitles' in currentChartSettings
        ? (currentChartSettings.selectedTitles[0] as
            | ScatterChartWidget['selectedTitles'][0]
            | BarChartWidget['selectedTitles'][0])
        : '';
    }
    return '';
  };
  const handleChange = (e: SelectChangeEvent) => {
    setChartType(e.target.value as ChartType);
    const updatedChartSettings = {
      ...currentChartSettings,
      title: currentChartSettings.title,
      overrideGlobalSettings: currentChartSettings.overrideGlobalSettings,
      globalSamplePeriod: currentChartSettings.globalSamplePeriod,
      samplePeriod: currentChartSettings.samplePeriod,
      overrideAssetTzValue: currentChartSettings.overrideAssetTzValue,
      overrideAssetTz: currentChartSettings.overrideAssetTz,
      timeRange: currentChartSettings.timeRange,
      dashboard: currentChartSettings.dashboard,
      aggBy: currentChartSettings.aggBy,
      startDate: currentChartSettings.startDate,
      endDate: currentChartSettings.endDate,
    };
    switch (e.target.value as ChartType) {
      case 'scatter':
        setScatterChartState({
          ...scatterChartState,
          ...updatedChartSettings,
          showStacked: false,
          ...(currentChartType !== 'bar' && currentChartType !== 'scatter'
            ? {
                assetMeasure: [],
              }
            : undefined),
        } as ScatterChartWidget);
        break;
      case 'bar':
        setBarChartSettings({
          ...barChartSettings,
          ...updatedChartSettings,
          ...(currentChartType !== 'bar' && currentChartType !== 'scatter'
            ? {
                assetMeasure: [],
              }
            : undefined),
        } as BarChartWidget);
        break;
      case 'bullet' as PlotType:
        setBulletChartState({
          ...bulletChartState,
          ...updatedChartSettings,
          selectedDbMeasureId: getFirstMeasure(),
          assetMeasure: {
            assetId: '',
            measureId: [],
          },
        } as BulletChartWidget);
        setHeatmapChartSettings(updatedChartSettings as HeatmapChartWidget);
        break;
      case 'heatmap':
        setHeatmapChartSettings({
          ...heatmapChartSettings,
          ...updatedChartSettings,
          selectedDbMeasureId: getFirstMeasure(),
          assetMeasure: {
            assetId: '',
            measureId: [],
          },
        } as HeatmapChartWidget);
        break;
      case 'indicator':
        setGaugeChartState({
          ...gaugeChartState,
          ...updatedChartSettings,
          selectedDbMeasureId: getFirstMeasure(),
          assetMeasure: {
            assetId: '',
            measureId: [],
          },
        } as GaugeChartWidget);
        break;
      case 'sankey':
        setSankeyChartSettingsState({
          ...SankeyChartSettingsState,
          ...updatedChartSettings,
        } as SankeyChartWidget);
        break;
      default:
        throw new Error('Invalid chart type');
    }
  };

  useEffect(() => {
    if (open) {
      setChartType(chartType);
    }
  }, [open, chartType, selectedDbMeasureIdToName]);

  const getChartTypeSettings = (chartType: PlotType) => {
    switch (chartType) {
      case 'heatmap':
        return { ...heatmapChartSettings, isDirty: true };
      case 'scatter':
        return { ...scatterChartState, isDirty: true };
      case 'bar':
        return { ...barChartSettings, isDirty: true };
      case 'indicator':
        return { ...gaugeChartState, isDirty: true };
      case 'bullet' as PlotType:
        return { ...bulletChartState, isDirty: true };
      case 'sankey':
        return { ...SankeyChartSettingsState, isDirty: true };
      default:
        throw new Error('Invalid chart type');
    }
  };
  const resetToLastSaved = () => {
    onUpdateClick(
      {
        chartType: currentChartType,
        settings: currentChartSettings,
      } as ChartWidget,
      chartAxis,
    );
    switch (currentChartType) {
      case 'heatmap':
        setHeatmapChartSettings(currentChartSettings as HeatmapChartWidget);
        break;
      case 'scatter':
        setScatterChartState(currentChartSettings as ScatterChartWidget);
        break;
      case 'bar':
        setBarChartSettings(currentChartSettings as BarChartWidget);
        break;
      case 'indicator':
        setGaugeChartState(currentChartSettings as GaugeChartWidget);
        break;
      case 'bullet':
        setBulletChartState(currentChartSettings as BulletChartWidget);
        break;
      case 'sankey':
        setSankeyChartSettingsState(currentChartSettings as SankeyChartWidget);
        break;
    }
    setTitle(currentChartSettings.title);
    if (currentChartType !== chartType) {
      switch (chartType) {
        case 'heatmap':
          setHeatmapChartSettings(DEFAULT_HEAT_MAP_SETTINGS);
          break;
        case 'scatter':
          setScatterChartState(DEFAULT_SCATTER_CHART_SETTINGS);
          break;
        case 'bar':
          setBarChartSettings(DEFAULT_BAR_CHART_SETTINGS);
          break;
        case 'indicator':
          setGaugeChartState(DEFAULT_INDICATOR_CHART_SETTINGS);
          break;
        case 'bullet':
          setBulletChartState(DEFAULT_BULLET_CHART_SETTINGS);
          break;
        case 'sankey':
          setSankeyChartSettingsState(DEFAULT_SANKY_CHART_SETTINGS);
          break;
      }
    }
    setChartType(currentChartType);
    onCancel();
  };
  const handleTitleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.name === 'value') {
      setTitle({ ...title, [event.target.name]: event.target.value });
    } else if (event.target.name === 'isVisible') {
      setTitle({ ...title, [event.target.name]: event.target.checked });
    }
  };

  return (
    <>
      <CustomDialog
        title={`${
          currentChartType === 'indicator'
            ? 'Gauge'
            : currentChartType === 'scatter'
            ? 'Trend'
            : currentChartType.charAt(0).toUpperCase() + currentChartType.slice(1)
        } Chart Setting`}
        maxWidth="lg"
        content={
          <FormControl component="fieldset" fullWidth>
            <FormGroup>
              {chartType === 'bar' || chartType === 'scatter' ? (
                <Card sx={{ p: 1, display: 'flex', verticalAlign: 'center', mb: 2 }}>
                  <RadioGroup
                    title="Chart Type"
                    sx={{ pl: 2 }}
                    aria-labelledby="demo-controlled-radio-buttons-group"
                    name="radio-buttons-group"
                    value={chartType}
                    onChange={handleChange}
                    row
                  >
                    <FormControlLabel value="scatter" control={<Radio />} label="Trend" />
                    <FormControlLabel value="bar" control={<Radio />} label="Bar" />
                  </RadioGroup>
                </Card>
              ) : null}
              {/* {chartType === 'bar' || chartType === 'scatter' ? (
                <Card sx={{ mt: 2, p: 1 }}>
                  <TextField
                    name="x-Axis-label"
                    onChange={(event) => handleAxisChange(event)}
                    value={chartAxis.xAsisLabel}
                    label="X-Axis Label"
                    variant="outlined"
                    margin="normal"
                    fullWidth
                  />
                </Card>
              ) : null} */}
              {chartType === 'heatmap' && (
                <>
                  <HeatmapChartSettings
                    selectedDbMeasureIdToName={selectedDbMeasureIdToName}
                    heatmapChartSettings={heatmapChartSettings}
                    handleHeatmapChartSettingsUpdate={setHeatmapChartSettings}
                  />
                </>
              )}
              {chartType === 'scatter' && (
                <>
                  <ScatterChartSettings
                    selectedDbMeasureIdToName={selectedDbMeasureIdToName}
                    scatterChartSettings={scatterChartState}
                    handleScatterChartSettingsUpdate={setScatterChartState}
                  />
                </>
              )}
              {chartType === 'bar' && (
                <>
                  <BarChartSettings
                    selectedDbMeasureIdToName={selectedDbMeasureIdToName}
                    barChartSettings={barChartSettings}
                    handleBarChartSettingsUpdate={setBarChartSettings}
                  />
                </>
              )}
              {chartType === 'indicator' && (
                <>
                  <GaugeChartSettings
                    gaugeChartSettings={gaugeChartState}
                    handleGaugeChartSettingsUpdate={setGaugeChartState}
                    selectedDbMeasureIdToName={selectedDbMeasureIdToName}
                  />
                </>
              )}
              {chartType === 'bullet' && (
                <>
                  <BulletChartSettings
                    bulletChartSettings={bulletChartState}
                    handlebulletChartSettingsUpdate={setBulletChartState}
                    selectedDbMeasureIdToName={selectedDbMeasureIdToName}
                  />
                </>
              )}
              {chartType === 'sankey' && (
                <>
                  <SankeyChartSettings
                    settings={SankeyChartSettingsState}
                    handleSettings={setSankeyChartSettingsState}
                  />
                </>
              )}
            </FormGroup>
          </FormControl>
        }
        open={open}
        onClose={resetToLastSaved}
        dialogActions={
          <>
            <Button
              autoFocus
              sx={{ mr: 'auto' }}
              variant="outlined"
              size="medium"
              startIcon={<CancelIcon />}
              onClick={() => {
                resetToLastSaved();
              }}
            >
              Cancel
            </Button>
            <Button
              variant="contained"
              size="medium"
              startIcon={<SaveAsIcon />}
              disabled={
                // (chartType === 'bar' &&
                //   // barChartSettings.assetMeasure.length === 0 &&
                //   barChartSettings.assetMeasure.some((measure) =>
                //     measure.measureId.some((id) => id === ''),
                //   )) ||
                // // (barChartSettings.selectedTitles.length === 0 ||
                // (chartType === 'scatter' && scatterChartState.selectedTitles.length === 0) ||
                // (chartType === 'bullet' && bulletChartState.selectedDbMeasureId === '') ||
                // (chartType === 'heatmap' && heatmapChartSettings.selectedDbMeasureId === '') ||
                // (chartType === 'indicator' && gaugeChartState.selectedDbMeasureId === '') ||
                (chartType === 'bar' && !barChartSettings.isValid) ||
                (chartType === 'scatter' && !scatterChartState.isValid) ||
                (chartType === 'bullet' && !bulletChartState.isValid) ||
                (chartType === 'heatmap' && !heatmapChartSettings.isValid) ||
                (chartType === 'indicator' && !gaugeChartState.isValid) ||
                (chartType === 'sankey' &&
                  (SankeyChartSettingsState.Label.length === 0 ||
                    SankeyChartSettingsState.connections.length === 0 ||
                    SankeyChartSettingsState.connections.some(
                      (conn) => conn.source === '' || conn.destination === '',
                    ) ||
                    !SankeyChartSettingsState.isValid))
              }
              onClick={() => {
                onUpdateClick(
                  {
                    chartType,
                    settings: getChartTypeSettings(chartType as PlotType),
                  } as ChartWidget,
                  chartAxis,
                );
              }}
            >
              Update
            </Button>
          </>
        }
      />
    </>
  );
};
