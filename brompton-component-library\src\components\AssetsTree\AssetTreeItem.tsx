import * as React from 'react';
import TreeItem, {
  TreeItemProps,
  TreeItemContentProps,
  useTreeItem,
} from '@mui/lab/TreeItem';
import { AssetNode, NodeConfig } from './AssetNode';

type AssetTreeItemProps = TreeItemProps & {
  onExpandToggle: (nodeId: string) => void;
  nodeConfig: NodeConfig;
  checkboxSelection: boolean;
};

const AssetNodeContainer = (
  expandToggleHandler: (nodeId: string) => void,
  nodeConfig: NodeConfig,
  checkboxSelection: boolean
) =>
  React.forwardRef(function AssetNodeContainer(
    props: TreeItemContentProps,
    ref
  ) {
    const { nodeId } = props;

    const treeItemContext = useTreeItem(nodeId);

    return (
      <AssetNode
        ref={ref}
        {...props}
        {...treeItemContext}
        onExpandToggle={expandToggleHandler}
        nodeConfig={nodeConfig}
        checkbox={checkboxSelection}
      />
    );
  });

export const AssetTreeItem = ({
  onExpandToggle,
  nodeConfig,
  checkboxSelection,
  ...rest
}: AssetTreeItemProps) => (
  <TreeItem
    ContentComponent={AssetNodeContainer(
      onExpandToggle,
      nodeConfig,
      checkboxSelection
    )}
    {...rest}
  />
);
