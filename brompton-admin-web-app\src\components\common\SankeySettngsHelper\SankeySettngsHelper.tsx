import CancelIcon from '@mui/icons-material/Cancel';
import LaunchIcon from '@mui/icons-material/Launch';
import {
  Autocomplete,
  Box,
  Button,
  Card,
  Checkbox,
  CircularProgress,
  Divider,
  FormControl,
  FormControlLabel,
  IconButton,
  MenuItem,
  OutlinedInput,
  Radio,
  RadioGroup,
  Select,
  SelectChangeEvent,
  TextField,
  Tooltip,
  Typography,
} from '@mui/material';
import { AnyAction, ThunkDispatch } from '@reduxjs/toolkit';
import { useRouter } from 'next/router';
import React, { useEffect, useMemo, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Label } from '~/components/ChartSettings/SankeyChartSettings';
import { Role, useRolePermission } from '~/hooks/useRolePermission';
import { AssetMeasurement } from '~/measurements/domain/types';
import { useGetAllAssetQuery, useGetAllBackOfficeAssetTypesQuery } from '~/redux/api/assetsApi';
import { useGetDashboardByCustomerIdQuery } from '~/redux/api/dashboardApi';
import { useGetDashboardTemplatesQuery } from '~/redux/api/dashboardTemplate';
import { measuresApi } from '~/redux/api/measuresApi';
import { getActiveCustomer, getCustomerId } from '~/redux/selectors/customerSelectors';
import {
  getCurrentAssetType,
  getCurrentDashboardId,
  getMetricsIdToName,
  isDashboardDirty,
} from '~/redux/selectors/dashboardSelectors';
import { getDeletedWidgets, getWidgets } from '~/redux/selectors/widgetSelectors';
import { dashboardSlice } from '~/redux/slices/dashboardSlice';
import { RootState } from '~/redux/store';
import { AssetTypeOption } from '~/types/asset';
import { AggByOptions } from '~/types/dashboard';
import { SankeyChartWidget } from '~/types/widgets';
import { assetTypePathMapperFilterTemplates } from '~/utils/mappers/asset-type-mapper';
import { assetsPathMapper, formatMetricLabel } from '~/utils/utils';
import CustomDialog from '../CustomDialog';

type SankeySettingsHelperProps = {
  mode: 'dashboard' | 'template';
  label: Label;
  labels: Label[];
  setLabels: (labels: Label[]) => void;
  index: number;
  settings: SankeyChartWidget;
  handleSettings: (
    value: ((prevState: SankeyChartWidget) => SankeyChartWidget) | SankeyChartWidget,
  ) => void;
};

const SankeySettingsHelper = ({
  mode,
  index,
  label,
  labels,
  setLabels,
  settings,
  handleSettings,
}: SankeySettingsHelperProps) => {
  const router = useRouter();
  const { hasDashboardPermission } = useRolePermission();
  const currentDashboard = useSelector(getCurrentDashboardId);
  const dispatch = useDispatch<ThunkDispatch<RootState, any, AnyAction>>();
  const customerId = useSelector(getCustomerId);
  const metricsIdToName = useSelector(getMetricsIdToName);
  const activeCustomer = useSelector(getActiveCustomer);
  const assetTypeTemplate = useSelector(getCurrentAssetType);
  const widgets = useSelector(getWidgets);
  const deleteWidgets = useSelector(getDeletedWidgets);
  const isDashboardStateDirty = useSelector(isDashboardDirty);
  const [selectedMetric, setSelectedMetric] = useState<{ label: string; value: string } | null>(
    null,
  );
  const [selectedAsset, setSelectedAsset] = useState<{ label: string; id: number } | null>(null);
  const [measureData, setMeasureData] = useState<AssetMeasurement[]>([]);
  const [assetToAssetType, seAssetToAssetType] = useState<number | null>(null);
  const [loadingMeasure, setLoadingMeasure] = useState(false);
  const [confirm, setConfirm] = useState<boolean>(false);
  const [assetTypesTemplateWithPath, setAssetTypeTemplatesWithPath] = useState<AssetTypeOption[]>(
    [],
  );

  const { data: assetData, isFetching: isAssetReloading } = useGetAllAssetQuery(
    { customerId, parentIds: [] },
    {
      skip: !customerId,
      refetchOnMountOrArgChange: true,
    },
  );

  const { data: dashboardList, isLoading: isLoadingDashboards } = useGetDashboardByCustomerIdQuery(
    {
      customerId: activeCustomer?.id ?? 0,
      search: null,
    },
    {
      skip: !activeCustomer?.id,
    },
  );

  const {
    data: assetTypeListData,
    isLoading: isAssetTypeLoading,
    isSuccess: isSuccessfullBackOffieAssetTypes,
  } = useGetAllBackOfficeAssetTypesQuery(undefined, {
    skip: settings.mode === 'dashboard' || !assetTypeTemplate || assetTypeTemplate <= 0,
  });

  const { data: dashboardTemplates, isLoading: isLoadingDashboardTemplates } =
    useGetDashboardTemplatesQuery(
      {
        assetTypeId: mode === 'dashboard' ? assetToAssetType ?? 0 : assetTypeTemplate ?? 0,
      },
      {
        skip:
          mode === 'dashboard'
            ? assetToAssetType === null || assetToAssetType === 0
            : !assetTypeTemplate || assetTypeTemplate <= 0 || assetTypeTemplate === null,
      },
    );

  const assetTypesWithPath = useMemo(() => assetsPathMapper(assetData ?? []), [assetData]);

  useEffect(() => {
    if (isSuccessfullBackOffieAssetTypes && assetTypeListData) {
      setAssetTypeTemplatesWithPath(
        assetTypePathMapperFilterTemplates(
          assetTypeListData.map((item) => ({
            ...item,
            name: item.name,
            id: item.id,
          })),
        ),
      );
    }
  }, [assetTypeListData, isSuccessfullBackOffieAssetTypes]);

  useEffect(() => {
    if (
      assetData &&
      settings.mode === 'dashboard' &&
      label.linkedDashboard?.type === 'template' &&
      settings.assetOrAssetType !== null
    ) {
      const assetToType = assetData?.find(
        (asset) => asset.id === settings.assetOrAssetType,
      )?.assetTypeId;
      seAssetToAssetType(assetToType ?? null);
    }
  }, [settings.assetOrAssetType, settings.mode, assetData, label.linkedDashboard?.type]);

  useEffect(() => {
    if (mode === 'template') {
      setSelectedMetric({
        label: label.sourceLabel ?? '',
        value: label.sourceName,
      });
    }
  }, [mode, label]);
  useEffect(() => {
    if (
      !selectedAsset && // haven't manually selected yet
      !isAssetReloading &&
      label.sourceAssetMeasure?.assetId // there's a default
    ) {
      const matchedAsset = assetTypesWithPath.find(
        (a) => a.id.toString() === label.sourceAssetMeasure!.assetId,
      );
      if (matchedAsset) {
        setSelectedAsset({ label: matchedAsset.label, id: matchedAsset.id });
      }
    }
  }, [label.sourceAssetMeasure?.assetId, selectedAsset, isAssetReloading, assetTypesWithPath]);

  useEffect(() => {
    const fetchMeasure = async () => {
      if (!selectedAsset) {
        setMeasureData([]);
        return;
      }
      setLoadingMeasure(true);
      try {
        const response = await dispatch(
          measuresApi.endpoints.getAllMeasurements.initiate({
            customerId,
            assetId: selectedAsset.id,
          }),
        ).unwrap();

        if (Array.isArray(response)) {
          setMeasureData(response);
        } else {
          setMeasureData([]);
        }
      } catch (error) {
        console.error('Failed to fetch measurements:', error);
        setMeasureData([]);
      } finally {
        setLoadingMeasure(false);
      }
    };
    fetchMeasure();
  }, [selectedAsset, dispatch, customerId]);

  const handleSourceFromChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newSourceFrom = e.target.value as 'Calculated' | 'Default';

    const newLabels = [...labels];
    newLabels[index] = {
      ...label,
      sourceFrom: newSourceFrom,
      sourceName: '',
      sourceLabel: '',
      sourceAssetMeasure:
        newSourceFrom === 'Calculated'
          ? undefined
          : {
              assetId: label.sourceAssetMeasure?.assetId || '',
              measureId: label.sourceAssetMeasure?.measureId || '',
            },
    };
    setLabels(newLabels);

    handleSettings((prevState) => ({
      ...prevState,
      Label: newLabels,
      isValid:
        newSourceFrom === 'Calculated'
          ? false
          : !!label.sourceAssetMeasure?.assetId && !!label.sourceAssetMeasure?.measureId,
    }));
  };

  const handleAssetChange = (
    _event: React.SyntheticEvent,
    newValue: { label: string; id: number } | null,
  ) => {
    setSelectedAsset(newValue);

    const newLabels = [...labels];
    newLabels[index] = {
      ...label,
      sourceAssetMeasure: {
        assetId: newValue?.id.toString() ?? '',
        measureId: '',
      },
    };
    setLabels(newLabels);

    handleSettings((prevState) => ({
      ...prevState,
      Label: newLabels,
      isValid: false,
    }));
  };

  const handleMetricChange = (
    _event: React.SyntheticEvent,
    newValue: {
      label: string;
      value: string;
    } | null,
  ) => {
    if (newValue) {
      setSelectedMetric(newValue);
      const newLabels = [...labels];
      newLabels[index] = {
        ...label,
        sourceAssetMeasure: {
          assetId: '',
          measureId: newValue.value,
        },
        sourceName: newValue.value,
        sourceLabel: newValue.label,
      };
      setLabels(newLabels);
      handleSettings((prevState) => ({
        ...prevState,
        Label: newLabels,
        isValid: true,
      }));
    }
  };
  const handleSingleMeasurementChange = (
    _event: React.SyntheticEvent,
    newValue: { label: string; id: number } | null,
  ) => {
    // user cleared
    if (!newValue) {
      const newLabels = [...labels];
      newLabels[index] = {
        ...label,
        sourceAssetMeasure: {
          assetId: selectedAsset?.id.toString() ?? '',
          measureId: '',
        },
        sourceName: '',
        sourceLabel: '',
      };
      setLabels(newLabels);

      handleSettings((prevState) => ({
        ...prevState,
        Label: newLabels,
        isValid: false,
      }));
      return;
    }

    const newMeasureId = String(newValue.id);
    const newMeasureLabel = newValue.label;

    const newLabels = [...labels];
    newLabels[index] = {
      ...label,
      sourceAssetMeasure: {
        assetId: selectedAsset?.id.toString() ?? '',
        measureId: newMeasureId,
      },
      sourceName: newMeasureId,
      sourceLabel: newMeasureLabel,
    };
    setLabels(newLabels);

    handleSettings((prevState) => ({
      ...prevState,
      Label: newLabels,
      isValid: true,
    }));
  };

  const handleCalculatedLabelChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    const newLabels = [...labels];
    newLabels[index] = {
      ...label,
      sourceName: value,
      sourceLabel: value,
    };
    setLabels(newLabels);

    handleSettings((prevState) => ({
      ...prevState,
      Label: newLabels,
      isValid: value.trim() !== '',
    }));
  };

  const handleLabelChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    const newLabels = [...labels];
    newLabels[index] = {
      ...label,
      sourceLabel: value,
    };
    setLabels(newLabels);

    handleSettings((prevState) => ({
      ...prevState,
      Label: newLabels,
      isValid: value.trim() !== '',
    }));
  };

  /**
   * 13) Add new row at the end
   */
  const handleAddRow = () => {
    setLabels([
      ...labels,
      {
        sourceFrom: 'Default',
        sourceName: '',
        sourceLabel: '',
        sourceAssetMeasure: { assetId: '', measureId: '' },
      },
    ]);
  };

  const selectedMeasureObj =
    label.sourceAssetMeasure?.measureId && measureData.length > 0
      ? measureData.find((m) => String(m.id) === label.sourceAssetMeasure!.measureId)
      : null;

  const selectedMeasure = selectedMeasureObj
    ? {
        id: selectedMeasureObj.id,
        label: formatMetricLabel(selectedMeasureObj.tag),
      }
    : null;

  const handleChange = (event: SelectChangeEvent<number>, child: React.ReactNode) => {
    const newLabels = [...labels];
    newLabels[index] = {
      ...label,
      aggBy: (event.target.value as number) ?? 1,
    };
    setLabels(newLabels);

    handleSettings((prevState) => ({
      ...prevState,
      Label: newLabels,
    }));
  };

  const handleDashboardOrTemplate = (event: React.ChangeEvent<HTMLInputElement>, value: string) => {
    const updatedLabels = [...labels];
    updatedLabels[index] = {
      ...label,
      linkedDashboard: {
        ...(label.linkedDashboard ?? {}),
        type: value as 'dashboard' | 'template',
        id: label.linkedDashboard?.id ?? 0,
        title: label.linkedDashboard?.title ?? '',
      },
    };
    setLabels(updatedLabels);
    handleSettings((prev) => ({
      ...prev,
      Label: updatedLabels,
    }));
  };

  const handleDashboardChange = () => {
    if (currentDashboard === -2) {
      // If the current dashboard is a template, we don't allow any changes
      return false;
    }
    if (!hasDashboardPermission('dashboard.update', Role.POWER_USER)) {
      return false;
    }
    if (
      widgets.filter((widget) => widget.type !== 'chart' && widget.settings.isDirty).length > 0 ||
      deleteWidgets.length > 0 ||
      widgets.filter((widget) => widget.type === 'chart' && widget.settings?.settings.isDirty)
        .length > 0 ||
      isDashboardStateDirty
    ) {
      setConfirm(true);
      return true;
    }
    return false;
  };

  const openDashboard = () => {
    if (handleDashboardChange()) return;

    const linked = label.linkedDashboard;
    if (!linked) return;

    const { id, title, type, openInNewTab } = linked;
    const path = `/customer/${activeCustomer?.id}/dashboard/${id}`;

    if (openInNewTab) {
      window.open(path, '_blank');
      return;
    }

    if (type === 'dashboard') {
      dispatch(dashboardSlice.actions.setDashboardCrumb({ dashboardId: id, title }));
      dispatch(dashboardSlice.actions.setCurrentDashboardId(id));
      dispatch(dashboardSlice.actions.setCurrentDashboardTitle(title));
      router.push(path);
    }

    if (type === 'template') {
      dispatch(dashboardSlice.actions.setTemplateId(id));
      dispatch(dashboardSlice.actions.setTemplateName(title));
      dispatch(
        dashboardSlice.actions.setWidget({
          widgets: [],
          deleteWidgets: [],
          widgetLayout: [],
          lastWidgetId: 0,
        }),
      );
      router.push(path);
    }
  };

  const dashboardOptions =
    (label.linkedDashboard?.type ?? 'dashboard') === 'dashboard'
      ? dashboardList?.items ?? []
      : dashboardTemplates?.items ?? [];

  const selectedDashboard =
    dashboardOptions.find((d) => d.id === label.linkedDashboard?.id) ?? null;

  return (
    <Box component={Card} sx={{ p: 2, mb: 2 }}>
      <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 1 }}>
        <RadioGroup
          sx={{ display: 'flex', flexDirection: 'row' }}
          value={label.sourceFrom}
          onChange={handleSourceFromChange}
        >
          <FormControlLabel value="Default" control={<Radio />} label="Measure" />
          <FormControlLabel value="Calculated" control={<Radio />} label="Virtual" />
        </RadioGroup>

        <Box>
          {index === labels.length - 1 && (
            <Button color="primary" variant="contained" onClick={handleAddRow} sx={{ mr: 1 }}>
              Add Label
            </Button>
          )}

          <Button
            color="error"
            variant="contained"
            onClick={() => {
              const newLabels = [...labels];
              newLabels.splice(index, 1);
              setLabels(newLabels);
              handleSettings((prevState) => ({
                ...prevState,
                Label: newLabels,
              }));
            }}
          >
            Delete
          </Button>
        </Box>
      </Box>

      {label.sourceFrom === 'Calculated' ? (
        <TextField
          key={index}
          fullWidth
          value={label.sourceName}
          onChange={handleCalculatedLabelChange}
          required={true}
          FormHelperTextProps={{ sx: { color: 'red' } }}
          helperText={label.sourceName === '' ? 'Enter a virtual label' : ''}
        />
      ) : (
        <>
          {mode === 'dashboard' ? (
            <>
              <FormControl fullWidth>
                <Box display="flex" alignItems="center" width="100%" gap={1}>
                  <Select
                    fullWidth
                    labelId={'aggregate-by-label'}
                    id={'aggregate-by-select'}
                    label="Aggregate By"
                    value={label?.aggBy ?? 1}
                    input={
                      <OutlinedInput
                        label="Aggregate By"
                        sx={{
                          mb: 2,
                          p: 0.2,
                          '& legend': {
                            maxWidth: '100%',
                            height: 'fit-content',
                            '& span': {
                              opacity: 1,
                            },
                          },
                        }}
                      />
                    }
                    onChange={handleChange}
                  >
                    {AggByOptions.map((option) => (
                      <MenuItem key={option.value} value={option.value}>
                        {option.label}
                      </MenuItem>
                    ))}
                  </Select>
                </Box>
              </FormControl>
              <Box sx={{ width: '100%', display: 'flex', mb: 1, gap: 1 }}>
                <Autocomplete
                  fullWidth
                  id={`asset-autocomplete-${index}`}
                  loading={isAssetReloading}
                  options={
                    !isAssetReloading
                      ? assetTypesWithPath.map((asset) => ({
                          label: asset.label,
                          id: asset.id,
                        }))
                      : []
                  }
                  getOptionLabel={(option) => option?.label ?? ''}
                  onChange={handleAssetChange}
                  value={selectedAsset}
                  renderInput={(params) => (
                    <TextField
                      {...params}
                      label="Select Asset"
                      variant="outlined"
                      InputProps={{
                        ...params.InputProps,
                        endAdornment: (
                          <>
                            {isAssetReloading && <CircularProgress color="inherit" size={20} />}
                            {params.InputProps.endAdornment}
                          </>
                        ),
                      }}
                    />
                  )}
                />

                <Autocomplete
                  fullWidth
                  loading={loadingMeasure}
                  options={
                    !loadingMeasure
                      ? measureData.map((measure) => ({
                          label: formatMetricLabel(measure.tag),
                          id: measure.id,
                        }))
                      : []
                  }
                  getOptionLabel={(option) => option?.label ?? ''}
                  value={selectedMeasure}
                  onChange={handleSingleMeasurementChange}
                  disabled={!selectedAsset}
                  renderInput={(params) => (
                    <TextField
                      {...params}
                      label="Measurement"
                      variant="outlined"
                      InputProps={{
                        ...params.InputProps,
                        endAdornment: (
                          <>
                            {loadingMeasure && <CircularProgress color="inherit" size={20} />}
                            {params.InputProps.endAdornment}
                          </>
                        ),
                      }}
                    />
                  )}
                />
              </Box>
              <TextField
                key={`${index}-label-override`}
                sx={{ mt: 2 }}
                fullWidth
                label="Label"
                value={label.sourceLabel}
                onChange={handleLabelChange}
                required={true}
                FormHelperTextProps={{ sx: { color: 'red' } }}
                helperText={label.sourceLabel === '' ? 'Enter a label' : ''}
              />

              <Divider sx={{ mt: 4 }} />

              <>
                <Box sx={{ my: 1.5 }}>
                  <FormControl component="fieldset">
                    <RadioGroup
                      row
                      value={label.linkedDashboard?.type ?? 'dashboard'}
                      onChange={handleDashboardOrTemplate}
                      name="dashboardOrTemplate"
                    >
                      <FormControlLabel value="template" control={<Radio />} label="Template" />
                      <FormControlLabel value="dashboard" control={<Radio />} label="Dashboard" />
                    </RadioGroup>
                  </FormControl>
                </Box>
                {settings.mode === 'template' && label.linkedDashboard?.type === 'template' ? (
                  <Box sx={{ display: 'flex', mt: 2 }}>
                    <Autocomplete
                      fullWidth
                      disablePortal
                      loading={isAssetReloading}
                      id="combo-box-demo"
                      options={assetTypesWithPath.map((item) => {
                        return {
                          id: item.value.toString(),
                          label: item.label,
                        };
                      })}
                      value={
                        assetTypesWithPath
                          .map((item) => {
                            return {
                              id: item.value.toString(),
                              label: item.label,
                            };
                          })
                          .find((item) => item.id === settings!.assetOrAssetType?.toString()) ??
                        null
                      }
                      sx={{ p: 2, pb: 0, pt: 0 }}
                      onChange={(event, value) => {
                        handleSettings({
                          ...settings,
                          assetOrAssetType: Number(value?.id) ?? null,
                        });
                      }}
                      renderInput={(params) => <TextField {...params} label="Asset Type" />}
                    />
                  </Box>
                ) : null}
                {settings.mode === 'dashboard' && label.linkedDashboard?.type === 'template' ? (
                  <Box sx={{ display: 'flex', my: 1 }}>
                    <Autocomplete
                      fullWidth
                      id={`asset-autocomplete`}
                      loading={isAssetReloading}
                      options={
                        !isAssetReloading
                          ? assetTypesWithPath.map((asset) => ({
                              label: asset.label,
                              id: asset.id,
                            }))
                          : []
                      }
                      getOptionLabel={(option) => option?.label ?? ''}
                      onChange={(event, value) => {
                        handleSettings({
                          ...settings,
                          assetOrAssetType: value?.id ?? null,
                        });
                      }}
                      value={assetTypesWithPath?.find(
                        (asset) => asset.id === settings!.assetOrAssetType,
                      )}
                      renderInput={(params) => (
                        <TextField {...params} label="Select Asset" variant="outlined" />
                      )}
                    />
                  </Box>
                ) : null}
                <Box sx={{ display: 'flex' }}>
                  <Autocomplete
                    loading={isLoadingDashboards || isLoadingDashboardTemplates}
                    id="dashboards-combo-box"
                    options={dashboardOptions.map((d) => ({
                      id: d.id,
                      title: d.title,
                    }))}
                    getOptionLabel={(option) => option.title}
                    onChange={(_, value) => {
                      const updatedLabels = [...labels];
                      updatedLabels[index] = {
                        ...label,
                        linkedDashboard: value
                          ? {
                              ...(label.linkedDashboard ?? { type: 'dashboard' }),
                              id: value.id,
                              title: value.title,
                            }
                          : undefined,
                      };
                      setLabels(updatedLabels);
                      handleSettings((prev) => ({
                        ...prev,
                        Label: updatedLabels,
                      }));
                    }}
                    value={
                      selectedDashboard
                        ? { id: selectedDashboard.id, title: selectedDashboard.title }
                        : null
                    }
                    sx={{ width: '100%', my: 1.5 }}
                    isOptionEqualToValue={(option, value) => option.id === value.id}
                    loadingText={
                      (label.linkedDashboard?.type ?? 'dashboard') === 'dashboard'
                        ? 'Loading Dashboards...'
                        : 'Loading Dashboard Templates...'
                    }
                    renderInput={(params) => (
                      <TextField
                        {...params}
                        label={
                          label.linkedDashboard?.type === 'template'
                            ? 'Link Dashboard Template'
                            : 'Link Dashboard'
                        }
                        InputProps={{
                          ...params.InputProps,
                          endAdornment: (
                            <>
                              {isLoadingDashboards || isLoadingDashboardTemplates ? (
                                <CircularProgress color="inherit" size={20} />
                              ) : null}
                              {params.InputProps.endAdornment}
                            </>
                          ),
                        }}
                      />
                    )}
                  />

                  {label.linkedDashboard?.id !== null && label.linkedDashboard?.id ? (
                    <IconButton
                      disableRipple
                      disableTouchRipple
                      id={'title-widget-link-icon'}
                      edge="start"
                      color="inherit"
                      sx={{
                        zIndex: 10,
                        ml: 0.5,
                      }}
                      {...(settings.mode === 'dashboard' ? { onClick: openDashboard } : {})}
                    >
                      <Tooltip
                        title={
                          <Typography variant="body2" color="inherit" fontSize={'0.7rem'}>
                            {label.linkedDashboard?.type === 'dashboard' ? (
                              <>Open Dashboard - {label.linkedDashboard?.title}</>
                            ) : (
                              <>Open Dashboard Template- {label.linkedDashboard?.title}</>
                            )}

                            {settings.mode}
                          </Typography>
                        }
                      >
                        <LaunchIcon />
                      </Tooltip>
                    </IconButton>
                  ) : null}
                </Box>
                {label.linkedDashboard?.id !== null &&
                label.linkedDashboard?.id &&
                label.linkedDashboard.type === 'dashboard' ? (
                  <FormControl fullWidth>
                    <FormControlLabel
                      control={
                        <Checkbox
                          color="primary"
                          checked={label.linkedDashboard?.openInNewTab ?? false}
                          onChange={(
                            event: React.ChangeEvent<HTMLInputElement>,
                            checked: boolean,
                          ) => {
                            const updatedLabels = [...labels];
                            updatedLabels[index] = {
                              ...label,
                              linkedDashboard: {
                                ...(label.linkedDashboard ?? {
                                  type: 'dashboard',
                                  id: 0,
                                  title: '',
                                }),
                                openInNewTab: checked,
                              },
                            };
                            setLabels(updatedLabels);
                            handleSettings((prev) => ({
                              ...prev,
                              Label: updatedLabels,
                            }));
                          }}
                        />
                      }
                      label="Open in New Tab"
                    />
                  </FormControl>
                ) : null}
              </>
            </>
          ) : null}
          {mode === 'template' ? (
            <>
              <Autocomplete
                fullWidth
                id={`asset-autocomplete-${index}`}
                loading={isAssetReloading}
                options={Object.keys(metricsIdToName).map((metrics) => ({
                  value: metrics,
                  label: metricsIdToName[metrics],
                }))}
                getOptionLabel={(option) => option?.label ?? ''}
                onChange={handleMetricChange}
                value={selectedMetric ?? null}
                renderInput={(params) => (
                  <TextField {...params} label="Metric" variant="outlined" />
                )}
              />

              <Box sx={{ my: 1.5 }}>
                <FormControl component="fieldset">
                  <RadioGroup
                    row
                    value={label.linkedDashboard?.type ?? 'dashboard'}
                    onChange={handleDashboardOrTemplate}
                    name="dashboardOrTemplate"
                  >
                    <FormControlLabel value="template" control={<Radio />} label="Template" />
                    <FormControlLabel value="dashboard" control={<Radio />} label="Dashboard" />
                  </RadioGroup>
                </FormControl>
              </Box>

              {label.linkedDashboard?.type === 'template' && (
                <Box sx={{ display: 'flex' }}>
                  <Autocomplete
                    fullWidth
                    loading={isAssetReloading}
                    id="asset-type-autocomplete"
                    options={assetTypesWithPath.map((item) => ({
                      id: item.value.toString(),
                      label: item.label,
                    }))}
                    value={
                      assetTypeTemplate
                        ? {
                            id: assetTypeTemplate.toString(),
                            label:
                              assetTypesTemplateWithPath.find(
                                (item) => item.value === assetTypeTemplate,
                              )?.label ?? '',
                          }
                        : assetTypesWithPath
                            .map((item) => ({
                              id: item.value.toString(),
                              label: item.label,
                            }))
                            .find((item) => item.id === settings.assetOrAssetType?.toString()) ??
                          null
                    }
                    disabled={!!assetTypeTemplate}
                    onChange={(event, value) => {
                      if (!assetTypeTemplate) {
                        // setAssetType(Number(value?.id));
                        handleSettings({
                          ...settings,
                          assetOrAssetType: Number(value?.id) ?? null,
                        });
                      }
                    }}
                    isOptionEqualToValue={(option, value) => option.id === value.id}
                    renderInput={(params) => <TextField {...params} label="Asset Type" />}
                  />
                </Box>
              )}

              <Box sx={{ display: 'flex' }}>
                <Autocomplete
                  loading={isLoadingDashboards || isLoadingDashboardTemplates}
                  id="template-dashboards-combo-box"
                  options={dashboardOptions.map((d) => ({
                    id: d.id,
                    title: d.title,
                  }))}
                  getOptionLabel={(option) => option.title}
                  onChange={(_, value) => {
                    const updatedLabels = [...labels];
                    updatedLabels[index] = {
                      ...label,
                      linkedDashboard: value
                        ? {
                            ...(label.linkedDashboard ?? { type: 'dashboard' }),
                            id: value.id,
                            title: value.title,
                          }
                        : undefined,
                    };
                    setLabels(updatedLabels);
                    handleSettings((prev) => ({
                      ...prev,
                      Label: updatedLabels,
                    }));
                  }}
                  value={
                    selectedDashboard
                      ? { id: selectedDashboard.id, title: selectedDashboard.title }
                      : null
                  }
                  sx={{ width: '100%', my: 1.5 }}
                  isOptionEqualToValue={(option, value) => option.id === value.id}
                  loadingText={
                    (label.linkedDashboard?.type ?? 'dashboard') === 'dashboard'
                      ? 'Loading Dashboards...'
                      : 'Loading Dashboard Templates...'
                  }
                  renderInput={(params) => (
                    <TextField
                      {...params}
                      label={
                        label.linkedDashboard?.type === 'template'
                          ? 'Link Dashboard Template'
                          : 'Link Dashboard'
                      }
                      InputProps={{
                        ...params.InputProps,
                        endAdornment: (
                          <>
                            {isLoadingDashboards || isLoadingDashboardTemplates ? (
                              <CircularProgress color="inherit" size={20} />
                            ) : null}
                            {params.InputProps.endAdornment}
                          </>
                        ),
                      }}
                    />
                  )}
                />

                {label.linkedDashboard?.id ? (
                  <IconButton
                    disableRipple
                    disableTouchRipple
                    edge="start"
                    color="inherit"
                    sx={{ zIndex: 10, ml: 0.5 }}
                    onClick={() => {
                      const { id, title, type, openInNewTab } = label.linkedDashboard!;
                      const path = `/customer/${activeCustomer?.id}/dashboard/${id}`;
                      if (openInNewTab) {
                        window.open(path, '_blank');
                      } else {
                        router.push(path);
                      }
                    }}
                  >
                    <Tooltip
                      title={
                        <Typography variant="body2" color="inherit" fontSize={'0.7rem'}>
                          {label.linkedDashboard?.type === 'dashboard'
                            ? `Open Dashboard - ${label.linkedDashboard?.title}`
                            : `Open Template - ${label.linkedDashboard?.title}`}
                        </Typography>
                      }
                    >
                      <LaunchIcon />
                    </Tooltip>
                  </IconButton>
                ) : null}
              </Box>

              {label.linkedDashboard?.type === 'dashboard' && label.linkedDashboard?.id && (
                <FormControl fullWidth>
                  <FormControlLabel
                    control={
                      <Checkbox
                        color="primary"
                        checked={label.linkedDashboard?.openInNewTab ?? false}
                        onChange={(
                          event: React.ChangeEvent<HTMLInputElement>,
                          checked: boolean,
                        ) => {
                          const updatedLabels = [...labels];
                          updatedLabels[index] = {
                            ...label,
                            linkedDashboard: {
                              ...(label.linkedDashboard ?? {
                                type: 'dashboard',
                                id: 0,
                                title: '',
                              }),
                              openInNewTab: checked,
                            },
                          };
                          setLabels(updatedLabels);
                          handleSettings((prev) => ({
                            ...prev,
                            Label: updatedLabels,
                          }));
                        }}
                      />
                    }
                    label="Open in New Tab"
                  />
                </FormControl>
              )}
            </>
          ) : null}
        </>
      )}

      <CustomDialog
        title="You have unsaved changes."
        content={<Typography color={'error'}>Do you want to still proceed?</Typography>}
        dialogActions={
          <>
            <Button
              onClick={() => {
                setConfirm(false);
              }}
              variant="outlined"
              startIcon={<CancelIcon />}
            >
              Cancel
            </Button>
            <Button
              onClick={() => {
                setConfirm(false);
                const linked = label.linkedDashboard;
                if (!linked) return;

                const { id, title, type, openInNewTab } = linked;
                const path = `/customer/${activeCustomer?.id}/dashboard/${id}`;

                if (openInNewTab) {
                  window.open(path, '_blank');
                  return;
                }

                if (type === 'dashboard') {
                  dispatch(dashboardSlice.actions.setDashboardCrumb({ dashboardId: id, title }));
                  dispatch(dashboardSlice.actions.setCurrentDashboardId(id));
                  dispatch(dashboardSlice.actions.setCurrentDashboardTitle(title));
                  router.push(path);
                }

                if (type === 'template') {
                  dispatch(dashboardSlice.actions.setTemplateId(id));
                  dispatch(dashboardSlice.actions.setTemplateName(title));
                  dispatch(
                    dashboardSlice.actions.setWidget({
                      widgets: [],
                      deleteWidgets: [],
                      widgetLayout: [],
                      lastWidgetId: 0,
                    }),
                  );
                  router.push(path);
                }
              }}
              variant="contained"
              color="error"
            >
              Proceed
            </Button>
          </>
        }
        onClose={() => setConfirm(false)}
        open={confirm}
      />
    </Box>
  );
};

export default SankeySettingsHelper;
