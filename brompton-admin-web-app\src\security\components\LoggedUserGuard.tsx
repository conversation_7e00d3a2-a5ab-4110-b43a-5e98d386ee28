export const LoggedUserGuard = (props: { children: JSX.Element }) => {
  // const router = useRouter();
  // const dispatch = useDispatch();
  // const { setUserDetails } = dashboardSlice.actions;
  //
  // const { data: userDetails, error } = useGetMeQuery();
  //
  // useEffect(() => {
  //   if (userDetails) {
  //     debugger;
  //     dispatch(setUserDetails(userDetails));
  //     router.push('/dashboard');
  //   }
  // }, [userDetails]);
  //
  // useEffect(() => {
  //   if (error) {
  //     debugger;
  //     router.push('/login');
  //   }
  // }, [error]);

  return <>{props.children}</>;
};
