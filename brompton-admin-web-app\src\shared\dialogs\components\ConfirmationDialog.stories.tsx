import type { Meta, StoryObj } from '@storybook/react';
import { ConfirmationDialog } from './ConfirmationDialog';

const meta: Meta<typeof ConfirmationDialog> = {
  component: ConfirmationDialog,
  argTypes: { onDelete: { action: 'delete' }, onCancel: { action: 'cancel' } },
};

export default meta;
type Story = StoryObj<typeof ConfirmationDialog>;

export const ClosedDialog: Story = {
  args: {
    open: false,
    title: 'Closed dialog',
  },
};

export const OpenDeleteConfirmationDialog: Story = {
  args: {
    open: true,
    title: 'Delete measurement',
    description: 'Are you sure you wish to delete this measurement?',
  },
};
