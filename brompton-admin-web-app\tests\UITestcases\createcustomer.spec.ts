import { test, expect } from '@playwright/test';
import userEvent from '@testing-library/user-event';

test('createcustomer', async ({ page }) => {
  // opening the URL
  await page.goto('https://test.pivotol.ai/login'); // 60 seconds
  // Go to the username  and password
  await page.getByLabel('Username *').click();
  await page.getByLabel('Username *').fill('test');
  await page.getByLabel('Password *').click();
  await page.getByLabel('Password *').fill('asdfasdf');
  // click on Login Button
  await page.click('#__next > div > div > form > div > button');
  await page.waitForTimeout(3000);
  // click on new dashboard
  await page.getByText('Add Dashboard').click();
  await page.waitForTimeout(3000);
  //click on customer
  await page.click('//*[@id="__next"]/div/div[1]/div/ul/li[2]/a/div[2]/span');
  await page.waitForTimeout(2000);
  // create new customer
  await page.click('//*[@id="__next"]/div/div[2]/p/button');
  await page.getByLabel('Name id *').click();
  await page.getByLabel('Name id *').fill('testautocust');
  await page.getByLabel('Name *').click();
  await page.getByLabel('Name *').fill('testautocust');
  await page.getByLabel('Address *').click();
  await page.getByLabel('Address *').fill('testauto');
  await page.waitForSelector('.MuiInputBase-input.MuiOutlinedInput-input', { timeout: 10000 });
  // select choose file
  const fileInput = page.locator('input[type="file"]');
  await fileInput.setInputFiles('C:\\Users\\<USER>\\Pictures\\Saved Pictures\\logo.jfif');
  // click on add button
  await page.getByRole('button', { name: 'Add' }).click();
  await page.waitForTimeout(3000);
  await page.close();
});
