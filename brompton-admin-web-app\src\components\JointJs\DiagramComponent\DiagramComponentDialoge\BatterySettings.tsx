import React from 'react';
import { dia } from '@joint/core';
import {
  Box,
  TextField,
  FormControl,
  FormLabel,
  RadioGroup,
  FormControlLabel,
  Radio,
} from '@mui/material';
import Battery from '~/components/CreateElement/Battery';

type BatterySettingsProps = {
  selectedElement: dia.Element<dia.Element.Attributes, dia.ModelSetOptions>;
  handleAttrChangeElement: (attr: string, value: string) => void;
};

const BatterySettings = ({ selectedElement, handleAttrChangeElement }: BatterySettingsProps) => {
  const {
    maxCapacity = 100,
    orientation = 'vertical',
    style = 'solid',
  } = selectedElement.get('data') ?? {};

  if (!(selectedElement instanceof Battery)) {
    return null;
  }

  const handleMaxCapacityChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newMaxCapacity = Math.min(100000, Math.max(0, parseInt(e.target.value) || 0));
    handleAttrChangeElement('maxCapacity', newMaxCapacity.toString());
    selectedElement.prop('data/maxCapacity', newMaxCapacity);
  };

  const handleOrientationChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newOrientation = e.target.value as 'vertical' | 'horizontal';
    handleAttrChangeElement('orientation', newOrientation);
    selectedElement.prop('data/orientation', newOrientation);
  };

  const handleStyleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newStyle = e.target.value as 'bar' | 'solid';
    handleAttrChangeElement('style', newStyle);
    selectedElement.prop('data/style', newStyle);
  };

  return (
    <Box mb={2} display="flex" flexDirection="column" gap={2}>
      <TextField
        label="Maximum Capacity"
        type="number"
        value={maxCapacity}
        onChange={handleMaxCapacityChange}
        inputProps={{ min: 0, max: 100000 }}
        fullWidth
      />
      <FormControl component="fieldset">
        <FormLabel component="legend">Orientation</FormLabel>
        <RadioGroup value={orientation} onChange={handleOrientationChange} row>
          <FormControlLabel value="vertical" control={<Radio />} label="Vertical" />
          <FormControlLabel value="horizontal" control={<Radio />} label="Horizontal" />
        </RadioGroup>
      </FormControl>

      <FormControl component="fieldset">
        <FormLabel component="legend">Fill Style</FormLabel>
        <RadioGroup value={style} onChange={handleStyleChange} row>
          <FormControlLabel value="solid" control={<Radio />} label="Solid" />
          <FormControlLabel value="bar" control={<Radio />} label="Bar" />
        </RadioGroup>
      </FormControl>
    </Box>
  );
};

export default BatterySettings;
