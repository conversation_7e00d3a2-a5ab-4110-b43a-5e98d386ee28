import React from 'react';
import { HeatmapChartContainer } from '~/layout/containers/chart/HeatmapChartContainer';
import { BarChartContainer } from '~/layout/containers/chart/BarChartContainer';
import { ScatterChartContainer } from '~/layout/containers/chart/ScatterChartContainer';
import { ChartWidget } from '~/types/widgets';
import { GaugeChartContainer } from './GaugeChartContainer';
import { BulletChartContainer } from './BulletChartContainer';
import SankeyChartWidgetContainer from './SankeyChartWidgetContainer';

type ChartWidgetContainerProps = {
  id: string;
  settings: ChartWidget;
};

export function ChartWidgetContainer({ id, settings }: ChartWidgetContainerProps): JSX.Element {
  const { chartType, settings: chartSettings } = settings;

  return (
    <div style={{ height: '100%' }}>
      {chartType === 'bar' && <BarChartContainer id={id} settings={chartSettings} />}
      {chartType === 'scatter' && <ScatterChartContainer id={id} settings={chartSettings} />}
      {chartType === 'heatmap' && <HeatmapChartContainer id={id} settings={chartSettings} />}
      {chartType === 'indicator' && <GaugeChartContainer id={id} settings={chartSettings} />}
      {chartType === 'bullet' && <BulletChartContainer id={id} settings={chartSettings} />}
      {chartType === 'sankey' && <SankeyChartWidgetContainer id={id} settings={chartSettings} />}
    </div>
  );
}
