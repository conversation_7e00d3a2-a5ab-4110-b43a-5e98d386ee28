import { yupResolver } from '@hookform/resolvers/yup';
import { Button, DialogActions, DialogContent, TextField } from '@mui/material';
import { Dispatch, SetStateAction } from 'react';
import { Controller, useForm } from 'react-hook-form';
import * as yup from 'yup';
import { useCloneAssetTemplateByAssetTypeMutation } from '~/redux/api/assetsApi';
import { AlertSnackbar } from '~/shared/snackbars/components/AlertSnackbar';
import { useSnackbar } from '~/shared/snackbars/snackbar-hooks';
import CustomDialog from '../common/CustomDialog';

// ✅ Define validation schema using Yup
const schema = yup.object().shape({
  model_number: yup.string().required('Model Number is required'),
  manufacturer: yup.string().required('Manufacturer is required'),
});

type CloneAssetTemplateFormProps = {
  open: boolean;
  setOpenCloneDialog: Dispatch<SetStateAction<boolean>>;
  assetTypeId: number;
  templateId: number;
  defaultModelNumber: string;
  defaultManufacturer: string;
};

const CloneAssetTemplateForm = ({
  open,
  setOpenCloneDialog,
  assetTypeId,
  templateId,
  defaultModelNumber,
  defaultManufacturer,
}: CloneAssetTemplateFormProps) => {
  const [snackbarState, showSuccessAlert, showErrorAlert] = useSnackbar();
  const [cloneAssetTemplate, { isLoading }] = useCloneAssetTemplateByAssetTypeMutation();

  const {
    handleSubmit,
    control,
    reset,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(schema),
    defaultValues: {
      model_number: defaultModelNumber,
      manufacturer: defaultManufacturer,
    },
  });

  // ✅ Handle form submission
  const onSubmit = async (data: { model_number: string; manufacturer: string }) => {
    try {
      await cloneAssetTemplate({
        assetTypeId: assetTypeId.toString(),
        templateId: templateId.toString(),
        model_number: data.model_number,
        manufacturer: data.manufacturer,
      }).unwrap();
      showSuccessAlert('Asset template cloned successfully!');
      setOpenCloneDialog(false);
      reset();
    } catch (error: any) {
      showErrorAlert(error?.data?.exception ?? 'Failed to clone template.');
    }
  };

  return (
    <>
      <AlertSnackbar {...snackbarState} />
      <CustomDialog
        open={open}
        dialogActions={null}
        onClose={() => {
          setOpenCloneDialog(false);
          reset();
        }}
        title="Clone Asset Template"
        maxWidth="sm"
        content={
          <form onSubmit={handleSubmit(onSubmit)}>
            <DialogContent dividers>
              <Controller
                name="model_number"
                control={control}
                render={({ field }) => (
                  <TextField
                    {...field}
                    label="Model Number"
                    fullWidth
                    error={!!errors.model_number}
                    helperText={errors.model_number?.message}
                    margin="dense"
                  />
                )}
              />
              <Controller
                name="manufacturer"
                control={control}
                render={({ field }) => (
                  <TextField
                    {...field}
                    label="Manufacturer"
                    fullWidth
                    error={!!errors.manufacturer}
                    helperText={errors.manufacturer?.message}
                    margin="dense"
                  />
                )}
              />
            </DialogContent>
            <DialogActions>
              <Button onClick={() => setOpenCloneDialog(false)} color="primary" variant="outlined">
                Cancel
              </Button>
              <Button type="submit" color="primary" variant="contained" disabled={isLoading}>
                {isLoading ? 'Cloning...' : 'Clone'}
              </Button>
            </DialogActions>
          </form>
        }
      />
    </>
  );
};

export default CloneAssetTemplateForm;
