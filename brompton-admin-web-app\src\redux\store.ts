import { persistReducer, persistStore } from 'redux-persist';
import { PersistConfig } from 'redux-persist/es/types';
import storage from 'redux-persist/lib/storage';

import { combineReducers, configureStore } from '@reduxjs/toolkit';
import { authApi } from '~/redux/api/authApi';
import { dashboardSlice } from '~/redux/slices/dashboardSlice';
import { dashboardListSlice } from './slices/dashboardListSlice';
import { diagramSlice } from './slices/diagramSlice';
import { solarSlice } from './slices/solarSlice';

const rootReducer = combineReducers({
  [dashboardSlice.name]: dashboardSlice.reducer,
  [dashboardListSlice.name]: dashboardListSlice.reducer,
  [authApi.reducerPath]: authApi.reducer,
  [solarSlice.name]: solarSlice.reducer,
  [diagramSlice.name]: diagramSlice.reducer,
});

const persistConfig: PersistConfig<ReturnType<typeof rootReducer>> = {
  key: 'root',
  storage,
  whitelist: ['dashboard', 'dashboardList'],
};

const persistedReducer = persistReducer(persistConfig, rootReducer);

export const store = configureStore({
  reducer: persistedReducer,
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      thunk: true,
      immutableCheck: false, // ✅ FIXED typo here
      serializableCheck: {
        ignoredActions: [
          'dashboard/setChartStartDate',
          'dashboard/setChartEndDate',
          'dashboard/setTemplateChartStartDate',
          'dashboard/setTemplateChartEndDate',
          'persist/PERSIST',
          'persist/REHYDRATE',
          'diagram/setDiagram',
          'diagram/setSelectedElement',
          'diagram/setSelectedLink',
          'diagram/setHoveredLink',
          'diagram/setElementAttrs',
          'diagram/setIconVisible',
          'diagram/addSelectedElement',
        ],
        ignoredPaths: [
          'dashboard.chart.startDate',
          'dashboard.chart.endDate',
          'dashboard.template.chart.startDate',
          'dashboard.template.chart.endDate',
          'diagram.diagram',
          'diagram.selectedElement',
          'diagram.hoveredLink',
          'diagram.selectedLink',
        ],
      },
    }).concat(authApi.middleware),
});

export const persistor = persistStore(store);

export type RootState = ReturnType<typeof store.getState>;
