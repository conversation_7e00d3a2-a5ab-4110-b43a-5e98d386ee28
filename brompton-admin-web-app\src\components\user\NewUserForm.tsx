import { yupResolver } from '@hookform/resolvers/yup';
import { Alert, Autocomplete, Box, Button, TextField } from '@mui/material';
import { Controller, useForm } from 'react-hook-form';
import { AlertMessage } from '~/shared/forms/types';
import React, { useEffect } from 'react';
import { User, userSchema } from '~/types/users';

type NewUserFormProps = {
  loading: boolean;
  alertMessage: AlertMessage | undefined;
  onValidSubmit: (data: any) => Promise<unknown>;
  customerItemList: { id: number; label: string }[];
  onClose?: () => void;
  resetForm?: boolean;
};

const roleList = [
  { id: 'USER', label: 'USER' },
  { id: 'ADMIN', label: 'ADMIN' },
  { id: 'POWER_USER', label: 'POWER_USER' },
];

export default function NewUserForm({
  loading,
  alertMessage,
  onValidSubmit,
  customerItemList,
  onClose,
  resetForm,
}: NewUserFormProps): JSX.Element {
  const { control, handleSubmit, reset, getValues, setValue } = useForm<User>({
    defaultValues: {
      username: '',
      password: '',
      first_name: '',
      last_name: '',
      customer_ids_user: [],
      customer_ids_admin: [],
      customer_ids_power_user: [],
      email: '',
      country_code: '',
      phone_no: '',
      user_role: 'USER',
    },
    resolver: yupResolver(userSchema),
  });
  useEffect(() => {
    if (resetForm) {
      reset();
    }
  }, [resetForm]);
  return (
    <form
      autoComplete="off"
      onSubmit={handleSubmit(async (data) => {
        try {
          const admin_customers = data.customer_ids_admin;
          const user_customers = data.customer_ids_user;
          const power_user_customers = data.customer_ids_power_user;
          await onValidSubmit({
            username: data.username,
            password: data.password,
            first_name: data.first_name,
            last_name: data.last_name,
            phone_no: data.phone_no,
            country_code: data.country_code,
            scoped_roles: [
              {
                role: 'ADMIN',
                customer_ids: admin_customers,
              },
              {
                role: 'USER',
                customer_ids: user_customers,
              },
              {
                role: 'POWER_USER',
                customer_ids: power_user_customers,
              },
            ],
            email: data.email,
          });
          // reset();
        } catch (err) {}
      })}
      noValidate
    >
      <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
        <Controller
          name="first_name"
          control={control}
          render={({ field: { onChange, onBlur, value }, fieldState }) => (
            <TextField
              error={!!fieldState.error}
              helperText={fieldState.error?.message}
              onChange={onChange}
              onBlur={onBlur}
              value={value}
              label="First Name"
              variant="outlined"
              margin="normal"
              disabled={loading}
              fullWidth
              required
            />
          )}
        />

        <Controller
          name="last_name"
          control={control}
          render={({ field: { onChange, onBlur, value }, fieldState }) => (
            <TextField
              error={!!fieldState.error}
              helperText={fieldState.error?.message}
              onChange={onChange}
              onBlur={onBlur}
              value={value}
              label="Last Name"
              variant="outlined"
              margin="normal"
              disabled={loading}
              fullWidth
              required
            />
          )}
        />

        <Controller
          name="username"
          control={control}
          render={({ field: { onChange, onBlur, value }, fieldState }) => (
            <TextField
              error={!!fieldState.error}
              helperText={fieldState.error?.message}
              onChange={onChange}
              onBlur={onBlur}
              value={value}
              label="Username"
              variant="outlined"
              margin="normal"
              autoComplete="off"
              disabled={loading}
              fullWidth
              required
            />
          )}
        />

        <Controller
          name="password"
          control={control}
          render={({ field: { onChange, onBlur, value }, fieldState }) => (
            <TextField
              error={!!fieldState.error}
              helperText={fieldState.error?.message}
              onChange={onChange}
              onBlur={onBlur}
              value={value}
              type="password"
              label="Password"
              variant="outlined"
              margin="normal"
              autoComplete="new-password"
              disabled={loading}
              fullWidth
              required
            />
          )}
        />

        <Controller
          name="email"
          control={control}
          render={({ field: { onChange, onBlur, value }, fieldState }) => (
            <TextField
              error={!!fieldState.error}
              helperText={fieldState.error?.message}
              onChange={onChange}
              onBlur={onBlur}
              value={value}
              type="email"
              label="Email"
              variant="outlined"
              margin="normal"
              disabled={loading}
              fullWidth
              required
            />
          )}
        />
        <Box sx={{ display: 'flex', gap: 2, width: '100%' }}>
          <Controller
            name="country_code"
            control={control}
            render={({ field: { onChange, onBlur, value }, fieldState }) => (
              <TextField
                sx={{ width: '30%' }}
                error={!!fieldState.error}
                helperText={fieldState.error?.message}
                onChange={onChange}
                onBlur={onBlur}
                value={value}
                type="text"
                label="Country Code"
                variant="outlined"
                margin="normal"
                disabled={loading}
                fullWidth
                required
              />
            )}
          />

          <Controller
            name="phone_no"
            control={control}
            render={({ field: { onChange, onBlur, value }, fieldState }) => (
              <TextField
                sx={{ width: '70%' }}
                error={!!fieldState.error}
                helperText={fieldState.error?.message}
                onChange={onChange}
                onBlur={onBlur}
                value={value}
                type="text"
                label="Phone Number"
                variant="outlined"
                margin="normal"
                disabled={loading}
                fullWidth
                required
              />
            )}
          />
        </Box>
        <Controller
          name="customer_ids_user"
          control={control}
          render={({ field }) => (
            <Autocomplete
              sx={{
                mt: 2,
              }}
              fullWidth
              multiple
              options={customerItemList}
              getOptionLabel={(option) => option.label}
              value={customerItemList.filter((item) => field.value.includes(item.id))}
              onChange={(_, value) => {
                field.onChange(value.map((item) => item.id));
                const adminCustomers = getValues('customer_ids_admin');
                const powerUserCustomers = getValues('customer_ids_power_user');
                const filteredAdminCustomers = adminCustomers.filter(
                  (customer) => !value.find((item) => item.id === customer),
                );
                const filteredPowerUserCustomers = powerUserCustomers.filter(
                  (customer) => !value.find((item) => item.id === customer),
                );
                setValue('customer_ids_admin', filteredAdminCustomers);
                setValue('customer_ids_power_user', filteredPowerUserCustomers);
              }}
              renderInput={(params) => <TextField {...params} label="Select Customer for User" />}
              isOptionEqualToValue={(option, value) => option.id === value.id}
            />
          )}
        />

        <Controller
          name="customer_ids_power_user"
          control={control}
          render={({ field }) => (
            <Autocomplete
              sx={{
                mt: 3,
              }}
              value={customerItemList.filter((item) => field.value.includes(item.id))}
              fullWidth
              multiple
              options={customerItemList}
              getOptionLabel={(option) => option.label}
              onChange={(_, value) => {
                field.onChange(value.map((item) => item.id));
                const userCustomers = getValues('customer_ids_user');
                const adminCustomers = getValues('customer_ids_admin');
                const filteredUserCustomers = userCustomers.filter(
                  (customer) => !value.find((item) => item.id === customer),
                );
                const filteredAdminCustomers = adminCustomers.filter(
                  (customer) => !value.find((item) => item.id === customer),
                );
                setValue('customer_ids_user', filteredUserCustomers, {
                  shouldValidate: true,
                });
                setValue('customer_ids_admin', filteredAdminCustomers, {
                  shouldValidate: true,
                });
              }}
              renderInput={(params) => (
                <TextField {...params} label="Select Customer for Power User" />
              )}
              isOptionEqualToValue={(option, value) => option.id === value.id}
            />
          )}
        />

        <Controller
          name="customer_ids_admin"
          control={control}
          render={({ field }) => (
            <Autocomplete
              sx={{
                mt: 3,
              }}
              fullWidth
              multiple
              value={customerItemList.filter((item) => field.value.includes(item.id))}
              options={customerItemList}
              getOptionLabel={(option) => option.label}
              onChange={(_, value) => {
                field.onChange(value.map((item) => item.id));
                const userCustomers = getValues('customer_ids_user');
                const powerUserCustomers = getValues('customer_ids_power_user');
                const filteredUserCustomers = userCustomers.filter(
                  (customer) => !value.find((item) => item.id === customer),
                );
                const filteredPowerUserCustomers = powerUserCustomers.filter(
                  (customer) => !value.find((item) => item.id === customer),
                );
                setValue('customer_ids_user', filteredUserCustomers);
                setValue('customer_ids_power_user', filteredPowerUserCustomers);
              }}
              renderInput={(params) => <TextField {...params} label="Select Customer for Admin" />}
              isOptionEqualToValue={(option, value) => option.id === value.id}
            />
          )}
        />
        <Box sx={{ width: '100%', display: 'flex', gap: 2, mt: 3 }}>
          <Button fullWidth type="submit" variant="contained" size="large" disabled={loading}>
            Submit
          </Button>
          <Button
            fullWidth
            variant="outlined"
            onClick={() => {
              reset();
              onClose && onClose();
            }}
          >
            Cancel
          </Button>
        </Box>
        {alertMessage && (
          <Alert severity={alertMessage.severity} sx={{ mt: 3 }}>
            {alertMessage.message}
          </Alert>
        )}
      </Box>
    </form>
  );
}
