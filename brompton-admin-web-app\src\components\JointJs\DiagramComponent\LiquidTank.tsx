import { dia, util } from '@joint/core';

export type LiquidTankConfig = {
  barSetting: {
    direction: 'vertical' | 'horizontal';
  };
  colors?: {
    low?: string;
    medium?: string;
    high?: string;
    full?: string;
  };
  level: number;
  maxCapacity: number; // Added maxCapacity to the configuration
  showProgress?: boolean; // Added showProgress to the configuration
  rangePicker: Array<{
    value: number;
    operator: '>' | '>=' | '<' | '<=' | '==';
    color: string;
  }>;
};

export default class LiquidTank extends dia.Element {
  private interval: NodeJS.Timeout | null = null;
  barSettings: {
    direction: 'vertical' | 'horizontal';
  };
  rangePicker: Array<{
    value: number;
    color: string;
    operator: '>' | '>=' | '<' | '<=' | '==' | '!=';
  }>;
  maxCapacity: number; // Added maxCapacity property
  showProgress: boolean; // Added showProgress property

  constructor(
    attributes = {},
    options = {},
    config: LiquidTankConfig = {
      barSetting: {
        direction: 'vertical',
      },
      colors: { low: '#ffa500', medium: '#ffff00', high: '#008000', full: '#ff0000' },
      level: 0,
      maxCapacity: 100, // Default maxCapacity set to 100
      showProgress: true, // Default to show progress as true
      rangePicker: [],
    },
  ) {
    super(attributes, options);
    this.barSettings = {
      direction: config.barSetting.direction ?? 'vertical',
    };
    this.rangePicker = config.rangePicker;
    this.maxCapacity = config.maxCapacity;
    this.showProgress = config.showProgress !== undefined ? config.showProgress : true; // Default to true
    this.level = config.level;
    this.startProgress();
  }

  defaults(): Partial<dia.Element.Attributes> {
    return {
      ...super.defaults,
      type: 'LiquidTank',
      size: {
        width: 160,
        height: 300,
      },
      level: 0,
      attrs: {
        root: {
          magnetSelector: 'body',
        },
        legs: {
          fill: 'none',
          stroke: '#350100',
          strokeWidth: 8,
          strokeLinecap: 'round',
          d: 'M 20 calc(h) l -5 10 M calc(w - 20) calc(h) l 5 10',
        },
        body: {
          stroke: 'gray',
          strokeWidth: 4,
          x: 0,
          y: 0,
          width: 'calc(w)',
          height: 'calc(h)',
          rx: 120,
          ry: 10,
          fill: {
            type: 'linearGradient',
            stops: [
              { offset: '0%', color: 'gray' },
              { offset: '30%', color: 'white' },
              { offset: '70%', color: 'white' },
              { offset: '100%', color: 'gray' },
            ],
          },
        },
        border: {
          x: 'calc(w / 4)',
          y: 10,
          width: 'calc(w / 2)',
          height: 'calc(h - 20)',
          stroke: '#350100',
          strokeWidth: 2,
          rx: 10,
          ry: 10,
          fill: 'none',
          display: this.showProgress ? 'block' : 'none', // Show or hide border based on showProgress
        },
        fill: {
          x: 'calc(w / 4)',
          y: 10,
          width: 'calc(w / 2)',
          height: 'calc(h - 20)',
          fill: 'orange',
          clipPath: 'inset(100% 0 0 0)',
          rx: 10,
          ry: 10,
          display: this.showProgress ? 'block' : 'none', // Show or hide fill based on showProgress
        },
        label: {
          text: 'Tank 1',
          textAnchor: 'middle',
          textVerticalAnchor: 'top',
          x: 'calc(w / 2)',
          y: 'calc(h + 10)',
          fontSize: 14,
          fontFamily: 'sans-serif',
          fill: '#350100',
        },
        levelLabel: {
          text: '0',
          textAnchor: 'middle',
          x: 'calc(w / 2)',
          y: 'calc(h / 2)',
          fontSize: 20,
          fontFamily: 'sans-serif',
          fill: '#000000',
          display: this.showProgress ? 'block' : 'none', // Show or hide level label based on showProgress
        },
      },
    };
  }

  preinitialize() {
    this.markup = util.svg/* xml */ `
      <path @selector="legs"/>
      <rect @selector="body"/>
      <rect @selector="border"/>
      <rect @selector="fill"/>
      <text @selector="label" />
      <text @selector="levelLabel" />
    `;
  }

  get level() {
    return this.get('level') || 0;
  }

  set level(level) {
    const maxLevel = Math.min(level, this.maxCapacity);
    const newLevel = Math.max(0, maxLevel);
    this.set('level', newLevel);

    if (this.showProgress) {
      const clipPathInset = 100 - (newLevel / this.maxCapacity) * 100;
      if (this.barSettings.direction === 'horizontal') {
        this.attr('fill/clipPath', `inset(0 ${clipPathInset}% 0 0)`);
      } else {
        this.attr('fill/clipPath', `inset(${clipPathInset}% 0 0 0)`);
      }
    }

    // Apply rangePicker formatting
    this.applyRangePickerColor(newLevel);
    this.attr('levelLabel/text', `${newLevel}`);
  }

  startProgress(random = true) {
    if (this.interval) {
      clearInterval(this.interval);
    }
    if (random) {
      this.interval = setInterval(() => {
        const randomLevel = Math.floor(Math.random() * (this.maxCapacity + 1));
        this.level = randomLevel;
      }, 1000);
    }
  }

  stopProgress() {
    if (this.interval) {
      clearInterval(this.interval);
      this.interval = null;
    }
  }

  updateRangePicker(
    newRangePicker: Array<{
      value: number;
      color: string;
      operator: '>' | '>=' | '<' | '<=' | '==' | '!=';
    }>,
  ) {
    this.rangePicker = newRangePicker;
    this.level = this.level;
  }

  updateDirection(direction: 'horizontal' | 'vertical') {
    this.barSettings = {
      direction,
    };
    if (direction === 'horizontal') {
      this.attr('border/width', 'calc(w - 40)');
      this.attr('border/height', 'calc(h / 4)');
      this.attr('border/x', 20);
      this.attr('border/y', 'calc(h / 2)');

      this.attr('fill/width', 'calc(w - 40)');
      this.attr('fill/height', 'calc(h / 4)');
      this.attr('fill/x', 20);
      this.attr('fill/y', 'calc(h / 2)');
    } else if (direction === 'vertical') {
      this.attr('border/width', 'calc(w / 2)');
      this.attr('border/height', 'calc(h - 20)');
      this.attr('border/x', 'calc(w / 4)');
      this.attr('border/y', 10);
      this.attr('fill/width', 'calc(w / 2)');
      this.attr('fill/height', 'calc(h - 20)');
      this.attr('fill/x', 'calc(w / 4)');
      this.attr('fill/y', 10);
    }
    this.level = this.level;
  }

  applyRangePickerColor(level: number) {
    if (!this.showProgress) {
      return; // Do nothing if progress is not to be shown
    }

    let fillColor = 'orange';
    for (let i = this.rangePicker.length - 1; i >= 0; i--) {
      const condition = this.rangePicker[i];
      if (this.evaluateCondition(level, condition.value, condition.operator)) {
        fillColor = condition.color;
      } else {
        break;
      }
    }
    this.attr('fill/fill', fillColor);
  }

  evaluateCondition(a: number, b: number, operator: '>' | '>=' | '<' | '<=' | '==' | '!=') {
    switch (operator) {
      case '>':
        return a > b;
      case '>=':
        return a >= b;
      case '<':
        return a < b;
      case '<=':
        return a <= b;
      case '==':
        return a === b;
      case '!=':
        return a !== b;
      default:
        return false;
    }
  }

  updateMaxCapacity(newMaxCapacity: number) {
    this.maxCapacity = Math.max(0, newMaxCapacity);
    this.level = this.level;
  }

  updateShowProgress(showProgress: boolean) {
    this.showProgress = showProgress;
    this.attr('fill/display', this.showProgress ? 'block' : 'none');
    this.attr('border/display', this.showProgress ? 'block' : 'none');
    this.attr('levelLabel/display', this.showProgress ? 'block' : 'none');
    this.level = this.level; // Reapply the level to ensure visual state updates
  }
}
