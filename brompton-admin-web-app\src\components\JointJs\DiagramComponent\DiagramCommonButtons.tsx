import { dia, shapes } from '@joint/core';
import ArrowDownwardIcon from '@mui/icons-material/ArrowDownward';
import ArrowUpwardIcon from '@mui/icons-material/ArrowUpward';
import CancelIcon from '@mui/icons-material/Cancel';
import DeleteIcon from '@mui/icons-material/Delete';
import HighlightOffIcon from '@mui/icons-material/HighlightOff';
import LayersIcon from '@mui/icons-material/Layers';
import LayersClearIcon from '@mui/icons-material/LayersClear';
import RemoveRoadIcon from '@mui/icons-material/RemoveRoad';
import SaveIcon from '@mui/icons-material/Save';
import ZoomInIcon from '@mui/icons-material/ZoomIn';
import ZoomOutIcon from '@mui/icons-material/ZoomOut';
import { Box, Button, IconButton, Radio, TextField, Tooltip, Typography } from '@mui/material';
import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import CustomDialog from '~/components/common/CustomDialog';
import Progress from '~/components/CreateElement/Progress';
import {
  useCreateDiagramMutation,
  useGetAllDiagramsQuery,
  useUpdateDiagramMutation,
} from '~/redux/api/diagramApi';
import {
  getCurrentGraph,
  getDiagramId,
  getDiagramName,
  getElementsVariables,
  getSelectedElements,
  getZoomLevel,
} from '~/redux/selectors/diagramSelector';
import { diagramSlice } from '~/redux/slices/diagramSlice';
import { AlertSnackbar } from '~/shared/snackbars/components/AlertSnackbar';
import { useSnackbar } from '~/shared/snackbars/snackbar-hooks';

type DiagramCommonButtonsProps = {
  graphRef: React.MutableRefObject<dia.Graph<dia.Graph.Attributes, dia.ModelSetOptions> | null>;
  selectedElement: dia.Element<dia.Element.Attributes, dia.ModelSetOptions> | null;
  deleteSelectedElement: () => void;
  removeAllLinks: () => void;
  zoomIn: () => void;
  zoomOut: () => void;
  removeAllVertexTools: () => void;
};

const DiagramCommonButtons = ({
  graphRef,
  selectedElement,
  deleteSelectedElement,
  removeAllLinks,
  zoomIn,
  zoomOut,
  removeAllVertexTools,
}: DiagramCommonButtonsProps) => {
  const dispatch = useDispatch();
  const selectedElementsList = useSelector(getSelectedElements);
  const diagrmaId = useSelector(getDiagramId);
  const { setDiagramId, setDiagramName, setSelectedElement } = diagramSlice.actions;
  const zoomLevel = useSelector(getZoomLevel);
  const diagram = useSelector(getCurrentGraph);
  const diagramName = useSelector(getDiagramName);
  const [name, setName] = useState('');
  const [save, setSave] = useState(false);
  const [snackbarState, showSuccessAlert, showErrorAlert] = useSnackbar();
  const elementsVariables = useSelector(getElementsVariables);
  const { refetch } = useGetAllDiagramsQuery();
  const [createDiagram, { isError, isLoading, isSuccess, data, error: diagramError }] =
    useCreateDiagramMutation();
  const [updateDiagram, { isError: isUpdateError, isSuccess: isUpdateSuccess }] =
    useUpdateDiagramMutation();
  const [flow, setFlow] = useState<'create' | 'edit'>('create');

  useEffect(() => {
    if (diagrmaId > 0) {
      setFlow('edit');
    } else {
      setFlow('create');
    }
  }, [diagrmaId]);

  useEffect(() => {
    if (diagramName !== '') {
      setName(diagramName);
    } else {
      setName('');
    }
  }, [diagramName]);

  useEffect(() => {
    if (isUpdateError) {
      showErrorAlert('Diagram updation failed!!');
    }
    if (isUpdateSuccess) {
      showSuccessAlert('Diagram updated!!');
      setSave(false);
    }
  }, [isUpdateError, isUpdateSuccess]);

  useEffect(() => {
    if (
      isError &&
      diagramError &&
      'data' in diagramError &&
      typeof diagramError.data === 'object' &&
      diagramError.data &&
      'exception' in diagramError.data
    ) {
      showErrorAlert((diagramError.data as { exception: string }).exception);
    }
    if (isSuccess && data) {
      showSuccessAlert('Diagram created!!');
      dispatch(setDiagramId(data.id ?? 0));
      dispatch(setDiagramName(data?.name ?? ''));
      refetch();
      setSave(false);
    }
  }, [isError, isSuccess, data, dispatch]);

  const unGroupElements = () => {
    if (!graphRef.current || !selectedElement) {
      showErrorAlert('Please select a single group element to ungroup.');
      return;
    }
    if (selectedElement.get('type') === 'groupElement') {
      const embeddedCells = selectedElement.getEmbeddedCells();
      embeddedCells.forEach((cell) => {
        selectedElement.unembed(cell);
      });
      selectedElement.remove();
      dispatch(setSelectedElement(null));
      showSuccessAlert('Elements ungrouped.');
    } else {
      showErrorAlert('The selected element is not a group.');
    }
  };
  const groupElements = () => {
    if (!graphRef.current || selectedElementsList.length <= 1) {
      showErrorAlert('Please select more than one element to group.');
      return;
    }

    const links = graphRef.current!.getLinks();
    links.forEach((link) => {
      const sourceId = link.getSourceCell()?.id;
      const targetId = link.getTargetCell()?.id;
      if (
        sourceId &&
        targetId &&
        selectedElementsList.some((el) => el.id === sourceId) &&
        selectedElementsList.some((el) => el.id === targetId)
      ) {
        link.remove();
      }
    });

    // Calculate the bounding box that contains all selected elements
    let bbox = selectedElementsList[0].getBBox();
    selectedElementsList.forEach((element) => {
      bbox = bbox.union(element.getBBox());
    });

    // Create a new rectangle group element
    const groupElement = new shapes.standard.Rectangle();

    // Set position and size based on the bounding box
    const padding = 100; // Add padding to ensure the group is larger than the selected elements
    groupElement.position(bbox.x - padding, bbox.y - padding);
    groupElement.resize(bbox.width + padding * 2, bbox.height + padding * 2);

    groupElement.attr({
      body: {
        fill: 'transparent',
        stroke: '#e1e1e1',
        strokeWidth: 1,
      },
      label: {
        text: 'Group',
        fill: '#000',
        fontSize: 12,
      },
    });
    groupElement.set('type', 'groupElement');
    groupElement.addTo(graphRef.current!);

    // Embed selected elements into the group
    selectedElementsList.forEach((element) => {
      groupElement?.embed(element);
    });
    groupElement.toBack();
    // Fit the group to the embedded elements with added padding (optional, as we've already added padding)
    groupElement?.fitEmbeds({ padding: 70, deep: true });

    dispatch(setSelectedElement(null));
    showSuccessAlert('Elements grouped.');
  };

  const moveElementFront = () => {
    if (selectedElement) {
      selectedElement.toFront();
    }
  };

  const moveElementBack = () => {
    if (selectedElement) {
      selectedElement.toBack();
    }
  };
  return (
    <>
      <AlertSnackbar {...snackbarState} />
      <Box
        sx={{
          position: 'absolute',
          flexDirection: 'column',
          display: 'flex',
          right: selectedElement && selectedElementsList.length === 0 ? 380 : 40,
          gap: 1,
          top: 10,
        }}
      >
        <Tooltip title="Save Diagram" arrow>
          <IconButton
            disabled={
              !!isLoading ||
              !!(graphRef && graphRef.current && graphRef.current.getCells().length === 0)
            }
            disableRipple
            sx={{
              backgroundColor: (theme) => theme.palette.primary.main,
              color: '#fff',
            }}
            onClick={() => setSave(true)}
          >
            <SaveIcon />
          </IconButton>
        </Tooltip>

        {selectedElement && (
          <Tooltip title="Delete Selected Element" arrow>
            <IconButton
              disableRipple
              sx={{
                backgroundColor: (theme) => theme.palette.primary.main,
                color: '#fff',
              }}
              onClick={deleteSelectedElement}
            >
              <DeleteIcon />
            </IconButton>
          </Tooltip>
        )}
        {selectedElement ? (
          <>
            <Tooltip title="Move Element to Front" arrow>
              <IconButton
                disableRipple
                sx={{
                  backgroundColor: (theme) => theme.palette.primary.main,
                  color: '#fff',
                }}
                onClick={moveElementFront}
              >
                <ArrowUpwardIcon />
              </IconButton>
            </Tooltip>

            <Tooltip title="Move Element to Back" arrow>
              <IconButton
                disableRipple
                sx={{
                  backgroundColor: (theme) => theme.palette.primary.main,
                  color: '#fff',
                }}
                onClick={moveElementBack}
              >
                <ArrowDownwardIcon />
              </IconButton>
            </Tooltip>
          </>
        ) : null}
        <Tooltip title="Remove All Links" arrow>
          <IconButton
            disableRipple
            sx={{
              backgroundColor: (theme) => theme.palette.primary.main,
              color: '#fff',
            }}
            onClick={removeAllLinks}
          >
            <RemoveRoadIcon />
          </IconButton>
        </Tooltip>

        <Tooltip title="Zoom In" arrow>
          <IconButton
            disableRipple
            sx={{
              backgroundColor: (theme) => theme.palette.primary.main,
              color: '#fff',
            }}
            onClick={zoomIn}
          >
            <ZoomInIcon />
          </IconButton>
        </Tooltip>

        <Tooltip title="Zoom Out" arrow>
          <IconButton
            disableRipple
            sx={{
              backgroundColor: (theme) => theme.palette.primary.main,
              color: '#fff',
            }}
            onClick={zoomOut}
          >
            <ZoomOutIcon />
          </IconButton>
        </Tooltip>

        <Tooltip title="Remove All Anchors" arrow>
          <IconButton
            disableRipple
            sx={{
              backgroundColor: (theme) => theme.palette.primary.main,
              color: '#fff',
            }}
            onClick={removeAllVertexTools}
          >
            <HighlightOffIcon />
          </IconButton>
        </Tooltip>

        {selectedElement && selectedElement?.getEmbeddedCells()?.length > 1 ? (
          <Tooltip title="Ungroup elements" arrow>
            <IconButton
              disableRipple
              sx={{
                backgroundColor: (theme) => theme.palette.primary.main,
                color: '#fff',
              }}
              onClick={unGroupElements}
            >
              <LayersClearIcon />
            </IconButton>
          </Tooltip>
        ) : null}
        {selectedElementsList.length > 1 ? (
          <Tooltip title="Group elements" arrow>
            <IconButton
              disableRipple
              sx={{
                backgroundColor: (theme) => theme.palette.primary.main,
                color: '#fff',
              }}
              onClick={groupElements}
            >
              <LayersIcon />
            </IconButton>
          </Tooltip>
        ) : null}
      </Box>
      <CustomDialog
        open={save}
        title={
          <Typography variant="h5">{diagrmaId <= 0 ? 'Save Diagram' : 'Update Diagram'}</Typography>
        }
        onClose={() => {
          setSave(false);
        }}
        content={
          <>
            {diagrmaId > 0 ? (
              <Box sx={{ display: 'flex', flexDirection: 'row' }}>
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <Radio
                    checked={flow === 'create'}
                    onChange={() => setFlow('create')}
                    value="create"
                    name="radio-buttons"
                    inputProps={{ 'aria-label': 'Create' }}
                  />
                  <Typography variant="body1">Save as</Typography>
                </Box>
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <Radio
                    checked={flow === 'edit'}
                    onChange={() => setFlow('edit')}
                    value="edit"
                    name="radio-buttons"
                    inputProps={{ 'aria-label': 'Edit' }}
                  />
                  <Typography variant="body1">Update</Typography>
                </Box>
              </Box>
            ) : null}
            <TextField
              label="Name"
              sx={{ mt: 2 }}
              name="name"
              fullWidth
              value={name}
              onChange={(event) => {
                setName(event.target.value);
              }}
            />
          </>
        }
        dialogActions={
          <>
            <Button variant="outlined" startIcon={<CancelIcon />} onClick={() => setSave(false)}>
              Cancel
            </Button>
            <Button
              variant="contained"
              startIcon={<SaveIcon />}
              disabled={isLoading || name === ''}
              onClick={() => {
                if (flow === 'create') {
                  createDiagram({
                    name: name,
                    data: JSON.stringify({ diagram, elementsVariables, zoomLevel }, null),
                  });
                }
                if (flow === 'edit') {
                  updateDiagram({
                    id: diagrmaId,
                    name: name,
                    data: JSON.stringify({ diagram, elementsVariables, zoomLevel }, null),
                  });
                }
              }}
            >
              Save
            </Button>
          </>
        }
      />
    </>
  );
};

export default DiagramCommonButtons;
