import React from 'react';
import { styled } from '@mui/system';

export interface TrayItemWidgetProps {
  model: any;
  color: string;
  name: string;
  value?: number;
}

const Tray = styled('div')<{ color: string }>(
  {
    color: 'white',
    fontFamily: 'Helvetica, Arial',
    padding: '5px',
    margin: '0px 10px',
    borderRadius: '5px',
    marginBottom: '2px',
    cursor: 'pointer',
  },
  ({ color }) => ({
    border: `solid 1px ${color}`,
  }),
);

export const TrayItemWidget: React.FC<TrayItemWidgetProps> = (props) => {
  const handleDragStart = (event: React.DragEvent<HTMLDivElement>) => {
    event.dataTransfer.setData('storm-Diagram-node', JSON.stringify(props.model));
  };

  return (
    <Tray color={props.color} draggable={true} onDragStart={handleDragStart}>
      {props.name}
    </Tray>
  );
};
