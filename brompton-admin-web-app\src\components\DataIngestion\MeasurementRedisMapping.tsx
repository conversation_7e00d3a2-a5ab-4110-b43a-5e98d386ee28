import {
  Alert,
  AlertColor,
  Box,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Typography,
} from '@mui/material';
import React, { useEffect, useMemo, useState } from 'react';
import { useSelector } from 'react-redux';
import { useGetAllAssetQuery } from '~/redux/api/assetsApi';
import { getCustomerId } from '~/redux/selectors/customerSelectors';
import { assetsPathMapper } from '~/utils/utils';
import AssetMeasureRow from './AssetMeasureRow';

interface Props {
  csvData: {
    previewRows: string[][];
    columns: string[];
    originalFile?: File;
    signalNames?: string[];
    compositeSignals?: { fileAsset: string; fileMeasure: string }[];
  };
  columnMapping: Record<string, string>;
  onMappingChange: (mapping: Record<string, string>) => void;
  isLoading?: boolean;
}

type AssetOption = {
  label: string;
  id: number;
};

type MeasurementOption = {
  id: string;
  name: string;
};

// Add AlertMessage type
type AlertMessage = {
  message: string;
  severity: AlertColor;
};

const MeasurementRedisMapping: React.FC<Props> = ({
  csvData,
  columnMapping,
  onMappingChange,
  isLoading,
}) => {
  const customerId = useSelector(getCustomerId);
  const [uniqueRows, setUniqueRows] = useState<{ fileAsset: string; fileMeasure: string }[]>([]);
  const [mappings, setMappings] = useState<{
    [key: string]: { assetId: string; measurementId: string };
  }>({});

  // Replace string error with AlertMessage
  const [alertMessage, setAlertMessage] = useState<AlertMessage | null>(null);

  const {
    data: assetData,
    isLoading: isAssetLoading,
    isFetching: isAssetReloading,
  } = useGetAllAssetQuery(
    { customerId, parentIds: [] },
    {
      skip: !customerId,
      refetchOnMountOrArgChange: true,
    },
  );

  const assetTypesWithPath = useMemo(() => assetsPathMapper(assetData ?? []), [assetData]);

  useEffect(() => {
    // Use signal names from the API response if available
    if (csvData.compositeSignals && Array.isArray(csvData.compositeSignals)) {
      const result = csvData.compositeSignals.map(({ fileAsset, fileMeasure }) => ({
        fileAsset: fileAsset.trim(),
        fileMeasure: fileMeasure.trim(),
      }));

      setUniqueRows(result);

      const initialMappings: {
        [key: string]: { assetId: string; measurementId: string };
      } = {};

      result.forEach((row) => {
        const key = `${row.fileAsset}||${row.fileMeasure}`;
        initialMappings[key] = { assetId: '', measurementId: '' };
      });

      setMappings(initialMappings);

      if (result.length > 0) {
        setAlertMessage({
          message: `Found ${result.length} unique signals in the CSV data.`,
          severity: 'info',
        });
      }
      return;
    } else {
      // Extract from CSV if no signal names provided
      const header = csvData.columns;
      const assetIndex = header.indexOf(columnMapping.asset);
      const measureIndex = header.indexOf(columnMapping.measure);

      if (measureIndex === -1) {
        setAlertMessage({
          message: 'Measure column not found in CSV data',
          severity: 'error',
        });
        return;
      }

      const seen = new Set<string>();
      const result: { fileAsset: string; fileMeasure: string }[] = [];

      csvData.previewRows.forEach((row) => {
        const fileAsset = assetIndex >= 0 ? row[assetIndex] : 'N/A';
        const fileMeasure = row[measureIndex]?.trim();
        if (!fileMeasure) return;

        const key = `${fileAsset}||${fileMeasure}`;
        if (!seen.has(key)) {
          seen.add(key);
          result.push({ fileAsset, fileMeasure });
        }
      });

      setUniqueRows(result);

      // Initialize mappings
      const initialMappings: {
        [key: string]: { assetId: string; measurementId: string };
      } = {};

      result.forEach((row) => {
        const key = `${row.fileAsset}||${row.fileMeasure}`;
        initialMappings[key] = { assetId: '', measurementId: '' };
      });

      setMappings(initialMappings);

      // Show success message for found signals
      if (result.length > 0) {
        setAlertMessage({
          message: `Found ${result.length} unique signals in the CSV data.`,
          severity: 'info',
        });
      }
    }
  }, [csvData, columnMapping]);

  const handleAssetChange = (key: string, event: any, newValue: AssetOption | null) => {
    setMappings((prev) => ({
      ...prev,
      [key]: {
        ...prev[key],
        assetId: newValue ? String(newValue.id) : '',
        measurementId: '', // Reset measurement when asset changes
      },
    }));

    // Clear any error messages when user makes changes
    if (alertMessage?.severity === 'error') {
      setAlertMessage(null);
    }
  };

  const handleMeasurementChange = (key: string, event: any, newValue: MeasurementOption | null) => {
    setMappings((prev) => ({
      ...prev,
      [key]: {
        ...prev[key],
        measurementId: newValue ? newValue.id : '',
      },
    }));

    // Clear any error messages when user makes changes
    if (alertMessage?.severity === 'error') {
      setAlertMessage(null);
    }
  };

  const allMapped = uniqueRows.every((row) => {
    const key = `${row.fileAsset}||${row.fileMeasure}`;
    return mappings[key]?.assetId && mappings[key]?.measurementId;
  });

  const handleInject = () => {
    if (!allMapped) {
      setAlertMessage({
        message: 'Please map all signals to assets and measurements',
        severity: 'error',
      });
      return;
    }

    const finalMapping: Record<string, string> = {};
    Object.entries(mappings).forEach(([key, value]) => {
      finalMapping[key.trim()] = value.measurementId;
    });

    setAlertMessage({
      message: 'Preparing to ingest data with the provided mappings...',
      severity: 'info',
    });

    onMappingChange(finalMapping);
  };

  useEffect(() => {
    const finalMapping: Record<string, string> = {};
    Object.entries(mappings).forEach(([key, value]) => {
      if (value.measurementId) {
        finalMapping[key.trim()] = value.measurementId;
      }
    });
    onMappingChange(finalMapping);
  }, [mappings, onMappingChange]);

  return (
    <Box>
      <Typography variant="h5" gutterBottom>
        Measurement to Redis Mapping
      </Typography>

      {alertMessage && (
        <Alert severity={alertMessage.severity} sx={{ mb: 3 }}>
          {alertMessage.message}
        </Alert>
      )}

      <Typography variant="body1" paragraph>
        Map each signal to an asset and measurement in the system.
        {uniqueRows.length > 0 && ` Found ${uniqueRows.length} unique signals.`}
      </Typography>

      <TableContainer component={Paper} sx={{ mb: 3, maxHeight: 600, overflow: 'auto' }}>
        <Table stickyHeader>
          <TableHead>
            <TableRow>
              <TableCell>File Asset</TableCell>
              <TableCell>File Signal</TableCell>
              <TableCell>System Asset</TableCell>
              <TableCell>System Measurement</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {uniqueRows.map((row, idx) => {
              const key = `${row.fileAsset.trim()}||${row.fileMeasure.trim()}`;
              const assetId = mappings[key]?.assetId || '';
              const measurementId = mappings[key]?.measurementId || '';

              return (
                <AssetMeasureRow
                  key={idx}
                  idx={idx}
                  row={row}
                  assetId={assetId}
                  measurementId={measurementId}
                  assetOptions={
                    !isAssetReloading
                      ? assetTypesWithPath.map((asset) => ({
                          label: asset.label,
                          id: asset.id,
                        }))
                      : []
                  }
                  isAssetLoading={isAssetReloading}
                  customerId={customerId || 0}
                  handleAssetChange={handleAssetChange}
                  handleMeasurementChange={handleMeasurementChange}
                />
              );
            })}
          </TableBody>
        </Table>
      </TableContainer>
    </Box>
  );
};

export default MeasurementRedisMapping;
