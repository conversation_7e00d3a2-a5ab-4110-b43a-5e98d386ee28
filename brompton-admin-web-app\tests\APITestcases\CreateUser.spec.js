const { test, expect } = require('@playwright/test');

test.describe('API Testing for POST Create User', () => {
  test('should successfully create a new user with status 201', async ({ request }) => {
    // Define headers
    const headers = {
      'BE-CsrfToken': 'OHi34V+PkJef3vbYUN4Xe9t35klLsZ4Pss4dZBfa3Q4=',
      'Content-Type': 'application/json',
      Cookie:
        'BE-AccessToken=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VybmFtZSI6InRlc3QiLCJzdWIiOjIsInJvbGVzIjp7IkFETUlOIjpbOCwxMjEsMTE5LDEwNiwxMTgsMTA4LDg2LDExMSwxMTIsODUsMTA3LDg0LDExMywxMTQsMTE3LDEyMCwxMjcsMTIzLDEyNiwxMjQsMTA5LDEyNSwxMjIsMTEwLDksODJdLCJVU0VSIjpbOCwxMjEsMTE5LDEwNiwxMTgsMTA4LDg2LDExMSwxMTIsODUsMTA3LDg0LDExMywxMTQsMTE3LDEyMCwxMjcsMTIzLDEyNiwxMjQsMTA5LDEyNSwxMjIsMTEwLDksODJdLCJQT1dFUl9VU0VSIjpbOCwxMjEsMTE5LDEwNiwxMTgsMTA4LDg2LDExMSwxMTIsODUsMTA3LDg0LDExMywxMTQsMTE3LDEyMCwxMjcsMTIzLDEyNiwxMjQsMTA5LDEyNSwxMjIsMTEwLDksODJdfSwiaWF0IjoxNzMxNTEwMDA4LCJleHAiOjE3MzE1MTcyMDh9.KPMk4EEgqKqRUnmx_PgodZEM7rJH7AU_ngfd-N9dqXM; BE-CSRFToken=OHi34V%2BPkJef3vbYUN4Xe9t35klLsZ4Pss4dZBfa3Q4%3D',
    };

    // Define body payload
    const payload = {
      username: 'customer_user',
      password: 'asdfasdf',
      first_name: 'Just',
      last_name: 'Customer',
      scoped_roles: [
        {
          role: 'USER',
          cusotmer_ids: [1],
        },
      ],
      email: '<EMAIL>',
    };

    // Send POST request
    const response = await request.post('https://test.brompton.ai/api/v0/users', {
      headers,
      data: payload,
    });

    // Validate the response status is 201 for successful creation
    expect(response.status()).toBe(201);

    // Check that the response contains expected content (e.g., user details or success message)
    const responseBody = await response.text();
    console.log('Response:', responseBody);
    expect(responseBody).toContain('expected_content_here'); // Adjust to actual response content, such as the new username or email
  });
});
