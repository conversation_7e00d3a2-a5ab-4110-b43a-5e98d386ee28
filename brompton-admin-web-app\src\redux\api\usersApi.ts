import { ScopedUser, UserDetailsResponse } from '~/types/users';
import { Customer, NewCustomer } from '~/types/customers';
import { authApi } from '~/redux/api/authApi';
import { UserPreferences } from '~/types/userPreferences';

export const usersApi = authApi
  .enhanceEndpoints({
    addTagTypes: ['User', 'me', 'UserPreferences'],
  })
  .injectEndpoints({
    endpoints: (builder) => ({
      getCurrentUserDetails: builder.query<UserDetailsResponse, void>({
        query: () => '/v0/users/me',
        providesTags: ['User', 'me'],
      }),
      createUser: builder.mutation<Customer, NewCustomer>({
        invalidatesTags: ['User'],
        query: (newCustomer) => {
          const { nameId: name_id, ...rest } = newCustomer;
          return {
            url: '/v0/customers',
            method: 'POST',
            body: { name_id, ...rest },
          };
        },
      }),
      createScopedUser: builder.mutation<UserDetailsResponse, ScopedUser>({
        invalidatesTags: ['User'],
        query: (user) => {
          return {
            url: '/v0/users',
            method: 'POST',
            body: user,
          };
        },
      }),
      resetPassword: builder.mutation<
        void,
        { current_password: string; new_password: string; confirm_password: string }
      >({
        query: ({ current_password, new_password, confirm_password }) => {
          return {
            url: '/v0/users/reset-password',
            method: 'PUT',
            body: { current_password, new_password, confirm_password },
          };
        },
      }),
      logout: builder.mutation({
        invalidatesTags: ['User', 'me', 'UserPreferences'],
        query: () => ({
          url: '/v0/sessions/invalidate',
          method: 'POST',
        }),
      }),
      getUserPreferences: builder.query<{ preferences: UserPreferences }, void>({
        query: () => `/v0/users/preference`,
        providesTags: ['UserPreferences'],
      }),
      updateUserPreferences: builder.mutation<any, { preferences: Partial<UserPreferences> }>({
        invalidatesTags: ['UserPreferences'],
        query: ({ preferences }) => ({
          url: `/v0/users/preference`,
          method: 'POST',
          body: {
            preferences: preferences,
          },
        }),
      }),
    }),
    // TODO - this is not working
    // extraOptions: {
    //     // Global error handler
    //     onError: (error, { endpointName, originalArgs }) => {
    //         if (error.status === 401 && endpointName !== 'getMe') {
    //             api.util.invalidateTags(['User', 'me']);
    //         }
    //     },
    // },
  });

export const {
  useGetCurrentUserDetailsQuery,
  useCreateScopedUserMutation,
  useLogoutMutation,
  useGetUserPreferencesQuery,
  useUpdateUserPreferencesMutation,
  useResetPasswordMutation,
} = usersApi;
