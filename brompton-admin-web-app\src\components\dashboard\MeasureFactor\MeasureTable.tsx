import { Box, Button, TextField } from '@mui/material';
import {
  DataGrid,
  GridCellModesModel,
  GridColDef,
  GridRowId,
  GridRowsProp,
} from '@mui/x-data-grid';
import { TimePicker } from '@mui/x-date-pickers';
import dayjs from 'dayjs';
import { Dispatch, SetStateAction, useCallback, useEffect, useMemo, useState } from 'react';

interface SelectedCellParams {
  id: GridRowId;
  field: string;
}
type MeasureTableProps = {
  effective_dates: string;
  setEffectiveDates: Dispatch<
    SetStateAction<
      {
        effectiveDate: string;
        rows: object[];
      }[]
    >
  >;
};
const MeasureTable = ({ effective_dates, setEffectiveDates }: MeasureTableProps) => {
  const [selectedCellParams, setSelectedCellParams] = useState<SelectedCellParams | null>(null);
  const [cellModesModel, setCellModesModel] = useState<GridCellModesModel>({});

  const columns: GridColDef[] = [
    {
      field: 'monday',
      headerName: 'Monday',
      editable: true,
      align: 'left',
      headerAlign: 'left',
      width: 200,
      renderCell: (params) => (
        <Box>
          <Box>Time : {params.row.monday.time}</Box>
          <Box>Value : {params.row.monday.value}</Box>
        </Box>
      ),
      renderEditCell(params) {
        return (
          <Box display={'flex'}>
            <TimePicker
              defaultValue={dayjs(params.row.monday.time, 'HH:mm')}
              onChange={(value) => {
                if (value) {
                  setRows((prevRows) => {
                    const updatedRows = prevRows.map((row) => {
                      if (row.id === params.id) {
                        return {
                          ...row,
                          monday: {
                            ...row.monday,
                            time: value.format('HH:mm'),
                          },
                        };
                      }
                      return row;
                    });
                    return updatedRows;
                  });
                  params.api.setEditCellValue({
                    id: params.id,
                    field: params.field,
                    value: {
                      time: value.format('HH:mm'),
                      value: params.row.monday.value,
                    },
                  });
                }
              }}
            />
            <TextField
              type="number"
              defaultValue={params.row.monday.value}
              onChange={(event) => {
                const newValue = event.target.value;
                setRows((prevRows) => {
                  const updatedRows = prevRows.map((row) => {
                    if (row.id === params.id) {
                      return {
                        ...row,
                        monday: {
                          ...row.monday,
                          value: newValue,
                        },
                      };
                    }
                    return row;
                  });
                  return updatedRows;
                });
                params.api.setEditCellValue({
                  id: params.id,
                  field: params.field,
                  value: {
                    time: params.row.monday.time,
                    value: newValue,
                  },
                });
              }}
            />
          </Box>
        );
      },
    },
    {
      field: 'tuesday',
      headerName: 'Tuesday',
      editable: true,
      align: 'left',
      headerAlign: 'left',
      width: 200,
      renderCell: (params) => (
        <Box>
          <Box>Time: {params.row.tuesday.time}</Box>
          <Box>Value: {params.row.tuesday.value}</Box>
        </Box>
      ),
      renderEditCell(params) {
        return (
          <Box display={'flex'}>
            <TimePicker
              defaultValue={dayjs(params.row.tuesday.time, 'HH:mm')}
              onChange={(value) => {
                if (value) {
                  setRows((prevRows) => {
                    const updatedRows = prevRows.map((row) => {
                      if (row.id === params.id) {
                        return {
                          ...row,
                          tuesday: {
                            ...row.tuesday,
                            time: value.format('HH:mm'),
                          },
                        };
                      }
                      return row;
                    });
                    return updatedRows;
                  });
                  params.api.setEditCellValue({
                    id: params.id,
                    field: params.field,
                    value: {
                      time: value.format('HH:mm'),
                      value: params.row.tuesday.value,
                    },
                  });
                }
              }}
            />
            <TextField
              type="number"
              defaultValue={params.row.tuesday.value}
              onChange={(event) => {
                const newValue = event.target.value;
                params.api.setEditCellValue({
                  id: params.id,
                  field: params.field,
                  value: {
                    time: params.row.tuesday.time,
                    value: newValue,
                  },
                });
                setRows((prevRows) => {
                  const updatedRows = prevRows.map((row) => {
                    if (row.id === params.id) {
                      return {
                        ...row,
                        tuesday: {
                          ...row.tuesday,
                          value: newValue,
                        },
                      };
                    }
                    return row;
                  });
                  return updatedRows;
                });
              }}
            />
          </Box>
        );
      },
    },
    {
      field: 'wednesday',
      headerName: 'Wednesday',
      editable: true,
      align: 'left',
      headerAlign: 'left',
      width: 200,
      renderCell: (params) => (
        <Box>
          <Box>Time: {params.row.wednesday.time}</Box>
          <Box>Value: {params.row.wednesday.value}</Box>
        </Box>
      ),
      renderEditCell(params) {
        return (
          <Box display={'flex'}>
            <TimePicker
              defaultValue={dayjs(params.row.wednesday.time, 'HH:mm')}
              onChange={(value) => {
                if (value) {
                  setRows((prevRows) => {
                    const updatedRows = prevRows.map((row) => {
                      if (row.id === params.id) {
                        return {
                          ...row,
                          wednesday: {
                            ...row.wednesday,
                            time: value.format('HH:mm'),
                          },
                        };
                      }
                      return row;
                    });
                    return updatedRows;
                  });
                  params.api.setEditCellValue({
                    id: params.id,
                    field: params.field,
                    value: {
                      time: value.format('HH:mm'),
                      value: params.row.wednesday.value,
                    },
                  });
                  setRows((prevRows) => {
                    const updatedRows = prevRows.map((row) => {
                      if (row.id === params.id) {
                        return {
                          ...row,
                          wednesday: {
                            ...row.wednesday,
                            time: value.format('HH:mm'),
                          },
                        };
                      }
                      return row;
                    });
                    return updatedRows;
                  });
                }
              }}
            />
            <TextField
              type="number"
              defaultValue={params.row.wednesday.value}
              onChange={(event) => {
                const newValue = event.target.value;
                params.api.setEditCellValue({
                  id: params.id,
                  field: params.field,
                  value: {
                    time: params.row.wednesday.time,
                    value: newValue,
                  },
                });
                setRows((prevRows) => {
                  const updatedRows = prevRows.map((row) => {
                    if (row.id === params.id) {
                      return {
                        ...row,
                        wednesday: {
                          ...row.wednesday,
                          value: newValue,
                        },
                      };
                    }
                    return row;
                  });
                  return updatedRows;
                });
              }}
            />
          </Box>
        );
      },
    },
    {
      field: 'thursday',
      headerName: 'Thursday',
      editable: true,
      align: 'left',
      headerAlign: 'left',
      width: 200,
      renderCell: (params) => (
        <Box>
          <Box>Time: {params.row.thursday.time}</Box>
          <Box>Value: {params.row.thursday.value}</Box>
        </Box>
      ),
      renderEditCell(params) {
        return (
          <Box display={'flex'}>
            <TimePicker
              defaultValue={dayjs(params.row.thursday.time, 'HH:mm')}
              onChange={(value) => {
                if (value) {
                  setRows((prevRows) => {
                    const updatedRows = prevRows.map((row) => {
                      if (row.id === params.id) {
                        return {
                          ...row,
                          thursday: {
                            ...row.thursday,
                            time: value.format('HH:mm'),
                          },
                        };
                      }
                      return row;
                    });
                    return updatedRows;
                  });
                  params.api.setEditCellValue({
                    id: params.id,
                    field: params.field,
                    value: {
                      time: value.format('HH:mm'),
                      value: params.row.thursday.value,
                    },
                  });
                }
              }}
            />
            <TextField
              type="number"
              defaultValue={params.row.thursday.value}
              onChange={(event) => {
                const newValue = event.target.value;
                params.api.setEditCellValue({
                  id: params.id,
                  field: params.field,
                  value: {
                    time: params.row.thursday.time,
                    value: newValue,
                  },
                });
                setRows((prevRows) => {
                  const updatedRows = prevRows.map((row) => {
                    if (row.id === params.id) {
                      return {
                        ...row,
                        thursday: {
                          ...row.thursday,
                          value: newValue,
                        },
                      };
                    }
                    return row;
                  });
                  return updatedRows;
                });
              }}
            />
          </Box>
        );
      },
    },
    {
      field: 'friday',
      headerName: 'Friday',
      editable: true,
      align: 'left',
      headerAlign: 'left',
      width: 200,
      renderCell: (params) => (
        <Box>
          <Box>Time: {params.row.friday.time}</Box>
          <Box>Value: {params.row.friday.value}</Box>
        </Box>
      ),
      renderEditCell(params) {
        return (
          <Box display={'flex'}>
            <TimePicker
              defaultValue={dayjs(params.row.friday.time, 'HH:mm')}
              onChange={(value) => {
                if (value) {
                  params.api.setEditCellValue({
                    id: params.id,
                    field: params.field,
                    value: {
                      time: value.format('HH:mm'),
                      value: params.row.friday.value,
                    },
                  });
                  setRows((prevRows) => {
                    const updatedRows = prevRows.map((row) => {
                      if (row.id === params.id) {
                        return {
                          ...row,
                          friday: {
                            ...row.friday,
                            time: value.format('HH:mm'),
                          },
                        };
                      }
                      return row;
                    });
                    return updatedRows;
                  });
                }
              }}
            />
            <TextField
              type="number"
              defaultValue={params.row.friday.value}
              onChange={(event) => {
                const newValue = event.target.value;
                params.api.setEditCellValue({
                  id: params.id,
                  field: params.field,
                  value: {
                    time: params.row.friday.time,
                    value: newValue,
                  },
                });
                setRows((prevRows) => {
                  const updatedRows = prevRows.map((row) => {
                    if (row.id === params.id) {
                      return {
                        ...row,
                        friday: {
                          ...row.friday,
                          value: newValue,
                        },
                      };
                    }
                    return row;
                  });
                  return updatedRows;
                });
              }}
            />
          </Box>
        );
      },
    },
    {
      field: 'saturday',
      headerName: 'Saturday',
      editable: true,
      align: 'left',
      headerAlign: 'left',
      width: 200,
      renderCell: (params) => (
        <Box>
          <Box>Time: {params.row.saturday.time}</Box>
          <Box>Value: {params.row.saturday.value}</Box>
        </Box>
      ),
      renderEditCell(params) {
        return (
          <Box display={'flex'}>
            <TimePicker
              defaultValue={dayjs(params.row.saturday.time, 'HH:mm')}
              onChange={(value) => {
                if (value) {
                  params.api.setEditCellValue({
                    id: params.id,
                    field: params.field,
                    value: {
                      time: value.format('HH:mm'),
                      value: params.row.saturday.value,
                    },
                  });
                  setRows((prevRows) => {
                    const updatedRows = prevRows.map((row) => {
                      if (row.id === params.id) {
                        return {
                          ...row,
                          saturday: {
                            ...row.saturday,
                            time: value.format('HH:mm'),
                          },
                        };
                      }
                      return row;
                    });
                    return updatedRows;
                  });
                }
              }}
            />
            <TextField
              type="number"
              defaultValue={params.row.saturday.value}
              onChange={(event) => {
                const newValue = event.target.value;
                params.api.setEditCellValue({
                  id: params.id,
                  field: params.field,
                  value: {
                    time: params.row.saturday.time,
                    value: newValue,
                  },
                });
                setRows((prevRows) => {
                  const updatedRows = prevRows.map((row) => {
                    if (row.id === params.id) {
                      return {
                        ...row,
                        saturday: {
                          ...row.saturday,
                          value: newValue,
                        },
                      };
                    }
                    return row;
                  });
                  return updatedRows;
                });
              }}
            />
          </Box>
        );
      },
    },
    {
      field: 'sunday',
      headerName: 'Sunday',
      type: 'number',
      editable: true,
      align: 'left',
      headerAlign: 'left',
      width: 200,
      renderCell: (params) => (
        <Box>
          <Box>Time: {params.row.sunday.time}</Box>
          <Box>Value: {params.row.sunday.value}</Box>
        </Box>
      ),
      renderEditCell(params) {
        return (
          <Box display={'flex'}>
            <TimePicker
              defaultValue={dayjs(params.row.sunday.time, 'HH:mm')}
              onChange={(value) => {
                if (value) {
                  params.api.setEditCellValue({
                    id: params.id,
                    field: params.field,
                    value: {
                      time: value.format('HH:mm'),
                      value: params.row.sunday.value,
                    },
                  });
                  setRows((prevRows) => {
                    const updatedRows = prevRows.map((row) => {
                      if (row.id === params.id) {
                        return {
                          ...row,
                          sunday: {
                            ...row.sunday,
                            time: value.format('HH:mm'),
                          },
                        };
                      }
                      return row;
                    });
                    return updatedRows;
                  });
                }
              }}
            />
            <TextField
              type="number"
              defaultValue={params.row.sunday.value}
              onChange={(event) => {
                const newValue = event.target.value;
                params.api.setEditCellValue({
                  id: params.id,
                  field: params.field,
                  value: {
                    time: params.row.sunday.time,
                    value: newValue,
                  },
                });
                setRows((prevRows) => {
                  const updatedRows = prevRows.map((row) => {
                    if (row.id === params.id) {
                      return {
                        ...row,
                        sunday: {
                          ...row.sunday,
                          value: newValue,
                        },
                      };
                    }
                    return row;
                  });
                  return updatedRows;
                });
              }}
            />
          </Box>
        );
      },
    },
    {
      field: 'actions',
      headerName: 'Actions',
      align: 'center',
      headerAlign: 'center',
      width: 200,
      renderCell: (params) => (
        <Button
          variant={'contained'}
          color={'error'}
          onClick={() => {
            setRows((prevRows) => prevRows.filter((row) => row.id !== params.id));
          }}
        >
          Delete
        </Button>
      ),
    },
  ];
  const [rows, setRows] = useState<GridRowsProp>([
    {
      id: 1,
      monday: {
        time: '',
        value: '',
      },
      tuesday: {
        time: '',
        value: '',
      },
      wednesday: {
        time: '',
        value: '',
      },
      thursday: {
        time: '',
        value: '',
      },
      friday: {
        time: '',
        value: '',
      },
      saturday: {
        time: '',
        value: '',
      },
      sunday: {
        time: '',
        value: '',
      },
    },
  ]);
  const handleCellFocus = useCallback((event: React.FocusEvent<HTMLDivElement>) => {
    const row = event.currentTarget.parentElement;
    const id = row!.dataset.id!;
    const field = event.currentTarget.dataset.field!;
    setSelectedCellParams({ id, field });
  }, []);

  const cellMode = useMemo(() => {
    if (!selectedCellParams) {
      return 'view';
    }
    const { id, field } = selectedCellParams;
    return cellModesModel[id]?.[field]?.mode || 'view';
  }, [cellModesModel, selectedCellParams]);
  useEffect(() => {
    const filteredRows = rows.filter((row) => {
      const weekdays = [
        'monday',
        'tuesday',
        'wednesday',
        'thursday',
        'friday',
        'saturday',
        'sunday',
      ];
      return weekdays.some((day) => row[day].value !== '');
    });
    const factorTimeOfDayValue = filteredRows.map((row) => {
      const weekdays = [
        'monday',
        'tuesday',
        'wednesday',
        'thursday',
        'friday',
        'saturday',
        'sunday',
      ];
      return weekdays.map((day, index) => {
        if (row[day].value === '') return;
        if (row[day].time === '') return;
        return {
          effectiveDate: effective_dates,
          timeOfDay: row[day].time,
          value: Number(row[day].value),
          weekday: index + 1,
        };
      });
    });
    setEffectiveDates((prev) => {
      return prev.map((date) => {
        if (date.effectiveDate === effective_dates) {
          return {
            ...date,
            rows: factorTimeOfDayValue
              .map((row) => row.filter((day) => day !== undefined))
              .flatMap((row) => row) as {
              effectiveDate: string;
              timeOfDay: any;
              value: number;
              weekday: number;
            }[],
          };
        }
        return date;
      });
    });
  }, [rows]);
  return (
    <>
      <Box display={'flex'} justifyContent={'end'}>
        <Button
          onClick={() => {
            setRows([
              ...rows,
              {
                id: rows.length + 1,
                monday: {
                  time: '',
                  value: '',
                },
                tuesday: {
                  time: '',
                  value: '',
                },
                wednesday: {
                  time: '',
                  value: '',
                },
                thursday: {
                  time: '',
                  value: '',
                },
                friday: {
                  time: '',
                  value: '',
                },
                saturday: {
                  time: '',
                  value: '',
                },
                sunday: {
                  time: '',
                  value: '',
                },
              },
            ]);
          }}
        >
          Add Time
        </Button>
      </Box>
      <DataGrid
        rows={rows}
        showCellVerticalBorder
        showColumnVerticalBorder
        columns={columns}
        rowSelection={false}
        editMode="cell"
        hideFooter
        slotProps={{
          toolbar: {
            cellMode,
            selectedCellParams,
            setSelectedCellParams,
            cellModesModel,
            setCellModesModel,
          },
          cell: {
            onFocus: handleCellFocus,
          },
        }}
      />
    </>
  );
};

export default MeasureTable;
