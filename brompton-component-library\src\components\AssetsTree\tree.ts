// copmany = customer
// activo = asset
// medicion = measurement
// metric = metric
export type NodeType = 'company' | 'activo' | 'medicion' | 'metric';

export type TreeNode = Pick<Tree, 'id' | 'tag' | 'type'>;

export class Tree {
  readonly id: string;
  readonly tag: string;
  readonly children: Tree[];
  readonly size: number;
  readonly type: NodeType;

  constructor(id: string, tag: string, type: NodeType, children: Tree[] = []) {
    this.id = id;
    this.tag = tag;
    this.type = type;
    this.children = children;
    this.size =
      1 +
      children
        .map((child) => child.size)
        .reduce((accum, curr) => accum + curr, 0);
  }

  find(id: string): TreeNode | null {
    if (id === this.id) {
      return { id: this.id, tag: this.tag, type: this.type };
    } else {
      return (
        this.children
          .map((child) => child.find(id))
          .find((treeNode) => treeNode !== null) ?? null
      );
    }
  }
}
