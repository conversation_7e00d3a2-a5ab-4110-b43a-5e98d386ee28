import {
  Box,
  Button,
  Card,
  Chip,
  Container,
  MenuItem,
  OutlinedInput,
  Select,
  Typography,
} from '@mui/material';
import { DatePicker } from '@mui/x-date-pickers';
import dayjs from 'dayjs';
import { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';
import { Excursion } from '~/measurements/domain/types';
import { useGetExcursionsQuery } from '~/redux/api/alertApi';
import { getActiveCustomer } from '~/redux/selectors/customerSelectors';
import { getDateTimeFormat, getThousandSeparator } from '~/redux/selectors/userPreferences';
import { AlertSnackbar } from '~/shared/snackbars/components/AlertSnackbar';
import { useSnackbar } from '~/shared/snackbars/snackbar-hooks';
import { formatNumber, roundNumber } from '~/utils/utils';
import DataTable from '../common/DataTable/DataTable';
import Loader from '../common/Loader';
import PageName from '../common/PageName/PageName';

const ExcursionsList = () => {
  const ActiveCustomer = useSelector(getActiveCustomer);
  const thousandSeparator = useSelector(getThousandSeparator);
  const [snackbarState, showSuccessAlert, showErrorAlert] = useSnackbar();
  const dateTimeFormat = useSelector(getDateTimeFormat);

  // Extract separate formats
  const dateFormat = dateTimeFormat.split(' ')[0]; // e.g., 'DD-MM-YYYY'
  const timeFormat = dateTimeFormat.split(' ').slice(1).join(' '); // e.g., 'hh:mm:ss a'

  const [filterFields, setFilterFields] = useState<{
    measurementTag?: string;
    state?: string;
    fromDate?: string;
    toDate?: string;
    showFilters: boolean;
  }>({
    measurementTag: undefined,
    state: undefined,
    fromDate: undefined,
    toDate: undefined,
    showFilters: false,
  });

  const [filtedData, setFilteredData] = useState<Excursion[]>([]);

  const { isFetching, data: excursions, isError, refetch } = useGetExcursionsQuery();

  useEffect(() => {
    if (excursions) {
      let filteredAlerts = excursions.items;

      if (ActiveCustomer?.id) {
        filteredAlerts = filteredAlerts.filter(
          (excursion) => excursion.alert.customerId.toString() === ActiveCustomer.id.toString(),
        );
      }

      if (filterFields.showFilters) {
        filteredAlerts = filteredAlerts.filter((excursion) => {
          const startDateOnly = new Date(excursion.start_time).toLocaleDateString('en-CA'); // YYYY-MM-DD
          const endDateOnly = new Date(excursion.end_time).toLocaleDateString('en-CA'); // YYYY-MM-DD

          const matchesFromDate = !filterFields.fromDate || startDateOnly >= filterFields.fromDate;
          const matchesToDate = !filterFields.toDate || endDateOnly <= filterFields.toDate;
          const matchesDateRange = matchesFromDate && matchesToDate;

          const matchesState = !filterFields.state || filterFields.state === excursion.alert.state;

          return matchesDateRange && matchesState;
        });
      }

      setFilteredData(filteredAlerts);
    }
  }, [excursions, filterFields, ActiveCustomer]);

  useEffect(() => {
    if (
      filterFields.showFilters &&
      !filterFields.state &&
      !filterFields.fromDate &&
      !filterFields.toDate &&
      excursions
    ) {
      setFilteredData(excursions.items);
      setFilterFields({ ...filterFields, showFilters: false });
    } else if (filterFields.showFilters && excursions) {
      let filteredAlerts = excursions.items;

      const matchesState = (excursion: Excursion) =>
        !filterFields.state || filterFields.state === excursion.alert.state;

      const matchesDateRange = (excursion: Excursion) => {
        const startDateOnly = new Date(excursion.start_time).toISOString().split('T')[0];
        const endDateOnly = new Date(excursion.end_time).toISOString().split('T')[0];

        const matchesFromDate = !filterFields.fromDate || startDateOnly >= filterFields.fromDate;
        const matchesToDate = !filterFields.toDate || endDateOnly <= filterFields.toDate;

        return matchesFromDate && matchesToDate;
      };

      filteredAlerts = filteredAlerts.filter(
        (excursion) => matchesState(excursion) && matchesDateRange(excursion),
      );

      setFilteredData(filteredAlerts);
    }
  }, [filterFields, excursions]);

  const DataTableColumns = [
    {
      field: 'measurementTag',
      headerName: 'Measurement Tag',
      flex: 2,
    },
    { field: 'description', headerName: 'Alert Description', flex: 1.5 },
    { field: 'minmaxavg', headerName: 'Min | Max | Avg', flex: 1.5 },
    {
      field: 'startTime',
      headerName: 'Start Time',
      flex: 1,
      renderCell: (params: any) => {
        const [date, time] = params.value.split('\n');
        return (
          <Box>
            <Typography variant="body2">{date}</Typography>
            <Typography variant="caption" color="textSecondary">
              {time}
            </Typography>
          </Box>
        );
      },
    },
    {
      field: 'endTime',
      headerName: 'End Time',
      flex: 1,
      renderCell: (params: any) => {
        const [date, time] = params.value.split('\n');
        return (
          <Box>
            <Typography variant="body2">{date}</Typography>
            <Typography variant="caption" color="textSecondary">
              {time}
            </Typography>
          </Box>
        );
      },
    },
    { field: 'duration', headerName: 'Duration', flex: 1 },
  ];

  const TableData = filtedData.map((excursion) => {
    const durationMs =
      ((excursion.time_duration.days || 0) * 24 * 60 * 60 +
        (excursion.time_duration.hours || 0) * 60 * 60 +
        (excursion.time_duration.minutes || 0) * 60) *
      1000;

    const formattedAsTime = dayjs.utc(durationMs).format(timeFormat);

    const formatToThreeDecimals = (value: string | number): string => {
      const [intPart, decPart] = Number(value).toFixed(3).split('.');
      const paddedInt = intPart.padStart(2, '0');
      return `${paddedInt}.${decPart}`;
    };

    return {
      id: excursion.id,
      measurementTag: excursion.measureTag || 'NA',
      description: excursion.alert.description,
      minmaxavg: `${formatToThreeDecimals(excursion.min_value)} | ${formatToThreeDecimals(
        excursion.max_value,
      )} | ${formatToThreeDecimals(excursion.avg_value)}`,
      startTime: `${dayjs(excursion.start_time).format(dateFormat)}\n${dayjs(
        excursion.start_time,
      ).format(timeFormat)}`,
      endTime: `${dayjs(excursion.end_time).format(dateFormat)}\n${dayjs(excursion.end_time).format(
        timeFormat,
      )}`,
      duration: `${formattedAsTime}`,
    };
  });

  return (
    <Box>
      <AlertSnackbar {...snackbarState} />
      {isFetching ? (
        <Loader
          style={{
            height: '70vh',
          }}
        />
      ) : (
        <>
          {isError && <Typography>Error fetching alerts</Typography>}
          {excursions?.items && (
            <Box>
              <DataTable
                columns={DataTableColumns}
                data={TableData}
                filterOptions={
                  <>
                    <Card sx={{ p: 1, height: 600 }}>
                      <Select
                        value={filterFields.state ?? ''}
                        onChange={(e) =>
                          setFilterFields({
                            ...filterFields,
                            state: e.target.value || undefined,
                          })
                        }
                        input={
                          <OutlinedInput
                            label="State"
                            sx={{
                              mt: 2,
                              '& legend': {
                                maxWidth: '100%',
                                height: 'fit-content',
                                '& span': {
                                  opacity: 1,
                                },
                              },
                            }}
                          />
                        }
                        variant="outlined"
                        fullWidth
                        label="State"
                      >
                        <MenuItem value="EXCEEDED">EXCEEDED</MenuItem>
                        <MenuItem value="NORMAL">NORMAL</MenuItem>
                      </Select>

                      <DatePicker
                        sx={{ width: '100%', my: 3 }}
                        label="From Date"
                        value={filterFields.fromDate ? dayjs(filterFields.fromDate) : null}
                        format={dateTimeFormat.split(' ')[0]}
                        onChange={(newValue) => {
                          setFilterFields({
                            ...filterFields,
                            fromDate: newValue ? dayjs(newValue).format('YYYY-MM-DD') : undefined,
                          });
                        }}
                        maxDate={filterFields.toDate ? dayjs(filterFields.toDate) : null}
                      />

                      <DatePicker
                        sx={{ width: '100%' }}
                        label="To Date"
                        format={dateTimeFormat.split(' ')[0]}
                        value={filterFields.toDate ? dayjs(filterFields.toDate) : null}
                        onChange={(newValue) => {
                          setFilterFields({
                            ...filterFields,
                            toDate: newValue ? dayjs(newValue).format('YYYY-MM-DD') : undefined,
                          });
                        }}
                        minDate={filterFields.fromDate ? dayjs(filterFields.fromDate) : null}
                      />
                    </Card>

                    <Box mt={2} display={'flex'} justifyContent={'space-between'}>
                      <Button
                        variant="contained"
                        color="primary"
                        fullWidth
                        onClick={async () => {
                          setFilteredData(
                            excursions.items.filter((excursion) => {
                              // Convert excursion dates to timestamps
                              const startTimeTimestamp = new Date(excursion.start_time).getTime();
                              const endTimeTimestamp = new Date(excursion.end_time).getTime();

                              // Convert filter fields to timestamps
                              const fromDateTimestamp = filterFields.fromDate
                                ? new Date(filterFields.fromDate).getTime()
                                : null;
                              const toDateTimestamp = filterFields.toDate
                                ? new Date(filterFields.toDate).getTime()
                                : null;

                              // Check date range independently
                              const matchesDateRange =
                                (!fromDateTimestamp || endTimeTimestamp >= fromDateTimestamp) &&
                                (!toDateTimestamp || startTimeTimestamp <= toDateTimestamp);

                              // Check state filter independently
                              const matchesState =
                                !filterFields.state || filterFields.state === excursion.alert.state;

                              // Allow data to pass if it matches either filter
                              return matchesDateRange || matchesState;
                            }),
                          );

                          setFilterFields({ ...filterFields, showFilters: true });
                          await refetch();
                        }}
                      >
                        Apply Filters
                      </Button>

                      <Button
                        variant="outlined"
                        fullWidth
                        color="error"
                        onClick={() => {
                          setFilterFields({
                            measurementTag: undefined,
                            state: undefined,
                            fromDate: undefined,
                            toDate: undefined,
                            showFilters: true,
                          });
                          setFilteredData(excursions.items);
                        }}
                      >
                        Clear Filters
                      </Button>
                    </Box>
                  </>
                }
                filteredData={
                  <>
                    {filterFields.showFilters && (
                      <Box sx={{ mb: 1 }}>
                        {(filterFields.fromDate || filterFields.toDate) && (
                          <Chip
                            label={`Date Range: ${
                              filterFields.fromDate ? `From ${filterFields.fromDate}` : ''
                            }${filterFields.fromDate && filterFields.toDate ? ' | ' : ''}${
                              filterFields.toDate ? `To ${filterFields.toDate}` : ''
                            }`}
                            onDelete={() => {
                              setFilterFields({
                                ...filterFields,
                                toDate: undefined,
                                fromDate: undefined,
                              });
                              setFilteredData(
                                excursions.items.filter((excursion) => {
                                  const startDateOnly = new Date(excursion.start_time)
                                    .toISOString()
                                    .split('T')[0];
                                  const endDateOnly = new Date(excursion.end_time)
                                    .toISOString()
                                    .split('T')[0];

                                  const matchesFromDate =
                                    !filterFields.fromDate ||
                                    startDateOnly >= filterFields.fromDate;

                                  const matchesToDate =
                                    !filterFields.toDate || endDateOnly <= filterFields.toDate;

                                  return matchesFromDate && matchesToDate;
                                }),
                              );
                            }}
                            sx={{ mr: 1, mb: 1 }}
                          />
                        )}

                        {filterFields.state && (
                          <Chip
                            label={`State: ${filterFields.state}`}
                            onDelete={() => {
                              setFilterFields({
                                ...filterFields,
                                state: undefined,
                              });
                              setFilteredData(
                                excursions.items.filter((excursion) => {
                                  const matchesState =
                                    !filterFields.state ||
                                    filterFields.state === excursion.alert.state;

                                  return matchesState;
                                }),
                              );
                            }}
                            sx={{ mr: 1, mb: 1 }}
                          />
                        )}

                        {filterFields.fromDate !== '' ||
                        filterFields.toDate !== '' ||
                        filterFields.state !== '' ? (
                          <Chip
                            label={'Clear all'}
                            sx={{
                              color: 'primary.main',
                              border: 'unset',
                              mb: 1,
                            }}
                            onClick={() => {
                              setFilterFields({
                                measurementTag: undefined,
                                state: undefined,
                                fromDate: undefined,
                                toDate: undefined,
                                showFilters: false,
                              });
                            }}
                          />
                        ) : null}
                      </Box>
                    )}
                  </>
                }
              />
            </Box>
          )}
        </>
      )}
    </Box>
  );
};

export default ExcursionsList;
