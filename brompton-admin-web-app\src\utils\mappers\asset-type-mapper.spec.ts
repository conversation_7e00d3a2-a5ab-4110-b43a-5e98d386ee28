import { assetTypePathMapper } from './asset-type-mapper';

const engineAssetType = { id: 43, name: 'Engine', parentType: null, asset_template_count: 0 };
const fanAssetType = { id: 55, name: 'Fan', parentType: null, asset_template_count: 0 };
const bladeAssetType = {
  id: 44,
  name: 'Blade',
  parentType: fanAssetType.id,
  asset_template_count: 0,
};

describe('Asset type path mapper', () => {
  describe('single root asset type', () => {
    it('should have a path equal to its name', () => {
      const result = assetTypePathMapper([engineAssetType]);

      expect(result[0].label).toBe('Engine');
    });
  });

  describe('two root asset type', () => {
    it('should have path equal to their name', () => {
      const result = assetTypePathMapper([engineAssetType, fanAssetType]);

      expect(result[0].label).toBe('Engine');
      expect(result[1].label).toBe('Fan');
    });
  });

  describe('child asset type', () => {
    it("should have path equal to its name prepended by its parent's", () => {
      const result = assetTypePathMapper([fanAssetType, bladeAssetType]);

      expect(result[0].label).toBe('Fan');
      expect(result[1].label).toBe('Fan > Blade');
    });
  });

  describe('grand child asset type', () => {
    it("should have path equal to its name prepended by its parent's and grandparent's", () => {
      const result = assetTypePathMapper([
        fanAssetType,
        bladeAssetType,
        { id: 22, name: 'Axis', parentType: bladeAssetType.id, asset_template_count: 0 },
      ]);

      expect(result[0].label).toBe('Fan');
      expect(result[2].label).toBe('Fan > Blade > Axis');
    });
  });

  describe('grand child asset type with missing grand parent', () => {
    it('should throw an error', () => {
      expect(() =>
        assetTypePathMapper([
          bladeAssetType,
          { id: 22, name: 'Axis', parentType: bladeAssetType.id, asset_template_count: 0 },
        ]),
      ).toThrowError('Asset parent type not found');
    });
  });
});
