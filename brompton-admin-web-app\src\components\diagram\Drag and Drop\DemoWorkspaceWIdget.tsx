import * as React from 'react';
import { styled } from '@mui/system';

export interface DemoWorkspaceWidgetProps {
  children: React.ReactNode;
  buttons?: React.ReactNode;
}

const Toolbar = styled('div')({
  padding: '5px',
  display: 'flex',
  flexShrink: 0,
});

const Content = styled('div')({
  flexGrow: 1,
  height: '100%',
});

const Container = styled('div')({
  background: 'black',
  display: 'flex',
  flexDirection: 'column',
  height: '100%',
  borderRadius: '5px',
  overflow: 'hidden',
});

export const DemoButton = styled('button')({
  background: 'rgb(60, 60, 60)',
  fontSize: '14px',
  padding: '5px 10px',
  border: 'none',
  color: 'white',
  outline: 'none',
  cursor: 'pointer',
  margin: '2px',
  borderRadius: '3px',
  '&:hover': {
    background: 'rgb(0, 192, 255)',
  },
});

export const DemoWorkspaceWidget: React.FC<DemoWorkspaceWidgetProps> = (props) => {
  return (
    <Container>
      <Toolbar>{props.buttons}</Toolbar>
      <Content>{props.children}</Content>
    </Container>
  );
};
