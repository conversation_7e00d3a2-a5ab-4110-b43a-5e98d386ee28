import { Button, Typography } from '@mui/material';
import CustomDialog from '../CustomDialog';
import CancelIcon from '@mui/icons-material/Cancel';
import { useDispatch, useSelector } from 'react-redux';
import { useRouter } from 'next/router';
import { dashboardSlice } from '~/redux/slices/dashboardSlice';
import { getActiveCustomer } from '~/redux/selectors/customerSelectors';
type ConfirmChangeProps = {
  confirm: boolean;
  setConfirm: (value: boolean) => void;
  dashboardCustomer: 'Dashboard' | 'Customer' | null;
  setDashboardCustomer: (value: 'Dashboard' | 'Customer' | null) => void;
  customer: any;
  setCustomer: (value: any) => void;
  dashboard: any;
  setDashboard: (value: any) => void;
};
const ConfirmChange = ({
  confirm,
  customer,
  dashboard,
  dashboardCustomer,
  setConfirm,
  setCustomer,
  setDashboard,
  setDashboardCustomer,
}: ConfirmChangeProps) => {
  const dispatch = useDispatch();
  const router = useRouter();
  const activeCustomer = useSelector(getActiveCustomer);
  return (
    <>
      <CustomDialog
        title="You have unsaved changes."
        content={<Typography color={'error'}>Do you want to still proceed?</Typography>}
        dialogActions={
          <>
            <Button
              onClick={() => {
                setConfirm(false);
              }}
              variant="outlined"
              startIcon={<CancelIcon />}
            >
              Cancel
            </Button>
            <Button
              onClick={() => {
                setConfirm(false);
                dispatch(dashboardSlice.actions.selectMainPanel('chart'));
                if (dashboardCustomer === 'Customer' && customer) {
                  dispatch(dashboardSlice.actions.setCurrentDashboardTitle(''));
                  dispatch(dashboardSlice.actions.resetDashboardCrumb());
                  router.push(`/customer/${customer.id}`);
                  dispatch(dashboardSlice.actions.setActiveCustomer(customer));
                  setCustomer(null);
                }
                if (dashboardCustomer === 'Dashboard' && dashboard) {
                  dispatch(dashboardSlice.actions.resetDashboardCrumb());
                  dispatch(dashboardSlice.actions.setCurrentDashboardTitle(dashboard.title));
                  router.push(`/customer/${activeCustomer?.id}/dashboard/${dashboard.id}`);
                  setDashboard(null);
                }
              }}
              variant="contained"
              color="error"
            >
              Proceed
            </Button>
          </>
        }
        onClose={() => setConfirm(false)}
        open={confirm}
      />
    </>
  );
};
export default ConfirmChange;
