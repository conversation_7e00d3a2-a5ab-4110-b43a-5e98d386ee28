import { AlertColor } from '@mui/material';
import { useReducer } from 'react';

type SnackbarState = {
  open: boolean;
  message: string;
  severity: AlertColor;
  onClose: () => unknown;
};

type SnackbarAction = { type: 'open'; message: string; severity: AlertColor } | { type: 'close' };

export const useSnackbar = (): [
  SnackbarState,
  (message: string) => void,
  (message: string) => void,
] => {
  const [snackbarState, snackbarDispatch] = useReducer(
    (state: SnackbarState, action: SnackbarAction) =>
      action.type === 'open'
        ? {
            ...state,
            open: true,
            message: action.message,
            severity: action.severity,
          }
        : {
            ...state,
            open: false,
          },
    {
      open: false,
      message: '',
      severity: 'info',
      onClose: () => snackbarDispatch({ type: 'close' }),
    },
  );
  return [
    snackbarState,
    (message) => snackbarDispatch({ type: 'open', message, severity: 'success' }),
    (message) => snackbarDispatch({ type: 'open', message, severity: 'error' }),
  ];
};
