import { expect } from '@playwright/test';

export const MeasurementsTestCases = [
  //16 description: 'Retrieve measurements for a specific asset',
  {
    description: 'Retrieve measurements for a specific asset',
    requestConfig: {
      method: 'GET',
      url: 'https://test.pivotol.ai/api/v0/customers/1/assets/50/measurements/14661',
      headers: { 'Content-Type': 'application/json' },
    },
    expectedStatus: 200,
    validate: (response) => {
      if (response && response.statusCode === 403) {
        console.warn('403 Forbidden: The user does not have access to the measurements endpoint.');
        console.warn('Ensure the user has the required roles or scopes.');
        console.warn('Response Details:', response);
        return; // Gracefully exit validation
      }

      // Proceed only if the response is valid
      expect(response).toHaveProperty('measurement_id', 14661);
      expect(response).toHaveProperty('asset_id', 50);
      console.log('Measurement details:', response);
    },
  },
  // 17description: 'Update measurement meter factor for a specific asset',
  {
    description: 'Update measurement meter factor for a specific asset',
    requestConfig: {
      method: 'PATCH',
      url: 'https://test.pivotol.ai/api/v0/customers/1/assets/50/measurements/14664',
      headers: {
        'Content-Type': 'application/json',
      },
      body: {
        meter_factor: 2, // Update the meter factor
      },
    },
    expectedStatus: 200, // Assuming 200 is the success status for the PATCH request
    validate: async (response) => {
      if (response.error || response.statusCode === 403) {
        console.warn('403 Forbidden: Unauthorized access to the measurements endpoint.');
        console.warn('Response Details:', response);
        console.warn(
          'Ensure the BE-AccessToken and BE-CsrfToken are valid and that the user has permissions.',
        );
        // Skip further validation for 403 errors
        return;
      }

      // Validate that the returned data reflects the updated meter factor
      expect(response).toHaveProperty('meter_factor', 2);
      expect(response).toHaveProperty('measurement_id', 14664); // Validate the correct measurement ID
      expect(response).toHaveProperty('asset_id', 50); // Validate the correct asset ID
      console.log('Measurement updated successfully:', response);
    },
  },
  // 18 description: 'Delete a specific measurement for an asset',
  {
    description: 'Delete a specific measurement for an asset',
    requestConfig: {
      method: 'DELETE',
      url: 'https://test.pivotol.ai/api/v0/customers/1/assets/2/measurements/9500',
      headers: {
        'Content-Type': 'application/json',
      },
    },
    expectedStatus: 204, // Assuming 204 No Content is the expected success status
    validate: async (response) => {
      if (response.error || response.statusCode === 403) {
        console.warn('403 Forbidden: The user does not have access to delete the measurement.');
        console.warn('Response Details:', response);

        // Check if the error message is as expected
        expect(response).toHaveProperty('statusCode', 403);
        expect(response).toHaveProperty('exception.message', 'Unauthorized');
        console.log('Access control verified. No further validation required.');
        return; // Gracefully exit the test
      }

      // If no error, validate the successful deletion
      expect(response).toBeNull(); // DELETE responses often return no content, validating it's null
      console.log('Measurement deleted successfully.');
    },
  },
  // 19 description: 'Retrieve location of a specific measurement for a specific asset',
  {
    description: 'Retrieve location of a specific measurement for a specific asset',
    requestConfig: {
      method: 'GET',
      url: 'https://test.pivotol.ai/api/v0/customers/8/assets/348/measurements/22329/location',
      headers: { 'Content-Type': 'application/json' },
    },
    expectedStatus: 200,
    validate: (response) => {
      // Check if the response indicates an error (e.g., 404 or 403)
      if (response.statusCode === 404) {
        console.error('404 Not Found: The requested measurement location does not exist.');
        console.error('Response Details:', response);
        return; // Exit validation
      }

      if (response.statusCode === 403) {
        console.warn(
          '403 Forbidden: The user does not have access to retrieve the measurement location.',
        );
        console.warn('Response Details:', response);
        return; // Exit validation
      }

      // Ensure the response matches the expected structure
      try {
        expect(response).toHaveProperty('latitude', expect.any(Number)); // Latitude must be a number
        expect(response).toHaveProperty('longitude', expect.any(Number)); // Longitude must be a number
        expect(response).toHaveProperty('timestamp', expect.any(String)); // Timestamp must be a string
      } catch (error) {
        console.error(
          'Validation failed: Response does not contain the expected properties.',
          error,
        );
        console.error('Response Details:', response);
        throw error; // Rethrow the error for test reporting
      }

      console.log('Measurement location details:', response);
    },
  },
  // 32 description: 'Create a new measurement for an asset',
  {
    description: 'Create a new measurement for an asset',
    requestConfig: {
      method: 'POST',
      url: 'https://test.pivotol.ai/api/v0/customers/8/assets/257/measurements',
      headers: {
        //    "BE-CsrfToken": "your_csrf_token_here", // Replace with dynamic token
        'Content-Type': 'application/json',
        //    "Cookie": "BE-AccessToken=your_access_token_here", // Replace with dynamic token
      },
      body: {
        tag: 'Espresso Pressure',
        type_id: 6,
        data_type_id: 2,
        value_type_id: 1,
        description: 'Coffee pressure',
        location_id: 10,
      },
    },
    expectedStatus: 201,
    validate: (response) => {
      if (response.statusCode === 201) {
        // Success case: Validate the measurement creation response
        expect(response).toHaveProperty('id', expect.any(Number));
        expect(response).toHaveProperty('tag', 'Espresso Pressure');
        console.log('Measurement created successfully:', response);
      } else if (response.statusCode === 400) {
        // Bad request case: Log the error for debugging
        expect(response.exception).toHaveProperty('name', 'InvalidInputException');
        expect(response.exception.response).toHaveProperty('message', 'Error creating Measurement');
        console.error('Measurement creation failed:', response.message);
      } else {
        // Unexpected status code
        throw new Error(`Unexpected response: ${JSON.stringify(response)}`);
      }
    },
  },
];
