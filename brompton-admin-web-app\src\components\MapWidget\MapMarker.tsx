import AddIcon from '@mui/icons-material/Add';
import CancelIcon from '@mui/icons-material/Cancel';
import LaunchIcon from '@mui/icons-material/Launch';
import {
  Autocomplete,
  Box,
  Button,
  Card,
  CardHeader,
  Checkbox,
  FormControl,
  FormControlLabel,
  FormGroup,
  FormHelperText,
  FormLabel,
  Grid,
  IconButton,
  Input,
  TextField,
  Tooltip,
  Typography,
} from '@mui/material';
import { useRouter } from 'next/router';
import { Dispatch, SetStateAction, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useFetchUniqueLocation } from '~/hooks/useFetchUniqueLocation';
import { Role, useRolePermission } from '~/hooks/useRolePermission';
import { LocationDTO } from '~/measurements/domain/types';
import { useGetDashboardByCustomerIdQuery } from '~/redux/api/dashboardApi';
import { getActiveCustomer } from '~/redux/selectors/customerSelectors';
import { getMetricsIdToName, isDashboardDirty } from '~/redux/selectors/dashboardSelectors';
import { getDbMeasureIdToName } from '~/redux/selectors/treeSelectors';
import { getDeletedWidgets, getWidgets } from '~/redux/selectors/widgetSelectors';
import { dashboardSlice } from '~/redux/slices/dashboardSlice';
import { MapWidget } from '~/types/widgets';
import { formatMetricLabel } from '~/utils/utils';
import MultiMeasureSelection from '../common/MultiMeasureSelection';
import MapMeasureSelect from './MapMeasureSelect';

type MapMarkerProps = {
  currentSettings: number;
  settings: MapWidget;
  handleSettingsChange: Dispatch<SetStateAction<MapWidget>>;
};
const MapMarker = ({ settings, handleSettingsChange, currentSettings }: MapMarkerProps) => {
  const router = useRouter();
  const dispatch = useDispatch();
  const selectedDbMeasureIdToName = useSelector(getDbMeasureIdToName);
  const metricsIdToName = useSelector(getMetricsIdToName);
  const activeCustomer = useSelector(getActiveCustomer);
  const { hasDashboardPermission } = useRolePermission();
  const widgets = useSelector(getWidgets);
  const deleteWidgets = useSelector(getDeletedWidgets);
  const isDashboardStateDirty = useSelector(isDashboardDirty);
  const {
    isUniqueLocation,
    location,
    data: markerDetails,
  } = useFetchUniqueLocation({
    currentSettings,
    settings,
  });
  const { data: dashboardList, isLoading: isLoadingDashboards } = useGetDashboardByCustomerIdQuery(
    {
      customerId: activeCustomer?.id ?? 0,
      search: null,
    },
    {
      skip: !activeCustomer?.id,
    },
  );
  useEffect(() => {
    if (!settings.markers[currentSettings].overrideLocation) {
      if (settings.markers[currentSettings].assetMeasures.length === 0) {
        handleSettingsChange(({ markers, ...prevState }) => {
          const updatedMarkers = markers.map((marker, index) => {
            if (index === currentSettings) {
              return {
                ...marker,
                location: {
                  lat: 0,
                  lon: 0,
                },
              };
            }
            return marker;
          });
          return {
            ...prevState,
            isValid: true,
            markers: updatedMarkers,
          };
        });
      } else if (Object.keys(markerDetails).length > 1) {
        handleSettingsChange(({ markers, ...prevState }) => {
          const updatedMarkers = markers.map((marker, index) => {
            if (index === currentSettings) {
              return {
                ...marker,
                location: {
                  lat: 0,
                  lon: 0,
                },
              };
            }
            return marker;
          });
          return {
            ...prevState,
            isValid: false,
            markers: updatedMarkers,
          };
        });
      } else {
        Object.keys(markerDetails).map((key) => {
          const location = JSON.parse(key) as LocationDTO;
          handleSettingsChange(({ markers, ...prevState }) => {
            const updatedMarkers = markers.map((marker, index) => {
              if (index === currentSettings) {
                return {
                  ...marker,
                  location: {
                    lat: location.latitude,
                    lon: location.longitude,
                  },
                };
              }
              return marker;
            });
            return {
              ...prevState,
              isValid: true,
              markers: updatedMarkers,
            };
          });
        });
      }
    }
  }, [
    location,
    settings.markers[currentSettings].selectedTitles,
    markerDetails,
    settings.markers[currentSettings].overrideLocation,
  ]);

  useEffect(() => {
    if (settings.mode === 'template') {
      handleSettingsChange(({ markers, ...prevState }) => {
        const updatedMarkers = markers.map((marker, index) => {
          if (index === currentSettings) {
            return {
              ...marker,
              assetMeasures: [],
            };
          }
          return marker;
        });
        return {
          ...prevState,
          markers: updatedMarkers,
        };
      });
    }
  }, [settings.mode, settings.markers[currentSettings].selectedTitles]);

  const handleChangeMeasure = (updatedTitles: string[]) => {
    handleSettingsChange(({ markers, ...prevState }) => {
      const updatedMarkers = markers.map((marker, index) => {
        if (index === currentSettings) {
          const removedTitles = marker.selectedTitles.filter(
            (title) => !updatedTitles.includes(title),
          );
          const addedTitles = updatedTitles.filter(
            (title) => !marker.selectedTitles.includes(title),
          );
          const newLabelAndUnits = { ...marker.labelAndUnits };
          removedTitles.forEach((title) => {
            delete newLabelAndUnits[title];
          });
          addedTitles.forEach((title) => {
            newLabelAndUnits[title] = {
              label: selectedDbMeasureIdToName[title],
              unit: '',
              value: '',
            };
          });
          return {
            ...marker,
            selectedTitles: updatedTitles,
            labelAndUnits: newLabelAndUnits,
          };
        }
        return marker;
      });
      return {
        ...prevState,
        markers: updatedMarkers,
      };
    });
  };
  const handleMarkerName = (e: React.ChangeEvent<HTMLInputElement>) => {
    handleSettingsChange(({ markers, ...prevState }) => {
      const { name, value } = e.target;
      const updatedMarkers = markers.map((marker, index) => {
        if (index === currentSettings) {
          return {
            ...marker,
            markerName: value,
          };
        }
        return marker;
      });
      return {
        ...prevState,
        markers: updatedMarkers,
      };
    });
  };
  const handleMarkerColor = (e: React.ChangeEvent<HTMLInputElement>) => {
    handleSettingsChange(({ markers, ...prevState }) => {
      const { name, value } = e.target;
      const updatedMarkers = markers.map((marker, index) => {
        if (index === currentSettings) {
          return {
            ...marker,
            color: value,
          };
        }
        return marker;
      });
      return {
        ...prevState,
        markers: updatedMarkers,
      };
    });
  };
  const handleDeleteMarker = () => {
    handleSettingsChange(({ markers, ...prevState }) => {
      // Create a copy of the markers array to avoid direct mutation
      const updatedMarkers = [...markers];
      // Remove the marker at the currentSettings index
      updatedMarkers.splice(currentSettings, 1);

      // Return the updated state with the modified markers
      return {
        ...prevState,
        markers: updatedMarkers,
      };
    });
  };
  const setOnChangeLabel = (e: React.ChangeEvent<HTMLInputElement>) => {
    handleSettingsChange(({ markers, ...prevState }) => {
      const { name, value } = e.target;
      const updatedMarkers = markers.map((marker, index) => {
        if (index === currentSettings) {
          const newLabelAndUnits = JSON.parse(JSON.stringify(marker.labelAndUnits));
          newLabelAndUnits[name].label = value;
          return {
            ...marker,
            labelAndUnits: newLabelAndUnits,
          };
        }
        return marker;
      });
      return {
        ...prevState,
        markers: updatedMarkers,
      };
    });
  };
  const setOnChangeUnit = (e: React.ChangeEvent<HTMLInputElement>) => {
    handleSettingsChange(({ markers, ...prevState }) => {
      const { name, value } = e.target;
      const updatedMarkers = markers.map((marker, index) => {
        if (index === currentSettings) {
          const newLabelAndUnits = JSON.parse(JSON.stringify(marker.labelAndUnits));
          newLabelAndUnits[name].unit = value;
          return {
            ...marker,
            labelAndUnits: newLabelAndUnits,
          };
        }
        return marker;
      });
      return {
        ...prevState,
        markers: updatedMarkers,
      };
    });
  };
  const handleChangeLocationLat = (e: React.ChangeEvent<HTMLInputElement>) => {
    handleSettingsChange(({ markers, ...rest }) => {
      const { name, value } = e.target;
      const updatedMarkers = markers.map((marker, index) => {
        if (index === currentSettings) {
          return {
            ...marker,
            location: {
              ...marker.location,
              lat: parseFloat(value),
            },
          };
        }
        return marker;
      });
      return {
        ...rest,
        markers: updatedMarkers,
      };
    });
  };
  const handleChangeLocationLong = (e: React.ChangeEvent<HTMLInputElement>) => {
    handleSettingsChange(({ markers, ...rest }) => {
      const { name, value } = e.target;
      const updatedMarkers = markers.map((marker, index) => {
        if (index === currentSettings) {
          return {
            ...marker,
            location: {
              ...marker.location,
              lon: parseFloat(value),
            },
          };
        }
        return marker;
      });
      return {
        ...rest,
        markers: updatedMarkers,
      };
    });
  };

  const addMapMeasureSelect = () => {
    handleSettingsChange(({ markers, ...rest }) => {
      const updatedMarkers = markers.map((marker, index) => {
        if (index === currentSettings) {
          return {
            ...marker,
            assetMeasures: [
              ...marker.assetMeasures,
              {
                assetId: '',
                measureId: '',
              },
            ],
          };
        }
        return marker;
      });
      return {
        ...rest,
        markers: updatedMarkers,
      };
    });
  };

  const removeMapMeasureSelect = (removeIndex: number) => {
    handleSettingsChange(({ markers, ...rest }) => {
      const updatedMarkers = markers.map((marker, index) => {
        if (index === currentSettings) {
          return {
            ...marker,
            assetMeasures: marker.assetMeasures.filter((_, i) => i !== removeIndex),
          };
        }
        return marker;
      });
      return {
        ...rest,
        markers: updatedMarkers,
      };
    });
  };

  const handleOverrideLocationChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const checked = event.target.checked;

    handleSettingsChange(({ markers, ...prevState }) => {
      const updatedMarkers = markers.map((marker, index) => {
        if (index === currentSettings) {
          return {
            ...marker,
            overrideLocation: checked,
            location: checked
              ? { lat: marker.location.lat || 0, lon: marker.location.lon || 0 }
              : { lat: 0, lon: 0 }, // Reset the location if not overriding
          };
        }
        return marker;
      });

      return {
        ...prevState,
        markers: updatedMarkers,
      };
    });
  };
  const handleChangeDashboard = (
    e: React.SyntheticEvent,
    value: { id: number; title: string } | null,
  ) => {
    handleSettingsChange(({ markers, ...prevState }) => {
      const updatedMarkers = markers.map((marker, index) => {
        if (index === currentSettings) {
          return {
            ...marker,
            dashboard: value,
          };
        }
        return marker;
      });

      return {
        ...prevState,
        markers: updatedMarkers,
      };
    });
  };
  const handleDashboardChange = () => {
    if (!hasDashboardPermission('dashboard.update', Role.POWER_USER)) {
      return false;
    }
    if (
      widgets.filter((widget) => widget.type !== 'chart' && widget.settings.isDirty).length > 0 ||
      deleteWidgets.length > 0 ||
      widgets.filter((widget) => widget.type === 'chart' && widget.settings?.settings.isDirty)
        .length > 0 ||
      isDashboardStateDirty
    ) {
      // setConfirm(true);
      return true;
    }
    return false;
  };
  const openDashboard = () => {
    // if (handleDashboardChange()) return;
    const dashboard = settings.markers[currentSettings].dashboard;
    if (dashboard !== null) {
      dispatch(
        dashboardSlice.actions.setDashboardCrumb({
          dashboardId: dashboard.id,
          title: dashboard.title,
        }),
      );
      dispatch(dashboardSlice.actions.setCurrentDashboardId(dashboard.id));
      dispatch(dashboardSlice.actions.setCurrentDashboardTitle(dashboard.title));
      router.push(`/customer/${activeCustomer?.id}/dashboard/${dashboard.id}`);
    }
  };
  return (
    <Box>
      <CardHeader
        sx={{ pl: 1, pr: 1, pt: 0 }}
        title={
          <Box display="flex" justifyContent="space-between" width="100%" mt={1.5}>
            <Typography variant="h6">Marker #{currentSettings + 1}</Typography>
            <Button
              startIcon={<CancelIcon />}
              variant="contained"
              color="error"
              onClick={handleDeleteMarker}
            >
              Remove
            </Button>
          </Box>
        }
      ></CardHeader>
      <Grid container spacing={2}>
        {/* Marker Name */}
        <Grid item xs={12} sm={6}>
          <FormControl fullWidth>
            <FormLabel>Marker Name</FormLabel>
            <Input
              fullWidth
              sx={{ mb: 1 }}
              defaultValue={settings.markers[currentSettings].markerName}
              onChange={handleMarkerName}
            />
          </FormControl>
        </Grid>

        {/* Marker Color */}
        <Grid item xs={12} sm={6}>
          <FormControl fullWidth>
            <FormLabel>Marker Color</FormLabel>
            <Input
              type="color"
              fullWidth
              sx={{ pl: 1, pr: 1 }}
              defaultValue={settings.markers[currentSettings]?.color || '#1EE61E'}
              value={settings.markers[currentSettings]?.color}
              onChange={handleMarkerColor}
            />
          </FormControl>
        </Grid>
      </Grid>
      <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
        <FormControlLabel
          control={
            <Checkbox
              checked={settings.markers[currentSettings]?.overrideLocation}
              onChange={handleOverrideLocationChange}
            />
          }
          label="Override default location"
        />
      </Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
        <FormGroup sx={{ pt: 0, pb: 0, width: '49%' }}>
          <TextField
            type="number"
            label="Latitude"
            disabled={!settings.markers[currentSettings]?.overrideLocation}
            onChange={handleChangeLocationLat}
            value={settings.markers[currentSettings]?.location.lat || 0}
            defaultValue={settings.markers[currentSettings]?.location.lat || 0}
            variant="outlined"
            margin="normal"
            fullWidth
          />
        </FormGroup>
        <FormGroup sx={{ pt: 0, pb: 0, width: '49%' }}>
          <TextField
            type="number"
            disabled={!settings.markers[currentSettings]?.overrideLocation}
            label="Longitude"
            onChange={handleChangeLocationLong}
            value={settings.markers[currentSettings]?.location.lon || 0}
            defaultValue={settings.markers[currentSettings]?.location.lon || 0}
            variant="outlined"
            margin="normal"
            fullWidth
          />
        </FormGroup>
      </Box>
      <Box sx={{ display: 'flex', gap: 1 }}>
        <Autocomplete
          loading={isLoadingDashboards}
          id="dashboards-combo-box"
          options={
            dashboardList?.items?.map((dashboard) => {
              return { id: dashboard.id, title: dashboard.title };
            }) ?? []
          }
          getOptionLabel={(option) => option.title}
          onChange={handleChangeDashboard}
          sx={{ width: '100%', mt: 1 }}
          isOptionEqualToValue={(option, value) => option.id === value.id}
          value={settings.markers[currentSettings].dashboard ?? null}
          renderInput={(params) => <TextField {...params} label="Link Dashboard" />}
        />
        {settings.markers[currentSettings].dashboard !== null &&
          settings.markers[currentSettings].dashboard?.id && (
            <IconButton
              disableRipple
              disableTouchRipple
              id={'title-widget-link-icon'}
              edge="start"
              color="inherit"
              sx={{
                zIndex: 10,
                mr: 0.5,
              }}
              onClick={openDashboard}
            >
              <Tooltip
                title={
                  <Typography variant="body2" color="inherit" fontSize={'0.7rem'}>
                    Open Dashboard - {settings.markers[currentSettings].dashboard?.title ?? ''}
                  </Typography>
                }
              >
                <LaunchIcon />
              </Tooltip>
            </IconButton>
          )}
      </Box>
      <Box>
        <FormHelperText error={Object.keys(markerDetails).length > 1}>
          {Object.keys(markerDetails).length > 1 ? (
            <>
              <Typography>Please select measures with same locations</Typography>
              {Object.keys(markerDetails).map((key) => {
                return markerDetails[key].map((item, index) => {
                  return (
                    <Box key={index} sx={{ display: 'flex', justifyContent: 'space-between' }}>
                      {formatMetricLabel(selectedDbMeasureIdToName[item.measureData])} - Latitude -{' '}
                      {item.currLocation?.latitude} Longitude- {item.currLocation?.longitude}
                    </Box>
                  );
                });
              })}
            </>
          ) : null}
        </FormHelperText>
      </Box>
      <FormHelperText error={!isUniqueLocation}>
        {isUniqueLocation ? '' : 'Location is not match with other measurements location.'}
      </FormHelperText>

      {settings.mode === 'template' && (
        <Box mt={2}>
          <MultiMeasureSelection
            mode={settings.mode}
            handleChangeMeasure={handleChangeMeasure}
            selectedMeasureNames={settings.markers[currentSettings].selectedTitles}
          />
          {settings.markers[currentSettings].selectedTitles
            .filter((title) => title !== '')
            .map((title, index) => {
              return (
                <Box
                  key={index}
                  sx={{
                    display: 'flex',
                    justifyContent: 'space-between',
                  }}
                >
                  <FormGroup
                    sx={{
                      width: '49%',
                    }}
                  >
                    <TextField
                      name={title}
                      onChange={setOnChangeLabel}
                      value={settings.markers[currentSettings].labelAndUnits[title]?.label}
                      label={'Label  for ' + metricsIdToName[title]}
                      variant="outlined"
                      margin="normal"
                      fullWidth
                    />
                  </FormGroup>
                  <FormGroup
                    sx={{
                      width: '49%',
                    }}
                  >
                    <TextField
                      name={title}
                      onChange={setOnChangeUnit}
                      value={settings.markers[currentSettings].labelAndUnits[title]?.unit}
                      label={'Unit for ' + metricsIdToName[title]}
                      variant="outlined"
                      margin="normal"
                      fullWidth
                    />
                  </FormGroup>
                </Box>
              );
            })}
        </Box>
      )}
      {settings.mode === 'dashboard' && (
        <>
          <Box mt={2}>
            <Box sx={{ display: 'flex', justifyContent: 'end' }}>
              <Button
                variant="contained"
                sx={{ mb: 2 }}
                startIcon={<AddIcon />}
                onClick={addMapMeasureSelect}
              >
                Add
              </Button>
            </Box>
            {settings.markers[currentSettings].assetMeasures.map((assetMeasure, index) => (
              <Box key={index} mb={2}>
                <Box sx={{ display: 'flex', gap: 2, alignItems: 'center' }}>
                  <MapMeasureSelect
                    assetMeasure={assetMeasure}
                    handleSettingsChange={handleSettingsChange}
                    currentSettings={currentSettings}
                    index={index}
                  />
                  <Button
                    variant="contained"
                    color="error"
                    sx={{ maxHeight: '300px' }}
                    startIcon={<CancelIcon />}
                    onClick={() => removeMapMeasureSelect(index)}
                  >
                    Delete
                  </Button>
                </Box>
              </Box>
            ))}
          </Box>
          {settings.markers[currentSettings].assetMeasures
            .filter((title) => title.assetId !== '' && title.measureId !== '')
            .map((title, index) => {
              return (
                <Box
                  key={index}
                  sx={{
                    display: 'flex',
                    justifyContent: 'space-between',
                  }}
                >
                  <FormGroup
                    sx={{
                      width: '100%',
                    }}
                  >
                    <TextField
                      name={title.measureId}
                      onChange={setOnChangeLabel}
                      value={
                        settings.markers[currentSettings].labelAndUnits[title.measureId]?.label
                      }
                      label="Label"
                      variant="outlined"
                      margin="normal"
                      fullWidth
                    />
                  </FormGroup>
                </Box>
              );
            })}
        </>
      )}
    </Box>
  );
};

export default MapMarker;
