<?xml version="1.0" encoding="UTF-8"?>
<svg id="Layer_1" data-name="Layer 1" xmlns="http://www.w3.org/2000/svg" version="1.1" viewBox="0 0 31.2 51">
  <defs>
    <style>
      .cls-1, .cls-2 {
        fill: none;
        stroke: #231f20;
        stroke-miterlimit: 10;
      }

      .cls-3 {
        fill: #231f20;
        stroke-width: 0px;
      }

      .cls-2 {
        stroke-linecap: round;
      }
    </style>
  </defs>
  <g>
    <ellipse class="cls-1" cx="22.2" cy="5.2" rx="4.7" ry="3" transform="translate(12.6 25.9) rotate(-78.3)"/>
    <ellipse class="cls-1" cx="15.5" cy="4.9" rx="3.6" ry="2.3" transform="translate(8.8 19.7) rotate(-83.2)"/>
    <ellipse class="cls-1" cx="9.8" cy="5.7" rx="2.9" ry="1.8" transform="translate(3.6 15.2) rotate(-87.3)"/>
    <ellipse class="cls-1" cx="5.1" cy="7.9" rx="1.5" ry="2.4" transform="translate(-.2 .1) rotate(-1.4)"/>
    <ellipse class="cls-1" cx="1.7" cy="11.5" rx="1.2" ry="1.9" transform="translate(-.3 0) rotate(-1.4)"/>
    <path class="cls-2" d="M19.2,26.9c1.5-2.6,3.5-2.4,3.9-8.3s-.8-8.5-7.5-8.8S1.6,12.3,1.7,20c0,6.5,4.1,8.1,2.9,17.8s-1,12.4,6,12.8c5.8.3,7.7-3.2,7.9-7s.2-4.1,0-6.4"/>
  </g>
  <g>
    <path class="cls-3" d="M17.6,35.8c-.6.3-1.4.5-2.3.5s-2.1-.4-2.9-1.1c-.7-.8-1.1-1.8-1.1-3s.4-2.4,1.2-3.2c.8-.8,1.8-1.2,3.1-1.2s1.4.1,2,.3v1c-.6-.3-1.3-.5-2-.5s-1.7.3-2.3,1c-.6.6-.9,1.5-.9,2.6s.3,1.8.8,2.4c.6.6,1.3.9,2.2.9s1.6-.2,2.2-.6v.9Z"/>
    <path class="cls-3" d="M22.3,36.3c-1.2,0-2.1-.4-2.8-1.2-.7-.8-1.1-1.8-1.1-3s.4-2.4,1.1-3.2c.7-.8,1.7-1.2,3-1.2s2.1.4,2.8,1.2c.7.8,1.1,1.8,1.1,3s-.4,2.4-1.1,3.2c-.7.8-1.7,1.2-2.9,1.2ZM22.4,28.6c-.9,0-1.6.3-2.1.9-.5.6-.8,1.5-.8,2.5s.3,1.9.8,2.5c.5.6,1.2.9,2.1.9s1.6-.3,2.2-.9c.5-.6.8-1.4.8-2.5s-.3-2-.8-2.6c-.5-.6-1.2-.9-2.1-.9Z"/>
    <path class="cls-3" d="M30.7,37.6h-3.2v-.5c0-.2,0-.4.2-.6.1-.2.3-.4.4-.5.2-.2.4-.3.6-.5.2-.1.4-.3.6-.5.2-.2.3-.3.4-.5.1-.2.2-.4.2-.6s0-.4-.2-.5c-.1-.1-.3-.2-.6-.2-.5,0-.9.2-1.3.6v-.9c.3-.3.8-.4,1.4-.4s.4,0,.6,0c.2,0,.3.2.5.3.1.1.2.3.3.4,0,.2.1.4.1.6s0,.5-.2.8c-.1.2-.2.4-.4.6-.2.2-.3.3-.5.5-.2.1-.4.3-.5.4s-.3.2-.4.3c-.1.1-.2.2-.2.3h0c0,0,2.3,0,2.3,0v.7Z"/>
  </g>
</svg>