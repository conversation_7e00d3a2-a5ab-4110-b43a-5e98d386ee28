import { ThunkDispatch } from '@reduxjs/toolkit';
import { useEffect, useState } from 'react';
import { useDispatch } from 'react-redux';
import { weatherApi } from '~/redux/api/weatherApi';
import { RootState } from '~/redux/store';
import { Weather } from '~/types/widgets';

type useFetchWeatherProps = {
  setting: Weather;
};
export type weatherData = {
  air_temperature: number;
  feels_like: number;
  relative_humidity: number;
  sea_level_pressure: number;
  wind_direction: number;
  wind_direction_cardinal: string;
  conditions: string;
  wind_avg: number;
  uv: number;
  station_units: {
    units_direction: string;
    units_distance: string;
    units_other: string;
    units_precip: string;
    units_pressure: string;
    units_temp: string;
    units_wind: string;
  };
};
export const useFetchWeather = ({ setting }: useFetchWeatherProps) => {
  const [weatherData, setWeatherData] = useState<weatherData | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [isError, setIsError] = useState<boolean>(false);
  const dispatch = useDispatch<ThunkDispatch<RootState, any, any>>();
  useEffect(() => {
    const fetchWeatherData = async () => {
      setIsLoading(true);
      await dispatch(
        weatherApi.endpoints.getWeather.initiate({
          stationId: setting.stationId,
          startDate: setting.startDate,
          endDate: setting.endDate,
        }),
      )
        .then((res) => {
          if (res.data) {
            setWeatherData(res.data as weatherData);
          }
        })
        .catch(() => {
          setIsError(true);
        });
      setIsLoading(false);
    };
    if (setting.stationId !== '') fetchWeatherData();
  }, [setting.stationId, setting.overrideGlobalSettings, setting.startDate, setting.endDate]);
  return {
    weatherData,
    isLoading,
    isError,
  };
};
