const { test, expect } = require('@playwright/test');

/**
 * Authenticate and retrieve tokens
 */
async function authenticate(request) {
  const authResponse = await request.post('https://test.brompton.ai/api/v0/sessions', {
    headers: { 'Content-Type': 'application/json' },
    data: { username: 'test', password: 'asdfasdf' },
  });

  const status = authResponse.status();
  const responseBody = await authResponse.text();
  console.log(`Status: ${status}, Response: ${responseBody}`);

  if (![200, 201].includes(status)) {
    throw new Error(`Authentication failed. Status: ${status}, Response: ${responseBody}`);
  }

  const authData = JSON.parse(responseBody);
  const accessToken = `BE-AccessToken=${authData.access_token}; BE-CSRFToken=${authData.csrf_token}`;
  const csrfToken = authData.csrf_token;

  if (!accessToken || !csrfToken) {
    throw new Error('Authentication failed: No access or CSRF token returned');
  }

  //console.log('Tokens:', { accessToken, csrfToken });
  return { accessToken, csrfToken };
}

/**
 * Helper function for generic API requests with retry on 401 Unauthorized
 */
async function makeApiRequest(
  request,
  authTokens,
  { method, url, headers = {}, body },
  expectedStatus,
  expectedProps,
) {
  // Validate URL before making the request
  if (!url || !url.startsWith('http')) {
    throw new Error(`Invalid URL: ${url}. Ensure it is a fully qualified URL.`);
  }

  // Log the constructed URL for debugging
  console.log(`Making ${method} request to URL: ${url}`);

  let response;
  try {
    // Make the API request
    response = await request[method.toLowerCase()](url, {
      headers: {
        ...headers,
        'BE-CSRFToken': authTokens.csrfToken,
        Cookie: authTokens.accessToken,
      },
      data: body,
    });
  } catch (error) {
    console.error(`Request to ${url} failed:`, error);
    throw error;
  }
  /*
    let response = await request[method.toLowerCase()](url, {
    headers: {
        ...headers,
        "BE-CSRFToken": authTokens.csrfToken,
        "Cookie": authTokens.accessToken
      },
      data: body,
    });*/

  const status = response.status();
  const responseBody = await response.text();

  // Log headers for debuggings
  console.log('Request Headers:', {
    'BE-CSRFToken': authTokens.csrfToken,
    Cookie: authTokens.accessToken,
  });

  // Handle CSRF validation failure (401)
  if (status === 401 && responseBody.includes('Failed CSRF validation')) {
    console.warn('401 Failed CSRF validation - Re-authenticating and retrying...');

    // Re-authenticate to refresh tokens
    authTokens = await authenticate(request);

    // Retry the request with new tokens
    response = await request[method.toLowerCase()](url, {
      headers: {
        ...headers,
        'BE-CSRFToken': authTokens.csrfToken,
        Cookie: authTokens.accessToken,
      },
      data: body,
    });
  }

  const finalStatus = response.status();
  const finalResponseBody = await response.text();

  // Assert response status
  if (finalStatus !== expectedStatus) {
    throw new Error(
      `Expected status ${expectedStatus}, but received ${finalStatus}. Response: ${finalResponseBody}`,
    );
  }
  // If the response body is empty, return null
  if (!responseBody.trim()) {
    console.log('Empty response body detected. Skipping JSON parsing.');
    return null; // Return null for empty responses
  }
  // Parse and validate the response body
  if (!finalResponseBody.trim()) {
    console.log('Empty response body detected. Skipping JSON parsing.');
    return null;
  }
  // Validate response properties
  const jsonResponse = JSON.parse(finalResponseBody);
  for (const [key, value] of Object.entries(expectedProps)) {
    expect(jsonResponse).toHaveProperty(key, value);
  }

  return jsonResponse;
}

test.describe('API Test Suite', () => {
  let authTokens;
  let queryCsrfToken; // Define queryCsrfToken at the suite level
  const apiUrl = 'https://test.brompton.ai/api/v0/customers';

  // Authenticate before running tests
  test.beforeAll(async ({ request }) => {
    authTokens = await authenticate(request);
    queryCsrfToken = authTokens.csrfToken;
  });

  test('GET /users/me - Retrieve user info', async ({ request }) => {
    const response = await makeApiRequest(
      request,
      authTokens,
      {
        method: 'GET',
        url: 'https://test.brompton.ai/api/v0/users/me',
      },
      200, // Expected status code
      { username: 'test', email: expect.any(String) }, // Expected properties
    );
  });

  test('GET /customers/85/logo - Retrieve customer logo', async ({ request }) => {
    const response = await makeApiRequest(
      request,
      authTokens,
      {
        method: 'GET',
        url: 'https://test.brompton.ai/api/v0/customers/85/logo',
      },
      200, // Expected status code
      { logo: expect.any(String) }, // Expected properties
    );
  });
  // Case 3
  test('POST /customers/84/dashboards - Create/update dashboard', async ({ request }) => {
    const customerId = 84; // Replace dynamically if necessary
    const url = `https://test.brompton.ai/api/v0/customers/${customerId}/dashboards`;

    try {
      // Attempt to create a new dashboard
      const response = await makeApiRequest(
        request,
        authTokens,
        {
          method: 'POST',
          url: url,
          headers: {
            'Content-Type': 'application/json',
          },
          body: {
            title: 'Overview-2',
            data: JSON.stringify({
              currentDashboardId: 89,
              dashboardTitle: 'Overview-2',
            }),
          },
        },
        201, // Expected status code
        { id: expect.any(Number), title: 'Overview-2' }, // Expected properties
      );

      console.log('Dashboard created successfully:', response);
    } catch (error) {
      // Handle 409 Conflict
      if (error.message.includes('409')) {
        let conflictResponse;
        try {
          conflictResponse = JSON.parse(error.message.split('Response: ')[1]);
        } catch (parseError) {
          console.error('Failed to parse conflict response:', error.message);
          throw parseError;
        }

        if (conflictResponse?.message === 'Dashboard already exists') {
          console.warn('Conflict: Dashboard already exists. Attempting to handle...');

          // Extract dashboard ID if available
          const existingDashboardId = conflictResponse.exception?.response?.id;

          if (existingDashboardId) {
            // Update existing dashboard
            const updateResponse = await makeApiRequest(
              request,
              authTokens,
              {
                method: 'PUT', // Replace with appropriate method for updating
                url: `${url}/${existingDashboardId}`, // Use extracted dashboard ID
                headers: {
                  'Content-Type': 'application/json',
                },
                body: {
                  title: 'Updated Overview',
                  data: JSON.stringify({
                    currentDashboardId: 89,
                    dashboardTitle: 'Updated Overview',
                  }),
                },
              },
              200, // Expected status code for update
              { id: expect.any(Number), title: 'Updated Overview' }, // Expected properties for update
            );

            console.log('Dashboard updated successfully:', updateResponse);
          } else {
            console.error(
              'Existing dashboard ID is undefined. Please verify the conflict response.',
            );
          }
        } else {
          // Unexpected conflict response
          console.error('Unexpected conflict response:', conflictResponse);
          throw new Error(`Unexpected conflict response: ${conflictResponse}`);
        }
      } else {
        // Re-throw other unexpected errors
        throw error;
      }
    }
  });

  test('POST /customers/8/dashboards - Create or update dashboard', async ({ request }) => {
    const customerId = 8; // Replace dynamically if necessary
    const createUrl = `https://test.brompton.ai/api/v0/customers/${customerId}/dashboards`;

    try {
      // Step 1: Attempt to create a new dashboard
      const response = await makeApiRequest(
        request,
        authTokens,
        {
          method: 'POST',
          url: createUrl,
          headers: {
            'Content-Type': 'application/json',
          },
          body: {
            title: 'Overview',
            data: JSON.stringify({
              currentDashboardId: 89,
              dashboardTitle: 'Overview',
            }),
          },
        },
        200, // Expected status code for successful creation
        { id: expect.any(Number), title: 'Overview' }, // Expected properties
      );

      console.log('Dashboard created successfully:', response);
    } catch (error) {
      // Step 2: Handle 409 Conflict
      if (error.message.includes('409')) {
        let conflictResponse;
        try {
          conflictResponse = JSON.parse(error.message.split('Response: ')[1]);
        } catch (parseError) {
          console.error('Failed to parse conflict response:', error.message);
          throw parseError;
        }

        if (conflictResponse?.message === 'Dashboard already exists') {
          console.warn('Conflict: Dashboard already exists. Attempting to handle...');

          // Step 3: Fetch existing dashboard ID or use the response if provided
          const existingDashboardId = conflictResponse.exception?.response?.id;

          if (existingDashboardId) {
            // Update the existing dashboard
            const updateResponse = await makeApiRequest(
              request,
              authTokens,
              {
                method: 'PATCH',
                url: `${createUrl}/${existingDashboardId}`,
                headers: {
                  'Content-Type': 'application/json',
                },
                body: {
                  title: 'Updated Overview',
                  data: JSON.stringify({
                    currentDashboardId: 89,
                    dashboardTitle: 'Updated Overview',
                  }),
                },
              },
              200, // Expected status code for update
              { id: expect.any(Number), title: 'Updated Overview' }, // Expected properties for update
            );

            console.log('Dashboard updated successfully:', updateResponse);
          } else {
            console.error('Dashboard ID not found in conflict response. Cannot update.');
          }
        } else {
          console.error('Unexpected conflict response:', conflictResponse);
          throw new Error(`Unexpected conflict response: ${conflictResponse}`);
        }
      } else {
        // Step 4: Re-throw unexpected errors
        throw error;
      }
    }
  });

  test('POST /users/preference - Update user preferences', async ({ request }) => {
    const response = await makeApiRequest(
      request,
      authTokens,
      {
        method: 'POST',
        url: 'https://test.brompton.ai/api/v0/users/preference',
        headers: {
          'Content-Type': 'application/json',
        },
        body: {
          preferences: {
            DATE_FORMAT: 'DD-MM-YYYY',
            CURRENCY: 'INR',
            DEFAULT_CUSTOMER: '1',
          },
        },
      },
      204, // Expected status code for no content
      { logo: expect.any(String) },
    );

    // Assert response is null for 204
    expect(response).toBeNull();

    console.log('User preferences updated successfully.');
  });

  test('GET /customers/9/dashboards - Retrieve dashboards', async ({ request }) => {
    const url = 'https://test.brompton.ai/api/v0/customers/9/dashboards';

    const response = await makeApiRequest(
      request,
      authTokens,
      {
        method: 'GET',
        url: url,
        headers: {}, // Additional headers if required
      },
      200, // Expected status code
      { items: expect.any(Array), total: expect.any(Number) }, // Adjusted to match the actual response structure
    );

    console.log('Retrieved dashboards:', response);

    // Additional validation
    expect(response.items).toBeInstanceOf(Array); // Assert `items` is an array
    expect(response.total).toBeGreaterThanOrEqual(0); // Assert `total` is a non-negative number
  });

  test('GET /users/preference - Retrieve user preferences', async ({ request }) => {
    const response = await makeApiRequest(
      request,
      authTokens,
      {
        method: 'GET', // Ensure method is defined correctly
        url: 'https://test.brompton.ai/api/v0/users/preference',
        headers: {
          'BE-CSRFToken': authTokens.csrfToken,
          Cookie: authTokens.accessToken,
        },
      },
      200, // Expected status code
      { preferences: expect.any(Object) }, // Expected properties
    );

    expect(response).toHaveProperty('preferences');
    console.log('User preferences:', response.preferences);
  });

  test('GET /customers/8/dashboards/42 - Retrieve a specific dashboard', async ({ request }) => {
    const response = await makeApiRequest(
      request,
      authTokens,
      {
        method: 'GET',
        url: 'https://test.brompton.ai/api/v0/customers/8/dashboards/42',
        headers: {},
      },
      200,
      { id: 42, title: expect.any(String) },
    );

    console.log('Retrieved dashboard:', response);
    expect(response).toHaveProperty('id', 42);
    expect(response).toHaveProperty('title');
    expect(typeof response.title).toBe('string');
  });

  // post -create user
  test('POST /users - Create a new user or handle if user already exists', async ({ request }) => {
    const url = 'https://test.brompton.ai/api/v0/users';
    const userPayload = {
      username: 'customer_user',
      password: 'asdfasdf',
      first_name: 'Just',
      last_name: 'Customer',
      scoped_roles: [
        {
          role: 'USER',
          cusotmer_ids: [1], // Fix typo if necessary: should be `customer_ids`
        },
      ],
      email: '<EMAIL>',
    };

    try {
      // Attempt to create a new user
      const response = await makeApiRequest(
        request,
        authTokens,
        {
          method: 'POST',
          url: url,
          headers: {
            'Content-Type': 'application/json',
          },
          body: userPayload,
        },
        201, // Expected status code for successful creation
        { id: expect.any(Number), username: 'customer_user' }, // Expected properties
      );

      console.log('User created successfully:', response);
    } catch (error) {
      // Handle 400 error for existing user
      if (error.message.includes('400') && error.message.includes('User already exists')) {
        console.warn('User already exists. Verifying in the existing list of users.');

        // Fetch the list of users to confirm
        const listResponse = await makeApiRequest(
          request,
          authTokens,
          {
            method: 'GET',
            url: `${url}?limit=100`, // Adjust query parameters if needed
            headers: {
              'Content-Type': 'application/json',
            },
          },
          200, // Expected status code for fetching users
          { total: expect.any(Number) }, // Validate the structure of the response
        );

        console.log('Fetched user list:', listResponse);

        // Check if the user exists in the list
        const existingUser = listResponse.items.find(
          (user) => user.username === userPayload.username,
        );
        expect(existingUser).toBeDefined();
        expect(existingUser).toHaveProperty('username', userPayload.username);

        console.log('User confirmed in the existing list:', existingUser);
      } else {
        // Re-throw unexpected errors
        throw error;
      }
    }
  });

  //post- scoped user
  test('POST /users - Create a scoped user or verify existence', async ({ request }) => {
    const url = 'https://test.brompton.ai/api/v0/users';
    const userPayload = {
      username: 'scoped_user',
      password: 'asdfasdf',
      first_name: 'Scoped',
      last_name: 'User',
      scoped_roles: [
        {
          role: 'ADMIN',
          customer_ids: [1, 2], // Adjust if the key is incorrect
        },
      ],
      email: '<EMAIL>',
    };

    try {
      // Attempt to create a new user
      const response = await makeApiRequest(
        request,
        authTokens,
        {
          method: 'POST',
          url: url,
          headers: {
            'Content-Type': 'application/json',
          },
          body: userPayload,
        },
        201, // Expected status code for successful creation
        { id: expect.any(Number), username: 'scoped_user' }, // Expected properties
      );

      console.log('Scoped user created successfully:', response);
    } catch (error) {
      // Handle 400 error for existing user
      if (error.message.includes('400') && error.message.includes('User already exists')) {
        console.warn('Scoped user already exists. Verifying in the existing list of users.');

        // Fetch the list of users to confirm
        const listResponse = await makeApiRequest(
          request,
          authTokens,
          {
            method: 'GET',
            url: `${url}?limit=100`, // Adjust query parameters if needed
            headers: {
              'Content-Type': 'application/json',
            },
          },
          200, // Expected status code for fetching users
          { total: expect.any(Number) }, // Validate the structure of the response
        );

        console.log('Fetched user list:', listResponse);

        // Check if the user exists in the list
        const existingUser = listResponse.items.find(
          (user) => user.username === userPayload.username,
        );
        expect(existingUser).toBeDefined();
        expect(existingUser).toHaveProperty('username', userPayload.username);

        console.log('Scoped user confirmed in the existing list:', existingUser);
      } else {
        // Re-throw unexpected errors
        throw error;
      }
    }
  });

  //post- create new user
  test('POST /customers - Create a new customer or verify existence', async ({ request }) => {
    const url = 'https://test.brompton.ai/api/v0/customers';
    const customerPayload = {
      name: 'Apple',
      name_id: 'apple',
      address: 'Palo Altro',
    };

    try {
      // Attempt to create a new customer
      const response = await makeApiRequest(
        request,
        authTokens,
        {
          method: 'POST',
          url: url,
          headers: {
            'Content-Type': 'application/json',
          },
          body: customerPayload,
        },
        201, // Expected status code for successful creation
        { id: expect.any(Number), name: 'Apple' }, // Expected properties
      );

      console.log('Customer created successfully:', response);
    } catch (error) {
      // Handle 400 error for existing customer
      if (error.message.includes('400') && error.message.includes('already exists')) {
        console.warn('Customer already exists. Verifying in the existing list of customers.');

        // Fetch the list of customers to confirm
        const listResponse = await makeApiRequest(
          request,
          authTokens,
          {
            method: 'GET',
            url: `${url}?limit=100`, // Adjust query parameters if needed
            headers: {
              'Content-Type': 'application/json',
            },
          },
          200, // Expected status code for fetching customers
          { total: expect.any(Number) }, // Validate the structure of the response
        );

        console.log('Fetched customer list:', listResponse);

        // Check if the customer exists in the list
        const existingCustomer = listResponse.items.find(
          (customer) => customer.name_id === customerPayload.name_id,
        );
        expect(existingCustomer).toBeDefined();
        expect(existingCustomer).toHaveProperty('name', customerPayload.name);

        console.log('Customer confirmed in the existing list:', existingCustomer);
      } else {
        // Re-throw unexpected errors
        throw error;
      }
    }
  });
});
