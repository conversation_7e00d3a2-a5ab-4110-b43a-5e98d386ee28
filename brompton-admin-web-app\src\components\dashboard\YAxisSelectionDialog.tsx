import { Button, Dialog, DialogActions, DialogContent, DialogTitle } from '@mui/material';
import Radio from '@mui/material/Radio';
import RadioGroup from '@mui/material/RadioGroup';
import FormControlLabel from '@mui/material/FormControlLabel';
import FormControl from '@mui/material/FormControl';
import FormLabel from '@mui/material/FormLabel';
import React, { useEffect } from 'react';
import { YAxisSide } from '~/types/widgets';

type YAxisConfirmationDialogProps = {
  open: boolean;
  onAdd: (axisSide: YAxisSide) => unknown;
  onCancel: () => unknown;
};

export const YAxisConfirmationDialog = (props: YAxisConfirmationDialogProps): JSX.Element => {
  const { open, onAdd, onCancel } = props;

  const [value, setValue] = React.useState<YAxisSide>('left');

  useEffect(() => {
    setValue('left');
  }, [open]);
  const handleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setValue((event.target as HTMLInputElement).value as YAxisSide);
  };

  const handleAddClick = () => {
    onAdd(value as YAxisSide);
  };

  return (
    <Dialog fullWidth={true} open={open} onClose={onCancel}>
      <DialogTitle>Select y-axis position</DialogTitle>
      <DialogContent>
        <FormControl>
          <FormLabel id="demo-controlled-radio-buttons-group">Position</FormLabel>
          <RadioGroup
            aria-labelledby="demo-controlled-radio-buttons-group"
            name="radio-buttons-group"
            value={value}
            onChange={handleChange}
            row
          >
            <FormControlLabel value="left" control={<Radio />} label="Left" />
            <FormControlLabel value="right" control={<Radio />} label="Right" />
          </RadioGroup>
        </FormControl>
      </DialogContent>
      <DialogActions sx={{ justifyContent: 'flex-start', p: 3 }}>
        <Button autoFocus sx={{ mr: 'auto' }} size="medium" onClick={onCancel}>
          Cancel
        </Button>
        <Button variant="contained" size="medium" onClick={handleAddClick}>
          Add
        </Button>
      </DialogActions>
    </Dialog>
  );
};
