import { test, expect, locator } from '@playwright/test';
import { LoginDetails } from '../../POM/LoginDetails';

test('Addmeasure', async ({ page }) => {
  //Login
  const Login1 = new LoginDetails(page);
  await Login1.lauchURL(); // launching the URL
  await Login1.login('test', 'asdfasdf'); // Valid Deatils

  await page.click(
    '#__next > div > div.MuiStack-root.css-92qf02 > div > div > div.MuiBox-root.css-xy51px > div.MuiBox-root.css-9nra4q > button',
  );
  await page.waitForTimeout(1000);
  console.log('clicked on new dashboard button');
  // click on brenes

  // Or if you know its position, for example, the first one:
  const clicl = page.locator('div[role="button"].MuiTreeItem-iconContainer').nth(0); // Adjust the index as needed
  await clicl.click();
  console.log('brenes');
  await page.waitForTimeout(5000);

  /* click on measures double clicl dd will colse
    const M1 = page.locator('MuiTreeItem-iconContainer.css-1frvrrz').nth(4); // Adjust the index as needed
    await M1.click();*/

  //mainpanel click
  const c1 = page.locator('//svg[@data-testid="ChevronRightIcon"]'); // Adjust the index as needed
  await c1.click();
  console.log('manipanel');
  await page.waitForTimeout(2000);

  await page.waitForTimeout(5000);
  console.log('find the arrow');

  await page.close();
});
