import {
  DataTypesCollection,
  calc_engine_template_DTO,
  calc_engine_template_instance,
  create_calc_engine_instance,
  measure_calc_engine,
  poll_period_collection,
} from '~/types/calc_engine';
import { authApi } from './authApi';

export const calculationEngineApi = authApi
  .enhanceEndpoints({
    addTagTypes: ['CalculationEngine', 'poll_period', 'DataTypes'],
  })
  .injectEndpoints({
    endpoints: (builder) => ({
      getCalculationEngineTemplates: builder.query<calc_engine_template_DTO, void>({
        query: () => `/v0/calc-engine/templates`,
        providesTags: ['CalculationEngine'],
      }),
      getCalculationByMeasureId: builder.query<measure_calc_engine, number>({
        query: (id) => `/v0/calc-engine/measurement/${id}`,
        providesTags: ['CalculationEngine'],
      }),
      createCalculationEngineTemplate: builder.mutation<void, calc_engine_template_instance>({
        query: (body) => ({
          url: `/v0/calc-engine/templates`,
          method: 'POST',
          body,
        }),
        invalidatesTags: ['CalculationEngine'],
      }),
      editCalculationEngineTemplate: builder.mutation<void, calc_engine_template_instance>({
        query: ({ id, ...rest }) => ({
          url: `/v0/calc-engine/templates/${id}`,
          method: 'PUT',
          body: rest,
        }),
        invalidatesTags: ['CalculationEngine'],
      }),
      getPollPeriods: builder.query<poll_period_collection, void>({
        query: () => `/v0/calc-engine/poll-periods`,
        providesTags: ['poll_period'],
      }),
      dataTypes: builder.query<DataTypesCollection, void>({
        query: () => `/v0/calc-engine/data-types`,
        providesTags: ['DataTypes'],
      }),
      createCalculationEngineInstance: builder.mutation<
        calc_engine_template_DTO,
        create_calc_engine_instance
      >({
        query: (body) => ({
          url: `/v0/calc-engine/templates/instance/${body.customerId}`,
          method: 'POST',
          body,
        }),
        invalidatesTags: ['CalculationEngine'],
      }),
      updateCalculationEngineInstance: builder.mutation<void, create_calc_engine_instance>({
        query: ({ id, ...rest }) => ({
          url: `/v0/calc-engine/templates/instance/${id}`,
          method: 'PUT',
          body: rest,
        }),
        invalidatesTags: ['CalculationEngine'],
      }),
    }),
  });

export const {
  useGetCalculationEngineTemplatesQuery,
  useGetPollPeriodsQuery,
  useCreateCalculationEngineInstanceMutation,
  useDataTypesQuery,
  useCreateCalculationEngineTemplateMutation,
  useEditCalculationEngineTemplateMutation,
  useGetCalculationByMeasureIdQuery,
  useUpdateCalculationEngineInstanceMutation,
} = calculationEngineApi;
