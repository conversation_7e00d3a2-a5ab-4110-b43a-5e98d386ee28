import ArrowDownwardIcon from '@mui/icons-material/ArrowDownward';
import ArrowUpwardIcon from '@mui/icons-material/ArrowUpward';
import RemoveIcon from '@mui/icons-material/Remove';
import { Box, Card, CardContent, CardMedia, Grid, Typography } from '@mui/material';
import { useSelector } from 'react-redux';
import CommonWidgetContainer from '~/components/common/CommonWidgetContainer';
import Loader from '~/components/common/Loader';
import NoMeasureSelected from '~/components/common/NoMeasureSelected';
import { useFetchKPICurrentWidget } from '~/hooks/useFetchKPICurrentWidget';
import { getDbMeasureIdToName } from '~/redux/selectors/treeSelectors';
import { KpiCurrentWidget } from '~/types/widgets';
import {
  checkTrendUp,
  formatMetricLabel,
  formatNumber,
  hasNoMeasureSelected,
  roundNumber,
} from '~/utils/utils';
import ImageStatsSettings from './ImageStatsSettings';
import { getThousandSeparator } from '~/redux/selectors/userPreferences';

type KPICurrentWidgetContainerProps = {
  id: string;
  settings: KpiCurrentWidget;
};
const imageSizeCalc = (settings: KpiCurrentWidget) => {
  if (settings.imageSize === 'Small') {
    return 'calc(100% - 100px)';
  } else if (settings.imageSize === 'Large') {
    return 'calc(100% - 50px)';
  }
  return 'calc(100% - 70px)';
};
const KPICurrentWidgetContainer = ({ id, settings }: KPICurrentWidgetContainerProps) => {
  const { results, unitOfMeasure } = useFetchKPICurrentWidget(settings);
  const fontSettings = {
    fontSize: settings.font?.size + 'px',
    color: settings.font?.color,
    fontWeight: settings.font?.weight,
  };
  const selectedDbMeasureIdToName = useSelector(getDbMeasureIdToName);
  const defaultTitle = formatMetricLabel(selectedDbMeasureIdToName[settings.selectedDbMeasureId]);
  const thousandSeparator = useSelector(getThousandSeparator);

  return (
    <>
      <CommonWidgetContainer
        id={id}
        settings={settings}
        widgetName="KPI Current"
        widgetContent={
          <Box
            sx={{
              height: '100%',
              width: '100%',
            }}
          >
            {settings.image === null ? (
              <Box
                style={{
                  width: '100%',
                  height: '100%',
                  display: 'flex',
                  justifyContent: 'center',
                  alignItems: 'center',
                }}
              >
                <Typography>Please select image from Widget settings.</Typography>
              </Box>
            ) : (
              <>
                {hasNoMeasureSelected(settings) ? (
                  <NoMeasureSelected />
                ) : (
                  <>
                    {settings.samples.length === 0 ? (
                      <>
                        <Box
                          style={{
                            width: '100%',
                            height: '100%',
                            display: 'flex',
                            justifyContent: 'center',
                            alignItems: 'center',
                          }}
                        >
                          <Typography>
                            Please select at least one sample from Widget settings.
                          </Typography>
                        </Box>
                      </>
                    ) : (
                      <>
                        {results.isLoading ? (
                          <Loader
                            style={{
                              height: '100%',
                            }}
                          />
                        ) : (
                          <>
                            {results.isError ? (
                              <Box
                                sx={{
                                  display: 'flex',
                                  justifyContent: 'center',
                                  height: '100%',
                                  alignItems: 'center',
                                }}
                              >
                                <Typography>Error while fetching data</Typography>
                              </Box>
                            ) : (
                              <Box sx={{ height: '100%', width: '100%' }}>
                                {results.data && (
                                  <>
                                    <Box
                                      sx={{
                                        width: '100%',
                                        display: 'flex',
                                        justifyContent: 'center',
                                        marginY: '10px',
                                      }}
                                    >
                                      <Typography
                                        variant="inherit"
                                        sx={{
                                          fontSize: settings.title.isVisible
                                            ? settings.title.fontSize + 'px'
                                            : undefined,
                                          fontWeight: settings.title.isVisible
                                            ? settings.title.fontWeight
                                            : undefined,
                                          color: settings.title.isVisible
                                            ? settings.title.color
                                            : undefined,
                                        }}
                                        component="div"
                                      >
                                        {settings.title.isVisible
                                          ? settings.title.value
                                          : defaultTitle}
                                      </Typography>
                                    </Box>
                                    {settings.placement === 'Image-Center' && (
                                      <>
                                        <Box
                                          sx={{
                                            height: '50%',
                                            display: 'flex',
                                            alignItems: 'center',
                                            justifyContent: 'center',
                                          }}
                                        >
                                          <img
                                            src={settings.image}
                                            style={{
                                              height: imageSizeCalc(settings),
                                              width: '100%',
                                            }}
                                          />
                                        </Box>
                                        <Box
                                          sx={{
                                            display: 'flex',
                                            justifyContent: 'space-around',
                                            p: 1,
                                            width: '100%',
                                          }}
                                        >
                                          <Box sx={{ width: '100%' }}>
                                            {results.data
                                              .map((data) => {
                                                return {
                                                  data: data['ts,val']?.slice(-2),
                                                  agg: data.period,
                                                };
                                              })
                                              .map((aggregrations, i) => (
                                                <Box
                                                  key={i}
                                                  sx={{
                                                    display: 'flex',
                                                    justifyContent: 'space-around',
                                                    p: 1,
                                                    width: '100%',
                                                  }}
                                                >
                                                  <Grid container>
                                                    <Grid md={6} textAlign={'center'}>
                                                      <Typography sx={{ ...fontSettings }}>
                                                        {aggregrations.agg === '1hr'
                                                          ? 'CURRENT'
                                                          : aggregrations.agg === '30day'
                                                          ? 'MONTHLY'
                                                          : aggregrations.agg}
                                                      </Typography>
                                                    </Grid>
                                                    <Grid
                                                      md={6}
                                                      textAlign={'center'}
                                                      sx={{
                                                        display: 'flex',
                                                        justifyContent: 'center',
                                                      }}
                                                    >
                                                      {
                                                        <Typography
                                                          sx={{
                                                            display: 'flex',
                                                            alignItems: 'center',
                                                            ...fontSettings,
                                                          }}
                                                        >
                                                          {thousandSeparator
                                                            ? formatNumber(
                                                                aggregrations.data?.[
                                                                  aggregrations.data?.length - 1
                                                                ]?.[1],
                                                              )
                                                            : roundNumber(
                                                                aggregrations.data[
                                                                  aggregrations.data?.length - 1
                                                                ]?.[1],
                                                              )}{' '}
                                                          {unitOfMeasure}
                                                          {checkTrendUp(aggregrations.data) ===
                                                            'up' && <ArrowUpwardIcon />}
                                                          {checkTrendUp(aggregrations.data) ===
                                                            'down' && <ArrowDownwardIcon />}
                                                          {checkTrendUp(aggregrations.data) ===
                                                            'same' && <RemoveIcon />}
                                                        </Typography>
                                                      }
                                                    </Grid>
                                                  </Grid>
                                                </Box>
                                              ))}
                                          </Box>
                                        </Box>
                                      </>
                                    )}
                                    {settings.placement === 'Image-Right' && (
                                      <Card
                                        sx={{
                                          display: 'flex',
                                          boxShadow: 'unset',
                                          p: 1,
                                          justifyContent: 'space-around',
                                        }}
                                      >
                                        <Box
                                          sx={{
                                            display: 'flex',
                                            flexDirection: 'column',
                                            width: '50%',
                                          }}
                                        >
                                          <CardContent
                                            sx={{
                                              flex: '1 0 auto',
                                              display: 'flex',
                                              justifyContent: 'center',
                                              alignItems: 'center',
                                              pl: 1,
                                              pr: 1,
                                            }}
                                          >
                                            <Box
                                              sx={{
                                                display: 'flex',
                                                justifyContent: 'space-around',
                                                p: 1,
                                                width: '100%',
                                              }}
                                            >
                                              <Box sx={{ width: '100%' }}>
                                                {results.data
                                                  .map((data, i) => {
                                                    return {
                                                      data: data['ts,val'].slice(-2),
                                                      agg: data.period,
                                                    };
                                                  })
                                                  .map((aggregrations, i) => (
                                                    <Box
                                                      key={i}
                                                      sx={{
                                                        display: 'flex',
                                                        justifyContent: 'space-around',
                                                        p: 1,
                                                        pl: 0,
                                                        pr: 0,
                                                        width: '100%',
                                                      }}
                                                    >
                                                      <Grid container>
                                                        <Grid md={6} textAlign={'center'}>
                                                          <Typography sx={{ ...fontSettings }}>
                                                            {aggregrations.agg === '1hr'
                                                              ? 'CURRENT'
                                                              : aggregrations.agg === '30day'
                                                              ? 'MONTHLY'
                                                              : aggregrations.agg}
                                                          </Typography>
                                                        </Grid>
                                                        <Grid
                                                          md={6}
                                                          textAlign={'center'}
                                                          sx={{
                                                            display: 'flex',
                                                            justifyContent: 'center',
                                                          }}
                                                        >
                                                          {
                                                            <Typography
                                                              sx={{
                                                                display: 'flex',
                                                                alignItems: 'center',
                                                                ...fontSettings,
                                                              }}
                                                            >
                                                              {thousandSeparator
                                                                ? formatNumber(
                                                                    aggregrations.data[
                                                                      aggregrations.data.length - 1
                                                                    ]?.[1],
                                                                  )
                                                                : roundNumber(
                                                                    aggregrations.data[
                                                                      aggregrations.data.length - 1
                                                                    ]?.[1],
                                                                  )}{' '}
                                                              {unitOfMeasure}
                                                              {checkTrendUp(aggregrations.data) ===
                                                                'up' && <ArrowUpwardIcon />}
                                                              {checkTrendUp(aggregrations.data) ===
                                                                'down' && <ArrowDownwardIcon />}
                                                              {checkTrendUp(aggregrations.data) ===
                                                                'same' && <RemoveIcon />}
                                                            </Typography>
                                                          }
                                                        </Grid>
                                                      </Grid>
                                                    </Box>
                                                  ))}
                                              </Box>
                                            </Box>
                                          </CardContent>
                                        </Box>
                                        <CardMedia
                                          component="img"
                                          sx={{
                                            width:
                                              settings.imageSize === 'Small'
                                                ? 'calc(50% - 100px)'
                                                : settings.imageSize === 'Large'
                                                ? 'calc(50% - 50px)'
                                                : 'calc(50% - 70px)',
                                            objectFit: 'contain',
                                            display: 'flex',
                                            justifyContent: 'center',
                                            alignSelf: 'center',
                                          }}
                                          image={settings.image}
                                          alt={`${settings.image}`}
                                        />
                                      </Card>
                                    )}
                                    {settings.placement === 'Image-Left' && (
                                      <Card
                                        sx={{
                                          display: 'flex',
                                          boxShadow: 'unset',
                                          justifyContent: 'space-around',
                                        }}
                                      >
                                        <CardMedia
                                          component="img"
                                          sx={{
                                            width:
                                              settings.imageSize === 'Small'
                                                ? 'calc(50% - 100px)'
                                                : settings.imageSize === 'Large'
                                                ? 'calc(50% - 50px)'
                                                : 'calc(50% - 70px)',
                                            objectFit: 'contain',
                                            display: 'flex',
                                            justifyContent: 'center',
                                            alignSelf: 'center',
                                            ml: 5,
                                          }}
                                          image={settings.image}
                                          alt={`${settings.image}`}
                                        />
                                        <Box
                                          sx={{
                                            display: 'flex',
                                            flexDirection: 'column',
                                            width: '50%',
                                          }}
                                        >
                                          <CardContent
                                            sx={{
                                              flex: '1 0 auto',
                                              display: 'flex',
                                              justifyContent: 'center',
                                              alignItems: 'center',
                                              pr: 0,
                                            }}
                                          >
                                            <Box
                                              sx={{
                                                display: 'flex',
                                                justifyContent: 'space-around',
                                                p: 1,
                                                width: '100%',
                                                pr: 0,
                                              }}
                                            >
                                              <Box sx={{ width: '100%' }}>
                                                {results.data
                                                  .map((data, i) => {
                                                    return {
                                                      data: Array.isArray(data['ts,val'])
                                                        ? data['ts,val'].slice(-2)
                                                        : [],
                                                      agg: data.period,
                                                    };
                                                  })
                                                  .map((aggregrations, i) => (
                                                    <Box
                                                      key={i}
                                                      sx={{
                                                        display: 'flex',
                                                        justifyContent: 'space-around',
                                                        p: 1,
                                                        pl: 0,
                                                        pr: 0,
                                                        width: '100%',
                                                      }}
                                                    >
                                                      <Grid container>
                                                        <Grid md={6} textAlign={'center'}>
                                                          <Typography sx={{ ...fontSettings }}>
                                                            {aggregrations.agg === '1hr'
                                                              ? 'CURRENT'
                                                              : aggregrations.agg === '30day'
                                                              ? 'MONTHLY'
                                                              : aggregrations.agg}
                                                          </Typography>
                                                        </Grid>
                                                        <Grid
                                                          md={6}
                                                          textAlign={'center'}
                                                          sx={{
                                                            display: 'flex',
                                                            justifyContent: 'center',
                                                          }}
                                                        >
                                                          {
                                                            <Typography
                                                              sx={{
                                                                display: 'flex',
                                                                alignItems: 'center',
                                                                ...fontSettings,
                                                              }}
                                                            >
                                                              {thousandSeparator
                                                                ? formatNumber(
                                                                    aggregrations.data[
                                                                      aggregrations.data.length - 1
                                                                    ]?.[1],
                                                                  )
                                                                : roundNumber(
                                                                    aggregrations.data[
                                                                      aggregrations.data.length - 1
                                                                    ]?.[1],
                                                                  )}{' '}
                                                              {unitOfMeasure}
                                                              {checkTrendUp(aggregrations.data) ===
                                                                'up' && <ArrowUpwardIcon />}
                                                              {checkTrendUp(aggregrations.data) ===
                                                                'down' && <ArrowDownwardIcon />}
                                                              {checkTrendUp(aggregrations.data) ===
                                                                'same' && <RemoveIcon />}
                                                            </Typography>
                                                          }
                                                        </Grid>
                                                      </Grid>
                                                    </Box>
                                                  ))}
                                              </Box>
                                            </Box>
                                          </CardContent>
                                        </Box>
                                      </Card>
                                    )}
                                  </>
                                )}
                              </Box>
                            )}
                          </>
                        )}
                      </>
                    )}
                  </>
                )}
              </>
            )}
          </Box>
        }
        widgetType="image-stats"
        settingsDialog={ImageStatsSettings}
      />
    </>
  );
};

export default KPICurrentWidgetContainer;
