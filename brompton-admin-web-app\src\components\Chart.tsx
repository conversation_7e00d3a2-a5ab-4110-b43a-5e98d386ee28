import ArticleIcon from '@mui/icons-material/Article';
import PictureAsPdfIcon from '@mui/icons-material/PictureAsPdf';
import { Box, useMediaQuery } from '@mui/material';
import { ThunkDispatch } from '@reduxjs/toolkit';
import html2canvas from 'html2canvas';
import jsPDF from 'jspdf';
import dynamic from 'next/dynamic';
import { useRouter } from 'next/router';
import { Annotations, Data, Datum, Layout, PlotData, ScatterData } from 'plotly.js';
import { ParsedUrlQuery } from 'querystring';
import React, { ReactNode, useEffect, useRef, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import * as XLSX from 'xlsx';
import Loader from '~/components/common/Loader';
import { CustomAnnotation } from '~/measurements/domain/types';
import { dashboardTemplateApi } from '~/redux/api/dashboardTemplate';
import { useGetAllMeasurementsQuery } from '~/redux/api/measuresApi';
import { getActiveCustomer } from '~/redux/selectors/customerSelectors';
import { getZoomEnabled } from '~/redux/selectors/dashboardSelectors';
import { getDbMeasureIdToName, getSelectedNodeIds } from '~/redux/selectors/treeSelectors';
import { dashboardSlice } from '~/redux/slices/dashboardSlice';
import { RootState } from '~/redux/store';
import { AlertSnackbar } from '~/shared/snackbars/components/AlertSnackbar';
import { useSnackbar } from '~/shared/snackbars/snackbar-hooks';
import { DashboardState, TimeRangeOptions } from '~/types/dashboard';
import {
  AssetMeasurementDetailsWithLastFetchAndSucess,
  AssetMeasurementDetailsWithLastFetchTime,
} from '~/types/timeseries';
import {
  BarChartWidget,
  BulletChartWidget,
  ChartSettings,
  ChartType,
  ChartWidget,
  GaugeChartWidget,
  ImageTextDetails,
  SankeyChartWidget,
  ScatterChartWidget,
} from '~/types/widgets';
import { assetsPathMapper, getPreviousDate } from '~/utils/utils';
import { ChartSettingDialog } from './ChartSettings/ChartSettingDialog';
import CommonWidgetContainer from './common/CommonWidgetContainer';
import NoMeasureSelected from './common/NoMeasureSelected';
import ChartAnnotation from './dashboard/ChartAnnotation';
import UpdateAnnotation from './dashboard/ChartAnnotation/UpdateAnnotation';
import { assetsApi } from '~/redux/api/assetsApi';

const Plot = dynamic(() => import('react-plotly.js'), { ssr: false });

export interface IChartProps {
  id: string;
  chartType: ChartType;
  settings: ChartSettings;
  data: Data[];
  layout: Partial<Layout>;
  isLoading: boolean;
  tsData?: number;
  measureIdAnnotation?: Annotations[];
  removedResults?: AssetMeasurementDetailsWithLastFetchTime[];
  successfulResults?: AssetMeasurementDetailsWithLastFetchTime[];
  successAndFailedMeasurements?: AssetMeasurementDetailsWithLastFetchAndSucess[];
  showSettings: boolean;
}

function hasNoMeasuresSelected(
  chartType: ChartType,
  settings: ChartSettings,
  routerCondition?: ParsedUrlQuery,
): boolean {
  if (routerCondition && routerCondition.measurement_trend) return false;

  if (settings.mode === 'template') {
    if (chartType === 'bullet' || chartType === 'indicator' || chartType === 'heatmap') {
      return (settings as BulletChartWidget).selectedDbMeasureId === '';
    }
    if (chartType === 'bar' || chartType === 'scatter') {
      return (settings as ScatterChartWidget | BarChartWidget).selectedTitles.length === 0;
    }
    if (chartType === 'sankey') {
      return (
        (settings as SankeyChartWidget).connections.length === 0 ||
        (settings as SankeyChartWidget).Label.length === 0
      );
    }
    return false;
  }

  if (chartType === 'bullet' || chartType === 'heatmap') {
    const bulletSettings = settings as BulletChartWidget;
    const measureIds = bulletSettings.assetMeasure?.measureId;
    return (
      !Array.isArray(measureIds) ||
      measureIds.length === 0 ||
      measureIds.every((mId) => !mId.trim())
    );
  }
  if (chartType === 'indicator') {
    const bulletSettings = settings as GaugeChartWidget;
    const measureIds = bulletSettings.assetMeasure?.measureId;
    return (
      !Array.isArray(measureIds) ||
      measureIds.length === 0 ||
      measureIds.every((mId) => !mId.trim())
    );
  }

  if (chartType === 'sankey') {
    const sankeySettings = settings as SankeyChartWidget;
    // If there are no connections, consider that "no measure selected"
    return !sankeySettings.connections || sankeySettings.connections.length === 0;
  }

  if (chartType === 'scatter' || chartType === 'bar') {
    const scatterSettings = settings as ScatterChartWidget;
    // If there's no array, or all measureIds are empty
    if (!Array.isArray(scatterSettings.assetMeasure) || scatterSettings.assetMeasure.length === 0) {
      return true;
    }

    const atLeastOneMeasureSelected = scatterSettings.assetMeasure.some(
      (am) => Array.isArray(am.measureId) && am.measureId.some((mId) => mId.trim() !== ''),
    );

    return !atLeastOneMeasureSelected;
  }

  return false;
}

const Chart: React.FC<IChartProps> = ({
  id,
  chartType,
  tsData,
  settings,
  data,
  layout,
  isLoading,
  showSettings,
  measureIdAnnotation,
  removedResults,
  successfulResults,
  successAndFailedMeasurements,
}) => {
  const mobile = useMediaQuery('(max-width:600px)');
  const router = useRouter();
  const [snackbarState, showSuccessAlert, showErrorAlert] = useSnackbar();
  const containerRef = useRef(null);
  const dispatch = useDispatch<ThunkDispatch<RootState, any, any>>();
  const [dimensions, setDimensions] = useState({ width: 0, height: 0 });
  const [chartData, setChartData] = useState<Data[]>(data);
  const [measureIdAnnotations, setMeasureIdAnnotations] = useState<
    Array<Partial<Annotations | CustomAnnotation>>
  >([]);
  const [chartLayout, setChartLayout] = useState<Partial<Layout>>(layout);
  const [annotation, setAnnotation] = useState<{
    showAnnotation: boolean;
    measurementId: string | null;
    time: number | null;
    value: number | null;
    settings: Annotations | null;
  }>({
    showAnnotation: false,
    measurementId: null,
    time: null,
    value: null,
    settings: null,
  });
  const [updateAnnotation, setUpdateAnnotation] = useState<{
    id: number | null;
    showAnnotation: boolean;
    measurementId: string | null;
    time: number | null;
    value: number | null;
    settings: Annotations | null;
  }>({
    id: null,
    showAnnotation: false,
    measurementId: null,
    time: null,
    value: null,
    settings: null,
  });
  const [chartDialog, setChartDialog] = useState<boolean>(false);
  const selectedDbMeasureIdToName = useSelector(getDbMeasureIdToName);
  const enabledZoom = useSelector(getZoomEnabled);
  const selectedNodeIds = useSelector(getSelectedNodeIds);
  const activeCustomer = useSelector(getActiveCustomer);

  const { data: assetMeasurements } = useGetAllMeasurementsQuery(
    {
      assetId: settings.assetOrAssetType ?? 0,
      customerId: activeCustomer?.id ?? 0,
    },
    {
      skip:
        settings.mode === 'template' ||
        settings.assetOrAssetType === null ||
        settings.assetOrAssetType <= 0 ||
        router.pathname === '/measurement-browser',
    },
  );

  useEffect(() => {
    if (measureIdAnnotation) {
      setMeasureIdAnnotations(measureIdAnnotation);
    }
  }, [measureIdAnnotation]);
  useEffect(() => {
    if (containerRef.current) {
      const updateDimensions = () => {
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        // @ts-ignore
        const newWidth = containerRef.current?.offsetWidth;
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        // @ts-ignore
        const newHeight = containerRef.current?.offsetHeight;

        // Only update state if dimensions have changed
        if (dimensions.width !== newWidth || dimensions.height !== newHeight) {
          setDimensions({
            width: newWidth,
            height: newHeight,
          });
        }
      };

      const resizeObserver = new ResizeObserver(() => updateDimensions());
      resizeObserver.observe(containerRef.current);

      // Initial update
      updateDimensions();

      return () => {
        if (containerRef.current) {
          resizeObserver.unobserve(containerRef.current);
        }
      };
    }
  }, []);

  useEffect(() => {
    setChartData(data);
    setChartLayout(layout);
  }, [data, layout]);

  const onCancel = () => {
    setChartDialog(false);
  };

  const onUpdateClick = (chartSettings: ChartWidget) => {
    setChartDialog(false);
    dispatch(
      dashboardSlice.actions.setCurrentWidgetSettings({
        id,
        type: 'chart',
        settings: chartSettings,
      }),
    );
  };

  const createXLSXFile = (): void => {
    if (chartType !== 'indicator' && chartType !== ('bullet' as ChartType)) {
      const data = chartData as Data[];
      const workbook = XLSX.utils.book_new();
      data.forEach((dataItem1, index) => {
        // Convert each Data object to a worksheet
        const worksheetData = [];
        const dataItem = dataItem1 as PlotData | ScatterData;
        for (let i = 0; i < dataItem.x.length; i++) {
          let rowData: {
            Time: string;
            [y: string]: Datum | Datum[];
            z?: any;
          };
          if (dataItem1.type === 'heatmap') {
            rowData = {
              // Time: new Date((dataItem.x[i] as string) + ' UTC')
              //   .toISOString()
              //   .replace(/T/, ' ')
              //   .replace(/\..+/, ''),
              Time: dataItem.x[i] as string,
              [`${dataItem.name}`]: dataItem.y[i],
            };
            rowData.z = dataItem.z[i];
          } else {
            rowData = {
              Time: new Date((dataItem.x[i] as string) + ' UTC')
                .toISOString()
                .replace(/T/, ' ')
                .replace(/\..+/, ''),
              [`${dataItem.name}`]: dataItem.y[i],
            };
          }
          worksheetData.push(rowData);
        }
        const worksheet = XLSX.utils.json_to_sheet(worksheetData);
        XLSX.utils.book_append_sheet(workbook, worksheet, `Data ${index + 1}`);
      });
      // Write the workbook to a file
      XLSX.writeFile(workbook, 'ChartData.xlsx');
    } else {
      // add worksheet to workbook
      const workbook = XLSX.utils.book_new();
      const worksheetData = [];
      const data = chartData as Data[];
      const dataItem = data[0] as PlotData | ScatterData;
      worksheetData.push({
        Time: tsData ? new Date(tsData).toISOString().replace(/T/, ' ').replace(/\..+/, '') : '',
        Value: dataItem.value,
      });
      const worksheet = XLSX.utils.json_to_sheet(worksheetData);
      XLSX.utils.book_append_sheet(workbook, worksheet, `Data`);
      XLSX.writeFile(workbook, 'ChartData.xlsx');
    }
  };

  const exportToPDF = async () => {
    if (containerRef.current) {
      const canvas = await html2canvas(containerRef.current);
      const imgData = canvas.toDataURL('image/png');

      const pdf = new jsPDF({
        orientation: 'landscape',
        unit: 'mm',
        format: 'a4',
      });
      const imgProps = pdf.getImageProperties(imgData);
      const pdfWidth = pdf.internal.pageSize.getWidth();
      const pdfHeight = (imgProps.height * pdfWidth) / imgProps.width;

      pdf.addImage(imgData, 'PNG', 0, 0, pdfWidth, pdfHeight);
      pdf.save('chart.pdf');
    }
  };
  const options = [
    {
      method: exportToPDF,
      content: (
        <>
          <PictureAsPdfIcon style={{ marginRight: '5px' }} />
          Export To PDF
        </>
      ),
    },
    chartType !== 'sankey'
      ? {
          method: createXLSXFile,
          content: (
            <>
              <ArticleIcon style={{ marginRight: '5px' }} />
              Export To Excel
            </>
          ),
        }
      : null,
  ];

  const noMeasureSelected =
    router.pathname !== '/dynamic-chart' &&
    hasNoMeasuresSelected(chartType, settings, router.query);

  const openDashboardTemplate = async (
    dashboardId: number,
    openInNewTab: boolean | undefined,
    assetId: string,
    dashboard: {
      id: number;
      title: string;
    },
  ) => {
    const assetTypesWithPathForAsset: {
      value: number;
      id: number;
      label: string;
    }[] = [];
    const { data: assets } = await dispatch(
      assetsApi.endpoints.getAllAsset.initiate({
        customerId: activeCustomer?.id ?? 0,
        parentIds: [],
      }),
    );

    if (assets) {
      assetTypesWithPathForAsset.push(...assetsPathMapper(assets ?? []));
    }
    const { data: templateData } = await dispatch(
      dashboardTemplateApi.endpoints.getDashboardTemplateDetails.initiate(dashboardId),
    );

    if (templateData && templateData?.data && assetMeasurements) {
      const templateDetailsData = JSON.parse(templateData.data) as {
        widget: DashboardState['widget'];
        topPanel: DashboardState['template']['topPanel'];
        chart: DashboardState['template']['chart'];
      };
      const metricToMeasurementMap: Record<string, string[]> = {};
      assetMeasurements?.forEach((measurement) => {
        if (measurement.metric_id !== null) {
          const metricIdStr = measurement.metric_id.toString();
          const measurementIdStr = measurement.id.toString();

          if (!metricToMeasurementMap[metricIdStr]) {
            metricToMeasurementMap[metricIdStr] = [];
          }

          metricToMeasurementMap[metricIdStr].push(measurementIdStr);
        }
      });
      templateDetailsData.widget.widgets.map((widget) => {
        if (widget.type === 'chart') {
          widget.settings.settings.dashboardOrTemplate =
            widget.settings.settings.dashboardOrTemplate ?? 'template';
          widget.settings.settings.assetOrAssetType = settings.assetOrAssetType ?? null;
          widget.settings.settings.mode = 'dashboard';
          if ('assetMeasure' in widget.settings.settings && widget.settings.settings.assetMeasure) {
            if (Array.isArray(widget.settings.settings.assetMeasure)) {
              const measureIds: string[] = [];
              if ('selectedTitles' in widget.settings.settings) {
                widget.settings.settings.selectedTitles.forEach((title) => {
                  if (metricToMeasurementMap[title]) {
                    measureIds.push(...metricToMeasurementMap[title]); // <-- Fix here
                  }
                });
                widget.settings.settings.selectedTitles = [];
              }
              widget.settings.settings.assetMeasure.push({
                assetId: settings.assetOrAssetType?.toString() ?? '',
                measureId: measureIds,
              });
            } else {
              const measureIds: string[] = [];
              if (
                'selectedDbMeasureId' in widget.settings.settings &&
                metricToMeasurementMap[widget.settings.settings.selectedDbMeasureId]
              ) {
                measureIds.push(
                  ...metricToMeasurementMap[widget.settings.settings.selectedDbMeasureId],
                );
                widget.settings.settings.selectedDbMeasureId = '';
              }
              widget.settings.settings.assetMeasure.assetId =
                settings.assetOrAssetType?.toString() ?? '';
              widget.settings.settings.assetMeasure.measureId = measureIds;
            }
          }
          return widget;
        }

        widget.settings.dashboardOrTemplate = widget.settings.dashboardOrTemplate ?? 'template';
        widget.settings.assetOrAssetType = settings.assetOrAssetType ?? null;
        widget.settings.mode = 'dashboard';
        if (widget.type === 'dashboard-widget') {
          const assetPath = assetTypesWithPathForAsset.find(
            (assetType) => assetType.id?.toString() === assetId,
          );
          widget.settings.assetId = assetId;
          widget.settings.assetOption = {
            id: assetPath?.id ?? 0,
            label: assetPath?.label ?? '',
          };
        }
        if (widget.type === 'map') {
          widget.settings.markers = widget.settings.markers.map((marker) => {
            if (marker.selectedTitles.length > 0) {
              const measureIds: string[] = [];
              marker.selectedTitles.forEach((title) => {
                if (metricToMeasurementMap[title]) {
                  measureIds.push(...metricToMeasurementMap[title]); // <-- Fix here
                }
              });
              measureIds.forEach((measureId) => {
                marker.assetMeasures.push({
                  assetId: settings.assetOrAssetType?.toString() ?? '',
                  measureId: measureId,
                });
              });
              let labelUnits = {};
              measureIds.forEach((measureId) => {
                const measure = assetMeasurements?.find(
                  (measure) => measure.id === Number(measureId),
                );

                labelUnits = {
                  ...labelUnits,
                  [measureId]: {
                    label: measure?.tag ?? '',
                    unit: '',
                    value: '',
                  },
                };
              });
              marker.labelAndUnits = labelUnits;
            }
            marker.selectedTitles = [];
            return marker;
          });
        } else {
          if ('assetMeasure' in widget.settings && widget.settings.assetMeasure) {
            if (Array.isArray(widget.settings.assetMeasure)) {
              const measureIds: string[] = [];
              if ('selectedTitles' in widget.settings) {
                widget.settings.selectedTitles.forEach((title) => {
                  if (metricToMeasurementMap[title]) {
                    measureIds.push(...metricToMeasurementMap[title]); // <-- Fix here
                  }
                });
                widget.settings.selectedTitles = [];
              }
              if (widget.type === 'image') {
                const selectedDbMeasureIdToName: {
                  [key: string]: string;
                } = {};
                measureIds.forEach((measureId) => {
                  const measureName = assetMeasurements?.find(
                    (measure) => measure.id === Number(measureId),
                  )?.tag;
                  selectedDbMeasureIdToName[measureId] = measureName ?? '';
                });
                widget.settings.dbMeasureIdToName = selectedDbMeasureIdToName;
                const measureIdToImageTextDetails: Record<string, ImageTextDetails> = {};
                measureIds.forEach((measureId) => {
                  const measureName = assetMeasurements?.find(
                    (measure) => measure.id === Number(measureId),
                  );
                  const existingTextDetails =
                    widget.settings.measureIdToImageTextDetails[measureName?.metric_id ?? ''];
                  measureIdToImageTextDetails[measureId] = {
                    label: measureName?.tag ?? '',
                    unit: existingTextDetails?.unit ?? '',
                    id: measureId,
                    positionX: existingTextDetails?.positionX ?? 100,
                    positionY: existingTextDetails?.positionY ?? 100,
                    value: existingTextDetails?.value ?? '',
                    dashboard: existingTextDetails?.dashboard ?? null,
                    openDashboardInNewTab: existingTextDetails?.openDashboardInNewTab ?? false,
                    dashboardOrTemplate: existingTextDetails?.dashboardOrTemplate ?? 'dashboard',
                    assetOrAssetType: existingTextDetails?.assetOrAssetType ?? null,
                  };
                });
                widget.settings.measureIdToImageTextDetails = measureIdToImageTextDetails;
              }
              widget.settings.assetMeasure.push({
                assetId: settings.assetOrAssetType?.toString() ?? '',
                measureId: measureIds,
              });
            } else {
              const measureIds: string[] = [];
              if (
                'selectedDbMeasureId' in widget.settings &&
                metricToMeasurementMap[widget.settings.selectedDbMeasureId]
              ) {
                measureIds.push(...metricToMeasurementMap[widget.settings.selectedDbMeasureId]);
                widget.settings.selectedDbMeasureId = '';
              }
              widget.settings.assetMeasure.assetId = settings.assetOrAssetType?.toString() ?? '';
              widget.settings.assetMeasure.measureId = measureIds;
            }
          }
        }
        return widget;
      });
      dispatch(
        dashboardSlice.actions.setDashboardCrumb({
          dashboardId: -2,
          title: dashboard.title + '-' + templateData?.asset_template?.assetType?.name,
          templateId: dashboard.id,
          assetType: templateData?.asset_template?.assetType?.id,
          assetId: settings?.assetOrAssetType ? Number(settings.assetOrAssetType) : undefined,
        }),
      );
      router.push(`/customer/${activeCustomer?.id}/dashboard/-2`);
      dispatch(dashboardSlice.actions.setCurrentDashboardId(-2));
      dispatch(
        dashboardSlice.actions.setCurrentDashboardTitle(
          dashboard.title + '-' + templateData?.asset_template?.assetType?.name,
        ),
      );
      dispatch(
        dashboardSlice.actions.setWidget({
          widgets: templateDetailsData.widget.widgets,
          deleteWidgets: [],
          widgetLayout: templateDetailsData.widget.widgetLayout,
          lastWidgetId: templateDetailsData.widget.lastWidgetId,
        }),
      );
      dispatch(dashboardSlice.actions.setWidgetsLayout(templateDetailsData.widget.widgetLayout));
      dispatch(dashboardSlice.actions.setSamplePeriod(templateDetailsData.topPanel.samplePeriod));
      dispatch(
        dashboardSlice.actions.setRefreshTimeInterval(templateDetailsData.topPanel.refreshInterval),
      );
      dispatch(dashboardSlice.actions.setTimeRangeType(templateDetailsData.topPanel.timeRangeType));
      const minutes: number =
        TimeRangeOptions[templateDetailsData.topPanel.timeRangeType ?? 6].serverValue;
      const start = getPreviousDate(minutes);
      if (templateDetailsData.topPanel.timeRangeType !== 0) {
        dispatch(dashboardSlice.actions.setChartStartDate(new Date(start)));
        dispatch(dashboardSlice.actions.setChartEndDate(new Date()));
      } else {
        dispatch(
          dashboardSlice.actions.setChartStartDate(new Date(templateDetailsData.chart.startDate)),
        );
        dispatch(
          dashboardSlice.actions.setChartEndDate(new Date(templateDetailsData.chart.endDate)),
        );
      }
    }
  };

  return (
    <>
      <AlertSnackbar {...snackbarState} />
      <CommonWidgetContainer
        id={id}
        settings={settings}
        successAndFailedMeasurements={successAndFailedMeasurements}
        removedResults={removedResults}
        successfulResults={successfulResults}
        widgetName="Chart"
        customSettingsDialogOpen={chartDialog}
        setCustomSettingsDialogOpen={setChartDialog}
        customSettingsDialog={
          showSettings && (
            <ChartSettingDialog
              id={id}
              open={chartDialog}
              onCancel={onCancel}
              selectedDbMeasureIdToName={selectedDbMeasureIdToName}
              onUpdateClick={onUpdateClick}
              currentChartType={chartType}
              currentChartSettings={settings}
            />
          )
        }
        widgetType="chart"
        options={
          options.filter((option) => option !== null) as {
            method: () => void | Promise<void>;
            content: ReactNode;
          }[]
        }
        widgetContent={
          <>
            <Box
              sx={{
                display: 'flex',
                height: '100%',
                flexDirection: 'column',
                alignItems: 'center',
              }}
              ref={containerRef}
            >
              {noMeasureSelected ? (
                <>
                  <NoMeasureSelected
                    message={
                      chartType === 'sankey'
                        ? 'Please Configure Labels and Connections form Widget settings'
                        : undefined
                    }
                  />
                </>
              ) : (
                <>
                  {isLoading ? (
                    <Loader />
                  ) : (
                    <>
                      {chartData && (
                        <>
                          <Plot
                            data={chartData}
                            useResizeHandler={true}
                            style={{ width: '100%', height: '100%' }}
                            layout={{
                              margin: { l: 20, r: 20, t: 40, b: 20 },
                              legend: {
                                x: 1,
                                y: 1,
                                xanchor: 'right',
                                yanchor: 'auto',
                                orientation: 'v', // vertical instead of horizontal
                                font: { size: 12 },
                                bgcolor: 'red', // transparent background
                                borderwidth: 0,
                              },
                              autosize: true,
                              ...chartLayout,
                              annotations: measureIdAnnotations.map((annotation) => ({
                                ...annotation,
                              })),
                              yaxis: {
                                title: {
                                  text: 'Value',
                                  standoff: 15, // adds space between axis line and title text
                                },
                                position: 0,
                                fixedrange: enabledZoom ? true : undefined,
                                automargin: true, // helps with dynamic margin for tick labels
                                ...chartLayout.yaxis,
                              },
                              xaxis: {
                                title: {
                                  text: 'Time',
                                  standoff: 15,
                                },
                                position:
                                  (chartType === 'bar' || chartType === 'scatter') &&
                                  'showRangeSlider' in settings &&
                                  settings.showRangeSlider
                                    ? undefined
                                    : 0,
                                fixedrange: enabledZoom ? true : undefined,
                                automargin: true,
                                ...chartLayout.xaxis,
                                rangeslider:
                                  (chartType === 'bar' || chartType === 'scatter') &&
                                  'showRangeSlider' in settings &&
                                  settings.showRangeSlider
                                    ? { visible: true }
                                    : undefined,
                              },
                            }}
                            onClickAnnotation={(event: Readonly<Plotly.ClickAnnotationEvent>) => {
                              if (chartType === 'scatter' || chartType === 'bar') {
                                // Ensure event.annotation.x is a string
                                const clickedDateTime = event.annotation.x as string;
                                const matchingAnnotation:
                                  | Partial<CustomAnnotation | Annotations>
                                  | undefined = measureIdAnnotations.find(
                                  (annotation) => annotation.x === clickedDateTime,
                                );
                                const dateString = matchingAnnotation?.x as string;
                                // Get the timestamp in milliseconds (UTC)
                                const timestamp = new Date(dateString).getTime();
                                setUpdateAnnotation({
                                  id: (matchingAnnotation as CustomAnnotation).id,
                                  measurementId: (
                                    matchingAnnotation as CustomAnnotation
                                  ).measurement_id.toString(),
                                  settings: matchingAnnotation as Annotations,
                                  showAnnotation: true,
                                  time: timestamp,
                                  value: matchingAnnotation?.y as number,
                                });
                              }
                            }}
                            onClick={(event) => {
                              if (chartType === 'scatter' || chartType === 'bar') {
                                const measurementId = event.points[0].data.customdata as Datum[];
                                if (measurementId && measurementId.length > 0) {
                                  const dateString = event.points[0].x as string;
                                  // Get the timestamp in milliseconds (UTC)
                                  const timestamp = new Date(dateString).getTime(); // This is UTC
                                  setAnnotation({
                                    showAnnotation: true,
                                    measurementId: measurementId[0] as string,
                                    time: timestamp,
                                    value: event.points[0].y as number,
                                    settings: null,
                                  });
                                }
                              }
                              if (chartType === 'sankey' && event.points?.[0]) {
                                const point = event.points[0];
                                const raw = point.customdata;
                                let labelData: {
                                  sourceName: string;
                                  asset: string;
                                  linkedDashboard?: {
                                    id: number;
                                    title: string;

                                    type: 'dashboard' | 'template';
                                    openInNewTab?: boolean;
                                  };
                                } | null = null;
                                let assetId = '';
                                try {
                                  labelData = typeof raw === 'string' ? JSON.parse(raw) : null;
                                } catch (e) {
                                  console.warn('[Sankey Click] Failed to parse customdata', e);
                                }
                                if (labelData?.asset) {
                                  assetId = labelData.asset;
                                }
                                if (labelData?.linkedDashboard) {
                                  const { id, title, type, openInNewTab } =
                                    labelData.linkedDashboard;

                                  const path =
                                    type === 'dashboard' &&
                                    `/customer/${activeCustomer?.id}/dashboard/${id}`;

                                  if (type === 'dashboard') {
                                    dispatch(
                                      dashboardSlice.actions.setDashboardCrumb({
                                        dashboardId: id,
                                        title: title,
                                      }),
                                    );
                                    dispatch(dashboardSlice.actions.setCurrentDashboardId(id));
                                    dispatch(
                                      dashboardSlice.actions.setCurrentDashboardTitle(title),
                                    );
                                  } else {
                                    const dashboard = { id, title };
                                    openDashboardTemplate(id, openInNewTab, assetId, dashboard);
                                  }

                                  if (openInNewTab && path) {
                                    window.open(path, '_blank');
                                  } else {
                                    if (path) router.push(path);
                                  }
                                }
                              }
                            }}
                            config={{
                              responsive: true,
                              displaylogo: false,
                              displayModeBar: false, // This will hide the entire mode bar
                              modeBarButtonsToRemove: ['pan2d', 'lasso2d'],
                            }}
                          />
                        </>
                      )}
                    </>
                  )}
                </>
              )}
            </Box>
          </>
        }
      />
      <ChartAnnotation
        annotation={annotation}
        id={id}
        setAnnotation={setAnnotation}
        setMeasureIdAnnotations={setMeasureIdAnnotations}
        showErrorAlert={showErrorAlert}
        showSuccessAlert={showSuccessAlert}
      />
      <UpdateAnnotation
        id={id}
        updateAnnotation={updateAnnotation}
        setUpdateAnnotation={setUpdateAnnotation}
        setMeasureIdAnnotations={setMeasureIdAnnotations}
        showErrorAlert={showErrorAlert}
        showSuccessAlert={showSuccessAlert}
      />
    </>
  );
};

export default Chart;
