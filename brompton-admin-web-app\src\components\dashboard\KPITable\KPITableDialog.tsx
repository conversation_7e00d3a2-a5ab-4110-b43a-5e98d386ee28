import { Box, Checkbox, FormControlLabel, FormGroup, TextField } from '@mui/material';
import { useEffect } from 'react';
import { useSelector } from 'react-redux';
import DataWidgetSettingsContainer from '~/components/common/DataWidgetSettingsContainer/DataWidgetSettingsContainer';
import MultiMeasureSelect from '~/components/common/MultiMeasureSelect';
import MultiMeasureSelectionMenu from '~/components/common/MultiMeasureSelectionMenu';
import { getMetricsIdToName } from '~/redux/selectors/dashboardSelectors';
import { KPITable, setMultiMeasureWidgetSettings } from '~/types/widgets';

type KPITableDialogProps = {
  settings: KPITable;
  handleSettingsChange: (value: ((prevState: KPITable) => KPITable) | KPITable) => void;
};
const KPITableDialog = ({ settings, handleSettingsChange }: KPITableDialogProps) => {
  const metricsIdToName = useSelector(getMetricsIdToName);

  const onHandleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const [key, subKey] = event.target.name.split('.');
    handleSettingsChange({
      ...settings,
      [key]: {
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        //@ts-ignore
        ...settings[key],
        [subKey]: event.target.value,
      },
    });
  };
  const handleCheckChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const [key, subKey] = event.target.name.split('.');
    handleSettingsChange({
      ...settings,
      [key]: {
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        //@ts-ignore
        ...settings[key],
        [subKey]: event.target.checked,
      },
    });
  };

  useEffect(() => {
    if (settings.mode === 'template' && !settings.title.isVisible) {
      const titles: Record<string, string> = {};
      settings.selectedTitles.forEach((title) => {
        titles[title] = metricsIdToName[title];
      });
      handleSettingsChange((prevState) => ({
        ...prevState,
        dbMeasureIdToName: titles,
        title: {
          ...prevState.title,
          value: prevState.title.isVisible
            ? prevState.title.value
            : Object.values(titles).join(' Vs.'),
        },
      }));
    }
  }, [settings.mode, settings.selectedTitles, metricsIdToName, settings.title]);
  return (
    <>
      <DataWidgetSettingsContainer
        settings={settings}
        setSettings={handleSettingsChange}
        dataTabChildren={
          <>
            {settings.mode === 'dashboard' ? (
              <MultiMeasureSelectionMenu
                mode={settings.mode}
                settings={settings}
                setSettings={handleSettingsChange as setMultiMeasureWidgetSettings}
              />
            ) : (
              <MultiMeasureSelect
                mode={settings.mode}
                settings={settings}
                setSettings={handleSettingsChange as setMultiMeasureWidgetSettings}
              />
            )}
          </>
        }
        feelTabChidren={
          <>
            <Box>
              <FormControlLabel
                control={
                  <Checkbox
                    checked={settings?.min?.show ?? false}
                    onChange={handleCheckChange}
                    name="min.show"
                  />
                }
                label="Show Min"
              />
            </Box>
            {settings?.min?.show && (
              <FormGroup>
                <TextField
                  name="min.label"
                  label="Min Label"
                  onChange={onHandleChange}
                  value={settings.min.label ?? 'Min'}
                  variant="outlined"
                  margin="normal"
                  fullWidth
                />
              </FormGroup>
            )}
            <Box>
              <FormControlLabel
                control={
                  <Checkbox
                    checked={settings?.max?.show ?? false}
                    onChange={handleCheckChange}
                    name="max.show"
                  />
                }
                label="Show Max"
              />
            </Box>
            {settings?.max?.show && (
              <FormGroup>
                <TextField
                  name="max.label"
                  label="Max Label"
                  onChange={onHandleChange}
                  value={settings.max.label ?? 'Max'}
                  variant="outlined"
                  margin="normal"
                  fullWidth
                />
              </FormGroup>
            )}
            <Box>
              <FormControlLabel
                control={
                  <Checkbox
                    checked={settings?.mean?.show ?? false}
                    onChange={handleCheckChange}
                    name="mean.show"
                  />
                }
                label="Show Mean"
              />
            </Box>
            {settings?.mean?.show && (
              <FormGroup>
                <TextField
                  name="mean.label"
                  label="Mean Label"
                  onChange={onHandleChange}
                  value={settings.mean.label ?? 'Mean'}
                  variant="outlined"
                  margin="normal"
                  fullWidth
                />
              </FormGroup>
            )}
            <Box>
              <FormControlLabel
                control={
                  <Checkbox
                    checked={settings?.delta?.show ?? false}
                    onChange={handleCheckChange}
                    name="delta.show"
                  />
                }
                label="Show delta"
              />
            </Box>
            {settings?.delta?.show && (
              <FormGroup>
                <TextField
                  name="delta.label"
                  label="Delta Label"
                  onChange={onHandleChange}
                  value={settings.delta.label ?? 'Delta'}
                  variant="outlined"
                  margin="normal"
                  fullWidth
                />
              </FormGroup>
            )}
            <Box>
              <FormControlLabel
                control={
                  <Checkbox
                    checked={settings?.sum?.show ?? false}
                    onChange={handleCheckChange}
                    name="sum.show"
                  />
                }
                label="Show Sum"
              />
            </Box>
            {settings?.sum?.show && (
              <FormGroup>
                <TextField
                  name="sum.label"
                  label="Sum Label"
                  onChange={onHandleChange}
                  value={settings.sum.label ?? 'Sum'}
                  variant="outlined"
                  margin="normal"
                  fullWidth
                />
              </FormGroup>
            )}
            <Box>
              <FormControlLabel
                control={
                  <Checkbox
                    checked={settings?.current?.show ?? false}
                    onChange={handleCheckChange}
                    name="current.show"
                  />
                }
                label="Show Current"
              />
            </Box>
            {settings?.current?.show && (
              <FormGroup>
                <TextField
                  name="current.label"
                  label="Current Label"
                  onChange={onHandleChange}
                  value={settings.current.label ?? 'Current'}
                  variant="outlined"
                  margin="normal"
                  fullWidth
                />
              </FormGroup>
            )}
          </>
        }
      />
    </>
  );
};

export default KPITableDialog;
