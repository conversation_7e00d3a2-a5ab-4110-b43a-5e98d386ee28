import { yupResolver } from '@hookform/resolvers/yup';
import { Al<PERSON>, Box, Button, TextField, Typography } from '@mui/material';
import { Controller, useFieldArray, useForm } from 'react-hook-form';
import { createAssetTemplateData, CreateAssetTemplateSchema } from '~/measurements/domain/types';
import AddIcon from '@mui/icons-material/Add';
import AssetMeasurements from './AssetMeasurements';
import {
  useGetAllDatasourcesQuery,
  useGetAllDataTypesQuery,
  useGetAllLocationsQuery,
  useGetAllMeasureTypesQuery,
  useGetAllValueTypesQuery,
} from '~/redux/api/measuresApi';
import { useEffect, useMemo, useState } from 'react';
import { mapListToOptions } from '~/utils/utils';
import { ControlledAutocomplete } from '~/shared/forms/components/ControlledAutocomplete';
import {
  useCreateAssetTemplateMutation,
  useGetAllBackOfficeAssetTypesQuery,
} from '~/redux/api/assetsApi';
import { AlertMessage } from '~/shared/forms/types';
import { InvalidInputException } from '~/errors/exceptions';

type AssetTemplateFormProps = {
  loading: boolean;
};
const AssetTemplateForm = ({ loading }: AssetTemplateFormProps) => {
  const {
    control,
    handleSubmit,
    formState: { errors },
    watch,
  } = useForm<createAssetTemplateData>({
    defaultValues: {
      manufacturer: '',
      model_number: '',
      measurements: [
        {
          type_id: undefined,
          data_type_id: undefined,
          datasource_id: undefined,
          metric_id: undefined,
          value_type_id: undefined,
          meter_factor: undefined,
          description: '',
          location_id: undefined,
        },
      ],
    },
    resolver: yupResolver(CreateAssetTemplateSchema),
  });
  const { fields, append, remove } = useFieldArray({
    control,
    name: 'measurements',
  });
  const { data: valueTypeList } = useGetAllValueTypesQuery();
  const { data: datasourceList } = useGetAllDatasourcesQuery({});
  const { data: locationsList } = useGetAllLocationsQuery();
  const { data: dataTypeList } = useGetAllDataTypesQuery();
  const { data: measurementTypeList } = useGetAllMeasureTypesQuery();
  const { data: assetTypeListData } = useGetAllBackOfficeAssetTypesQuery();
  const [
    createAssetTemplate,
    { isError, isLoading, isSuccess, data: createAssetTemplateData, error },
  ] = useCreateAssetTemplateMutation();
  const [alertMessage, setAlertMessage] = useState<AlertMessage | undefined>(undefined);
  useEffect(() => {
    if (isSuccess && createAssetTemplateData) {
      setAlertMessage({
        message: `Asset Template "${createAssetTemplateData.id}" created successfully!`,
        severity: 'success',
      });
    }

    if (isError && error) {
      if (error instanceof InvalidInputException) {
        setAlertMessage({ message: error.message, severity: 'error' });
      } else {
        setAlertMessage({ message: 'Server error', severity: 'error' });
      }
    }
  }, [createAssetTemplateData, error, isError, isSuccess]);

  //   console.log(data);
  const valueTypeOptions = useMemo(() => mapListToOptions(valueTypeList ?? []), [valueTypeList]);
  const datasourceOptions = useMemo(
    () => mapListToOptions(datasourceList?.items ?? []),
    [datasourceList],
  );
  const locationsListOption = useMemo(
    () => mapListToOptions(locationsList?.items ?? []),
    [locationsList],
  );
  const dataTypesListOptions = useMemo(() => mapListToOptions(dataTypeList ?? []), [dataTypeList]);
  const measurementTypeListOptions = useMemo(
    () => mapListToOptions(measurementTypeList ?? []),
    [measurementTypeList],
  );
  const assetTypeListOptions = useMemo(
    () =>
      mapListToOptions(
        assetTypeListData?.map((item) => ({
          ...item,
          name: `${item.name} (${item.asset_template_count})`,
          id: item.id,
        })) ?? [],
      ),
    [assetTypeListData],
  );

  const assetTypeId = watch('assetTypeId');
  return (
    <>
      <form
        onSubmit={handleSubmit(async (data) => {
          try {
            // await createAssetTemplate(data);
            // reset();
          } catch (err) {
            console.log(err);
            // Handle the error here
          }
        })}
        noValidate
      >
        <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
          <ControlledAutocomplete
            // eslint-disable-next-line @typescript-eslint/ban-ts-comment
            //@ts-ignore
            control={control}
            fieldName={`assetTypeId`}
            label="Asset Type"
            options={assetTypeListOptions}
          />
          <Controller
            name="manufacturer"
            control={control}
            render={({ field: { onChange, onBlur, value }, fieldState }) => (
              <TextField
                error={!!fieldState.error}
                helperText={fieldState.error?.message}
                onChange={onChange}
                onBlur={onBlur}
                value={value}
                label="Manufacturer"
                variant="outlined"
                margin="normal"
                disabled={loading}
                fullWidth
                required
              />
            )}
          />
          <Controller
            name="model_number"
            control={control}
            render={({ field: { onChange, onBlur, value }, fieldState }) => (
              <TextField
                error={!!fieldState.error}
                helperText={fieldState.error?.message}
                onChange={onChange}
                onBlur={onBlur}
                value={value}
                label="Model Number"
                variant="outlined"
                margin="normal"
                disabled={loading}
                fullWidth
                required
              />
            )}
          />
        </Box>
        <Button
          onClick={() =>
            append({ type_id: null, data_type_id: null, value_type_id: null, metric_id: null })
          }
          variant="contained"
          size="large"
          startIcon={<AddIcon />}
        >
          Add Measurement
        </Button>
        {fields.map((field, index) => (
          <AssetMeasurements
            index={index}
            key={index}
            control={control}
            errors={errors}
            field={field}
            remove={remove}
            valueTypeOptions={valueTypeOptions}
            datasourceOptions={datasourceOptions}
            locationsListOption={locationsListOption}
            dataTypesListOptions={dataTypesListOptions}
            measurementTypeListOptions={measurementTypeListOptions}
            assetTypeId={assetTypeId}
          />
        ))}
        <Typography color={'red'}>{errors?.measurements?.message}</Typography>
        <Button
          type="submit"
          variant="contained"
          size="large"
          sx={{ mt: 2, width: 200 }}
          disabled={loading}
        >
          Submit
        </Button>
        {alertMessage && (
          <Alert severity={alertMessage.severity} sx={{ mt: 3 }}>
            {alertMessage.message}
          </Alert>
        )}
      </form>
    </>
  );
};

export default AssetTemplateForm;
