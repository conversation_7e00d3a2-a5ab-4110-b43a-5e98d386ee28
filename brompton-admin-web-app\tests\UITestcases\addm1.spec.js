import { test, expect } from '@playwright/test';

test('test', async ({ page }) => {
  await page.goto('https://test.brompton.ai/login');
  await page.getByLabel('Username *').click();
  await page.getByLabel('Username *').fill('test');
  await page.getByLabel('Username *').press('Tab');
  await page.getByLabel('Password *').fill('asdfasdf');
  await page.getByRole('button', { name: 'Log in' }).click();
  await page.locator('.MuiBox-root > div:nth-child(3) > button').click();

  const a1 = page.getByRole('treeitem', { name: '<PERSON>rene<PERSON>' });
  a1.getByRole('button');
  await a1.click();

  const a2 = page.getByRole('treeitem', { name: 'MAINPANEL_MQTT' });
  a2.getByRole('button');
  await a2.click();

  const a3 = page.getByRole('treeitem', { name: 'Power' });
  a3.getByRole('button');
  await a3.click();

  await page.getByLabel('ActivePowerDemand', { exact: true }).check();
  await page.locator('.react-grid-layout').click();
  // await page.locator('.react-grid-layout').click();
  // await page.locator('.react-grid-layout').click();
  await page.getByLabel('Asset Timezone').uncheck();
  await page.getByLabel('Open').nth(1).click();
  await page.locator('#combo-box-demo').fill('to');
  await page.getByRole('option', { name: 'today-' }).click();
  await page.getByRole('button', { name: 'Proceed' }).click();
  await page.getByLabel('open drawer').nth(2).click();
  await page.getByRole('menuitem', { name: 'Widget Settings' }).click();
  await page.getByText('Brenes\\MAINPANEL_MQTT\\').click();
  await page.getByRole('option', { name: 'Brenes\\MAINPANEL_MQTT\\AverageLineVoltage' }).click();
  await page.getByRole('option', { name: 'Brenes\\MAINPANEL_MQTT\\AverageLineVoltage' }).click();
  await page.locator('#menu- div').first().click();
  await page.getByRole('button', { name: 'Update' }).click();
  await page.getByLabel('open drawer').nth(2).click({
    button: 'right',
  });
  await page.getByLabel('open drawer').nth(2).click();
  await page.getByRole('menuitem', { name: 'Widget Settings' }).click();
  await page.getByRole('combobox').nth(1).click();
  await page.getByRole('option', { name: 'Brenes\\MAINPANEL_MQTT\\PhaseAVoltage' }).click();
  await page.locator('#menu- div').first().click();
  await page.getByRole('button', { name: 'Update' }).click();

  await page.close();
});
