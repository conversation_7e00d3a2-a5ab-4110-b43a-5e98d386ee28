import { Autocomplete, TextField } from '@mui/material';
import { useRouter } from 'next/router';
import { useDispatch, useSelector } from 'react-redux';
import { getActiveCustomer } from '~/redux/selectors/customerSelectors';
import { dashboardSlice } from '~/redux/slices/dashboardSlice';
import { Customer } from '~/types/customers';

type CustomerListProps = {
  customers: Customer[];
};

export default function CustomerList({ customers }: CustomerListProps): JSX.Element {
  const router = useRouter();
  const dispatch = useDispatch();
  const activeCustomer = useSelector(getActiveCustomer);
  const handleChange = (e: React.SyntheticEvent, value: Customer | null) => {
    if (value === null) {
      return;
    }
    dispatch(dashboardSlice.actions.setActiveCustomer(value));
    router.push(`/dashboard?customerId=${value?.nameId}`);
  };

  return (
    <Autocomplete<Customer>
      id="combo-box-demo"
      options={customers}
      getOptionLabel={(option) => option.name}
      onChange={handleChange}
      sx={{ width: 300 }}
      value={activeCustomer ?? null}
      renderInput={(params) => <TextField {...params} label="Select Customer" />}
    />
  );
}
