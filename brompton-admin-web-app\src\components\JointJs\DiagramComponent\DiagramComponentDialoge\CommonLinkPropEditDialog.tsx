import {
  Box,
  Button,
  Dialog,
  FormControl,
  Grid,
  InputAdornment,
  InputLabel,
  MenuItem,
  Select,
  Slider,
  Switch,
  TextField,
  Tooltip,
  Typography,
} from '@mui/material';
import { styled } from '@mui/material/styles';
import { FC } from 'react';
import { useDispatch } from 'react-redux';
import CustomDialog from '~/components/common/CustomDialog';
import { diagramSlice } from '~/redux/slices/diagramSlice';
import CancelIcon from '@mui/icons-material/Cancel';
import SaveAsIcon from '@mui/icons-material/SaveAs';
import InfoIcon from '@mui/icons-material/Info';

interface ICommonLinkPropEditDialog {
  errors: {
    lineWidth: boolean;
    strokeWidth: boolean;
    liquidStrokeWidth: boolean;
  };
  editLinkDialogOpen: boolean;
  linkAttrs: {
    width: number;
    style: string;
    endStyle: string;
    color: string;
    direction: string;
    outlineStrokeWidth?: number;
    outlineStrokeColor?: string;
    liquidStrokeWidth?: number;
    liquidStrokeColor?: string;
    liquidDashArray?: string;
    animation?: {
      enabled?: boolean;
      speed?: number;
    };
  };
  handleAttrChange: (
    attr: string,
    value:
      | string
      | number
      | {
          enabled?: boolean;
          speed?: number;
        },
  ) => void;
  handleUpdateLink: () => void;
}
const BootstrapDialog = styled(Dialog)(({ theme }) => ({
  '& .MuiDialogContent-root': {
    padding: theme.spacing(2),
  },
  '& .MuiDialogActions-root': {
    padding: theme.spacing(1),
  },
}));
const CommonLinkPropEditDialog: FC<ICommonLinkPropEditDialog> = ({
  editLinkDialogOpen,
  linkAttrs,
  handleAttrChange,
  handleUpdateLink,
  errors,
}) => {
  const dispatch = useDispatch();
  const { setEditLinkDialogOpen } = diagramSlice.actions;
  return (
    <CustomDialog
      maxWidth="md"
      open={editLinkDialogOpen}
      onClose={() => dispatch(setEditLinkDialogOpen(false))}
      title={<> Edit Link/Pipe Properties</>}
      content={
        <>
          {/* <Typography variant="subtitle1" sx={{ mt: 2 }}>
            Animation
          </Typography>
          <Switch
            checked={linkAttrs.animation?.enabled}
            onChange={(e) =>
              handleAttrChange('animation', {
                ...linkAttrs.animation,
                enabled: e.target.checked ?? false, // Default: false if undefined
              })
            }
          /> */}

          {linkAttrs.animation?.enabled && (
            <>
              <Typography variant="subtitle1" sx={{ mt: 2 }}>
                Animation Speed
              </Typography>
              <Slider
                value={linkAttrs.animation?.speed || 0}
                min={0}
                max={100}
                step={1}
                onChange={(e, value) =>
                  handleAttrChange('animation', {
                    ...linkAttrs.animation,
                    speed: value as number,
                  })
                }
              />
            </>
          )}

          <Grid container spacing={2}>
            <Grid item sm={12} md={6}>
              <TextField
                label="Line Width"
                type="number"
                value={linkAttrs.width || ''}
                onChange={(e) => handleAttrChange('width', e.target.value)}
                fullWidth
                margin="dense"
                error={errors.lineWidth} // Show error if validation fails
                helperText={errors.lineWidth ? 'Line width must not exceed 30' : ''}
              />
            </Grid>

            <Grid item sm={12} md={6}>
              <TextField
                label="Line Color"
                type="color"
                value={linkAttrs.color}
                onChange={(e) => handleAttrChange('color', e.target.value)}
                fullWidth
                margin="dense"
              />
            </Grid>

            {!linkAttrs.animation?.enabled && (
              <>
                <Grid item sm={12} md={6}>
                  <FormControl fullWidth>
                    <InputLabel id="demo-simple-select-label">Line Style</InputLabel>
                    <Select
                      labelId="demo-simple-select-label"
                      label="Line Style"
                      value={linkAttrs.style}
                      onChange={(e) => handleAttrChange('style', e.target.value)}
                      fullWidth
                      margin="dense"
                    >
                      <MenuItem value="solid">Solid</MenuItem>
                      <MenuItem value="dash">Dash</MenuItem>
                      <MenuItem value="dashdot">Dash Dot</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>

                <Grid item sm={12} md={6}>
                  <FormControl fullWidth>
                    <InputLabel id="demo-simple-select-label">End Style</InputLabel>
                    <Select
                      labelId="demo-simple-select-label"
                      label="End Style"
                      value={linkAttrs.endStyle || 'none'}
                      onChange={(e) => handleAttrChange('endStyle', e.target.value)}
                      fullWidth
                      margin="dense"
                    >
                      <MenuItem value="none">None</MenuItem>
                      <MenuItem value="arrow">Arrow</MenuItem>
                      <MenuItem value="triangle">Triangle</MenuItem>
                      <MenuItem value="dot">Dot</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>
              </>
            )}

            {linkAttrs.animation?.enabled && (
              <>
                <Grid item sm={12} md={6}>
                  <TextField
                    label="Stroke Width"
                    type="number"
                    value={linkAttrs.outlineStrokeWidth || ''}
                    onChange={(e) =>
                      handleAttrChange('outlineStrokeWidth', parseInt(e.target.value))
                    }
                    fullWidth
                    margin="dense"
                    error={errors.strokeWidth} // Show error if validation fails
                    helperText={errors.strokeWidth ? 'Stroke width must not exceed 30' : ''}
                  />
                </Grid>

                <Grid item sm={12} md={6}>
                  <TextField
                    label="Stroke Color"
                    type="color"
                    value={linkAttrs.outlineStrokeColor}
                    onChange={(e) => handleAttrChange('outlineStrokeColor', e.target.value)}
                    fullWidth
                    margin="dense"
                  />
                </Grid>

                <Grid item sm={12} md={6}>
                  <TextField
                    label="Liquid Stroke Width"
                    type="number"
                    value={linkAttrs.liquidStrokeWidth || ''}
                    onChange={(e) =>
                      handleAttrChange('liquidStrokeWidth', parseInt(e.target.value))
                    }
                    fullWidth
                    margin="dense"
                    error={errors.liquidStrokeWidth} // Show error if validation fails
                    helperText={
                      errors.liquidStrokeWidth ? 'Liquid stroke width must not exceed 30' : ''
                    }
                  />
                </Grid>

                <Grid item sm={12} md={6}>
                  <TextField
                    label="Liquid Stroke Color"
                    type="color"
                    value={linkAttrs.liquidStrokeColor}
                    onChange={(e) => handleAttrChange('liquidStrokeColor', e.target.value)}
                    fullWidth
                    margin="dense"
                  />
                </Grid>

                <Grid item sm={12}>
                  <TextField
                    label="Liquid Dash Pattern"
                    placeholder="15 20 (where 15 is the length of the dash and 20 is the gap between dashes)"
                    type="text"
                    value={linkAttrs.liquidDashArray} // Default value: '10,20'
                    onChange={(e) => handleAttrChange('liquidDashArray', e.target.value)}
                    fullWidth
                    margin="dense"
                    InputProps={{
                      endAdornment: (
                        <Tooltip
                          arrow
                          title="Liquid dash pattern simulates flow inside the pipe using moving dashed lines, creating the effect of liquid in motion."
                        >
                          <InputAdornment position="end">
                            <InfoIcon
                              color="primary"
                              style={{ cursor: 'pointer' }}
                              onClick={() =>
                                alert('Enter dash pattern as comma-separated values, e.g., 10,20')
                              }
                            />
                          </InputAdornment>
                        </Tooltip>
                      ),
                    }}
                  />
                </Grid>
              </>
            )}
          </Grid>

          {linkAttrs.animation?.enabled && (
            <Select
              sx={{ mt: 2 }}
              label="Flow Direction"
              value={linkAttrs.direction || 'forward'}
              onChange={(e) => handleAttrChange('direction', e.target.value)}
              fullWidth
              margin="dense"
            >
              <MenuItem value="forward">Forward Flow</MenuItem>
              <MenuItem value="backward">Backward Flow</MenuItem>
              <MenuItem value="bidirectional">Bidirectional Flow</MenuItem>
            </Select>
          )}
        </>
      }
      dialogActions={
        <Box sx={{ width: '100%', display: 'inherit', justifyContent: 'space-between' }}>
          <Button
            variant="outlined"
            startIcon={<CancelIcon />}
            onClick={() => {
              dispatch(setEditLinkDialogOpen(false));
            }}
            color="primary"
          >
            Cancel
          </Button>
          <Button
            startIcon={<SaveAsIcon />}
            onClick={handleUpdateLink}
            color="primary"
            variant="contained"
          >
            Update
          </Button>
        </Box>
      }
    />
  );
};

export default CommonLinkPropEditDialog;
