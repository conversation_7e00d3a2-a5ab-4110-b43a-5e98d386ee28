import { yupResolver } from '@hookform/resolvers/yup';
import { <PERSON>ert, Box, Button, Checkbox, FormControlLabel, InputAdornment } from '@mui/material';
import { Dispatch, SetStateAction, useEffect, useMemo, useState } from 'react';
import { Controller, useForm } from 'react-hook-form';
import { useSelector } from 'react-redux';
import {
  AssetMeasurementSchema,
  DataType,
  Datasource,
  MeasurementLocation,
  MeasurementType,
  NewAssetMeasurement,
  UnitOfMeasure,
  ValueType,
} from '~/measurements/domain/types';
import { getCustomerId } from '~/redux/selectors/customerSelectors';
import { getMainPanel } from '~/redux/selectors/dashboardSelectors';
import { ControlledAutocomplete } from '~/shared/forms/components/ControlledAutocomplete';
import { ControlledTextField } from '~/shared/forms/components/ControlledTextField';
import { AlertMessage } from '~/shared/forms/types';
import { Asset } from '~/types/asset';
import CalcEngine from './CalcEngine/CalcEngine';
import MeasureTimeVaringFactor from './MeasureFactor/MeasureTimeVaringFactor';

type NewMeasurementFormProps = {
  loading: boolean;
  measurementTypeList: MeasurementType[];
  dataTypeList: DataType[];
  valueTypeList: ValueType[];
  unitOfMeasureList: UnitOfMeasure[];
  locationList: MeasurementLocation[];
  datasourceList: Datasource[];
  alertMessage: AlertMessage | undefined;
  onMeasurementTypeIdChange: (measurementTypeId: number) => unknown;
  onValidSubmit: (data: NewAssetMeasurement) => Promise<unknown>;
  calcEngine: boolean;
  setCalcEngine: Dispatch<SetStateAction<boolean>>;
  factor: boolean;
  setFactor: Dispatch<SetStateAction<boolean>>;
  parentAsset: Asset;
  assetPath: string;
};

const mapListToOptions = (
  typeList: { id: number; name: string }[],
): { id: string; label: string }[] => {
  return typeList.map((type) => ({ id: type.id.toString(), label: type.name }));
};

export default function NewMeasurementForm({
  loading,
  measurementTypeList,
  dataTypeList,
  valueTypeList,
  unitOfMeasureList,
  locationList,
  datasourceList,
  alertMessage,
  onMeasurementTypeIdChange,
  onValidSubmit,
  calcEngine,
  setCalcEngine,
  parentAsset,
  factor,
  setFactor,
  assetPath,
}: NewMeasurementFormProps): JSX.Element {
  const customerId = useSelector(getCustomerId);
  const { control, watch, handleSubmit, reset } = useForm<NewAssetMeasurement>({
    defaultValues: {
      tag: '',
      description: '',
      typeId: undefined,
      dataTypeId: undefined,
      valueTypeId: undefined,
      unitOfMeasureId: undefined,
      locationId: undefined,
      datasourceId: undefined,
      meterFactor: null,
      writeback: false,
    },
    resolver: yupResolver(AssetMeasurementSchema),
  });
  const [data, setData] = useState<NewAssetMeasurement | null>(null);
  const mainPanel = useSelector(getMainPanel);
  const selectedMeasurementTypeId = watch('typeId');
  const dataSourceTye = watch('datasourceId');
  const tag = watch('tag');
  useEffect(() => {
    onMeasurementTypeIdChange(selectedMeasurementTypeId);
  }, [onMeasurementTypeIdChange, selectedMeasurementTypeId]);

  const measurementTypeOptions = useMemo(
    () => mapListToOptions(measurementTypeList),
    [measurementTypeList],
  );
  const dataTypeOptions = useMemo(() => mapListToOptions(dataTypeList), [dataTypeList]);
  const valueTypeOptions = useMemo(() => mapListToOptions(valueTypeList), [valueTypeList]);
  const unitOfMeasureOptions = useMemo(
    () => mapListToOptions(unitOfMeasureList),
    [unitOfMeasureList],
  );
  const locationOptions = useMemo(() => mapListToOptions(locationList), [locationList]);
  const datasourceOptions = useMemo(() => mapListToOptions(datasourceList), [datasourceList]);

  return (
    <>
      {!calcEngine && !factor ? (
        <form
          onSubmit={handleSubmit(async (data) => {
            try {
              if (
                datasourceList?.find((item) => item.name === 'Calculation')?.id.toString() ===
                  dataSourceTye &&
                mainPanel === 'newMeasure'
              ) {
                setCalcEngine(true);
                setData(data);
                return;
              }
              if (
                datasourceList?.find((item) => item.name === 'TimeVaryingFactor')?.id.toString() ===
                  dataSourceTye &&
                mainPanel === 'newMeasure'
              ) {
                setFactor(true);
                setData(data);
                return;
              }
              await onValidSubmit(data);
              reset();
            } catch (err) {}
          })}
          noValidate
        >
          <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
            <ControlledTextField
              control={control}
              fieldName="tag"
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start" disablePointerEvents disableTypography>{`${
                    assetPath === '' ? parentAsset.tag : assetPath
                  }:`}</InputAdornment>
                ),
              }}
              label="Tag"
              loading={loading}
              required
            />

            <ControlledTextField
              control={control}
              fieldName="description"
              label="Description"
              loading={loading}
              multiline
            />

            <ControlledAutocomplete
              control={control}
              fieldName="typeId"
              label="Select measurement type"
              loading={loading}
              options={measurementTypeOptions}
              required
            />

            <ControlledAutocomplete
              control={control}
              fieldName="dataTypeId"
              label="Select data type"
              loading={loading}
              options={dataTypeOptions}
              required
            />

            <ControlledAutocomplete
              control={control}
              fieldName="valueTypeId"
              label="Select value type"
              loading={loading}
              options={valueTypeOptions}
              required
            />

            <ControlledAutocomplete
              control={control}
              fieldName="unitOfMeasureId"
              label={
                selectedMeasurementTypeId ? 'Select unit of measure' : 'Choose metric type first'
              }
              loading={loading || !selectedMeasurementTypeId}
              options={unitOfMeasureOptions}
            />

            <ControlledAutocomplete
              control={control}
              fieldName="locationId"
              label="Select location"
              loading={loading}
              options={locationOptions}
            />

            <ControlledAutocomplete
              control={control}
              fieldName="datasourceId"
              label="Select datasource"
              loading={loading}
              options={datasourceOptions}
            />

            <ControlledTextField
              control={control}
              fieldName="meterFactor"
              label="Meter factor"
              loading={loading}
              type="number"
            />
            {!dataSourceTye && (
              <Controller
                control={control}
                name="writeback"
                render={({ field: { onChange, value, ...rest } }) => (
                  <FormControlLabel
                    sx={{
                      width: '100%',
                    }}
                    control={
                      <Checkbox
                        checked={!!value}
                        onChange={(e) => {
                          console.log('Checkbox state:', e.target.checked);
                          onChange(e.target.checked);
                        }}
                      />
                    }
                    label="Writeback"
                  />
                )}
              />
            )}
            <Button
              type="submit"
              variant="contained"
              size="large"
              sx={{ mt: 2, width: 200 }}
              disabled={loading}
            >
              {datasourceList?.find((item) => item.name === 'Calculation')?.id.toString() ===
                dataSourceTye ||
              datasourceList?.find((item) => item.name === 'TimeVaryingFactor')?.id.toString() ===
                dataSourceTye
                ? 'Next'
                : 'Submit'}
            </Button>
            {alertMessage && (
              <Alert severity={alertMessage.severity} sx={{ mt: 3, mb: 3 }}>
                {alertMessage.message}
              </Alert>
            )}
          </Box>
        </form>
      ) : (
        <>
          {
            <>
              {data && calcEngine ? (
                <CalcEngine
                  setCalcEngine={setCalcEngine}
                  measure={data ?? {}}
                  parentAsset={parentAsset}
                  assetPath={assetPath === '' ? parentAsset.tag : assetPath}
                />
              ) : null}
            </>
          }
          {
            <>
              {data && factor ? (
                <Box>
                  <MeasureTimeVaringFactor
                    measure={data ?? {}}
                    parentAsset={parentAsset}
                    assetPath={assetPath === '' ? parentAsset.tag : assetPath}
                    setFactor={setFactor}
                  />
                </Box>
              ) : null}
            </>
          }
        </>
      )}
    </>
  );
}
