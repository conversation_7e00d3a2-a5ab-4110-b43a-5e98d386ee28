import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { Customer } from '~/types/customers';
import { dashboardList } from '~/types/dashboardList';

export const initialState: dashboardList = {
  currentCustomer: null,
  currentDashboardId: 0,
  searchValue: null,
};
export const dashboardListSlice = createSlice({
  name: 'dashboardlist',
  initialState,
  reducers: {
    setCustomer: (state, action: PayloadAction<Customer>) => {
      state.currentCustomer = action.payload;
    },
    setCurrentDashboardId: (state, action: PayloadAction<dashboardList['currentDashboardId']>) => {
      state.currentDashboardId = action.payload;
      state.searchValue = null;
    },
    setSearchValue: (state, action: PayloadAction<dashboardList['searchValue']>) => {
      state.searchValue = action.payload;
    },
  },
});
