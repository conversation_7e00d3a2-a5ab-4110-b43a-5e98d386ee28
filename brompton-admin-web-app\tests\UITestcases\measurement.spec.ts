import { test, expect } from '@playwright/test';
test('measurement', async ({ page }) => {
  // opening the URL
  await page.goto('https://test.pivotol.ai/login'); // 60 seconds
  // Go to the username  and password
  await page.getByLabel('Username *').click();
  await page.getByLabel('Username *').fill('normaltest');
  await page.getByLabel('Password *').click();
  await page.getByLabel('Password *').fill('password123');
  // click on Login Button
  await page.click('#__next > div > div > form > div > button');
  await page.waitForTimeout(3000);
  // click on new dashboard
  await page.getByText('Add Dashboard').click({ timeout: 80000 });
  // select customer
  await page.getByLabel('Select Customer').click();
  await page.getByRole('combobox', { name: 'Customer' }).click();
  await page.waitForTimeout(8000);

  //click on asset
  const image = page.getByText('test');
  await image.click({ button: 'right' });
  await page.waitForTimeout(5000);
  // click on new measurement option from dropdown
  const image1 = page.locator('//*[@id="basic-menu"]/div[3]/ul/li[5]');
  await image1.click();
  await page.waitForTimeout(6000);
  //create new measurement
  await page.getByLabel('Tag *').click();
  await page.getByLabel('Tag *').fill('test create measurement');
  await page.getByLabel('Description').click();
  await page.getByLabel('Description').fill('test');
  await page.getByLabel('Select measurement type *').click();
  await page.getByRole('option', { name: 'Acceleration', exact: true }).click();
  await page.getByLabel('Select data type *').click();
  await page.getByRole('option', { name: 'INT', exact: true }).click();
  await page.getByLabel('Select value type *').click();
  await page.getByRole('option', { name: 'count', exact: true }).click();
  await page.getByLabel('Select unit of measure').click();
  await page.getByRole('option', { name: 'ft/s²', exact: true }).click();
  await page.getByLabel('Select location').click();
  await page.getByRole('option', { name: 'Bottom', exact: true }).click();
  await page.getByLabel('Select datasource').click();
  await page.getByRole('option', { name: 'CO2e', exact: true }).click();
  await page.getByLabel('Meter factor').click();
  await page.getByLabel('Meter factor').fill('0');
  await page.getByText('Submit').click();
  await page.waitForTimeout(6000);
  await page.close();
});
