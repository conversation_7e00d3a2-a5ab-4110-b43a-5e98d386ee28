import { dia } from '@joint/core';
import { Delete as DeleteIcon } from '@mui/icons-material';
import AddIcon from '@mui/icons-material/Add';
import {
  Box,
  Card,
  FormControl,
  Grid,
  IconButton,
  MenuItem,
  Select,
  TextField,
  Tooltip,
  Typography,
} from '@mui/material';
import { ChangeEvent, useEffect, useState } from 'react';
import { DraggableLabel } from '~/components/CreateElement/DraggableLabel';
import {
  borderRule,
  colorRule,
  conditionalRule,
  elementSettings,
  elementVariable,
  titleRule,
} from '~/types/diagram';
const dropDownfixSxs = {
  width: '100%',
  p: 0.3,
  '& fieldset': {
    '& legend': {
      maxWidth: '100%',
      height: 'auto',
      '& span': {
        opacity: 1,
      },
    },
  },
};
type CreateElementCommonSettingsProps = {
  graph: dia.Graph<dia.Graph.Attributes, dia.ModelSetOptions>;
  selectedElement: dia.Element<dia.Element.Attributes, dia.ModelSetOptions>;
};
const CreateElementCommonSettings = ({
  graph,
  selectedElement,
}: CreateElementCommonSettingsProps) => {
  const { variables = [], conditionalRule = [] } =
    (selectedElement.get('data') as elementSettings) || {};
  const [elementVariables, setElementsVariables] = useState<elementVariable[]>(variables);
  const [conditionalRules, setConditionalRules] = useState<conditionalRule[]>(conditionalRule);

  useEffect(() => {
    if (selectedElement) {
      setElementsVariables(variables);
      setConditionalRules(conditionalRule);
    }
  }, [selectedElement]);

  const addNewVariable = () => {
    const newVariable: elementVariable = {
      variableName: '',
      label: '',
      assetId: '',
      measurementId: '',
      value: '',
    };

    const updatedVariables = [...elementVariables, newVariable];
    setElementsVariables(updatedVariables);
    selectedElement.prop('data/variables', updatedVariables);

    const basePosition = selectedElement.position();
    const offsetY = elementVariables.length * 40;

    const draggableLabel = new DraggableLabel(
      newVariable.label || `Label ${updatedVariables.length}`,
      basePosition.x + 100,
      basePosition.y + offsetY,
    );
    draggableLabel.addTo(graph);

    const variableLabels = selectedElement.get('data')?.variableLabels || [];
    const updatedVariableLabels = [...variableLabels, draggableLabel.id];
    selectedElement.prop('data/variableLabels', updatedVariableLabels);
  };

  const handleAddConditionalRule = () => {
    const newRule: conditionalRule = {
      variable: '',
      conditionalOperator: '==',
      value: '',
      applicableTo: 'border',
      rules: {
        color: undefined,
        borderWidth: undefined,
        borderStyle: undefined,
        borderColor: undefined,
        titleColor: undefined,
        fontSize: undefined,
      },
    };
    const updatedRules = [...conditionalRules, newRule];
    setConditionalRules(updatedRules);
    selectedElement.prop('data/conditionalRule', updatedRules);
  };

  const updateVariableName = (
    e: ChangeEvent<HTMLInputElement | HTMLTextAreaElement>,
    index: number,
  ) => {
    const newVariableName = e.target.value;
    const elements = graph.getCells().filter((cell) => cell.id !== selectedElement.id);
    const existingVariableNames = elements.reduce<string[]>((acc, cell) => {
      const cellVariables: elementVariable[] = cell.get('data')?.variables || [];
      return [...acc, ...cellVariables.map((variable) => variable.variableName)];
    }, []);

    const updatedVariables = elementVariables.map((variable, i) =>
      i === index
        ? {
            ...variable,
            variableName: newVariableName,
            error: existingVariableNames.includes(newVariableName),
            errorMsg: existingVariableNames.includes(newVariableName)
              ? 'Variable name must be unique.'
              : '',
          }
        : variable,
    );

    setElementsVariables(updatedVariables);
    selectedElement.prop('data/variables', updatedVariables);
  };

  const updateLabelName = (
    e: ChangeEvent<HTMLInputElement | HTMLTextAreaElement>,
    index: number,
  ) => {
    const newLabelName = e.target.value;

    const updatedVariables = elementVariables.map((variable, i) =>
      i === index ? { ...variable, label: newLabelName } : variable,
    );
    setElementsVariables(updatedVariables);
    selectedElement.prop('data/variables', updatedVariables);

    const variableLabels = selectedElement.get('data')?.variableLabels || [];
    const labelId = variableLabels[index];
    if (labelId) {
      const draggableLabel = graph.getCell(labelId) as DraggableLabel;
      if (draggableLabel) {
        draggableLabel.attr('label/text', newLabelName);
      } else {
        console.error(`No draggable label found for ID: ${labelId}`);
      }
    }
  };

  const handleDeleteVariable = (index: number) => {
    const updatedVariables = elementVariables.filter((_, i) => i !== index);
    setElementsVariables(updatedVariables);
    selectedElement.prop('data/variables', updatedVariables);

    const variableLabels = selectedElement.get('data')?.variableLabels || [];
    const labelIdToDelete = variableLabels[index];
    if (labelIdToDelete) {
      const draggableLabel = graph.getCell(labelIdToDelete);
      if (draggableLabel) {
        draggableLabel.remove();
      }
    }

    const updatedVariableLabels = variableLabels.filter((_: any, i: number) => i !== index);
    selectedElement.prop('data/variableLabels', updatedVariableLabels);
  };

  const handleUpdateConditionalRule = (
    index: number,
    field: keyof conditionalRule,
    value: string | number | object,
  ) => {
    const updatedRules = conditionalRules.map((rule, i) =>
      i === index ? { ...rule, [field]: value } : rule,
    );
    setConditionalRules(updatedRules);
    selectedElement.prop('data/conditionalRule', updatedRules);
  };
  const handleDeleteConditionalRule = (index: number) => {
    const updatedRules = conditionalRules.filter((_, i) => i !== index);
    setConditionalRules(updatedRules);
    selectedElement.prop('data/conditionalRule', updatedRules);
  };
  const RenderRules = ({ rule, index }: { rule: conditionalRule; index: number }) => {
    switch (rule.applicableTo) {
      case 'color':
        return (
          <Grid item xs={4}>
            <TextField
              label="Color"
              type="color"
              value={(rule.rules as colorRule).color || null}
              onChange={(e) =>
                handleUpdateConditionalRule(index, 'rules', {
                  ...rule.rules,
                  color: e.target.value,
                })
              }
              fullWidth
            />
          </Grid>
        );
      case 'background':
        return (
          <Grid item xs={4}>
            <TextField
              label="Background Color"
              type="color"
              value={(rule.rules as colorRule).color || null}
              onChange={(e) =>
                handleUpdateConditionalRule(index, 'rules', {
                  ...rule.rules,
                  color: e.target.value,
                })
              }
              fullWidth
            />
          </Grid>
        );
      case 'border':
        return (
          <>
            <Grid item xs={4}>
              <TextField
                label="Border Width"
                type="number"
                value={(rule.rules as borderRule).borderWidth || null}
                onChange={(e) =>
                  handleUpdateConditionalRule(index, 'rules', {
                    ...rule.rules,
                    borderWidth: Number(e.target.value),
                  })
                }
                fullWidth
              />
            </Grid>
            <Grid item xs={4}>
              <Select
                label="Border Style"
                sx={dropDownfixSxs}
                value={(rule.rules as borderRule).borderStyle || null}
                onChange={(e) =>
                  handleUpdateConditionalRule(index, 'rules', {
                    ...rule.rules,
                    borderStyle: e.target.value as borderRule['borderStyle'],
                  })
                }
                fullWidth
              >
                {['solid', 'dotted', 'dashed', 'none'].map((style) => (
                  <MenuItem key={style} value={style}>
                    {style}
                  </MenuItem>
                ))}
              </Select>
            </Grid>
            <Grid item xs={4}>
              <TextField
                label="Border Color"
                type="color"
                value={(rule.rules as borderRule).borderColor || null}
                onChange={(e) =>
                  handleUpdateConditionalRule(index, 'rules', {
                    ...rule.rules,
                    borderColor: e.target.value,
                  })
                }
                fullWidth
              />
            </Grid>
          </>
        );
      case 'title':
        return (
          <>
            <Grid item xs={4}>
              <TextField
                label="Title Color"
                type="color"
                value={(rule.rules as titleRule).titleColor || null}
                onChange={(e) =>
                  handleUpdateConditionalRule(index, 'rules', {
                    ...rule.rules,
                    titleColor: e.target.value,
                  })
                }
                fullWidth
              />
            </Grid>
            <Grid item xs={4}>
              <TextField
                label="Font Size"
                type="number"
                value={(rule.rules as titleRule).fontSize || null}
                onChange={(e) =>
                  handleUpdateConditionalRule(index, 'rules', {
                    ...rule.rules,
                    fontSize: Number(e.target.value),
                  })
                }
                fullWidth
              />
            </Grid>
          </>
        );
      default:
        return null;
    }
  };
  return (
    <Box sx={{ maxHeight: '80vh', overflow: 'auto', p: 1, height: '90%' }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
        <Typography variant="h6">Variables</Typography>
        <Tooltip title="Add a new variable to the selected element" placement="bottom">
          <IconButton
            color="primary"
            sx={{
              backgroundColor: 'primary.main',
              color: 'white',
              '&:hover': {
                backgroundColor: 'primary.dark',
              },
            }}
            onClick={addNewVariable}
            aria-label="add variable"
          >
            <AddIcon />
          </IconButton>
        </Tooltip>
      </Box>
      {elementVariables.length === 0 ? (
        <Box
          sx={{
            display: 'flex',
            height: '100%',
            justifyContent: 'center',
            alignItems: 'center',
          }}
        >
          <Typography>Press `+` icon to add new variables</Typography>
        </Box>
      ) : (
        <>
          <Box mt={2}>
            {elementVariables.map((variable, index) => (
              <Card key={index} sx={{ p: 2 }}>
                <Grid container spacing={2} alignItems="center">
                  <Grid item xs={5.5}>
                    <TextField
                      name="variableName"
                      fullWidth
                      label={`Variable ${index + 1}`}
                      value={variable.variableName || ''}
                      onChange={(e) => updateVariableName(e, index)}
                      error={!!variable.error}
                      helperText={variable.error ? variable.errorMsg : ''}
                    />
                  </Grid>

                  <Grid item xs={5.5}>
                    <TextField
                      name="labelName"
                      fullWidth
                      label={`Label ${index + 1}`}
                      value={variable.label || ''}
                      onChange={(e) => updateLabelName(e, index)}
                      error={!!variable.error}
                      helperText={variable.error ? variable.errorMsg : ''}
                    />
                  </Grid>
                  <Grid item xs={1} display="flex" justifyContent="center">
                    <Tooltip title={`Delete Variable ${index + 1} and Label ${index + 1}`}>
                      <IconButton color="error" onClick={() => handleDeleteVariable(index)}>
                        <DeleteIcon />
                      </IconButton>
                    </Tooltip>
                  </Grid>
                </Grid>
              </Card>
            ))}
          </Box>
          <Box sx={{ mt: 2, display: 'flex', justifyContent: 'space-between' }}>
            <Typography variant="h6">Conditional Rules</Typography>
            <Tooltip title="Add a new variable to the selected element" placement="bottom">
              <IconButton
                color="primary"
                sx={{
                  backgroundColor: 'primary.main',
                  color: 'white',
                  '&:hover': {
                    backgroundColor: 'primary.dark',
                  },
                }}
                onClick={handleAddConditionalRule}
                aria-label="add variable"
              >
                <AddIcon />
              </IconButton>
            </Tooltip>
          </Box>
          {conditionalRules.map((rule, index) => (
            <Card sx={{ mt: 2, p: 2 }} key={index}>
              <Box
                mt={2}
                sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}
              >
                <Typography variant="h5">Conditional Rule #{index + 1}</Typography>
                <Tooltip title={`Delete Rule ${index + 1}`}>
                  <IconButton color="error" onClick={() => handleDeleteConditionalRule(index)}>
                    <DeleteIcon />
                  </IconButton>
                </Tooltip>
              </Box>
              <Grid container spacing={2} mt={1}>
                <Grid item xs={6}>
                  <Select
                    label="Variable"
                    name="variable"
                    sx={dropDownfixSxs}
                    value={rule.variable}
                    onChange={(e) =>
                      handleUpdateConditionalRule(index, 'variable', e.target.value as string)
                    }
                  >
                    {elementVariables
                      .filter(({ variableName }) => variableName && variableName !== '')
                      .map((variable, idx) => (
                        <MenuItem key={idx} value={idx}>
                          {variable.variableName}
                        </MenuItem>
                      ))}
                  </Select>
                </Grid>
                <Grid item xs={6}>
                  <FormControl fullWidth>
                    <Select
                      label="Operator"
                      name="conditionalOperator"
                      sx={dropDownfixSxs}
                      value={rule.conditionalOperator}
                      onChange={(e) =>
                        handleUpdateConditionalRule(
                          index,
                          'conditionalOperator',
                          e.target.value as string,
                        )
                      }
                    >
                      {['==', '!=', '>', '<', '>=', '<='].map((op) => (
                        <MenuItem key={op} value={op}>
                          {op}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </Grid>
              </Grid>

              <Grid container spacing={2} mt={1}>
                <Grid item xs={6}>
                  <TextField
                    label="Value"
                    value={rule.value}
                    onChange={(e) => handleUpdateConditionalRule(index, 'value', e.target.value)}
                    fullWidth
                  />
                </Grid>
                <Grid item xs={6}>
                  <FormControl fullWidth>
                    <Select
                      label="Apply to"
                      name="applicableTo"
                      sx={dropDownfixSxs}
                      value={rule.applicableTo}
                      onChange={(e) =>
                        handleUpdateConditionalRule(index, 'applicableTo', e.target.value as string)
                      }
                    >
                      {['border', 'title', 'background', 'color'].map((target) => (
                        <MenuItem key={target} value={target}>
                          {target.charAt(0).toUpperCase() + target.slice(1)}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </Grid>
                <Box width={'100%'} sx={{ pl: 2, mt: 2 }}>
                  <Typography variant="h6">Conditional Formatting Rules</Typography>
                </Box>
                <RenderRules rule={rule} index={index} />
              </Grid>
            </Card>
          ))}
        </>
      )}
    </Box>
  );
};

export default CreateElementCommonSettings;
