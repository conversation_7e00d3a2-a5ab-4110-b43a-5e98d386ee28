import {
  DefaultPortModel,
  DefaultLinkFactory,
  DefaultLinkModel,
  DeserializeEvent,
} from '@projectstorm/react-diagrams';
import * as React from 'react';
import { useDispatch } from 'react-redux';

export class AdvancedLinkModel extends DefaultLinkModel {
  constructor() {
    super({
      type: 'advanced',
      // width: 10,
    });
  }

  deserialize(event: DeserializeEvent<this>): void {
    super.deserialize(event);
  }
  serialize(): any {
    return {
      ...super.serialize(),
    };
  }
}

export class AdvancedPortModel extends DefaultPortModel {
  createLinkModel(): AdvancedLinkModel {
    return new AdvancedLinkModel();
  }
  deserialize(event: DeserializeEvent<this>): void {
    super.deserialize(event);
  }
  serialize(): any {
    return {
      ...super.serialize(),
    };
  }
}

interface AdvancedLinkSegmentProps {
  model: AdvancedLinkModel; // Specify the type of the 'model' prop
  path: string;
}

export const AdvancedLinkSegment: React.FC<AdvancedLinkSegmentProps> = ({ model, path }) => {
  const dispatch = useDispatch();
  const initialRef: any = null;
  const pathRef = React.useRef(initialRef);
  const circleRef = React.useRef<SVGCircleElement | null>(null);
  const percentRef = React.useRef(0);
  const animationFrameIdRef = React.useRef<number>(0);

  const [radius, setRadius] = React.useState<number>(0);
  const [strokeWidth, setStrokeWidth] = React.useState(0);

  const targetPort = model.getTargetPort();
  const targetPortName = targetPort ? targetPort.getName() : null;

  const sourcePort = model.getSourcePort();
  const sourcePortName = sourcePort ? sourcePort.getName() : null;

  React.useEffect(() => {
    // Check if there is a solar panel node with value
    const parsedModel = JSON.parse(
      JSON.stringify(model.getSourcePort()?.getParent().serialize() ?? null),
    );

    if (targetPortName === null) {
      setRadius(0);
      setStrokeWidth(2);
    } else if (sourcePortName === 'solarPanel' || targetPortName === 'solarPanel') {
      if (parsedModel.type === 'solarPanel') {
        if (parsedModel.value == 0) {
          setRadius(0);
          setStrokeWidth(2);
        } else {
          setRadius(5);
          setStrokeWidth(2);
        }
      } else if (model.getTargetPort().getParent().getOptions().type === 'solarPanel') {
        const parentOptions = model.getTargetPort().getParent().getOptions() as any;
        if (parentOptions.value == 0) {
          setRadius(0);
          setStrokeWidth(2);
        } else {
          setRadius(5);
          setStrokeWidth(2);
        }
      }
    } else if (sourcePortName === 'building' || targetPortName === 'building') {
      if (parsedModel.type === 'building') {
        if (parsedModel.value == 0) {
          setRadius(0);
          setStrokeWidth(2);
        } else {
          setRadius(5);
          setStrokeWidth(2);
        }
      } else if (model.getTargetPort().getParent().getOptions().type === 'building') {
        const parentOptions = model.getTargetPort().getParent().getOptions() as any;
        if (parentOptions.value == 0) {
          setRadius(0);
          setStrokeWidth(2);
        } else {
          setRadius(5);
          setStrokeWidth(2);
        }
      }
    } else {
      setRadius(5);
      setStrokeWidth(2);
    }
  }, [sourcePortName, dispatch, targetPortName]);

  const getDirection = (sourcePortName: any, targetPortName: any) => {
    let direction = 'outward';
    if (sourcePortName === 'building') {
      direction = 'inward';
    } else if (targetPortName === 'building') {
      direction = 'outward';
    } else if (sourcePortName === 'solarPanel') {
      direction = 'outward';
    } else if (targetPortName === 'solarPanel') {
      direction = 'inward';
    }

    return direction;
  };

  const animate = () => {
    if (!circleRef.current || !pathRef.current) {
      return;
    }
    const direction = getDirection(sourcePortName, targetPortName);
    if (direction === 'inward') {
      percentRef.current -= 0.5;
      if (percentRef.current < 0) {
        percentRef.current = 100;
      }
    } else {
      percentRef.current += 0.5;
      if (percentRef.current > 100) {
        percentRef.current = 0;
      }
    }

    const point = pathRef.current.getPointAtLength(
      pathRef.current.getTotalLength() * (percentRef.current / 100.0),
    );

    circleRef.current.setAttribute('cx', '' + point.x);
    circleRef.current.setAttribute('cy', '' + point.y);

    animationFrameIdRef.current = requestAnimationFrame(animate);
  };

  React.useEffect(() => {
    animate();
    return () => {
      cancelAnimationFrame(animationFrameIdRef.current);
    };
  }, [animate]);

  React.useEffect(() => {
    if (pathRef.current && circleRef.current) {
      const initialPoint = pathRef.current.getPointAtLength(0);
      circleRef.current.setAttribute('cx', '' + initialPoint.x);
      circleRef.current.setAttribute('cy', '' + initialPoint.y);
      circleRef.current.setAttribute('r', String(radius));
    }
  }, [radius]);

  return (
    <>
      <path
        fill="none"
        ref={(ref) => {
          pathRef.current = ref;
        }}
        strokeWidth={strokeWidth}
        stroke="rgba(0,0,0)"
        d={path}
      />
      <circle
        ref={(ref) => {
          circleRef.current = ref;
        }}
        //   r= {radius}
        fill="black"
      />
    </>
  );
};

export class AdvancedLinkFactory extends DefaultLinkFactory {
  constructor() {
    super('advanced');
  }

  generateModel(): AdvancedLinkModel {
    return new AdvancedLinkModel();
  }

  generateLinkSegment(model: AdvancedLinkModel, selected: boolean, path: string) {
    return (
      <g>
        <AdvancedLinkSegment model={model} path={path} />
      </g>
    );
  }
}
