import { Card, FormControl, MenuItem, Select } from '@mui/material';
import React, { useEffect, useState } from 'react';
import { RealTimeChart, setSingleMeasureWidgetSettings } from '~/types/widgets';
import SingleMeasureSelect from '../common/SingleMeasureSelect';
import TitleSettings from '../common/TitleSettings';

type RealTimeChartSettingDialogProps = {
  settings: RealTimeChart;
  handleSettingsChange: (
    value: ((prevState: RealTimeChart) => RealTimeChart) | RealTimeChart,
  ) => void;
};

const RealTimeChartSettingsDialog = ({
  settings,
  handleSettingsChange,
}: RealTimeChartSettingDialogProps) => {
  const [title, setTitle] = useState(settings.title);

  useEffect(() => {
    setTitle(settings.title);
  }, [settings]);

  const handleTitleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.name === 'value') {
      setTitle({ ...title, [event.target.name]: event.target.value });
      handleSettingsChange({
        ...settings,
        title: {
          ...title,
          value: event.target.value,
        },
      });
    } else if (event.target.name === 'isVisible') {
      setTitle({ ...title, [event.target.name]: event.target.checked });
      handleSettingsChange({
        ...settings,
        title: {
          ...title,
          isVisible: event.target.checked,
        },
      });
    }
  };

  const handleFontSize = (event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const fontSize = Number(event.target.value);

    handleSettingsChange({
      ...settings,
      fontSize: fontSize,
    });
  };

  const handleColorChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setTitle({ ...title, [event.target.name]: event.target.value });
    handleSettingsChange({
      ...settings,
      title: {
        ...title,
        [event.target.name]: event.target.value,
      },
    });
  };

  return (
    <FormControl component="fieldset" fullWidth>
      <Card>
        <TitleSettings
          fontSize={settings.fontSize}
          fontWeight={settings.fontWeight}
          title={title}
          handleFontSize={handleFontSize}
          handleTitleChange={handleTitleChange}
          handleColorChange={handleColorChange}
        />
      </Card>

      <Card sx={{ my: 2, px: 1, p: 2 }}>
        <SingleMeasureSelect
          id={'real-time-widget'}
          settings={settings}
          setSettings={handleSettingsChange as setSingleMeasureWidgetSettings}
        />
      </Card>

      <Card sx={{ p: 1 }}>
        <FormControl fullWidth>
          <Select
            labelId="static-dropdown"
            id="static-dropdown"
            sx={{
              width: '100%',
              p: 0.3,
              '& fieldset': {
                '& legend': {
                  maxWidth: '100%',
                  height: 'auto',
                  '& span': {
                    opacity: 1,
                  },
                },
              },
            }}
            value={settings.retention}
            onChange={(event) => {
              handleSettingsChange({
                ...settings,
                retention: Number(event.target.value),
              });
            }}
            label="Visible Period (min)"
          >
            <MenuItem value={0} style={{ display: 'none' }} />
            {[1, 2, 5].map((value) => (
              <MenuItem key={value} value={value}>
                {value}
              </MenuItem>
            ))}
          </Select>
        </FormControl>
      </Card>
    </FormControl>
  );
};

export default RealTimeChartSettingsDialog;
