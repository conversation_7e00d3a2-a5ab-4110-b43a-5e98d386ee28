import {
  Box,
  Card,
  Checkbox,
  FormControl,
  FormControlLabel,
  FormGroup,
  MenuItem,
  Select,
  SelectChangeEvent,
  TextField,
} from '@mui/material';
import { DateTimePicker } from '@mui/x-date-pickers';
import dayjs from 'dayjs';
import { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';
import { getEndDate } from '~/redux/selectors/chartSelectors';
import { getAssetTz } from '~/redux/selectors/topPanleSelectors';
import { getDateTimeFormat } from '~/redux/selectors/userPreferences';
import { TimeRangeOptions } from '~/types/dashboard';
import { Widgets } from '~/types/widgets';
import { getPreviousDate, getPreviousDateRelativeToEndTime } from '~/utils/utils';
export type setSettings = (value: ((prevState: Widgets) => Widgets) | Widgets) => void;
type OverRideGlobalSettingsProps<T extends Widgets> = {
  settings: T;
  setSettings: (value: ((prevState: T) => T) | T) => void;
  isInvalidTimeRange?: boolean;
};

const OverRideGlobalSettings = ({
  settings,
  setSettings,
  isInvalidTimeRange,
}: OverRideGlobalSettingsProps<Widgets>) => {
  const { timeRange, startDate, endDate, overrideGlobalSettings, isRelativeToGlboalEndTime } =
    settings;
  const assetTz = useSelector(getAssetTz);
  const globalendDate = useSelector(getEndDate);
  const dateTimeFormat = useSelector(getDateTimeFormat);

  const handleTimeRangeChange = (event: SelectChangeEvent<number>) => {
    const timeRangeValue = event.target.value as number;
    if (timeRangeValue !== 0) {
      const minutes: number = TimeRangeOptions[timeRangeValue].serverValue;
      let start = getPreviousDate(minutes);
      let end = new Date().getTime();

      if (settings.isRelativeToGlboalEndTime && timeRangeValue >= 1 && timeRangeValue <= 12) {
        start = getPreviousDateRelativeToEndTime(minutes, globalendDate); // Already calculated, just reuse it
        end = globalendDate;
      }

      setSettings({
        ...settings,
        timeRange: timeRangeValue,
        startDate: start,
        endDate: end,
        isRelativeToGlboalEndTime:
          settings.isRelativeToGlboalEndTime && timeRangeValue >= 1 && timeRangeValue <= 12,
      });
    } else {
      setSettings({
        ...settings,
        timeRange: timeRangeValue,
        isRelativeToGlboalEndTime: false,
      });
    }
  };
  const handleOverrideGlobalSettings = (
    event: React.ChangeEvent<HTMLInputElement>,
    checked: boolean,
  ) => {
    if (checked) {
      const timeRangeValue = settings.timeRange;
      const minutes: number = TimeRangeOptions[timeRangeValue].serverValue;
      const start = getPreviousDate(minutes);
      setSettings({
        ...settings,
        overrideGlobalSettings: checked,
        isRelativeToGlboalEndTime: settings.isRelativeToGlboalEndTime,
        startDate: start,
        endDate: new Date().getTime(),
      });
    } else {
      setSettings({
        ...settings,
        overrideGlobalSettings: checked,
        isRelativeToGlboalEndTime: settings.isRelativeToGlboalEndTime,
      });
    }
  };
  const handleRelativeToGlobalEndTime = (
    event: React.ChangeEvent<HTMLInputElement>,
    checked: boolean,
  ) => {
    if (checked) {
      const minutes: number = TimeRangeOptions[settings.timeRange].serverValue;
      const referenceEndDate = globalendDate;
      const start = getPreviousDateRelativeToEndTime(minutes, referenceEndDate); // Use globalendDate if relative mode is ON
      setSettings({
        ...settings,
        isRelativeToGlboalEndTime: checked,
        startDate: start,
        endDate: globalendDate,
      });
    } else {
      const timeRangeValue = settings.timeRange;
      const minutes: number = TimeRangeOptions[timeRangeValue].serverValue;
      const start = getPreviousDate(minutes);
      setSettings({
        ...settings,
        isRelativeToGlboalEndTime: checked,
        startDate: start,
        endDate: new Date().getTime(),
      });
    }
  };
  return (
    <Card>
      <Box>
        <FormGroup>
          <FormControlLabel
            control={
              <Checkbox
                sx={{ p: 2 }}
                checked={overrideGlobalSettings}
                onChange={handleOverrideGlobalSettings}
                name="overrideGlobalSettings"
              />
            }
            label="Override Global Time Range Settings"
          />
        </FormGroup>
      </Box>
      {overrideGlobalSettings ? (
        <>
          {timeRange >= 1 && timeRange <= 12 ? (
            <FormGroup>
              <FormControlLabel
                control={
                  <Checkbox
                    sx={{ p: 2 }}
                    checked={isRelativeToGlboalEndTime}
                    onChange={handleRelativeToGlobalEndTime}
                    name="isRelativeToGlboalEndTime"
                  />
                }
                label="Relative To Global End Time?"
              />
            </FormGroup>
          ) : null}
          <Box
            sx={{
              width: '100%',
            }}
          >
            <FormControl
              fullWidth
              sx={{
                p: 2,
                pt: 0,
                pb: 0,
              }}
            >
              <Select
                labelId="time-range-select-label"
                id="time-range-select"
                value={timeRange}
                name="timeRange"
                key={'timeRange'}
                label="Time Range"
                onChange={handleTimeRangeChange}
                sx={{
                  p: 0.2,
                  '& legend': {
                    maxWidth: '100%',
                    height: 'fit-content',
                    '& span': {
                      opacity: 1,
                    },
                  },
                }}
              >
                {TimeRangeOptions.map((option) => (
                  <MenuItem key={option.value} value={option.value}>
                    {option.label}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Box>
          <Box>
            {timeRange !== 0 && (
              <>
                <Box display="flex" width={'100%'} mt={2} p={2} pt={0} pb={0}>
                  <TextField
                    id="outlined-read-start-date"
                    label="Start Date"
                    value={dayjs(startDate).format(dateTimeFormat)}
                    sx={{
                      minWidth: '170px',
                    }}
                    fullWidth
                    InputProps={{
                      readOnly: true,
                    }}
                  />
                </Box>
                <Box display="inline-flex" width={'100%'} mt={2} gap={3} p={2} pt={0}>
                  <TextField
                    id="outlined-read-end-date"
                    label="End Date"
                    fullWidth
                    sx={{
                      minWidth: '170px',
                    }}
                    value={dayjs(endDate).format(dateTimeFormat)}
                    InputProps={{
                      readOnly: true,
                    }}
                  />
                </Box>
              </>
            )}
            {timeRange === 0 && (
              <>
                <Box display="flex" mt={2} p={2} pt={0} pb={0}>
                  <DateTimePicker
                    label={'Start Date'}
                    key={'startDate'}
                    sx={{
                      minWidth: '220px',
                      width: '100%',
                    }}
                    value={dayjs(startDate)}
                    onAccept={(newValue) => {
                      newValue &&
                        setSettings({
                          ...settings,
                          startDate: newValue.toDate().getTime(),
                        });
                    }}
                    closeOnSelect={false}
                    ampm={false}
                    disableFuture={true}
                    format={dateTimeFormat}
                    slotProps={{
                      textField: {
                        error: !!isInvalidTimeRange,
                        helperText: isInvalidTimeRange
                          ? 'Start Date cannot be greater than End Date'
                          : '',
                      },
                    }}
                  />
                </Box>
                <Box display="flex" gap={3} mt={2} p={2} pt={0}>
                  <DateTimePicker
                    key={'endDate'}
                    sx={{
                      minWidth: '100%',
                      width: '100%',
                    }}
                    label={'End Date'}
                    value={dayjs(endDate)}
                    onAccept={(newValue) => {
                      newValue &&
                        setSettings({
                          ...settings,
                          endDate: newValue.toDate().getTime(),
                        });
                      // newValue &&
                      //   handleGlobalTimeRangeSettingChange({
                      //     setting: 'endDate',
                      //     value: newValue.toDate().getTime(),
                      //   });
                    }}
                    slotProps={{
                      textField: {
                        error: !!isInvalidTimeRange,
                        helperText: isInvalidTimeRange
                          ? 'End Date cannot be less than Start Date'
                          : '',
                      },
                    }}
                    closeOnSelect={false}
                    ampm={false}
                    format={dateTimeFormat}
                    maxDate={
                      assetTz || settings.overrideAssetTzValue
                        ? dayjs(new Date()).add(1, 'day')
                        : dayjs(new Date())
                    }
                  />
                </Box>
              </>
            )}
          </Box>
        </>
      ) : null}
    </Card>
  );
};

export default OverRideGlobalSettings;
