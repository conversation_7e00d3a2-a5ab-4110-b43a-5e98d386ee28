API_URL="dev.pivotol.ai/api"
NEXT_PUBLIC_ENVIRONMENT=dev
NEXT_PUBLIC_TS_API_ENDPOINT="/timeseries/api/v1_0/timeseries"
NEXT_PUBLIC_TS_IMPORT_API_ENDPOINT="/timeseries/api/v1_0"
NEXT_PUBLIC_TS_API_ENDPOINT_V2="/timeseries/api/v2_0/timeseries"
NEXT_PUBLIC_BE_FAST_API_URL="https://dev.pivotol.ai/api"
NEXT_PUBLIC_BE_ADMIN_API_URL="https://dev.pivotol.ai/api"
NEXT_PUBLIC_OPENOBSERVE_CLIENT_TOKEN=rumOylr3n2GEFdVrLWY
NEXT_PUBLIC_OPENOBSERVE_APPLICATION_ID=web-application-id
NEXT_PUBLIC_OPENOBSERVE_SITE=monitor.dev.pivotol.ai
NEXT_PUBLIC_OPENOBSERVE_SERVICE=my-web-application
NEXT_PUBLIC_OPENOBSERVE_ENV=dev
NEXT_PUBLIC_OPENOBSERVE_VERSION=0.0.1
NEXT_PUBLIC_OPENOBSERVE_ORGANIZATION_IDENTIFIER=default
NEXT_PUBLIC_OPENOBSERVE_INSECURE_HTTP=false
NEXT_PUBLIC_OPENOBSERVE_API_VERSION=v1