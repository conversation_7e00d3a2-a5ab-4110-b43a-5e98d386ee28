import { RootState } from '~/redux/store';
import { createSelector } from 'reselect';

const selectChartState = (state: RootState) => state.dashboard.chart;

export const getGlobalStartDate = createSelector([selectChartState], (chart) => chart.startDate);

export const getGlobalEndDate = createSelector([selectChartState], (chart) => chart.endDate);

const selectDashboardState = (state: RootState) => state.dashboard;
export const getWidgets = createSelector(
  [selectDashboardState],
  (dashboard) => dashboard.widget.widgets,
);
export const getWidgetsLayout = createSelector(
  [selectDashboardState],
  (dashboard) => dashboard.widget.widgetLayout,
);

export const getWidgetLayoutById = (id: string) =>
  createSelector([getWidgetsLayout], (widgets) => widgets.find((widget) => widget.i === id));

export const getDeletedWidgets = createSelector(
  [selectDashboardState],
  (dashboard) => dashboard.widget.deleteWidgets,
);

export const getLastWidgetId = createSelector([getWidgets], (widgets) => {
  return widgets.length;
});

export const getDesktopMobileMode = createSelector(
  [selectDashboardState],
  (dashboard) => dashboard.desktopMobile ?? 0,
);

export const getResponsiveLayouts = createSelector(
  [selectDashboardState],
  (dashboard) => dashboard.responsiveLayouts,
);

export const getCurrentLayoutMode = createSelector([getDesktopMobileMode], (desktopMobile) =>
  desktopMobile === 0 ? 'desktop' : 'mobile',
);
