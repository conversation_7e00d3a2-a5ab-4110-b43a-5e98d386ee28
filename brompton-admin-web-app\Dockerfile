# Stage 1: Build the application
FROM node:16.15.0-slim as builder
 
WORKDIR /app
 
# Copy package.json and yarn.lock
COPY package.json yarn.lock ./
 
# Install dependencies using Yarn
RUN yarn install
 
# Copy the rest of the application files
COPY . .
 
# Build the Next.js application
RUN yarn build
 
# Stage 2: Run the application
FROM node:16.15.0-slim as runner
 
WORKDIR /app
 
# Install only production dependencies using Yarn
COPY .env.production ./

COPY package.json yarn.lock ./

RUN yarn install --production
 
# Copy the .next folder from the build stage
COPY --from=builder /app/.next ./.next
 
# Copy the public folder if it exists
COPY --from=builder /app/public ./public
 
# Copy the necessary files for the application to run
COPY --from=builder /app/package.json ./package.json
COPY --from=builder /app/next.config.js ./next.config.js
 
# Expose the port the app runs on
EXPOSE 3000
 
# Start the Next.js application
CMD ["yarn", "start"]