import { Paper, Select } from '@material-ui/core';
import {
  Button,
  MenuItem,
  TableBody,
  TableCell,
  TableContainer,
  TableRow,
  TextField,
} from '@mui/material';
import { TimePicker } from '@mui/x-date-pickers';
import dayjs from 'dayjs';
import { Dispatch, SetStateAction } from 'react';
import { useSelector } from 'react-redux';
import { getDateTimeFormat } from '~/redux/selectors/userPreferences';

type DayTimeProps = {
  index: number;
  rows: { day: number; time: string; value: number }[];
  effective_dates: {
    effectiveDate: string;
    rows: {
      day: number;
      time: string;
      value: number;
    }[];
  }[];
  setEffectiveDates: Dispatch<
    SetStateAction<
      {
        effectiveDate: string;
        rows: {
          day: number;
          time: string;
          value: number;
        }[];
      }[]
    >
  >;
};
const DayTime = ({ index, effective_dates, rows, setEffectiveDates }: DayTimeProps) => {
  const dateTimeFormat = useSelector(getDateTimeFormat);

  return (
    <>
      <Button
        variant="contained"
        color="primary"
        sx={{ mt: 2 }}
        onClick={() => {
          setEffectiveDates((prev) => {
            return prev.map((d, i) =>
              i === index
                ? {
                    effectiveDate: d.effectiveDate,
                    rows: [...d.rows, { day: 1, time: '', value: 0 }],
                  }
                : d,
            );
          });
        }}
      >
        Add Day Time
      </Button>
      <TableContainer
        component={Paper}
        sx={{
          padding: 0.5,
          mt: 2,
        }}
      >
        <TableRow>
          <TableCell width={400}>Day</TableCell>
          <TableCell width={400}>Time</TableCell>
          <TableCell width={400}>Value</TableCell>
          <TableCell>Actions</TableCell>
        </TableRow>
        <TableBody>
          {rows.map((row, indx) => (
            <TableRow key={indx}>
              <TableCell>
                <Select
                  variant="outlined"
                  style={{
                    width: '100%',
                  }}
                  value={row.day}
                  onChange={(event) => {
                    const newDay = event.target.value;
                    setEffectiveDates((prev) => {
                      return prev.map((d, i) =>
                        i === index
                          ? {
                              effectiveDate: d.effectiveDate,
                              rows: d.rows.map((r, j) =>
                                j === indx
                                  ? {
                                      ...r,
                                      day: Number(newDay),
                                    }
                                  : r,
                              ),
                            }
                          : d,
                      );
                    });
                  }}
                >
                  {[
                    'Monday',
                    'Tuesday',
                    'Wednesday',
                    'Thursday',
                    'Friday',
                    'Saturday',
                    'Sunday',
                  ].map((day, i) => (
                    <MenuItem key={i} value={i}>
                      {day}
                    </MenuItem>
                  ))}
                </Select>
              </TableCell>
              <TableCell>
                <TimePicker
                  sx={{
                    '.MuiInputBase-input': {
                      padding: '16.5px 14px',
                    },
                  }}
                  label="Time"
                  defaultValue={dayjs(row.time, dateTimeFormat.split(' ')[1])}
                  format={dateTimeFormat.split(' ')[1]}
                  value={row.time ? dayjs(row.time, dateTimeFormat.split(' ')[1]) : null}
                  onChange={(newValue) => {
                    const newTime = newValue?.format(dateTimeFormat.split(' ')[1]) ?? '';
                    setEffectiveDates((prev) => {
                      return prev.map((d, i) =>
                        i === index
                          ? {
                              effectiveDate: d.effectiveDate,
                              rows: d.rows.map((r, j) =>
                                j === indx
                                  ? {
                                      ...r,
                                      time: newTime,
                                    }
                                  : r,
                              ),
                            }
                          : d,
                      );
                    });
                  }}
                />
              </TableCell>
              <TableCell>
                <TextField
                  type="number"
                  variant="outlined"
                  fullWidth
                  defaultValue={row.value}
                  required
                  onChange={(event) => {
                    const newValue = event.target.value;
                    setEffectiveDates((prev) => {
                      return prev.map((d, i) =>
                        i === index
                          ? {
                              effectiveDate: d.effectiveDate,
                              rows: d.rows.map((r, j) =>
                                j === indx
                                  ? {
                                      ...r,
                                      value: Number(newValue),
                                    }
                                  : r,
                              ),
                            }
                          : d,
                      );
                    });
                  }}
                />
              </TableCell>
              <TableCell>
                <Button
                  variant="contained"
                  color="error"
                  onClick={() => {
                    setEffectiveDates((prev) => {
                      return prev.map((d, i) =>
                        i === index
                          ? {
                              effectiveDate: d.effectiveDate,
                              rows: d.rows.filter((r, j) => j !== indx),
                            }
                          : d,
                      );
                    });
                  }}
                >
                  Delete
                </Button>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </TableContainer>
    </>
  );
};
export default DayTime;
