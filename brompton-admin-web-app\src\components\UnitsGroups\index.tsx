import { Box, Button, Container } from '@mui/material';
import { useState } from 'react';
import PageName from '../common/PageName/PageName';
import UnitsGroupsForm from './UnitGroupsForm';

const UnitsOfGroup = () => {
  const [openForm, setOpenForm] = useState(false);
  const [editItem, setEditItem] = useState<any>(null);
  return (
    <Container
      sx={{
        maxWidth: '100%',
        width: '100%',
        '@media (min-width: 1200px)': { width: '100%', maxWidth: '100%' },
      }}
    >
      <Box pl={0} pb={0} pt={0} sx={{ display: 'flex' }}>
        <Box
          sx={{
            flexGrow: 1,
            mt: 2,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
          }}
        >
          <PageName name="Unit groups" />
          <Box sx={{ display: 'flex' }}>
            <Button
              variant="contained"
              onClick={() => {
                setEditItem(null);
                setOpenForm(true);
              }}
              sx={{ mb: 2 }}
            >
              Add New Unit
            </Button>
          </Box>
        </Box>
      </Box>
      <UnitsGroupsForm
        openForm={openForm}
        setOpenForm={setOpenForm}
        editItem={editItem}
        setEditItem={setEditItem}
      />
    </Container>
  );
};

export default UnitsOfGroup;
