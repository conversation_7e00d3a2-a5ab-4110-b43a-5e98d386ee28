import { Box } from '@mui/material';
import CommonWidgetContainer from '~/components/common/CommonWidgetContainer';
import { Weather } from '~/types/widgets';

type WeatherContainerProps = {
  id: string;
  settings: Weather;
};

const WeatherContainer = ({ id, settings }: WeatherContainerProps) => {
  // eslint-disable-next-line @typescript-eslint/ban-ts-comment
  //@ts-ignore
  if (window?.myWidgetParam === undefined) {
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    //@ts-ignore
    window.myWidgetParam = [];
  }
  // eslint-disable-next-line @typescript-eslint/ban-ts-comment
  //@ts-ignore
  window.myWidgetParam.push({
    id: 11,
    cityid: '2647116',
    appid: '391e1ca8cb04144190a89c304d89f8f0',
    units: 'metric',
    containerid: 'openweathermap-widget-' + id,
  });
  (function () {
    const script = document.createElement('script');
    script.async = true;
    script.src =
      '//openweathermap.org/themes/openweathermap/assets/vendor/owm/js/weather-widget-generator.js';
    const s = document.getElementsByTagName('script')[0];
    if (s) {
      s.parentNode?.insertBefore(script, s);
    }
  })();
  return (
    <CommonWidgetContainer
      id={id}
      settings={settings}
      widgetType="Weather"
      widgetName="Weather"
      widgetContent={
        <Box
          sx={{
            height: '100%',
            width: '100%',
          }}
          id={'openweathermap-widget-' + id}
        ></Box>
      }
    />
  );
};
export default WeatherContainer;
