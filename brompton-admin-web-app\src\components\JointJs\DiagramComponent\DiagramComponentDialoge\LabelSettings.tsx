import { dia, shapes } from '@joint/core';
import { Box, MenuItem, Slider, TextField, Typography } from '@mui/material';
import { useEffect, useState } from 'react';
import { fontWeight, LabelSettings as LabelConfigs } from '~/types/diagram';

type LabelSettingsProps = {
  graph: dia.Graph;
  selectedElement: dia.Element<dia.Element.Attributes, dia.ModelSetOptions> | null;
};

const LabelSettings = ({ graph, selectedElement }: LabelSettingsProps) => {
  // Initialize default values
  const defaultData: LabelConfigs = {
    fontColor: '#000000',
    fontSize: 12,
    fontWeight: 'normal',
    backgroundColor: 'transparent',
    opacity: 1,
    isLabel: true,
  };

  // Safely retrieve `data` or use default values
  const data = (selectedElement?.get('data') as LabelConfigs) || defaultData;

  const [color, setColor] = useState<string>(data.fontColor);
  const [size, setSize] = useState<number>(data.fontSize);
  const [weight, setWeight] = useState<fontWeight>(data.fontWeight);
  const [backgroundColor, setBackgroundColor] = useState<string>(data.backgroundColor);
  const [opacity, setOpacity] = useState<number>(data.opacity * 100);

  useEffect(() => {
    if (selectedElement) {
      const updatedData = (selectedElement.get('data') as LabelConfigs) || defaultData;
      setColor(updatedData.fontColor);
      setSize(updatedData.fontSize);
      setWeight(updatedData.fontWeight);
      setBackgroundColor(updatedData.backgroundColor);
      setOpacity((updatedData.opacity ?? 1) * 100);
    }
  }, [selectedElement]);

  const handleUpdate = (field: keyof LabelConfigs, value: any) => {
    if (!selectedElement) return;

    // Get current data or initialize with defaultData
    const currentData = (selectedElement.get('data') as LabelConfigs) || defaultData;

    // Update the data object
    const updatedData = { ...currentData, [field]: value };
    selectedElement.set('data', updatedData);

    // Update visual attributes
    switch (field) {
      case 'fontColor':
        selectedElement.attr('label/style/color', value);
        break;
      case 'fontSize':
        selectedElement.attr('label/style/fontSize', value + 'px');
        break;
      case 'fontWeight':
        selectedElement.attr('label/style/fontWeight', value);
        break;
      case 'backgroundColor':
        selectedElement.attr('body/fill', value);
        break;
      case 'opacity':
        selectedElement.attr('body/fill-opacity', value); //
        break;
      default:
        break;
    }
  };

  if (!(selectedElement instanceof shapes.standard.TextBlock)) {
    return null;
  }

  return (
    <Box>
      {/* Font Color */}
      <TextField
        label="Font Color"
        type="color"
        value={color}
        onChange={(e) => {
          const newColor = e.target.value;
          setColor(newColor);
          handleUpdate('fontColor', newColor);
        }}
        fullWidth
        margin="normal"
      />

      {/* Font Size */}
      <TextField
        label="Font Size"
        type="number"
        value={size}
        onChange={(e) => {
          let newSize = Number(e.target.value);
          if (newSize < 12) newSize = 12;
          if (newSize > 30) newSize = 30;

          setSize(newSize);
          handleUpdate('fontSize', newSize);
        }}
        fullWidth
        margin="normal"
        inputProps={{
          min: 12,
          max: 30,
        }}
        helperText="Font size must be between 12px and 30px."
      />

      {/* Font Weight */}
      <TextField
        select
        label="Font Weight"
        value={weight}
        onChange={(e) => {
          const newWeight = e.target.value as fontWeight;
          setWeight(newWeight);
          handleUpdate('fontWeight', newWeight);
        }}
        fullWidth
        margin="normal"
      >
        {[
          'normal',
          'bold',
          'lighter',
          'bolder',
          '100',
          '200',
          '300',
          '400',
          '500',
          '600',
          '700',
          '800',
          '900',
        ].map((weight) => (
          <MenuItem key={weight} value={weight}>
            {weight}
          </MenuItem>
        ))}
      </TextField>

      {/* Background Color */}
      <TextField
        label="Background Color"
        type="color"
        value={backgroundColor === 'transparent' ? '#000000' : backgroundColor} // Placeholder color when transparent
        onChange={(e) => {
          const newBackgroundColor = e.target.value;
          setBackgroundColor(newBackgroundColor === '#000000' ? 'transparent' : newBackgroundColor); // Handle transparent
          handleUpdate(
            'backgroundColor',
            newBackgroundColor === '#000000' ? 'transparent' : newBackgroundColor,
          );
        }}
        fullWidth
        margin="normal"
      />

      <Box margin="normal" sx={{ width: '95%' }}>
        <Typography gutterBottom>Background Opacity</Typography>
        <Slider
          value={opacity}
          min={0}
          max={100}
          step={1}
          onChange={(e, newValue) => {
            const newOpacity = (newValue as number) / 100; // Convert 0-100 to 0-1
            setOpacity(newValue as number);
            handleUpdate('opacity', newOpacity);
          }}
          valueLabelDisplay="auto"
          aria-labelledby="background-transparency-slider"
        />
      </Box>
    </Box>
  );
};

export default LabelSettings;
