import { MenuItem, OutlinedInput, Select } from '@mui/material';
import React from 'react';
import { SelectChangeEvent } from '@mui/material/Select';
import { GroupByOptions } from '~/types/dashboard';

type AggregateByProps = {
  id: string;
  label: string;
  x: string;
  handleUpdate: (e: SelectChangeEvent) => void;
};

export function GroupBy({ id, label, x, handleUpdate }: AggregateByProps) {
  return (
    <Select
      labelId={'agg-by-label-' + id}
      id={'agg-by-select-' + id}
      value={x}
      label={label}
      fullWidth
      input={
        <OutlinedInput
          label={label}
          sx={{
            p: 0.5,
            '& legend': {
              maxWidth: '100%',
              height: 'fit-content',
              '& span': {
                opacity: 1,
              },
            },
          }}
        />
      }
      onChange={handleUpdate}
    >
      {GroupByOptions.map((option) => (
        <MenuItem key={option} value={option}>
          {option}
        </MenuItem>
      ))}
    </Select>
  );
}
