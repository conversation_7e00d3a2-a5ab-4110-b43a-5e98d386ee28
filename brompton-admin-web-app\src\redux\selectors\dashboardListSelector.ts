import { RootState } from '~/redux/store';
import { createSelector } from 'reselect';

export const dashboardListState = (state: RootState) => state.dashboardlist;

export const getCurrentDashboardId = createSelector(
  [dashboardListState],
  (dashboardList) => dashboardList.currentDashboardId,
);

export const getSearchedValue = createSelector(
  [dashboardListState],
  (dashboardList) => dashboardList.searchValue,
);
