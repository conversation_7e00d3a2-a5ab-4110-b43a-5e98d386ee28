import { AssetWithMeasurements, mapAssetTree } from '~/utils/mappers/asset-tree-mapper';
import { AssetMeasurement } from '~/measurements/domain/types';
import { Customer } from '~/types/customers';
import { Tree } from '~/components/AssetsTree';

describe('Asset tree mapper', () => {
  const customer: Customer = {
    logo: '',
    id: 3422,
    nameId: 'ms',
    name: '<PERSON>',
    address: '<PERSON>',
  };

  test('empty asset list should create tree with customer root node', () => {
    const tree = mapAssetTree(customer, []);

    expect(tree.size).toBe(1);
    expect(tree.tag).toBe('Microsoft');
    expect(tree.id).toBe('-1');
    expect(tree.type).toBe('company');
  });

  describe('root level assets', () => {
    let tree: Tree;

    beforeAll(() => {
      const mainBuilding: AssetWithMeasurements = {
        id: 34,
        tag: 'Main Building',
        parentIds: [],
        childrenIds: [],
        assetTypeId: 34,
        measurementGroups: [],
      };

      const secondaryBuilding: AssetWithMeasurements = {
        id: 43,
        tag: 'Secondary Building',
        parentIds: [],
        childrenIds: [],
        assetTypeId: 34,
        measurementGroups: [],
      };

      tree = mapAssetTree(customer, [mainBuilding, secondaryBuilding]);
    });

    it('should create tree with customer as root', () => {
      expect(tree.tag).toBe('Microsoft');
      expect(tree.id).toBe('-1');
    });

    it('should create tree with three nodes', () => {
      expect(tree.size).toBe(3);
    });

    it('should have asset as root children', () => {
      const children = tree.children;

      expect(children[0].tag).toBe('Main Building');
      expect(children[1].tag).toBe('Secondary Building');
    });
  });

  describe('root level asset with child', () => {
    let tree: Tree;

    beforeAll(() => {
      const mainBuilding: AssetWithMeasurements = {
        id: 34,
        tag: 'Main Building',
        parentIds: [],
        childrenIds: [43],
        assetTypeId: 34,
        measurementGroups: [],
      };

      const office: AssetWithMeasurements = {
        id: 43,
        tag: 'Office',
        parentIds: [34],
        childrenIds: [],
        assetTypeId: 34,
        measurementGroups: [],
      };

      tree = mapAssetTree(customer, [mainBuilding, office]);
    });

    it('should create tree with three nodes', () => {
      expect(tree.size).toBe(3);
    });

    it('should have main building as a child', () => {
      const child = tree.children[0];

      expect(child.tag).toBe('Main Building');
      expect(child.type).toBe('activo');
    });

    it('should have office as a grandchild', () => {
      const child = tree.children[0];
      const grandchild = child.children[0];

      expect(grandchild.tag).toBe('Office');
      expect(grandchild.type).toBe('activo');
    });
  });

  describe('root level asset with measurement', () => {
    let tree: Tree;

    beforeAll(() => {
      const officeTemperatureMeasurement: AssetMeasurement = {
        id: 34,
        tag: 'Office temperature',
        description: 'temperature in the room',
        dataTypeId: 45,
        valueTypeId: 25,
        measurementId: 8,
        typeId: 9,
      };

      const office: AssetWithMeasurements = {
        id: 43,
        tag: 'Office',
        parentIds: [],
        childrenIds: [],
        assetTypeId: 34,
        measurementGroups: [{ type: 'Temperature', measurements: [officeTemperatureMeasurement] }],
      };

      tree = mapAssetTree(customer, [office]);
    });

    it('should have office as child', () => {
      const child = tree.children[0];

      expect(child.tag).toBe('Office');
      expect(child.type).toBe('activo');
    });

    it('should have measurement type as grandchild', () => {
      const child = tree.children[0];
      const grandchild = child.children[0];

      expect(grandchild.id).toBe('43:9');
      expect(grandchild.tag).toBe('Temperature');
      expect(grandchild.type).toBe('medicion');
    });

    it('should have measurement as grandgrandchild', () => {
      const child = tree.children[0];
      const grandchild = child.children[0];
      const grandgrandchild = grandchild.children[0];

      expect(grandgrandchild.id).toBe('m:43:34');
      expect(grandgrandchild.tag).toBe('Office temperature');
      expect(grandgrandchild.type).toBe('metric');
    });
  });

  test('given a asset with missing child, it should be ignored', () => {
    const building: AssetWithMeasurements = {
      id: 4443,
      tag: 'Building',
      parentIds: [],
      childrenIds: [404],
      assetTypeId: 34,
      measurementGroups: [],
    };

    const tree = mapAssetTree(customer, [building]);

    expect(tree.size).toBe(2);
    expect(tree.tag).toBe('Microsoft');
    expect(tree.children[0].tag).toBe('Building');
  });
});
