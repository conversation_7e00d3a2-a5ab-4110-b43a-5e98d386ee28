const { test, expect } = require('@playwright/test');

/**
 * Centralized configuration
 */
const config = {
  baseUrl: 'https://test.brompton.ai/api/v0',
  auth: {
    username: process.env.API_USERNAME || 'test',
    password: process.env.API_PASSWORD || 'asdfasdf',
  },
};

/**
 * Authenticate and retrieve tokens
 */
async function authenticate(request) {
  const authUrl = `${config.baseUrl}/sessions`;
  const payload = { username: config.auth.username, password: config.auth.password };

  console.log('Authenticating at:', authUrl);

  try {
    const response = await request.post(authUrl, {
      headers: { 'Content-Type': 'application/json' },
      data: payload,
    });

    const status = response.status();
    const body = await response.json();

    console.log('Auth Response:', body);

    if (![200, 201].includes(status)) {
      throw new Error(
        `Authentication failed. Status: ${status}, Response: ${JSON.stringify(body)}`,
      );
    }

    const tokens = {
      accessToken: `BE-AccessToken=${body.access_token}; BE-CSRFToken=${body.csrf_token}`,
      csrfToken: body.csrf_token,
    };

    if (!tokens.accessToken || !tokens.csrfToken) {
      throw new Error('Authentication failed: Missing tokens in response.');
    }

    return tokens;
  } catch (error) {
    console.error('Authentication Error:', error.message);
    throw error;
  }
}

/**
 * Generic API request handler
 */
async function makeApiRequest(request, authTokens, options, expectedStatus, expectedProps = {}) {
  const url = `${config.baseUrl}${options.endpoint}`;
  console.log(`Making ${options.method} request to ${url}`);

  try {
    const response = await request[options.method.toLowerCase()](url, {
      headers: {
        'BE-CSRFToken': authTokens.csrfToken,
        Cookie: authTokens.accessToken,
        ...options.headers,
      },
      data: options.body || null,
    });

    const status = response.status();
    const responseText = await response.text();

    console.log(`Response Status: ${status}, Raw Body: ${responseText}`);

    let body;
    try {
      body = JSON.parse(responseText); // Attempt to parse JSON
    } catch (jsonError) {
      console.warn('Response is not valid JSON. Returning raw text.');
      body = responseText; // Fallback to raw text
    }

    if (status !== expectedStatus) {
      if (status === 400 && body?.message) {
        console.warn('Handling gracefully for known 400 error:', body.message);
        return body; // Return the response for specific handling of 400 errors
      }

      console.error('Request Details:', {
        url,
        method: options.method,
        headers: options.headers,
        body: options.body,
      });
      throw new Error(
        `Unexpected status. Expected: ${expectedStatus}, Received: ${status}, Body: ${JSON.stringify(
          body,
        )}`,
      );
    }

    if (expectedProps && typeof body === 'object') {
      // Validate response properties if body is an object
      for (const [key, value] of Object.entries(expectedProps)) {
        expect(body).toHaveProperty(key, value);
      }
    }

    return body;
  } catch (error) {
    console.error(`API Request Failed: ${error.message}`);
    throw error;
  }
}

/**
 * Data-driven test cases
 */
const testCases = [
  {
    name: 'GET /users/me - Retrieve user info',
    request: { method: 'GET', endpoint: '/users/me' },
    expectedStatus: 200,
    expectedProps: { username: 'test', email: expect.any(String) },
  },
  {
    name: 'GET /customers/85/logo - Retrieve customer logo',
    request: { method: 'GET', endpoint: '/customers/85/logo' },
    expectedStatus: 200,
    expectedProps: { logo: expect.any(String) },
  },
  {
    name: 'POST /customers/84/dashboards - Create/update dashboard',
    request: {
      method: 'POST',
      endpoint: '/customers/84/dashboards',
      headers: { 'Content-Type': 'application/json' },
      body: {
        title: 'Overview-3',
        data: JSON.stringify({ currentDashboardId: 89, dashboardTitle: 'Overview-3' }),
      },
    },
    expectedStatus: 201,
    expectedProps: { title: 'Overview-3' },
  },
];

/**
 * Playwright Test Suite
 */
test.describe('Generic API Test Suite', () => {
  let authTokens;

  // Authenticate before running tests
  test.beforeAll(async ({ request }) => {
    authTokens = await authenticate(request);
  });

  // Run all test cases dynamically
  testCases.forEach(({ name, request: options, expectedStatus, expectedProps }) => {
    test(name, async ({ request: apiRequest }) => {
      const response = await makeApiRequest(
        apiRequest,
        authTokens,
        options,
        expectedStatus,
        expectedProps,
      );
      console.log(`${name} - Response:`, response);
    });
  });

  // Example of adding a dynamic test case for creating a user
  test('POST /users - Create a new user dynamically', async ({ request: apiRequest }) => {
    const userPayload = {
      username: 'dynamic_user',
      password: 'dynamic123',
      first_name: 'Dynamic',
      last_name: 'User',
      email: '<EMAIL>',
    };

    const response = await makeApiRequest(
      apiRequest,
      authTokens,
      {
        method: 'POST',
        endpoint: '/users',
        headers: { 'Content-Type': 'application/json' },
        body: userPayload,
      },
      201,
      { username: userPayload.username },
    );

    console.log('User created successfully:', response);
  });
});
