export type FactorType = {
  id: number;
  name: string;
  updateAt: string;
  createdAt: string;
};

export type FactorTypeDTO = {
  total: number;
  items: FactorType[];
};

export type factorSchedules = {
  id: number;
  factor: number;
  effectiveDate: string;
  createdAt?: string;
  updatedAt?: string;
};

export type factorSchedulesDTO = {
  total: number;
  items: factorSchedules[];
};

export type factorScheduleTimes = {
  factor_schdule: number;
  timeOfDay: string;
  weekday: number;
  value: number;
};

export type factorScheduleTimesDTO = {
  items: factorScheduleTimes[];
  total: number;
};

export type timeVaringVariant = {
  measurement: number;
  seasonal: boolean;
  factorType: number;
};

export type timeVaringVariantDTO = {
  id?: number;
  timeVaryingFactor: timeVaringVariant;
  factorSchedule: {
    effectiveDate: string;
  }[];
  factorTimeOfDayValue: {
    effectiveDate: string;
    timeOfDay: string;
    value: number;
    weekday: number;
  }[];
};
export type timeVariangFactor = {
  id: number;
  factorType: number;
  measurement: number;
  seasonal: boolean;
  createdAt: string;
  updatedAt: string;
};
export type factorScheduleTime = {
  factorSchedule: number;
  timeOfDay: string;
  weekday: number;
  value: number;
  createdAt: string;
  updatedAt: string;
};
export type factorScheduleEffectiveDateTime = {
  effectiveDate: string;
  factorTimeOfDayValue: factorScheduleTime[];
};
export type FactorScheduleDetails = {
  id: number;
  factorType: number;
  measurement: number;
  seasonal: boolean;
  createdAt: string;
  updatedAt: string;
  factorSchedule: factorSchedules[];
  factorTimeOfDayValue: factorScheduleEffectiveDateTime[];
};
