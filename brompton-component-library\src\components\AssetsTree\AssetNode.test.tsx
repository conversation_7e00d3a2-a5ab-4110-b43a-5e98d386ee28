import { render, screen } from '@testing-library/react';
import * as stories from 'components/AssetsTree/AssetNode.stories';
import { composeStories } from '@storybook/testing-react';
import React from 'react';
import userEvent from '@testing-library/user-event';
import { NodeConfig } from './AssetNode';

const {
  ExpandedAssetNode,
  CheckedMetricNode,
  MetricNode,
  MeasurementNode,
  AssetItemNode,
  CompanyNode,
  NoCheckboxAssetItemNode,
} = composeStories(stories);

describe('AssetNode', () => {
  const toggleExpandHandler = jest.fn();

  describe('expanded asset', () => {
    beforeEach(() => {
      render(<ExpandedAssetNode onExpandToggle={toggleExpandHandler} />);
    });

    it('should have an expanded button', () => {
      const expandElement = screen.getByRole('button', { expanded: true });

      expect(expandElement).not.toBeNull();
    });
  });

  describe('checked asset', () => {
    const selectHandler = jest.fn();

    beforeEach(() => {
      render(<CheckedMetricNode handleSelection={selectHandler} />);
    });

    it('should have a text with its tag', () => {
      expect(screen.getByText('Line Voltage'));
    });

    it('should have a checked checkbox', () => {
      const checkboxElement = screen.getByRole('checkbox', { checked: true });

      expect(checkboxElement).not.toBeNull();
    });

    describe('when checking box', () => {
      it('should trigger select callback', () => {
        const checkboxElement = screen.getByRole('checkbox');

        checkboxElement.click();

        expect(selectHandler).toBeCalled();
      });
    });
  });

  describe('metric asset', () => {
    const selectHandler = jest.fn();

    beforeEach(() => {
      render(<MetricNode handleSelection={selectHandler} />);
    });

    it('should have a text with its tag', () => {
      expect(screen.getByText('Line Voltage'));
    });

    it('should have an unchecked checkbox', () => {
      const checkboxElement = screen.getByRole('checkbox', { checked: false });

      expect(checkboxElement).not.toBeNull();
    });

    describe('when checking box', () => {
      it('should trigger select callback', () => {
        const checkboxElement = screen.getByRole('checkbox');

        checkboxElement.click();

        expect(selectHandler).toBeCalled();
      });
    });

    it('should display metric icon', () => {
      const icon = screen.getByTestId('analytics.svg');

      expect(icon).not.toBeNull();
    });

    it('should not display expand button', () => {
      const expandElement = screen.queryByRole('button');

      expect(expandElement).toBeNull();
    });
  });

  describe('measurement asset', () => {
    beforeEach(() => {
      render(<MeasurementNode />);
    });

    it('should display measurement icon', () => {
      const icon = screen.getByTestId('tachometer-fast-alt.svg');

      expect(icon).not.toBeNull();
    });

    it('should display expand button', () => {
      const expandElement = screen.getByRole('button', { expanded: false });

      expect(expandElement).not.toBeNull();
    });
  });

  describe('asset item', () => {
    const toggleExpandHandler = jest.fn();
    const selectHandler = jest.fn();
    const contextMenuHandler = jest.fn();

    beforeEach(() => {
      render(
        <AssetItemNode
          onExpandToggle={toggleExpandHandler}
          handleSelection={selectHandler}
          nodeConfig={{
            ...(AssetItemNode.args?.nodeConfig as NodeConfig),
            contextMenuActions: [
              { label: 'New child', onClick: contextMenuHandler },
            ],
          }}
        />
      );
    });

    it('should have a text with its tag', () => {
      expect(screen.getByText('California'));
    });

    it('should have collapsed button', () => {
      const collapseElement = screen.getByRole('button', { expanded: false });

      expect(collapseElement).not.toBeNull();
    });

    it('should have an unchecked checkbox', () => {
      const checkboxElement = screen.getByRole('checkbox', { checked: false });

      expect(checkboxElement).not.toBeNull();
    });

    describe('when clicking button', () => {
      it('should trigger toggle callback', () => {
        const collapseElement = screen.getByRole('button');

        collapseElement.click();

        expect(toggleExpandHandler).toBeCalled();
      });
    });

    describe('when right clicking', () => {
      beforeEach(async () => {
        const textElement = screen.getByText('California');
        await userEvent.pointer({ keys: '[MouseRight>]', target: textElement });
        await userEvent.click(screen.getByText('New child'));
      });

      it('should trigger context menu callback', () => {
        expect(contextMenuHandler).toBeCalled();
      });

      it('should trigger context menu callback with clicked node id', async () => {
        expect(contextMenuHandler.mock.calls[0][0]).toBe('1');
      });
    });

    describe('when checking box', () => {
      it('should trigger select callback', () => {
        const checkboxElement = screen.getByRole('checkbox');

        checkboxElement.click();

        expect(selectHandler).toBeCalled();
      });
    });

    it('should display asset icon', () => {
      const icon = screen.getByTestId('building.svg');

      expect(icon).not.toBeNull();
    });
  });

  describe('no checkbox asset item', () => {
    const toggleExpandHandler = jest.fn();
    const selectHandler = jest.fn();
    beforeEach(() => {
      render(
        <NoCheckboxAssetItemNode
          onExpandToggle={toggleExpandHandler}
          handleSelection={selectHandler}
        />
      );
    });

    it('should not have a checkbox', () => {
      const checkboxElement = screen.queryByRole('checkbox');

      expect(checkboxElement).toBeNull();
    });

    describe('when clicking text', () => {
      it('should trigger select callback', () => {
        const row = screen.getByText('California');

        row.click();

        expect(selectHandler).toBeCalled();
      });
    });
  });

  describe('company item', () => {
    const selectHandler = jest.fn();

    beforeEach(() => {
      render(
        <CompanyNode
          onExpandToggle={toggleExpandHandler}
          handleSelection={selectHandler}
        />
      );
    });

    it('should display company icon', () => {
      const icon = screen.getByTestId('user-circle.svg');

      expect(icon).not.toBeNull();
    });

    it('should not have a checked checkbox', () => {
      const checkboxElement = screen.queryByRole('checkbox');

      expect(checkboxElement).toBeNull();
    });

    it('should have an expanded button', () => {
      const expandElement = screen.getByRole('button', { expanded: false });

      expect(expandElement).not.toBeNull();
    });
  });
});
