import {
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON>ton,
  CircularProgress,
  <PERSON>,
  Step,
  Step<PERSON><PERSON><PERSON>,
  Stepper,
  Typography,
} from '@mui/material';
import { Fragment, useEffect, useState } from 'react';

import { useInjectTsDataMutation, useUploadCsvMutation } from '~/redux/api/dataIngestionApi';
import CsvPreviewAndMapping from './CsvPreviewAndMapping';
import CsvUploadForm from './CsvUploadForm';
import MeasurementRedisMapping from './MeasurementRedisMapping';

const steps = ['Upload CSV', 'Map Columns', 'Map Data'];

const extractErrorDetails = (error: any) => {
  if (!error) return { message: 'Unknown error', details: null };

  // The API might return error details in error.data.detail or directly in error.data
  const errorDetail = error.data?.detail || error.data;

  if (!errorDetail) {
    return {
      message: error.message || 'Unknown error occurred',
      details: null,
    };
  }

  if (errorDetail.error === 'Non-existent Redis Keys' && errorDetail.missing_details) {
    const friendlyMessages = errorDetail.missing_details.map((item: any) => {
      const asset = item.asset || '[Unknown asset]';
      const measurement = item.measurement || '[Unknown measurement]';
      const redisKey = item.redis_key;
      return `For asset "${asset}" and measurement "${measurement}", the Redis key "${redisKey}" does not exist.`;
    });

    return {
      message: errorDetail.message || 'Some Redis keys do not exist.',
      details: {
        friendlyMessages,
        ...errorDetail,
      },
    };
  }

  // Handle partial success responses
  if (
    errorDetail.status === 'partial_success' ||
    errorDetail.status === 'success' ||
    errorDetail.status === 'error'
  ) {
    return {
      message: errorDetail.message || 'Some operations failed. Please check the details below.',
      details: {
        error: errorDetail.error,
        validation_errors: errorDetail.validation_errors,
        processing_errors: errorDetail.processing_errors,
        non_existent_keys: errorDetail.non_existent_keys,
        invalid_composite_keys:
          errorDetail.invalid_composites || errorDetail.invalid_composite_keys,
        csv_composite_keys: errorDetail.csv_composite_keys,
        expected_keys: errorDetail.expected_keys,
        failed_operations: errorDetail.failed_operations,
        signal_summary: errorDetail.signal_summary,
        total_points: errorDetail.total_points,
        success_count: errorDetail.success_count,
        failed_count: errorDetail.failed_count,
        status: errorDetail.status,
        warning: errorDetail.warning,
        suggestion: errorDetail.suggestion,
      },
    };
  }

  return {
    message: errorDetail.message || 'Failed to process data. Please check your inputs.',
    details: {
      error: errorDetail.error,
      validation_errors: errorDetail.validation_errors,
      processing_errors: errorDetail.processing_errors,
      non_existent_keys: errorDetail.non_existent_keys,
      invalid_composite_keys: errorDetail.invalid_composites || errorDetail.invalid_composite_keys,
      csv_composite_keys: errorDetail.csv_composite_keys,
      expected_keys: errorDetail.expected_keys,
    },
  };
};

const DataIngestion = () => {
  const [activeStep, setActiveStep] = useState(0);
  const [csvData, setCsvData] = useState<{
    columns: string[];
    previewRows: any[][];
    signalNames?: string[];
    originalFile?: File;
    compositeSignals?: { fileAsset: string; fileMeasure: string }[];
    timestampFormat?: 'UTC' | 'IST' | 'SEPARATE';
  } | null>(null);
  const [columnMapping, setColumnMapping] = useState<Record<string, string> | null>(null);
  const [redisMapping, setRedisMapping] = useState<Record<string, string>>({});
  const [cacheId, setCacheId] = useState<string | null>(null);
  const [uploadResult, setUploadResult] = useState<any | null>(null);
  const [injectResult, setInjectResult] = useState<any | null>(null);
  const [originalFile, setOriginalFile] = useState<File | null>(null); // Store the original file
  const [resetTrigger, setResetTrigger] = useState(false);
  const [showSuccessMessage, setShowSuccessMessage] = useState(false);
  const [isResetting, setIsResetting] = useState(false);
  const [injectErrorState, setInjectError] = useState<{
    message: string;
    details?: {
      error?: string;
      validation_errors?: string;
      processing_errors?: string;
      non_existent_keys?: string[];
      invalid_composite_keys?: string[];
      csv_composite_keys?: string[];
      expected_keys?: string[];
      failed_operations?: Record<
        string,
        Array<{
          timestamp: number;
          value: number;
          error: string;
        }>
      >;
      signal_summary?: Record<
        string,
        {
          redis_key: string;
          total_count: number;
          success_count: number;
          failed_count: number;
        }
      >;
      total_points?: number;
      success_count?: number;
      failed_count?: number;
      status?: string;
      warning?: string;
      suggestion?: string;
      friendlyMessages?: string[];
      missing_details?: { asset?: string; measurement?: string; redis_key: string }[];
    } | null;
  } | null>(null);

  // RTK Query hooks
  const [uploadCsv, { isLoading: isUploading, error: uploadError }] = useUploadCsvMutation();
  const [injectTsData, { isLoading: isInjecting, error: injectError }] = useInjectTsDataMutation();

  const handleNext = () => {
    // Clear any error messages when moving to a new step
    setInjectError(null);
    // We can't directly clear uploadError as it comes from RTK Query

    setActiveStep((prevActiveStep) => prevActiveStep + 1);
  };

  const handleBack = () => {
    // Clear any error messages when moving to a previous step
    setInjectError(null);
    // We can't directly clear uploadError as it comes from RTK Query

    setActiveStep((prevActiveStep) => prevActiveStep - 1);
  };

  // Add a function to handle column mapping changes
  const handleColumnMappingChange = (mapping: Record<string, string>) => {
    setColumnMapping(mapping);

    // Log the mapping for debugging
    console.log('Column mapping updated:', mapping);

    // Check if the mapping is valid
    const requiredFields = ['asset', 'measure', 'value'];
    const hasRequiredFields = requiredFields.every((field) => !!mapping[field]);

    const hasTimestamp = !!mapping.timestamp;
    const hasDateTime = !!mapping.date && !!mapping.time;

    console.log('Mapping valid:', hasRequiredFields && (hasTimestamp || hasDateTime));
  };

  // Add a function to handle redis mapping changes
  const handleRedisMappingChange = (mapping: Record<string, string>) => {
    setRedisMapping(mapping);
  };

  // Add a function to handle timestamp format changes
  const handleTimestampFormatChange = (format: 'UTC' | 'IST' | 'SEPARATE') => {
    setCsvData((prevData) =>
      prevData
        ? {
            ...prevData,
            timestampFormat: format,
          }
        : null,
    );
  };

  // Reset function to clear all state and return to the first step
  const resetFlow = () => {
    setIsResetting(false);
    setActiveStep(0);
    setCsvData(null);
    setColumnMapping(null);
    setRedisMapping({});
    setCacheId(null);
    setUploadResult(null);
    setInjectResult(null);
    setOriginalFile(null);
    setResetTrigger((prev) => !prev); // Toggle to force re-render
    setShowSuccessMessage(false);
    setInjectError(null);
  };

  // Effect to handle successful data injection
  useEffect(() => {
    if (injectResult && injectResult.status === 'success') {
      // Show success message briefly before resetting
      setShowSuccessMessage(true);
      setIsResetting(true);

      // Set a timeout to reset the flow after showing the success message
      const timer = setTimeout(() => {
        resetFlow();
      }, 3000); // 3 seconds delay to show success message

      return () => clearTimeout(timer);
    }
  }, [injectResult]);

  const handleUploadParsed = async (data: any, file: File) => {
    try {
      // Store the original file
      setOriginalFile(file);

      // Store the CSV data for the mapping step
      setCsvData({
        columns: data.columns,
        previewRows: data.previewRows,
        originalFile: file,
      });
    } catch (err: any) {
      console.error('Failed to process CSV:', err);
    }
  };

  // Add validation functions for each step
  const validateStep0 = () => {
    return !!csvData && csvData.columns && csvData.columns.length > 0;
  };

  const validateStep1 = () => {
    if (!columnMapping) return false;

    // Check for required fields: asset, measure, value
    const requiredFields = ['asset', 'measure', 'value'];
    const hasRequiredFields = requiredFields.every((field) => !!columnMapping[field]);

    // Check for timestamp fields based on format
    let hasValidTimestamp = false;

    if (csvData?.timestampFormat === 'SEPARATE') {
      hasValidTimestamp = !!columnMapping.date && !!columnMapping.time;
    } else {
      // For UTC or IST formats
      hasValidTimestamp = !!columnMapping.timestamp;
    }

    return hasRequiredFields && hasValidTimestamp;
  };

  const validateStep2 = () => {
    // Check if all signals are mapped
    if (!Object.keys(redisMapping).length) return false;

    // Additional validation logic if needed
    return true;
  };

  // Update the handleNext function to include validation and API calls
  const handleNextWithValidation = async () => {
    // Clear any previous error messages
    setInjectError(null);
    // We can't directly clear uploadError as it comes from RTK Query

    if (activeStep === 0) {
      // Validate step 0
      if (!validateStep0()) return;

      // If valid, move to next step
      handleNext();
    } else if (activeStep === 1) {
      // Validate step 1
      if (!validateStep1()) return;

      try {
        // Now that we have the mapping, we can upload the file with the measurement column
        if (!originalFile || !columnMapping?.measure) {
          throw new Error('Original file or measure column mapping is missing');
        }

        // Create form data for the API
        const formData = new FormData();
        formData.append('file', originalFile);
        formData.append('measurementColumn', columnMapping.measure);
        formData.append('assetColumn', columnMapping.asset);

        // Call the API
        const result = await uploadCsv(formData).unwrap();

        // Store the result
        setUploadResult(result);
        setCacheId(result.cache_id);

        // Update CSV data with signal names from the API
        setCsvData((prevData) =>
          prevData
            ? {
                ...prevData,
                compositeSignals: result.composite_signals || [],
              }
            : null,
        );

        // Move to the next step
        handleNext();
      } catch (err: any) {
        console.error('Failed to upload CSV:', err);

        // If we have available signals in the error response, still store them
        if (err.data && err.data.available_columns) {
          setCsvData((prevData) =>
            prevData
              ? {
                  ...prevData,
                  signalNames: err.data.available_signals || [],
                }
              : null,
          );
        }
      }
    } else if (activeStep === 2) {
      // Validate step 2
      if (!validateStep2()) return;

      // Handle the final submission
      try {
        // Ensure we have the original file
        if (!originalFile) {
          throw new Error('Original file is missing');
        }

        // Create form data for the API
        const formData = new FormData();
        formData.append('file', originalFile);

        // Ensure all keys in the mapping are trimmed
        const trimmedMapping: Record<string, string> = {};
        Object.entries(redisMapping).forEach(([signal, redisKey]) => {
          trimmedMapping[signal.trim()] = redisKey;
        });

        formData.append('signal_key_mapping_str', JSON.stringify(trimmedMapping));
        formData.append('data_type', 'REAL'); // Default to REAL, could be made configurable

        // Add the signal_column and entity_column parameters with updated names
        if (columnMapping) {
          formData.append('measurement_column', columnMapping.measure);
          formData.append('asset_column', columnMapping.asset);
          formData.append('value_column', columnMapping.value);

          // Add timestamp columns based on format
          if (csvData?.timestampFormat === 'UTC' && columnMapping.timestamp) {
            formData.append('timestamp_utc_column', columnMapping.timestamp);
          } else if (csvData?.timestampFormat === 'IST' && columnMapping.timestamp) {
            formData.append('timestamp_ist_column', columnMapping.timestamp);
          } else if (csvData?.timestampFormat === 'SEPARATE') {
            if (columnMapping.date) formData.append('date_column', columnMapping.date);
            if (columnMapping.time) formData.append('time_column', columnMapping.time);
          }
        } else {
          throw new Error('Column mapping is missing');
        }

        // Add cache_id if available
        if (cacheId) {
          formData.append('cache_id', cacheId);
        }

        // Call the API
        const result = await injectTsData(formData).unwrap();

        // Store the result
        setInjectResult(result);
        setInjectError(null); // Clear any previous errors
      } catch (err: any) {
        console.error('Failed to inject data:', err);

        const errorInfo = extractErrorDetails(err);
        setInjectError(errorInfo);

        // Update the injectResult with error status only
        setInjectResult({
          status: 'error',
        });
      }
    }
  };

  // Determine if we should show the stepper content or success message
  const showStepperContent = !showSuccessMessage;

  // Add this useEffect to clear errors when the active step changes
  useEffect(() => {
    // Clear error messages when changing steps
    setInjectError(null);
    // We can't directly clear uploadError as it comes from RTK Query
  }, [activeStep]);

  return (
    <Box p={4}>
      <Typography variant="h4" mb={3}>
        Data Ingestion Flow
      </Typography>
      <Stepper activeStep={activeStep} alternativeLabel sx={{ mb: 4 }}>
        {steps.map((label) => (
          <Step key={label}>
            <StepLabel>{label}</StepLabel>
          </Step>
        ))}
      </Stepper>
      <Paper elevation={2} sx={{ p: 4 }}>
        {uploadError && !showSuccessMessage && (
          <Alert severity="error" sx={{ mb: 3 }}>
            {typeof uploadError === 'string'
              ? uploadError
              : 'Failed to upload CSV. Please try again.'}
          </Alert>
        )}

        {injectErrorState && !showSuccessMessage && injectErrorState.details?.missing_details && (
          <Box mb={3}>
            <Alert severity="error" sx={{ mb: 1 }}>
              <Box mb={1} fontWeight="bold">
                {injectErrorState.message}
              </Box>

              {(() => {
                const groups = injectErrorState.details.missing_details.reduce<
                  Record<string, { asset: string; measurement: string }[]>
                >((acc, curr) => {
                  if (!acc[curr.redis_key]) acc[curr.redis_key] = [];
                  acc[curr.redis_key].push({
                    asset: curr.asset || '[Unknown asset]',
                    measurement: curr.measurement || '[Unknown measurement]',
                  });
                  return acc;
                }, {});

                return Object.entries(groups).map(([redisKey, entries]) => (
                  <Box key={redisKey} mb={2}>
                    <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
                      Redis key &quot;{redisKey}&quot; does not exist:
                    </Typography>
                    <Box component="ul" sx={{ pl: 3, m: 0 }}>
                      {entries.map(({ asset, measurement }, idx) => (
                        <li key={idx}>
                          Asset: <strong>{asset}</strong>, Measurement:{' '}
                          <strong>{measurement}</strong>
                        </li>
                      ))}
                    </Box>
                  </Box>
                ));
              })()}
            </Alert>
          </Box>
        )}

        {showSuccessMessage && injectResult && injectResult.status === 'success' && (
          <Alert severity="success" sx={{ mb: 3 }}>
            {injectResult.message ||
              `Successfully injected ${injectResult.success_count} data points. Redirecting to start...`}
          </Alert>
        )}

        {!showSuccessMessage && injectResult && injectResult.status === 'error' && (
          <Alert severity="error" sx={{ mb: 3 }}>
            {injectResult.message ||
              'Failed to inject data. Please check your inputs and try again.'}
          </Alert>
        )}

        {isResetting && (
          <Box display="flex" justifyContent="center" my={4}>
            <CircularProgress />
          </Box>
        )}

        {!isResetting && showStepperContent && activeStep === 0 && (
          <CsvUploadForm
            onParsed={handleUploadParsed}
            isLoading={isUploading}
            key={`upload-form-${resetTrigger}`}
          />
        )}

        {!isResetting && showStepperContent && activeStep === 1 && csvData && (
          <CsvPreviewAndMapping
            columns={csvData.columns}
            previewRows={csvData.previewRows}
            initialMapping={columnMapping || {}}
            onMappingChange={handleColumnMappingChange}
            onTimestampFormatChange={handleTimestampFormatChange}
          />
        )}

        {!isResetting && showStepperContent && activeStep === 2 && csvData && columnMapping && (
          <MeasurementRedisMapping
            csvData={csvData}
            columnMapping={columnMapping}
            onMappingChange={handleRedisMappingChange}
            isLoading={isInjecting}
          />
        )}
      </Paper>
      {!isResetting && showStepperContent && (
        <Box display="flex" justifyContent="space-between" mt={3}>
          <Button
            variant="outlined"
            disabled={activeStep === 0 || isUploading || isInjecting}
            onClick={handleBack}
          >
            Back
          </Button>
          <Button
            variant="contained"
            disabled={
              isUploading ||
              isInjecting ||
              (activeStep === 0 && !validateStep0()) ||
              (activeStep === 1 && !validateStep1()) ||
              (activeStep === 2 && !validateStep2())
            }
            onClick={handleNextWithValidation}
          >
            {activeStep === steps.length - 1 ? 'Ingest Data' : 'Next'}
          </Button>
        </Box>
      )}
    </Box>
  );
};

export default DataIngestion;
