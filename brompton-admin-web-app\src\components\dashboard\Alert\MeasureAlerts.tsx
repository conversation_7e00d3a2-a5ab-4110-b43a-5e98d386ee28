import {
  Box,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Typography,
} from '@mui/material';
import { useSelector } from 'react-redux';
import Loader from '~/components/common/Loader';
import {
  useGetAlertsByMeasurementQuery,
  useGetConditionsQuery,
  useGetPeriodQuery,
} from '~/redux/api/alertApi';
import { getActiveCustomer } from '~/redux/selectors/customerSelectors';
import { getSelectedViewMeasureId } from '~/redux/selectors/treeSelectors';

const MeasureAlerts = () => {
  const activeCustomer = useSelector(getActiveCustomer);
  const SelectedViewMeasureId = useSelector(getSelectedViewMeasureId);
  const measurementId = SelectedViewMeasureId.split(':');
  const { data: alerts, isFetching: isLoadingAlerts } = useGetAlertsByMeasurementQuery(
    { measurementId: measurementId[2].toString() },
    {
      skip: !activeCustomer?.id || !SelectedViewMeasureId,
    },
  );
  const { data: conditions } = useGetConditionsQuery();
  const { data: periods } = useGetPeriodQuery();
  return (
    <Box>
      <Typography>Measure Alerts</Typography>
      {isLoadingAlerts && <Loader />}
      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>ID</TableCell>
              <TableCell>Asset</TableCell>
              <TableCell>Condition</TableCell>
              <TableCell>Measurement</TableCell>
              <TableCell>Period</TableCell>
              <TableCell>Threshold Type</TableCell>
              <TableCell>Threshold Value</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {alerts?.items?.map((alert) => (
              <TableRow key={alert.id}>
                <TableCell>{alert.asset}</TableCell>
                <TableCell>
                  {
                    conditions?.items?.find((condition) => condition.id === alert.condition)
                      ?.condition
                  }
                </TableCell>

                <TableCell>
                  {periods?.items?.find((period) => period.id === alert.period)?.label}
                </TableCell>
                <TableCell>{alert.thresholdType}</TableCell>
                <TableCell>{alert.thresholdValue}</TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>
    </Box>
  );
};
export default MeasureAlerts;
