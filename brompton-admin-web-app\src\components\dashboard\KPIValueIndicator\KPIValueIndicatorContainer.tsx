import { Box, Grid, Typography } from '@mui/material';
import { useSelector } from 'react-redux';
import CommonWidgetContainer from '~/components/common/CommonWidgetContainer';
import { CustomizedCircularProgress } from '~/components/common/CustomizedCircularProgress';
import Loader from '~/components/common/Loader';
import { NoDataFound } from '~/components/common/NoDataFound';
import NoMeasureSelected from '~/components/common/NoMeasureSelected';
import { useFetchKPIValueIndicator } from '~/hooks/useFetchKPIValueIndicator';
import { getMetricsIdToName } from '~/redux/selectors/dashboardSelectors';
import { KPIValueIndicator } from '~/types/widgets';
import { formatMetricLabel, formatNumber, hasNoMeasureSelected, roundNumber } from '~/utils/utils';
import KPIValueIndicatorSettingsDialog from './KPIValueIndicatorSettingsDialog';
import { getThousandSeparator } from '~/redux/selectors/userPreferences';

type KPIValueIndicatorProps = {
  id: string;
  settings: KPIValueIndicator;
};
function hasNoMeasuresSelected(assetMeasure: KPIValueIndicator['assetMeasure']): boolean {
  // If there's no measureId array or it is empty, that's "no measure"
  if (!assetMeasure?.measureId || assetMeasure.measureId.length === 0) {
    return true;
  }
  // If all measure IDs are blank strings, that's also "no measure"
  return assetMeasure.measureId.every((mId) => mId.trim() === '');
}
const KPIValueIndicatorContainer = ({ id, settings }: KPIValueIndicatorProps) => {
  const metricsIdToName = useSelector(getMetricsIdToName);
  const defaultTitle =
    settings.mode === 'dashboard'
      ? formatMetricLabel(
          Object.keys(settings?.dbMeasureIdToName).length > 0 &&
            settings.assetMeasure.measureId.length > 0
            ? settings.dbMeasureIdToName[settings.assetMeasure.measureId[0]] ?? 'Unknown Measure'
            : 'Unknown Measure',
        )
      : settings.mode === 'template' && settings.selectedDbMeasureId !== ''
      ? metricsIdToName[settings.selectedDbMeasureId] ?? 'Unknown Measure'
      : settings.title.value;
  const { data, isLoading } = useFetchKPIValueIndicator(id, settings);
  const noMeasuresSelected = hasNoMeasureSelected(settings);
  const thousandSeparator = useSelector(getThousandSeparator);

  return (
    <>
      <CommonWidgetContainer
        id={id}
        settings={settings}
        widgetName="Circle"
        widgetContent={
          <>
            {noMeasuresSelected ? (
              <NoMeasureSelected />
            ) : (
              <>
                {isLoading && <Loader />}
                {!isLoading && !data && (
                  <Box
                    sx={{
                      position: 'absolute',
                      height: '100%',
                      width: '100%',
                      display: 'flex',
                      justifyContent: 'center',
                      alignItems: 'center',
                    }}
                  >
                    <NoDataFound />
                  </Box>
                )}
                {!isLoading && data && (
                  <Grid container height={'100%'} p={2}>
                    <Grid
                      xs={6}
                      item
                      sx={{
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        height: '100%',
                      }}
                    >
                      <Box>
                        <Typography
                          variant="h5"
                          sx={{
                            fontSize: settings.title.isVisible
                              ? settings.title.fontSize + 'px'
                              : undefined,
                            fontWeight: settings.title.isVisible
                              ? settings.title.fontWeight
                              : undefined,
                            color: settings.title.isVisible ? settings.title.color : undefined,
                          }}
                          component="div"
                        >
                          {settings.title.isVisible ? settings.title.value : defaultTitle}
                        </Typography>
                        <Typography
                          variant="h3"
                          color="textPrimary"
                          sx={{
                            fontWeight: 'bold',
                          }}
                        >
                          {settings.prefix.isVisible ? settings.prefix.value : null}{' '}
                          {thousandSeparator
                            ? formatNumber(data.lastValue)
                            : roundNumber(data.lastValue)}{' '}
                          {settings.suffix.isVisible ? settings.suffix.value : null}
                        </Typography>
                      </Box>
                    </Grid>
                    <Grid
                      xs={6}
                      item
                      sx={{
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        height: '100%',
                      }}
                    >
                      <Box
                        sx={{
                          height: '100%',
                          width: '100%',
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                        }}
                      >
                        <CustomizedCircularProgress
                          value={data.differencePercentage}
                          sx={{ color: settings.lineColor }}
                        />
                      </Box>
                    </Grid>
                  </Grid>
                )}
              </>
            )}
          </>
        }
        settingsDialog={KPIValueIndicatorSettingsDialog}
        widgetType="kpi-value-indicator"
      />
    </>
  );
};

export default KPIValueIndicatorContainer;
