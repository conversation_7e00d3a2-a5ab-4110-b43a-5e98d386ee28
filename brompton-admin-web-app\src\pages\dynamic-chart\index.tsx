import { Box, Container } from '@mui/material';
import PageName from '~/components/common/PageName/PageName';
import TestDashboards from '~/components/TestDahboard/TestDashboards';

const DynamicChart = () => {
  return (
    <Container
      sx={{
        maxWidth: '100%',
        width: '100%',
        '@media (min-width: 1200px)': { width: '100%', maxWidth: '100%' },
      }}
    >
      <Box py={2}>
        <PageName name="Dynamic Chart" />
      </Box>
      <TestDashboards />
    </Container>
  );
};

export default DynamicChart;
