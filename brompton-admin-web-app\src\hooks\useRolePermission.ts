import { jwtDecode } from 'jwt-decode';
import { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';
import { getActiveCustomer } from '~/redux/selectors/customerSelectors';
import { getUserToken } from '~/redux/selectors/dashboardSelectors';
export enum Role {
  SUPER_ADMIN,
  ADMIN,
  USER,
  POWER_USER,
}
export type UserToken = {
  exp: number;
  iat: number;
  roles: {
    [key in keyof typeof Role]: number[];
  };
};
export const userPermissions = ['user.view', 'customer.view', 'alert.view', 'diagram.view'];
export const powerUserPermissions = [
  ...userPermissions,
  'customer.create',
  'customer.update',
  'customer.delete',
  'customer.view',
  'asset-template.create',
  'asset-template.update',
  'asset-template.delete',
  'asset-template.view',
  'asset-instance.create',
  'asset-instance.update',
  'asset-instance.delete',
  'asset-instance.view',
  'asset.types.view',
  'diagram.create',
  'diagram.update',
  'diagram.delete',
  'diagram.view',
  'alert.create',
  'alert.update',
  'alert.delete',
  'alert.view',
  'unit.measure.view',
  'calculation-engine.view',
  'calculation-engine.create',
  'calculation-engine.update',
  'calculation-engine.delete',
];
export const adminPermissions = [
  ...powerUserPermissions,
  'asset-template.clone',
  'user.create',
  'user.update',
  'user.delete',
  'user.view',
];
export const superAdminPermissions = [
  ...adminPermissions,
  'dashboard.create',
  'dashboard.update',
  'dashboard.delete',
  'dashboard-template.create',
  'dashboard-template.update',
  'dashboard-template.delete',
  'dashboard-template.view',
];
export const userWisePermissions: { [key in Role]: string[] } = {
  [Role.SUPER_ADMIN]: superAdminPermissions,
  [Role.ADMIN]: adminPermissions,
  [Role.POWER_USER]: powerUserPermissions,
  [Role.USER]: userPermissions,
};
export type UserPermissionName =
  | 'user.view'
  | 'user.create'
  | 'user.update'
  | 'user.delete'
  | 'alert.create'
  | 'alert.update'
  | 'alert.delete'
  | 'alert.view'
  | 'customer.create'
  | 'customer.update'
  | 'customer.delete'
  | 'customer.view'
  | 'calculation-engine.create'
  | 'calculation-engine.update'
  | 'calculation-engine.delete'
  | 'calculation-engine.view'
  | 'diagram.create'
  | 'diagram.update'
  | 'diagram.delete'
  | 'diagram.view'
  | 'asset-template.clone'
  | 'asset-template.create'
  | 'asset-template.update'
  | 'asset-template.delete'
  | 'asset-template.view'
  | 'asset-instance.create'
  | 'asset-instance.update'
  | 'asset-instance.delete'
  | 'asset-instance.view'
  | 'asset.types.view'
  | 'dashboard.create'
  | 'dashboard.update'
  | 'dashboard.delete'
  | 'dashboard.view'
  | 'unit.measure.view';
export const userDashboardPermissions = ['dashboard.view', 'measurement.view', 'asset.view'];
export const powerUserDashboardPermissions = [
  ...userDashboardPermissions,
  'dashboard.update',
  'dashboard.delete',
  'dashboard.create',
  'widget.create',
  'measurement.create',
  'measurement.update',
  'measurement.delete',
  'measurement.selection',
  'asset.create',
  'asset.update',
  'asset.delete',
  'alert.create',
  'alert.update',
  'alert.delete',
  'alert.view',
];
export const adminDashboardPermissions = [
  ...powerUserDashboardPermissions,
  'dashboard.update',
  'dashboard.delete',
  'dashboard.create',
  'alert.create',
  'widget.create',
  'measurement.create',
  'measurement.update',
  'measurement.delete',
  'asset.create',
  'asset.update',
  'asset.delete',
];
export const superAdminDashboardPermissions = [
  ...adminDashboardPermissions,
  'dashboard.update',
  'dashboard.delete',
  'dashboard.create',
  'alert.create',
  'widget.create',
  'measurement.create',
  'measurement.update',
  'measurement.delete',
  'asset.create',
  'asset.update',
  'asset.delete',
];
export const userWiseDashboardPermissions: { [key in Role]: string[] } = {
  [Role.SUPER_ADMIN]: superAdminDashboardPermissions,
  [Role.ADMIN]: adminDashboardPermissions,
  [Role.POWER_USER]: powerUserDashboardPermissions,
  [Role.USER]: userDashboardPermissions,
};
export type dashboardPermissions =
  | 'customer.create'
  | 'customer.update'
  | 'dashboard.update'
  | 'dashboard.delete'
  | 'dashboard.create'
  | 'dashboard.view'
  | 'alert.create'
  | 'widget.create'
  | 'measurement.create'
  | 'measurement.update'
  | 'measurement.delete'
  | 'measurement.selection'
  | 'asset.create'
  | 'asset.update'
  | 'asset.delete'
  | 'asset.view'
  | 'measurement.view'
  | 'alert.create'
  | 'alert.update'
  | 'alert.delete'
  | 'alert.view'
  | 'dashboard-template.view'
  | 'dashboard-template.create'
  | 'dashboard-template.update'
  | 'dashboard-template.delete';

export type PermissionName = UserPermissionName;
export type DashboardPermissionName = dashboardPermissions;
export const useRolePermission = () => {
  const userToken = useSelector(getUserToken);
  const activeCustomer = useSelector(getActiveCustomer);
  const [userCustomerRoles, setUserCustomerRoles] = useState<UserToken['roles']>({
    SUPER_ADMIN: [],
    ADMIN: [],
    USER: [],
    POWER_USER: [],
  });
  useEffect(() => {
    if (userToken) {
      const decodedToken = jwtDecode(userToken) as UserToken;
      const SUPER_ADMIN = decodedToken.roles.SUPER_ADMIN ?? [];
      const ADMIN = decodedToken.roles.ADMIN ?? [];
      const USER = decodedToken.roles.USER ?? [];
      const POWER_USER = decodedToken.roles.POWER_USER ?? [];
      setUserCustomerRoles({
        SUPER_ADMIN,
        ADMIN,
        USER,
        POWER_USER,
      });
    }
  }, [userToken]);

  const hasPermission = (permission: PermissionName) => {
    if (!userToken) return false;
    let role = userCustomerRoles.SUPER_ADMIN.length > 0 ? Role.SUPER_ADMIN : Role.ADMIN;
    if (userCustomerRoles.ADMIN.length > 0) {
      role = Role.ADMIN;
    } else if (userCustomerRoles.USER.length > 0) {
      role = Role.USER;
    } else if (userCustomerRoles.POWER_USER.length > 0) {
      role = Role.POWER_USER;
    } else {
      role = Role.SUPER_ADMIN;
    }
    // return permissions[permission].includes(role);
    return userWisePermissions[role].includes(permission);
  };
  const hasDashboardPermission = (
    permission: DashboardPermissionName,
    requiredRole: Role = Role.SUPER_ADMIN,
  ) => {
    if (!userToken) return false;
    let hasRole = false;
    switch (requiredRole) {
      case Role.USER:
        hasRole =
          userCustomerRoles.USER.includes(activeCustomer?.id ?? 0) ||
          userCustomerRoles.POWER_USER.includes(activeCustomer?.id ?? 0) ||
          userCustomerRoles.ADMIN.includes(activeCustomer?.id ?? 0);
        break;
      case Role.POWER_USER:
        hasRole =
          userCustomerRoles.POWER_USER.includes(activeCustomer?.id ?? 0) ||
          userCustomerRoles.ADMIN.includes(activeCustomer?.id ?? 0);
        break;
      case Role.ADMIN:
        hasRole = userCustomerRoles.ADMIN.includes(activeCustomer?.id ?? 0);
        break;
      default:
        hasRole = false;
    }
    // return hasRole && dashboardPermissions[permission].includes(requiredRole);
    return hasRole && userWiseDashboardPermissions[requiredRole].includes(permission);
  };
  return { hasPermission, hasDashboardPermission };
};
