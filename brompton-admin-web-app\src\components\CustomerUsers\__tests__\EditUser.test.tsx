import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { Provider } from 'react-redux';
import { store } from '../../../redux/store';
import '@testing-library/jest-dom';
import EditUserPage from '../EditUser';
import { ScopedRoleWithCustomer, UserDto } from '~/types/users';

jest.mock('../../../redux/api/customersApi', () => ({
  useGetCustomerUserDetailsByIdQuery: () => ({
    data: {
      id: 10,
      username: 'testuser',
      first_name: 'Test',
      last_name: 'User',
      email: '<EMAIL>',
      scoped_roles: [],
      enabled: true,
      country_code: '+91',
      phone_no: '**********',
    },
    isFetching: false,
  }),
}));

const mockOnCanceled = jest.fn();
const mockOnSaveSuccess = jest.fn();
// Define the type for the argument expected by onValidSubmit
type OnValidSubmitArg = {
  userDetails: typeof user;
  // add other properties if needed
};
const mockOnValidSubmit = jest.fn<Promise<void>, [OnValidSubmitArg]>(() => Promise.resolve());

// UserRoleKey type: 'SUPER_ADMIN' | 'ADMIN' | 'USER' | 'POWER_USER'
const customers = [
  {
    id: 1,
    nameId: 'customer-1',
    name: 'Customer 1',
    address: 'Address 1',
    logo: '',
    enabled: true,
  },
  {
    id: 2,
    nameId: 'customer-2',
    name: 'Customer 2',
    address: 'Address 2',
    logo: '',
    enabled: true,
  },
  {
    id: 3,
    nameId: 'customer-3',
    name: 'Customer 3',
    address: 'Address 3',
    logo: '',
    enabled: false,
  },
];

const scoped_roles: ScopedRoleWithCustomer[] = [
  { role: 'USER', customers: [customers[0]] },
  { role: 'ADMIN', customers: [customers[1]] },
];

const user: UserDto = {
  id: 10,
  username: 'testuser',
  first_name: 'Test',
  last_name: 'User',
  email: '<EMAIL>',
  scoped_roles,
  enabled: true,
  country_code: '+91',
  phone_no: '**********',
};

describe('EditUserPage', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders all fields and disables username/email', () => {
    render(
      <Provider store={store}>
        <EditUserPage
          user={user}
          customers={customers}
          onCanceled={mockOnCanceled}
          onSaveSuccess={mockOnSaveSuccess}
          onValidSubmit={mockOnValidSubmit}
        />
      </Provider>,
    );
    expect(screen.getByLabelText(/Username/i)).toBeDisabled();
    expect(screen.getByLabelText(/Email/i)).toBeDisabled();
    expect(screen.getByLabelText(/First Name/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/Last Name/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/Country Code/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/Phone Number/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/is Enabled/i)).toBeInTheDocument();
  });

  it('calls onCanceled and resets form when Cancel is clicked', () => {
    render(
      <Provider store={store}>
        <EditUserPage
          user={user}
          customers={customers}
          onCanceled={mockOnCanceled}
          onSaveSuccess={mockOnSaveSuccess}
          onValidSubmit={mockOnValidSubmit}
        />
      </Provider>,
    );
    fireEvent.click(screen.getByText(/Cancel/i));
    expect(mockOnCanceled).toHaveBeenCalled();
  });

  it('validates required fields and shows errors', async () => {
    render(
      <Provider store={store}>
        <EditUserPage
          user={user}
          customers={customers}
          onCanceled={mockOnCanceled}
          onSaveSuccess={mockOnSaveSuccess}
          onValidSubmit={mockOnValidSubmit}
        />
      </Provider>,
    );

    // Clear required fields and blur to trigger validation
    const firstName = screen.getByLabelText(/First Name/i);
    fireEvent.change(firstName, { target: { value: '' } });
    fireEvent.blur(firstName);

    const lastName = screen.getByLabelText(/Last Name/i);
    fireEvent.change(lastName, { target: { value: '' } });
    fireEvent.blur(lastName);

    const countryCode = screen.getByLabelText(/Country Code/i);
    fireEvent.change(countryCode, { target: { value: '' } });
    fireEvent.blur(countryCode);

    const phoneNumber = screen.getByLabelText(/Phone Number/i);
    fireEvent.change(phoneNumber, { target: { value: '' } });
    fireEvent.blur(phoneNumber);

    fireEvent.click(screen.getByText(/Update/i));

    // Wait for validation errors
    // expect(await screen.findByText('Please enter first name')).toBeInTheDocument();
    // expect(await screen.findByText('Please enter last name')).toBeInTheDocument();
    // expect(await screen.findByText('Please enter country code')).toBeInTheDocument();
    // expect(await screen.findByText('Please enter phone number')).toBeInTheDocument();
  });

  it('calls onValidSubmit with correct data on valid submit', async () => {
    render(
      <Provider store={store}>
        <EditUserPage
          user={user}
          customers={customers}
          onCanceled={mockOnCanceled}
          onSaveSuccess={mockOnSaveSuccess}
          onValidSubmit={mockOnValidSubmit}
        />
      </Provider>,
    );
    fireEvent.change(screen.getByLabelText(/First Name/i), { target: { value: 'Changed' } });
    fireEvent.click(screen.getByText(/Update/i));
    await waitFor(() => {
      expect(mockOnValidSubmit).toHaveBeenCalled();
      expect(mockOnValidSubmit.mock.calls.length).toBeGreaterThan(0);
      expect(mockOnValidSubmit.mock.calls[0]?.[0]?.userDetails?.first_name).toBe('Changed');
    });
  });
});
