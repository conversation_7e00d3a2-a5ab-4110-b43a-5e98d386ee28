import { Data, Layout } from 'plotly.js';
import { useEffect, useRef, useState } from 'react';
import { useSelector } from 'react-redux';
import { UnitOfMeasure } from '~/measurements/domain/types';
import { getAssetTz } from '~/redux/selectors/topPanleSelectors';
import { getDateTimeFormatForChart, getThousandSeparator } from '~/redux/selectors/userPreferences';
import { AssetMeasurementDetails } from '~/types/measures';
import {
  AssetMeasurementDetailsWithLastFetchTime,
  SingleScatterTimeSeriesData,
} from '~/types/timeseries';
import { ChartMeasureSetting, KPISparkline } from '~/types/widgets';
import {
  calulationXaxisValue,
  calulationYaxisValue,
  formatMetricLabel,
  formatMetricTag,
  formatNumber,
  roundNumber,
} from '~/utils/utils';
import { useGetMeasuresTsData } from './useGetMeasuresTsData';

type Stats = {
  lastValue?: string;
};

type ChartData = {
  data: Data[];
  removedResults: AssetMeasurementDetailsWithLastFetchTime[];
  layout: Partial<Layout>;
  stats: Stats;
  uom: string;
};

type TrendResult = {
  isError: boolean;
  error?: string;
  lastFetchTime: number;
  tsData: SingleScatterTimeSeriesData;
  measureData: AssetMeasurementDetails;
  unitOfMeasures: UnitOfMeasure[];
};

type SingleChartData = {
  layout: Partial<Layout>;
  stats: Stats;
};

function transformScatterDataForPlotly(
  results: TrendResult[],
  dbMeasureIdToSetting: Record<string, ChartMeasureSetting>,
  selectedSamplePeriod: number,
  showArea: boolean,
  titleInfo: {
    value: string;
    isVisible: boolean;
    color: string;
    fontSize?: number;
    fontWeight?: string;
  },
  sparkLineColor: string,
  dateFormats: string,
  useAssetTz: boolean,
  thousandSeparator: boolean,
): ChartData {
  const traces: Data[] = [];
  const layout: Partial<Layout> = {
    showlegend: true,
    titlefont: {
      size: titleInfo.fontSize,
      color: titleInfo.color,
    },
    annotations: [],
    yaxis: {
      side: 'left',
    },
    yaxis2: {
      side: 'right',
      overlaying: 'y',
    },
  };

  let chartNumber = 1;
  const filteredResults = results.filter((result) => result && result.tsData);
  const removedResults = results.filter(
    (result) => !result || !result.tsData || result.isError || result.error,
  );
  let uom = '';
  for (let i = 0; i < filteredResults.length; i++) {
    const result = filteredResults[i];

    if (result && result.tsData) {
      const seriesData = result.tsData;
      const measureData = result.measureData;
      const unitOfMeasures = result.unitOfMeasures;
      const values = seriesData['ts,val'];
      if (!values) continue;
      const x = calulationXaxisValue(seriesData, useAssetTz);
      const y = calulationYaxisValue(seriesData);

      const unitsOfMeasure = unitOfMeasures.find(
        (data) => data.id === measureData.unitOfMeasureId,
      ) || { name: '', id: 0 };

      const title = formatMetricLabel(measureData.tag);
      uom = unitsOfMeasure.name ?? '';
      traces.push({
        type: 'scatter',
        fill: 'tozeroy',
        marker: { color: sparkLineColor },
        x,
        y: y.map((value) => (thousandSeparator ? formatNumber(value) : roundNumber(value))),
        hovertemplate: `${title}: %{y} ${unitsOfMeasure.name}<br>Time: %{x}<extra></extra>'`,
        name: `${formatMetricTag(measureData.tag)} (${unitsOfMeasure.name})`,
        yaxis: dbMeasureIdToSetting[result.measureData.id]?.yAxisSide === 'right' ? 'y2' : 'y',
        mode: 'lines',
      });

      chartNumber++;
    }
  }
  if (results[0]?.tsData?.['ts,val']?.slice(-1)?.length === 0) {
    return {
      data: [],
      layout: { ...layout },
      stats: { lastValue: '-' },
      uom: '',
      removedResults: removedResults.map((res) => {
        return {
          ...res.measureData,
          lastFetchTime: res.lastFetchTime,
          partialFailed: removedResults.length !== results.length,
        };
      }),
    };
  }
  const singleChartData: SingleChartData = {
    stats: { lastValue: results[0]?.tsData?.['ts,val']?.slice(-1)[0][1].toString() },
    layout: {
      annotations: [],
      shapes: [],
      margin: { b: 0, l: 15, r: 15, t: 0, pad: 0 },
    },
  };
  layout.showlegend = false;
  singleChartData.layout.showlegend = false;
  layout.xaxis = {
    showline: false,
    showgrid: false,
    showticklabels: false,
    autotick: false,
  };
  singleChartData.layout.xaxis = {
    showline: false,
    showgrid: false,
    showticklabels: false,
    autotick: false,
  };
  singleChartData.layout.yaxis = {
    showline: false,
    showgrid: false,
    showticklabels: false,
    autotick: false,
  };
  layout.yaxis = {
    showgrid: false,
    zeroline: false,
    showline: false,
    showticklabels: false,
  };
  layout.annotations = [];
  layout.shapes = [];
  singleChartData.layout.annotations = [];
  singleChartData.layout.shapes = [];

  return {
    data: traces,
    layout: { ...layout, ...singleChartData.layout },
    stats: { ...singleChartData.stats },
    removedResults: removedResults.map((res) => {
      return {
        ...res.measureData,
        lastFetchTime: res.lastFetchTime,
        partialFailed: removedResults.length !== results.length,
      };
    }),
    uom,
  };
}

export function useFetchKPISparkLine(widgetId: string, state: KPISparkline) {
  const selectedSamplePeriod = state.samplePeriod || 0;
  const { dbMeasureIdToSetting } = state;
  const dateFormats = useSelector(getDateTimeFormatForChart);
  const thousandSeparator = useSelector(getThousandSeparator);
  const prevResultsRef = useRef<TrendResult[]>([]);
  const [allDataFetched, setAllDataFetched] = useState<{
    chartData: Data[];
    isLoading: boolean;
    isError: boolean;
    stats: Stats;
    uom: string;
    layoutData: Partial<Layout>;
    removedResults: AssetMeasurementDetailsWithLastFetchTime[];
  }>({
    chartData: [] as Data[],
    isLoading: true,
    isError: false,
    uom: '',
    stats: {
      min: '',
      max: '',
      avg: '',
    } as Stats,
    layoutData: {
      showlegend: true,
      title: 'Chart',
    } as Partial<Layout>,
    removedResults: [],
  });
  const [chartResults, setChartResults] = useState<TrendResult[] | undefined>(undefined);
  const useAssetTz = useSelector(getAssetTz);
  const [selectedTitles, setSelectedTitles] = useState<string[]>([]);
  useEffect(() => {
    if (
      state.mode === 'dashboard' &&
      state.assetMeasure.assetId !== '' &&
      state.assetMeasure.measureId.some((measure) => measure !== '')
    ) {
      setSelectedTitles(state.assetMeasure.measureId);
    }
    if (state.mode === 'template') {
      if (state.selectedDbMeasureId !== '') {
        setSelectedTitles([state.selectedDbMeasureId]);
      } else {
        setSelectedTitles([]);
      }
    }
  }, [state.assetMeasure, state.selectedDbMeasureId, state.mode]);
  const {
    data: measureData,
    isLoading: isMeasureDataLoading,
    successAndFailedMeasurements,
  } = useGetMeasuresTsData({
    selectedTitles,
    dataFetchSettings: state,
    assetMeasure: [state.assetMeasure],
  });
  useEffect(() => {
    if (isMeasureDataLoading) {
      setChartResults(undefined);
      setAllDataFetched((prev) => {
        return {
          ...prev,
          isLoading: true,
          isError: false,
        };
      });
    } else if (measureData) {
      const updated: TrendResult[] = [];
      (measureData || []).forEach((result) => {
        if (!result.isError && result.tsData) {
          updated.push(result);
        } else {
          const existing = prevResultsRef.current.find(
            (r) => r.measureData.measurementId === result?.measureData?.measurementId,
          );
          if (existing) {
            updated.push({
              ...existing,
              lastFetchTime: result.lastFetchTime,
              isError: result.isError,
              error: result.error,
              tsData: {
                ...existing.tsData,
                error: result.error,
              },
            });
          } else {
            updated.push(result);
          }
        }
      });
      prevResultsRef.current = updated; // Keep latest working state
      setChartResults(updated as TrendResult[]);
    } else {
      setChartResults(undefined);
      setAllDataFetched((prev) => {
        return {
          ...prev,
          isError: true,
          isLoading: false,
        };
      });
    }
  }, [isMeasureDataLoading, measureData]);

  useEffect(() => {
    if (chartResults) {
      const chartData = transformScatterDataForPlotly(
        chartResults,
        dbMeasureIdToSetting,
        selectedSamplePeriod,
        false,
        state.title,
        state.sparkLineColor,
        dateFormats,
        useAssetTz,
        thousandSeparator,
      );
      setAllDataFetched({
        chartData: chartData.data,
        isLoading: false,
        isError: false,
        removedResults: chartData.removedResults,
        layoutData: chartData.layout,
        stats: chartData.stats,
        uom: chartData.uom,
      });
    }
  }, [
    state.showSparkLine,
    state.sparkLineColor,
    chartResults,
    state.title.value,
    state.title.isVisible,
    state.title.fontSize,
    state.title.fontWeight,
    state.title.color,
    selectedSamplePeriod,
    dbMeasureIdToSetting,
    state.overrideGlobalSettings,
    state.startDate,
    state.endDate,
    state.timeRange,
    state.globalSamplePeriod,
    state.title,
    dateFormats,
    thousandSeparator,
  ]);
  return { ...allDataFetched, successAndFailedMeasurements };
}
