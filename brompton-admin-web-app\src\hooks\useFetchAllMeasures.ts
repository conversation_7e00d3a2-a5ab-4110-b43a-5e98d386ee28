import { measuresApi } from '~/redux/api/measuresApi';
import { useDispatch } from 'react-redux';
import { useEffect, useState } from 'react';
import { ThunkDispatch } from 'redux-thunk';
import { RootState } from '~/redux/store';
import { AssetIdWithMeasurements } from '~/measurements/domain/types';

export function useFetchAllMeasures(customerId: number, assetIds: number[]) {
  const dispatch = useDispatch<ThunkDispatch<RootState, any, any>>();
  const [measurementList, setMeasurementList] = useState<AssetIdWithMeasurements[]>([]);

  useEffect(() => {
    const fetchData = async () => {
      if (assetIds.length !== 0) {
        const promise = assetIds
          .filter((assetId) => assetId !== -1)
          .map(async (assetId) => {
            return dispatch(
              measuresApi.endpoints.getAllMeasurements.initiate({ customerId, assetId }),
            ).then(
              ({ data }) => {
                return { assetId, measurements: data } as AssetIdWithMeasurements;
              },
              () => {
                return undefined;
              },
            );
          });
        const results = await Promise.all(promise);
        const r = results.filter(
          (measurement): measurement is AssetIdWithMeasurements => measurement !== undefined,
        );

        setMeasurementList(r);
      }
    };

    fetchData();
  }, [assetIds, customerId, dispatch]);

  return measurementList;
}
