import { dia } from '@joint/core';
import { ThunkDispatch } from '@reduxjs/toolkit';
import { isEqual } from 'lodash';
import { MutableRefObject, useCallback, useEffect, useRef, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { UnitOfMeasure } from '~/measurements/domain/types';
import { measuresApi } from '~/redux/api/measuresApi';
import { timeseriesApi } from '~/redux/api/timeseriesApi';
import { getCustomerId } from '~/redux/selectors/customerSelectors';
import {
  getAssetTz,
  getGlobalSamplePeriod,
  getGlobalTimeRangeType,
} from '~/redux/selectors/topPanleSelectors';
import { getDbMeasureIdToAssetIdMap } from '~/redux/selectors/treeSelectors';
import { getGlobalEndDate, getGlobalStartDate } from '~/redux/selectors/widgetSelectors';
import { dashboardSlice } from '~/redux/slices/dashboardSlice';
import { RootState } from '~/redux/store';
import { AggByOptions, SamplePeriodOptions } from '~/types/dashboard';
import { cellNamespace, elementVariable } from '~/types/diagram';
import { AssetMeasurementDetails, measurementsUnitsDTO } from '~/types/measures';
import {
  AssetMeasurementDetailsWithLastFetchAndSucess,
  SingleScatterTimeSeriesData,
} from '~/types/timeseries';
import { DiagramWidget } from '~/types/widgets';
import { formatNumber, roundNumber } from '~/utils/utils';
type AssetMeasurementDetailsWithLastFetchAndSucessWithAgg =
  AssetMeasurementDetailsWithLastFetchAndSucess & {
    aggBy?: number;
  };
type MeasuresData = {
  isLoading: boolean;
  isError: boolean;
  error?: string;
  lastFetchTime?: number;
  tsData: SingleScatterTimeSeriesData;
  measureData: AssetMeasurementDetails;
  aggBy: number;
  samplePeriod: number;
  timeRange: number;
  startDate: number;
  endDate: number;
  assetTz: boolean;
  globalSamplePeriod: boolean;
  unitOfMeasures: UnitOfMeasure[];
};

function getDiagramData({
  filteredResults,
  graphRef,
  selectedTitles,
}: {
  filteredResults: MeasuresData[];
  graphRef: MutableRefObject<dia.Graph<dia.Graph.Attributes, dia.ModelSetOptions> | null>;
  selectedTitles: string[];
}) {
  for (let i = 0; i < filteredResults.length; i++) {
    const tsData = filteredResults[i].tsData;
    if (!tsData) continue;
    if (selectedTitles.includes(filteredResults[i].measureData.id.toString()) === false) {
      continue;
    }
    const yValues = tsData?.['ts,val']?.map((value) => value[1]);
    const lastValue = yValues.length > 0 ? roundNumber(yValues?.slice(-1).pop() ?? 0) || '-' : '-';
    graphRef.current?.getCells().forEach((cell) => {
      const variables = cell?.get('data')?.variables ?? [];
      variables.forEach((variable: elementVariable, index: number) => {
        if (variable.measurementId === filteredResults[i]?.measureData.id.toString()) {
          variable.value = formatNumber(lastValue);
          cell.prop(
            `data/variables/${index}/value`,
            lastValue === '-' ? '--' : `${formatNumber(lastValue)} ${tsData?.tag_meta?.uom ?? ''}`,
          );
          cell.prop(`data/variables/${index}/uom`, tsData?.tag_meta?.uom);
        }
      });
    });
  }
}
const useDiagramWidgetData = (id: string, settings: DiagramWidget) => {
  const dispatch = useDispatch<ThunkDispatch<RootState, any, any>>();
  const customerId = useSelector(getCustomerId);
  const globalStartDate = useSelector(getGlobalStartDate);
  const globalEndDate = useSelector(getGlobalEndDate);
  const dbMeasureIdToAssetIdMap = useSelector(getDbMeasureIdToAssetIdMap);
  const globalSamplePeriod = useSelector(getGlobalSamplePeriod);
  const timeRangeType = useSelector(getGlobalTimeRangeType);
  const assetTz = useSelector(getAssetTz);
  const [assetMeasure, setAssetMeasure] = useState<{ assetId: string; measureId: string }[]>([]);
  const [assetMeasureVariables, setAssetMeasureVariables] = useState<elementVariable[]>([]);
  const [successAndFailedMeasurements, setSuccessAndFailedMeasurements] = useState<
    AssetMeasurementDetailsWithLastFetchAndSucessWithAgg[]
  >([]);
  const [selectedTitles, setSelectedTitles] = useState<string[]>([]);
  const graphRef = useRef<dia.Graph | null>(null);
  const previousGraphJSON = useRef<string | null>(null);
  const [startDate, setStartDate] = useState(globalStartDate);
  const [endDate, setEndDate] = useState(globalEndDate);
  const [timeRange, setTimeRange] = useState(timeRangeType);

  const [samplePeriod, setSamplePeriod] = useState(globalSamplePeriod);
  const { isRealTime, retainPeriod, refreshInterval } = settings;
  const [state, setState] = useState<{
    data: undefined | MeasuresData[];
    isLoading: boolean;
    isError: boolean;
  }>({
    data: undefined,
    isLoading: true,
    isError: false,
  });
  const {
    startDate: selectedStartDate,
    endDate: selectedEndDate,
    samplePeriod: selectedSamplePeriod,
    timeRange: selectedTimeRange,
    globalSamplePeriod: isGlobalSamplePeriodOverridden,
    overrideGlobalSettings: isGlobalTimeRangeSettingsOverridden,
    overrideAssetTz: isOverrideAssetTz,
    isRelativeToGlboalEndTime,
    overrideAssetTzValue,
  } = settings;

  const [assetTzOverride, setAssetTzOverride] = useState(isOverrideAssetTz);
  const [assetTzOverrideValue, setAssetTzOverrideValue] = useState(overrideAssetTzValue);
  useEffect(() => {
    if (isGlobalTimeRangeSettingsOverridden) {
      setStartDate(selectedStartDate);
      setEndDate(selectedEndDate);
      setTimeRange(selectedTimeRange);
    } else {
      setStartDate(globalStartDate);
      setEndDate(globalEndDate);
      setTimeRange(timeRangeType);
    }

    if (isGlobalSamplePeriodOverridden) {
      setSamplePeriod(selectedSamplePeriod);
    } else {
      setSamplePeriod(globalSamplePeriod);
    }
    if (isOverrideAssetTz) {
      setAssetTzOverride(true);
      setAssetTzOverrideValue(overrideAssetTzValue);
    } else {
      setAssetTzOverride(false);
    }
  }, [
    globalEndDate,
    globalSamplePeriod,
    globalStartDate,
    timeRangeType,
    isGlobalSamplePeriodOverridden,
    isGlobalTimeRangeSettingsOverridden,
    isRelativeToGlboalEndTime,
    selectedEndDate,
    selectedSamplePeriod,
    selectedStartDate,
    selectedTimeRange,
    isOverrideAssetTz,
    overrideAssetTzValue,
  ]);
  const retainPeriodRef = useRef(retainPeriod);

  const applyRetention = (
    tsData: Record<number, SingleScatterTimeSeriesData>,
    retentionMinutes: number,
  ): Record<number, SingleScatterTimeSeriesData> => {
    // const retentionTime = Date.now() - retentionMinutes * 60000; // Calculate retention boundary
    const browserOffsetMinutes = new Date().getTimezoneOffset(); // Get browser's timezone offset in minutes
    const browserOffsetMs = browserOffsetMinutes * 60000; // Convert to milliseconds
    const retentionTime = Date.now() - retentionMinutes * 60000 - browserOffsetMs; // Adjusted retention boundary
    return Object.fromEntries(
      Object.entries(tsData)
        .filter(([, series]) => series.error === undefined) // Filter out errored series
        .map(([key, series]) => {
          const filteredPoints = series['ts,val'].filter(
            ([timestamp]) => timestamp >= retentionTime,
          );
          return [
            key,
            {
              ...series,
              'ts,val': filteredPoints,
            },
          ];
        }),
    );
  };

  useEffect(() => {
    retainPeriodRef.current = retainPeriod;
  }, [retainPeriod]);
  useEffect(() => {
    if (!graphRef.current) {
      const graph = new dia.Graph(
        {},
        {
          cellNamespace: cellNamespace,
        },
      );
      graphRef.current = graph;
    }
  }, []);
  useEffect(() => {
    if (settings.jsonFile !== '' && graphRef.current) {
      const parsedContent = JSON.parse(settings.jsonFile)?.graph;
      if (parsedContent) {
        graphRef.current?.fromJSON(parsedContent);
        const titles = graphRef.current
          ?.getCells()
          .filter((cell) => cell.get('type') !== 'Pipe')
          .flatMap((cell) => {
            const variables = (cell.get('data')?.variables ?? []) as elementVariable[];
            return variables.map((variable) => variable.measurementId);
          });
        const uniqueTitles: string[] = Array.from(new Set(titles));
        const assetWithMeasure = graphRef.current
          ?.getCells()
          .filter((cell) => cell.get('type') !== 'Pipe')
          .flatMap((cell) => {
            const variables = (cell.get('data')?.variables ?? []) as elementVariable[];
            return variables.map((variable) => {
              return variable;
            });
          })
          .filter(
            (assetMeasure) => assetMeasure.assetId !== '' && assetMeasure.measurementId !== '',
          );
        setAssetMeasure(
          assetWithMeasure.map((variable) => {
            return {
              assetId: variable.assetId,
              measureId: variable.measurementId,
            };
          }),
        );
        setAssetMeasureVariables(assetWithMeasure);
        if (assetWithMeasure.length === 0) {
          setState({
            data: undefined,
            isError: false,
            isLoading: false,
          });
        }
        setSelectedTitles(uniqueTitles);
      }
    }
  }, [settings.jsonFile, settings.elementIdVariabels, settings.elementVariable]);

  const groupAssetMeasures = (assetMeasures: Array<{ assetId: string; measureId: string }>) => {
    return assetMeasures.reduce<Record<number, number[]>>((acc, cur) => {
      const asset = Number(cur.assetId);
      const measure = Number(cur.measureId);
      if (!acc[asset]) acc[asset] = [];
      acc[asset].push(measure);
      return acc;
    }, {});
  };

  const fetchAllAssetMeasures = useCallback(
    async (
      customerId: number,
      assetsWithMeasures: { asset_id: number; measurement_ids: number[] }[],
    ): Promise<measurementsUnitsDTO> => {
      if (!assetsWithMeasures || assetsWithMeasures.length === 0) {
        return { items: [], total: 0 };
      }

      const result = await dispatch(
        measuresApi.endpoints.getMeasuresWithAssetMeasures.initiate({
          customerId,
          data: assetsWithMeasures,
        }),
      );

      const { error, isError, data } = result;
      if (isError || error || !data) {
        console.error('Error fetching batch measures', error);
        return { items: [], total: 0 };
      }

      return data as measurementsUnitsDTO;
    },
    [customerId, dispatch],
  );

  const fetchHistoryTimeseriesData = useCallback(
    async (
      tsDbMeasureIds: number[],
    ): Promise<{ error: boolean; tsData: Record<number, SingleScatterTimeSeriesData> }> => {
      if (tsDbMeasureIds.length === 0) {
        return { error: true, tsData: {} };
      }
      const endDate = new Date();
      const startDate = new Date(endDate.getTime() - retainPeriodRef.current * 60000);

      const { data: tsData, isSuccess: isTsSuccess } = await dispatch(
        timeseriesApi.endpoints.getMultipleHistoryMeasurementSeries.initiate({
          customerId,
          measId: tsDbMeasureIds.filter(Boolean).sort().join(','),
          start: startDate.getTime(),
          end: endDate.getTime(),
          assetTz: false,
        }),
      );

      if (!isTsSuccess || !tsData) {
        return { error: true, tsData: {} };
      }

      // Apply retention logic
      const filteredTsData = applyRetention(tsData, retainPeriodRef.current);

      return { error: false, tsData: filteredTsData };
    },
    [dispatch, customerId],
  );
  const fetchTimeseriesDataForDifferent = useCallback(
    async (
      tsDbMeasureIds: number,
      startDate: number,
      endDate: number,
      aggBy: number,
      assetTz: boolean,
      timeRangeType: number,
    ): Promise<{ error: boolean; tsData: Record<number, SingleScatterTimeSeriesData> }> => {
      if (tsDbMeasureIds === 0) {
        return { error: true, tsData: {} };
      }
      const { data: tsData, isSuccess: isTsSuccess } = await dispatch(
        timeseriesApi.endpoints.getMultiMeasurementSeries.initiate(
          {
            customerId,
            measId: tsDbMeasureIds?.toString(),
            start: settings.overrideGlobalSettings ? settings.startDate : startDate,
            end: settings.overrideGlobalSettings ? settings.endDate : endDate,
            agg: AggByOptions[aggBy].serverValue,
            agg_period: settings.globalSamplePeriod
              ? SamplePeriodOptions[selectedSamplePeriod].serverValue
              : SamplePeriodOptions[globalSamplePeriod].serverValue,
            timeRangeType: timeRangeType,
            assetTz: settings.overrideAssetTzValue ? settings.overrideAssetTz ?? false : assetTz,
          },
          {
            // forceRefetch: true,
          },
        ),
      );

      if (!isTsSuccess || !tsData) {
        return {
          error: true,
          tsData: {},
        };
      }

      return { error: false, tsData };
    },
    [
      dispatch,
      customerId,
      settings.globalSamplePeriod,
      settings.samplePeriod,
      settings.timeRange,
      settings.overrideGlobalSettings,
      settings.overrideAssetTzValue,
      settings.overrideAssetTz,
      settings.isRelativeToGlboalEndTime,
      globalSamplePeriod,
      selectedSamplePeriod,
      timeRangeType,
      assetTz,
      globalStartDate,
      globalEndDate,
      isRelativeToGlboalEndTime,
    ],
  );
  useEffect(() => {
    const fetchData = async () => {
      if (assetMeasureVariables.length > 0) {
        setState({ data: undefined, isLoading: false, isError: false });
        try {
          const timeRangeSelection = settings.overrideGlobalSettings
            ? settings.timeRange
            : timeRangeType;
          const startDateSelection = settings.overrideGlobalSettings
            ? settings.startDate
            : globalStartDate;
          const endDateSelection = settings.overrideGlobalSettings
            ? settings.endDate
            : globalEndDate;
          const assetTzSelection = settings.overrideAssetTzValue
            ? settings.overrideAssetTz ?? false
            : assetTz;

          const grouped = groupAssetMeasures(assetMeasure);
          const batched = await fetchAllAssetMeasures(
            customerId,
            Object.entries(grouped).map(([asset_id, measurement_ids]) => ({
              asset_id: Number(asset_id),
              measurement_ids,
            })),
          );

          const batchedResults = batched.items.map((measureData) => {
            const assetMeasureId = measureData.id;
            const measurementId = measureData.measurement_id;
            const matchedVariable = assetMeasureVariables.find(
              (v) => v.measurementId === assetMeasureId.toString(),
            );

            return {
              isLoading: false,
              isError: false,
              error: '',
              lastFetchTime: Date.now(),
              measureData: {
                ...measureData,
                id: assetMeasureId,
                measurementId: measurementId, // this is what TS API needs
                metricId: measureData.metricId ?? null,
                tag: measureData.tag ?? '',
              },
              unitOfMeasures: measureData.unitOfMeasure ? [measureData.unitOfMeasure] : [],
              aggBy: matchedVariable?.aggBy ?? settings.aggBy ?? 1,
              samplePeriod,
              timeRange,
              startDate,
              endDate,
              globalSamplePeriod: !!globalSamplePeriod,
              assetTz: assetTzSelection,
              tsData: {
                tag: measurementId,
                period: '',
                'ts,val': [],
                tag_meta: { uom: measureData.unitOfMeasure?.name ?? '' },
              },
            } as MeasuresData;
          });

          const results = batchedResults.filter((result) => result.measureData);
          const tsResults = await Promise.all(
            results.map(async (res) => {
              const measurementId = res.measureData?.measurementId;
              if (!measurementId) {
                return Promise.resolve(undefined);
              }
              const { error, tsData } = await fetchTimeseriesDataForDifferent(
                measurementId, // single ID, not array
                startDateSelection,
                endDateSelection,
                res.aggBy,
                assetTzSelection,
                timeRangeSelection,
              );
              if (
                error ||
                !tsData ||
                !tsData[measurementId] ||
                tsData[measurementId].error ||
                !tsData[measurementId]['ts,val']
              ) {
                return {
                  isLoading: false,
                  isError: true,
                  error: 'Timeseries error',
                  assetTz: assetTzSelection,
                  tsData: undefined as unknown as SingleScatterTimeSeriesData,
                  measureData: res.measureData,
                  aggBy: res.aggBy,
                  samplePeriod: res.samplePeriod,
                  timeRange: timeRangeSelection,
                  startDate: startDateSelection,
                  endDate: endDateSelection,
                  globalSamplePeriod: res.globalSamplePeriod,
                  unitOfMeasures: res.unitOfMeasures || [],
                } as MeasuresData;
              }
              return {
                isLoading: false,
                isError: false,
                error: '',
                lastFetchTime: Date.now(),
                tsData: tsData[measurementId],
                measureData: res.measureData,
                aggBy: res.aggBy,
                samplePeriod: res.samplePeriod,
                timeRange: timeRangeSelection,
                startDate: startDateSelection,
                endDate: endDateSelection,
                globalSamplePeriod: res.globalSamplePeriod,
                unitOfMeasures: res.unitOfMeasures || [],
              } as MeasuresData;
            }),
          );
          const measuresDataArray: MeasuresData[] = tsResults.filter((d): d is MeasuresData => !!d);
          setState({
            data: measuresDataArray,
            isLoading: false,
            isError: false,
          });
          const updated: AssetMeasurementDetailsWithLastFetchAndSucessWithAgg[] = [];
          measuresDataArray.forEach((res) => {
            if (!res.isError && res.tsData) {
              updated.push({
                ...res.measureData,
                lastFetchTime: res.lastFetchTime,
                isSuccess: !res.isError,
              });
            } else {
              const existing = successAndFailedMeasurements.find(
                (r) => r.measurementId === res?.measureData?.measurementId,
              );

              if (existing) {
                updated.push({
                  ...existing,
                  lastFetchTime: existing.lastFetchTime,
                  isSuccess: !res.isError,
                });
              } else {
                updated.push({
                  ...res.measureData,
                  lastFetchTime: res.lastFetchTime,
                  isSuccess: !res.isError,
                });
              }
            }
          });
          const seenIds = new Set();
          const finalVals: AssetMeasurementDetailsWithLastFetchAndSucessWithAgg[] = [];
          updated.forEach((update) => {
            if (!seenIds.has(update.measurementId)) {
              seenIds.add(update.measurementId);
              finalVals.push(update);
            }
          });
          setSuccessAndFailedMeasurements([...finalVals]);
        } catch (error) {
          console.error('Error fetching data:', error);
          setState({ data: undefined, isLoading: false, isError: true });
          setSuccessAndFailedMeasurements([]);
        }
      }
    };
    if (!isRealTime) fetchData();
  }, [
    selectedTitles,
    assetMeasureVariables,
    globalStartDate,
    globalEndDate,
    globalSamplePeriod,
    timeRangeType,
    assetTz,
    dispatch,
    fetchTimeseriesDataForDifferent,
    settings.isRelativeToGlboalEndTime,
    settings.globalSamplePeriod,
    settings.samplePeriod,
    settings.overrideGlobalSettings,
    settings.overrideAssetTzValue,
    settings.overrideAssetTz,
    settings.timeRange,
    settings.startDate,
    settings.endDate,
    isRealTime,
    settings,
  ]);
  useEffect(() => {
    if (!graphRef.current) {
      graphRef.current = new dia.Graph({}, { cellNamespace: cellNamespace });
    }
  }, []);

  useEffect(() => {
    if (!state || !graphRef.current || !settings.jsonFile) return;

    if (graphRef.current && settings.jsonFile) {
      const parsedContent = JSON.parse(settings.jsonFile)?.graph;
      if (parsedContent) {
        graphRef.current.fromJSON(parsedContent);
      }
    }

    const filteredResults = state.data?.filter((data) => !data.isError && data.tsData) ?? [];
    getDiagramData({
      filteredResults,
      graphRef,
      selectedTitles,
    });

    let hasChanges = false;

    graphRef.current.getCells().forEach((cell) => {
      const variables = (cell.get('data')?.variables ?? []) as elementVariable[];
      if (variables.length > 0) {
        const firstVariable = variables[0];
        const newLevel = firstVariable.value || 0; // Default to 0 if no value
        const updatedData = {
          ...cell.get('data'),
          level: newLevel,
        };

        if (!isEqual(cell.get('data'), updatedData)) {
          hasChanges = true;
          cell.prop('data', updatedData);
        }
      }
    });

    if (hasChanges) {
      const currentGraphJSON = JSON.stringify({ graph: graphRef.current.toJSON() }, null);
      if (previousGraphJSON.current !== currentGraphJSON) {
        previousGraphJSON.current = currentGraphJSON; // Update ref
        dispatch(
          dashboardSlice.actions.setCurrentWidgetSettings({
            id: id,
            type: 'Diagram',
            settings: {
              ...settings,
              jsonFile: currentGraphJSON,
            },
          }),
        );
      }
    }
  }, [state, selectedTitles, assetMeasureVariables, dispatch, settings.jsonFile]);

  useEffect(() => {
    if (!isRealTime) {
      return;
    }
    const intervalId = setInterval(() => {
      const fetcHistoryMeasuresData = async () => {
        setState({ ...state, isLoading: false, isError: false });
        const grouped = groupAssetMeasures(assetMeasure);
        const batched = await fetchAllAssetMeasures(
          customerId,
          Object.entries(grouped).map(([asset_id, measurement_ids]) => ({
            asset_id: Number(asset_id),
            measurement_ids,
          })),
        );

        const results = batched.items.map((measureData) => {
          const assetMeasureId = measureData.id;
          const measurementId = measureData.measurement_id;

          const matchedVariable = assetMeasureVariables.find(
            (v) => assetMeasureId !== null && v.measurementId === assetMeasureId.toString(),
          );

          return {
            isLoading: false,
            isError: false,
            error: '',
            lastFetchTime: Date.now(),
            measureData: {
              ...measureData,
              id: assetMeasureId,
              measurementId: measurementId,
              metricId: measureData.metricId ?? null,
              tag: measureData.tag ?? '',
            },
            unitOfMeasures: measureData.unitOfMeasure ? [measureData.unitOfMeasure] : [],
            tsData: {
              tag: measurementId,
              period: '',
              'ts,val': [],
              tag_meta: { uom: measureData.unitOfMeasure?.name ?? '' },
            },
            aggBy: matchedVariable?.aggBy ?? settings.aggBy ?? 1,
            samplePeriod,
            timeRange,
            startDate,
            endDate,
            globalSamplePeriod: !!globalSamplePeriod,
            assetTz: assetTzOverride ? assetTzOverrideValue : assetTz,
          } as MeasuresData;
        });

        const tsMeasureIds = results.map((result) => result.measureData?.measurementId);
        const { error, tsData } = await fetchHistoryTimeseriesData(tsMeasureIds.filter(Boolean));
        if (error) {
          setState({ data: undefined, isLoading: false, isError: true });
          return;
        }
        results.forEach((result) => {
          const seriesData = tsData[result.measureData?.measurementId];
          if (!seriesData || seriesData?.error || seriesData?.['ts,val'] === undefined) {
            result.isLoading = false;
            result.isError = true;
            result.error = seriesData?.error ?? 'No data available';
          } else {
            result.isLoading = false;
            result.tsData = seriesData;
            result.lastFetchTime = Date.now();
          }
        });
        const updated: AssetMeasurementDetailsWithLastFetchAndSucessWithAgg[] = [];
        results.forEach((res) => {
          if (!res.isError && res.tsData) {
            updated.push({
              ...res.measureData,
              lastFetchTime: res.lastFetchTime,
              isSuccess: !res.isError,
            });
          } else {
            const existing = successAndFailedMeasurements.find(
              (r) => r.measurementId === res?.measureData?.measurementId,
            );

            if (existing) {
              updated.push({
                ...existing,
                lastFetchTime: existing.lastFetchTime,
                isSuccess: !res.isError,
              });
            } else {
              updated.push({
                ...res.measureData,
                lastFetchTime: res.lastFetchTime,
                isSuccess: !res.isError,
              });
            }
          }
        });
        const seenIds = new Set();
        const finalVals: AssetMeasurementDetailsWithLastFetchAndSucessWithAgg[] = [];
        updated.forEach((update) => {
          if (!seenIds.has(update.measurementId)) {
            seenIds.add(update.measurementId);
            finalVals.push(update);
          }
        });
        setSuccessAndFailedMeasurements([...finalVals]);
        setState({
          ...state,
          data: results,
          isLoading: false,
          isError: false,
        });
      };
      if (assetMeasureVariables && assetMeasureVariables.length > 0) fetcHistoryMeasuresData();
    }, refreshInterval * 1000);

    return () => clearInterval(intervalId);
  }, [dispatch, isRealTime, refreshInterval, customerId, selectedTitles]);
  return {
    ...state,
    successAndFailedMeasurements,
  };
};

export default useDiagramWidgetData;
