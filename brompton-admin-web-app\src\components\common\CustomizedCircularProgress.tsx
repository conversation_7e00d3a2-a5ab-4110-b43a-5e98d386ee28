import Box from '@mui/material/Box';
import CircularProgress, {
  circularProgressClasses,
  CircularProgressProps,
} from '@mui/material/CircularProgress';

export function CustomizedCircularProgress(props: CircularProgressProps) {
  return (
    <Box sx={{ position: 'relative', flexGrow: 1, height: '100%', width: '100%' }}>
      <CircularProgress
        {...props}
        variant="determinate"
        sx={{
          color: (theme) => theme.palette.grey[theme.palette.mode === 'light' ? 200 : 800],
          height: '100% !important',
          width: '100% !important',
        }}
        thickness={4}
        value={100}
      />
      <CircularProgress
        {...props}
        variant="determinate"
        disableShrink
        sx={{
          color: (theme) => (theme.palette.mode === 'light' ? '#1a90ff' : '#308fe8'),
          ...props.sx,
          animationDuration: '550ms',
          height: '100% !important',
          width: '100% !important',
          position: 'absolute',
          left: 0,
          [`& .${circularProgressClasses.circle}`]: {
            strokeLinecap: 'round',
          },
        }}
        thickness={4}
        // value={50}
      />
    </Box>
  );
}
