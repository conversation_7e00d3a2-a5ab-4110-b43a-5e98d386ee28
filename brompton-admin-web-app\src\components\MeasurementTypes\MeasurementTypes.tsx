import { yupResolver } from '@hookform/resolvers/yup';
import AddIcon from '@mui/icons-material/Add';
import EditIcon from '@mui/icons-material/Edit';
import { Box, Button, CircularProgress, IconButton, TextField } from '@mui/material';
import { DataGrid, GridColDef } from '@mui/x-data-grid';
import { useEffect, useState } from 'react';
import { Controller, useForm } from 'react-hook-form';
import * as yup from 'yup';
import {
  useCreateMeasurementTypeMutation,
  useGetAllMeasureTypesQuery,
  useGetMeasurementTypeByIdMutation,
  useUpdateMeasurementTypeMutation,
} from '~/redux/api/measuresApi';
import { AlertSnackbar } from '~/shared/snackbars/components/AlertSnackbar';
import { useSnackbar } from '~/shared/snackbars/snackbar-hooks';
import CustomDialog from '../common/CustomDialog';
import PageName from '../common/PageName/PageName';

// Form validation schema
const measurementTypeSchema = yup.object().shape({
  name: yup.string().required('Please enter a measurement type name'),
  category: yup
    .string()
    .nullable()
    .transform((value) => (value === '' ? null : value)),
  description: yup
    .string()
    .nullable()
    .transform((value) => (value === '' ? null : value)),
});

type FormData = yup.InferType<typeof measurementTypeSchema>;

// Define the MeasurementType interface
interface MeasurementType {
  id: number;
  name: string;
  category?: string | null;
  description?: string | null;
}

const MeasurementTypes = () => {
  const [snackbarState, showSuccessAlert, showErrorAlert] = useSnackbar();
  const [selectedRow, setSelectedRow] = useState<MeasurementType | null>(null);
  const [createOrEdit, setCreateOrEdit] = useState<'create' | 'edit' | null>(null);
  // const [deleteConfirmation, setDeleteConfirmation] = useState<{
  //   open: boolean;
  //   measurementType: MeasurementType | null;
  // }>({
  //   open: false,
  //   measurementType: null,
  // });

  const { data: measureTypes, isLoading: loadingMeasures, refetch } = useGetAllMeasureTypesQuery();
  const [createMeasurementType, { isLoading: isCreating }] = useCreateMeasurementTypeMutation();
  const [updateMeasurementType, { isLoading: isUpdating }] = useUpdateMeasurementTypeMutation();
  // const [deleteMeasurementType, { isLoading: isDeleting }] = useDeleteMeasurementTypeMutation();
  const [getMeasurementTypeById] = useGetMeasurementTypeByIdMutation();

  const isProcessing = isCreating || isUpdating;

  const {
    control,
    handleSubmit,
    reset,
    setValue,
    formState: { errors },
  } = useForm<FormData>({
    defaultValues: {
      name: '',
      category: null,
      description: null,
    },
    resolver: yupResolver(measurementTypeSchema),
  });

  // Effect to populate form when editing
  useEffect(() => {
    if (createOrEdit === 'edit' && selectedRow) {
      setValue('name', selectedRow.name);
      setValue('category', selectedRow.category || null);
      setValue('description', selectedRow.description || null);
    }
  }, [createOrEdit, selectedRow, setValue]);

  // Simplify the onSubmit function to focus on the core functionality
  const onSubmit = async (data: FormData) => {
    try {
      if (createOrEdit === 'create') {
        await createMeasurementType({
          name: data.name.trim(),
          category: data.category,
          description: data.description,
        }).unwrap();

        showSuccessAlert('Measurement type created successfully');
      } else if (createOrEdit === 'edit' && selectedRow) {
        await updateMeasurementType({
          id: selectedRow.id,
          name: data.name,
          category: data.category,
          description: data.description,
        }).unwrap();

        showSuccessAlert('Measurement type updated successfully');
      }

      refetch();
      setCreateOrEdit(null);
      setSelectedRow(null);
      reset();
    } catch (error: any) {
      console.error('Error in form submission:', error);
      showErrorAlert(error?.data?.exception || 'Error processing measurement type');
    }
  };

  // const handleDeleteClick = (measurementType: MeasurementType) => {
  //   setDeleteConfirmation({
  //     open: true,
  //     measurementType,
  //   });
  // };

  // const handleDeleteConfirm = async () => {
  //   if (deleteConfirmation.measurementType) {
  //     try {
  //       await deleteMeasurementType(deleteConfirmation.measurementType.id).unwrap();
  //       showSuccessAlert('Measurement type deleted successfully');
  //       refetch(); // Refresh the list after deletion
  //     } catch (error: any) {
  //       console.error('Error deleting measurement type:', error);

  //       if (error?.status === 404) {
  //         showErrorAlert('Measurement type not found');
  //       } else if (error?.status === 403) {
  //         showErrorAlert('You do not have permission to delete this measurement type');
  //       } else {
  //         showErrorAlert(error?.data?.message || 'Error deleting measurement type');
  //       }
  //     }
  //   }
  //   setDeleteConfirmation({
  //     open: false,
  //     measurementType: null,
  //   });
  // };

  // const handleDeleteCancel = () => {
  //   setDeleteConfirmation({
  //     open: false,
  //     measurementType: null,
  //   });
  // };

  const handleEditClick = async (id: number) => {
    try {
      const response = await getMeasurementTypeById(id).unwrap();

      const measurementType: MeasurementType = {
        id: response.id,
        name: response.name,
        category: response.category || null,
        description: response.description || null,
      };

      setSelectedRow(measurementType);
      setCreateOrEdit('edit');
    } catch (error: any) {
      console.error('Error fetching measurement type details:', error);
      showErrorAlert(error?.data?.message || 'Error fetching measurement type details');
    }
  };

  const columns: GridColDef[] = [
    {
      field: 'name',
      headerName: 'Measurement Type',
      flex: 1.5,
      valueGetter: (params) => params.row.name || 'N/A',
    },
    {
      field: 'category',
      headerName: 'Category',
      flex: 1,
      valueGetter: (params) => params.row.category || 'N/A',
    },
    {
      field: 'description',
      headerName: 'Description',
      flex: 1,
      valueGetter: (params) => params.row.description || 'N/A',
    },
    {
      field: 'actions',
      headerName: 'Actions',
      flex: 1,
      renderCell: (params) => {
        const row = params.row as MeasurementType;
        return (
          <Box sx={{ display: 'flex', gap: 1 }}>
            <IconButton color="primary" size="small" onClick={() => handleEditClick(row.id)}>
              <EditIcon />
            </IconButton>
            {/* <IconButton color="error" size="small" onClick={() => handleDeleteClick(row)}>
              <DeleteIcon />
            </IconButton> */}
          </Box>
        );
      },
    },
  ];

  return (
    <Box pt={1}>
      <AlertSnackbar {...snackbarState} />

      <Box display="flex" justifyContent="space-between" alignItems={'center'} mb={2}>
        <Box py={2}>
          <PageName name="Measurement Types" />
        </Box>

        <Button
          variant="contained"
          color="primary"
          startIcon={<AddIcon />}
          onClick={() => {
            setCreateOrEdit('create');
            reset();
          }}
        >
          New Measurement Type
        </Button>
      </Box>

      {loadingMeasures ? (
        <Box display="flex" justifyContent="center" alignItems="center" height={200}>
          <CircularProgress />
        </Box>
      ) : (
        <DataGrid
          sx={{
            height: 'calc(100vh - 120px)',
            '& .MuiDataGrid-columnHeader': {
              background: '#F9FAFB',
            },
          }}
          rows={measureTypes ? [...measureTypes].sort((a, b) => b.id - a.id) : []}
          columns={columns}
          autoPageSize
          initialState={{
            sorting: {
              sortModel: [{ field: 'id', sort: 'desc' }],
            },
          }}
          slotProps={{
            toolbar: {
              showQuickFilter: true,
              printOptions: { disableToolbarButton: true },
              csvOptions: { disableToolbarButton: true },
            },
          }}
        />
      )}

      {/* Dialog for creating/editing measurement types */}
      <CustomDialog
        open={createOrEdit !== null}
        maxWidth="md"
        onClose={() => {
          reset();
          setCreateOrEdit(null);
          setSelectedRow(null);
        }}
        title={
          <>{createOrEdit === 'create' ? 'Add New Measurement Type' : 'Edit Measurement Type'}</>
        }
        content={
          <Box component="form" onSubmit={handleSubmit(onSubmit)}>
            <Controller
              name="name"
              control={control}
              render={({ field }) => (
                <TextField
                  {...field}
                  label="Measurement Type Name"
                  fullWidth
                  margin="normal"
                  error={!!errors.name}
                  helperText={errors.name?.message}
                  disabled={isProcessing}
                />
              )}
            />

            {/* <Controller
              name="unitNames"
              control={control}
              render={({ field }) => (
                <Box>
                  <Typography fontWeight="bold" my={2}>
                    Units of Measure
                  </Typography>

                  {(field.value ?? []).map((unit, index) => (
                    <Box
                      key={index}
                      display="flex"
                      justifyContent="center"
                      alignItems="center"
                      gap={2}
                      mt={1}
                    >
                      <TextField
                        fullWidth
                        label={`Unit ${index + 1}`}
                        value={unit}
                        onChange={(e) => {
                          const updated = [...(field.value ?? [])];
                          updated[index] = e.target.value;
                          field.onChange(updated);
                        }}
                        sx={{ mb: 1 }}
                        error={!!errors.unitNames?.[index]}
                        helperText={errors.unitNames?.[index]?.message}
                      />
                      <IconButton
                        color="error"
                        onClick={() => {
                          const updated = (field.value ?? []).filter((_, i) => i !== index);
                          field.onChange(updated);
                        }}
                        disabled={(field.value ?? []).length === 1}
                      >
                        <DeleteIcon />
                      </IconButton>
                    </Box>
                  ))}

                  <Button
                    variant="outlined"
                    onClick={() => field.onChange([...(field.value ?? []), ''])}
                    sx={{ mt: 1 }}
                  >
                    + Add Unit
                  </Button>

                  {typeof errors.unitNames === 'string' && (
                    <Typography color="error" variant="body2" mt={1}>
                      {errors.unitNames}
                    </Typography>
                  )}
                </Box>
              )}
            /> */}

            {/* <Controller
              name="category"
              control={control}
              render={({ field }) => (
                <TextField
                  {...field}
                  label="Category"
                  fullWidth
                  margin="normal"
                  error={!!errors.category}
                  helperText={errors.category?.message}
                  disabled={isProcessing}
                />
              )}
            />

            <Controller
              name="description"
              control={control}
              render={({ field }) => (
                <TextField
                  {...field}
                  label="Description"
                  fullWidth
                  margin="normal"
                  multiline
                  rows={3}
                  error={!!errors.description}
                  helperText={errors.description?.message}
                  disabled={isProcessing}
                />
              )}
            /> */}

            <Box sx={{ mt: 3, display: 'flex', justifyContent: 'flex-end', gap: 2 }}>
              <Button
                variant="outlined"
                onClick={() => {
                  reset();
                  setCreateOrEdit(null);
                  setSelectedRow(null);
                }}
                disabled={isProcessing}
              >
                Cancel
              </Button>
              <Button type="submit" variant="contained" color="primary" disabled={isProcessing}>
                {isProcessing ? (
                  <>
                    <CircularProgress size={18} sx={{ mr: 1 }} />
                    {createOrEdit === 'create' ? 'Creating...' : 'Updating...'}
                  </>
                ) : createOrEdit === 'create' ? (
                  'Add'
                ) : (
                  'Update'
                )}
              </Button>
            </Box>
          </Box>
        }
        dialogActions={null}
      />

      {/* Delete Confirmation Dialog */}
      {/* <Dialog open={deleteConfirmation.open} onClose={handleDeleteCancel}>
        <DialogTitle>
          <Typography variant="h4">Delete Measurement Type?</Typography>
        </DialogTitle>
        <DialogContent>
          <Typography color="error">This action cannot be undone.</Typography>
          {deleteConfirmation.measurementType && (
            <Typography mt={2}>
              Are you sure you want to delete the measurement type "
              {deleteConfirmation.measurementType.name}"?
            </Typography>
          )}
        </DialogContent>
        <DialogActions>
          <Button
            variant="outlined"
            startIcon={<CancelIcon />}
            onClick={handleDeleteCancel}
            disabled={isDeleting}
          >
            Cancel
          </Button>
          <Button
            variant="contained"
            color="error"
            startIcon={<DeleteIcon />}
            onClick={handleDeleteConfirm}
            disabled={isDeleting}
          >
            {isDeleting ? (
              <>
                <CircularProgress size={24} sx={{ mr: 1 }} color="inherit" />
                Deleting...
              </>
            ) : (
              'Delete'
            )}
          </Button>
        </DialogActions>
      </Dialog> */}
    </Box>
  );
};

export default MeasurementTypes;
