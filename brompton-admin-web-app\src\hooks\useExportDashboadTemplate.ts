import { ThunkDispatch } from '@reduxjs/toolkit';
import { useDispatch } from 'react-redux';
import { useState } from 'react';
import { dashboardTemplateApi } from '~/redux/api/dashboardTemplate';
import { RootState } from '~/redux/store';

const useExportDashboardTemplate = () => {
  const dispatch = useDispatch<ThunkDispatch<RootState, any, any>>();
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState<{
    type: 'success' | 'error' | 'info';
    text: string;
  } | null>(null);

  const exportTemplate = async (templateId: number) => {
    if (!templateId) return;

    try {
      setLoading(true);
      setMessage({ type: 'info', text: 'Exporting dashboard template data...' });

      // Fetch template data
      const result = await dispatch(
        dashboardTemplateApi.endpoints.getDashboardTemplateDetails.initiate(templateId),
      );

      if ('error' in result) {
        setMessage({ type: 'error', text: 'Error fetching dashboard template. Please try again.' });
        console.error('Error fetching dashboard template:', result.error);
        setLoading(false);
        return;
      }

      const tsData = result.data;

      if (!tsData) {
        setMessage({ type: 'error', text: 'No template data available for export.' });
        setLoading(false);
        return;
      }

      // Convert template data to JSON format
      const jsonString = JSON.stringify(tsData, null, 2);

      // Create a Blob and trigger a file download
      const blob = new Blob([jsonString], { type: 'application/json' });
      const link = document.createElement('a');
      link.href = URL.createObjectURL(blob);
      link.download = `${tsData.title.replace(/\s+/g, '_')}.json`; // Use template title in filename
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      setMessage({ type: 'success', text: 'Dashboard template exported successfully!' });
    } catch (error) {
      console.error('Error exporting dashboard template:', error);
      setMessage({ type: 'error', text: 'Failed to export dashboard template.' });
    } finally {
      setLoading(false);
    }
  };

  return { exportTemplate, loading, message, setMessage };
};

export default useExportDashboardTemplate;
