// tests/api.test.js
const { test, expect, request } = require('@playwright/test');

test.describe('API Test Suite', () => {
  test('DELETE /customers/1/assets/2/measurements/9500 deletes measurement successfully', async ({
    request,
  }) => {
    // Set headers
    const headers = {
      'BE-CsrfToken': 'bIE86VrZwYg5n1wcKMBq89EaOM5MVjPrGhcSzQu9naA=',
      Cookie:
        'BE-AccessToken=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VybmFtZSI6InRlc3QiLCJzdWIiOjIsInJvbGVzIjp7IkFETUlOIjpbMTI4LDgsMTIxLDExOSwxMDYsMTE4LDEwOCw4NiwxMTEsMTEyLDg1LDEwNyw4NCwxMTMsMTE0LDExNywxMjAsMTI3LDEyMywxMjYsMTI0LDEwOSwxMjUsMTIyLDExMCw5LDgyXSwiVVNFUiI6WzEyOCw4LDEyMSwxMTksMTA2LDExOCwxMDgsODYsMTExLDExMiw4NSwxMDcsODQsMTEzLDExNCwxMTcsMTIwLDEyNywxMjMsMTI2LDEyNCwxMDksMTI1LDEyMiwxMTAsOSw4Ml0sIlBPV0VSX1VTRVIiOlsxMjgsOCwxMjEsMTE5LDEwNiwxMTgsMTA4LDg2LDExMSwxMTIsODUsMTA3LDg0LDExMywxMTQsMTE3LDEyMCwxMjcsMTIzLDEyNiwxMjQsMTA5LDEyNSwxMjIsMTEwLDksODJdfSwiaWF0IjoxNzMxNTY1MjIyLCJleHAiOjE3MzE1NzI0MjJ9.mej4jUYqTrgZiCOcHWaNDYOjVpJI0WuHGokCDSeh3FQ; BE-CSRFToken=bIE86VrZwYg5n1wcKMBq89EaOM5MVjPrGhcSzQu9naA%3D',
    };

    // Make DELETE request
    const response = await request.delete(
      'https://test.brompton.ai/api/v0/customers/1/assets/2/measurements/9500',
      {
        headers: headers,
      },
    );

    // Check response status
    expect(response.status()).toBe(204); // Assuming 204 No Content indicates successful deletion

    // Optionally, you can check if the resource was deleted by making a GET request to the same URL to verify it no longer exists
    const verifyResponse = await request.get(
      'https://test.brompton.ai/api/v0/customers/1/assets/2/measurements/9500',
      {
        headers: headers,
      },
    );
    expect(verifyResponse.status()).toBe(404); // Assuming 404 Not Found indicates resource was deleted
  });
});
