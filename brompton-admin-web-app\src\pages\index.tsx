import { CircularProgress } from '@mui/material';
import { useGetCurrentUserDetailsQuery } from '~/redux/api/usersApi';
import { useEffect } from 'react';
import { useRouter } from 'next/router';

export default function HomePage() {
  const router = useRouter();
  const { isSuccess, isLoading, isError } = useGetCurrentUserDetailsQuery();

  useEffect(() => {
    if (isSuccess) {
      router.push('/customer/');
    }
    if (isError) {
      router.push('/login');
    }
  }, [isSuccess, isError, router]);

  return <>{isLoading && <CircularProgress />}</>;
}
