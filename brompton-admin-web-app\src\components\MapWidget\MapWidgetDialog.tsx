import AddIcon from '@mui/icons-material/Add';
import { Button, Checkbox, FormControlLabel, FormGroup, Grid, TextField } from '@mui/material';
import { Dispatch, SetStateAction } from 'react';
import { MapWidget } from '~/types/widgets';
import DataWidgetSettingsContainer from '../common/DataWidgetSettingsContainer/DataWidgetSettingsContainer';
import MapMarker from './MapMarker';
type MapWidgetDialogProps = {
  settings: MapWidget;
  handleSettingsChange: Dispatch<SetStateAction<MapWidget>>;
};
const MapWidgetDialog = ({ settings, handleSettingsChange: setSettings }: MapWidgetDialogProps) => {
  const addMarker = () => {
    setSettings((prevState) => {
      return {
        ...prevState,
        markers: [
          ...prevState.markers,
          {
            markerName: '',
            assetMeasures: [],
            location: { lat: 0, lon: 0 },
            color: '#000000',
            selectedTitles: [],
            labelAndUnits: {},
            overrideLocation: false,
            dashboard: null,
          },
        ],
      };
    });
  };
  const handleZoomLevelChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { value } = e.target;
    setSettings((prevState) => {
      return {
        ...prevState,
        zoomLevel: parseInt(value),
      };
    });
  };
  const handleCenterLongChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { value } = e.target;
    setSettings((prevState) => {
      return {
        ...prevState,
        mapCenter: {
          ...prevState.mapCenter,
          lon: parseFloat(value),
        },
      };
    });
  };
  const handleCenterLatChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { value } = e.target;
    setSettings((prevState) => {
      return {
        ...prevState,
        mapCenter: {
          ...prevState.mapCenter,
          lat: parseFloat(value),
        },
      };
    });
  };
  const handleOverrideMapCenter = (e: React.ChangeEvent<HTMLInputElement>, checked: boolean) => {
    setSettings((prevState) => ({
      ...prevState,
      changeMapCenter: e.target.checked,
    }));
  };

  return (
    <DataWidgetSettingsContainer
      settings={settings}
      setSettings={setSettings}
      hideSettings={{
        title: true,
      }}
      dataTabChildren={
        <>
          <Button variant="contained" sx={{ mb: 2 }} startIcon={<AddIcon />} onClick={addMarker}>
            Add Marker
          </Button>
          {settings.markers?.map((_, index) => {
            return (
              <MapMarker
                key={index}
                settings={settings}
                handleSettingsChange={setSettings}
                currentSettings={index}
              />
            );
          })}
        </>
      }
      feelTabChidren={
        <>
          <FormGroup>
            <TextField
              name="zoomLevel"
              type="number"
              label="Zoom level"
              onChange={handleZoomLevelChange}
              value={settings.zoomLevel}
              variant="outlined"
              margin="normal"
              fullWidth
            />
          </FormGroup>
          <FormGroup>
            <FormControlLabel
              control={
                <Checkbox
                  checked={settings?.changeMapCenter}
                  onChange={handleOverrideMapCenter}
                  name="changeMapCenter"
                />
              }
              label="Override Map Center"
            />
          </FormGroup>
          {settings.changeMapCenter ? (
            <Grid container spacing={2}>
              {/* Center Latitude */}
              <Grid item xs={12} sm={6}>
                <TextField
                  name="centerLat"
                  type="number"
                  label="Center Latitude"
                  onChange={handleCenterLatChange}
                  value={settings.mapCenter.lat}
                  variant="outlined"
                  margin="normal"
                  fullWidth
                />
              </Grid>

              {/* Center Longitude */}
              <Grid item xs={12} sm={6}>
                <TextField
                  name="centerLong"
                  type="number"
                  label="Center Longitude"
                  onChange={handleCenterLongChange}
                  value={settings.mapCenter.lon}
                  variant="outlined"
                  margin="normal"
                  fullWidth
                />
              </Grid>
            </Grid>
          ) : null}
        </>
      }
    />
  );
};
export default MapWidgetDialog;
