import { useSelector } from 'react-redux';
import { WidgetContainer } from '~/layout/containers/WidgetContainer';
import { getWidgets, getWidgetsLayout } from '~/redux/selectors/widgetSelectors';

const WidgetLayoutDrawer = () => {
  const widgets = useSelector(getWidgets);
  const widgetLayout = useSelector(getWidgetsLayout);
  return (
    <WidgetContainer
      dashboardId={0}
      isDashboardDetailsSuccess={true}
      isError={false}
      isLoading={false}
      widgets={widgets}
      widgetLayout={widgetLayout}
    />
  );
};

export default WidgetLayoutDrawer;
