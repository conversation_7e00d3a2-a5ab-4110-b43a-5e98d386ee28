import {
  Autocomplete,
  Box,
  Checkbox,
  CircularProgress,
  FormControl,
  FormControlLabel,
  MenuItem,
  Stack,
  TextField,
} from '@mui/material';
import Select, { SelectChangeEvent } from '@mui/material/Select';
import React, { useEffect, useMemo, useState } from 'react';
import { useSelector } from 'react-redux';
import { MeasureSettingSelector } from '~/components/common/MeasureSettingSelector';
import { useGetAllAssetQuery } from '~/redux/api/assetsApi';
import { useGetAllMeasurementsQuery } from '~/redux/api/measuresApi';
import { getCustomerId } from '~/redux/selectors/customerSelectors';
import { getMetricsIdToName, selectDashboardState } from '~/redux/selectors/dashboardSelectors';
import {
  ChartMeasureSetting,
  ScatterChartWidget,
  setForecastWidgetSettings,
  setMultiMeasureChartSettings,
  setMultiMeasureWidgetSettings,
  setSumDeltaWidgetSettings,
} from '~/types/widgets';
import { assetsPathMapper, formatMetricLabel } from '~/utils/utils';
import ChartLegendsSettings from '../common/ChartLegendsSettings';
import ChartThreshHold from '../common/ChartThreshHold';
import DataWidgetSettingsContainer from '../common/DataWidgetSettingsContainer/DataWidgetSettingsContainer';
import MeasureColorSelector from '../common/MeasueColorSelector';
import MultiMeasureSelection from '../common/MultiMeasureSelection';
import MultiMeasureSelectionMenu from '../common/MultiMeasureSelectionMenu';
import ShowMinMax from '../common/ShowMinMax';
import ForecastSettings from '../ForecastSettings/ForecastSettings';
import SumDeltaSettings from '../SumDelta/SumDeltaSettings';

type ScatterChartSettingsProps = {
  selectedDbMeasureIdToName: { [key: string]: string };
  scatterChartSettings: ScatterChartWidget;
  handleScatterChartSettingsUpdate: (
    value: ((prevState: ScatterChartWidget) => ScatterChartWidget) | ScatterChartWidget,
  ) => void;
};
type AssetOption = {
  label: string;
  id: number;
};

type MeasureOption = {
  label: string;
  id: string; // Changed to string
};
export function ScatterChartSettings({
  selectedDbMeasureIdToName,
  scatterChartSettings,
  handleScatterChartSettingsUpdate,
}: ScatterChartSettingsProps) {
  const { dbMeasureIdToSetting, selectedSparkTitle } = scatterChartSettings;
  const customerId = useSelector(getCustomerId);
  const metricsIdToName = useSelector(getMetricsIdToName);
  const dashboardState = useSelector(selectDashboardState);
  const [selectedMeasures, setSelectedMeasures] = useState<string[]>([]);
  const [selectedAsset, setSelectedAsset] = useState<AssetOption | null>(null);
  const [selectedMeasure, setSelectedMeasure] = useState<MeasureOption | null>(null);
  const [selectedMetric, setSelectedMetric] = useState<string | null>(null);
  const {
    data: assetData,
    isLoading: isAssetLoading,
    isFetching: isAssetReloading,
    error: assetError,
  } = useGetAllAssetQuery(
    { customerId, parentIds: [] },
    {
      skip: !customerId,
      refetchOnMountOrArgChange: true,
    },
  );
  const assetTypesWithPath = useMemo(() => assetsPathMapper(assetData ?? []), [assetData]);

  const { data: measurementData, isLoading: isMeasurementLoading } = useGetAllMeasurementsQuery(
    { customerId, assetId: selectedAsset ? selectedAsset.id : 0 },
    {
      skip: !selectedAsset || selectedAsset.id === 0,
      refetchOnMountOrArgChange: true,
    },
  );
  const measurementOptions: MeasureOption[] = useMemo(() => {
    if (!measurementData) return [];
    return measurementData.map((measure) => ({
      label: formatMetricLabel(measure.tag), // Adjust based on your actual measure data structure
      id: String(measure.id), // Convert number to string
    }));
  }, [measurementData]);
  useEffect(() => {
    if (
      scatterChartSettings.mode === 'template' &&
      scatterChartSettings.showStacked &&
      scatterChartSettings.selectedSparkMeasure &&
      scatterChartSettings.selectedSparkMeasure.measureId !== ''
    ) {
      setSelectedMetric(scatterChartSettings.selectedSparkMeasure?.measureId);
      handleScatterChartSettingsUpdate((prevState) => ({
        ...prevState,
        selectedSparkMeasure: {
          assetId: scatterChartSettings?.selectedSparkMeasure?.assetId ?? '',
          measureId: scatterChartSettings?.selectedSparkMeasure?.measureId ?? '',
        },
      }));
    }
  }, [
    scatterChartSettings.mode,
    scatterChartSettings.showStacked,
    scatterChartSettings.selectedSparkMeasure,
  ]);
  useEffect(() => {
    if (
      scatterChartSettings.selectedSparkMeasure &&
      scatterChartSettings.selectedSparkMeasure.assetId !== '' &&
      assetTypesWithPath.length > 0
    ) {
      const currentAssetId = scatterChartSettings.selectedSparkMeasure.assetId;
      const asset = assetTypesWithPath.find((a) => String(a.id) === currentAssetId);
      if (asset) {
        setSelectedAsset({ label: asset.label, id: asset.id });
      } else {
        setSelectedAsset(null);
      }
    } else {
      setSelectedAsset(null);
    }

    if (
      scatterChartSettings.selectedSparkMeasure &&
      scatterChartSettings.selectedSparkMeasure.measureId !== '' &&
      measurementOptions.length > 0
    ) {
      const currentMeasureId = scatterChartSettings.selectedSparkMeasure.measureId;
      const measure = measurementOptions.find((m) => String(m.id) === currentMeasureId);
      if (measure) {
        setSelectedMeasure({ label: measure.label, id: measure.id });
      } else {
        setSelectedMeasure(null);
      }
    } else {
      setSelectedMeasure(null);
    }
  }, [
    scatterChartSettings.showStacked,
    scatterChartSettings.selectedSparkMeasure,
    assetTypesWithPath,
    measurementOptions,
  ]);
  useEffect(() => {
    const hasValidAssetMeasure = scatterChartSettings.assetMeasure?.some(
      (assetMeas) =>
        assetMeas.assetId.trim() !== '' &&
        assetMeas.measureId.some((measure) => measure.trim() !== ''),
    );
    if (hasValidAssetMeasure) {
      const titles = scatterChartSettings.assetMeasure
        .flatMap((assetMeas) => assetMeas.measureId)
        .filter((measure) => measure.trim() !== '');
      setSelectedMeasures(titles);
    } else {
      setSelectedMeasures([]);
    }
  }, [scatterChartSettings.assetMeasure]);
  const handleDbMeasureIdToSettingUpdate = (
    updatedDbMeasureIdToYAxisSide: Record<string, ChartMeasureSetting>,
  ) => {
    handleScatterChartSettingsUpdate({
      ...scatterChartSettings,
      dbMeasureIdToSetting: updatedDbMeasureIdToYAxisSide,
    });
  };
  const handleDbMeasureIdAnnotationToSettingUpdate = (
    updatedDbMeasureIdAnnotationToYAxisSide: Record<string, boolean>,
  ) => {
    handleScatterChartSettingsUpdate({
      ...scatterChartSettings,
      dbMeasureIdToAnnotation: updatedDbMeasureIdAnnotationToYAxisSide,
    });
  };
  const handleShowArea = (event: React.ChangeEvent<HTMLInputElement>, checked: boolean) => {
    const { name } = event.target;
    handleScatterChartSettingsUpdate(
      name === 'showSparkLine'
        ? {
            ...scatterChartSettings,
            [name]: checked,
            selectedTitles: checked
              ? scatterChartSettings.selectedTitles.slice(0, 1)
              : scatterChartSettings.selectedTitles,
          }
        : { ...scatterChartSettings, [name]: checked },
    );
  };
  const handleShowStacked = (event: React.ChangeEvent<HTMLInputElement>, checked: boolean) => {
    const { name } = event.target;
    if (!checked) {
      handleScatterChartSettingsUpdate({
        ...scatterChartSettings,
        [name]: checked,
        selectedSparkTitle: '',
        selectedSparkMeasure: undefined,
        showForecast: false,
      });
    } else {
      handleScatterChartSettingsUpdate({
        ...scatterChartSettings,
        [name]: checked,
        showForecast: false,
      });
    }
  };
  useEffect(() => {
    if (scatterChartSettings.showForecast) {
      handleScatterChartSettingsUpdate((prevState) => ({
        ...prevState,
        showStacked: false,
        selectedSparkTitle: '',
      }));
    }
  }, [scatterChartSettings.showForecast]);
  const handleChangeMeasure = (updatedTitles: string[]) => {
    handleScatterChartSettingsUpdate((prevState) => {
      const { barColors = [], ...rest } = prevState;

      const updatedColors = barColors.filter((color) => updatedTitles.includes(color.measureId));
      const newColors = updatedTitles
        .filter((title) => !barColors.map((color) => color.measureId).includes(title))
        .map((title) => ({
          measureId: title,
          color: '#0baf28',
        }));
      const updatedDbMeasureIdToName: Record<string, string> = updatedTitles.reduce(
        (acc, title) => {
          acc[title] = metricsIdToName[title];
          return acc;
        },
        {} as Record<string, string>,
      );
      return {
        ...rest,
        barColors: [...updatedColors, ...newColors],
        selectedTitles: updatedTitles,
        dbMeasureIdToName: {
          ...prevState.dbMeasureIdToName,
          ...updatedDbMeasureIdToName,
        },
      };
    });
  };

  useEffect(() => {
    if (scatterChartSettings.mode === 'template') {
      const titles: Record<string, string> = {};
      scatterChartSettings.selectedTitles.forEach((title) => {
        titles[title] = metricsIdToName[title];
      });
      handleScatterChartSettingsUpdate((prevState) => ({
        ...prevState,
        dbMeasureIdToName: titles,
        title: {
          ...prevState.title,
          value: prevState.title.isVisible
            ? prevState.title.value
            : Object.values(titles).join(' Vs.'),
        },
      }));
    }
  }, [
    scatterChartSettings.mode,
    selectedDbMeasureIdToName,
    scatterChartSettings.selectedTitles,
    metricsIdToName,
  ]);
  const setOverRideSettings = (event: React.ChangeEvent<HTMLInputElement>, checked: boolean) => {
    const { name } = event.target;
    let sameplePeriod = scatterChartSettings.samplePeriod;
    if (name === 'globalSamplePeriod') {
      sameplePeriod = checked
        ? scatterChartSettings.samplePeriod
        : dashboardState.topPanel.samplePeriod;
    }
    let timeRange = scatterChartSettings.timeRange;
    if (name === 'overrideGlobalSettings') {
      timeRange = checked ? scatterChartSettings.timeRange : dashboardState.topPanel.timeRangeType;
    }
    handleScatterChartSettingsUpdate({
      ...scatterChartSettings,
      samplePeriod: sameplePeriod,
      timeRange: timeRange,
      [event.target.name]: checked,
    });
  };

  const handleBarColorChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    handleScatterChartSettingsUpdate(({ barColors, ...rest }) => {
      const { name, value } = e.target;
      const updatedColors = barColors.filter((color) => {
        return color.measureId !== name;
      });
      updatedColors.push({ measureId: name, color: value });
      return {
        ...rest,
        barColors: updatedColors,
      };
    });
  };
  const handleLegendYChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const legendY = Number(e.target.value);
    handleScatterChartSettingsUpdate({
      ...scatterChartSettings,
      legendY,
    });
  };
  const handleSelectedMeasureId = (e: SelectChangeEvent<string>) => {
    handleScatterChartSettingsUpdate({
      ...scatterChartSettings,
      selectedTitles: [e.target.value],
    });
  };
  const handleSelectedMeasureIdSingleSparkLine = (e: SelectChangeEvent<string>) => {
    handleScatterChartSettingsUpdate({
      ...scatterChartSettings,
      selectedSparkTitle: e.target.value,
    });
  };
  useEffect(() => {
    if (selectedMeasures.length > 1) {
      handleScatterChartSettingsUpdate({
        ...scatterChartSettings,
        showSparkLine: false,
      });
    }
  }, [selectedMeasures]);
  return (
    <Stack gap={1}>
      <DataWidgetSettingsContainer
        settings={scatterChartSettings}
        setSettings={handleScatterChartSettingsUpdate}
        dataTabChildren={
          <Box p={2} pl={0} pr={0} pb={0}>
            {scatterChartSettings.mode === 'dashboard' ? (
              <MultiMeasureSelectionMenu
                mode={scatterChartSettings.mode}
                settings={scatterChartSettings}
                setSettings={handleScatterChartSettingsUpdate as setMultiMeasureWidgetSettings}
              />
            ) : (
              <MultiMeasureSelection
                mode={scatterChartSettings.mode}
                handleChangeMeasure={handleChangeMeasure}
                selectedMeasureNames={scatterChartSettings.selectedTitles}
              />
            )}
          </Box>
        }
        feelTabChidren={
          <>
            {!scatterChartSettings?.showSparkLine ? (
              <>
                <Box>
                  <MeasureSettingSelector
                    selectedDbMeasureIdToName={scatterChartSettings.dbMeasureIdToName}
                    dbMeasureIdToSetting={dbMeasureIdToSetting}
                    handleDbMeasureIdToSettingUpdate={handleDbMeasureIdToSettingUpdate}
                    selectedMeasureNames={
                      scatterChartSettings.mode === 'template'
                        ? scatterChartSettings.selectedTitles
                        : selectedMeasures
                    }
                    dbMeasureIdToAnnotation={scatterChartSettings.dbMeasureIdToAnnotation}
                    handleDbMeasureIdToAnnotationUpdate={handleDbMeasureIdAnnotationToSettingUpdate}
                  />
                </Box>
                <Box>
                  <ChartLegendsSettings
                    ledendY={scatterChartSettings.legendY}
                    handleLegendYChange={handleLegendYChange}
                  />
                </Box>
              </>
            ) : null}
            <Box>
              <FormControlLabel
                control={
                  <Checkbox
                    checked={scatterChartSettings.showRangeSlider}
                    onChange={() => {
                      handleScatterChartSettingsUpdate({
                        ...scatterChartSettings,
                        showRangeSlider: !scatterChartSettings.showRangeSlider,
                      });
                    }}
                    name="showRangeSlider"
                  />
                }
                label="Show Range Slider"
              />
            </Box>
            {selectedMeasures.length === 1 ? (
              <Box>
                <FormControlLabel
                  control={
                    <Checkbox
                      checked={scatterChartSettings?.showSparkLine}
                      onChange={handleShowArea}
                      name="showSparkLine"
                    />
                  }
                  label="Show As Sparkline?"
                />
              </Box>
            ) : null}
            {scatterChartSettings?.showSparkLine ? (
              <>
                {/* <FormControl
              fullWidth
              sx={{
                p: 2,
                pt: 0,
              }}
            >
              <Box display="flex" alignItems="center" width="100%" gap={1}>
                <Select
                  labelId="y-select-lable"
                  id="heatmap-series"
                  fullWidth
                  label="Selected Metric"
                  input={
                    <OutlinedInput
                      label="Selected Metric"
                      sx={{
                        p: 0.2,
                        '& legend': {
                          maxWidth: '100%',
                          height: 'fit-content',
                          '& span': {
                            opacity: 1,
                          },
                        },
                      }}
                    />
                  }
                  value={scatterChartSettings?.selectedTitles?.[0]}
                  onChange={handleSelectedMeasureId}
                >
                  {Object.keys(selectedDbMeasureIdToName).map((dbMeasureId) => {
                    return (
                      <MenuItem key={dbMeasureId} value={dbMeasureId}>
                        {selectedDbMeasureIdToName[dbMeasureId]}
                      </MenuItem>
                    );
                  })}
                </Select>
              </Box>
            </FormControl> */}
              </>
            ) : (
              <>
                <Box>
                  <FormControlLabel
                    control={
                      <Checkbox
                        checked={scatterChartSettings?.showArea}
                        onChange={handleShowArea}
                        name="showArea"
                      />
                    }
                    label="Show As Area?"
                  />
                </Box>

                {scatterChartSettings.mode === 'dashboard' && selectedMeasures.length === 1 && (
                  <Box>
                    <ForecastSettings
                      settings={scatterChartSettings}
                      setSettings={handleScatterChartSettingsUpdate as setForecastWidgetSettings}
                    />
                  </Box>
                )}
                {scatterChartSettings.mode === 'template' &&
                  scatterChartSettings.selectedTitles.length === 1 && (
                    <Box>
                      <ForecastSettings
                        settings={scatterChartSettings}
                        setSettings={handleScatterChartSettingsUpdate as setForecastWidgetSettings}
                      />
                    </Box>
                  )}
                <Box>
                  <SumDeltaSettings
                    selectedTitles={
                      scatterChartSettings.mode === 'dashboard'
                        ? selectedMeasures
                        : scatterChartSettings.selectedTitles
                    }
                    settings={scatterChartSettings}
                    setSettings={handleScatterChartSettingsUpdate as setSumDeltaWidgetSettings}
                  />
                </Box>
                <Box>
                  <ChartThreshHold
                    settings={scatterChartSettings}
                    setSettings={handleScatterChartSettingsUpdate as setMultiMeasureChartSettings}
                  />
                </Box>
                <Box>
                  <FormControlLabel
                    control={
                      <Checkbox
                        checked={scatterChartSettings.showStacked}
                        onChange={handleShowStacked}
                        name="showStacked"
                      />
                    }
                    label="Show As Sub Plot?"
                  />
                  {scatterChartSettings.mode === 'dashboard' &&
                    scatterChartSettings.showStacked && (
                      <Box sx={{ display: 'flex', gap: 2 }}>
                        <FormControl fullWidth>
                          <Autocomplete
                            fullWidth
                            id={`asset-autocomplete`}
                            loading={isAssetReloading}
                            options={
                              !isAssetReloading
                                ? assetTypesWithPath.map((asset) => ({
                                    label: asset.label,
                                    id: asset.id,
                                  }))
                                : []
                            }
                            getOptionLabel={(option) => option.label}
                            onChange={(event, newValue) => {
                              setSelectedAsset(newValue);
                              setSelectedMeasure(null);
                              handleScatterChartSettingsUpdate((prevSettings) => ({
                                ...prevSettings,
                                selectedSparkMeasure: {
                                  assetId: newValue ? String(newValue.id) : '',
                                  measureId: '',
                                },
                                dbMeasureIdToName: {
                                  ...prevSettings.dbMeasureIdToName,
                                  [String(newValue?.id)]: newValue?.label ?? '',
                                },
                              }));
                            }}
                            value={selectedAsset}
                            renderInput={(params) => (
                              <TextField
                                {...params}
                                label="Select Asset"
                                variant="outlined"
                                InputProps={{
                                  ...params.InputProps,
                                  endAdornment: (
                                    <>
                                      {isAssetLoading ? (
                                        <CircularProgress color="inherit" size={20} />
                                      ) : null}
                                      {params.InputProps.endAdornment}
                                    </>
                                  ),
                                }}
                              />
                            )}
                          />
                        </FormControl>
                        {selectedAsset && (
                          <FormControl fullWidth>
                            <Autocomplete
                              fullWidth
                              id={`measurement-autocomplete`}
                              loading={isMeasurementLoading}
                              options={measurementOptions}
                              getOptionLabel={(option) => option.label}
                              onChange={(event, newValue) => {
                                setSelectedMeasure(newValue);
                                handleScatterChartSettingsUpdate((prevSettings) => ({
                                  ...prevSettings,
                                  selectedSparkMeasure: {
                                    assetId: prevSettings.selectedSparkMeasure?.assetId ?? '',
                                    measureId: newValue ? String(newValue.id) : '',
                                  },
                                  dbMeasureIdToName: {
                                    ...prevSettings.dbMeasureIdToName,
                                    [newValue ? String(newValue.id) : '']: newValue?.label ?? '',
                                  },
                                }));
                              }}
                              value={selectedMeasure}
                              renderInput={(params) => (
                                <TextField
                                  {...params}
                                  label="Select Measurement"
                                  variant="outlined"
                                  InputProps={{
                                    ...params.InputProps,
                                    endAdornment: (
                                      <>
                                        {isMeasurementLoading ? (
                                          <CircularProgress color="inherit" size={20} />
                                        ) : null}
                                        {params.InputProps.endAdornment}
                                      </>
                                    ),
                                  }}
                                />
                              )}
                            />
                          </FormControl>
                        )}
                      </Box>
                    )}
                  {scatterChartSettings.mode === 'template' && scatterChartSettings.showStacked && (
                    <Box sx={{ display: 'flex', gap: 2 }}>
                      <FormControl fullWidth>
                        <Select
                          label="Metric"
                          sx={{
                            width: '100%',
                            p: 0.3,
                            '& fieldset': {
                              '& legend': {
                                maxWidth: '100%',
                                height: 'auto',
                                '& span': {
                                  opacity: 1,
                                },
                              },
                            },
                          }}
                          value={selectedMetric ?? ''}
                          onChange={(e: SelectChangeEvent<string>) => {
                            if (!e.target.value) return;
                            setSelectedMetric(e.target.value);
                            handleScatterChartSettingsUpdate((prevSettings) => ({
                              ...prevSettings,
                              selectedSparkMeasure: {
                                assetId: prevSettings.selectedSparkMeasure?.assetId ?? '',
                                measureId: e.target.value,
                              },
                            }));
                          }}
                        >
                          {Object.keys(metricsIdToName).map((metricId) => {
                            return (
                              <MenuItem key={metricId} value={metricId}>
                                {metricsIdToName[metricId]}
                              </MenuItem>
                            );
                          })}
                        </Select>
                      </FormControl>
                    </Box>
                  )}
                  {/* {scatterChartSettings.showStacked && (
                <FormControl
                  fullWidth
                  sx={{
                    pt: 2,
                  }}
                >
                  <Box display="flex" alignItems="center" width="100%" gap={1}>
                    <Select
                      labelId="y-select-lable"
                      id="heatmap-series"
                      fullWidth
                      label="Selected Stacked"
                      input={
                        <OutlinedInput
                          label="Selected Stacked"
                          sx={{
                            p: 0.2,
                            '& legend': {
                              maxWidth: '100%',
                              height: 'fit-content',
                              '& span': {
                                opacity: 1,
                              },
                            },
                          }}
                        />
                      }
                      value={selectedSparkTitle}
                      onChange={handleSelectedMeasureIdSingleSparkLine}
                    >
                      {Object.keys(selectedDbMeasureIdToName).map((dbMeasureId) => {
                        return (
                          <MenuItem key={dbMeasureId} value={dbMeasureId}>
                            {selectedDbMeasureIdToName[dbMeasureId]}
                          </MenuItem>
                        );
                      })}
                    </Select>
                  </Box>
                </FormControl>
              )} */}
                </Box>
                {/* {!scatterChartSettings.showStacked && !scatterChartSettings.showForecast && ( */}
                <Box>
                  <ShowMinMax
                    selectedTitles={selectedMeasures}
                    settings={scatterChartSettings}
                    setSettings={handleScatterChartSettingsUpdate as setMultiMeasureChartSettings}
                  />
                </Box>
                {/* )} */}
              </>
            )}
            {scatterChartSettings.mode === 'dashboard' ? (
              <MeasureColorSelector
                selectedDbMeasureIdToName={scatterChartSettings.dbMeasureIdToName}
                selectedMeasureNames={selectedMeasures}
                settings={scatterChartSettings}
                handleBarColorChange={handleBarColorChange}
                overrideGlobalBarColor={scatterChartSettings.overrideGlobalBarColor}
                setOverRideSettings={setOverRideSettings}
              />
            ) : (
              <MeasureColorSelector
                selectedDbMeasureIdToName={scatterChartSettings.dbMeasureIdToName}
                selectedMeasureNames={scatterChartSettings.selectedTitles}
                settings={scatterChartSettings}
                handleBarColorChange={handleBarColorChange}
                overrideGlobalBarColor={scatterChartSettings.overrideGlobalBarColor}
                setOverRideSettings={setOverRideSettings}
              />
            )}
          </>
        }
      />
    </Stack>
  );
}
