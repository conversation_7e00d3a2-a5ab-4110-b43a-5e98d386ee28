import {
  BasePositionModelOptions,
  DefaultNodeModel,
  DefaultNodeModelOptions,
  DeserializeEvent,
  NodeModel,
  NodeModelGenerics,
} from '@projectstorm/react-diagrams';
import { CustomPortModel } from './CustomPortModel';
import { CustomType } from './CustomType';

export interface CustomNodeModelGenerics {
  PORT: CustomPortModel;
}
export interface CustomNodeModelOptions extends BasePositionModelOptions {
  name?: string;
  value?: number;
}
export interface CustomNodeModelGenerics extends NodeModelGenerics {
  OPTIONS: CustomNodeModelOptions;
}
export class CustomNodeModel extends NodeModel<CustomNodeModelGenerics> {
  constructor(name: string, type: CustomType, value?: number, options?: CustomNodeModelOptions) {
    super({
      type: type,
      name: name,
      value: value,
      ...(options ? options : {}),
    });
  }

  deserialize(event: DeserializeEvent<this>): void {
    super.deserialize(event);
    const data = event.data;
    this.options.name = data.name;
    this.options.value = data.value;
  }

  serialize(): any {
    return {
      ...super.serialize(),
      name: this.options.name,
      value: this.options.value, // Include the value property in serialization
    };
  }
}
