// src/slices/customersApi.ts
import { authApi } from '~/redux/api/authApi';
import {
  Customer,
  CustomerCollection,
  CustomerDto,
  EditCustomer,
  NewCustomer,
} from '~/types/customers';
import {
  CustomerImageDTO,
  EditUserDto,
  UserCollection,
  UserDetailDto,
  customerImage,
  searchUser,
} from '~/types/users';

// Helper function to map DTO to domain
function mapDtoToDomain(customerDto: CustomerDto): Customer {
  const { name_id: nameId, ...rest } = customerDto;
  return { nameId, ...rest };
}

export const customersApi = authApi
  .enhanceEndpoints({
    addTagTypes: ['Customer', 'Customer-images', 'image'],
  })
  .injectEndpoints({
    endpoints: (builder) => ({
      createCustomer: builder.mutation<Customer, NewCustomer>({
        query: (newCustomer) => {
          const { nameId: name_id, ...rest } = newCustomer;
          return {
            url: '/v0/customers',
            method: 'POST',
            body: { name_id, ...rest },
          };
        },
        transformResponse: (response: CustomerDto) => {
          return mapDtoToDomain(response);
        },
        invalidatesTags: [{ type: 'Customer' }],
      }),
      getCustomers: builder.query<Customer[], { is_logo?: boolean }>({
        query: ({ is_logo }) => `/v0/customers?is_logo=${is_logo ? true : false}`,
        transformResponse: (response: CustomerCollection) => response.items.map(mapDtoToDomain),
        providesTags: () => [{ type: 'Customer' }],
      }),
      editCustomerById: builder.mutation<void, { customerId: number; userDetails: EditCustomer }>({
        query: ({ customerId, userDetails }) => {
          return {
            url: `/v0/customers/${customerId}`,
            method: 'PATCH',
            body: { ...userDetails },
          };
        },
      }),
      getCustomerByNameId: builder.query<Customer | null, string>({
        query: (nameId) => `/v0/customers/${nameId}`,
        providesTags: (result, error, nameId) => [
          { type: 'Customer' },
          { type: 'Customer', id: nameId },
        ],
        transformResponse: (response: CustomerDto) => mapDtoToDomain(response),
      }),
      getCustomerUsersById: builder.mutation<UserCollection, searchUser>({
        query: ({ customer_id, customer_name, email, role, user_name }) => {
          const query = new URLSearchParams();
          if (customer_id) {
            query.set('customer_id', customer_id.toString());
          }
          if (customer_name) query.set('customer_name', customer_name);
          if (email) query.set('email', email);
          if (role) query.set('role', role);
          if (user_name) query.set('user_name', user_name);
          return {
            url: `/v0/users?${query.toString()}`,
            method: 'GET',
          };
        },
        transformResponse: (response: UserCollection) => {
          response.items.map((item) => {
            if (!item.scoped_roles) {
              item.scoped_roles = [];
            }
            return item;
          });
          return response;
        },
      }),
      getCustomerUserDetailsById: builder.query<UserDetailDto, { id: number }>({
        query: ({ id }) => `/v0/users/${id}`,
        transformResponse: (response: UserDetailDto) => {
          if (response.scoped_roles === null) {
            response.scoped_roles = [];
          }
          return response;
        },
      }),
      getCustomerImages: builder.query<CustomerImageDTO, { id: number }>({
        query: ({ id }) => `/v0/customers/${id}/custom-image`,
        providesTags: () => [{ type: 'Customer-images' }],
      }),
      getImage: builder.query<customerImage, { id: number; imageId: string }>({
        query: ({ id, imageId }) => `/v0/customers/${id}/custom-image/${imageId}`,
        providesTags: () => [{ type: 'image' }],
      }),
      createCustomerImages: builder.mutation<
        { id: number; logo: string },
        { id: number; image: string }
      >({
        query: ({ id, image }) => {
          return {
            url: `/v0/customers/${id}/custom-image`,
            method: 'POST',
            body: { image: image },
          };
        },
        invalidatesTags: [{ type: 'Customer-images' }],
      }),
      editCustomerUsersById: builder.mutation<void, { userId: number; userDetails: EditUserDto }>({
        query: ({ userId, userDetails }) => {
          const { email, ...rest } = userDetails;
          return {
            url: `/v0/users/${userId}`,
            method: 'PATCH',
            body: { ...rest },
          };
        },
      }),
      deleteCustomerById: builder.mutation<void, number>({
        query: (customerId) => ({ url: `/v0/customers/${customerId}`, method: 'DELETE' }),
        invalidatesTags: [{ type: 'Customer' }],
      }),
    }),
    // extraOptions: {
    //   onError: (error: Error, { endpointName }) => {
    //     if (error instanceof FetchBaseQueryError) {
    //       if (error.status === 400 && endpointName === 'createCustomer' && error.data) {
    //         throw new InvalidInputException(error.data.message);
    //       }
    //     }
    //   },
    // },
  });

export const {
  useGetCustomersQuery,
  useCreateCustomerMutation,
  useDeleteCustomerByIdMutation,
  useGetCustomerUsersByIdMutation,
  useEditCustomerUsersByIdMutation,
  useEditCustomerByIdMutation,
  useGetCustomerByNameIdQuery,
  useGetCustomerUserDetailsByIdQuery,
  useGetCustomerImagesQuery,
  useCreateCustomerImagesMutation,
  useGetImageQuery,
} = customersApi;
