   Linting and checking validity of types ...

Failed to compile.

./src/components/ActiveCustomers/ActiveCustomers.tsx
42:24  Warning: 'setAlertMessage' is assigned a value but never used.  @typescript-eslint/no-unused-vars
62:16  Warning: 'isLoadingDashboards' is assigned a value but never used.  @typescript-eslint/no-unused-vars
134:6  Warning: React Hook useEffect has missing dependencies: 'refetch', 'showErrorAlert', and 'showSuccessAlert'. Either include them or remove the dependency array.  react-hooks/exhaustive-deps
145:6  Warning: React Hook useEffect has missing dependencies: 'refetch', 'showErrorAlert', and 'showSuccessAlert'. Either include them or remove the dependency array.  react-hooks/exhaustive-deps
174:13  Warning: Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element  @next/next/no-img-element
175:36  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
224:51  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
269:21  Warning: Forbidden non-null assertion.  @typescript-eslint/no-non-null-assertion

./src/components/AlertAnalytics/AlertAnalytics.tsx
21:8  Warning: 'HomeButton' is defined but never used.  @typescript-eslint/no-unused-vars
155:6  Warning: React Hook useMemo has a missing dependency: 'interval'. Either include it or remove the dependency array.  react-hooks/exhaustive-deps
290:6  Warning: React Hook useMemo has missing dependencies: 'getAggsGroupedCounts', 'getAssetTypeGroupedCounts', 'getCounts', 'getDurations', 'getEnabledDisabledGroupedCounts', 'getLabels', 'getMTypeGroupedCounts', 'getStatesGroupedCounts', 'getThresholdTypeGroupedCounts', and 'getTimeDurations'. Either include them or remove the dependency array.  react-hooks/exhaustive-deps
291:9  Warning: 'getFilteredData' is assigned a value but never used.  @typescript-eslint/no-unused-vars

./src/components/Alerts/AlertsList.tsx
4:8  Warning: 'QueryStatsIcon' is defined but never used.  @typescript-eslint/no-unused-vars
69:6  Warning: React Hook useEffect has an unnecessary dependency: 'status'. Either exclude it or remove the dependency array. Outer scope values like 'status' aren't valid dependencies because mutating them doesn't re-render the component.  react-hooks/exhaustive-deps
78:10  Warning: 'hideInfo' is assigned a value but never used.  @typescript-eslint/no-unused-vars
78:20  Warning: 'setHideInfo' is assigned a value but never used.  @typescript-eslint/no-unused-vars
149:13  Warning: Forbidden non-null assertion.  @typescript-eslint/no-non-null-assertion
209:6  Warning: React Hook useEffect has missing dependencies: 'showErrorAlert' and 'showSuccessAlert'. Either include them or remove the dependency array.  react-hooks/exhaustive-deps
218:6  Warning: React Hook useEffect has missing dependencies: 'refetch', 'showErrorAlert', and 'showSuccessAlert'. Either include them or remove the dependency array.  react-hooks/exhaustive-deps
1100:33  Warning: Forbidden non-null assertion.  @typescript-eslint/no-non-null-assertion

./src/components/Alerts/edit/EditAlert.tsx
50:6  Warning: React Hook useEffect has a missing dependency: 'getCustomerUser'. Either include it or remove the dependency array.  react-hooks/exhaustive-deps
52:51  Warning: 'getValues' is assigned a value but never used.  @typescript-eslint/no-unused-vars
118:6  Warning: React Hook useEffect has a missing dependency: 'setValue'. Either include it or remove the dependency array.  react-hooks/exhaustive-deps

./src/components/AlertSetting/AlertWidgetSettingsDialogs.tsx
70:6  Warning: React Hook useEffect has missing dependencies: 'errorAlerts', 'handleSettingsChange', and 'settings'. Either include them or remove the dependency array. If 'handleSettingsChange' changes too often, find the parent component that defines it and wrap that definition in useCallback.  react-hooks/exhaustive-deps
95:6  Warning: React Hook useMemo has an unnecessary dependency: 'settings.mode'. Either exclude it or remove the dependency array.  react-hooks/exhaustive-deps

./src/components/AlertStats/AlertStatsData.tsx
1:33  Warning: 'Paper' is defined but never used.  @typescript-eslint/no-unused-vars

./src/components/AssetTemplate/AssetInstanceMeasureTable.tsx
55:3  Warning: 'fields' is defined but never used.  @typescript-eslint/no-unused-vars
57:3  Warning: 'datasourceOptions' is defined but never used.  @typescript-eslint/no-unused-vars
58:3  Warning: 'locationsListOption' is defined but never used.  @typescript-eslint/no-unused-vars

./src/components/AssetTemplate/AssetMeasurements.tsx
21:45  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/components/AssetTemplate/AssetMeasureTable.tsx
53:3  Warning: 'datasourceOptions' is defined but never used.  @typescript-eslint/no-unused-vars

./src/components/AssetTemplate/AssetsStepper.tsx
55:24  Warning: 'admin' is assigned a value but never used.  @typescript-eslint/no-unused-vars
152:6  Warning: React Hook useEffect has missing dependencies: 'reset', 'resetCalcMeasureValues', 'resetMeasure', 'setCalcMeasurements', and 'setMeasureValue'. Either include them or remove the dependency array.  react-hooks/exhaustive-deps
224:6  Warning: React Hook useEffect has missing dependencies: 'calcMeasureSetValue', 'calcMeasurements', 'currentCalcMeasureIndex', 'measurements', and 'setCalcMeasurements'. Either include them or remove the dependency array.  react-hooks/exhaustive-deps

./src/components/AssetTemplate/AssetTemplateFileImporter.tsx
50:52  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
53:59  Warning: 'createAssetTemplateData' is assigned a value but never used.  @typescript-eslint/no-unused-vars
81:28  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
84:6  Warning: React Hook useEffect has missing dependencies: 'setImportData', 'showErrorAlert', and 'showSuccessAlert'. Either include them or remove the dependency array. If 'setImportData' changes too often, find the parent component that defines it and wrap that definition in useCallback.  react-hooks/exhaustive-deps
120:33  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/components/AssetTemplate/AssetTemplateForm.tsx
63:5  Warning: 'createAssetTemplate' is assigned a value but never used.  @typescript-eslint/no-unused-vars
64:16  Warning: 'isLoading' is assigned a value but never used.  @typescript-eslint/no-unused-vars
115:39  Warning: 'data' is defined but never used.  @typescript-eslint/no-unused-vars

./src/components/AssetTemplate/AssetTemplatesList.tsx
5:3  Warning: 'FileDownload' is defined but never used.  @typescript-eslint/no-unused-vars
71:43  Warning: 'showErrorAlert' is assigned a value but never used.  @typescript-eslint/no-unused-vars
80:74  Warning: 'i' is defined but never used.  @typescript-eslint/no-unused-vars

./src/components/AssetTemplate/AssetTemplateStepper.tsx
29:31  Warning: 'setCurrentMeasureIndex' is assigned a value but never used.  @typescript-eslint/no-unused-vars
58:9  Warning: 'handleComplete' is assigned a value but never used.  @typescript-eslint/no-unused-vars
65:9  Warning: 'handleReset' is assigned a value but never used.  @typescript-eslint/no-unused-vars

./src/components/AssetTemplate/CloneAssetTemplateForm.tsx
62:21  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/components/AssetTemplate/EditAssetTemplateForm.tsx
70:69  Warning: 'error' is assigned a value but never used.  @typescript-eslint/no-unused-vars
98:7  Warning: 'calcMeasureGetValues' is assigned a value but never used.  @typescript-eslint/no-unused-vars
181:6  Warning: React Hook useEffect has missing dependencies: 'calcMeasureSetValue', 'calcMeasurements', 'currentCalcMeasureIndex', 'fields', and 'setCalcMeasurements'. Either include them or remove the dependency array.  react-hooks/exhaustive-deps
281:6  Warning: React Hook useEffect has missing dependencies: 'append', 'measurementIds', and 'setValue'. Either include them or remove the dependency array.  react-hooks/exhaustive-deps
926:31  Warning: Forbidden non-null assertion.  @typescript-eslint/no-non-null-assertion

./src/components/AssetTemplate/SearchAssetTemplates.tsx
57:3  Warning: 'selectedTemplate' is defined but never used.  @typescript-eslint/no-unused-vars
58:3  Warning: 'showMeasure' is defined but never used.  @typescript-eslint/no-unused-vars
70:10  Warning: 'snackbarState' is assigned a value but never used.  @typescript-eslint/no-unused-vars
70:43  Warning: 'showErrorAlert' is assigned a value but never used.  @typescript-eslint/no-unused-vars
81:55  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
81:60  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
81:65  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
173:10  Warning: 'defaultAssetType' is assigned a value but never used.  @typescript-eslint/no-unused-vars
191:6  Warning: React Hook useEffect has an unnecessary dependency: 'status'. Either exclude it or remove the dependency array. Outer scope values like 'status' aren't valid dependencies because mutating them doesn't re-render the component.  react-hooks/exhaustive-deps
240:74  Warning: 'i' is defined but never used.  @typescript-eslint/no-unused-vars

./src/components/AssetTemplateInstance/CreateAssetTemplateInstance/CreateAssetTemplateInstance.tsx
74:17  Warning: 'customersList' is assigned a value but never used.  @typescript-eslint/no-unused-vars
107:6  Warning: React Hook useEffect has missing dependencies: 'assetTypesWithPath' and 'setAssetTemplateValue'. Either include them or remove the dependency array.  react-hooks/exhaustive-deps
120:6  Warning: React Hook useEffect has missing dependencies: 'assetTemplateId' and 'setAssetTemplateValue'. Either include them or remove the dependency array.  react-hooks/exhaustive-deps
127:18  Warning: 'errors' is assigned a value but never used.  @typescript-eslint/no-unused-vars
150:57  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
150:62  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
182:6  Warning: React Hook useEffect has missing dependencies: 'dispatch' and 'measurements.length'. Either include them or remove the dependency array.  react-hooks/exhaustive-deps
238:10  Warning: 'isFirst' is assigned a value but never used.  @typescript-eslint/no-unused-vars
262:6  Warning: React Hook useEffect has a missing dependency: 'setAssetTemplateValue'. Either include it or remove the dependency array.  react-hooks/exhaustive-deps
304:6  Warning: React Hook useEffect has missing dependencies: 'getMeasureValues' and 'setMeasureValue'. Either include them or remove the dependency array.  react-hooks/exhaustive-deps
322:15  Warning: 'data' is assigned a value but never used.  @typescript-eslint/no-unused-vars
386:21  Warning: 'tag' is assigned a value but never used.  @typescript-eslint/no-unused-vars
445:6  Warning: React Hook useEffect has missing dependencies: 'resetAssetTemplate' and 'resetMeasure'. Either include them or remove the dependency array.  react-hooks/exhaustive-deps
523:51  Warning: 'data' is defined but never used.  @typescript-eslint/no-unused-vars
730:73  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/components/AssetToTemplate/AssetToTemplate.tsx
16:5  Warning: 'assetTypeListData' is assigned a value but never used.  @typescript-eslint/no-unused-vars
25:9  Warning: 'assetTypeOption' is assigned a value but never used.  @typescript-eslint/no-unused-vars

./src/components/AssetToTemplate/AssetToTemplateForm/AssetToTemplateForm.tsx
55:24  Warning: 'admin' is assigned a value but never used.  @typescript-eslint/no-unused-vars
76:5  Warning: 'existingMeasurementMetrics' is assigned a value but never used.  @typescript-eslint/no-unused-vars
77:5  Warning: 'setExistingMeasurementMetrics' is assigned a value but never used.  @typescript-eslint/no-unused-vars
100:7  Warning: 'measurementGetValues' is assigned a value but never used.  @typescript-eslint/no-unused-vars
108:7  Warning: 'calcMeasureGetValues' is assigned a value but never used.  @typescript-eslint/no-unused-vars
337:6  Warning: React Hook useEffect has missing dependencies: 'calcMeasureSetValue', 'calcMeasurements', 'currentCalcMeasureIndex', 'fields', and 'setCalcMeasurements'. Either include them or remove the dependency array.  react-hooks/exhaustive-deps
414:39  Warning: 'data' is defined but never used.  @typescript-eslint/no-unused-vars

./src/components/AssetTypes/AssetTypesListPage.tsx
38:18  Warning: 'editLoading' is assigned a value but never used.  @typescript-eslint/no-unused-vars
54:6  Warning: React Hook useEffect has missing dependencies: 'showErrorAlert' and 'showSuccessAlert'. Either include them or remove the dependency array.  react-hooks/exhaustive-deps
64:6  Warning: React Hook useEffect has missing dependencies: 'showErrorAlert' and 'showSuccessAlert'. Either include them or remove the dependency array.  react-hooks/exhaustive-deps
107:6  Warning: React Hook useEffect has a missing dependency: 'setValue'. Either include it or remove the dependency array.  react-hooks/exhaustive-deps

./src/components/CalcEngine/ExpressionBuilder.tsx
83:9  Warning: 'handleVariableChange' is assigned a value but never used.  @typescript-eslint/no-unused-vars
91:9  Warning: 'handleVariableTypeChange' is assigned a value but never used.  @typescript-eslint/no-unused-vars
97:9  Warning: 'handleCommentsChange' is assigned a value but never used.  @typescript-eslint/no-unused-vars
105:66  Warning: 'child' is defined but never used.  @typescript-eslint/no-unused-vars
108:9  Warning: 'handleVariableMeasureChange' is assigned a value but never used.  @typescript-eslint/no-unused-vars
167:56  Warning: 'index' is defined but never used.  @typescript-eslint/no-unused-vars

./src/components/CalcEngine/ExpressionTemplateSelector.tsx
46:6  Warning: React Hook useEffect has a missing dependency: 'expressionTemplates'. Either include it or remove the dependency array.  react-hooks/exhaustive-deps
55:6  Warning: React Hook useEffect has a missing dependency: 'setExpressionTemplate'. Either include it or remove the dependency array. If 'setExpressionTemplate' changes too often, find the parent component that defines it and wrap that definition in useCallback.  react-hooks/exhaustive-deps
56:66  Warning: 'child' is defined but never used.  @typescript-eslint/no-unused-vars

./src/components/Chart.tsx
145:9  Warning: 'mobile' is assigned a value but never used.  @typescript-eslint/no-unused-vars
149:57  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
149:62  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
187:9  Warning: 'selectedNodeIds' is assigned a value but never used.  @typescript-eslint/no-unused-vars
236:49  Warning: The ref value 'containerRef.current' will likely have changed by the time this effect cleanup function runs. If this ref points to a node rendered by React, copy 'containerRef.current' to a variable inside the effect, and use that variable in the cleanup function.  react-hooks/exhaustive-deps
240:6  Warning: React Hook useEffect has missing dependencies: 'dimensions.height' and 'dimensions.width'. Either include them or remove the dependency array.  react-hooks/exhaustive-deps
274:17  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/components/ChartSettings/BarChartSettings.tsx
89:6  Warning: React Hook useEffect has a missing dependency: 'handleBarChartSettingsUpdate'. Either include it or remove the dependency array. If 'handleBarChartSettingsUpdate' changes too often, find the parent component that defines it and wrap that definition in useCallback.  react-hooks/exhaustive-deps

./src/components/ChartSettings/BulletChartSettings.tsx
17:3  Warning: 'selectedDbMeasureIdToName' is defined but never used.  @typescript-eslint/no-unused-vars

./src/components/ChartSettings/ChartSettingDialog.tsx
60:21  Warning: 'setChartAxis' is assigned a value but never used.  @typescript-eslint/no-unused-vars
273:9  Warning: 'handleTitleChange' is assigned a value but never used.  @typescript-eslint/no-unused-vars

./src/components/ChartSettings/GaugeChartSettings.tsx
17:3  Warning: 'selectedDbMeasureIdToName' is defined but never used.  @typescript-eslint/no-unused-vars

./src/components/ChartSettings/HeatmapChartSettings.tsx
23:3  Warning: 'selectedDbMeasureIdToName' is defined but never used.  @typescript-eslint/no-unused-vars

./src/components/ChartSettings/SankeyChartSettings.tsx
85:6  Warning: React Hook useEffect has a missing dependency: 'handleSettings'. Either include it or remove the dependency array. If 'handleSettings' changes too often, find the parent component that defines it and wrap that definition in useCallback.  react-hooks/exhaustive-deps

./src/components/ChartSettings/ScatterChartSettings.tsx
60:33  Warning: 'selectedSparkTitle' is assigned a value but never used.  @typescript-eslint/no-unused-vars
72:12  Warning: 'assetError' is assigned a value but never used.  @typescript-eslint/no-unused-vars
112:6  Warning: React Hook useEffect has a missing dependency: 'handleScatterChartSettingsUpdate'. Either include it or remove the dependency array. If 'handleScatterChartSettingsUpdate' changes too often, find the parent component that defines it and wrap that definition in useCallback.  react-hooks/exhaustive-deps
226:6  Warning: React Hook useEffect has a missing dependency: 'handleScatterChartSettingsUpdate'. Either include it or remove the dependency array. If 'handleScatterChartSettingsUpdate' changes too often, find the parent component that defines it and wrap that definition in useCallback.  react-hooks/exhaustive-deps
274:6  Warning: React Hook useEffect has a missing dependency: 'handleScatterChartSettingsUpdate'. Either include it or remove the dependency array. If 'handleScatterChartSettingsUpdate' changes too often, find the parent component that defines it and wrap that definition in useCallback.  react-hooks/exhaustive-deps
320:9  Warning: 'handleSelectedMeasureId' is assigned a value but never used.  @typescript-eslint/no-unused-vars
326:9  Warning: 'handleSelectedMeasureIdSingleSparkLine' is assigned a value but never used.  @typescript-eslint/no-unused-vars
339:6  Warning: React Hook useEffect has missing dependencies: 'handleScatterChartSettingsUpdate' and 'scatterChartSettings'. Either include them or remove the dependency array. If 'handleScatterChartSettingsUpdate' changes too often, find the parent component that defines it and wrap that definition in useCallback.  react-hooks/exhaustive-deps

./src/components/ChartSettings/StatsWidgetSettingDialog.tsx
74:65  Warning: 'child' is defined but never used.  @typescript-eslint/no-unused-vars

./src/components/common/AssetMeasure.tsx
39:57  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
102:37  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
111:44  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/components/common/ChartLegendsSettings.tsx
3:3  Warning: 'FormLabel' is defined but never used.  @typescript-eslint/no-unused-vars
4:3  Warning: 'TextField' is defined but never used.  @typescript-eslint/no-unused-vars
8:3  Warning: 'Typography' is defined but never used.  @typescript-eslint/no-unused-vars
10:3  Warning: 'FormHelperText' is defined but never used.  @typescript-eslint/no-unused-vars

./src/components/common/ChartThreshHold.tsx
21:5  Warning: 'checked' is defined but never used.  @typescript-eslint/no-unused-vars

./src/components/common/CommonRealTimeSettings.tsx
29:71  Warning: 'child' is defined but never used.  @typescript-eslint/no-unused-vars

./src/components/common/CommonWidgetContainer.tsx
79:3  Warning: 'removedResults' is defined but never used.  @typescript-eslint/no-unused-vars
80:3  Warning: 'successfulResults' is defined but never used.  @typescript-eslint/no-unused-vars
158:21  Warning: 'id' is defined but never used.  @typescript-eslint/no-unused-vars

./src/components/common/ConfirmChange/ConfirmChange.tsx
13:13  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
14:24  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
15:14  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
16:25  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
26:3  Warning: 'setDashboardCustomer' is defined but never used.  @typescript-eslint/no-unused-vars

./src/components/common/CreateDashboard/CreateDashboard.tsx
31:11  Warning: 'globalAdmin' is assigned a value but never used.  @typescript-eslint/no-unused-vars
31:24  Warning: 'admin' is assigned a value but never used.  @typescript-eslint/no-unused-vars

./src/components/common/CustomSettingsDialog.tsx
22:3  Warning: 'title' is defined but never used.  @typescript-eslint/no-unused-vars
26:3  Warning: 'onClose' is defined but never used.  @typescript-eslint/no-unused-vars
27:3  Warning: 'dialogActions' is defined but never used.  @typescript-eslint/no-unused-vars
29:10  Warning: 'isOpen' is assigned a value but never used.  @typescript-eslint/no-unused-vars
30:9  Warning: 'handleDialogOpen' is assigned a value but never used.  @typescript-eslint/no-unused-vars
32:20  Warning: 'setSettings' is assigned a value but never used.  @typescript-eslint/no-unused-vars

./src/components/common/CustomWidgetMeasureSettingsSelector.tsx
37:16  Warning: 'setUnit' is assigned a value but never used.  @typescript-eslint/no-unused-vars

./src/components/common/DashboardsCrumb/DashboardCrumbs.tsx
18:57  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
18:62  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
335:6  Warning: React Hook useEffect has a missing dependency: 'dispatch'. Either include it or remove the dependency array.  react-hooks/exhaustive-deps

./src/components/common/DataTable/DataTable.tsx
17:51  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
19:18  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/components/common/DataWidgetContainer.tsx
107:16  Warning: 'isAssetTypeLoading' is assigned a value but never used.  @typescript-eslint/no-unused-vars
133:6  Warning: React Hook useEffect has missing dependencies: 'setSettings' and 'settings'. Either include them or remove the dependency array. If 'setSettings' changes too often, find the parent component that defines it and wrap that definition in useCallback.  react-hooks/exhaustive-deps
171:6  Warning: React Hook useEffect has missing dependencies: 'setSettings' and 'settings'. Either include them or remove the dependency array. If 'setSettings' changes too often, find the parent component that defines it and wrap that definition in useCallback.  react-hooks/exhaustive-deps
222:6  Warning: React Hook useEffect has a missing dependency: 'setSettings'. Either include it or remove the dependency array. If 'setSettings' changes too often, find the parent component that defines it and wrap that definition in useCallback.  react-hooks/exhaustive-deps
417:53  Warning: Forbidden non-null assertion.  @typescript-eslint/no-non-null-assertion
451:73  Warning: Forbidden non-null assertion.  @typescript-eslint/no-non-null-assertion

./src/components/common/DataWidgetSettingsContainer/DataWidgetSettingsContainer.tsx
27:10  Warning: 'isThereAssetMeasure' is assigned a value but never used.  @typescript-eslint/no-unused-vars
217:6  Warning: React Hook useEffect has missing dependencies: 'exculdedSettings', 'setSettings', and 'settings'. Either include them or remove the dependency array. If 'setSettings' changes too often, find the parent component that defines it and wrap that definition in useCallback.  react-hooks/exhaustive-deps
228:5  Warning: React Hook useEffect has a complex expression in the dependency array. Extract it to a separate variable so it can be statically checked.  react-hooks/exhaustive-deps
228:18  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
229:5  Warning: React Hook useEffect has a complex expression in the dependency array. Extract it to a separate variable so it can be statically checked.  react-hooks/exhaustive-deps
229:18  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
230:5  Warning: React Hook useEffect has a complex expression in the dependency array. Extract it to a separate variable so it can be statically checked.  react-hooks/exhaustive-deps
230:18  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
231:5  Warning: React Hook useEffect has a complex expression in the dependency array. Extract it to a separate variable so it can be statically checked.  react-hooks/exhaustive-deps
231:18  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
232:5  Warning: React Hook useEffect has a complex expression in the dependency array. Extract it to a separate variable so it can be statically checked.  react-hooks/exhaustive-deps
232:18  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
233:5  Warning: React Hook useEffect has a complex expression in the dependency array. Extract it to a separate variable so it can be statically checked.  react-hooks/exhaustive-deps
233:18  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
234:5  Warning: React Hook useEffect has a complex expression in the dependency array. Extract it to a separate variable so it can be statically checked.  react-hooks/exhaustive-deps
234:18  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
235:5  Warning: React Hook useEffect has a complex expression in the dependency array. Extract it to a separate variable so it can be statically checked.  react-hooks/exhaustive-deps
235:18  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
236:5  Warning: React Hook useEffect has a complex expression in the dependency array. Extract it to a separate variable so it can be statically checked.  react-hooks/exhaustive-deps
236:18  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
237:5  Warning: React Hook useEffect has a complex expression in the dependency array. Extract it to a separate variable so it can be statically checked.  react-hooks/exhaustive-deps
237:18  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/components/common/DataWidgetSettingsContainer/WidgetDataSettingsTabContainer.tsx
57:57  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
57:62  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
59:10  Warning: 'snackbarState' is assigned a value but never used.  @typescript-eslint/no-unused-vars
59:25  Warning: 'showSuccessAlert' is assigned a value but never used.  @typescript-eslint/no-unused-vars
59:43  Warning: 'showErrorAlert' is assigned a value but never used.  @typescript-eslint/no-unused-vars
93:16  Warning: 'isAssetTypeLoading' is assigned a value but never used.  @typescript-eslint/no-unused-vars
627:47  Warning: Forbidden non-null assertion.  @typescript-eslint/no-non-null-assertion
660:67  Warning: Forbidden non-null assertion.  @typescript-eslint/no-non-null-assertion

./src/components/common/DataWidgetSettingsContainer/WidgetLookFeelContainer.tsx
60:13  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/components/common/DataWidgetSettingsContainer/WidgetTimeContext.tsx
1:10  Warning: 'Box' is defined but never used.  @typescript-eslint/no-unused-vars
25:3  Warning: 'children' is defined but never used.  @typescript-eslint/no-unused-vars
32:10  Warning: 'assetTypesWithPath' is assigned a value but never used.  @typescript-eslint/no-unused-vars
37:10  Warning: 'assetToAssetType' is assigned a value but never used.  @typescript-eslint/no-unused-vars
39:40  Warning: 'isAssetReloading' is assigned a value but never used.  @typescript-eslint/no-unused-vars
49:16  Warning: 'isAssetTypeLoading' is assigned a value but never used.  @typescript-eslint/no-unused-vars

./src/components/common/DataWidgetSettingsExceptional/DataWidgetSettingsExceptional.tsx
71:3  Warning: 'children' is defined but never used.  @typescript-eslint/no-unused-vars
89:63  Warning: 'child' is defined but never used.  @typescript-eslint/no-unused-vars

./src/components/common/DeleteWidgetDialog.tsx
13:41  Warning: 'widgetName' is defined but never used.  @typescript-eslint/no-unused-vars

./src/components/common/DialogTransition.tsx
7:34  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
7:39  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/components/common/FavouriteTabs/FavouriteTabs.tsx
107:13  Warning: 'editDashboard' is assigned a value but never used.  @typescript-eslint/no-unused-vars
110:18  Warning: 'isEditLoading' is assigned a value but never used.  @typescript-eslint/no-unused-vars
122:41  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
127:6  Warning: React Hook useEffect has missing dependencies: 'error', 'showErrorAlert', and 'showSuccessAlert'. Either include them or remove the dependency array.  react-hooks/exhaustive-deps

./src/components/common/Home/HomeButton.tsx
9:16  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/components/common/LeftMenu/LeftMenuDrawer.tsx
9:8  Warning: 'MenuIcon' is defined but never used.  @typescript-eslint/no-unused-vars
78:9  Warning: 'LeftPanelOpen' is assigned a value but never used.  @typescript-eslint/no-unused-vars
101:29  Warning: 'isLoading' is assigned a value but never used.  @typescript-eslint/no-unused-vars
153:10  Warning: 'settingAnchorEl' is assigned a value but never used.  @typescript-eslint/no-unused-vars
154:9  Warning: 'handleSettingMenu' is assigned a value but never used.  @typescript-eslint/no-unused-vars
184:9  Warning: 'setTopPanelVisibility' is assigned a value but never used.  @typescript-eslint/no-unused-vars
191:9  Warning: 'onLayoutChangeToKiosk' is assigned a value but never used.  @typescript-eslint/no-unused-vars

./src/components/common/MeasueColorSelector.tsx
3:3  Warning: 'Card' is defined but never used.  @typescript-eslint/no-unused-vars

./src/components/common/MeasureSettingSelector.tsx
117:75  Warning: 'name' is defined but never used.  @typescript-eslint/no-unused-vars

./src/components/common/MultiMeasureSelect.tsx
58:37  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
96:37  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/components/common/MultiMeasureSelection.tsx
2:10  Warning: 'useEffect' is defined but never used.  @typescript-eslint/no-unused-vars
46:37  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
84:37  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/components/common/MultiMeasureSelectionMenu.tsx
2:23  Warning: 'IconButton' is defined but never used.  @typescript-eslint/no-unused-vars
2:35  Warning: 'Tooltip' is defined but never used.  @typescript-eslint/no-unused-vars
2:44  Warning: 'Typography' is defined but never used.  @typescript-eslint/no-unused-vars
56:6  Warning: React Hook useEffect has a missing dependency: 'setSettings'. Either include it or remove the dependency array. If 'setSettings' changes too often, find the parent component that defines it and wrap that definition in useCallback.  react-hooks/exhaustive-deps

./src/components/common/OverRideGlobalSettings.tsx
15:10  Warning: 'useEffect' is defined but never used.  @typescript-eslint/no-unused-vars
15:21  Warning: 'useState' is defined but never used.  @typescript-eslint/no-unused-vars

./src/components/common/RightSideDrawer.tsx
74:29  Warning: 'e' is defined but never used.  @typescript-eslint/no-unused-vars
91:25  Warning: Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element  @next/next/no-img-element

./src/components/common/SankeySettngsHelper/SankeySettingsConnector.tsx
49:9  Warning: 'metricsIdToName' is assigned a value but never used.  @typescript-eslint/no-unused-vars

./src/components/common/SankeySettngsHelper/SankeySettngsHelper.tsx
76:57  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
116:16  Warning: 'isAssetTypeLoading' is assigned a value but never used.  @typescript-eslint/no-unused-vars
180:36  Warning: Forbidden non-null assertion.  @typescript-eslint/no-non-null-assertion
186:6  Warning: React Hook useEffect has a missing dependency: 'label.sourceAssetMeasure'. Either include it or remove the dependency array.  react-hooks/exhaustive-deps
395:50  Warning: Forbidden non-null assertion.  @typescript-eslint/no-non-null-assertion
405:59  Warning: 'child' is defined but never used.  @typescript-eslint/no-unused-vars
703:55  Warning: Forbidden non-null assertion.  @typescript-eslint/no-non-null-assertion
739:49  Warning: Forbidden non-null assertion.  @typescript-eslint/no-non-null-assertion
1025:35  Warning: 'title' is assigned a value but never used.  @typescript-eslint/no-unused-vars
1025:42  Warning: 'type' is assigned a value but never used.  @typescript-eslint/no-unused-vars
1025:65  Warning: Forbidden non-null assertion.  @typescript-eslint/no-non-null-assertion

./src/components/common/Share/Share.tsx
3:3  Warning: 'Box' is defined but never used.  @typescript-eslint/no-unused-vars
6:3  Warning: 'CircularProgress' is defined but never used.  @typescript-eslint/no-unused-vars
14:8  Warning: 'LinkIcon' is defined but never used.  @typescript-eslint/no-unused-vars
27:10  Warning: 'isLoading' is assigned a value but never used.  @typescript-eslint/no-unused-vars
28:10  Warning: 'showLink' is assigned a value but never used.  @typescript-eslint/no-unused-vars

./src/components/common/ShowMinMax.tsx
10:70  Warning: 'checked' is defined but never used.  @typescript-eslint/no-unused-vars
18:70  Warning: 'checked' is defined but never used.  @typescript-eslint/no-unused-vars
26:70  Warning: 'checked' is defined but never used.  @typescript-eslint/no-unused-vars

./src/components/common/SingleAssetMeasure.tsx
66:17  Warning: 'isMeasurementFetching' is assigned a value but never used.  @typescript-eslint/no-unused-vars
98:37  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
111:43  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/components/common/SingleMeasureSelect.tsx
36:6  Warning: React Hook useEffect has missing dependencies: 'metricsIdToName', 'setSettings', and 'settings'. Either include them or remove the dependency array. If 'setSettings' changes too often, find the parent component that defines it and wrap that definition in useCallback.  react-hooks/exhaustive-deps

./src/components/common/TopPanel/CustomerList.tsx
10:3  Warning: 'customerList' is defined but never used.  @typescript-eslint/no-unused-vars
11:3  Warning: 'isCustomerListLoading' is defined but never used.  @typescript-eslint/no-unused-vars
12:3  Warning: 'handleChangeCustomer' is defined but never used.  @typescript-eslint/no-unused-vars
13:3  Warning: 'activeCustomer' is defined but never used.  @typescript-eslint/no-unused-vars

./src/components/common/UserPreferences/UserPreferences.tsx
35:59  Warning: 'child' is defined but never used.  @typescript-eslint/no-unused-vars

./src/components/CreateElement/CreateElement.tsx
19:11  Warning: 'paper' is assigned a value but never used.  @typescript-eslint/no-unused-vars
53:11  Warning: 'tempPaper' is assigned a value but never used.  @typescript-eslint/no-unused-vars

./src/components/CreateElement/JointJSEditor.tsx
71:10  Warning: 'batteryModalOpen' is assigned a value but never used.  @typescript-eslint/no-unused-vars
72:10  Warning: 'currentBattery' is assigned a value but never used.  @typescript-eslint/no-unused-vars
82:43  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
238:25  Warning: Forbidden non-null assertion.  @typescript-eslint/no-non-null-assertion

./src/components/customer/EditCustomerForm.tsx
2:34  Warning: 'Typography' is defined but never used.  @typescript-eslint/no-unused-vars
4:10  Warning: 'Customer' is defined but never used.  @typescript-eslint/no-unused-vars
20:10  Warning: 'renderLogo' is assigned a value but never used.  @typescript-eslint/no-unused-vars
20:22  Warning: 'setRenderLogo' is assigned a value but never used.  @typescript-eslint/no-unused-vars
39:6  Warning: React Hook useEffect has a missing dependency: 'setValue'. Either include it or remove the dependency array.  react-hooks/exhaustive-deps
132:20  Warning: Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element  @next/next/no-img-element
132:20  Warning: img elements must have an alt prop, either with meaningful text, or an empty string for decorative images.  jsx-a11y/alt-text

./src/components/CustomerListContainer.tsx
100:6  Warning: React Hook useEffect has a missing dependency: 'dispatch'. Either include it or remove the dependency array.  react-hooks/exhaustive-deps

./src/components/CustomerUsers/EditUser.tsx
24:25  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
31:3  Warning: 'onSaveSuccess' is defined but never used.  @typescript-eslint/no-unused-vars
34:24  Warning: 'setAlertMessage' is assigned a value but never used.  @typescript-eslint/no-unused-vars
88:6  Warning: React Hook useEffect has a missing dependency: 'setValue'. Either include it or remove the dependency array.  react-hooks/exhaustive-deps
384:41  Warning: 'value' is defined but never used.  @typescript-eslint/no-unused-vars

./src/components/CustomerUsers/SearchCustomerUsers.tsx
43:24  Warning: 'setAlertMessage' is assigned a value but never used.  @typescript-eslint/no-unused-vars
51:20  Warning: 'setCustomer' is assigned a value but never used.  @typescript-eslint/no-unused-vars
92:36  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
94:6  Warning: React Hook useEffect has missing dependencies: 'showErrorAlert' and 'showSuccessAlert'. Either include them or remove the dependency array.  react-hooks/exhaustive-deps
104:29  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
113:6  Warning: React Hook useEffect has missing dependencies: 'onSearch', 'setAddUser', 'showErrorAlert', and 'showSuccessAlert'. Either include them or remove the dependency array. If 'setAddUser' changes too often, find the parent component that defines it and wrap that definition in useCallback.  react-hooks/exhaustive-deps
144:23  Warning: 'index' is defined but never used.  @typescript-eslint/no-unused-vars
167:53  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
167:58  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
193:6  Warning: React Hook useEffect has a missing dependency: 'customer'. Either include it or remove the dependency array.  react-hooks/exhaustive-deps
238:6  Warning: React Hook useEffect has a missing dependency: 'onSearch'. Either include it or remove the dependency array.  react-hooks/exhaustive-deps
316:49  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/components/dashboard/Alert/CreateMeasureAlert.tsx
51:57  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
85:6  Warning: React Hook useEffect has a missing dependency: 'getCustomerUser'. Either include it or remove the dependency array.  react-hooks/exhaustive-deps
152:6  Warning: React Hook useEffect has missing dependencies: 'customerId', 'dispatch', and 'setValue'. Either include them or remove the dependency array.  react-hooks/exhaustive-deps
177:6  Warning: React Hook useEffect has a missing dependency: 'reset'. Either include it or remove the dependency array.  react-hooks/exhaustive-deps
181:6  Warning: React Hook useEffect has a missing dependency: 'props'. Either include it or remove the dependency array. However, 'props' will change when *any* prop changes, so the preferred fix is to destructure the 'props' object outside of the useEffect call and refer to those specific props inside useEffect.  react-hooks/exhaustive-deps
192:6  Warning: React Hook useEffect has missing dependencies: 'measurementId' and 'setValue'. Either include them or remove the dependency array.  react-hooks/exhaustive-deps
192:7  Warning: React Hook useEffect has a complex expression in the dependency array. Extract it to a separate variable so it can be statically checked.  react-hooks/exhaustive-deps

./src/components/dashboard/AlertWidget/index.tsx
23:10  Warning: 'hasNoMeasuresSelected' is defined but never used.  @typescript-eslint/no-unused-vars
49:57  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
49:62  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
55:25  Warning: 'showSuccessAlert' is assigned a value but never used.  @typescript-eslint/no-unused-vars
221:33  Warning: Forbidden non-null assertion.  @typescript-eslint/no-non-null-assertion

./src/components/dashboard/AssetDashboardTemplate.tsx
122:6  Warning: React Hook useEffect has a missing dependency: 'dispatch'. Either include it or remove the dependency array.  react-hooks/exhaustive-deps
353:55  Warning: '_' is defined but never used.  @typescript-eslint/no-unused-vars
709:6  Warning: React Hook useEffect has missing dependencies: 'assetId' and 'assetMeasurementsListMap'. Either include them or remove the dependency array.  react-hooks/exhaustive-deps
729:6  Warning: React Hook useEffect has missing dependencies: 'customerId', 'dispatch', 'router', 'showErrorAlert', and 'showSuccessAlert'. Either include them or remove the dependency array.  react-hooks/exhaustive-deps
734:19  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
742:23  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/components/dashboard/AssetDetail.tsx
30:10  Warning: 'assetTypesWithPath' is assigned a value but never used.  @typescript-eslint/no-unused-vars

./src/components/dashboard/AssetMeasurements.tsx
23:10  Warning: 'formatChartDateToAssetTz' is defined but never used.  @typescript-eslint/no-unused-vars
112:13  Warning: 'link' is assigned a value but never used.  @typescript-eslint/no-unused-vars

./src/components/dashboard/AssetTemplateInstance.tsx
13:9  Warning: 'currentNode' is assigned a value but never used.  @typescript-eslint/no-unused-vars
15:35  Warning: 'isAssetDataLoading' is assigned a value but never used.  @typescript-eslint/no-unused-vars

./src/components/dashboard/CalcEngine/CalcEngine.tsx
54:10  Warning: 'outputMeasure' is assigned a value but never used.  @typescript-eslint/no-unused-vars
114:6  Warning: React Hook useEffect has missing dependencies: 'activeCustomer?.id', 'createInstance', 'dispatch', 'expressionTemplate?.id', 'isPersistance', 'iswriteback', 'pollPeriod', and 'variables'. Either include them or remove the dependency array.  react-hooks/exhaustive-deps
136:66  Warning: 'child' is defined but never used.  @typescript-eslint/no-unused-vars
172:6  Warning: React Hook useEffect has a missing dependency: 'dispatch'. Either include it or remove the dependency array.  react-hooks/exhaustive-deps

./src/components/dashboard/CalcEngine/EditCalcEngine.tsx
106:6  Warning: React Hook useEffect has a missing dependency: 'editError'. Either include it or remove the dependency array.  react-hooks/exhaustive-deps
150:6  Warning: React Hook useEffect has missing dependencies: 'activeCustomer?.id', 'calcEngineTemplates', 'dispatch', 'expressionTemplate?.id', 'isPersistance', 'measure.measurement_id', 'pollPeriod', 'updateTemplate', 'variables', and 'writeback'. Either include them or remove the dependency array.  react-hooks/exhaustive-deps
188:66  Warning: 'child' is defined but never used.  @typescript-eslint/no-unused-vars

./src/components/dashboard/ChartAnnotation/index.tsx
24:10  Warning: 'formatChartDateToAssetTz' is defined but never used.  @typescript-eslint/no-unused-vars
100:6  Warning: React Hook useEffect has missing dependencies: 'annotation.measurementId', 'annotation.settings', 'annotation.time', 'annotation.value', 'setAnnotation', 'setMeasureIdAnnotations', 'showErrorAlert', and 'showSuccessAlert'. Either include them or remove the dependency array. If 'showErrorAlert' changes too often, find the parent component that defines it and wrap that definition in useCallback.  react-hooks/exhaustive-deps

./src/components/dashboard/ChartAnnotation/UpdateAnnotation.tsx
112:6  Warning: React Hook useEffect has missing dependencies: 'setMeasureIdAnnotations', 'setUpdateAnnotation', 'showErrorAlert', 'showSuccessAlert', 'updateAnnotation.id', 'updateAnnotation.settings', 'updateAnnotation.time', and 'updateAnnotation.value'. Either include them or remove the dependency array. If 'showErrorAlert' changes too often, find the parent component that defines it and wrap that definition in useCallback.  react-hooks/exhaustive-deps

./src/components/dashboard/DashboardTemplateTItleDialog.tsx
72:6  Warning: React Hook useEffect has a missing dependency: 'getValues'. Either include it or remove the dependency array.  react-hooks/exhaustive-deps
87:6  Warning: React Hook useEffect has a missing dependency: 'setValue'. Either include it or remove the dependency array.  react-hooks/exhaustive-deps

./src/components/dashboard/DashboardWidget/DashboardWidgetSettingsDialog.tsx
56:57  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
56:62  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
92:16  Warning: 'isTemplateSuccess' is assigned a value but never used.  @typescript-eslint/no-unused-vars
93:14  Warning: 'isTemplateError' is assigned a value but never used.  @typescript-eslint/no-unused-vars
122:12  Warning: 'assetError' is assigned a value but never used.  @typescript-eslint/no-unused-vars
133:16  Warning: 'isAssetTypeLoading' is assigned a value but never used.  @typescript-eslint/no-unused-vars
148:18  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
165:21  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
166:18  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
220:64  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
263:59  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
273:32  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
278:62  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
311:23  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
338:6  Warning: React Hook useEffect has missing dependencies: 'handleSettingsChange' and 'settings'. Either include them or remove the dependency array. If 'handleSettingsChange' changes too often, find the parent component that defines it and wrap that definition in useCallback.  react-hooks/exhaustive-deps
349:6  Warning: React Hook useEffect has missing dependencies: 'handleSettingsChange' and 'settings'. Either include them or remove the dependency array. If 'handleSettingsChange' changes too often, find the parent component that defines it and wrap that definition in useCallback.  react-hooks/exhaustive-deps
391:6  Warning: React Hook useEffect has missing dependencies: 'getFilteredAssetMeasurements' and 'settings.mode'. Either include them or remove the dependency array.  react-hooks/exhaustive-deps
652:55  Warning: '_' is defined but never used.  @typescript-eslint/no-unused-vars
1052:53  Warning: '_' is defined but never used.  @typescript-eslint/no-unused-vars
1080:6  Warning: React Hook useEffect has missing dependencies: 'assetId', 'assetMeasurementsListMap', 'dispatch', 'handleSettingsChange', and 'settings'. Either include them or remove the dependency array. If 'handleSettingsChange' changes too often, find the parent component that defines it and wrap that definition in useCallback.  react-hooks/exhaustive-deps
1082:37  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
1114:28  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/components/dashboard/DashboardWidget/index.tsx
31:57  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
31:62  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
54:16  Warning: 'isLoadingDashboardTemplates' is assigned a value but never used.  @typescript-eslint/no-unused-vars
56:16  Warning: 'isTemplateSuccess' is assigned a value but never used.  @typescript-eslint/no-unused-vars
57:14  Warning: 'isTemplateError' is assigned a value but never used.  @typescript-eslint/no-unused-vars
384:55  Warning: '_' is defined but never used.  @typescript-eslint/no-unused-vars
818:53  Warning: '_' is defined but never used.  @typescript-eslint/no-unused-vars
877:6  Warning: React Hook useEffect has missing dependencies: 'settings' and 'transformTemplate'. Either include them or remove the dependency array.  react-hooks/exhaustive-deps

./src/components/dashboard/DashboardWidget/transformTemplateHelper.ts
1:10  Warning: 'ThunkDispatch' is defined but never used.  @typescript-eslint/no-unused-vars
3:10  Warning: 'RootState' is defined but never used.  @typescript-eslint/no-unused-vars
6:10  Warning: 'elementVariable' is defined but never used.  @typescript-eslint/no-unused-vars
7:10  Warning: 'DashboardWidget' is defined but never used.  @typescript-eslint/no-unused-vars
7:27  Warning: 'ImageTextDetails' is defined but never used.  @typescript-eslint/no-unused-vars
8:10  Warning: 'formatMetricLabel' is defined but never used.  @typescript-eslint/no-unused-vars
42:37  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/components/dashboard/EditAssetForm.tsx
39:6  Warning: React Hook useEffect has a missing dependency: 'reset'. Either include it or remove the dependency array.  react-hooks/exhaustive-deps

./src/components/dashboard/EditMeasurementForm.tsx
31:8  Warning: 'MeasureFactor' is defined but never used.  @typescript-eslint/no-unused-vars
92:6  Warning: React Hook useEffect has a missing dependency: 'reset'. Either include it or remove the dependency array.  react-hooks/exhaustive-deps

./src/components/dashboard/ImportDashboard/index.tsx
33:50  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/components/dashboard/KPIBarChart/KPIBarChartSettings.tsx
3:3  Warning: 'Card' is defined but never used.  @typescript-eslint/no-unused-vars

./src/components/dashboard/KPIColorBox/KPIColorBoxContainer.tsx
36:66  Warning: Forbidden non-null assertion.  @typescript-eslint/no-non-null-assertion

./src/components/dashboard/KPICurrent/ImageStatsSettings.tsx
53:6  Warning: React Hook useEffect has missing dependencies: 'handleSettingsChange' and 'settings'. Either include them or remove the dependency array. If 'handleSettingsChange' changes too often, find the parent component that defines it and wrap that definition in useCallback.  react-hooks/exhaustive-deps
87:73  Warning: 'child' is defined but never used.  @typescript-eslint/no-unused-vars
93:68  Warning: 'child' is defined but never used.  @typescript-eslint/no-unused-vars
229:23  Warning: Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element  @next/next/no-img-element
257:48  Warning: 'i' is defined but never used.  @typescript-eslint/no-unused-vars

./src/components/dashboard/KPICurrent/KPICurrentWidgetContainer.tsx
155:43  Warning: Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element  @next/next/no-img-element
155:43  Warning: img elements must have an alt prop, either with meaningful text, or an empty string for decorative images.  jsx-a11y/alt-text
279:63  Warning: 'i' is defined but never used.  @typescript-eslint/no-unused-vars
423:63  Warning: 'i' is defined but never used.  @typescript-eslint/no-unused-vars

./src/components/dashboard/KPIPercentage/KPIPercentageContainer.tsx
4:8  Warning: 'Error' is defined but never used.  @typescript-eslint/no-unused-vars
24:5  Warning: 'isError' is assigned a value but never used.  @typescript-eslint/no-unused-vars

./src/components/dashboard/KPISparkLine/KPISparkLineDialog.tsx
67:68  Warning: 'child' is defined but never used.  @typescript-eslint/no-unused-vars

./src/components/dashboard/KPITable/KPITableContainer.tsx
51:53  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
51:58  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
51:63  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
64:53  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
64:58  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
64:63  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
77:53  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
77:58  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
77:63  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
90:53  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
90:58  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
90:63  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
103:53  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
103:58  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
103:63  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
116:53  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
116:58  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
116:63  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
126:49  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
126:54  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
126:59  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/components/dashboard/KPITable/KPITableDialog.tsx
59:6  Warning: React Hook useEffect has a missing dependency: 'handleSettingsChange'. Either include it or remove the dependency array. If 'handleSettingsChange' changes too often, find the parent component that defines it and wrap that definition in useCallback.  react-hooks/exhaustive-deps

./src/components/dashboard/KPIValueIndicator/KPIValueIndicatorContainer.tsx
19:10  Warning: 'hasNoMeasuresSelected' is defined but never used.  @typescript-eslint/no-unused-vars

./src/components/dashboard/LeftPanel.tsx
38:3  Warning: 'customerList' is defined but never used.  @typescript-eslint/no-unused-vars
39:3  Warning: 'dashboardList' is defined but never used.  @typescript-eslint/no-unused-vars
40:3  Warning: 'isCustomerListLoading' is defined but never used.  @typescript-eslint/no-unused-vars
48:9  Warning: 'currentDashboardId' is assigned a value but never used.  @typescript-eslint/no-unused-vars
49:9  Warning: 'isDashboardStateDirty' is assigned a value but never used.  @typescript-eslint/no-unused-vars
51:9  Warning: 'deleteWidgets' is assigned a value but never used.  @typescript-eslint/no-unused-vars
52:9  Warning: 'mainPanel' is assigned a value but never used.  @typescript-eslint/no-unused-vars
53:9  Warning: 'ActiveCustomer' is assigned a value but never used.  @typescript-eslint/no-unused-vars
111:6  Warning: React Hook useEffect has missing dependencies: 'closeConfirmationDialog', 'dispatch', 'showErrorAlert', and 'showSuccessAlert'. Either include them or remove the dependency array.  react-hooks/exhaustive-deps
120:12  Warning: '_' is assigned a value but never used.  @typescript-eslint/no-unused-vars
120:15  Warning: 'assetId' is assigned a value but never used.  @typescript-eslint/no-unused-vars
279:12  Warning: '_' is assigned a value but never used.  @typescript-eslint/no-unused-vars

./src/components/dashboard/MainAssetDetails.tsx
53:6  Warning: React Hook useEffect has missing dependencies: 'closeConfirmationDialog', 'dispatch', 'showErrorAlert', and 'showSuccessAlert'. Either include them or remove the dependency array.  react-hooks/exhaustive-deps

./src/components/dashboard/MainMeasureDetails.tsx
9:3  Warning: 'useGetAllBackOfficeAssetTypesQuery' is defined but never used.  @typescript-eslint/no-unused-vars
61:44  Warning: 'success' is assigned a value but never used.  @typescript-eslint/no-unused-vars
100:6  Warning: React Hook useEffect has missing dependencies: 'closeConfirmationDialog', 'dispatch', 'showErrorAlert', and 'showSuccessAlert'. Either include them or remove the dependency array.  react-hooks/exhaustive-deps
104:13  Warning: 'err' is assigned a value but never used.  @typescript-eslint/no-unused-vars
107:6  Warning: React Hook useEffect has a missing dependency: 'showErrorAlert'. Either include it or remove the dependency array.  react-hooks/exhaustive-deps

./src/components/dashboard/MeasureFactor/DayTime.tsx
41:27  Warning: 'effective_dates' is defined but never used.  @typescript-eslint/no-unused-vars

./src/components/dashboard/MeasureFactor/MeasureFactor.tsx
90:48  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
93:48  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
116:14  Warning: 'errorData' is assigned a value but never used.  @typescript-eslint/no-unused-vars
159:57  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
178:6  Warning: React Hook useEffect has missing dependencies: 'createTimeVaringFactor', 'dispatch', 'effective_dates', 'isSeasonal', and 'selectedAssetType'. Either include them or remove the dependency array.  react-hooks/exhaustive-deps
215:57  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
235:6  Warning: React Hook useEffect has missing dependencies: 'editTimeVaringFactor', 'effective_dates', 'isSeasonal', 'measure.measurement_id', 'selectedAssetType', 'successFull', and 'timeVaringFactorToUpdate?.id'. Either include them or remove the dependency array.  react-hooks/exhaustive-deps

./src/components/dashboard/MeasureFactor/MeasureTable.tsx
663:16  Warning: Forbidden non-null assertion.  @typescript-eslint/no-non-null-assertion
663:16  Warning: Forbidden non-null assertion.  @typescript-eslint/no-non-null-assertion
664:19  Warning: Forbidden non-null assertion.  @typescript-eslint/no-non-null-assertion
718:26  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
727:6  Warning: React Hook useEffect has missing dependencies: 'effective_dates' and 'setEffectiveDates'. Either include them or remove the dependency array. If 'setEffectiveDates' changes too often, find the parent component that defines it and wrap that definition in useCallback.  react-hooks/exhaustive-deps

./src/components/dashboard/MeasureFactor/MeasureTimeVaringFactor.tsx
86:14  Warning: 'errorData' is assigned a value but never used.  @typescript-eslint/no-unused-vars
113:48  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
180:6  Warning: React Hook useEffect has missing dependencies: 'editTimeVaringFactor', 'effective_dates', 'isSeasonal', 'measure.measurement_id', 'selectedAssetType', 'successFull', and 'timeVaringFactorToUpdate?.id'. Either include them or remove the dependency array.  react-hooks/exhaustive-deps
275:6  Warning: React Hook useEffect has missing dependencies: 'createTimeVaringFactor', 'dispatch', 'effective_dates', 'isSeasonal', and 'selectedAssetType'. Either include them or remove the dependency array.  react-hooks/exhaustive-deps

./src/components/dashboard/MultiPlotWidget/AssetMeasureConfig.tsx
23:3  Warning: 'dbMeasureIdToName' is defined but never used.  @typescript-eslint/no-unused-vars
25:3  Warning: 'assetMeasureLength' is defined but never used.  @typescript-eslint/no-unused-vars
45:9  Warning: 'updateMeasureField' is assigned a value but never used.  @typescript-eslint/no-unused-vars

./src/components/dashboard/MultiPlotWidget/AssetMeasureSelector.tsx
68:33  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
87:39  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/components/dashboard/MultiPlotWidget/index.tsx
52:30  Warning: 'index' is defined but never used.  @typescript-eslint/no-unused-vars
70:46  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/components/dashboard/MultiPlotWidget/MultiPlotWidgetSettingsDialog.tsx
82:6  Warning: React Hook useEffect has missing dependencies: 'handleSettingsChange' and 'metricsIdToName'. Either include them or remove the dependency array. If 'handleSettingsChange' changes too often, find the parent component that defines it and wrap that definition in useCallback.  react-hooks/exhaustive-deps

./src/components/dashboard/NewAssetForm.tsx
28:60  Warning: 'getValues' is assigned a value but never used.  @typescript-eslint/no-unused-vars
43:6  Warning: React Hook useEffect has a missing dependency: 'setValue'. Either include it or remove the dependency array.  react-hooks/exhaustive-deps

./src/components/dashboard/NewMeasurementForm.tsx
68:9  Warning: 'customerId' is assigned a value but never used.  @typescript-eslint/no-unused-vars
88:9  Warning: 'tag' is assigned a value but never used.  @typescript-eslint/no-unused-vars
224:57  Warning: 'rest' is defined but never used.  @typescript-eslint/no-unused-vars

./src/components/dashboard/TopPanel.tsx
99:10  Warning: 'de' is defined but never used.  @typescript-eslint/no-unused-vars
118:3  Warning: 'dashboardList' is defined but never used.  @typescript-eslint/no-unused-vars
120:3  Warning: 'isCustomerListSuccess' is defined but never used.  @typescript-eslint/no-unused-vars
121:3  Warning: 'isCustomerListLoading' is defined but never used.  @typescript-eslint/no-unused-vars
122:3  Warning: 'customerList' is defined but never used.  @typescript-eslint/no-unused-vars
155:9  Warning: 'isDashboardStateDirty' is assigned a value but never used.  @typescript-eslint/no-unused-vars
170:27  Warning: 'loading' is assigned a value but never used.  @typescript-eslint/no-unused-vars
192:17  Warning: 'dashboardTemplates' is assigned a value but never used.  @typescript-eslint/no-unused-vars
221:6  Warning: React Hook useEffect has a missing dependency: 'isNotDashboardTemplate'. Either include it or remove the dependency array.  react-hooks/exhaustive-deps
232:6  Warning: React Hook useEffect has a missing dependency: 'isNotDashboardTemplate'. Either include it or remove the dependency array.  react-hooks/exhaustive-deps
325:27  Warning: 'key' is defined but never used.  @typescript-eslint/no-unused-vars
578:6  Warning: React Hook useEffect has missing dependencies: 'assetTemplate', 'createDashboardtemplate', 'dashboardState.chart', 'dashboardState.topPanel', 'dashboardState.widget', 'measurementsError', 'saveAsGlobal', 'showErrorAlert', and 'templateTitleFromDashboard'. Either include them or remove the dependency array.  react-hooks/exhaustive-deps
586:6  Warning: React Hook useEffect has missing dependencies: 'showErrorAlert' and 'showSuccessAlert'. Either include them or remove the dependency array.  react-hooks/exhaustive-deps
607:6  Warning: React Hook useEffect has missing dependencies: 'dispatch', 'showErrorAlert', 'showSuccessAlert', and 'template'. Either include them or remove the dependency array.  react-hooks/exhaustive-deps
618:6  Warning: React Hook useEffect has missing dependencies: 'dispatch', 'showErrorAlert', and 'showSuccessAlert'. Either include them or remove the dependency array.  react-hooks/exhaustive-deps
643:6  Warning: React Hook useEffect has missing dependencies: 'crumbs', 'customerId', 'dispatch', 'router', 'showErrorAlert', and 'showSuccessAlert'. Either include them or remove the dependency array.  react-hooks/exhaustive-deps
691:36  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
1131:33  Warning: 'description' is defined but never used.  @typescript-eslint/no-unused-vars

./src/components/dashboard/__tests__/ResponsiveLayoutToggle.test.tsx
362:67  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
375:61  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/components/dashboard/__tests__/TopPanel.test.tsx
102:71  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
115:79  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
132:64  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
155:63  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/components/DashboardList/DashboardsTable.tsx
12:3  Warning: 'Card' is defined but never used.  @typescript-eslint/no-unused-vars
25:10  Warning: 'CustomError' is defined but never used.  @typescript-eslint/no-unused-vars
56:10  Warning: 'favorite' is assigned a value but never used.  @typescript-eslint/no-unused-vars
58:10  Warning: 'marksState' is assigned a value but never used.  @typescript-eslint/no-unused-vars
84:6  Warning: React Hook useEffect has missing dependencies: 'refetch', 'showErrorAlert', and 'showSuccessAlert'. Either include them or remove the dependency array.  react-hooks/exhaustive-deps
105:63  Warning: Forbidden non-null assertion.  @typescript-eslint/no-non-null-assertion

./src/components/DashboardList/SearchDashboard.tsx
1:23  Warning: 'CircularProgress' is defined but never used.  @typescript-eslint/no-unused-vars
12:11  Warning: 'data' is assigned a value but never used.  @typescript-eslint/no-unused-vars
12:17  Warning: 'isSuccess' is assigned a value but never used.  @typescript-eslint/no-unused-vars
12:28  Warning: 'isLoading' is assigned a value but never used.  @typescript-eslint/no-unused-vars
26:9  Warning: 'handleChange' is assigned a value but never used.  @typescript-eslint/no-unused-vars

./src/components/DashboardTemplates/index.tsx
188:6  Warning: React Hook useEffect has missing dependencies: 'dispatch', 'setAssetTemplate', 'setAssetType', 'setWidget', and 'setWidgetsLayout'. Either include them or remove the dependency array.  react-hooks/exhaustive-deps

./src/components/DashboardTemplates/MetricsList.tsx
24:3  Warning: 'dataSourceList' is defined but never used.  @typescript-eslint/no-unused-vars
25:3  Warning: 'dataTypeList' is defined but never used.  @typescript-eslint/no-unused-vars
26:3  Warning: 'measurementTypeList' is defined but never used.  @typescript-eslint/no-unused-vars
50:6  Warning: React Hook useEffect has missing dependencies: 'setMetrics' and 'setMetricsIdToName'. Either include them or remove the dependency array.  react-hooks/exhaustive-deps

./src/components/DashboardTemplates/__tests__/index.test.tsx
41:42  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
69:42  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
86:21  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
251:7  Error: Require statement not part of import statement.  @typescript-eslint/no-var-requires
256:7  Error: Require statement not part of import statement.  @typescript-eslint/no-var-requires
262:7  Error: Require statement not part of import statement.  @typescript-eslint/no-var-requires
587:20  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
884:80  Error: Unexpected empty arrow function.  @typescript-eslint/no-empty-function

./src/components/DataIngestion/AssetMeasureRow.tsx
31:43  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
32:49  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/components/DataIngestion/CsvPreviewAndMapping.tsx
11:3  Warning: 'Switch' is defined but never used.  @typescript-eslint/no-unused-vars
20:3  Warning: 'FormLabel' is defined but never used.  @typescript-eslint/no-unused-vars
26:16  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
56:9  Warning: 'allMapped' is assigned a value but never used.  @typescript-eslint/no-unused-vars
86:6  Warning: React Hook useEffect has missing dependencies: 'onTimestampFormatChange' and 'timestampFormat'. Either include them or remove the dependency array. If 'onTimestampFormatChange' changes too often, find the parent component that defines it and wrap that definition in useCallback.  react-hooks/exhaustive-deps

./src/components/DataIngestion/CsvUploadForm.tsx
10:20  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
14:53  Warning: 'isLoading' is assigned a value but never used.  @typescript-eslint/no-unused-vars
53:22  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/components/DataIngestion/DataIngestion.tsx
12:10  Warning: 'Fragment' is defined but never used.  @typescript-eslint/no-unused-vars
21:37  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
35:69  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
98:18  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
107:10  Warning: 'uploadResult' is assigned a value but never used.  @typescript-eslint/no-unused-vars
107:52  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
108:52  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
153:57  Warning: 'injectError' is assigned a value but never used.  @typescript-eslint/no-unused-vars
237:43  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
248:19  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
333:21  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
402:21  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/components/DataIngestion/MeasurementRedisMapping.tsx
54:3  Warning: 'isLoading' is defined but never used.  @typescript-eslint/no-unused-vars
67:16  Warning: 'isAssetLoading' is assigned a value but never used.  @typescript-eslint/no-unused-vars
160:50  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
176:56  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
196:9  Warning: 'handleInject' is assigned a value but never used.  @typescript-eslint/no-unused-vars

./src/components/diagram/Animation.tsx
21:16  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
35:16  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
49:21  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
83:81  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
102:81  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
115:6  Warning: React Hook React.useEffect has a missing dependency: 'model'. Either include it or remove the dependency array.  react-hooks/exhaustive-deps
117:41  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
117:62  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
132:9  Warning: The 'animate' function makes the dependencies of useEffect Hook (at line 164) change on every render. To fix this, wrap the definition of 'animate' in its own useCallback() Hook.  react-hooks/exhaustive-deps

./src/components/diagram/Application.ts
27:55  Warning: 'config' is defined but never used.  @typescript-eslint/no-unused-vars

./src/components/diagram/DiagramUtil.ts
3:20  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
4:18  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/components/diagram/Drag and Drop/BodyWidget.tsx
26:11  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
67:9  Warning: 'model' is assigned a value but never used.  @typescript-eslint/no-unused-vars
81:6  Warning: React Hook React.useEffect has a missing dependency: 'dispatch'. Either include it or remove the dependency array.  react-hooks/exhaustive-deps
111:32  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
210:33  Warning: 'event' is defined but never used.  @typescript-eslint/no-unused-vars

./src/components/diagram/Drag and Drop/DemoCanvasWidget.tsx
13:3  Warning: 'background' is assigned a value but never used.  @typescript-eslint/no-unused-vars

./src/components/diagram/Drag and Drop/TragItemWidget.tsx
5:10  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/components/diagram/model/CustomNodeFactory.tsx
14:30  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
19:17  Warning: 'event' is defined but never used.  @typescript-eslint/no-unused-vars
19:24  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/components/diagram/model/CustomNodeModel.ts
3:3  Warning: 'DefaultNodeModel' is defined but never used.  @typescript-eslint/no-unused-vars
4:3  Warning: 'DefaultNodeModelOptions' is defined but never used.  @typescript-eslint/no-unused-vars
39:16  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/components/diagram/model/CustomNodeWidget.tsx
64:66  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/components/diagram/model/CustomPortFactory.ts
5:24  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
7:50  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
12:24  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/components/diagram/model/CustomPortModel.ts
2:3  Warning: 'DefaultLinkModel' is defined but never used.  @typescript-eslint/no-unused-vars
5:3  Warning: 'PortModelAlignment' is defined but never used.  @typescript-eslint/no-unused-vars
11:42  Warning: 'name' is defined but never used.  @typescript-eslint/no-unused-vars
22:41  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/components/diagram/New Link/CreateLinkState.ts
20:55  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
71:61  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
83:58  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/components/diagram/New Link/DefaultState.ts
31:49  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
54:49  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
63:49  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/components/DynamicCharts/index.tsx
24:8  Warning: 'HomeButton' is defined but never used.  @typescript-eslint/no-unused-vars
26:8  Warning: 'PageName' is defined but never used.  @typescript-eslint/no-unused-vars

./src/components/ExcursionsList/ExcursionsList.tsx
6:3  Warning: 'Container' is defined but never used.  @typescript-eslint/no-unused-vars
22:10  Warning: 'formatNumber' is defined but never used.  @typescript-eslint/no-unused-vars
22:24  Warning: 'roundNumber' is defined but never used.  @typescript-eslint/no-unused-vars
25:8  Warning: 'PageName' is defined but never used.  @typescript-eslint/no-unused-vars
29:9  Warning: 'thousandSeparator' is assigned a value but never used.  @typescript-eslint/no-unused-vars
30:25  Warning: 'showSuccessAlert' is assigned a value but never used.  @typescript-eslint/no-unused-vars
30:43  Warning: 'showErrorAlert' is assigned a value but never used.  @typescript-eslint/no-unused-vars
130:28  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
146:28  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/components/ExpressionTemplate/ExpressionOperands.tsx
3:23  Warning: 'FormHelperText' is defined but never used.  @typescript-eslint/no-unused-vars

./src/components/ExpressionTemplate/ExpressionTemplateBuilder.tsx
9:3  Warning: 'IconButton' is defined but never used.  @typescript-eslint/no-unused-vars
16:10  Warning: 'compile' is defined but never used.  @typescript-eslint/no-unused-vars
36:10  Warning: 'expressionValues' is assigned a value but never used.  @typescript-eslint/no-unused-vars
48:10  Warning: 'isValidSample' is assigned a value but never used.  @typescript-eslint/no-unused-vars
133:6  Warning: React Hook useEffect has a missing dependency: 'templateId'. Either include it or remove the dependency array.  react-hooks/exhaustive-deps
231:6  Warning: React Hook useEffect has a missing dependency: 'dataTypes?.items'. Either include it or remove the dependency array.  react-hooks/exhaustive-deps
265:6  Warning: React Hook useEffect has a missing dependency: 'dataTypes?.items'. Either include it or remove the dependency array.  react-hooks/exhaustive-deps

./src/components/ForecastSettings/ForecastSettings.tsx
27:65  Warning: 'child' is defined but never used.  @typescript-eslint/no-unused-vars

./src/components/ImageWidget/ImageUpload.tsx
37:18  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
74:6  Warning: React Hook useEffect has missing dependencies: 'imageUplaod', 'refetch', 'setSettings', 'showErrorAlert', and 'showSuccessAlert'. Either include them or remove the dependency array. If 'refetch' changes too often, find the parent component that defines it and wrap that definition in useCallback.  react-hooks/exhaustive-deps
128:13  Warning: Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element  @next/next/no-img-element

./src/components/ImageWidget/ImageWidgetDialog.tsx
63:10  Warning: 'selectedMeasures' is assigned a value but never used.  @typescript-eslint/no-unused-vars
79:9  Warning: 'handleIsEditableChange' is assigned a value but never used.  @typescript-eslint/no-unused-vars
86:9  Warning: 'handleChangeMeasure' is assigned a value but never used.  @typescript-eslint/no-unused-vars
127:74  Warning: 'value' is defined but never used.  @typescript-eslint/no-unused-vars
243:26  Warning: Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element  @next/next/no-img-element
256:25  Warning: Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element  @next/next/no-img-element

./src/components/ImageWidget/ImageWidgetFonts.tsx
40:63  Warning: 'child' is defined but never used.  @typescript-eslint/no-unused-vars

./src/components/ImageWidget/ImageWidgetLink.tsx
51:57  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
51:62  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
93:16  Warning: 'isAssetTypeLoading' is assigned a value but never used.  @typescript-eslint/no-unused-vars
121:6  Warning: React Hook useEffect has missing dependencies: 'setSettings' and 'settings'. Either include them or remove the dependency array. If 'setSettings' changes too often, find the parent component that defines it and wrap that definition in useCallback.  react-hooks/exhaustive-deps
599:45  Warning: Forbidden non-null assertion.  @typescript-eslint/no-non-null-assertion
599:45  Warning: Forbidden non-null assertion.  @typescript-eslint/no-non-null-assertion
642:39  Warning: Forbidden non-null assertion.  @typescript-eslint/no-non-null-assertion

./src/components/ImageWidget/MeasurementDashboard.tsx
59:57  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
59:62  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
77:16  Warning: 'isAssetTypeLoading' is assigned a value but never used.  @typescript-eslint/no-unused-vars
159:6  Warning: React Hook useEffect has missing dependencies: 'measureId', 'setSettings', and 'settings.measureIdToImageTextDetails'. Either include them or remove the dependency array. If 'setSettings' changes too often, find the parent component that defines it and wrap that definition in useCallback.  react-hooks/exhaustive-deps
159:7  Warning: React Hook useEffect has a complex expression in the dependency array. Extract it to a separate variable so it can be statically checked.  react-hooks/exhaustive-deps

./src/components/JointJs/CreateElement/CreateElementCommonSettings.tsx
57:6  Warning: React Hook useEffect has missing dependencies: 'conditionalRule' and 'variables'. Either include them or remove the dependency array.  react-hooks/exhaustive-deps
173:61  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/components/JointJs/CreateElement/index.tsx
29:5  Warning: 'elementsVariables' is assigned a value but never used.  @typescript-eslint/no-unused-vars
30:5  Warning: 'addNewVariableToElement' is assigned a value but never used.  @typescript-eslint/no-unused-vars
33:5  Warning: 'handleDeleteVariable' is assigned a value but never used.  @typescript-eslint/no-unused-vars
34:5  Warning: 'handleVariableChange' is assigned a value but never used.  @typescript-eslint/no-unused-vars
38:5  Warning: 'selectedElements' is assigned a value but never used.  @typescript-eslint/no-unused-vars
39:5  Warning: 'setCurrentSelectedElement' is assigned a value but never used.  @typescript-eslint/no-unused-vars
40:5  Warning: 'setSelectedElements' is assigned a value but never used.  @typescript-eslint/no-unused-vars
43:5  Warning: 'setCurrentBattery' is assigned a value but never used.  @typescript-eslint/no-unused-vars
44:5  Warning: 'batteryModalOpen' is assigned a value but never used.  @typescript-eslint/no-unused-vars
45:5  Warning: 'setBatteryModalOpen' is assigned a value but never used.  @typescript-eslint/no-unused-vars
46:5  Warning: 'currentBattery' is assigned a value but never used.  @typescript-eslint/no-unused-vars
113:34  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
207:36  Warning: Forbidden non-null assertion.  @typescript-eslint/no-non-null-assertion

./src/components/JointJs/DiagramComponent/DiagramCommonButtons.tsx
17:8  Warning: 'Progress' is defined but never used.  @typescript-eslint/no-unused-vars
96:6  Warning: React Hook useEffect has missing dependencies: 'showErrorAlert' and 'showSuccessAlert'. Either include them or remove the dependency array.  react-hooks/exhaustive-deps
116:6  Warning: React Hook useEffect has missing dependencies: 'diagramError', 'refetch', 'setDiagramId', 'setDiagramName', 'showErrorAlert', and 'showSuccessAlert'. Either include them or remove the dependency array.  react-hooks/exhaustive-deps
141:19  Warning: Forbidden non-null assertion.  @typescript-eslint/no-non-null-assertion
182:24  Warning: Forbidden non-null assertion.  @typescript-eslint/no-non-null-assertion

./src/components/JointJs/DiagramComponent/DiagramComponent.tsx
38:5  Warning: 'linkAttrs' is assigned a value but never used.  @typescript-eslint/no-unused-vars
51:5  Warning: 'resetZoom' is assigned a value but never used.  @typescript-eslint/no-unused-vars
58:5  Warning: 'handleOpenLinkEditDialog' is assigned a value but never used.  @typescript-eslint/no-unused-vars
59:5  Warning: 'errors' is assigned a value but never used.  @typescript-eslint/no-unused-vars
113:38  Warning: Forbidden non-null assertion.  @typescript-eslint/no-non-null-assertion
122:73  Warning: Forbidden non-null assertion.  @typescript-eslint/no-non-null-assertion

./src/components/JointJs/DiagramComponent/DiagramComponentDialoge/CommonElementPropEditDialog.tsx
89:3  Warning: 'imageUploaded' is defined but never used.  @typescript-eslint/no-unused-vars
90:3  Warning: 'opacity' is defined but never used.  @typescript-eslint/no-unused-vars
91:3  Warning: 'handleImageUploadInternal' is defined but never used.  @typescript-eslint/no-unused-vars
134:6  Warning: React Hook useEffect has a missing dependency: 'handleAttrChangeElement'. Either include it or remove the dependency array. If 'handleAttrChangeElement' changes too often, find the parent component that defines it and wrap that definition in useCallback.  react-hooks/exhaustive-deps
181:17  Warning: Forbidden non-null assertion.  @typescript-eslint/no-non-null-assertion
563:17  Warning: Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element  @next/next/no-img-element
610:75  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/components/JointJs/DiagramComponent/DiagramComponentDialoge/CommonLinkPropEditDialog.tsx
12:3  Warning: 'Switch' is defined but never used.  @typescript-eslint/no-unused-vars
61:7  Warning: 'BootstrapDialog' is assigned a value but never used.  @typescript-eslint/no-unused-vars

./src/components/JointJs/DiagramComponent/DiagramComponentDialoge/CommonSettings.tsx
19:8  Warning: '_' is defined but never used.  @typescript-eslint/no-unused-vars
214:15  Warning: 'updatedData' is assigned a value but never used.  @typescript-eslint/no-unused-vars

./src/components/JointJs/DiagramComponent/DiagramComponentDialoge/ImageUploadModal.tsx
68:13  Warning: Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element  @next/next/no-img-element

./src/components/JointJs/DiagramComponent/DiagramComponentDialoge/LabelSettings.tsx
11:26  Warning: 'graph' is defined but never used.  @typescript-eslint/no-unused-vars
40:6  Warning: React Hook useEffect has a missing dependency: 'defaultData'. Either include it or remove the dependency array.  react-hooks/exhaustive-deps
42:59  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/components/JointJs/DiagramComponent/DiagramComponentDialoge/LinkConfigModal.tsx
4:3  Warning: 'Dialog' is defined but never used.  @typescript-eslint/no-unused-vars
5:3  Warning: 'DialogActions' is defined but never used.  @typescript-eslint/no-unused-vars
6:3  Warning: 'DialogContent' is defined but never used.  @typescript-eslint/no-unused-vars
7:3  Warning: 'DialogTitle' is defined but never used.  @typescript-eslint/no-unused-vars
14:3  Warning: 'Switch' is defined but never used.  @typescript-eslint/no-unused-vars
78:29  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/components/JointJs/DiagramComponent/DiagramComponentDialoge/LiquidTankSettings.tsx
42:9  Warning: 'updateRangePicker' is assigned a value but never used.  @typescript-eslint/no-unused-vars
59:9  Warning: 'handleAddRangePicker' is assigned a value but never used.  @typescript-eslint/no-unused-vars
76:9  Warning: 'handleDeleteRangePicker' is assigned a value but never used.  @typescript-eslint/no-unused-vars
84:9  Warning: 'handleColorChange' is assigned a value but never used.  @typescript-eslint/no-unused-vars

./src/components/JointJs/DiagramComponent/DiagramComponentDialoge/NewElementDialog.tsx
111:58  Warning: Forbidden non-null assertion.  @typescript-eslint/no-non-null-assertion

./src/components/JointJs/DiagramComponent/ExtendedStandard.tsx
91:28  Warning: 'T' is defined but never used.  @typescript-eslint/no-unused-vars
97:28  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
143:17  Warning: 'attributes' is defined but never used.  @typescript-eslint/no-unused-vars
143:66  Warning: 'options' is defined but never used.  @typescript-eslint/no-unused-vars
143:76  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/components/JointJs/DiagramComponent/Pipe.tsx
50:26  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
50:40  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/components/JointJs/DiagramComponent/Pump.tsx
4:7  Warning: 'd' is assigned a value but never used.  @typescript-eslint/no-unused-vars
5:7  Warning: 'l' is assigned a value but never used.  @typescript-eslint/no-unused-vars
6:7  Warning: 'step' is assigned a value but never used.  @typescript-eslint/no-unused-vars

./src/components/JointJs/DiagramComponent/StatsChartElement.tsx
3:8  Warning: 'Plot' is defined but never used.  @typescript-eslint/no-unused-vars
7:35  Warning: 'label' is defined but never used.  @typescript-eslint/no-unused-vars

./src/components/JointJs/Palette/Palette.tsx
204:6  Warning: React Hook useEffect has missing dependencies: 'dispatch', 'setDiagramId', 'setDiagramName', and 'setSelectedElement'. Either include them or remove the dependency array.  react-hooks/exhaustive-deps
213:75  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
573:25  Warning: Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element  @next/next/no-img-element
636:25  Warning: Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element  @next/next/no-img-element
700:23  Warning: Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element  @next/next/no-img-element
741:47  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
809:23  Warning: Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element  @next/next/no-img-element
848:11  Warning: Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element  @next/next/no-img-element

./src/components/JointJSWidget/DiagramMeasureSelect.tsx
95:37  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
106:43  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/components/JointJSWidget/DiagramRenderVariables.tsx
39:18  Warning: Forbidden non-null assertion.  @typescript-eslint/no-non-null-assertion
82:5  Warning: Forbidden non-null assertion.  @typescript-eslint/no-non-null-assertion
97:41  Warning: Forbidden non-null assertion.  @typescript-eslint/no-non-null-assertion
104:9  Warning: 'isInvalid' is assigned a value but never used.  @typescript-eslint/no-unused-vars

./src/components/JointJSWidget/DiagramWidgetSettings.tsx
31:16  Warning: 'data' is defined but never used.  @typescript-eslint/no-unused-vars
43:5  Warning: 'isLoadingDashboards' is assigned a value but never used.  @typescript-eslint/no-unused-vars
45:5  Warning: 'dashboardOptions' is assigned a value but never used.  @typescript-eslint/no-unused-vars
48:5  Warning: 'handleChangeDashboard' is assigned a value but never used.  @typescript-eslint/no-unused-vars
73:67  Warning: '_' is defined but never used.  @typescript-eslint/no-unused-vars
107:6  Warning: React Hook useEffect has a missing dependency: 'handleSettingsChange'. Either include it or remove the dependency array. If 'handleSettingsChange' changes too often, find the parent component that defines it and wrap that definition in useCallback.  react-hooks/exhaustive-deps
149:32  Warning: Forbidden non-null assertion.  @typescript-eslint/no-non-null-assertion

./src/components/JointJSWidget/JointJsPaper.tsx
3:10  Warning: 'debounce' is defined but never used.  @typescript-eslint/no-unused-vars
43:3  Warning: 'setZoomLevelOnWidget' is defined but never used.  @typescript-eslint/no-unused-vars
63:6  Warning: React Hook useEffect has missing dependencies: 'paperInstanceRef' and 'setZoomLevel'. Either include them or remove the dependency array. If 'setZoomLevel' changes too often, find the parent component that defines it and wrap that definition in useCallback.  react-hooks/exhaustive-deps
99:43  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
165:16  Warning: The ref value 'paperRef.current' will likely have changed by the time this effect cleanup function runs. If this ref points to a node rendered by React, copy 'paperRef.current' to a variable inside the effect, and use that variable in the cleanup function.  react-hooks/exhaustive-deps
167:6  Warning: React Hook useEffect has missing dependencies: 'paperInstanceRef', 'zoomIn', and 'zoomOut'. Either include them or remove the dependency array.  react-hooks/exhaustive-deps
492:7  Warning: React Hook useEffect has a complex expression in the dependency array. Extract it to a separate variable so it can be statically checked.  react-hooks/exhaustive-deps

./src/components/JointJSWidget/JointJSWidget.tsx
59:10  Warning: 'jsonError' is assigned a value but never used.  @typescript-eslint/no-unused-vars
64:16  Warning: 'loadingData' is assigned a value but never used.  @typescript-eslint/no-unused-vars
65:5  Warning: 'isError' is assigned a value but never used.  @typescript-eslint/no-unused-vars
66:5  Warning: 'data' is assigned a value but never used.  @typescript-eslint/no-unused-vars
73:9  Warning: Forbidden non-null assertion.  @typescript-eslint/no-non-null-assertion
76:13  Warning: Forbidden non-null assertion.  @typescript-eslint/no-non-null-assertion
77:11  Warning: Forbidden non-null assertion.  @typescript-eslint/no-non-null-assertion
79:9  Warning: Forbidden non-null assertion.  @typescript-eslint/no-non-null-assertion
83:38  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
85:30  Warning: Forbidden non-null assertion.  @typescript-eslint/no-non-null-assertion
105:29  Warning: Forbidden non-null assertion.  @typescript-eslint/no-non-null-assertion
112:9  Warning: Forbidden non-null assertion.  @typescript-eslint/no-non-null-assertion
184:9  Warning: Forbidden non-null assertion.  @typescript-eslint/no-non-null-assertion
228:6  Warning: React Hook useEffect has missing dependencies: 'id' and 'settings'. Either include them or remove the dependency array.  react-hooks/exhaustive-deps
239:23  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
260:6  Warning: React Hook useEffect has missing dependencies: 'dispatch', 'id', and 'settings'. Either include them or remove the dependency array.  react-hooks/exhaustive-deps

./src/components/MapWidget/MapMarker.tsx
8:3  Warning: 'Card' is defined but never used.  @typescript-eslint/no-unused-vars
139:6  Warning: React Hook useEffect has missing dependencies: 'currentSettings', 'handleSettingsChange', and 'settings.markers'. Either include them or remove the dependency array. If 'handleSettingsChange' changes too often, find the parent component that defines it and wrap that definition in useCallback.  react-hooks/exhaustive-deps
141:5  Warning: React Hook useEffect has a complex expression in the dependency array. Extract it to a separate variable so it can be statically checked.  react-hooks/exhaustive-deps
143:5  Warning: React Hook useEffect has a complex expression in the dependency array. Extract it to a separate variable so it can be statically checked.  react-hooks/exhaustive-deps
164:6  Warning: React Hook useEffect has missing dependencies: 'currentSettings' and 'handleSettingsChange'. Either include them or remove the dependency array. If 'handleSettingsChange' changes too often, find the parent component that defines it and wrap that definition in useCallback.  react-hooks/exhaustive-deps
164:22  Warning: React Hook useEffect has a complex expression in the dependency array. Extract it to a separate variable so it can be statically checked.  react-hooks/exhaustive-deps
203:15  Warning: 'name' is assigned a value but never used.  @typescript-eslint/no-unused-vars
221:15  Warning: 'name' is assigned a value but never used.  @typescript-eslint/no-unused-vars
293:15  Warning: 'name' is assigned a value but never used.  @typescript-eslint/no-unused-vars
314:15  Warning: 'name' is assigned a value but never used.  @typescript-eslint/no-unused-vars
420:9  Warning: 'handleDashboardChange' is assigned a value but never used.  @typescript-eslint/no-unused-vars

./src/components/MapWidget/MapMeasureSelect.tsx
94:37  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
121:43  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/components/MapWidget/MapWidgetDialog.tsx
65:76  Warning: 'checked' is defined but never used.  @typescript-eslint/no-unused-vars

./src/components/MeasurementBrowser/CustomerMeasurementBrowser.tsx
120:10  Warning: 'filterModel' is assigned a value but never used.  @typescript-eslint/no-unused-vars
120:50  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
131:46  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
137:50  Warning: 'assetTypes' is assigned a value but never used.  @typescript-eslint/no-unused-vars
162:16  Warning: 'isAssetDataLoading' is assigned a value but never used.  @typescript-eslint/no-unused-vars
163:14  Warning: 'isMeasurementError' is assigned a value but never used.  @typescript-eslint/no-unused-vars
164:12  Warning: 'measurementError' is assigned a value but never used.  @typescript-eslint/no-unused-vars
176:44  Warning: 'success' is assigned a value but never used.  @typescript-eslint/no-unused-vars
219:47  Warning: 'error' is assigned a value but never used.  @typescript-eslint/no-unused-vars
226:6  Warning: React Hook useEffect has missing dependencies: 'showErrorAlert' and 'showSuccessAlert'. Either include them or remove the dependency array.  react-hooks/exhaustive-deps
256:6  Warning: React Hook useEffect has a missing dependency: 'applySorting'. Either include it or remove the dependency array.  react-hooks/exhaustive-deps
473:26  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
474:26  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
905:53  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/components/MeasurementTypes/MeasurementTypes.tsx
113:21  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
170:21  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/components/StatsSettings/StatsConfigSettings.tsx
34:65  Warning: 'child' is defined but never used.  @typescript-eslint/no-unused-vars

./src/components/TableSettings/TableSettingsDialog.tsx
16:10  Warning: 'title' is assigned a value but never used.  @typescript-eslint/no-unused-vars
20:10  Warning: 'selected' is assigned a value but never used.  @typescript-eslint/no-unused-vars
20:44  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
46:6  Warning: React Hook useEffect has a missing dependency: 'handleSettingsChange'. Either include it or remove the dependency array. If 'handleSettingsChange' changes too often, find the parent component that defines it and wrap that definition in useCallback.  react-hooks/exhaustive-deps

./src/components/TestDahboard/ChartsContainer.tsx
26:3  Warning: 'index' is defined but never used.  @typescript-eslint/no-unused-vars
38:22  Warning: 'tsData' is assigned a value but never used.  @typescript-eslint/no-unused-vars
63:15  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/components/TestDahboard/SearchForm.tsx
63:57  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
209:6  Warning: React Hook useEffect has missing dependencies: 'customerId' and 'dispatch'. Either include them or remove the dependency array.  react-hooks/exhaustive-deps
260:6  Warning: React Hook useEffect has missing dependencies: 'router.query' and 'submit'. Either include them or remove the dependency array.  react-hooks/exhaustive-deps
303:36  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/components/TestDahboard/TestDashboards.tsx
13:10  Warning: 'selectedAsset' is assigned a value but never used.  @typescript-eslint/no-unused-vars

./src/components/TitleSettings/TitleSettingsDialog.tsx
44:57  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
44:62  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
57:9  Warning: 'assetTypeTemplate' is assigned a value but never used.  @typescript-eslint/no-unused-vars
81:13  Warning: 'selectedMeasurement' is assigned a value but never used.  @typescript-eslint/no-unused-vars
84:6  Warning: React Hook useEffect has a missing dependency: 'settings.valueMode'. Either include it or remove the dependency array.  react-hooks/exhaustive-deps
103:6  Warning: React Hook useEffect has missing dependencies: 'handleSettingsChange' and 'settings.valueMode'. Either include them or remove the dependency array. If 'handleSettingsChange' changes too often, find the parent component that defines it and wrap that definition in useCallback.  react-hooks/exhaustive-deps
117:6  Warning: React Hook useEffect has a missing dependency: 'handleSettingsChange'. Either include it or remove the dependency array. If 'handleSettingsChange' changes too often, find the parent component that defines it and wrap that definition in useCallback.  react-hooks/exhaustive-deps
129:41  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
161:6  Warning: React Hook useEffect has missing dependencies: 'handleSettingsChange', 'settings.fixedColor', 'settings.fixedFontSize', and 'settings.fixedFontWeight'. Either include them or remove the dependency array. If 'handleSettingsChange' changes too often, find the parent component that defines it and wrap that definition in useCallback.  react-hooks/exhaustive-deps

./src/components/UnitsGroups/index.tsx
8:44  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/components/UnitsGroups/UnitGroupsForm.tsx
39:13  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
40:23  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
108:6  Warning: React Hook useEffect has missing dependencies: 'refetch', 'showErrorAlert', and 'showSuccessAlert'. Either include them or remove the dependency array.  react-hooks/exhaustive-deps
150:28  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
162:9  Warning: 'handleCreate' is assigned a value but never used.  @typescript-eslint/no-unused-vars

./src/components/UnitsOfMeasure/index.tsx
9:50  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/components/UnitsOfMeasure/UnitOfMeasuresData/index.tsx
30:16  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
31:25  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
63:6  Warning: React Hook useEffect has missing dependencies: 'refetch', 'showErrorAlert', and 'showSuccessAlert'. Either include them or remove the dependency array.  react-hooks/exhaustive-deps
73:6  Warning: React Hook useEffect has missing dependencies: 'refetch', 'showErrorAlert', and 'showSuccessAlert'. Either include them or remove the dependency array.  react-hooks/exhaustive-deps

./src/components/user/NewUserForm.tsx
11:25  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
17:7  Warning: 'roleList' is assigned a value but never used.  @typescript-eslint/no-unused-vars
51:6  Warning: React Hook useEffect has a missing dependency: 'reset'. Either include it or remove the dependency array.  react-hooks/exhaustive-deps

./src/hooks/useAlertAnalytics.ts
98:6  Warning: React Hook useEffect has a missing dependency: 'showErrorAlert'. Either include it or remove the dependency array. If 'showErrorAlert' changes too often, find the parent component that defines it and wrap that definition in useCallback.  react-hooks/exhaustive-deps

./src/hooks/useCommonWidgetHelper.ts
25:3  Warning: 'id' is defined but never used.  @typescript-eslint/no-unused-vars
42:40  Warning: 'isAssetReloading' is assigned a value but never used.  @typescript-eslint/no-unused-vars
77:16  Warning: 'isTemplateSuccess' is assigned a value but never used.  @typescript-eslint/no-unused-vars

./src/hooks/useCreateElementHook.ts
3:23  Warning: 'useSelector' is defined but never used.  @typescript-eslint/no-unused-vars
6:10  Warning: 'getElementsVariables' is defined but never used.  @typescript-eslint/no-unused-vars
196:69  Warning: 'handlerId' is defined but never used.  @typescript-eslint/no-unused-vars
346:20  Warning: Forbidden non-null assertion.  @typescript-eslint/no-non-null-assertion
359:20  Warning: Forbidden non-null assertion.  @typescript-eslint/no-non-null-assertion
402:6  Warning: React Hook useEffect has missing dependencies: 'addResizeDots', 'addRotationDots', 'clearHandlers', 'dispatch', 'hideIconTimeoutRef', 'paperRef', 'resizeDotsRef', and 'rotationDotsRef'. Either include them or remove the dependency array.  react-hooks/exhaustive-deps
437:26  Warning: Forbidden non-null assertion.  @typescript-eslint/no-non-null-assertion
501:37  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
503:22  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/hooks/useCreateTemplateFromAsset.ts
31:3  Warning: 'CalculationMeasurementSchemaData' is defined but never used.  @typescript-eslint/no-unused-vars
49:57  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
49:62  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
374:6  Warning: React Hook useEffect has a missing dependency: 'setMeasurementIds'. Either include it or remove the dependency array. If 'setMeasurementIds' changes too often, find the parent component that defines it and wrap that definition in useCallback.  react-hooks/exhaustive-deps

./src/hooks/useDashboardWidgetContainer.ts
19:27  Warning: 'layout' is defined but never used.  @typescript-eslint/no-unused-vars
26:64  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
48:5  Warning: 'oldItem' is defined but never used.  @typescript-eslint/no-unused-vars
49:5  Warning: 'newItem' is defined but never used.  @typescript-eslint/no-unused-vars
50:5  Warning: 'placeholder' is defined but never used.  @typescript-eslint/no-unused-vars
51:5  Warning: 'event' is defined but never used.  @typescript-eslint/no-unused-vars
52:5  Warning: 'element' is defined but never used.  @typescript-eslint/no-unused-vars

./src/hooks/useDiagramComponentHook.ts
79:3  Warning: 'rotateIconRef' is defined but never used.  @typescript-eslint/no-unused-vars
95:18  Warning: 'setErrors' is assigned a value but never used.  @typescript-eslint/no-unused-vars
113:5  Warning: 'setEditLinkDialogOpen' is assigned a value but never used.  @typescript-eslint/no-unused-vars
114:5  Warning: 'setLinkAttrs' is assigned a value but never used.  @typescript-eslint/no-unused-vars
132:9  Warning: 'selectedLink' is assigned a value but never used.  @typescript-eslint/no-unused-vars
182:7  Warning: Forbidden non-null assertion.  @typescript-eslint/no-non-null-assertion
185:41  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
187:28  Warning: Forbidden non-null assertion.  @typescript-eslint/no-non-null-assertion
210:27  Warning: Forbidden non-null assertion.  @typescript-eslint/no-non-null-assertion
219:6  Warning: React Hook useEffect has missing dependencies: 'graphRef', 'injectPatternIntoDefs', 'paperInstanceRef', 'setElementVairiables', and 'setZoomLevel'. Either include them or remove the dependency array.  react-hooks/exhaustive-deps
224:6  Warning: React Hook useEffect has missing dependencies: 'dispatch' and 'setDiagram'. Either include them or remove the dependency array.  react-hooks/exhaustive-deps
272:6  Warning: React Hook useEffect has a missing dependency: 'graphRef'. Either include it or remove the dependency array.  react-hooks/exhaustive-deps
272:7  Warning: React Hook useEffect has a complex expression in the dependency array. Extract it to a separate variable so it can be statically checked.  react-hooks/exhaustive-deps
529:26  Warning: Forbidden non-null assertion.  @typescript-eslint/no-non-null-assertion
541:37  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
554:35  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
555:32  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
560:47  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
567:37  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
575:20  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
579:58  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
584:20  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
586:53  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
587:59  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
599:57  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
607:39  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
608:34  Warning: Forbidden non-null assertion.  @typescript-eslint/no-non-null-assertion
610:15  Warning: Forbidden non-null assertion.  @typescript-eslint/no-non-null-assertion
615:39  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
617:34  Warning: Forbidden non-null assertion.  @typescript-eslint/no-non-null-assertion
619:34  Warning: Forbidden non-null assertion.  @typescript-eslint/no-non-null-assertion
712:9  Warning: 'clearHandlers' is assigned a value but never used.  @typescript-eslint/no-unused-vars

./src/hooks/useDiagramHelper.ts
46:9  Warning: 'zoomLevel' is assigned a value but never used.  @typescript-eslint/no-unused-vars
52:5  Warning: 'setIconPosition' is assigned a value but never used.  @typescript-eslint/no-unused-vars
53:5  Warning: 'setLinkAttrs' is assigned a value but never used.  @typescript-eslint/no-unused-vars
89:9  Warning: The 'zoomIn' function makes the dependencies of useEffect Hook (at line 642) change on every render. To fix this, wrap the definition of 'zoomIn' in its own useCallback() Hook.  react-hooks/exhaustive-deps
99:9  Warning: The 'zoomOut' function makes the dependencies of useEffect Hook (at line 642) change on every render. To fix this, wrap the definition of 'zoomOut' in its own useCallback() Hook.  react-hooks/exhaustive-deps
131:9  Warning: 'createPipeLink' is assigned a value but never used.  @typescript-eslint/no-unused-vars
209:40  Warning: Forbidden non-null assertion.  @typescript-eslint/no-non-null-assertion
228:11  Warning: 'aspectRatio' is assigned a value but never used.  @typescript-eslint/no-unused-vars
426:27  Warning: Forbidden non-null assertion.  @typescript-eslint/no-non-null-assertion
490:69  Warning: 'handlerId' is defined but never used.  @typescript-eslint/no-unused-vars
584:6  Warning: React Hook useEffect has missing dependencies: 'deleteSelectedElement' and 'graphRef'. Either include them or remove the dependency array.  react-hooks/exhaustive-deps
621:6  Warning: React Hook useEffect has a missing dependency: 'graphRef'. Either include it or remove the dependency array.  react-hooks/exhaustive-deps
687:43  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
800:22  Warning: Forbidden non-null assertion.  @typescript-eslint/no-non-null-assertion
898:22  Warning: Forbidden non-null assertion.  @typescript-eslint/no-non-null-assertion
1008:6  Warning: React Hook useEffect has missing dependencies: 'addResizeDots', 'addRotationDots', 'clearHandlers', 'deleteIconPositionRef', 'deleteSelectedElement', 'dispatch', 'graphRef', 'handleElementSelect', 'handleOpenLinkEditDialog', 'hideIconTimeoutRef', 'highlightSelectedPort', 'paperRef', 'removeAllSlectedElemts', 'resetAllPortHighlights', 'resizeDotsRef', 'rotationDotsRef', 'setHoveredLink', 'setIconVisible', 'setSelectedElement', 'setSelectedLink', 'sourceElementRef', and 'updateHandlersForPanning'. Either include them or remove the dependency array.  react-hooks/exhaustive-deps

./src/hooks/useDiagramWidgetData.ts
82:57  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
82:62  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
86:9  Warning: 'dbMeasureIdToAssetIdMap' is assigned a value but never used.  @typescript-eslint/no-unused-vars
286:5  Warning: React Hook useCallback has an unnecessary dependency: 'customerId'. Either exclude it or remove the dependency array.  react-hooks/exhaustive-deps
361:5  Warning: React Hook useCallback has missing dependencies: 'settings.endDate' and 'settings.startDate'. Either include them or remove the dependency array.  react-hooks/exhaustive-deps
549:6  Warning: React Hook useEffect has missing dependencies: 'assetMeasure', 'customerId', 'endDate', 'fetchAllAssetMeasures', 'samplePeriod', 'startDate', 'successAndFailedMeasurements', and 'timeRange'. Either include them or remove the dependency array.  react-hooks/exhaustive-deps
629:6  Warning: React Hook useEffect has missing dependencies: 'id' and 'settings'. Either include them or remove the dependency array.  react-hooks/exhaustive-deps
750:6  Warning: React Hook useEffect has missing dependencies: 'assetMeasure', 'assetMeasureVariables', 'assetTz', 'assetTzOverride', 'assetTzOverrideValue', 'endDate', 'fetchAllAssetMeasures', 'fetchHistoryTimeseriesData', 'globalSamplePeriod', 'samplePeriod', 'settings.aggBy', 'startDate', 'state', 'successAndFailedMeasurements', and 'timeRange'. Either include them or remove the dependency array. You can also do a functional update 'setState(s => ...)' if you only need 'state' in the 'setState' call.  react-hooks/exhaustive-deps

./src/hooks/useDiagramWidgetSettingsHelper.ts
29:9  Warning: 'dateTimeFormat' is assigned a value but never used.  @typescript-eslint/no-unused-vars
30:9  Warning: 'globalStartDate' is assigned a value but never used.  @typescript-eslint/no-unused-vars
31:9  Warning: 'assetTz' is assigned a value but never used.  @typescript-eslint/no-unused-vars
32:9  Warning: 'globalendDate' is assigned a value but never used.  @typescript-eslint/no-unused-vars
73:11  Warning: Forbidden non-null assertion.  @typescript-eslint/no-non-null-assertion
74:9  Warning: Forbidden non-null assertion.  @typescript-eslint/no-non-null-assertion
76:7  Warning: Forbidden non-null assertion.  @typescript-eslint/no-non-null-assertion
78:6  Warning: React Hook useEffect has a missing dependency: 'graphRef'. Either include it or remove the dependency array.  react-hooks/exhaustive-deps
83:11  Warning: Forbidden non-null assertion.  @typescript-eslint/no-non-null-assertion
86:15  Warning: Forbidden non-null assertion.  @typescript-eslint/no-non-null-assertion
87:13  Warning: Forbidden non-null assertion.  @typescript-eslint/no-non-null-assertion
89:11  Warning: Forbidden non-null assertion.  @typescript-eslint/no-non-null-assertion
93:11  Warning: Forbidden non-null assertion.  @typescript-eslint/no-non-null-assertion
179:11  Warning: Forbidden non-null assertion.  @typescript-eslint/no-non-null-assertion
222:6  Warning: React Hook useEffect has missing dependencies: 'graphRef', 'handleSettingsChange', and 'settings'. Either include them or remove the dependency array. If 'handleSettingsChange' changes too often, find the parent component that defines it and wrap that definition in useCallback.  react-hooks/exhaustive-deps
244:5  Warning: Forbidden non-null assertion.  @typescript-eslint/no-non-null-assertion

./src/hooks/useExportDashboadTemplate.ts
8:57  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
8:62  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/hooks/useFetchAllMeasures.ts
9:57  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
9:62  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/hooks/useFetchAnnotationData.ts
12:10  Warning: 'formatChartDateToAssetTz' is defined but never used.  @typescript-eslint/no-unused-vars
16:9  Warning: 'dateTimeFormat' is assigned a value but never used.  @typescript-eslint/no-unused-vars
17:46  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
17:51  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
17:56  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
35:6  Warning: React Hook useEffect has missing dependencies: 'state.mode' and 'state.selectedTitles'. Either include them or remove the dependency array.  react-hooks/exhaustive-deps
91:5  Warning: React Hook useCallback has a missing dependency: 'dashboard'. Either include it or remove the dependency array.  react-hooks/exhaustive-deps
169:6  Warning: React Hook useEffect has missing dependencies: 'fetchMeasureDataByAsset', 'state.assetMeasure', 'state.dbMeasureIdToAnnotation', and 'state.mode'. Either include them or remove the dependency array.  react-hooks/exhaustive-deps

./src/hooks/useFetchAssetMeausrementsHistory.ts
44:57  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
44:62  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
131:23  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/hooks/useFetchAssetPath.ts
9:57  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
9:62  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
39:6  Warning: React Hook useEffect has missing dependencies: 'customerId' and 'dispatch'. Either include them or remove the dependency array.  react-hooks/exhaustive-deps

./src/hooks/useFetchBarData.ts
2:24  Warning: 'TypedArray' is defined but never used.  @typescript-eslint/no-unused-vars
240:13  Warning: 'values' is assigned a value but never used.  @typescript-eslint/no-unused-vars
477:6  Warning: React Hook useEffect has a missing dependency: 'state.mode'. Either include it or remove the dependency array.  react-hooks/exhaustive-deps
578:6  Warning: React Hook useEffect has missing dependencies: 'selectedMeasures', 'state', and 'useAssetTz'. Either include them or remove the dependency array.  react-hooks/exhaustive-deps

./src/hooks/useFetchBulletChartData.ts
30:3  Warning: 'selectedMeasureName' is defined but never used.  @typescript-eslint/no-unused-vars
169:6  Warning: React Hook useEffect has a missing dependency: 'state.mode'. Either include it or remove the dependency array.  react-hooks/exhaustive-deps
220:6  Warning: React Hook useEffect has missing dependencies: 'dbMeasureIdToName' and 'selectedDbMeasureId'. Either include them or remove the dependency array.  react-hooks/exhaustive-deps

./src/hooks/useFetchCustomersByAdminRole.ts
4:10  Warning: 'UserDetailsResponse' is defined but never used.  @typescript-eslint/no-unused-vars
8:41  Warning: 'isUserDetailLoading' is assigned a value but never used.  @typescript-eslint/no-unused-vars
9:39  Warning: 'isCustomerDetailLoading' is assigned a value but never used.  @typescript-eslint/no-unused-vars

./src/hooks/useFetchDynamicChart.ts
85:5  Warning: 'chartNumber' is assigned a value but never used.  @typescript-eslint/no-unused-vars
102:57  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
102:62  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
165:6  Warning: React Hook useEffect has missing dependencies: 'activeCustomer?.id' and 'dispatch'. Either include them or remove the dependency array.  react-hooks/exhaustive-deps

./src/hooks/useFetchGaugeData.ts
30:3  Warning: 'selectedMeasureName' is defined but never used.  @typescript-eslint/no-unused-vars
31:3  Warning: 'selectedTitles' is defined but never used.  @typescript-eslint/no-unused-vars
167:6  Warning: React Hook useEffect has a missing dependency: 'state.mode'. Either include it or remove the dependency array.  react-hooks/exhaustive-deps
243:6  Warning: React Hook useEffect has missing dependencies: 'selectedDbMeasureId' and 'selectedTitles'. Either include them or remove the dependency array.  react-hooks/exhaustive-deps

./src/hooks/useFetchHeatMapData.ts
33:9  Warning: 'prevResultsRef' is assigned a value but never used.  @typescript-eslint/no-unused-vars
169:6  Warning: React Hook useEffect has a missing dependency: 'state.mode'. Either include it or remove the dependency array.  react-hooks/exhaustive-deps
221:6  Warning: React Hook useEffect has missing dependencies: 'router.pathname', 'selectedTitles', 'state.dbMeasureIdToName', and 'transformHeatmapDataForPlotly'. Either include them or remove the dependency array.  react-hooks/exhaustive-deps

./src/hooks/useFetchHeatMapMeasuresTsData.ts
82:10  Warning: 'assetTzOverride' is assigned a value but never used.  @typescript-eslint/no-unused-vars
320:6  Warning: React Hook useEffect has a missing dependency: 'router.pathname'. Either include it or remove the dependency array.  react-hooks/exhaustive-deps

./src/hooks/useFetchImageMeasuresTsData.ts
256:6  Warning: React Hook useEffect has a missing dependency: 'fetchMeasuresData'. Either include it or remove the dependency array.  react-hooks/exhaustive-deps
362:6  Warning: React Hook useEffect has missing dependencies: 'fetchHistoryTimeseriesData', 'fetchMeasureData', 'fetchUnitOfMeasure', and 'state'. Either include them or remove the dependency array. You can also do a functional update 'setState(s => ...)' if you only need 'state' in the 'setState' call.  react-hooks/exhaustive-deps

./src/hooks/useFetchImageWidget.ts
50:9  Warning: 'dispatch' is assigned a value but never used.  @typescript-eslint/no-unused-vars
89:6  Warning: React Hook useEffect has a missing dependency: 'state.mode'. Either include it or remove the dependency array.  react-hooks/exhaustive-deps
135:6  Warning: React Hook useEffect has missing dependencies: 'router.pathname' and 'thousandSeparator'. Either include them or remove the dependency array.  react-hooks/exhaustive-deps

./src/hooks/useFetchJointJSData.tsx
30:3  Warning: 'selectedSamplePeriod' is defined but never used.  @typescript-eslint/no-unused-vars
40:9  Warning: 'unit' is assigned a value but never used.  @typescript-eslint/no-unused-vars
49:57  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
49:62  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
167:6  Warning: React Hook useEffect has an unnecessary dependency: 'state.current'. Either exclude it or remove the dependency array. Mutable values like 'state.current' aren't valid dependencies because mutating them doesn't re-render the component.  react-hooks/exhaustive-deps

./src/hooks/useFetchKPIBar.ts
162:57  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
162:62  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
225:10  Warning: 'startDate' is assigned a value but never used.  @typescript-eslint/no-unused-vars
226:10  Warning: 'endDate' is assigned a value but never used.  @typescript-eslint/no-unused-vars
228:10  Warning: 'samplePeriod' is assigned a value but never used.  @typescript-eslint/no-unused-vars
258:6  Warning: React Hook useEffect has missing dependencies: 'globalSamplePeriod' and 'isGlobalSamplePeriodOverridden'. Either include them or remove the dependency array.  react-hooks/exhaustive-deps
317:5  Warning: React Hook useCallback has unnecessary dependencies: 'dbMeasureIdToAssetIdMap' and 'selectedTitles'. Either exclude them or remove the dependency array.  react-hooks/exhaustive-deps
362:5  Warning: React Hook useCallback has unnecessary dependencies: 'selectedTitles', 'settings.selectedDbMeasureId', 'settings.selectedSamplePeriod', and 'settings.showPrevious'. Either exclude them or remove the dependency array.  react-hooks/exhaustive-deps
464:6  Warning: React Hook useEffect has missing dependencies: 'aggBy', 'fetchMeasureDataByAsset', 'resultData', and 'settings'. Either include them or remove the dependency array. You can also do a functional update 'setResultData(r => ...)' if you only need 'resultData' in the 'setResultData' call.  react-hooks/exhaustive-deps
499:6  Warning: React Hook useEffect has missing dependencies: 'resultData', 'settings', and 'useAssetTz'. Either include them or remove the dependency array. You can also do a functional update 'setResultData(r => ...)' if you only need 'resultData' in the 'setResultData' call.  react-hooks/exhaustive-deps

./src/hooks/useFetchKPIBars.ts
298:6  Warning: React Hook useEffect has missing dependencies: 'router.pathname', 'selectedTitles', and 'state.title'. Either include them or remove the dependency array.  react-hooks/exhaustive-deps

./src/hooks/useFetchKPIColorBoxdata.ts
197:9  Warning: 'chartNumber' is assigned a value but never used.  @typescript-eslint/no-unused-vars
379:6  Warning: React Hook useEffect has a missing dependency: 'selectedDbMeasureIdToName'. Either include it or remove the dependency array.  react-hooks/exhaustive-deps

./src/hooks/useFetchKPICurrentWidget.ts
22:57  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
22:62  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
36:6  Warning: React Hook useEffect has a missing dependency: 'settings.mode'. Either include it or remove the dependency array.  react-hooks/exhaustive-deps
66:10  Warning: 'samplePeriod' is assigned a value but never used.  @typescript-eslint/no-unused-vars
71:23  Warning: 'setMeasureData' is assigned a value but never used.  @typescript-eslint/no-unused-vars
113:9  Warning: 'fetchMeasureData' is assigned a value but never used.  @typescript-eslint/no-unused-vars
181:5  Warning: React Hook useCallback has unnecessary dependencies: 'aggBy', 'endDate', 'settings.period', 'settings.samples', 'settings.selectedDbMeasureId', and 'startDate'. Either exclude them or remove the dependency array.  react-hooks/exhaustive-deps
218:5  Warning: React Hook useCallback has unnecessary dependencies: 'dbMeasureIdToAssetIdMap' and 'selectedTitles'. Either exclude them or remove the dependency array.  react-hooks/exhaustive-deps
373:6  Warning: React Hook useEffect has missing dependencies: 'assetTz', 'fetchMeasureDataByAsset', 'fetchTimeseriesData', 'fetchUnitOfMeasure', 'isOverrideAssetTz', 'router.pathname', 'settings.assetMeasure.assetId', and 'settings.assetMeasure.measureId'. Either include them or remove the dependency array.  react-hooks/exhaustive-deps

./src/hooks/useFetchKPISparkLine.ts
119:7  Warning: 'chartNumber' is assigned a value but never used.  @typescript-eslint/no-unused-vars
321:6  Warning: React Hook useEffect has a missing dependency: 'useAssetTz'. Either include it or remove the dependency array.  react-hooks/exhaustive-deps

./src/hooks/useFetchKPITableData.ts
191:9  Warning: 'layout' is assigned a value but never used.  @typescript-eslint/no-unused-vars
355:6  Warning: React Hook useEffect has a missing dependency: 'state.mode'. Either include it or remove the dependency array.  react-hooks/exhaustive-deps
435:6  Warning: React Hook useEffect has missing dependencies: 'selectedMeasures', 'state.mode', 'thousandSeparator', and 'useAssetTz'. Either include them or remove the dependency array. Mutable values like 'state.current' aren't valid dependencies because mutating them doesn't re-render the component.  react-hooks/exhaustive-deps

./src/hooks/useFetchKPIValueIndicator.ts
1:10  Warning: 'DataWidget' is defined but never used.  @typescript-eslint/no-unused-vars
6:10  Warning: 'useFetchMeasuresTsData' is defined but never used.  @typescript-eslint/no-unused-vars
19:61  Warning: 'state' is defined but never used.  @typescript-eslint/no-unused-vars
43:9  Warning: 'selectedDbMeasureId' is assigned a value but never used.  @typescript-eslint/no-unused-vars
65:6  Warning: React Hook useEffect has a missing dependency: 'state.mode'. Either include it or remove the dependency array.  react-hooks/exhaustive-deps
121:6  Warning: React Hook useEffect has a missing dependency: 'allDataFetched'. Either include it or remove the dependency array. You can also do a functional update 'setAllDataFetched(a => ...)' if you only need 'allDataFetched' in the 'setAllDataFetched' call.  react-hooks/exhaustive-deps

./src/hooks/useFetchMapData.ts
202:57  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
202:62  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
246:6  Warning: React Hook useEffect has a missing dependency: 'settings.mode'. Either include it or remove the dependency array.  react-hooks/exhaustive-deps
307:5  Warning: React Hook useCallback has an unnecessary dependency: 'customerId'. Either exclude it or remove the dependency array.  react-hooks/exhaustive-deps
493:6  Warning: React Hook useEffect has missing dependencies: 'customerId', 'dashboardState.topPanel.samplePeriod', 'dispatch', 'endDate', 'fetchAllAssetMeasures', 'router.pathname', 'selectedTitles', 'settings.mode', 'settings.timeRange', 'startDate', 'successAndFailedMeasurements', and 'timeRangeType'. Either include them or remove the dependency array.  react-hooks/exhaustive-deps
525:6  Warning: React Hook useEffect has missing dependencies: 'allDataFetched' and 'settings'. Either include them or remove the dependency array. You can also do a functional update 'setAllDataFetched(a => ...)' if you only need 'allDataFetched' in the 'setAllDataFetched' call.  react-hooks/exhaustive-deps

./src/hooks/useFetchMeasuresTsData.ts
14:10  Warning: 'AssetMeasureOptions' is defined but never used.  @typescript-eslint/no-unused-vars
21:30  Warning: 'isDashboardTemplatePage' is defined but never used.  @typescript-eslint/no-unused-vars
190:5  Warning: React Hook useCallback has missing dependencies: 'globalSamplePeriod', 'globalStartDate', 'isGlobalSamplePeriodOverridden', 'isGlobalTimeRangeSettingsOverridden', 'selectedEndDate', 'selectedSamplePeriod', and 'selectedStartDate'. Either include them or remove the dependency array.  react-hooks/exhaustive-deps
236:5  Warning: React Hook useCallback has an unnecessary dependency: 'assetTz'. Either exclude it or remove the dependency array.  react-hooks/exhaustive-deps
314:13  Warning: 'res' is assigned a value but never used.  @typescript-eslint/no-unused-vars
353:6  Warning: React Hook useCallback has an unnecessary dependency: 'isRealTime'. Either exclude it or remove the dependency array.  react-hooks/exhaustive-deps
414:6  Warning: React Hook useEffect has missing dependencies: 'fetchMeasuresData', 'metricToName', and 'router.pathname'. Either include them or remove the dependency array.  react-hooks/exhaustive-deps
522:6  Warning: React Hook useEffect has missing dependencies: 'fetchHistoryTimeseriesData', 'fetchMeasureData', 'fetchUnitOfMeasure', and 'state'. Either include them or remove the dependency array. You can also do a functional update 'setState(s => ...)' if you only need 'state' in the 'setState' call.  react-hooks/exhaustive-deps

./src/hooks/useFetchMultiForecast.ts
26:46  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
26:51  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
26:56  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
156:29  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
250:6  Warning: React Hook useEffect has missing dependencies: 'fetchForecastData', 'fetchMeasureDataByAsset', and 'fetchUnitOfMeasure'. Either include them or remove the dependency array.  react-hooks/exhaustive-deps

./src/hooks/useFetchMultiPlotData.ts
185:76  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
189:80  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
232:6  Warning: React Hook useEffect has a missing dependency: 'metricsIdToName'. Either include it or remove the dependency array.  react-hooks/exhaustive-deps

./src/hooks/useFetchRealTimeTsData.ts
6:10  Warning: 'useCustomTimeInterval' is defined but never used.  @typescript-eslint/no-unused-vars
7:10  Warning: 'dashboardSlice' is defined but never used.  @typescript-eslint/no-unused-vars
35:57  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
35:62  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
37:9  Warning: 'metricToName' is assigned a value but never used.  @typescript-eslint/no-unused-vars
50:54  Warning: 'widgetId' is assigned a value but never used.  @typescript-eslint/no-unused-vars
192:6  Warning: React Hook useEffect has missing dependencies: 'fetchMeasureData', 'fetchTimeseriesData', 'fetchUnitOfMeasure', and 'state'. Either include them or remove the dependency array. You can also do a functional update 'setState(s => ...)' if you only need 'state' in the 'setState' call.  react-hooks/exhaustive-deps

./src/hooks/useFetchSankeyData.ts
69:22  Warning: Forbidden non-null assertion.  @typescript-eslint/no-non-null-assertion
70:23  Warning: Forbidden non-null assertion.  @typescript-eslint/no-non-null-assertion
74:5  Warning: Forbidden non-null assertion.  @typescript-eslint/no-non-null-assertion
88:18  Warning: Forbidden non-null assertion.  @typescript-eslint/no-non-null-assertion
91:7  Warning: Forbidden non-null assertion.  @typescript-eslint/no-non-null-assertion
107:19  Warning: Forbidden non-null assertion.  @typescript-eslint/no-non-null-assertion
217:57  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
217:62  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
257:27  Warning: Forbidden non-null assertion.  @typescript-eslint/no-non-null-assertion
258:29  Warning: Forbidden non-null assertion.  @typescript-eslint/no-non-null-assertion
262:20  Warning: Forbidden non-null assertion.  @typescript-eslint/no-non-null-assertion
263:22  Warning: Forbidden non-null assertion.  @typescript-eslint/no-non-null-assertion
383:5  Warning: React Hook useCallback has an unnecessary dependency: 'customerId'. Either exclude it or remove the dependency array.  react-hooks/exhaustive-deps
527:5  Warning: React Hook useCallback has an unnecessary dependency: 'customerId'. Either exclude it or remove the dependency array.  react-hooks/exhaustive-deps
601:9  Warning: Forbidden non-null assertion.  @typescript-eslint/no-non-null-assertion
744:6  Warning: React Hook useEffect has missing dependencies: 'fetchAllAssetMeasures', 'fetchTimeseriesDataWithAgg', 'groupAssetMeasures', 'metricToName', 'router.pathname', and 'successAndFailedMeasurements'. Either include them or remove the dependency array.  react-hooks/exhaustive-deps
902:6  Warning: React Hook useEffect has missing dependencies: 'customerId' and 'fetchAllAssetMeasures'. Either include them or remove the dependency array.  react-hooks/exhaustive-deps
1020:6  Warning: React Hook useEffect has missing dependencies: 'fetchAllAssetMeasures', 'fetchHistoryTimeseriesData', 'groupAssetMeasures', 'state', and 'successAndFailedMeasurements'. Either include them or remove the dependency array. You can also do a functional update 'setState(s => ...)' if you only need 'state' in the 'setState' call.  react-hooks/exhaustive-deps
1109:6  Warning: React Hook useEffect has missing dependencies: 'allDataFetched', 'router.pathname', and 'settings'. Either include them or remove the dependency array. You can also do a functional update 'setAllDataFetched(a => ...)' if you only need 'allDataFetched' in the 'setAllDataFetched' call.  react-hooks/exhaustive-deps

./src/hooks/useFetchScatterData.ts
424:9  Warning: 'chartNumber' is assigned a value but never used.  @typescript-eslint/no-unused-vars
602:6  Warning: React Hook useEffect has a missing dependency: 'state.mode'. Either include it or remove the dependency array.  react-hooks/exhaustive-deps
729:6  Warning: React Hook useEffect has missing dependencies: 'selectedMesures', 'state', and 'useAssetTz'. Either include them or remove the dependency array.  react-hooks/exhaustive-deps

./src/hooks/useFetchScatterTest.ts
111:5  Warning: 'chartNumber' is assigned a value but never used.  @typescript-eslint/no-unused-vars
213:57  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
213:62  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
318:6  Warning: React Hook useEffect has a missing dependency: 'dispatch'. Either include it or remove the dependency array.  react-hooks/exhaustive-deps
362:6  Warning: React Hook useEffect has missing dependencies: 'color', 'dbMeasureIdToName', and 'measureId'. Either include them or remove the dependency array.  react-hooks/exhaustive-deps

./src/hooks/useFetchStatsData.ts
1:21  Warning: 'useRef' is defined but never used.  @typescript-eslint/no-unused-vars

./src/hooks/useFetchTableData.ts
35:9  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
48:15  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
140:11  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
174:6  Warning: React Hook useEffect has a missing dependency: 'state.mode'. Either include it or remove the dependency array.  react-hooks/exhaustive-deps
253:6  Warning: React Hook useEffect has a missing dependency: 'useAssetTz'. Either include it or remove the dependency array.  react-hooks/exhaustive-deps

./src/hooks/useFetchTitleData.ts
93:6  Warning: React Hook useEffect has missing dependencies: 'state.mode' and 'state.selectedDbMeasureId'. Either include them or remove the dependency array. If 'setSelectedMeasureId' needs the current value of 'state.selectedDbMeasureId', you can also switch to useReducer instead of useState and read 'state.selectedDbMeasureId' in the reducer.  react-hooks/exhaustive-deps

./src/hooks/useFetchTrends.ts
8:6  Warning: 'TrendResult' is defined but never used.  @typescript-eslint/no-unused-vars

./src/hooks/useFetchUniqueLocation.ts
19:57  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
19:62  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
42:6  Warning: React Hook useEffect has missing dependencies: 'currentSettings' and 'settings.markers'. Either include them or remove the dependency array.  react-hooks/exhaustive-deps
42:7  Warning: React Hook useEffect has a complex expression in the dependency array. Extract it to a separate variable so it can be statically checked.  react-hooks/exhaustive-deps
111:6  Warning: React Hook useEffect has a missing dependency: 'dispatch'. Either include it or remove the dependency array.  react-hooks/exhaustive-deps

./src/hooks/useFetchWeather.ts
35:57  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
35:62  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
57:6  Warning: React Hook useEffect has a missing dependency: 'dispatch'. Either include it or remove the dependency array.  react-hooks/exhaustive-deps

./src/hooks/useGetAffectedAssetWithTemplates.ts
16:57  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
16:62  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
17:9  Warning: 'customerId' is assigned a value but never used.  @typescript-eslint/no-unused-vars
31:33  Warning: Forbidden non-null assertion.  @typescript-eslint/no-non-null-assertion

./src/hooks/useGetAlertWidgetData.ts
27:9  Warning: 'periods' is assigned a value but never used.  @typescript-eslint/no-unused-vars
195:34  Warning: 'id' is defined but never used.  @typescript-eslint/no-unused-vars
197:57  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
197:62  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
244:8  Warning: React Hook useMemo has an unnecessary dependency: 'state.assetTypeMetrics'. Either exclude it or remove the dependency array.  react-hooks/exhaustive-deps
255:5  Warning: 'isError' is assigned a value but never used.  @typescript-eslint/no-unused-vars
291:38  Warning: Forbidden non-null assertion.  @typescript-eslint/no-non-null-assertion
423:6  Warning: React Hook useCallback has missing dependencies: 'activeCustomer?.id', 'getMeasuresByCustomer', and 'isFetchingAssets'. Either include them or remove the dependency array.  react-hooks/exhaustive-deps
450:6  Warning: React Hook useEffect has missing dependencies: 'data' and 'state.mode'. Either include them or remove the dependency array. You can also do a functional update 'setData(d => ...)' if you only need 'data' in the 'setData' call.  react-hooks/exhaustive-deps

./src/hooks/useGetHeatMapMeasuresTsData.ts
95:10  Warning: 'assetTzOverride' is assigned a value but never used.  @typescript-eslint/no-unused-vars
164:9  Warning: 'fetchMeasureDataByAsset' is assigned a value but never used.  @typescript-eslint/no-unused-vars
188:5  Warning: React Hook useCallback has an unnecessary dependency: 'dbMeasureIdToAssetIdMap'. Either exclude it or remove the dependency array.  react-hooks/exhaustive-deps
286:5  Warning: React Hook useCallback has unnecessary dependencies: 'assetMeasure' and 'customerId'. Either exclude them or remove the dependency array.  react-hooks/exhaustive-deps
322:5  Warning: React Hook useCallback has an unnecessary dependency: 'customerId'. Either exclude it or remove the dependency array.  react-hooks/exhaustive-deps
508:6  Warning: React Hook useEffect has missing dependencies: 'assetMeasure', 'fetchAllAssetMeasures', 'groupAssetMeasures', 'router.pathname', and 'successAndFailedMeasurements'. Either include them or remove the dependency array.  react-hooks/exhaustive-deps

./src/hooks/useGetMeasuresTsData.ts
219:5  Warning: React Hook useCallback has missing dependencies: 'globalSamplePeriod', 'globalStartDate', 'isGlobalSamplePeriodOverridden', 'isGlobalTimeRangeSettingsOverridden', 'selectedEndDate', 'selectedSamplePeriod', and 'selectedStartDate'. Either include them or remove the dependency array.  react-hooks/exhaustive-deps
310:5  Warning: React Hook useCallback has an unnecessary dependency: 'dbMeasureIdToAssetIdMap'. Either exclude it or remove the dependency array.  react-hooks/exhaustive-deps
344:5  Warning: React Hook useCallback has an unnecessary dependency: 'customerId'. Either exclude it or remove the dependency array.  react-hooks/exhaustive-deps
384:5  Warning: React Hook useCallback has a missing dependency: 'isOverrideAssetTz'. Either include it or remove the dependency array.  react-hooks/exhaustive-deps
436:5  Warning: React Hook useCallback has unnecessary dependencies: 'assetMeasure' and 'customerId'. Either exclude them or remove the dependency array.  react-hooks/exhaustive-deps
543:13  Warning: 'res' is assigned a value but never used.  @typescript-eslint/no-unused-vars
627:6  Warning: React Hook useCallback has missing dependencies: 'customerId', 'groupAssetMeasures', and 'successAndFailedMeasurements'. Either include them or remove the dependency array.  react-hooks/exhaustive-deps
704:6  Warning: React Hook useEffect has missing dependencies: 'assetMeasure', 'fetchMeasuresData', 'metricToName', and 'router.pathname'. Either include them or remove the dependency array.  react-hooks/exhaustive-deps
873:6  Warning: React Hook useEffect has missing dependencies: 'assetMeasure', 'fetchAllAssetMeasures', 'fetchHistoryTimeseriesData', 'groupAssetMeasures', 'state', and 'successAndFailedMeasurements'. Either include them or remove the dependency array. You can also do a functional update 'setState(s => ...)' if you only need 'state' in the 'setState' call.  react-hooks/exhaustive-deps

./src/hooks/useImageWidgetContainerHelper.ts
20:57  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
20:62  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
32:40  Warning: 'isAssetReloading' is assigned a value but never used.  @typescript-eslint/no-unused-vars
40:17  Warning: 'assetMeasurements' is assigned a value but never used.  @typescript-eslint/no-unused-vars

./src/hooks/useImageWidgetHook.ts
7:38  Warning: 'settings' is defined but never used.  @typescript-eslint/no-unused-vars
17:31  Warning: 'labelAndUnits' is assigned a value but never used.  @typescript-eslint/no-unused-vars

./src/hooks/useMultiPlotWidgetLogic.ts
50:58  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
78:26  Warning: 'subplotId' is defined but never used.  @typescript-eslint/no-unused-vars
117:27  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
118:27  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
155:9  Warning: Forbidden non-null assertion.  @typescript-eslint/no-non-null-assertion
157:25  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
158:25  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
165:9  Warning: Forbidden non-null assertion.  @typescript-eslint/no-non-null-assertion
166:25  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
167:25  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
178:9  Warning: Forbidden non-null assertion.  @typescript-eslint/no-non-null-assertion
180:25  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
181:25  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
188:9  Warning: Forbidden non-null assertion.  @typescript-eslint/no-non-null-assertion
189:25  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
190:25  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
201:9  Warning: Forbidden non-null assertion.  @typescript-eslint/no-non-null-assertion
203:25  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
204:25  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
211:9  Warning: Forbidden non-null assertion.  @typescript-eslint/no-non-null-assertion
212:25  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
213:25  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
233:9  Warning: Forbidden non-null assertion.  @typescript-eslint/no-non-null-assertion
235:25  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
236:25  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
248:9  Warning: Forbidden non-null assertion.  @typescript-eslint/no-non-null-assertion
249:25  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
250:25  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/hooks/useRealTimeChartHook.ts
28:57  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
28:62  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
54:5  Warning: React Hook useCallback has an unnecessary dependency: 'dbMeasureIdToAssetIdMap'. Either exclude it or remove the dependency array.  react-hooks/exhaustive-deps
135:19  Warning: 'retentionTime' is assigned a value but never used.  @typescript-eslint/no-unused-vars

./src/hooks/useTopPanelHelper.ts
19:57  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
19:62  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/layout/containers/chart/TableWidgetContainer.tsx
11:8  Warning: 'Error' is defined but never used.  @typescript-eslint/no-unused-vars
49:39  Warning: 'isError' is assigned a value but never used.  @typescript-eslint/no-unused-vars
94:39  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/layout/containers/EditAssetContainer.tsx
15:3  Warning: 'assetTypePathMapperFilterTemplates' is defined but never used.  @typescript-eslint/no-unused-vars
46:41  Warning: 'loading' is assigned a value but never used.  @typescript-eslint/no-unused-vars
93:6  Warning: React Hook useEffect has a missing dependency: 'dispatch'. Either include it or remove the dependency array.  react-hooks/exhaustive-deps

./src/layout/containers/EditMeasurementContainer.tsx
36:9  Warning: 'mainPanel' is assigned a value but never used.  @typescript-eslint/no-unused-vars
60:44  Warning: 'success' is assigned a value but never used.  @typescript-eslint/no-unused-vars
91:6  Warning: React Hook useEffect has a missing dependency: 'dispatch'. Either include it or remove the dependency array.  react-hooks/exhaustive-deps

./src/layout/containers/ImageWidgetContainer.tsx
47:45  Warning: Forbidden non-null assertion.  @typescript-eslint/no-non-null-assertion
89:6  Warning: React Hook useEffect has a missing dependency: 'settings'. Either include it or remove the dependency array.  react-hooks/exhaustive-deps

./src/layout/containers/MapWidgetContainer.tsx
50:6  Warning: React Hook useEffect has a missing dependency: 'mapData?.value'. Either include it or remove the dependency array.  react-hooks/exhaustive-deps
78:49  Warning: The ref value 'containerRef.current' will likely have changed by the time this effect cleanup function runs. If this ref points to a node rendered by React, copy 'containerRef.current' to a variable inside the effect, and use that variable in the cleanup function.  react-hooks/exhaustive-deps
82:6  Warning: React Hook useEffect has missing dependencies: 'dimensions.height' and 'dimensions.width'. Either include them or remove the dependency array.  react-hooks/exhaustive-deps

./src/layout/containers/NewAssetContainer.tsx
15:3  Warning: 'assetTypePathMapperFilterTemplates' is defined but never used.  @typescript-eslint/no-unused-vars

./src/layout/containers/NewMeasurementContainer.tsx
36:9  Warning: 'customerId' is assigned a value but never used.  @typescript-eslint/no-unused-vars
37:57  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
37:62  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
79:6  Warning: React Hook useEffect has a missing dependency: 'dispatch'. Either include it or remove the dependency array.  react-hooks/exhaustive-deps

./src/layout/containers/TreePanelContainer.tsx
65:3  Warning: 'onMeasurementEdit' is defined but never used.  @typescript-eslint/no-unused-vars
66:3  Warning: 'onMeasurementView' is defined but never used.  @typescript-eslint/no-unused-vars
73:3  Warning: 'onMetricAlertCreate' is defined but never used.  @typescript-eslint/no-unused-vars
74:3  Warning: 'onViewAlerts' is defined but never used.  @typescript-eslint/no-unused-vars
79:35  Warning: 'hasPermission' is assigned a value but never used.  @typescript-eslint/no-unused-vars

./src/layout/containers/WidgetContainer.tsx
73:11  Warning: 'mounted' is assigned a value but never used.  @typescript-eslint/no-unused-vars
140:30  Warning: 'e' is defined but never used.  @typescript-eslint/no-unused-vars

./src/measurements/domain/types.ts
497:38  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
595:38  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
1182:17  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
1411:18  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
1413:15  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
1424:15  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
1425:17  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
1461:21  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
1462:17  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
1463:21  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/pages/active-customer/index.tsx
11:17  Warning: 'loadingProps' is defined but never used.  @typescript-eslint/no-unused-vars

./src/pages/alert-analytics/index.tsx
9:15  Warning: 'loadingProps' is defined but never used.  @typescript-eslint/no-unused-vars

./src/pages/alerts/edit/[alertId].tsx
1:10  Warning: 'Box' is defined but never used.  @typescript-eslint/no-unused-vars
4:8  Warning: 'HomeButton' is defined but never used.  @typescript-eslint/no-unused-vars

./src/pages/alerts/[alertId].tsx
49:9  Warning: 'dispatch' is assigned a value but never used.  @typescript-eslint/no-unused-vars
49:57  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
49:62  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
50:25  Warning: 'showSuccessAlert' is assigned a value but never used.  @typescript-eslint/no-unused-vars
73:6  Warning: React Hook useEffect has a missing dependency: 'getCustomerUser'. Either include it or remove the dependency array.  react-hooks/exhaustive-deps
79:6  Warning: React Hook useEffect has a missing dependency: 'alertId'. Either include it or remove the dependency array.  react-hooks/exhaustive-deps
81:31  Warning: 'isSuccess' is assigned a value but never used.  @typescript-eslint/no-unused-vars
213:19  Warning: Forbidden non-null assertion.  @typescript-eslint/no-non-null-assertion
372:54  Warning: Forbidden non-null assertion.  @typescript-eslint/no-non-null-assertion
380:54  Warning: Forbidden non-null assertion.  @typescript-eslint/no-non-null-assertion

./src/pages/asset-templates/index.tsx
23:16  Warning: 'isAssetTypeLoading' is assigned a value but never used.  @typescript-eslint/no-unused-vars
38:6  Warning: React Hook useEffect has an unnecessary dependency: 'status'. Either exclude it or remove the dependency array. Outer scope values like 'status' aren't valid dependencies because mutating them doesn't re-render the component.  react-hooks/exhaustive-deps

./src/pages/create-element/index.tsx
8:15  Warning: 'loadingProps' is defined but never used.  @typescript-eslint/no-unused-vars

./src/pages/customer/index.tsx
48:6  Warning: React Hook useEffect has missing dependencies: 'router' and 'setActiveCustomer'. Either include them or remove the dependency array.  react-hooks/exhaustive-deps

./src/pages/customer/[customerId]/dashboard/[dashboardId].tsx
171:6  Warning: React Hook useEffect has a missing dependency: 'router'. Either include it or remove the dependency array.  react-hooks/exhaustive-deps
324:21  Warning: 'isStateFullScreen' is assigned a value but never used.  @typescript-eslint/no-unused-vars
325:9  Warning: 'kisok' is assigned a value but never used.  @typescript-eslint/no-unused-vars
400:6  Warning: React Hook useEffect has missing dependencies: 'activeCustomer?.id', 'dispatch', 'event_id', 'isMobile', 'router', and 'url_sample_period'. Either include them or remove the dependency array.  react-hooks/exhaustive-deps
426:6  Warning: React Hook useEffect has a missing dependency: 'dispatch'. Either include it or remove the dependency array.  react-hooks/exhaustive-deps

./src/pages/customer/[customerId]/index.tsx
47:6  Warning: React Hook useEffect has missing dependencies: 'router' and 'setActiveCustomer'. Either include them or remove the dependency array.  react-hooks/exhaustive-deps
63:6  Warning: React Hook useEffect has an unnecessary dependency: 'dashboardSlice.actions'. Either exclude it or remove the dependency array. Outer scope values like 'dashboardSlice.actions' aren't valid dependencies because mutating them doesn't re-render the component.  react-hooks/exhaustive-deps

./src/pages/customers/index.tsx
64:29  Warning: 'isError' is assigned a value but never used.  @typescript-eslint/no-unused-vars
64:44  Warning: 'deleteData' is assigned a value but never used.  @typescript-eslint/no-unused-vars
81:24  Warning: 'setAlertMessage' is assigned a value but never used.  @typescript-eslint/no-unused-vars
101:18  Warning: 'createLoading' is assigned a value but never used.  @typescript-eslint/no-unused-vars
125:6  Warning: React Hook useEffect has missing dependencies: 'showErrorAlert' and 'showSuccessAlert'. Either include them or remove the dependency array.  react-hooks/exhaustive-deps
159:6  Warning: React Hook useEffect has missing dependencies: 'refetch', 'showErrorAlert', and 'showSuccessAlert'. Either include them or remove the dependency array.  react-hooks/exhaustive-deps
186:6  Warning: React Hook useEffect has missing dependencies: 'createUserPreference', 'dispatch', and 'userPreferences'. Either include them or remove the dependency array.  react-hooks/exhaustive-deps
196:6  Warning: React Hook useEffect has missing dependencies: 'showErrorAlert' and 'showSuccessAlert'. Either include them or remove the dependency array.  react-hooks/exhaustive-deps

./src/pages/dashboard-template/import/index.tsx
12:3  Warning: 'Snackbar' is defined but never used.  @typescript-eslint/no-unused-vars
45:16  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
85:91  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
117:25  Warning: Forbidden non-null assertion.  @typescript-eslint/no-non-null-assertion
119:15  Warning: Forbidden non-null assertion.  @typescript-eslint/no-non-null-assertion

./src/pages/dashboard-template/list/index.tsx
70:27  Warning: 'loading' is assigned a value but never used.  @typescript-eslint/no-unused-vars
108:6  Warning: React Hook useEffect has missing dependencies: 'refetch', 'showErrorAlert', and 'showSuccessAlert'. Either include them or remove the dependency array.  react-hooks/exhaustive-deps

./src/pages/data-ingestion/index.tsx
9:15  Warning: 'loadingProps' is defined but never used.  @typescript-eslint/no-unused-vars

./src/pages/diagram/index.tsx
9:15  Warning: 'loadingProps' is defined but never used.  @typescript-eslint/no-unused-vars

./src/pages/measurement-browser/index.tsx
2:8  Warning: 'HomeButton' is defined but never used.  @typescript-eslint/no-unused-vars

./src/pages/notifications/index.tsx
2:21  Warning: 'Typography' is defined but never used.  @typescript-eslint/no-unused-vars

./src/pages/reports/index.tsx
2:21  Warning: 'Typography' is defined but never used.  @typescript-eslint/no-unused-vars

./src/pages/user-preferences/index.tsx
42:11  Warning: 'globalAdmin' is assigned a value but never used.  @typescript-eslint/no-unused-vars
42:24  Warning: 'admin' is assigned a value but never used.  @typescript-eslint/no-unused-vars
43:9  Warning: 'hasPowerUserAccess' is assigned a value but never used.  @typescript-eslint/no-unused-vars
48:9  Warning: 'router' is assigned a value but never used.  @typescript-eslint/no-unused-vars
63:6  Warning: React Hook useEffect has a missing dependency: 'currentUserPreferences.DEFAULT_CUSTOMER'. Either include it or remove the dependency array.  react-hooks/exhaustive-deps
74:6  Warning: React Hook useEffect has missing dependencies: 'currentUserPreferences.DATE_FORMAT' and 'data'. Either include them or remove the dependency array.  react-hooks/exhaustive-deps
77:59  Warning: 'child' is defined but never used.  @typescript-eslint/no-unused-vars
112:6  Warning: React Hook useEffect has missing dependencies: 'activeCustomer?.id', 'dispatch', 'selectedFormat', 'showErrorAlert', 'showSuccessAlert', and 'thousandSeparator'. Either include them or remove the dependency array.  react-hooks/exhaustive-deps
130:15  Warning: Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element  @next/next/no-img-element
131:38  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
154:46  Warning: 'i' is defined but never used.  @typescript-eslint/no-unused-vars

./src/pages/_app.tsx
233:13  Warning: Custom fonts not added in `pages/_document.js` will only load for a single page. This is discouraged. See: https://nextjs.org/docs/messages/no-page-custom-font  @next/next/no-page-custom-font

./src/redux/api/assetsApi.ts
75:37  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
75:42  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
226:45  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
238:50  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
273:9  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
284:9  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
307:9  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
323:9  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
345:25  Warning: 'parentId' is assigned a value but never used.  @typescript-eslint/no-unused-vars

./src/redux/api/customersApi.ts
124:19  Warning: 'email' is assigned a value but never used.  @typescript-eslint/no-unused-vars

./src/redux/api/dashboardApi.ts
94:27  Warning: 'result' is defined but never used.  @typescript-eslint/no-unused-vars
94:35  Warning: 'error' is defined but never used.  @typescript-eslint/no-unused-vars
94:44  Warning: 'customerId' is defined but never used.  @typescript-eslint/no-unused-vars
212:48  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
212:63  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/redux/api/dataIngestionApi.ts
45:45  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/redux/api/timeseriesApi.ts
18:68  Warning: 'timeRangeType' is defined but never used.  @typescript-eslint/no-unused-vars
48:70  Warning: 'timeRangeType' is defined but never used.  @typescript-eslint/no-unused-vars
106:68  Warning: 'timeRangeType' is defined but never used.  @typescript-eslint/no-unused-vars
180:68  Warning: 'timeRangeType' is defined but never used.  @typescript-eslint/no-unused-vars

./src/redux/api/usersApi.ts
60:47  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/redux/selectors/dashboardSelectors.ts
95:80  Warning: 'dashboard' is defined but never used.  @typescript-eslint/no-unused-vars
118:13  Warning: 'id' is assigned a value but never used.  @typescript-eslint/no-unused-vars
176:15  Warning: 'id' is assigned a value but never used.  @typescript-eslint/no-unused-vars

./src/redux/selectors/treeSelectors.ts
49:16  Warning: '_' is assigned a value but never used.  @typescript-eslint/no-unused-vars
77:16  Warning: '_' is assigned a value but never used.  @typescript-eslint/no-unused-vars

./src/redux/selectors/__tests__/widgetSelectors.test.ts
23:19  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
118:20  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
119:18  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
120:20  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
231:25  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
344:27  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
504:89  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
522:27  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
609:16  Warning: Forbidden non-null assertion.  @typescript-eslint/no-non-null-assertion
610:16  Warning: Forbidden non-null assertion.  @typescript-eslint/no-non-null-assertion
611:16  Warning: Forbidden non-null assertion.  @typescript-eslint/no-non-null-assertion
612:16  Warning: Forbidden non-null assertion.  @typescript-eslint/no-non-null-assertion
655:33  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/redux/slices/dashboardSlice.ts
1042:13  Warning: 'minutes' is assigned a value but never used.  @typescript-eslint/no-unused-vars
1268:20  Warning: 'assetId' is defined but never used.  @typescript-eslint/no-unused-vars
1285:20  Warning: 'assetId' is defined but never used.  @typescript-eslint/no-unused-vars

./src/security/components/AuthGuard.tsx
15:10  Warning: 'browserName' is assigned a value but never used.  @typescript-eslint/no-unused-vars
68:6  Warning: React Hook useEffect has missing dependencies: 'dispatch' and 'userPreferences.preferences'. Either include them or remove the dependency array.  react-hooks/exhaustive-deps

./src/security/components/UIWrapper.tsx
13:9  Warning: 'isLandscape' is assigned a value but never used.  @typescript-eslint/no-unused-vars

./src/security/containers/SignInContainer.tsx
23:57  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
89:18  Warning: '_' is assigned a value but never used.  @typescript-eslint/no-unused-vars
89:21  Warning: '__' is assigned a value but never used.  @typescript-eslint/no-unused-vars
89:37  Warning: '___' is assigned a value but never used.  @typescript-eslint/no-unused-vars
230:6  Warning: React Hook useEffect has missing dependencies: 'ActiveCustomer?.id', 'mobile', and 'setActiveCustomer'. Either include them or remove the dependency array.  react-hooks/exhaustive-deps

./src/types/calc_engine.ts
214:20  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/types/dashboardTemplate.ts
1:10  Warning: 'DashboardState' is defined but never used.  @typescript-eslint/no-unused-vars
38:16  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/types/diagram.ts
67:25  Warning: 'title' is defined but never used.  @typescript-eslint/no-unused-vars
69:21  Warning: 'title' is defined but never used.  @typescript-eslint/no-unused-vars
70:22  Warning: 'title' is defined but never used.  @typescript-eslint/no-unused-vars
243:15  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
259:15  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/types/measures.ts
3:3  Warning: 'EditAssetMeasurement' is defined but never used.  @typescript-eslint/no-unused-vars
111:16  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/utils/redux-toolkt-helpers.ts
18:22  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/utils/utils.ts
28:10  Warning: Forbidden non-null assertion.  @typescript-eslint/no-non-null-assertion
39:10  Warning: Forbidden non-null assertion.  @typescript-eslint/no-non-null-assertion
540:10  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
583:29  Warning: 'trace' is defined but never used.  @typescript-eslint/no-unused-vars

info  - Need to disable some ESLint rules? Learn more here: https://nextjs.org/docs/basic-features/eslint#disabling-rules
