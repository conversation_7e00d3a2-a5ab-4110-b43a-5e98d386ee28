import { Autocomplete, Box, CircularProgress, FormControl, TextField } from '@mui/material';
import React, { useEffect, useMemo, useState } from 'react';
import { useSelector } from 'react-redux';
import { useGetAllAssetQuery } from '~/redux/api/assetsApi';
import { useGetAllMeasurementsQuery } from '~/redux/api/measuresApi'; // Ensure this exists
import { getCustomerId } from '~/redux/selectors/customerSelectors';
import { SingleMeasureWidgets } from '~/types/widgets';
import { assetsPathMapper, formatMetricLabel } from '~/utils/utils';

type AssetOption = {
  label: string;
  id: number;
};

type MeasureOption = {
  label: string;
  id: string; // Changed to string
};

// Define props for the component
type SingleAssetMeasureProps = {
  id: string;
  settings: SingleMeasureWidgets;
  setSettings: (
    value: ((prevState: SingleMeasureWidgets) => SingleMeasureWidgets) | SingleMeasureWidgets,
  ) => void;
};

const SingleAssetMeasure = ({ id, settings, setSettings }: SingleAssetMeasureProps) => {
  const customerId = useSelector(getCustomerId);
  const [selectedAsset, setSelectedAsset] = useState<AssetOption | null>(null);
  const [selectedMeasure, setSelectedMeasure] = useState<MeasureOption | null>(null);

  const {
    data: assetData,
    isLoading: isAssetLoading,
    isFetching: isAssetReloading,
    error: assetError,
  } = useGetAllAssetQuery(
    { customerId, parentIds: [] },
    {
      skip: !customerId,
      refetchOnMountOrArgChange: true,
    },
  );

  const assetTypesWithPath = useMemo(() => assetsPathMapper(assetData ?? []), [assetData]);

  useEffect(() => {
    if (settings.assetMeasure.assetId !== '' && assetTypesWithPath.length > 0) {
      const currentAssetId = settings.assetMeasure.assetId;
      const asset = assetTypesWithPath.find((a) => String(a.id) === currentAssetId);
      if (asset) {
        setSelectedAsset({ label: asset.label, id: asset.id });
      } else {
        setSelectedAsset(null);
      }
    } else {
      setSelectedAsset(null);
    }
  }, [settings.assetMeasure.assetId, assetTypesWithPath]);

  const {
    data: measurementData,
    isLoading: isMeasurementLoading,
    isFetching: isMeasurementFetching,
    error: measurementError,
  } = useGetAllMeasurementsQuery(
    { customerId, assetId: selectedAsset ? selectedAsset.id : 0 },
    {
      skip: !selectedAsset || selectedAsset.id === 0,
      refetchOnMountOrArgChange: true,
    },
  );

  const measurementOptions: MeasureOption[] = useMemo(() => {
    if (!measurementData) return [];
    return measurementData.map((measure) => ({
      label: formatMetricLabel(measure.tag), // Adjust based on your actual measure data structure
      id: String(measure.id), // Convert number to string
    }));
  }, [measurementData]);

  useEffect(() => {
    if (settings.assetMeasure.measureId.length > 0 && measurementOptions.length > 0) {
      const currentMeasureId = settings.assetMeasure.measureId[0]; // First index
      const measure = measurementOptions.find((m) => m.id === currentMeasureId);
      if (measure) {
        setSelectedMeasure(measure);
      } else {
        setSelectedMeasure(null);
      }
    } else {
      setSelectedMeasure(null);
    }
  }, [settings.assetMeasure.measureId, measurementOptions]);

  const handleAssetChange = (event: any, newValue: AssetOption | null) => {
    setSelectedAsset(newValue);
    setSelectedMeasure(null);
    setSettings((prevSettings) => ({
      ...prevSettings,
      assetMeasure: {
        assetId: newValue ? String(newValue.id) : '',
        measureId: [],
      },
      dbMeasureIdToName: {},
    }));
  };

  const handleMeasurementChange = (event: any, newValue: MeasureOption | null) => {
    setSelectedMeasure(newValue);
    setSettings((prevSettings) => ({
      ...prevSettings,
      dbMeasureIdToName: {
        ...prevSettings.dbMeasureIdToName,
        [String(newValue?.id)]: newValue?.label ?? '',
      },
      title: {
        ...prevSettings.title,
        value: prevSettings.title.isVisible ? prevSettings.title.value : newValue?.label ?? '',
      },
      assetMeasure: {
        ...prevSettings.assetMeasure,
        measureId: newValue ? [newValue.id] : [],
      },
    }));
  };

  return (
    <Box sx={{ display: 'flex', gap: 2 }}>
      <FormControl fullWidth>
        <Autocomplete
          fullWidth
          id={`asset-autocomplete-${id}`}
          loading={isAssetReloading}
          options={
            !isAssetReloading
              ? assetTypesWithPath.map((asset) => ({
                  label: asset.label,
                  id: asset.id,
                }))
              : []
          }
          getOptionLabel={(option) => option.label}
          onChange={handleAssetChange}
          value={selectedAsset}
          renderInput={(params) => (
            <TextField
              {...params}
              label="Asset"
              variant="outlined"
              InputProps={{
                ...params.InputProps,
                endAdornment: (
                  <>
                    {isAssetLoading ? <CircularProgress color="inherit" size={20} /> : null}
                    {params.InputProps.endAdornment}
                  </>
                ),
              }}
            />
          )}
        />
      </FormControl>

      {/* Measurement Selection */}
      {selectedAsset && (
        <FormControl fullWidth>
          <Autocomplete
            fullWidth
            id={`measurement-autocomplete-${id}`}
            loading={isMeasurementLoading}
            options={measurementOptions}
            getOptionLabel={(option) => option.label}
            onChange={handleMeasurementChange}
            value={selectedMeasure}
            renderInput={(params) => (
              <TextField
                {...params}
                label="Measurement"
                variant="outlined"
                InputProps={{
                  ...params.InputProps,
                  endAdornment: (
                    <>
                      {isMeasurementLoading ? <CircularProgress color="inherit" size={20} /> : null}
                      {params.InputProps.endAdornment}
                    </>
                  ),
                }}
              />
            )}
          />
        </FormControl>
      )}

      {/* Optional: Display Error Messages */}
      {assetError && <div>Error loading assets.</div>}
      {measurementError && <div>Error loading measurements.</div>}
    </Box>
  );
};

export default SingleAssetMeasure;
