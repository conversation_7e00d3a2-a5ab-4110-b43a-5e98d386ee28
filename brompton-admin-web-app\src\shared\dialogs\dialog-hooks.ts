import { useReducer } from 'react';

type DialogState = {
  title: string;
  description: string;
  open: boolean;
  onDelete: () => unknown;
  onCancel: () => unknown;
};

export const useDialog = (): [
  DialogState,
  (title: string, description: string, onDelete: () => unknown) => void,
  () => void,
] => {
  const [dialogState, dialogDispatch] = useReducer(
    (
      state: DialogState,
      action:
        | { type: 'open'; title: string; description: string; onDelete: () => unknown }
        | { type: 'close' },
    ) =>
      action.type === 'open'
        ? {
            ...state,
            title: action.title,
            description: action.description,
            onDelete: action.onDelete,
            open: true,
          }
        : { ...state, open: false },
    {
      title: '',
      description: '',
      open: false,
      onDelete: () => {
        // do nothing
      },
      onCancel: () => {
        dialogDispatch({ type: 'close' });
      },
    },
  );
  return [
    dialogState,
    (title: string, description: string, onDelete: () => unknown) =>
      dialogDispatch({ type: 'open', title, description, onDelete }),
    () => dialogDispatch({ type: 'close' }),
  ];
};
