import { ThunkDispatch } from '@reduxjs/toolkit';
import { useDispatch, useSelector } from 'react-redux';
import { getCustomerId } from '~/redux/selectors/customerSelectors';
import { RootState } from '~/redux/store';
import { RealtimeSettings } from '~/types/widgets';
import { useCustomTimeInterval } from './useCustomTimeInterval';
import { dashboardSlice } from '~/redux/slices/dashboardSlice';
import { getMetricsIdToName } from '~/redux/selectors/dashboardSelectors';
import { useCallback, useEffect, useState } from 'react';
import { SingleScatterTimeSeriesData } from '~/types/timeseries';
import { AssetMeasurementDetails } from '~/types/measures';
import { UnitOfMeasure } from '~/measurements/domain/types';
import { measuresApi } from '~/redux/api/measuresApi';
import { getDbMeasureIdToAssetIdMap } from '~/redux/selectors/treeSelectors';
import { timeseriesApi } from '~/redux/api/timeseriesApi';

type RealtTimeTsDataProps = {
  selectedTitles: string[];
  dataFetchSettings: RealtimeSettings & {
    widgetId: string;
  };
};
type MeasuresData = {
  isLoading: boolean;
  isError: boolean;
  error: string;
  tsData: SingleScatterTimeSeriesData;
  measureData: AssetMeasurementDetails;
  unitOfMeasures: UnitOfMeasure[];
};
export const useFetchRealTimeTsData = ({
  selectedTitles,
  dataFetchSettings,
}: RealtTimeTsDataProps) => {
  const dispatch = useDispatch<ThunkDispatch<RootState, any, any>>();
  const customerId = useSelector(getCustomerId);
  const metricToName = useSelector(getMetricsIdToName);
  const dbMeasureIdToAssetIdMap = useSelector(getDbMeasureIdToAssetIdMap);
  const [state, setState] = useState<{
    data: undefined | MeasuresData[];
    isLoading: boolean;
    isError: boolean;
    forcastedData: undefined | MeasuresData[];
  }>({
    data: undefined,
    isLoading: true,
    isError: false,
    forcastedData: undefined,
  });
  const { isRealTime, refreshInterval, retainPeriod, widgetId } = dataFetchSettings;
  //   useCustomTimeInterval(
  //     () => {
  //       dispatch(dashboardSlice.actions.seWidgetSpecificRefreshInterval({ widgetId }));
  //     },
  //     isRealTime ? refreshInterval * 1000 : -1,
  //   );

  const fetchMeasureData = useCallback(
    async (measureId: string) => {
      if (!dbMeasureIdToAssetIdMap[measureId]) {
        throw new Error(`No assetId found for ${measureId}`);
      }

      if (!measureId) {
        throw new Error(`Error invalid measureId: ${measureId}`);
      }

      const { data: measureData, isSuccess: isMeasureDataSuccess } = await dispatch(
        measuresApi.endpoints?.getMeasurementById.initiate({
          customerId,
          assetId: dbMeasureIdToAssetIdMap[measureId],
          measId: measureId,
        }),
      );

      if (!isMeasureDataSuccess || !measureData) {
        throw new Error(`Error fetching measure data for measureId: ${measureId}`);
      }

      return measureData;
    },
    [customerId, dbMeasureIdToAssetIdMap, dispatch],
  );

  const fetchTimeseriesData = useCallback(
    async (
      tsDbMeasureIds: number[],
    ): Promise<{ error: boolean; tsData: Record<number, SingleScatterTimeSeriesData> }> => {
      if (tsDbMeasureIds.length === 0) {
        return { error: true, tsData: {} };
      }
      const endDate = new Date();
      const startDate = new Date(endDate.getTime() - retainPeriod * 60 * 1000).getTime();
      const { data: tsData, isSuccess: isTsSuccess } = await dispatch(
        timeseriesApi.endpoints.getMultipleHistoryMeasurementSeries.initiate({
          customerId,
          measId: tsDbMeasureIds.filter(Boolean).sort().join(','),
          start: startDate,
          end: endDate.getTime(),
          assetTz: false,
        }),
      );

      if (!isTsSuccess || !tsData) {
        return {
          error: true,
          tsData: {},
        };
      }

      return { error: false, tsData };
    },
    [dispatch, customerId, retainPeriod],
  );

  const fetchUnitOfMeasure = useCallback(
    async (assetMeasurementTypeId: number) => {
      const { data: unitOfMeasures, isSuccess: isUnitOfMeasureSuccess } = await dispatch(
        measuresApi.endpoints?.getUnitsOfMeasure.initiate({
          measurementTypeId: assetMeasurementTypeId,
        }),
      );

      if (!isUnitOfMeasureSuccess || !unitOfMeasures) {
        throw new Error('Error fetching unit of measure data');
      }

      return unitOfMeasures;
    },
    [dispatch],
  ); // include assetMeasurementTypeId in the dependency array
  useEffect(() => {
    if (!isRealTime) {
      return;
    }
    const intervalId = setInterval(() => {
      const fetchMeasuresData = async () => {
        setState({ data: undefined, isLoading: true, isError: false, forcastedData: undefined });

        const apiResults = selectedTitles
          .filter((measureId) => measureId && measureId !== '')
          .map(async (measureId) => {
            try {
              const measureData = await fetchMeasureData(measureId);
              const unitOfMeasures = await fetchUnitOfMeasure(measureData.typeId);
              return {
                isLoading: false,
                isError: false,
                error: '',
                measureData,
                unitOfMeasures,
              } as MeasuresData;
            } catch (error) {
              return {
                isLoading: false,
                isError: true,
                error: error,
              } as MeasuresData;
            }
          });
        const results = await Promise.all(apiResults);
        const tsMeasureIds = results.map((result) => result.measureData?.measurementId);
        const { error, tsData } = await fetchTimeseriesData(tsMeasureIds.filter(Boolean));
        const forcastRes: MeasuresData[] | undefined = undefined;
        if (error) {
          setState({ data: undefined, isLoading: false, isError: true, forcastedData: undefined });
          return;
        }
        results.forEach((result) => {
          const seriesData = tsData[result.measureData?.measurementId];
          if (seriesData?.error) {
            result.isLoading = false;
            result.isError = true;
            result.error = seriesData.error;
          } else {
            result.isLoading = false;
            result.tsData = seriesData;
          }
        });
        setState({
          ...state,
          data: results,
          isLoading: false,
          isError: false,
          forcastedData: forcastRes,
        });
      };
      if (selectedTitles && selectedTitles.length > 0) fetchMeasuresData();
    }, refreshInterval * 1000);

    return () => clearInterval(intervalId);
  }, [dispatch, isRealTime, refreshInterval, customerId, selectedTitles]);
  return state;
};
