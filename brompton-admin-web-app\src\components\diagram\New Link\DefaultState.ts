import { MouseEvent, TouchEvent } from 'react';
import {
  SelectingState,
  State,
  Action,
  InputType,
  ActionEvent,
  DragCanvasState,
} from '@projectstorm/react-canvas-core';
import { DiagramEngine, DragDiagramItemsState } from '@projectstorm/react-diagrams-core';
import { CreateLinkState } from './CreateLinkState';
import { AdvancedPortModel } from '../Animation';
import { CustomPortModel } from '../model/CustomPortModel';

export class DefaultState extends State<DiagramEngine> {
  dragCanvas: DragCanvasState;
  createLink: CreateLinkState;
  dragItems: DragDiagramItemsState;

  constructor() {
    super({ name: 'starting-state' });
    this.childStates = [new SelectingState()];
    this.dragCanvas = new DragCanvasState();
    this.createLink = new CreateLinkState();
    this.dragItems = new DragDiagramItemsState();

    // determine what was clicked on
    this.registerAction(
      new Action({
        type: InputType.MOUSE_DOWN,
        fire: (event: ActionEvent<MouseEvent> | any) => {
          const element = this.engine.getActionEventBus().getModelForEvent(event);

          // the canvas was clicked on, transition to the dragging canvas state
          if (!element) {
            this.transitionWithEvent(this.dragCanvas, event);
          }
          // initiate dragging a new link
          else if (element instanceof AdvancedPortModel || element instanceof CustomPortModel) {
            return;
          }
          // move the items (and potentially link points)
          else {
            this.transitionWithEvent(this.dragItems, event);
          }
        },
      }),
    );

    // touch drags the canvas
    this.registerAction(
      new Action({
        type: InputType.TOUCH_START,
        fire: (event: ActionEvent<TouchEvent> | any) => {
          this.transitionWithEvent(new DragCanvasState(), event);
        },
      }),
    );

    this.registerAction(
      new Action({
        type: InputType.MOUSE_UP,
        fire: (event: ActionEvent<MouseEvent> | any) => {
          const element = this.engine.getActionEventBus().getModelForEvent(event);

          if (element instanceof AdvancedPortModel || element instanceof CustomPortModel)
            this.transitionWithEvent(this.createLink, event);
        },
      }),
    );
  }
}
