import { AssetMeasurementDetails } from './measures';

export type ForecastSeriesDataParams = {
  customerId: number;
  measId: string;
  forecast: string;
  assetTz: boolean;
  agg: string;
};
export type ScatterSeriesDataParams = {
  customerId: number;
  start: number;
  end?: number;
  measId: string;
  agg_period: string;
  agg: string;
  timeRangeType: number;
  assetTz: boolean;
  error?: boolean;
  message?: string;
};

export type BarSeriesDataParams = {
  customerId: number;
  start: number;
  end?: number;
  measId: string;
  agg_period: string;
  agg: string;
};

export type HeatMapSeriesParams = {
  customerId: number;
  start: number;
  end?: number;
  measId: string;
  agg_period: string;
  agg: string;
  groupX: string;
  groupY: string;
  timeRangeType: number;
  assetTz: boolean;
};

export type ImageStatsSeriesParams = {
  customerId: number;
  start: number;
  end: number;
  measId: string;
  agg_period: string;
  agg: string;
  assetTz: boolean;
};
export type SingleImageStatsTimeSeriesData = {
  realtime: number;
  daily: {
    timeseries: [];
    status: string;
  };
  weekly: {
    timeseries: [];
    status: string;
  };
};
export type data = {
  tag: number;
  ts: number;
  val: number;
};
export type RealtimeResponse = {
  meas_id: string;
  realTimeValue: number;
  data: data[];
};
export type AggTsResponse = {
  tag: number;
  agg: string;
  period: string;
  ['ts,val']: [number, number][];
};
export type AggResponse = {
  meas_id: number;
  status: string;
  timeSeries: [number, number][];
  data: AggTsResponse[];
};
export type KPICurrentWidgetResponse = {
  agg: AggResponse[];
  realTime: RealtimeResponse[];
};
export type RealTimeRequest = { meas_id: number; use_asset_tz?: boolean; browser_tz?: string };
export type AggRequest = {
  meas_id: number;
  start: number;
  end: number;
  agg: string;
  agg_period: string;
  use_asset_tz?: boolean;
  browser_tz?: string;
};
export type KPICurrentWidgetRequest = {
  customerId: number;
  realTime: RealTimeRequest[];
  agg: AggRequest[];
};
export type SingleScatterTimeSeriesData = {
  tag: number;
  tag_meta: {
    uom: string;
  };
  error?: string;
  period: string;
  'ts,val': [number, number][];
};

export type SingleHeatMapTimeSeriesData = {
  tag: number;
  error?: string;
  'gb0,gb1,val': [string, string, number][];
};

export type HeatMapTimeSeriesData = SingleHeatMapTimeSeriesData[];

export type TimeValueDataPoint = [number, number];

export type TimeSeriesAggData = {
  tag: number;
  agg: string;
  period: string;
  tsVal: TimeValueDataPoint[];
};

export type AssetMeasurementDetailsWithLastFetchTime = AssetMeasurementDetails & {
  lastFetchTime: number;
  partialFailed: boolean;
};

export type AssetMeasurementDetailsWithLastFetchAndSucess = AssetMeasurementDetails & {
  lastFetchTime?: number;
  isSuccess: boolean;
};
