{"name": "react-date-range", "version": "1.0.0-alpha3", "description": "A React component for choosing dates and date ranges.", "main": "dist/index.js", "scripts": {"dev": "webpack-dev-server --hot --inline", "clear": "rm -rf dist/* & rm -rf demo/dist/*", "build": "NODE_ENV=production & yarn build-library & yarn build-demo", "build:production": "NODE_ENV=production & yarn build-library & yarn build-demo", "build:test": "NODE_ENV=test & yarn build-library & yarn build-demo", "build:dev": "NODE_ENV=dev & yarn build-library & yarn build-demo", "build-demo": "webpack -p", "build-library": "babel ./src --out-dir ./dist --ignore test.js & postcss 'src/styles.scss' -d dist --ext css & postcss 'src/theme/*.scss' -d 'dist/theme' --ext css", "lint": "eslint 'src/**/*.js'", "test": "jest", "preversion": "yarn clear & yarn build"}, "keywords": ["react", "date", "range", "datepicker", "rangepicker"], "contributors": ["Burak Can <<EMAIL>> (https://github.com/burakcan)", "<PERSON><PERSON><PERSON> <<EMAIL>> (https://github.com/mkg0)"], "license": "MIT", "repository": {"type": "git", "url": "http://github.com/Adphorus/react-date-range"}, "bugs": {"url": "http://github.com/Adphorus/react-date-range/issues"}, "dependencies": {"classnames": "^2.2.1", "date-fns": "2.0.0-alpha.7", "prop-types": "^15.5.10", "react-list": "^0.8.8"}, "peerDependencies": {"react": "^0.14 || ^15.0.0-rc || >=15.0"}, "devDependencies": {"autoprefixer": "^7.2.4", "babel-cli": "^6.26.0", "babel-eslint": "^8.1.2", "babel-jest": "^22.1.0", "babel-loader": "^7.1.2", "babel-plugin-date-fns": "^0.1.0", "babel-plugin-transform-export-extensions": "^6.22.0", "babel-plugin-transform-object-rest-spread": "^6.26.0", "babel-preset-env": "^1.6.1", "babel-preset-react": "^6.24.1", "css-loader": "^0.28.8", "enzyme": "^3.3.0", "enzyme-adapter-react-16": "^1.1.1", "eslint": "^4.15.0", "eslint-config-prettier": "^2.9.0", "eslint-plugin-import": "^2.8.0", "eslint-plugin-jsx-a11y": "^6.0.3", "eslint-plugin-prettier": "^2.4.0", "eslint-plugin-react": "^7.5.1", "html-webpack-plugin": "^2.30.1", "jest": "^22.1.4", "normalize.css": "^7.0.0", "postcss-import": "^11.0.0", "postcss-loader": "^2.0.10", "precss": "^2.0.0", "prettier": "^1.9.2", "react": "^16.2.0", "react-dom": "^16.2.0", "react-hot-loader": "^3.1.3", "style-loader": "^0.19.1", "webpack": "^3.10.0", "webpack-dev-server": "^2.9.7", "webpack-hot-middleware": "^2.21.0"}}