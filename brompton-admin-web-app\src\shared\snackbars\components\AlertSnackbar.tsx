import { Alert, AlertColor, Snackbar } from '@mui/material';

type AlertSnackbarProps = {
  open: boolean;
  severity: AlertColor;
  message: string;
  onClose: () => unknown;
};

export const AlertSnackbar = ({ open, severity, message, onClose }: AlertSnackbarProps) => (
  <Snackbar
    open={open}
    onClose={onClose}
    autoHideDuration={3000}
    anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
  >
    <Alert severity={severity}>{message}</Alert>
  </Snackbar>
);
