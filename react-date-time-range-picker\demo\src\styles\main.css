.Main {
  width: 800px;
  margin: 40px auto;
}

.Title {
  display: block;
  font-weight: 200;
  padding: 0;
  margin: 0 0 24px;
}

.Section {
  padding: 20px 0;
  border-top: 1px dashed rgba(0, 0, 0, 0.1);
  clear: both;
}

.Section ~ .Section {
  margin-top: 60px;
}

.Demo-description {
}

.label{
  padding-right: 30px;
  font-weight: bold;
  font-size: 0.8em;
}

.Demo-inputs > div{
  display: flex;
  justify-content: center;
  align-items: center;
  flex-wrap: wrap;
}

.Demo-inputs > div > *{
  margin: 0 10px 5px;
}

.newLine{
  flex: 0 1 100%;
}

.Demo-description input, .Demo-description select {
  padding: 10px;
  outline: 0;
  border: 1px solid #dadada;
  border-radius: 2px;
  min-width: 100px;
  height: 35px;
}

.Demo-description h2 {
  font-weight: 400;
  font-size: 1.4rem;
  text-align: center;
}

.Demo{
  text-align: center;
  padding-top: 30px;
}

.Mobile-Container {
  height: 500px;
  overflow-y: scroll;
  width: 296px;
  margin: 0 auto;
}

.PreviewArea{
  box-shadow: 1px 1px 81px rgba(41, 60, 74, 0.18);
}

.centered{
  margin: 0 auto;
}
