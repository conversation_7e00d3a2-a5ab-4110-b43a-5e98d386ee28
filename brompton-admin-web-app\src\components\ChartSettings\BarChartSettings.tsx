import { Box, Checkbox, FormControlLabel, Stack } from '@mui/material';
import React, { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';
import { MeasureSettingSelector } from '~/components/common/MeasureSettingSelector';
import { getMetricsIdToName, selectDashboardState } from '~/redux/selectors/dashboardSelectors';
import {
  BarChartWidget,
  ChartMeasureSetting,
  setForecastWidgetSettings,
  setMultiMeasureChartSettings,
  setMultiMeasureWidgetSettings,
  setSumDeltaWidgetSettings,
} from '~/types/widgets';
import ForecastSettings from '../ForecastSettings/ForecastSettings';
import SumDeltaSettings from '../SumDelta/SumDeltaSettings';
import ChartLegendsSettings from '../common/ChartLegendsSettings';
import ChartThreshHold from '../common/ChartThreshHold';
import DataWidgetSettingsContainer from '../common/DataWidgetSettingsContainer/DataWidgetSettingsContainer';
import MeasureColorSelector from '../common/MeasueColorSelector';
import MultiMeasureSelection from '../common/MultiMeasureSelection';
import MultiMeasureSelectionMenu from '../common/MultiMeasureSelectionMenu';
import ShowMinMax from '../common/ShowMinMax';

type BarChartSettingsProps = {
  barChartSettings: BarChartWidget;
  selectedDbMeasureIdToName: { [key: string]: string };
  handleBarChartSettingsUpdate: (
    value: ((prevState: BarChartWidget) => BarChartWidget) | BarChartWidget,
  ) => void;
};

export function BarChartSettings({
  barChartSettings,
  selectedDbMeasureIdToName,
  handleBarChartSettingsUpdate,
}: BarChartSettingsProps) {
  const { dbMeasureIdToSetting } = barChartSettings;
  const metricsIdToName = useSelector(getMetricsIdToName);
  const dashboardState = useSelector(selectDashboardState);
  const [selectedMeasures, setSelectedMeasures] = useState<string[]>([]);
  const handleDbMeasureIdToSettingUpdate = (
    updatedDbMeasureIdToSetting: Record<string, ChartMeasureSetting>,
  ) => {
    handleBarChartSettingsUpdate({
      ...barChartSettings,
      dbMeasureIdToSetting: updatedDbMeasureIdToSetting,
    });
  };
  const handleDbMeasureIdAnnotationToSettingUpdate = (
    updatedDbMeasureIdAnnotationToYAxisSide: Record<string, boolean>,
  ) => {
    handleBarChartSettingsUpdate({
      ...barChartSettings,
      dbMeasureIdToAnnotation: updatedDbMeasureIdAnnotationToYAxisSide,
    });
  };
  useEffect(() => {
    const hasValidAssetMeasure = barChartSettings.assetMeasure?.some(
      (assetMeas) =>
        assetMeas.assetId.trim() !== '' &&
        assetMeas.measureId.some((measure) => measure.trim() !== ''),
    );
    if (hasValidAssetMeasure) {
      const titles = barChartSettings.assetMeasure
        .flatMap((assetMeas) => assetMeas.measureId)
        .filter((measure) => measure.trim() !== '');
      setSelectedMeasures(titles);
    } else {
      setSelectedMeasures([]);
    }
  }, [barChartSettings.assetMeasure]);
  useEffect(() => {
    if (barChartSettings.mode === 'template') {
      const titles: Record<string, string> = {};
      barChartSettings.selectedTitles.forEach((title) => {
        titles[title] = metricsIdToName[title];
      });
      handleBarChartSettingsUpdate((prevState) => ({
        ...prevState,
        dbMeasureIdToName: titles,
        title: {
          ...prevState.title,
          value: prevState.title.isVisible
            ? prevState.title.value
            : Object.values(titles).join(' Vs.'),
        },
      }));
    }
  }, [
    barChartSettings.mode,
    selectedDbMeasureIdToName,
    barChartSettings.selectedTitles,
    metricsIdToName,
  ]);
  const handleChangeMeasure = (updatedTitles: string[]) => {
    handleBarChartSettingsUpdate((prevState) => {
      const { barColors = [], ...rest } = prevState;
      const updatedColors = barColors.filter((color) => updatedTitles.includes(color.measureId));
      const newColors = updatedTitles
        .filter((title) => !barColors.map((color) => color.measureId).includes(title))
        .map((title) => ({
          measureId: title,
          color: '#0baf28',
        }));
      const updatedDbMeasureIdToName: Record<string, string> = updatedTitles.reduce(
        (acc, title) => {
          acc[title] = metricsIdToName[title];
          return acc;
        },
        {} as Record<string, string>,
      );
      return {
        ...rest,
        barColors: [...updatedColors, ...newColors],
        selectedTitles: updatedTitles,
        dbMeasureIdToName: {
          ...prevState.dbMeasureIdToName,
          ...updatedDbMeasureIdToName,
        },
      };
    });
  };
  const handleBarColorChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    handleBarChartSettingsUpdate(({ barColors, ...rest }) => {
      const { name, value } = e.target;
      const updatedColors = barColors
        ? barColors.filter((color) => {
            return color.measureId !== name;
          })
        : [];
      updatedColors.push({ measureId: name, color: value });
      return {
        ...rest,
        barColors: updatedColors,
      };
    });
  };
  const setOverRideSettings = (event: React.ChangeEvent<HTMLInputElement>, checked: boolean) => {
    const { name } = event.target;
    let sameplePeriod = barChartSettings.samplePeriod;
    if (name === 'globalSamplePeriod') {
      sameplePeriod = checked
        ? barChartSettings.samplePeriod
        : dashboardState.topPanel.samplePeriod;
    }
    let timeRange = barChartSettings.timeRange;
    if (name === 'overrideGlobalSettings') {
      timeRange = checked ? barChartSettings.timeRange : dashboardState.topPanel.timeRangeType;
    }
    handleBarChartSettingsUpdate({
      ...barChartSettings,
      samplePeriod: sameplePeriod,
      timeRange: timeRange,
      [event.target.name]: checked,
    });
  };

  const handleLegendYChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const legendY = Number(e.target.value);
    handleBarChartSettingsUpdate({
      ...barChartSettings,
      legendY,
    });
  };
  const showStacked = (event: React.ChangeEvent<HTMLInputElement>) => {
    handleBarChartSettingsUpdate({
      ...barChartSettings,
      showStacked: { show: event.target.checked },
    });
  };
  return (
    <Stack gap={0}>
      <DataWidgetSettingsContainer
        settings={barChartSettings}
        setSettings={handleBarChartSettingsUpdate}
        dataTabChildren={
          <Box>
            {barChartSettings.mode === 'dashboard' ? (
              <MultiMeasureSelectionMenu
                mode={barChartSettings.mode}
                settings={barChartSettings}
                setSettings={handleBarChartSettingsUpdate as setMultiMeasureWidgetSettings}
              />
            ) : (
              <MultiMeasureSelection
                mode={barChartSettings.mode}
                handleChangeMeasure={handleChangeMeasure}
                selectedMeasureNames={barChartSettings.selectedTitles}
              />
            )}
          </Box>
        }
        feelTabChidren={
          <>
            <Box>
              <MeasureSettingSelector
                selectedDbMeasureIdToName={barChartSettings.dbMeasureIdToName}
                dbMeasureIdToSetting={dbMeasureIdToSetting}
                handleDbMeasureIdToSettingUpdate={handleDbMeasureIdToSettingUpdate}
                selectedMeasureNames={
                  barChartSettings.mode === 'template'
                    ? barChartSettings.selectedTitles
                    : selectedMeasures
                }
                dbMeasureIdToAnnotation={barChartSettings.dbMeasureIdToAnnotation}
                handleDbMeasureIdToAnnotationUpdate={handleDbMeasureIdAnnotationToSettingUpdate}
              />
            </Box>
            <Box>
              <ChartLegendsSettings
                ledendY={barChartSettings.legendY}
                handleLegendYChange={handleLegendYChange}
              />
            </Box>
            <Box>
              <FormControlLabel
                control={
                  <Checkbox
                    checked={barChartSettings.showRangeSlider}
                    onChange={() => {
                      handleBarChartSettingsUpdate({
                        ...barChartSettings,
                        showRangeSlider: !barChartSettings.showRangeSlider,
                      });
                    }}
                    name="showRangeSlider"
                  />
                }
                label="Show Range Slider"
              />
            </Box>
            {selectedMeasures.length > 1 && (
              <Box>
                <FormControlLabel
                  control={
                    <Checkbox
                      checked={barChartSettings.showStacked.show}
                      onChange={showStacked}
                      name="showStacked.show"
                    />
                  }
                  label="Show Stacked Bars"
                />
              </Box>
            )}
            {barChartSettings.mode === 'dashboard' && selectedMeasures.length === 1 && (
              <Box>
                <ForecastSettings
                  settings={barChartSettings}
                  setSettings={handleBarChartSettingsUpdate as setForecastWidgetSettings}
                />
              </Box>
            )}
            {barChartSettings.mode === 'template' &&
              barChartSettings.selectedTitles.length === 1 && (
                <Box>
                  <ForecastSettings
                    settings={barChartSettings}
                    setSettings={handleBarChartSettingsUpdate as setForecastWidgetSettings}
                  />
                </Box>
              )}
            <Box>
              <SumDeltaSettings
                selectedTitles={
                  barChartSettings.mode === 'dashboard'
                    ? selectedMeasures
                    : barChartSettings.selectedTitles
                }
                settings={barChartSettings}
                setSettings={handleBarChartSettingsUpdate as setSumDeltaWidgetSettings}
              />
            </Box>
            <Box>
              <ChartThreshHold
                settings={barChartSettings}
                setSettings={handleBarChartSettingsUpdate as setMultiMeasureChartSettings}
              />
            </Box>
            <Box>
              <ShowMinMax
                selectedTitles={selectedMeasures}
                settings={barChartSettings}
                setSettings={handleBarChartSettingsUpdate as setMultiMeasureChartSettings}
              />
            </Box>
            <Box>
              {barChartSettings.mode === 'dashboard' ? (
                <MeasureColorSelector
                  selectedDbMeasureIdToName={barChartSettings.dbMeasureIdToName}
                  selectedMeasureNames={selectedMeasures}
                  settings={barChartSettings}
                  handleBarColorChange={handleBarColorChange}
                  overrideGlobalBarColor={barChartSettings.overrideGlobalBarColor}
                  setOverRideSettings={setOverRideSettings}
                />
              ) : (
                <MeasureColorSelector
                  selectedDbMeasureIdToName={barChartSettings.dbMeasureIdToName}
                  selectedMeasureNames={barChartSettings.selectedTitles}
                  settings={barChartSettings}
                  handleBarColorChange={handleBarColorChange}
                  overrideGlobalBarColor={barChartSettings.overrideGlobalBarColor}
                  setOverRideSettings={setOverRideSettings}
                />
              )}
            </Box>
          </>
        }
      />
    </Stack>
  );
}
