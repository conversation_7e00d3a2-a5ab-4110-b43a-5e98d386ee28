const { test, expect, request } = require('@playwright/test');

test.describe('API Test Suite', () => {
  test('GET /assets-backoffice/asset-types/5/metrics retrieves asset metrics successfully', async ({
    request,
  }) => {
    // Set headers
    const headers = {
      'BE-CsrfToken': 'czIDJH/QjKfBj5utWf/RxmPBP0BT8h3PWW1jQXE+ROw=',
      Cookie:
        'BE-AccessToken=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VybmFtZSI6InRlc3QiLCJzdWIiOjIsInJvbGVzIjp7IkFETUlOIjpbMTI4LDgsMTIxLDExOSwxMDYsMTE4LDEwOCw4NiwxMTEsMTEyLDg1LDEwNyw4NCwxMTMsMTE0LDExNywxMjAsMTI3LDEyMywxMjYsMTI0LDEwOSwxMjUsMTIyLDExMCw5LDgyXSwiVVNFUiI6WzEyOCw4LDEyMSwxMTksMTA2LDExOCwxMDgsODYsMTExLDExMiw4NSwxMDcsODQsMTEzLDExNCwxMTcsMTIwLDEyNywxMjMsMTI2LDEyNCwxMDksMTI1LDEyMiwxMTAsOSw4Ml0sIlBPV0VSX1VTRVIiOlsxMjgsOCwxMjEsMTE5LDEwNiwxMTgsMTA4LDg2LDExMSwxMTIsODUsMTA3LDg0LDExMywxMTQsMTE3LDEyMCwxMjcsMTIzLDEyNiwxMjQsMTA5LDEyNSwxMjIsMTEwLDksODJdfSwiaWF0IjoxNzMxNTY3Nzk5LCJleHAiOjE3MzE1NzQ5OTl9.-1B_2e62YfCC9W7PndzEho24dlXcjrDa2K8JUBivBiQ; BE-CSRFToken=czIDJH%2FQjKfBj5utWf%2FRxmPBP0BT8h3PWW1jQXE%2BROw%3D',
    };

    // Make GET request
    const response = await request.get(
      'https://test.brompton.ai/api/v0/assets-backoffice/asset-types/5/metrics',
      {
        headers: headers,
      },
    );

    // Check response status
    expect(response.status()).toBe(200);

    // Verify response body if needed
    const responseBody = await response.json();
    console.log(responseBody);

    // Perform assertions on the response data
    expect(responseBody).toHaveProperty('items'); // Check that 'items' exists
    expect(Array.isArray(responseBody.items)).toBe(true); // Check if 'items' is an array
    expect(responseBody).toHaveProperty('total'); // Check that 'total' exists in the response
    expect(responseBody.total).toBeGreaterThanOrEqual(0); // Check that total is a non-negative integer
  });
});
