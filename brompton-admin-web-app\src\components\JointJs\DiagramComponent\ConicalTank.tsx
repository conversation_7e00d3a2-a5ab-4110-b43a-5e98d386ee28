import { dia, util } from '@joint/core';

export class ConicTank extends dia.Element {
  valLevel = 0;
  maxCapacity: number; // Added maxCapacity property
  colors: { low: string; medium: string; high: string; full: string };
  constructor(
    attributes = {},
    options = {},
    level = 20,
    maxCapacity = 100, // Default maxCapacity set to 100
    color = { low: '#ffa500', medium: '#ffff00', high: '#008000', full: '#ff0000' },
  ) {
    super(attributes, options);
    this.valLevel = level;
    this.maxCapacity = maxCapacity; // Initialize maxCapacity
    this.colors = {
      ...color,
    };
  }

  defaults(): Partial<dia.Element.Attributes> {
    return {
      ...super.defaults,
      type: 'ConicTank',
      size: {
        width: 160,
        height: 80,
      },
      attrs: {
        root: {
          magnetSelector: 'body',
        },
        body: {
          stroke: 'gray',
          strokeWidth: 4,
          x: 0,
          y: 0,
          width: 'calc(w)',
          height: 'calc(h - 35)',
          rx: 120,
          ry: 10,
          fill: {
            type: 'linearGradient',
            stops: [
              { offset: '0%', color: 'gray' },
              { offset: '30%', color: 'white' },
              { offset: '70%', color: 'white' },
              { offset: '100%', color: 'gray' },
            ],
          },
        },
        top: {
          x: 0,
          y: 15,
          width: 'calc(w)',
          height: 15,
          fill: 'none',
          stroke: 'gray',
          strokeWidth: 2,
        },
        bottom: {
          d: 'M 0 0 L calc(w) 0 L calc(w / 2 + 10) calc(h / 2) h -20 Z',
          transform: 'translate(0, calc(h - 45))',
          stroke: 'gray',
          strokeLinejoin: 'round',
          strokeWidth: 2,
          fill: {
            type: 'linearGradient',
            stops: [
              { offset: '10%', color: '#aaa' },
              { offset: '30%', color: '#fff' },
              { offset: '90%', color: '#aaa' },
            ],
            attrs: {
              gradientTransform: 'rotate(-10)',
            },
          },
        },
        label: {
          text: 'Tank 2',
          textAnchor: 'middle',
          textVerticalAnchor: 'bottom',
          x: 'calc(w / 2)',
          y: 170,
          fontSize: 14,
          fontFamily: 'sans-serif',
        },
      },
    };
  }

  get level() {
    return this.get('level') || 0;
  }

  set level(level) {
    const newLevel = Math.max(0, Math.min(this.maxCapacity, level)); // Use maxCapacity to bound the level
    this.set('level', newLevel);

    // Update border color based on the level as a percentage of maxCapacity
    let borderColor;
    const levelPercentage = (newLevel / this.maxCapacity) * 100;

    if (levelPercentage > 20 && levelPercentage <= 40) {
      borderColor = this.colors.medium;
    } else if (levelPercentage > 40 && levelPercentage <= 80) {
      borderColor = this.colors.high;
    } else if (levelPercentage > 80) {
      borderColor = this.colors.full;
    }

    // Set the stroke attribute dynamically
    this.attr('body/stroke', borderColor);
    this.attr('bottom/stroke', borderColor);
  }

  preinitialize() {
    this.markup = util.svg/* xml */ `
        <path @selector="bottom"/>
        <rect @selector="body"/>
        <rect @selector="top"/>
        <text @selector="label" />
      `;
  }

  updateMaxCapacity(newMaxCapacity: number) {
    this.maxCapacity = Math.max(0, newMaxCapacity);
    this.level = this.level; // Reapply the level to ensure it's bounded by the new maxCapacity
  }
}
