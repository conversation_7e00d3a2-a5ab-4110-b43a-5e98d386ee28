import {
  Checkbox,
  Divider,
  FormControl,
  FormControlLabel,
  FormGroup,
  MenuItem,
  Select,
  SelectChangeEvent,
  TextField,
  Typography,
} from '@mui/material';
import { ReactNode } from 'react';
import { ForecastWidget, forecastPeriods, setForecastWidgetSettings } from '~/types/widgets';

type ForecastSettingsProps = {
  settings: ForecastWidget;
  setSettings: setForecastWidgetSettings;
};
const ForecastSettings = ({ settings, setSettings }: ForecastSettingsProps) => {
  const handleShowForecast = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSettings((prevState) => ({
      ...prevState,
      showForecast: event.target.checked,
    }));
  };
  const handlePeriodChange = (event: SelectChangeEvent<string>, child: ReactNode) => {
    setSettings((prevState) => ({
      ...prevState,
      period: event.target.value as '24hr' | 'eom',
    }));
  };
  const handleColorChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSettings((prevState) => ({
      ...prevState,
      forecastColor: event.target.value,
    }));
  };
  const handleThresholdStyleChange = (event: SelectChangeEvent<'solid' | 'dash' | 'dot'>) => {
    setSettings((prevState) => ({
      ...prevState,
      meanStyle: event.target.value as 'solid' | 'dash' | 'dot',
    }));
  };
  const showMean = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSettings((prevState) => ({
      ...prevState,
      showMean: event.target.checked,
    }));
  };
  const handleMeanColorChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSettings((prevState) => ({
      ...prevState,
      meanColor: event.target.value,
    }));
  };
  const handleMeanNameChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSettings((prevState) => ({
      ...prevState,
      meanName: event.target.value,
    }));
  };
  return (
    <>
      <Divider />
      <Typography variant="h5" mt={2} mb={1}>
        Forecast Settings
      </Typography>
      <FormControlLabel
        control={
          <Checkbox
            checked={settings?.showForecast}
            onChange={handleShowForecast}
            name="showForecast"
          />
        }
        label="Show Forecast"
      />
      {settings?.showForecast && (
        <>
          <FormControl fullWidth>
            <Select
              sx={{
                width: '100%',
                p: 0.3,
                '& fieldset': {
                  '& legend': {
                    maxWidth: '100%',
                    height: 'auto',
                    '& span': {
                      opacity: 1,
                    },
                  },
                },
              }}
              value={settings.period}
              onChange={handlePeriodChange}
              label="Forecast Period"
            >
              {forecastPeriods.map((period) => {
                return (
                  <MenuItem key={period} value={period}>
                    {period}
                  </MenuItem>
                );
              })}
            </Select>
          </FormControl>
          <FormGroup>
            <TextField
              name="forecastColor"
              type="color"
              label="Forecast Color"
              onChange={handleColorChange}
              value={settings.forecastColor ?? '#000000'}
              variant="outlined"
              margin="normal"
              fullWidth
            />
          </FormGroup>
          <FormControlLabel
            control={<Checkbox checked={settings?.showMean} onChange={showMean} name="showMean" />}
            label="Show Mean"
          />
          {settings.showMean && (
            <>
              <FormGroup>
                <TextField
                  name="meanName"
                  label="Mean Name"
                  onChange={handleMeanNameChange}
                  value={settings.meanName ?? 'Mean'}
                  variant="outlined"
                  margin="normal"
                  fullWidth
                />
              </FormGroup>
              <FormGroup>
                <Select
                  sx={{
                    p: 0.3,
                    '& legend': {
                      maxWidth: '100%',
                      height: 'fit-content',
                      '& span': {
                        opacity: 1,
                      },
                    },
                  }}
                  name="thresholdStyle"
                  label="Threshold Line"
                  onChange={handleThresholdStyleChange}
                  value={settings.meanStyle}
                  variant="outlined"
                  fullWidth
                >
                  <MenuItem value="solid">Solid</MenuItem>
                  <MenuItem value="dash">Dash</MenuItem>
                  <MenuItem value="dot">Dot</MenuItem>
                </Select>
              </FormGroup>
              <FormGroup>
                <TextField
                  name="thresholdColor"
                  type="color"
                  label="Mean Color"
                  onChange={handleMeanColorChange}
                  value={settings.meanColor ?? '#000000'}
                  variant="outlined"
                  margin="normal"
                  fullWidth
                />
              </FormGroup>
            </>
          )}
        </>
      )}
    </>
  );
};

export default ForecastSettings;
