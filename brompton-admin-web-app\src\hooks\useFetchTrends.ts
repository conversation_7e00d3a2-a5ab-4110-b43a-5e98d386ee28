import { Data, Layout } from 'plotly.js';
import { useEffect, useState } from 'react';
import { UnitOfMeasure } from '~/measurements/domain/types';
import { useGetMultiMeasurementSeriesQuery } from '~/redux/api/timeseriesApi';
import { AssetMeasurementDetails } from '~/types/measures';
import { SingleScatterTimeSeriesData } from '~/types/timeseries';
import { formatChartDate } from '~/utils/utils';
type TrendResult = {
  tsData: SingleScatterTimeSeriesData;
  measureData: AssetMeasurementDetails;
  unitOfMeasures: UnitOfMeasure[];
};
type FetchTrendsProps = {
  customerId: number;
  measureId: number;
  startDate: number;
  endDate: number;
  threshold: number;
};
export const useFetchTrends = ({
  customerId,
  measureId,
  endDate,
  startDate,
  threshold,
}: FetchTrendsProps) => {
  const [allDataFetched, setAllDataFetched] = useState<{
    chartData: Data[];
    isLoading: boolean;
    error: boolean;
    layoutData: Partial<Layout>;
  }>({
    chartData: [] as Data[],
    isLoading: true,
    error: false,
    layoutData: {
      showlegend: true,
      title: 'Chart',
    } as Partial<Layout>,
  });
  const { data, isFetching, isError } = useGetMultiMeasurementSeriesQuery(
    {
      agg: 'twa',
      agg_period: '4hr',
      assetTz: true,
      customerId: customerId,
      measId: measureId.toString(),
      start: startDate,
      end: endDate,
      timeRangeType: 0,
    },
    {
      skip: !startDate || !endDate,
      refetchOnMountOrArgChange: true,
    },
  );
  useEffect(() => {
    if (data && !isFetching && !isError) {
      const chartData: Data[] = [];
      const layoutData: Partial<Layout> = {
        xaxis: {
          title: 'Time',
          position: 0,
        },
        yaxis: {
          title: 'Value',
          position: 0,
        },
        legend: {
          x: 0, // Position legend at the left
          y: -0.1, // Position legend slightly below the x-axis
          xanchor: 'left', // Anchor the legend to the right side of the x position
          yanchor: 'top', // Anchor the legend to the top side of the y position
        },
        autosize: true,
        margin: {
          t: 0,
          b: 200,
          pad: 0,
        },
      };
      if (!data[measureId]) {
        setAllDataFetched({
          chartData,
          error: true,
          isLoading: false,
          layoutData,
        });
        return;
      }
      const tsData = data[measureId]['ts,val'];
      const measureData = data[measureId].tag_meta;
      const traces: Data[] = [];
      const x = tsData.map((d) => formatChartDate(new Date(d[0])));
      const y = tsData.map((d) => d[1]);
      traces.push({
        x,
        y,
        type: 'scatter',
        mode: 'lines',
        hovertemplate: `%{y} ${measureData.uom} <br>Time: %{x}<extra></extra>`,
      });
      chartData.push(...traces);
      layoutData.shapes = [];
      layoutData.annotations = [];
      layoutData.shapes?.push({
        type: 'line',
        x0: 0,
        x1: 1,
        xref: 'paper',
        y0: threshold,
        y1: threshold,
        yref: 'y',
        line: {
          color: '#d3d3d3',
          width: 2,
          dash: 'solid',
        },
      });
      layoutData.annotations?.push({
        xref: 'paper',
        yref: 'y',
        x: 1,
        xanchor: 'left',
        y: threshold,
        text: `Threshold:${threshold} ${measureData.uom}`,
        showarrow: false,
      });
      setAllDataFetched({
        chartData,
        error: isError,
        isLoading: isFetching,
        layoutData: layoutData,
      });
    }
    if (isError) {
      setAllDataFetched({
        chartData: [] as Data[],
        error: true,
        isLoading: false,
        layoutData: {
          showlegend: true,
          title: 'Chart',
        } as Partial<Layout>,
      });
    }
  }, [data, isFetching, isError, measureId, threshold]);
  return allDataFetched;
};
