import { expect } from '@playwright/test';

export const assetTestCasess = [
  // 1 description: 'Retrieve details of a specific asset for a customer',
  {
    description: 'Retrieve details of a specific asset for a customer',
    requestConfig: {
      method: 'GET',
      url: 'https://test.pivotol.ai/api/v0/customers/8/assets/2',
      headers: {
        'Content-Type': 'application/json', // Optional if not required
      },
    },
    expectedStatus: 200, // Assuming 200 is the expected success status
    validate: (response) => {
      // Validation logic for the response
      if (response.error) {
        console.error(`Error fetching asset details: ${response.error}`);
        throw new Error('Asset retrieval failed.');
      }

      // Validate response structure
      expect(response).toHaveProperty('id', 2); // Asset ID should match the requested asset
      expect(response).toHaveProperty('tag', expect.any(String)); // Validate 'tag' instead of 'name'
      expect(response).toHaveProperty('type_id', expect.any(Number)); // Validate 'type_id' exists and is a number
      expect(response).toHaveProperty('latitude', expect.any(Number)); // Validate 'latitude' exists and is a number
      expect(response).toHaveProperty('longitude', expect.any(Number)); // Validate 'longitude' exists and is a number

      console.log('Asset details retrieved successfully:', response);
    },
  },
  // 2 description: 'Update a specific asset for a customer',
  {
    description: 'Update a specific asset for a customer',
    requestConfig: {
      method: 'PATCH',
      url: 'https://test.pivotol.ai/api/v0/customers/1/assets/2',
      headers: {
        'Content-Type': 'application/json',
      },
      body: {
        description: 'dubidubi', // Update the description field
      },
    },
    expectedStatus: 200, // Assuming 200 is the expected status for a successful PATCH request
    validate: async (response) => {
      if (response.error || response.statusCode === 403) {
        console.warn('403 Forbidden: Unauthorized access to the asset.');
        console.warn('Full Response:', response);
        console.warn(
          'Ensure that the BE-AccessToken and BE-CsrfToken are valid and that the user has permissions.',
        );
        // Skip further validation for 403 errors
        return;
      }

      // Confirm the returned data reflects the updated description
      expect(response).toHaveProperty('description', 'dubidubi');
      expect(response).toHaveProperty('id', 2); // Validate the correct asset ID
      console.log('Asset updated successfully:', response);
    },
  },
  // 3 description: 'Retrieve assets with parentIds filter',
  {
    description: 'Retrieve assets with parentIds filter',
    requestConfig: {
      method: 'GET',
      url: 'https://test.pivotol.ai/api/v0/customers/82/assets?parentIds=-1,318&=',
      headers: {
        'BE-CsrfToken': 'strsjoPhK+WYnHZNXVMRJWGCvzJ9KXdAsD6YLUWaV4E=',
        Cookie: 'BE-AccessToken=your_token_here', // Replace with a valid token during runtime
      },
    },
    expectedStatus: 200,
    validate: (response) => {
      expect(response).toHaveProperty('items');
      expect(response.items).toBeInstanceOf(Array);
      console.log('Assets fetched successfully:', response.items);
    },
  },
  // 4 description: 'Retrieve asset types',
  {
    description: 'Retrieve asset types',
    requestConfig: {
      method: 'GET',
      url: 'https://test.pivotol.ai/api/v0/assets-backoffice/asset-types',
      headers: {
        'BE-CsrfToken': 'strsjoPhK+WYnHZNXVMRJWGCvzJ9KXdAsD6YLUWaV4E=',
        Cookie: 'BE-AccessToken=your_token_here', // Replace with a valid token during runtime
      },
    },
    expectedStatus: 200,
    validate: (response) => {
      const assetTypes = response.items; // Map items to assetTypes for consistency
      expect(assetTypes).toBeInstanceOf(Array);
      console.log('Asset types retrieved successfully:', assetTypes);
    },
  },
  // 5 post description: 'Create a new asset type',
  {
    description: 'Create a new asset type',
    requestConfig: {
      method: 'POST',
      url: 'https://test.pivotol.ai/api/v0/assets-backoffice/asset-types',
      headers: {
        'BE-CsrfToken': 'strsjoPhK+WYnHZNXVMRJWGCvzJ9KXdAsD6YLUWaV4E=',
        'Content-Type': 'application/json',
        Cookie: 'BE-AccessToken=your_token_here', // Replace with a valid token during runtime
      },
      body: { name: 'Rocket Engine' },
    },
    expectedStatus: 201,
    validate: (response) => {
      if (response.statusCode === 201) {
        expect(response).toHaveProperty('id', expect.any(Number));
        expect(response).toHaveProperty('name', 'Rocket Engine');
      } else if (response.statusCode === 500) {
        expect(response.exception.code).toBe('23505');
        console.error('Duplicate entry detected:', response.message);
      } else {
        throw new Error('Unexpected response: ' + JSON.stringify(response));
      }
    },
  },
  // 6  description: 'Retrieve metrics for a specific asset type',
  {
    description: 'Retrieve metrics for a specific asset type',
    requestConfig: {
      method: 'GET',
      url: 'https://test.pivotol.ai/api/v0/assets-backoffice/asset-types/5/metrics',
      headers: {
        'BE-CsrfToken': 'strsjoPhK+WYnHZNXVMRJWGCvzJ9KXdAsD6YLUWaV4E=',
        Cookie: 'BE-AccessToken=your_token_here', // Replace with a valid token during runtime
      },
    },
    expectedStatus: 200,
    validate: (response) => {
      // Ensure the response contains the `items` property
      expect(response).toHaveProperty('items');

      // Validate that `items` is an array
      expect(response.items).toBeInstanceOf(Array);

      // Optionally, validate the `total` property
      expect(response).toHaveProperty('total', expect.any(Number));

      // Log the response for debugging
      console.log('Metrics retrieved successfully:', response.items);
    },
  },
  // 7 description: 'Retrieve asset templates for a specific asset type',
  {
    description: 'Retrieve asset templates for a specific asset type',
    requestConfig: {
      method: 'GET',
      url: 'https://test.pivotol.ai/api/v0/assets-backoffice/asset-types/5/asset-templates',
      headers: {
        'BE-CsrfToken': 'strsjoPhK+WYnHZNXVMRJWGCvzJ9KXdAsD6YLUWaV4E=',
        Cookie: 'BE-AccessToken=your_token_here', // Replace with a valid token during runtime
      },
    },
    expectedStatus: 200,
    validate: (response) => {
      expect(response).toHaveProperty('items');
      expect(response.items).toBeInstanceOf(Array);

      if (response.items.length > 0) {
        console.log('Asset templates retrieved:', response.items);
      } else {
        console.warn('No asset templates found.');
      }

      expect(response).toHaveProperty('total', expect.any(Number));
    },
  },
  // 8  description: 'Create a new asset instance',
  {
    description: 'Create a new asset instance',
    requestConfig: {
      method: 'POST',
      url: 'https://test.pivotol.ai/api/v0/assets-backoffice/asset-types/5/asset-templates/11/instances',
      headers: {
        'BE-CsrfToken': 'strsjoPhK+WYnHZNXVMRJWGCvzJ9KXdAsD6YLUWaV4E=',
        'Content-Type': 'application/json',
        Cookie: 'BE-AccessToken=your_token_here', // Replace with a valid token during runtime
      },
      body: {
        units_group_id: 40,
        asset: {
          tag: 'Testing Toyota Generator',
          description: 'Generator used for testing',
          parent_ids: [],
          time_zone: 'America/Argentina/Buenos_Aires',
          customer_id: 8,
        },
        measurements: [],
      },
    },
    expectedStatus: 201,
    validate: (response) => {
      if (response.statusCode === 201) {
        // Success case: validate the response structure
        expect(response).toHaveProperty('id', expect.any(Number));
        expect(response.asset).toHaveProperty('tag', 'Testing Toyota Generator');
        console.log('Asset instance created successfully:', response);
      } else if (response.statusCode === 400) {
        // Error case: handle the "Asset template not found" error
        expect(response.exception).toHaveProperty('name', 'InvalidInputException');
        expect(response.exception.response).toHaveProperty('message', 'Asset template not found');
        console.error('Failed to create asset instance:', response.message);
      } else {
        // Unexpected status code
        throw new Error(`Unexpected response: ${JSON.stringify(response)}`);
      }
    },
  },
  // 9 description: 'Create a new asset template for a specific asset type',
  {
    description: 'Create a new asset template for a specific asset type',
    requestConfig: {
      method: 'POST',
      url: 'https://test.pivotol.ai/api/v0/assets-backoffice/asset-types/9/asset-templates',
      headers: {
        //  "BE-CsrfToken": "strsjoPhK+WYnHZNXVMRJWGCvzJ9KXdAsD6YLUWaV4E=",
        'Content-Type': 'application/json',
        //  "Cookie": "BE-AccessToken=your_token_here", // Replace with a valid token dynamically
      },
      body: {
        manufacturer: 'SpaceX',
        model_number: 'Raptor',
        measurements: [],
      },
    },
    expectedStatus: 201,
    validate: (response) => {
      if (response.statusCode === 201) {
        // Success case
        expect(response).toHaveProperty('id', expect.any(Number));
        expect(response).toHaveProperty('manufacturer', 'SpaceX');
        console.log('Asset template created successfully:', response);
      } else if (response.statusCode === 500) {
        // Handle unique constraint violation
        expect(response.exception).toHaveProperty('name', 'UniqueConstraintViolationException');
        expect(response.exception.constraint).toBe('asset_template_manufacturer_model_no_unique');
        console.error('Duplicate asset template detected:', response.message);
      } else {
        // Unexpected status code
        throw new Error(`Unexpected response: ${JSON.stringify(response)}`);
      }
    },
  },
  // 10 description: 'Retrieve all timeseries data for a specific asset',
  {
    description: 'Retrieve all timeseries data for a specific asset',
    requestConfig: {
      method: 'GET',
      url: 'https://test.pivotol.ai/api/v0/timeseries/all/84',
      headers: {
        //   "BE-CsrfToken": "your_csrf_token_here", // Replace with a valid token dynamically
        //   "Cookie": "BE-AccessToken=your_access_token_here", // Replace with a valid token dynamically
        'Content-Type': 'application/json',
      },
    },
    expectedStatus: 200,
    validate: (response) => {
      if (response.statusCode === 200) {
        // Success case: Validate the timeseries data
        expect(response).toHaveProperty('id', 84);
        expect(response).toHaveProperty('data'); // Assuming the response contains `data`
        console.log('Timeseries data retrieved successfully:', response);
      } else if (response.statusCode === 404) {
        // Not Found case: Log the error for debugging
        expect(response.exception).toHaveProperty('name', 'NotFoundException');
        expect(response.exception.response).toHaveProperty(
          'message',
          'Cannot GET /v0/timeseries/all/84',
        );
        console.warn('Timeseries data not found:', response.message);
      } else {
        // Unexpected status code
        throw new Error(`Unexpected response: ${JSON.stringify(response)}`);
      }
    },
  },
  // 31 description: 'Create a new asset for a customer',
  {
    description: 'Create a new asset for a customer',
    requestConfig: {
      method: 'POST',
      url: 'https://test.pivotol.ai/api/v0/customers/2/assets',
      headers: {
        //    "BE-CsrfToken": "your_csrf_token_here", // Replace with dynamic token
        'Content-Type': 'application/json',
        //   "Cookie": "BE-AccessToken=your_access_token_here", // Replace with dynamic token
      },
      body: {
        tag: 'Sub Sub Compressor',
        type_id: 2,
        description: 'Heavy duty compressor',
        parent_ids: [],
        is_customer_primary: true,
      },
    },
    expectedStatus: 201,
    validate: (response) => {
      if (response.statusCode === 201) {
        // Success case: Validate the asset creation response
        expect(response).toHaveProperty('id', expect.any(Number));
        expect(response).toHaveProperty('tag', 'Sub Sub Compressor');
        console.log('Asset created successfully:', response);
      } else if (response.statusCode === 403) {
        // Forbidden case: Log the error for debugging
        expect(response.exception).toHaveProperty('name', 'ForbiddenException');
        expect(response.exception.response).toHaveProperty('message', 'Unauthorized');
        console.error('Authorization error:', response.message);
      } else {
        // Unexpected status code
        throw new Error(`Unexpected response: ${JSON.stringify(response)}`);
      }
    },
  },
];
