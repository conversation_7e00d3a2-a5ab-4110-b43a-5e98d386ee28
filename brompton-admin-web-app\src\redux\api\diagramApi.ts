import { diagram, diagramDTO } from '~/types/diagram';
import { authApi } from './authApi';

export const diagramApi = authApi
  .enhanceEndpoints({
    addTagTypes: ['diagram'],
  })
  .injectEndpoints({
    endpoints: (build) => ({
      createDiagram: build.mutation<
        {
          data: string;
          id: number;
          name: string;
        },
        { name: string; data: string }
      >({
        query: ({ name, data }) => {
          return {
            url: '/v0/diagram/',
            method: 'POST',
            body: {
              name,
              data,
            },
          };
        },
        invalidatesTags: ['diagram'],
      }),
      updateDiagram: build.mutation<
        {
          data: string;
          id: number;
          name: string;
        },
        { id: number; name: string; data: string }
      >({
        query: ({ id, name, data }) => {
          return {
            url: `/v0/diagram/${id}`,
            method: 'PATCH',
            body: {
              name,
              data,
            },
          };
        },
        // invalidatesTags: ['diagram'],
      }),
      getAllDiagrams: build.query<diagramDTO, void>({
        query: () => '/v0/diagram/',
        providesTags: ['diagram'],
      }),
      getDiagramDetails: build.query<diagram, { id: number }>({
        query: ({ id }) => `/v0/diagram/${id}`,
        providesTags: ['diagram'],
      }),
    }),
  });

export const {
  useGetAllDiagramsQuery,
  useGetDiagramDetailsQuery,
  useCreateDiagramMutation,
  useUpdateDiagramMutation,
} = diagramApi;
