import {
  Autocomplete,
  Box,
  Button,
  MenuItem,
  Select,
  SelectChangeEvent,
  TextField,
} from '@mui/material';
import { Dispatch, ReactNode, SetStateAction, useEffect, useState } from 'react';
import { useGetCalculationEngineTemplatesQuery } from '~/redux/api/calculationEngine';
import { useGetCustomersQuery } from '~/redux/api/customersApi';
import { calc_engine_template } from '~/types/calc_engine';
import { Customer } from '~/types/customers';
import ExpressionTemplateDetails from './ExpressionTemplateDetails';

type ExpressionTemplateSelectorProps = {
  expressionTemplate: calc_engine_template | null;
  setStep: Dispatch<SetStateAction<number>>;
  setExpressionTemplate: Dispatch<SetStateAction<calc_engine_template | null>>;
  customer: Customer | null;
  setCustomer: Dispatch<SetStateAction<Customer | null>>;
  templateId: number;
};
const ExpressionTemplateSelector = ({
  expressionTemplate,
  setExpressionTemplate,
  setStep,
  customer,
  setCustomer,
  templateId,
}: ExpressionTemplateSelectorProps) => {
  const { data: expressionTemplates } = useGetCalculationEngineTemplatesQuery();
  const [selectedTemplate, setSelectedTemplate] = useState<calc_engine_template | null>(
    expressionTemplate ?? null,
  );
  const { data: customerList, isLoading: isCustomerListLoading } = useGetCustomersQuery({});

  useEffect(() => {
    if (expressionTemplates && expressionTemplate) {
      setSelectedTemplate(
        expressionTemplates?.items?.find((template) => template.id === expressionTemplate.id) ??
          null,
      );
    }
  }, [expressionTemplate]);
  useEffect(() => {
    if (expressionTemplates && templateId && templateId !== 0) {
      setExpressionTemplate(
        (expressionTemplates?.items?.find(
          (template) => template.id === templateId,
        ) as calc_engine_template) || null,
      );
    }
  }, [templateId, expressionTemplates]);
  const handleChange = (event: SelectChangeEvent<number | null>, child: ReactNode) => {
    setExpressionTemplate(
      (expressionTemplates?.items?.find(
        (template) => template.id === event.target.value,
      ) as calc_engine_template) || null,
    );
  };
  const handleCustomerChange = (e: React.SyntheticEvent, value: Customer | null) => {
    setCustomer(value);
  };
  return (
    <Box mt={3} pl={3} pr={3}>
      <Box mb={3}>
        <Select
          value={expressionTemplate?.id ?? ''}
          sx={{
            width: 300,
            '& fieldset': {
              '& legend': {
                maxWidth: '100%',
                height: 'auto',
                '& span': {
                  opacity: 1,
                },
              },
            },
          }}
          onChange={handleChange}
          label="Expression Template"
        >
          {expressionTemplates?.items?.map((template) => (
            <MenuItem key={template.id} value={template.id}>
              {template.name}
            </MenuItem>
          ))}
        </Select>
      </Box>
      <Box>
        <Autocomplete<Customer>
          id="top-panel-customer-list"
          options={customerList ?? []}
          loading={isCustomerListLoading}
          getOptionLabel={(option) => option.name}
          onChange={handleCustomerChange}
          sx={{
            width: 300,
          }}
          isOptionEqualToValue={(option, value) => option.id === value.id}
          value={customer ?? null}
          renderInput={(params) => <TextField {...params} label="Customer" />}
        />
      </Box>

      <ExpressionTemplateDetails selectedTemplate={selectedTemplate} />
      <Box width={'100%'} mt={3}>
        <Button
          disabled={selectedTemplate === null || customer === null}
          onClick={() => setStep(1)}
        >
          Next
        </Button>
      </Box>
    </Box>
  );
};

export default ExpressionTemplateSelector;
