const { test, expect } = require('@playwright/test');

test.describe('API Testing for PATCH Dashboard Update', () => {
  test('should successfully update the dashboard with status 200', async ({ request }) => {
    // Define headers
    const headers = {
      'BE-CsrfToken': 'tsZ1a+6fnjL6lLBAEloF3C00Ob88Fm44ru9HQBgJToY=',
      'Content-Type': 'application/json',
      Cookie:
        'BE-AccessToken=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VybmFtZSI6InRlc3QiLCJzdWIiOjIsInJvbGVzIjp7IkFETUlOIjpbOCwxMjEsMTE5LDEwNiwxMTgsMTA4LDg2LDExMSwxMTIsODUsMTA3LDg0LDExMywxMTQsMTE3LDEyMCwxMjcsMTIzLDEyNiwxMjQsMTA5LDEyNSwxMjIsMTEwLDksODJdLCJVU0VSIjpbOCwxMjEsMTE5LDEwNiwxMTgsMTA4LDg2LDExMSwxMTIsODUsMTA3LDg0LDExMywxMTQsMTE3LDEyMCwxMjcsMTIzLDEyNiwxMjQsMTA5LDEyNSwxMjIsMTEwLDksODJdLCJQT1dFUl9VU0VSIjpbOCwxMjEsMTE5LDEwNiwxMTgsMTA4LDg2LDExMSwxMTIsODUsMTA3LDg0LDExMywxMTQsMTE3LDEyMCwxMjcsMTIzLDEyNiwxMjQsMTA5LDEyNSwxMjIsMTEwLDksODJdfSwiaWF0IjoxNzMxNTA4MTU1LCJleHAiOjE3MzE1MTUzNTV9.-bV6oYP96wYs6Kj7xXxI1lo_VoHKMWj801qHvrWHiOY; BE-CSRFToken=tsZ1a%2B6fnjL6lLBAEloF3C00Ob88Fm44ru9HQBgJToY%3D',
    };

    // Define body payload
    const payload = {
      data: {
        currentDashboardId: 65,
        dashboardTitle: 'animated dashboard',
        userDetails: {
          id: 2,
          username: 'test',
          email: '<EMAIL>',
          enabled: true,
          first_name: 'Dev',
          last_name: 'Test',
          global_role: 'ADMIN',
          scoped_roles: [],
        },
        customer: {
          nameId: 'brompton_energy_inc_',
          id: 8,
          name: 'Brompton Energy Inc.',
          address: 'Houston, TX',
        },
        userPreferences: {
          CURRENCY: 'EUR',
          DATE_FORMAT: 'DD-MM-YYYY HH:mm:ss',
          DEFAULT_CUSTOMER: '8',
        },
        dashboardCrumb: [],
        mainPanel: 'chart',
        isLeftPanelOpen: true,
        isDirty: false,
        kisok: true,
        fullScreen: true,
        rightSideBar: false,
        dateFormat: 0,
        rightSideBarActiveTab: '/icons/alerts.svg',
        topPanel: {
          isVisible: true,
          timeRangeType: 6,
          refreshInterval: 60000,
          samplePeriod: 2,
          assetTz: true,
        },
        tree: {
          currentSelectedNodeId: 'm:257:11286',
          selectedNodeIds: ['-1', 'm:257:11290', 'm:257:11286'],
          expandedNodeIds: ['-1', '257:23', '258', '257'],
          selectedViewMeasureId: '-1',
          dbMeasureIdToName: {
            11286: 'Brenes\\MAINPANEL\\AVERAGE_PHASE_VOLTAGE',
            11290: 'Brenes\\MAINPANEL\\AVERAGE_LINE_VOLTAGE',
          },
        },
        chart: {
          startDate: 1715062366072,
          endDate: 1715083966072,
        },
        widget: {
          widgets: [
            {
              id: '1',
              type: 'stats',
              settings: {
                title: { value: 'Average Line Voltage', isVisible: true },
                aggBy: 1,
                samplePeriod: 2,
                selectedDbMeasureId: '11290',
                showMin: false,
                showMax: false,
                showAvg: true,
                showDelta: false,
                showTotal: false,
                isValid: true,
              },
            },
            {
              id: '5',
              type: 'solar_panel',
              settings: {
                title: { value: 'Title', isVisible: false },
                isEditable: false,
                fontSize: 12,
                svgTexts: [
                  {
                    title: 'Real Power Volume',
                    position: { x: 100, y: 20 },
                    id: 'REAL_POWER_VOLUME',
                  },
                ],
                isValid: true,
                samplePeriod: 2,
                aggBy: 1,
              },
            },
          ],
          widgetLayout: [
            { w: 4, h: 2, x: 3, y: 18, i: '1' },
            { w: 11, h: 18, x: 0, y: 0, i: '5' },
          ],
          lastWidgetId: 5,
          deleteWidgets: [],
        },
      },
      title: 'animated dashboard',
    };

    // Send PATCH request
    const response = await request.patch(
      'https://test.brompton.ai/api/v0/customers/8/dashboards/58',
      {
        headers,
        data: payload,
      },
    );

    // Validate the response status is 200
    expect(response.status()).toBe(200);

    // Check the response contains expected content (customize based on API response)
    const responseBody = await response.text();
    console.log('Response:', responseBody);
    expect(responseBody).toContain('expected_content_here'); // Adjust this to actual expected content
  });
});
