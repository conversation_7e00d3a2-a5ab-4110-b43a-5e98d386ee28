import { useSelector } from 'react-redux';
import CreateAssetTemplateInstance from '../AssetTemplateInstance/CreateAssetTemplateInstance/CreateAssetTemplateInstance';
import { getActiveCustomer } from '~/redux/selectors/customerSelectors';
import {
  getCurrentSelectedAssetId,
  getCurrentSelectedNodeId,
} from '~/redux/selectors/treeSelectors';
import { useGetAssetByIdQuery } from '~/redux/api/assetsApi';
import { AssetDo } from '~/types/asset';

const AssetTemplateInstance = () => {
  const activeCustomer = useSelector(getActiveCustomer);
  const currentNode = useSelector(getCurrentSelectedNodeId);
  const assetId = useSelector(getCurrentSelectedAssetId);
  const { data: asset, isLoading: isAssetDataLoading } = useGetAssetByIdQuery(
    { customerId: activeCustomer?.id ?? 0, assetId: assetId },
    {
      skip: !activeCustomer?.id, // In RTK Query, the option is `skip` instead of `enabled`
    },
  );
  return (
    <>
      <CreateAssetTemplateInstance
        selectedCustomerId={activeCustomer ? activeCustomer.id : undefined}
        asset={asset as AssetDo}
      />
    </>
  );
};
export default AssetTemplateInstance;
