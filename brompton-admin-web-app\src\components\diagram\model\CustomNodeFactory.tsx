import { CustomNodeWidget } from './CustomNodeWidget';
import { CustomNodeModel } from './CustomNodeModel';
import { AbstractReactFactory } from '@projectstorm/react-canvas-core';
import { DiagramEngine } from '@projectstorm/react-diagrams-core';
import { CustomType } from './CustomType';

export class CustomNodeFactory extends AbstractReactFactory<CustomNodeModel, DiagramEngine> {
  type: CustomType;
  constructor(type: CustomType) {
    super(type);
    this.type = type;
  }

  generateReactWidget(event: any): JSX.Element {
    // console.log(this.engine)
    return <CustomNodeWidget engine={this.engine} node={event.model} type={this.type} />;
  }

  generateModel(event: any) {
    return new CustomNodeModel('', this.type, 0);
  }
}
