import { ThunkDispatch } from '@reduxjs/toolkit';
import { useCallback, useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { CustomAnnotation } from '~/measurements/domain/types';
import { annotationApi } from '~/redux/api/annotation';
import { measuresApi } from '~/redux/api/measuresApi';
import { getCustomerId } from '~/redux/selectors/customerSelectors';
import { getCurrentDashboardId } from '~/redux/selectors/dashboardSelectors';
import { getDateTimeFormat } from '~/redux/selectors/userPreferences';
import { getGlobalEndDate, getGlobalStartDate } from '~/redux/selectors/widgetSelectors';
import { BarChartWidget, ScatterChartWidget } from '~/types/widgets';
import { formatChartDateToAssetTz, formatDate } from '~/utils/utils';

const useFetchAnnotationData = (widgetId: string, state: ScatterChartWidget | BarChartWidget) => {
  const { dbMeasureIdToAnnotation, startDate, endDate, overrideGlobalSettings } = state;
  const dateTimeFormat = useSelector(getDateTimeFormat);
  const dispatch = useDispatch<ThunkDispatch<any, any, any>>();
  const dashboard = useSelector(getCurrentDashboardId);
  const globalStartDate = useSelector(getGlobalStartDate);
  const globalEndDate = useSelector(getGlobalEndDate);
  const [measureIdAnnotation, setMeasureIdAnnotation] = useState<CustomAnnotation[]>([]);
  const customerId = useSelector(getCustomerId);
  const [selectedMesures, setSelectedMesure] = useState<string[]>([]);
  useEffect(() => {
    if (state.mode === 'dashboard') {
      const validMeasures = state.assetMeasure
        .flatMap((assetMeas) => assetMeas.measureId)
        .filter((measure) => measure.trim() !== '');
      setSelectedMesure(validMeasures);
    }
    if (state.mode === 'template') {
      const selectedMesures = state.selectedTitles.map((title) => title);
      setSelectedMesure([...selectedMesures]);
    }
  }, [state.assetMeasure, state.showStacked]);
  const fetchAnnotationData = useCallback(
    async (measureId: number) => {
      try {
        const { data, isSuccess, isError } = await dispatch(
          annotationApi.endpoints.getAnnotations.initiate({
            dashboardId: dashboard.toString(),
            widgetId: widgetId,
            start: overrideGlobalSettings ? startDate : globalStartDate,
            end: overrideGlobalSettings ? endDate : globalEndDate,
            measureId: measureId.toString(),
          }),
        );

        if (!isSuccess || isError) {
          console.error('Failed to fetch annotations');
          return [];
        }

        // Parse and extract only the settings field
        const parsedSettings: CustomAnnotation[] = data.items
          .map((item) => {
            try {
              const settings = JSON.parse(item.settings || '{}') as CustomAnnotation;
              const annotationSettings: CustomAnnotation = {
                ...settings,
                // x: formatChartDateToAssetTz(new Date(Number(item.time_of_annotation))),
                x: formatDate(new Date(Number(item.time_of_annotation)), 'YYYY-MM-DD HH:mm:ss'),
                y: Number(item.value),
                captureevents: true,
                arrowsize: 20,
                arrowwidth: 20,
                arrowhead: 4,
                startarrowsize: 1,
                text: '',
                showarrow: false,
                visible: true,
                width: 3,
                height: 3,
                opacity: 1,
                id: item.id ?? 0,
                measurement_id: item.measurement_id,
              };
              return annotationSettings;
            } catch (error) {
              return undefined; // Return undefined if parsing fails
            }
          })
          .filter((annotation): annotation is CustomAnnotation => annotation !== undefined);

        return parsedSettings; // Return the parsed settings
      } catch (error) {
        console.error('Error fetching annotation data:', error);
        return [];
      }
    },
    [
      dispatch,
      startDate,
      endDate,
      globalEndDate,
      globalStartDate,
      overrideGlobalSettings,
      widgetId,
      selectedMesures,
    ],
  );

  const fetchMeasureDataByAsset = useCallback(
    async (assetId: string, measureId: string) => {
      if (assetId === '' || !assetId) {
        throw new Error(`No assetId found for ${measureId}`);
      }

      if (!measureId) {
        throw new Error(`Error invalid measureId: ${measureId}`);
      }

      const { data: measureData, isSuccess: isMeasureDataSuccess } = await dispatch(
        measuresApi.endpoints?.getMeasurementById.initiate({
          customerId,
          assetId: assetId,
          measId: measureId,
        }),
      );

      if (!isMeasureDataSuccess || !measureData) {
        throw new Error(`Error fetching measure data for measureId: ${measureId}`);
      }

      return measureData;
    },
    [customerId, dispatch],
  );
  useEffect(() => {
    const fetchAnnotations = async () => {
      if (Object.entries(state.dbMeasureIdToAnnotation).length > 0) {
        const measureIds = state.assetMeasure
          .flatMap((assetMeas) => assetMeas.measureId)
          .filter((measure) => measure.trim() !== '');
        const measurementDatas = state.assetMeasure.map(async (assetMeas) => {
          return Promise.all(
            assetMeas.measureId.map(async (measure) => {
              if (measure === '' || !measureIds.includes(measure)) return;
              return await fetchMeasureDataByAsset(assetMeas.assetId, measure);
            }),
          );
        });
        const results = (await Promise.all(measurementDatas)).flat().filter(Boolean);
        const allAnnotations: CustomAnnotation[] = [];
        const processedMeasureIds = new Set<number>(); // Set to track already processed measureIds
        for (const [dbMeasureId, isAnnotationTrue] of Object.entries(
          state.dbMeasureIdToAnnotation,
        )) {
          const meaureId = results.find((result) => result?.id === Number(dbMeasureId));
          if (
            isAnnotationTrue === true &&
            !processedMeasureIds.has(Number(dbMeasureId)) &&
            selectedMesures.includes(dbMeasureId) &&
            meaureId
          ) {
            const annotations = await fetchAnnotationData(Number(meaureId.measurementId));
            allAnnotations.push(...annotations); // Collect annotations for this measureId
            processedMeasureIds.add(Number(dbMeasureId)); // Mark this measureId as processed
          }
        }

        // Set all annotations once after fetching all data
        setMeasureIdAnnotation(allAnnotations);
      }
    };
    if (state.mode === 'dashboard') {
      fetchAnnotations();
    }
  }, [
    dbMeasureIdToAnnotation,
    startDate,
    endDate,
    globalEndDate,
    globalStartDate,
    overrideGlobalSettings,
    widgetId,
    fetchAnnotationData,
    selectedMesures,
  ]);

  return { measureIdAnnotation };
};

export default useFetchAnnotationData;
