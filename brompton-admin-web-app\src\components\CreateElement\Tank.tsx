import { dia, util } from '@joint/core';

export type LiquidTankConfig = {
  barSetting: {
    direction: 'vertical' | 'horizontal';
  };
  colors?: {
    low?: string;
    medium?: string;
    high?: string;
    full?: string;
  };
  level: number;
};

export default class LiquidTank extends dia.Element {
  private interval: NodeJS.Timeout | null = null;
  barSettings: {
    direction: 'vertical' | 'horizontal';
  };
  colors: { low: string; medium: string; high: string; full: string };

  constructor(
    attributes = {},
    options = {},
    config: LiquidTankConfig = {
      barSetting: {
        direction: 'vertical',
      },
      colors: { low: '#ffa500', medium: '#ffff00', high: '#008000', full: '#ff0000' },
      level: 0,
    },
  ) {
    super(attributes, options);
    this.barSettings = {
      direction: config.barSetting.direction ?? 'vertical',
    };
    this.colors = {
      low: config.colors?.low || '#ffa500',
      medium: config.colors?.medium || '#ffff00',
      high: config.colors?.high || '#008000',
      full: config.colors?.full || '#ff0000',
    };
    this.level = config.level;
    this.startProgress();
  }

  defaults(): Partial<dia.Element.Attributes> {
    return {
      ...super.defaults,
      type: 'LiquidTank',
      size: {
        width: 160,
        height: 300,
      },
      level: 0,
      attrs: {
        root: {
          magnetSelector: 'body',
        },
        legs: {
          fill: 'none',
          stroke: '#350100',
          strokeWidth: 8,
          strokeLinecap: 'round',
          d: 'M 20 calc(h) l -5 10 M calc(w - 20) calc(h) l 5 10',
        },
        body: {
          stroke: 'gray',
          strokeWidth: 4,
          x: 0,
          y: 0,
          width: 'calc(w)',
          height: 'calc(h)',
          rx: 120,
          ry: 10,
          fill: {
            type: 'linearGradient',
            stops: [
              { offset: '0%', color: 'gray' },
              { offset: '30%', color: 'white' },
              { offset: '70%', color: 'white' },
              { offset: '100%', color: 'gray' },
            ],
          },
        },
        border: {
          x: 'calc(w / 4)',
          y: 10,
          width: 'calc(w / 2)',
          height: 'calc(h - 20)',
          stroke: '#350100',
          strokeWidth: 2,
          rx: 10,
          ry: 10,
          fill: 'none',
        },
        fill: {
          x: 'calc(w / 4)',
          y: 10,
          width: 'calc(w / 2)',
          height: 'calc(h - 20)',
          fill: 'orange',
          clipPath: 'inset(100% 0 0 0)',
          rx: 10,
          ry: 10,
        },
        label: {
          //   text: 'Tank 1',
          textAnchor: 'middle',
          textVerticalAnchor: 'top',
          x: 'calc(w / 2)',
          y: 'calc(h + 10)',
          fontSize: 14,
          fontFamily: 'sans-serif',
          fill: '#350100',
        },
        levelLabel: {
          text: '0',
          textAnchor: 'middle',
          x: 'calc(w / 2)',
          y: 'calc(h / 2)',
          fontSize: 20,
          fontFamily: 'sans-serif',
          fill: '#000000',
        },
      },
    };
  }

  preinitialize() {
    this.markup = util.svg/* xml */ `
      <path @selector="legs"/>
      <rect @selector="body"/>
      <rect @selector="border"/>
      <rect @selector="fill"/>
      <text @selector="label" />
      <text @selector="levelLabel" />
    `;
  }

  get level() {
    return this.get('level') || 0;
  }

  set level(level) {
    const newLevel = Math.max(0, Math.min(100, level));
    this.set('level', newLevel);

    const clipPathInset = 100 - newLevel;
    if (this.barSettings.direction === 'horizontal') {
      this.attr('fill/clipPath', `inset(0 ${clipPathInset}% 0 0)`);
    } else {
      this.attr('fill/clipPath', `inset(${clipPathInset}% 0 0 0)`);
    }

    let fillColor = this.colors.low;
    if (newLevel > 20 && newLevel <= 40) {
      fillColor = this.colors.medium;
    } else if (newLevel > 40 && newLevel <= 80) {
      fillColor = this.colors.high;
    } else if (newLevel > 80) {
      fillColor = this.colors.full;
    }
    this.attr('fill/fill', fillColor);
    this.attr('levelLabel/text', `${newLevel}`);
  }

  startProgress(random = true) {
    if (this.interval) {
      clearInterval(this.interval);
    }
    if (random) {
      this.interval = setInterval(() => {
        const randomLevel = Math.floor(Math.random() * 101);
        this.level = randomLevel;
      }, 1000);
    }
  }

  stopProgress() {
    if (this.interval) {
      clearInterval(this.interval);
      this.interval = null;
    }
  }

  updateColors(newColors: { low?: string; medium?: string; high?: string; full?: string }) {
    this.colors = {
      ...this.colors,
      ...newColors,
    };
    this.level = this.level;
  }

  updateDirection(direction: 'horizontal' | 'vertical') {
    this.barSettings = {
      direction,
    };
    if (direction === 'horizontal') {
      // Adjust border and fill for horizontal layout
      this.attr('border/width', 'calc(w - 40)'); // Full width minus padding
      this.attr('border/height', 'calc(h / 4)'); // Reduced height for horizontal layout
      this.attr('border/x', 20); // Add padding to the left
      this.attr('border/y', 'calc(h / 2)'); // Vertically center the border

      this.attr('fill/width', 'calc(w - 40)'); // Match the border width
      this.attr('fill/height', 'calc(h / 4)'); // Match the border height
      this.attr('fill/x', 20); // Align with border's x position
      this.attr('fill/y', 'calc(h / 2)'); // Align with border's y position
    } else if (direction === 'vertical') {
      // Adjust border and fill for vertical layout
      this.attr('border/width', 'calc(w / 2)'); // Reduced width for vertical layout
      this.attr('border/height', 'calc(h - 20)'); // Full height minus padding
      this.attr('border/x', 'calc(w / 4)'); // Center horizontally
      this.attr('border/y', 10); // Align top with padding

      this.attr('fill/width', 'calc(w / 2)'); // Match the border width
      this.attr('fill/height', 'calc(h - 20)'); // Match the border height
      this.attr('fill/x', 'calc(w / 4)'); // Align with border's x position
      this.attr('fill/y', 10); // Align with border's y position
    }

    // Reapply the current level to reflect the changes
    this.level = this.level;
  }
}
