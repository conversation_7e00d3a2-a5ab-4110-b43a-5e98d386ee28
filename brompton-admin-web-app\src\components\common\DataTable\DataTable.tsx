import AddIcon from '@mui/icons-material/Add';
import { Box, Button, Drawer, Typography } from '@mui/material';
import {
  DataGrid,
  GridColDef,
  GridRowClassNameParams,
  GridToolbarQuickFilter,
  GridValidRowModel,
} from '@mui/x-data-grid';
import { Dispatch, SetStateAction, useState } from 'react';
import CustomNoRowsOverlay from '../NoRowsOverlay';
import { hexToRgbA } from '~/utils/utils';
import AddAlertDialoge from '../AddAlertDialoge/AddAlertDialoge';
import { useHasAdminAccess } from '~/hooks/useHasAdminAccess';
import { useHasPowerUserAccess } from '~/hooks/useHasPowerUserAccess';

type DataTableProps<R extends GridValidRowModel = any> = {
  columns: readonly GridColDef<R>[];
  data: readonly any[];
  filterOptions: React.ReactNode;
  filteredData?: React.ReactNode;
  getRowClassName?: (params: GridRowClassNameParams<R>) => string;
  showAddButton?: boolean;
};
const DataTable = ({
  columns,
  data,
  filterOptions,
  filteredData,
  getRowClassName,
  showAddButton,
}: DataTableProps) => {
  const [showFilter, setShowFilter] = useState(false);
  const [openAlertModal, setOpenAlertModal] = useState(false);
  const { globalAdmin, admin } = useHasAdminAccess();
  const hasPowerUserAccess = useHasPowerUserAccess();

  const openAlert = () => {
    setOpenAlertModal(true);
  };

  const closeAlert = () => {
    setOpenAlertModal(false);
  };

  function QuickSearchToolbar({
    setShowFilter,
    filteredData,
  }: {
    setShowFilter: Dispatch<SetStateAction<boolean>>;
    filteredData?: React.ReactNode;
  }) {
    return (
      <Box sx={{ mb: 0.5 }}>
        <Box
          sx={{
            p: 0.5,
            pb: 0,
            display: 'flex',
            gap: 2,
            mb: 2,
          }}
        >
          <Button
            variant="outlined"
            startIcon={<AddIcon />}
            sx={{
              height: 50,
            }}
            onClick={() => {
              setShowFilter(true);
            }}
          >
            Filter
          </Button>
          <GridToolbarQuickFilter
            variant="outlined"
            InputProps={{
              sx: {
                height: 50,
                width: 320,
                borderRadius: '10px',
                '& .MuiInputBase-input': {
                  px: 1,
                  py: 1.5,
                },
              },
            }}
          />
          {showAddButton && (globalAdmin || admin || hasPowerUserAccess) && (
            <>
              <Button
                variant="outlined"
                sx={{
                  height: 50,
                }}
                onClick={openAlert}
              >
                Add New
              </Button>
            </>
          )}
        </Box>
        {filteredData ? filteredData : null}
      </Box>
    );
  }
  return (
    <Box sx={{ height: 'calc(100vh - 75px)' }}>
      <AddAlertDialoge open={openAlertModal} close={closeAlert} />
      <DataGrid
        getRowClassName={getRowClassName}
        sx={{
          width: '100%',
          '& .MuiDataGrid-row.exceeded': {
            background: (theme) => `rgba(${hexToRgbA(theme.palette.warning.main)}, 0.2)`,
          },
          '& .MuiDataGrid-columnHeader': {
            background: '#F9FAFB',
          },
          '.MuiDataGrid-columnHeader': {
            width: 'fit-content !important',
            maxWidth: 'fit-content !important',
          },
          '.MuiDataGrid-overlayWrapper': {
            height: '270px',
          },
          '--DataGrid-overlayHeight': '270px',
        }}
        rows={data}
        autoPageSize
        slotProps={{
          toolbar: {
            showQuickFilter: true,
            printOptions: { disableToolbarButton: true },
            csvOptions: { disableToolbarButton: true },
          },
        }}
        slots={{
          toolbar: () => (
            <QuickSearchToolbar
              setShowFilter={setShowFilter}
              filteredData={filteredData ?? undefined}
            />
          ),
          noRowsOverlay: CustomNoRowsOverlay,
        }}
        density="comfortable"
        ignoreDiacritics
        columns={columns}
        disableRowSelectionOnClick
      />
      <Drawer
        anchor="right"
        open={showFilter}
        onClose={() => setShowFilter(false)}
        PaperProps={{
          sx: {
            width: '30%',
            padding: 2,
          },
        }}
      >
        <Typography variant="h4">Filter</Typography>
        <Typography variant="h6">Apply filters to table data</Typography>
        <Box width={'100%'}>{filterOptions}</Box>
      </Drawer>
    </Box>
  );
};

export default DataTable;
