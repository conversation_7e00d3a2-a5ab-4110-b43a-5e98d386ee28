// apiService.ts
import { createApi } from '@reduxjs/toolkit/query/react';
import { LoginFormData } from '~/security/components/SignInForm';
import { customBaseQuery } from '~/utils/customBaseQuery';

export const authApi = createApi({
  baseQuery: customBaseQuery,
  reducerPath: 'authApi',
  // refetchOnMountOrArgChange: true,
  tagTypes: ['me', 'auth', 'User'],
  endpoints: (builder) => ({
    loginUser: builder.mutation<{ access_token: string; csrf_token: string }, LoginFormData>({
      query: (credentials) => ({
        url: '/v0/sessions',
        method: 'POST',
        body: credentials,
      }),
      invalidatesTags: ['me', 'auth', 'User'],
    }),
    logoutUser: builder.mutation<void, void>({
      query: () => ({
        url: '/v0/sessions/invalidate',
        method: 'POST',
      }),
      invalidatesTags: ['me', 'auth', 'User', { type: 'User' }],
    }),
    forgotPassowrd: builder.mutation<void, { user: string }>({
      query: ({ user }) => ({
        url: '/v0/forgot/email-username',
        method: 'POST',
        body: {
          user,
        },
      }),
    }),
    validateUrl: builder.query<void, { url: string }>({
      query: ({ url }) => ({
        url: `/v0/forgot/${url}`,
      }),
    }),
    resetForgottenPassword: builder.mutation<
      void,
      { token: string; password: string; reset_password: string }
    >({
      query: ({ token, password, reset_password }) => ({
        url: '/v0/forgot/reset',
        method: 'POST',
        body: {
          token,
          password,
          reset_password,
        },
      }),
    }),
  }),
});

export const {
  useLoginUserMutation,
  useLogoutUserMutation,
  useForgotPassowrdMutation,
  useValidateUrlQuery,
  useResetForgottenPasswordMutation,
} = authApi;
