import {
  Box,
  Checkbox,
  FormControl,
  FormControlLabel,
  FormGroup,
  MenuItem,
  OutlinedInput,
  Select,
  SelectChangeEvent,
  TextField,
} from '@mui/material';
import { fontWeights } from '~/utils/utils';

type TitleSettingsProps = {
  title: {
    value: string;
    isVisible: boolean;
    color: string;
  };
  fontSize: number;
  fontWeight?: string;
  handleTitleChange: (event: React.ChangeEvent<HTMLInputElement>) => void;
  handleFontSize: (event: React.ChangeEvent<HTMLInputElement>) => void;
  handleFontWeight?: (event: SelectChangeEvent<string>, child: React.ReactNode) => void;
  handleColorChange: (event: React.ChangeEvent<HTMLInputElement>) => void;
};
const TitleSettings = ({
  title,
  fontSize,
  fontWeight,
  handleFontSize,
  handleTitleChange,
  handleFontWeight,
  handleColorChange,
}: TitleSettingsProps) => {
  return (
    <>
      <FormControlLabel
        control={
          <Checkbox
            sx={{ p: 2 }}
            checked={title.isVisible}
            onChange={handleTitleChange}
            name="isVisible"
          />
        }
        label="Show Title"
      />
      {title.isVisible ? (
        <>
          <FormGroup sx={{ p: 2, pb: 0, pt: 0 }}>
            <TextField
              name="value"
              onChange={handleTitleChange}
              value={title.value}
              label="Title"
              variant="outlined"
              margin="normal"
              fullWidth
            />
          </FormGroup>
          <FormGroup sx={{ p: 2, pt: 0, pb: 0 }}>
            <TextField
              name="fontSize"
              type="number"
              onChange={handleFontSize}
              defaultValue={12}
              value={fontSize}
              label="Font Size"
              variant="outlined"
              margin="normal"
              fullWidth
            />
          </FormGroup>
          <FormGroup sx={{ p: 2, pt: 0, pb: 0 }}>
            <TextField
              label="Font Color"
              name="color"
              type="color"
              onChange={handleColorChange}
              value={title.color}
              variant="outlined"
              margin="normal"
              fullWidth
            />
          </FormGroup>
          {handleFontWeight !== undefined ? (
            <FormControl sx={{ mb: 2, p: 2, pb: 0, width: '100%' }}>
              <Box display="flex" alignItems="center" width="100%" gap={1}>
                <Select
                  label="Font Weight"
                  labelId="y-select-lable"
                  id="tabel-series"
                  defaultValue="bolder"
                  input={
                    <OutlinedInput
                      label="Font Weight"
                      sx={{
                        p: 0.2,
                        '& legend': {
                          maxWidth: '100%',
                          height: 'fit-content',
                          '& span': {
                            opacity: 1,
                          },
                        },
                      }}
                    />
                  }
                  fullWidth
                  value={fontWeight}
                  onChange={handleFontWeight}
                >
                  {fontWeights.map((fonts: string) => {
                    return (
                      <MenuItem key={fonts} value={fonts}>
                        {fonts}
                      </MenuItem>
                    );
                  })}
                </Select>
              </Box>
            </FormControl>
          ) : null}
        </>
      ) : null}
    </>
  );
};

export default TitleSettings;
