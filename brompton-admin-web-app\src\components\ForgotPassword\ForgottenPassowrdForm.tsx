import { yupResolver } from '@hookform/resolvers/yup';
import { Alert, Box, Button, TextField, Typography } from '@mui/material';
import { useRouter } from 'next/router';
import { useEffect, useState } from 'react';
import { SubmitHandler, useForm } from 'react-hook-form';
import { useResetForgottenPasswordMutation, useValidateUrlQuery } from '~/redux/api/authApi';
import { forgottenPaswordSchema } from '~/types/users';
import Loader from '../common/Loader';
import Link from 'next/link';
import { AlertMessage } from '~/shared/forms/types';
import { CustomError } from '~/errors/CustomerErrorResponse';

// Define form input types based on the schema
interface ResetPasswordInputs {
  newPassword: string;
  confirmNewPassword: string;
}

const ResetForgottenPasswordForm = () => {
  const router = useRouter();
  const { token } = router.query;
  const {
    register,
    handleSubmit,
    reset,
    formState: { errors },
  } = useForm<ResetPasswordInputs>({
    resolver: yupResolver(forgottenPaswordSchema),
  });
  const [alertMessage, setAlertMessage] = useState<AlertMessage | undefined>(undefined);
  const [
    ResetForgottenPassowrd,
    {
      isError: isResettingError,
      isLoading: isResetting,
      isSuccess: isResetSuccess,
      error: resetError,
    },
  ] = useResetForgottenPasswordMutation();

  useEffect(() => {
    if (isResettingError && resetError) {
      const err = resetError as CustomError;
      setAlertMessage({
        message: err.message ?? 'Password reset failed',
        severity: 'error',
      });
    }
    if (isResetSuccess) {
      reset();
      setAlertMessage({
        message: 'Password set successfully. Go to Login page for further access.',
        severity: 'success',
      });
      const timer = setTimeout(() => {
        router.push('/login');
      }, 2000);
      return () => clearTimeout(timer);
    }
  }, [isResettingError, resetError, isResetSuccess, reset, router]);

  const { isError, error, isFetching, isSuccess } = useValidateUrlQuery(
    {
      url: token as string,
    },
    {
      skip: !token,
      refetchOnMountOrArgChange: true,
    },
  );

  const onSubmit: SubmitHandler<ResetPasswordInputs> = (data) => {
    ResetForgottenPassowrd({
      password: data.newPassword,
      reset_password: data.confirmNewPassword,
      token: token as string,
    });
  };

  return (
    <Box
      component="form"
      onSubmit={handleSubmit(onSubmit)}
      sx={{
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        gap: 2,
        width: '100%',
        maxWidth: 400,
        margin: '0 auto',
      }}
    >
      {isFetching ? (
        <Loader />
      ) : (
        <>
          {isError && error && (
            <>
              <Typography color="red" variant="h6">
                Reset password link is expired.
              </Typography>
              <Link href={'/login'}>Back To Login Page</Link>
            </>
          )}
          {isSuccess ? (
            <>
              <Typography variant="h5" component="h1" gutterBottom>
                Set Password
              </Typography>

              <TextField
                label="New Password"
                type="password"
                variant="outlined"
                fullWidth
                error={!!errors.newPassword}
                helperText={errors.newPassword?.message}
                {...register('newPassword')}
              />

              <TextField
                label="Confirm New Password"
                type="password"
                variant="outlined"
                fullWidth
                error={!!errors.confirmNewPassword}
                helperText={errors.confirmNewPassword?.message}
                {...register('confirmNewPassword')}
              />

              <Button
                type="submit"
                variant="contained"
                color="primary"
                fullWidth
                disabled={isResetting}
              >
                Reset Password
              </Button>
              {alertMessage && (
                <Alert severity={alertMessage.severity} sx={{ mt: 3, mb: 3 }}>
                  <Typography>{alertMessage.message}</Typography>
                </Alert>
              )}
            </>
          ) : null}
        </>
      )}
    </Box>
  );
};

export default ResetForgottenPasswordForm;
