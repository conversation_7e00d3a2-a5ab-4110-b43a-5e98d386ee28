import { ThunkDispatch } from '@reduxjs/toolkit';
import { dashboardTemplateApi } from '~/redux/api/dashboardTemplate';
import { RootState } from '~/redux/store';
import { DashboardState } from '~/types/dashboard';
import { DashboardTemplate } from '~/types/dashboardTemplate';
import { elementVariable } from '~/types/diagram';
import { DashboardWidget, ImageTextDetails, Widget } from '~/types/widgets';
import { formatMetricLabel } from '~/utils/utils';

/**
 * Transforms a dashboard template by mapping metrics to measurements for different widget types
 *
 * @param templateData The dashboard template data
 * @param assetMeasurementsListMap Map of metric IDs to measurements
 * @param settings The dashboard widget settings
 * @returns Object containing transformed widgets and metric measurements
 */
export const transformTemplate = async (
  templateData: DashboardTemplate | null,
  assetMeasurementsListMap: Map<number, { id: number; tag: string }[]>,
  settings: { assetOption: { id: number; label: string } },
) => {
  if (!templateData || !templateData.data) {
    return { widgets: [], metricMeasurements: {} };
  }

  const templateDetailsData = JSON.parse(templateData.data) as {
    widget: DashboardState['widget'];
    topPanel: DashboardState['template']['topPanel'];
    chart: DashboardState['template']['chart'];
  };

  const metricsToShow: number[] = [];
  const metricMeasurements: Record<string, { metricName: string; measurement: string }> = {};

  const widgets = await Promise.all(
    templateDetailsData.widget.widgets.map(async (widget) => {
      switch (widget.type) {
        case 'dashboard-widget': {
          // This is a mock implementation for testing
          // In a real implementation, you would dispatch an API call
          const dispatch = (action: any) => action;
          const { data: templateDataInner, isError } = await dispatch(
            dashboardTemplateApi.endpoints.getDashboardTemplateDetails.initiate(
              widget.settings.dashboardTemplateOption.id,
            ),
          );

          if (!templateDataInner || isError || !templateDataInner.data) {
            return widget;
          }

          const { metricMeasurements: metricMeasurementsInner, widgets } = await transformTemplate(
            templateDataInner,
            assetMeasurementsListMap,
            settings,
          );

          const templateDetailsInnerData = JSON.parse(templateDataInner.data) as {
            widget: DashboardState['widget'];
            topPanel: DashboardState['template']['topPanel'];
            chart: DashboardState['template']['chart'];
          };

          widget.settings.mode = 'dashboard';
          widget.settings.dashboardTemplateData = {
            widgets,
            deleteWidgets: [],
            widgetLayout: templateDetailsInnerData.widget.widgetLayout,
            lastWidgetId: templateDetailsInnerData.widget.lastWidgetId,
          };

          const metricToMeasures: Record<
            string,
            {
              metricName: string;
              measurement: string;
            }
          > = metricMeasurementsInner;

          Object.keys(metricToMeasures).forEach((key) => {
            metricMeasurements[key] = metricToMeasures[key];
          });

          return widget;
        }
        case 'title':
        case 'Weather': {
          widget.settings.isChildWidget = true;
          widget.settings.assetOrAssetType = settings.assetOption.id;
          widget.settings.mode = 'dashboard';
          return widget;
        }
        case 'stats': {
          if (!metricsToShow.includes(Number(widget.settings.selectedDbMeasureId))) {
            metricsToShow.push(Number(widget.settings.selectedDbMeasureId));
          }

          const measures =
            assetMeasurementsListMap.get(Number(widget.settings.selectedDbMeasureId)) !==
              undefined &&
            assetMeasurementsListMap.get(Number(widget.settings.selectedDbMeasureId));

          if (measures) {
            widget.settings.selectedDbMeasureId = measures[0].id.toString();
            widget.settings.assetMeasure = {
              assetId: settings.assetOption.id.toString(),
              measureId: measures.map((measure) => measure.id.toString()),
            };
          } else {
            widget.settings.selectedDbMeasureId = '';
            widget.settings.assetMeasure = {
              assetId: settings.assetOption.id.toString(),
              measureId: [],
            };
          }

          widget.settings.mode = 'dashboard';
          widget.settings.isChildWidget = true;
          widget.settings.assetOrAssetType = settings.assetOption.id;

          metricMeasurements[widget.settings.selectedDbMeasureId] = {
            metricName: widget.settings.selectedDbMeasureId,
            measurement: measures ? measures[0].tag : 'N/A',
          };

          return widget;
        }
        case 'map': {
          widget.settings.isChildWidget = true;
          widget.settings.assetOrAssetType = settings.assetOption.id;
          widget.settings.mode = 'dashboard';

          widget.settings.markers = widget.settings.markers.map((marker) => {
            if (marker.selectedTitles) {
              marker.selectedTitles.forEach((title) => {
                if (!metricsToShow.includes(Number(title))) {
                  metricsToShow.push(Number(title));
                }
              });

              const measures = marker.selectedTitles.flatMap((title) => {
                const measureList = assetMeasurementsListMap.get(Number(title));
                return measureList
                  ? measureList.map((measure) => ({
                      assetId: settings.assetOption.id.toString(),
                      measureId: measure.id.toString(),
                    }))
                  : [];
              });

              marker.assetMeasures = measures.length > 0 ? measures : [];

              marker.labelAndUnits = marker.selectedTitles.reduce((acc, title) => {
                const measureList = assetMeasurementsListMap.get(Number(title));
                if (measureList) {
                  measureList.forEach((measure) => {
                    acc[measure.id.toString()] = {
                      label: measure.tag,
                      unit: marker.labelAndUnits[measure.id.toString()]?.unit || '',
                      value: '',
                    };
                  });
                }
                return acc;
              }, {} as Record<string, { label: string; unit: string; value: string }>);

              marker.selectedTitles = measures.map((measure) => measure.measureId);
            } else {
              marker.assetMeasures = [];
              marker.labelAndUnits = {};
            }

            return marker;
          });

          return widget;
        }
        // Add other widget type cases as needed for testing

        default:
          return widget;
      }
    }),
  );

  return {
    widgets: widgets as Widget[],
    metricMeasurements,
  };
};
