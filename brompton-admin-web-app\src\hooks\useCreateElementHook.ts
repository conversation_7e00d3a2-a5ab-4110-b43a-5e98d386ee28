import { dia, shapes as DiagramShapes } from '@joint/core';
import { MutableRefObject, useEffect, useRef, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import Battery from '~/components/CreateElement/Battery';
import { DraggableLabel } from '~/components/CreateElement/DraggableLabel';
import { getElementsVariables } from '~/redux/selectors/diagramSelector';
import { diagramSlice } from '~/redux/slices/diagramSlice';
import { useSnackbar } from '~/shared/snackbars/snackbar-hooks';
import { cellNamespace, shapeMap } from '~/types/diagram';

type createElementHookProps = {
  graph: dia.Graph<dia.Graph.Attributes, dia.ModelSetOptions>;
  hideIconTimeoutRef: MutableRefObject<NodeJS.Timeout | null>;
  resizeDotsRef: MutableRefObject<HTMLDivElement[]>;
  rotationDotsRef: MutableRefObject<HTMLDivElement[]>;
  paperRef: MutableRefObject<HTMLDivElement | null>;
};
export const useCreateElementHook = ({
  graph,
  hideIconTimeoutRef,
  resizeDotsRef,
  rotationDotsRef,
  paperRef,
}: createElementHookProps) => {
  const dispatch = useDispatch();
  const { addElementVariable, setElementsVariables, removeElementsVariables } =
    diagramSlice.actions;

  const [snackbarState, showSuccessAlert, showErrorAlert] = useSnackbar();
  const paperInstanceRef = useRef<dia.Paper | null>(null);
  const elementHideTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const [currentSelectedElement, setCurrentSelectedElement] = useState<dia.Element | null>(null);
  const [selectedElements, setSelectedElements] = useState<dia.Element[]>([]);
  const [batteryModalOpen, setBatteryModalOpen] = useState<boolean>(false);
  const [currentBattery, setCurrentBattery] = useState<Battery | null>(null);
  const [elementsVariables, setElementVariables] = useState<
    Record<
      string,
      {
        label: string;
        variable: string;
      }[]
    >
  >({});

  const handleResizeStart = (e: MouseEvent, element: dia.Element, index: number) => {
    e.preventDefault();
    const startX = e.clientX;
    const startY = e.clientY;
    const startSize = element.size();
    const startPos = element.position();

    const onMouseMove = (moveEvent: MouseEvent) => {
      const deltaX = moveEvent.clientX - startX;
      const deltaY = moveEvent.clientY - startY;
      let newWidth = startSize.width;
      let newHeight = startSize.height;
      const newPosition = { ...startPos };
      // Adjust based on dot index
      switch (index) {
        case 0: // Top-left corner
          newWidth = startSize.width - deltaX;
          newHeight = startSize.height - deltaY;
          newPosition.x = startPos.x + deltaX;
          newPosition.y = startPos.y + deltaY;
          break;

        case 1: // Top-center
          newHeight = startSize.height - deltaY;
          newPosition.y = startPos.y + deltaY;
          break;

        case 2: // Top-right corner
          newWidth = startSize.width + deltaX;
          newHeight = startSize.height - deltaY;
          newPosition.y = startPos.y + deltaY;
          break;

        case 3: // Right-center
          newWidth = startSize.width + deltaX;
          break;

        case 4: // Bottom-right corner
          newWidth = startSize.width + deltaX;
          newHeight = startSize.height + deltaY;
          break;

        case 5: // Bottom-center
          newHeight = startSize.height + deltaY;
          break;

        case 6: // Bottom-left corner
          newWidth = startSize.width - deltaX;
          newHeight = startSize.height + deltaY;
          newPosition.x = startPos.x + deltaX;
          break;

        case 7: // Left-center
          newWidth = startSize.width - deltaX;
          newPosition.x = startPos.x + deltaX;
          break;

        default:
          break;
      }

      // Apply minimum width/height constraints
      newWidth = Math.max(newWidth, 20);
      newHeight = Math.max(newHeight, 20);

      // Update element's size and position
      element.resize(newWidth, newHeight);
      element.position(newPosition.x, newPosition.y);
    };

    const onMouseUp = () => {
      document.removeEventListener('mousemove', onMouseMove);
      document.removeEventListener('mouseup', onMouseUp);
    };

    document.addEventListener('mousemove', onMouseMove);
    document.addEventListener('mouseup', onMouseUp);
  };

  const addResizeDots = (element: dia.Element): HTMLDivElement[] => {
    const dotSize = 8;
    const dotColor = '#0077b6';
    const bbox = element.getBBox();
    const scale = paperInstanceRef.current?.scale().sx || 1; // Get the current scale
    const translation = paperInstanceRef.current?.translate() || { tx: 0, ty: 0 }; // Get the canvas translation

    const positions = [
      {
        x: bbox.x * scale + translation.tx,
        y: bbox.y * scale + translation.ty,
        cursor: 'nw-resize',
      },
      {
        x: (bbox.x + bbox.width / 2) * scale + translation.tx,
        y: bbox.y * scale + translation.ty,
        cursor: 'n-resize',
      },
      {
        x: (bbox.x + bbox.width) * scale + translation.tx,
        y: bbox.y * scale + translation.ty,
        cursor: 'ne-resize',
      },
      {
        x: (bbox.x + bbox.width) * scale + translation.tx,
        y: (bbox.y + bbox.height / 2) * scale + translation.ty,
        cursor: 'e-resize',
      },
      {
        x: (bbox.x + bbox.width) * scale + translation.tx,
        y: (bbox.y + bbox.height) * scale + translation.ty,
        cursor: 'se-resize',
      },
      {
        x: (bbox.x + bbox.width / 2) * scale + translation.tx,
        y: (bbox.y + bbox.height) * scale + translation.ty,
        cursor: 's-resize',
      },
      {
        x: bbox.x * scale + translation.tx,
        y: (bbox.y + bbox.height) * scale + translation.ty,
        cursor: 'sw-resize',
      },
      {
        x: bbox.x * scale + translation.tx,
        y: (bbox.y + bbox.height / 2) * scale + translation.ty,
        cursor: 'w-resize',
      },
    ];

    return positions.map((pos, index) => {
      const dot = document.createElement('div');
      dot.style.position = 'absolute';
      dot.style.width = `${dotSize}px`;
      dot.style.height = `${dotSize}px`;
      dot.style.backgroundColor = dotColor;
      dot.style.borderRadius = '50%';
      dot.style.cursor = pos.cursor;
      dot.style.left = `${pos.x}px`;
      dot.style.top = `${pos.y}px`;
      dot.style.boxShadow = '0 0 3px rgba(0,0,0,0.3)';
      dot.style.zIndex = '1000';

      dot.onmousedown = (e) => handleResizeStart(e, element, index);

      paperRef.current?.appendChild(dot);

      return dot;
    });
  };

  const handleRotationStart = (e: MouseEvent, element: dia.Element, handlerId: string) => {
    e.preventDefault();

    // Set grabbing cursor on drag start
    document.body.style.cursor = 'grabbing';

    // Get the center coordinates of the element for rotation calculations
    const centerX = element.position().x + element.size().width / 2;
    const centerY = element.position().y + element.size().height / 2;

    // Store the starting mouse position and initial rotation angle
    const initialAngle = element.get('angle') || 0;
    const startMousePos = { x: e.clientX, y: e.clientY };
    let previousAngle = initialAngle;

    // Helper functions similar to the provided code
    function getAngle(
      a: { x: number; y: number },
      b: { x: number; y: number },
      c: { x: number; y: number },
    ) {
      const AB = Math.sqrt(Math.pow(b.x - a.x, 2) + Math.pow(b.y - a.y, 2));
      const BC = Math.sqrt(Math.pow(b.x - c.x, 2) + Math.pow(b.y - c.y, 2));
      const AC = Math.sqrt(Math.pow(c.x - a.x, 2) + Math.pow(c.y - a.y, 2));
      return Math.acos((BC * BC + AB * AB - AC * AC) / (2 * BC * AB));
    }

    function ccw(
      a: { x: number; y: number },
      b: { x: number; y: number },
      c: { x: number; y: number },
    ) {
      const area2 = (b.x - a.x) * (c.y - a.y) - (b.y - a.y) * (c.x - a.x);
      if (area2 < 0) return -1;
      if (area2 > 0) return +1;
      return 0;
    }

    const onMouseMove = (moveEvent: MouseEvent) => {
      const currentMousePos = { x: moveEvent.clientX, y: moveEvent.clientY };
      const direction = ccw(startMousePos, { x: centerX, y: centerY }, currentMousePos);
      const angle = getAngle(startMousePos, { x: centerX, y: centerY }, currentMousePos);

      // Calculate new rotation angle based on direction
      const rotationAngle = direction === -1 ? previousAngle - angle : previousAngle + angle;

      // Apply the rotation to the element
      element.rotate(rotationAngle, true);

      // Update the previous angle for smooth incremental rotation
      previousAngle = rotationAngle;
    };

    const onMouseUp = () => {
      document.body.style.cursor = ''; // Reset cursor
      document.removeEventListener('mousemove', onMouseMove);
      document.removeEventListener('mouseup', onMouseUp);
    };

    document.addEventListener('mousemove', onMouseMove);
    document.addEventListener('mouseup', onMouseUp);
  };

  const addRotationDots = (element: dia.Element): HTMLDivElement[] => {
    const dotSize = 8;
    const rotationDotColor = '#ff8c00';
    const bbox = element.getBBox();
    const scale = paperInstanceRef.current?.scale().sx || 1; // Get current scale
    const translation = paperInstanceRef.current?.translate() || { tx: 0, ty: 0 }; // Get canvas translation

    const rotationPositions = [
      {
        x: (bbox.x + bbox.width / 2) * scale + translation.tx,
        y: (bbox.y - 20) * scale + translation.ty,
        cursor: 'grab',
        id: 'top',
      },
      {
        x: (bbox.x + bbox.width + 20) * scale + translation.tx,
        y: (bbox.y + bbox.height / 2) * scale + translation.ty,
        cursor: 'grab',
        id: 'right',
      },
      {
        x: (bbox.x + bbox.width / 2) * scale + translation.tx,
        y: (bbox.y + bbox.height + 20) * scale + translation.ty,
        cursor: 'grab',
        id: 'bottom',
      },
      {
        x: (bbox.x - 20) * scale + translation.tx,
        y: (bbox.y + bbox.height / 2) * scale + translation.ty,
        cursor: 'grab',
        id: 'left',
      },
    ];

    return rotationPositions.map((pos) => {
      const dot = document.createElement('div');
      dot.style.position = 'absolute';
      dot.style.width = `${dotSize}px`;
      dot.style.height = `${dotSize}px`;
      dot.style.backgroundColor = rotationDotColor;
      dot.style.borderRadius = '50%';
      dot.style.cursor = pos.cursor;
      dot.style.left = `${pos.x}px`;
      dot.style.top = `${pos.y}px`;
      dot.style.boxShadow = '0 0 3px rgba(0,0,0,0.3)';
      dot.style.zIndex = '1000';

      dot.onmousedown = (e: MouseEvent) => handleRotationStart(e, element, pos.id);

      paperRef.current?.appendChild(dot);
      return dot;
    });
  };

  const clearHandlers = () => {
    resizeDotsRef.current.forEach((dot) => dot.remove());
    resizeDotsRef.current = [];

    rotationDotsRef.current.forEach((dot) => dot.remove());
    rotationDotsRef.current = [];

    dispatch(diagramSlice.actions.setIconVisible(false));
  };

  useEffect(() => {
    const paper = new dia.Paper({
      el: paperRef.current as HTMLDivElement,
      model: graph,
      width: '100%',
      height: '85vh',
      gridSize: 12,
      drawGrid: true,
      overflow: false,
      cellNamespace,
    });

    paperInstanceRef.current = paper;

    paper.on('element:mouseenter', (elementView: dia.ElementView) => {
      const element = elementView.model;
      resizeDotsRef.current.forEach((dot) => dot.remove());
      resizeDotsRef.current = addResizeDots(element);

      rotationDotsRef.current.forEach((dot) => dot.remove());
      rotationDotsRef.current = addRotationDots(element);

      // dispatch(setSelectedElement(element as dia.Element));
      clearTimeout(hideIconTimeoutRef.current!);
      dispatch(diagramSlice.actions.setIconVisible(true));
    });

    paper.on('element:mouseenter', (elementView) => {
      const element = elementView.model;
      if (resizeDotsRef.current.length > 0) {
        resizeDotsRef.current.forEach((dot) => dot.remove());
        resizeDotsRef.current = []; // Clear the ref after removal
      }
      resizeDotsRef.current = addResizeDots(element);
      // dispatch(setSelectedElement(element as dia.Element));
      // dispatch(diagramSlice.actions.setElementAttrs(element.attr().body));
      clearTimeout(hideIconTimeoutRef.current!); // Cancel any hide timeout
      dispatch(diagramSlice.actions.setIconVisible(true)); // Show the icon
    });

    paper.on('element:mouseleave', () => {
      if (elementHideTimeoutRef.current) clearTimeout(elementHideTimeoutRef.current);
      elementHideTimeoutRef.current = setTimeout(() => {
        dispatch(diagramSlice.actions.setIconVisible(false));
        clearHandlers();
        resizeDotsRef.current.forEach((dot) => dot.remove());
        resizeDotsRef.current = [];
        rotationDotsRef.current.forEach((dot) => dot.remove());
        rotationDotsRef.current = [];
        elementHideTimeoutRef.current = null;
      }, 1000);
    });

    paper.on('cell:pointerdown', (cellView: dia.CellView, event: dia.Event) => {
      const cell = cellView.model;
      setCurrentSelectedElement(cell as dia.Element);
      if (cell.isElement()) {
        if (event.shiftKey) {
          setSelectedElements((prev) =>
            prev.includes(cell as dia.Element) ? prev : [...prev, cell as dia.Element],
          );
        } else {
          setSelectedElements([cell as dia.Element]);
        }
      }
    });

    paper.on('blank:pointerdown', () => {
      setSelectedElements([]);
      setCurrentSelectedElement(null);
    });

    paper.on('element:pointerclick', (elementView) => {
      const model = elementView.model;
      if (model instanceof Battery) {
        setCurrentBattery(model);
        setBatteryModalOpen(true);
      }
    });
  }, [graph]);

  useEffect(() => {
    if (graph) {
      graph.getElements().forEach((element) => {
        if (element.get('type') === 'selectionHighlight') {
          element.remove();
        }
      });
      selectedElements.forEach((element) => {
        const bbox = element.getBBox();
        const highlight = new DiagramShapes.standard.Rectangle();
        highlight.position(bbox.x - 5, bbox.y - 5);
        highlight.resize(bbox.width + 10, bbox.height + 10);
        highlight.attr({
          body: {
            fill: 'rgba(0, 0, 255, 0.1)',
            stroke: '#0000ff',
            strokeWidth: 1,
          },
        });
        highlight.set('type', 'selectionHighlight');
        highlight.addTo(graph);
      });
    }
  }, [selectedElements, graph]);

  const onDragStart = (event: React.DragEvent<HTMLDivElement>, shapeType: string) => {
    event.dataTransfer.setData('shape', shapeType);
  };

  const onDrop = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    const shapeType = event.dataTransfer.getData('shape');
    if (graph && shapeType) {
      const canvasRect = paperRef.current!.getBoundingClientRect();
      const position = {
        x: event.clientX - canvasRect.left,
        y: event.clientY - canvasRect.top,
      };
      const createShape = shapeMap[shapeType];
      if (createShape) {
        const newShape = createShape(shapeType);
        newShape.attr({
          body: {
            name: shapeType,
          },
        });
        newShape.position(position.x, position.y);
        newShape.addTo(graph);
      }
    }
  };

  const onDragOver = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
  };

  const deleteSelectedElements = () => {
    if (selectedElements.length === 0) {
      showErrorAlert('No elements selected to delete.');
      return;
    }

    graph.removeCells(selectedElements); // Delete the selected elements from the graph
    setSelectedElements([]); // Clear selection
    setCurrentSelectedElement(null); // Clear current selected element
    showSuccessAlert('Selected elements deleted.');
  };

  const addNewVariableToElement = () => {
    if (!currentSelectedElement) {
      showErrorAlert('No element selected to add a variable.');
      return;
    }

    const elementId = currentSelectedElement.id as string;

    // Update local state
    setElementVariables((prev) => ({
      ...prev,
      [elementId]: [
        ...(prev[elementId] || []),
        {
          label: '', // Optional title
          variable: '', // Initial variable value
        },
      ],
    }));

    // Dispatch Redux action
    dispatch(
      addElementVariable({
        key: elementId,
        value: { label: '', variable: '' },
      }),
    );
  };

  const debounce = (func: (...args: any[]) => void, delay: number) => {
    let timer: NodeJS.Timeout;
    return (...args: any[]) => {
      clearTimeout(timer);
      timer = setTimeout(() => func(...args), delay);
    };
  };

  const handleVariableChange = (
    element: dia.Element,
    index: number,
    field: 'label' | 'variable',
    value: string,
  ) => {
    const elementId = element.id as string;

    // Update local state
    setElementVariables((prev) => {
      const updatedVariables = prev[elementId]?.map((variable, i) =>
        i === index ? { ...variable, [field]: value } : variable,
      );

      return {
        ...prev,
        [elementId]: updatedVariables,
      };
    });

    // Debounced function for label updates
    const updateCanvas = debounce((updatedTitle: string) => {
      const existingLabel = graph
        .getElements()
        .find((cell) => cell.get('parentId') === element.id && cell.get('variableIndex') === index);

      if (existingLabel) {
        // Update existing label text and variable
        existingLabel.attr('label/text', updatedTitle); // Update text on canvas
        existingLabel.set('relatedVariable', elementsVariables[element.id]?.[index]?.variable); // Set related variable
      } else if (updatedTitle) {
        // Create a new label only if it doesn't exist
        const parentPosition = element.position();
        const labelPosition = {
          x: parentPosition.x + 50,
          y: parentPosition.y + 50 + index * 40,
        };

        const draggableLabel = new DraggableLabel(updatedTitle, labelPosition.x, labelPosition.y);
        draggableLabel.set('parentId', element.id); // Associate label with the parent element
        draggableLabel.set('variableIndex', index); // Store the index for identification
        draggableLabel.set('relatedVariable', elementsVariables[element.id]?.[index]?.variable); // Store related variable
        graph.addCell(draggableLabel);
      }
    }, 500);

    // Update Redux store
    const updatedVars =
      elementsVariables[elementId]?.map((variable, i) =>
        i === index ? { ...variable, [field]: value } : variable,
      ) || [];

    dispatch(
      setElementsVariables({
        key: elementId,
        value: updatedVars,
      }),
    );

    // Trigger label update
    const updatedVariables = elementsVariables[elementId]?.[index];
    const updatedTitle = field === 'label' ? value : updatedVariables?.label || '';
    updateCanvas(updatedTitle);
  };

  const handleDeleteVariable = (element: dia.Element, index: number) => {
    if (!element) return;

    const elementId = element.id as string;

    // Update local state
    setElementVariables((prev) => ({
      ...prev,
      [elementId]: prev[elementId]?.filter((_, i) => i !== index),
    }));

    // Dispatch Redux action
    dispatch(
      removeElementsVariables({
        key: elementId,
        index: index,
      }),
    );
  };

  const ungroupSelectedElement = () => {
    if (!currentSelectedElement || currentSelectedElement.get('type') !== 'groupElement') {
      showErrorAlert('No group selected to ungroup.');
      return;
    }

    // Get embedded cells and unembed them
    const embeddedCells = currentSelectedElement.getEmbeddedCells();
    embeddedCells.forEach((cell) => {
      currentSelectedElement.unembed(cell);
    });

    // Remove the group element itself
    graph.removeCells([currentSelectedElement]);
    setSelectedElements([]);
    setCurrentSelectedElement(null);
    showSuccessAlert('Group has been ungrouped.');
  };

  return {
    currentSelectedElement,
    setCurrentSelectedElement,
    selectedElements,
    setSelectedElements,
    onDragStart,
    onDrop,
    onDragOver,
    deleteSelectedElements,
    addNewVariableToElement,
    handleVariableChange,
    handleDeleteVariable,
    elementsVariables,
    snackbarState,
    ungroupSelectedElement,
    setCurrentBattery,
    batteryModalOpen,
    setBatteryModalOpen,
    currentBattery,
  };
};
