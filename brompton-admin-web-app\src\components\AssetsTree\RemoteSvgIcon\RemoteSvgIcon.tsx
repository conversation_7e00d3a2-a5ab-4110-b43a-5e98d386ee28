import { Icon } from '@mui/material';
import React from 'react';
import Image from 'next/image';

const RemoteSvgIcon: React.FC<{ url: string }> = ({ url }) => (
  <Icon sx={{ display: 'flex', mr: 0.5, height: 'auto' }}>
    <Image src={url} height={22} width={22} alt="" />
  </Icon>
);

export const UserCircleIcon = React.memo(() => <RemoteSvgIcon url="/icons/user-circle.svg" />);
export const BuildingIcon = React.memo(() => <RemoteSvgIcon url="/icons/building.svg" />);
export const TechoMeterIcon = React.memo(() => (
  <RemoteSvgIcon url="/icons/tachometer-fast-alt.svg" />
));
export const AnalyticsIcon = React.memo(() => <RemoteSvgIcon url="/icons/analytics.svg" />);
