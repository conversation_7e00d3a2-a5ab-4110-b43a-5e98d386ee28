import EditIcon from '@mui/icons-material/Edit';
import { Box, IconButton, Tooltip, Typography } from '@mui/material';
import { DataGrid, GridColDef } from '@mui/x-data-grid';
import Link from 'next/link';
import { useHasAdminAccess } from '~/hooks/useHasAdminAccess';
import { useRolePermission } from '~/hooks/useRolePermission';
import {
  useDataTypesQuery,
  useGetCalculationEngineTemplatesQuery,
} from '~/redux/api/calculationEngine';
import Loader from '../common/Loader';

export const CalcEngineList = () => {
  const { globalAdmin, admin } = useHasAdminAccess();
  const { hasPermission } = useRolePermission();
  const { data: dataTypes } = useDataTypesQuery();
  const { data: templates, isFetching, isError } = useGetCalculationEngineTemplatesQuery();

  if (isFetching) return <Loader />;

  if (isError) {
    return (
      <Box pl={3}>
        <Typography variant="h6">Error fetching templates</Typography>
      </Box>
    );
  }

  const rows =
    templates?.items.map((template) => ({
      id: template.id,
      name: template.name,
      description: template.description,
      expression: template.expression,
      dataType: dataTypes?.items.find((dataType) => dataType.id === template.dataType)?.name ?? '',
    })) || [];

  const columns: GridColDef[] = [
    { field: 'name', headerName: 'Name', flex: 1 },
    { field: 'description', headerName: 'Description', flex: 2 },
    { field: 'expression', headerName: 'Expression', flex: 2 },
    { field: 'dataType', headerName: 'Data Types', flex: 1 },
  ];

  if (globalAdmin || admin) {
    columns.push({
      field: 'actions',
      headerName: 'Actions',
      flex: 1,
      renderCell: (params) =>
        hasPermission('calculation-engine.update') ? (
          <Tooltip title="Edit">
            <IconButton
              component={Link}
              href={`/expression-template/edit/${params.id}`}
              size="small"
              color="primary"
            >
              <EditIcon fontSize="small" />
            </IconButton>
          </Tooltip>
        ) : null,
      sortable: false,
      filterable: false,
    });
  }

  return (
    <Box sx={{ height: 'calc(100vh - 75px)', width: '100%' }}>
      <DataGrid
        rows={rows}
        columns={columns}
        autoPageSize
        density="comfortable"
        sx={{
          '& .MuiDataGrid-columnHeader': {
            background: '#F9FAFB',
          },
        }}
      />
    </Box>
  );
};
