import { yupResolver } from '@hookform/resolvers/yup';
import FileUploadIcon from '@mui/icons-material/FileUpload';
import {
  Alert,
  Box,
  Button,
  Checkbox,
  FormControlLabel,
  IconButton,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TextField,
  Tooltip,
  Typography,
} from '@mui/material';
import { Dispatch, SetStateAction, useEffect, useState } from 'react';
import { Controller, useForm } from 'react-hook-form';
import * as yup from 'yup';
import { useHasAdminAccess } from '~/hooks/useHasAdminAccess';
import { createAssetTemplateDTO } from '~/measurements/domain/types';
import { useCreateAssetTemplateMutation } from '~/redux/api/assetsApi';
import { AlertSnackbar } from '~/shared/snackbars/components/AlertSnackbar';
import { useSnackbar } from '~/shared/snackbars/snackbar-hooks';
import { AssetTypeOption } from '~/types/asset';
import CustomDialog from '../common/CustomDialog';

type AssetTemplateFileImporterProps = {
  open: boolean;
  setImportData: Dispatch<SetStateAction<boolean>>;
  assetTypesWithPath: AssetTypeOption[];
};

// Validation Schema
const schema = yup.object().shape({
  model_number: yup.string().required('Model Number is required'),
  manufacturer: yup.string().required('Manufacturer is required'),
  save_as_global_asset_template: yup.boolean(),
});

const AssetTemplateFileImporter = ({ open, setImportData }: AssetTemplateFileImporterProps) => {
  const { globalAdmin } = useHasAdminAccess();
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [isValidFile, setIsValidFile] = useState<boolean>(false);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const [measurements, setMeasurements] = useState<any[]>([]); // Store measurements
  const [snackbarState, showSuccessAlert, showErrorAlert] = useSnackbar();
  const [assetType, setAssetType] = useState<{ id: string | number; name: string } | null>(null);
  const [createAssetTemplate, { isError, isSuccess, data: createAssetTemplateData, error }] =
    useCreateAssetTemplateMutation();
  // React Hook Form
  const {
    control,
    handleSubmit,
    setValue,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(schema),
    defaultValues: {
      manufacturer: '',
      model_number: '',
      save_as_global_asset_template: false,
    },
  });

  useEffect(() => {
    if (isSuccess) {
      showSuccessAlert('Asset Template imported successfully!');
      setImportData(false); // Close the modal on success
      setSelectedFile(null);
      setIsValidFile(false);
      setErrorMessage(null);
      setMeasurements([]); // Reset measurements
      setAssetType(null);
    }
    if (isError) {
      const err = error as any;
      showErrorAlert(err?.data?.exception || 'Failed to create Asset Template.');
    }
  }, [isSuccess, isError, error]);
  // Handle File Upload
  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    setSelectedFile(file);
    setIsValidFile(false); // Reset form until file is validated
    setErrorMessage(null); // Clear previous errors

    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const jsonData = JSON.parse(e.target?.result as string);
        const metaInfo = jsonData.metaInfo?.assetTemplate;
        const customer = jsonData.metaInfo?.customer;
        if (metaInfo && metaInfo.manufacturer && metaInfo.model_number) {
          setValue('manufacturer', metaInfo.manufacturer); // ✅ Set default value
          setValue('model_number', metaInfo.model_number);
          setIsValidFile(true); // Show the form once data is valid
          setMeasurements(jsonData.relatedEntities || []); // ✅ Store measurements
          setAssetType(metaInfo.selectedAsset ?? null);
          setValue('save_as_global_asset_template', customer === null);
        } else {
          throw new Error('Invalid JSON structure');
        }
      } catch (error) {
        console.error('Error parsing JSON file:', error);
        setErrorMessage('Invalid JSON file. Please upload a valid asset template.');
        setSelectedFile(null);
      }
    };
    reader.readAsText(file);
  };

  // Handle Form Submission
  const onSubmit = async (data: any) => {
    const transformedData: createAssetTemplateDTO = {
      assetTypeId: assetType?.id ? Number(assetType.id) : 0, // Convert asset type ID to number
      manufacturer: data.manufacturer,
      model_number: data.model_number,
      save_as_global_asset_template: data.save_as_global_asset_template,
      measurements: measurements.map((measurement) => ({
        type_id: measurement.measurement.type.id,
        data_type_id: measurement.measurement.data_type.id,
        value_type_id: measurement.measurement.value_type.id,
        metric_id: measurement.measurement.metric.id,
        description: measurement.measurement.description ?? undefined,
        location_id: measurement.measurement.location.id ?? undefined,
        datasource_id: measurement.measurement.datasource.id ?? undefined,
        meter_factor: measurement.measurement.meter_factor ?? undefined,
      })),
    };

    // console.log('Transformed Data for Submission:', transformedData);
    // setImportData(false); // Close the dialog after successful submission
    try {
      await createAssetTemplate(transformedData);
    } catch (error) {
      console.error('Error creating asset template:', error);
      showErrorAlert('Error creating asset template');
    }
  };

  return (
    <>
      <AlertSnackbar {...snackbarState} />
      <CustomDialog
        open={open}
        onClose={() => {
          setImportData(false);
          setSelectedFile(null);
          setIsValidFile(false);
          setErrorMessage(null);
          setMeasurements([]); // Reset measurements
          setAssetType(null);
        }}
        title="Import Asset Template"
        content={
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
            {/* File Upload Section */}
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <Typography variant="body1">Upload JSON File</Typography>
              <input
                type="file"
                accept="application/json"
                style={{ display: 'none' }}
                id="file-upload"
                onChange={handleFileUpload}
              />
              <Tooltip title="Upload JSON File">
                <label htmlFor="file-upload">
                  <IconButton component="span">
                    <FileUploadIcon />
                  </IconButton>
                </label>
              </Tooltip>
            </Box>

            {/* Show File Name or Error Message */}
            {errorMessage ? (
              <Alert severity="error">{errorMessage}</Alert>
            ) : (
              selectedFile && (
                <Typography variant="body2" sx={{ color: 'gray' }}>
                  Selected File: {selectedFile.name}
                </Typography>
              )
            )}

            {/* Editable Form Fields with Validation (Only When Valid) */}

            {isValidFile && (
              <Box
                component="form"
                onSubmit={handleSubmit(onSubmit)}
                sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}
              >
                {assetType && (
                  <Typography variant="h6" mt={2}>
                    Asset Type : <span style={{ fontWeight: 'bold' }}> &nbsp;{assetType.name}</span>
                  </Typography>
                )}
                <Controller
                  name="manufacturer"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      label="Manufacturer"
                      fullWidth
                      error={!!errors.manufacturer}
                      helperText={errors.manufacturer?.message}
                    />
                  )}
                />

                <Controller
                  name="model_number"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      label="Model Number"
                      fullWidth
                      error={!!errors.model_number}
                      helperText={errors.model_number?.message}
                    />
                  )}
                />
                <Controller
                  name="save_as_global_asset_template"
                  control={control}
                  render={({ field }) => (
                    <FormControlLabel
                      control={
                        <Checkbox
                          {...field}
                          checked={field.value}
                          disabled={!globalAdmin}
                          onChange={(e) => {
                            field.onChange(e.target.checked);
                          }}
                        />
                      }
                      label="Save as Global Asset Template"
                    />
                  )}
                />
                {/* Display Measurements in Table */}
                <Typography variant="h6" mt={2}>
                  Measurements
                </Typography>
                <TableContainer component={Paper} sx={{ maxHeight: 300, overflow: 'auto' }}>
                  <Table stickyHeader>
                    <TableHead>
                      <TableRow>
                        <TableCell>ID</TableCell>
                        <TableCell>Type</TableCell>
                        <TableCell>Data Type</TableCell>
                        <TableCell>Value Type</TableCell>
                        <TableCell>Metric</TableCell>
                        <TableCell>Datasource</TableCell>
                        <TableCell>Location</TableCell>
                        <TableCell>Meter Factor</TableCell>
                        <TableCell>Description</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {measurements.length > 0 ? (
                        measurements.map((measurement, index) => (
                          <TableRow key={index}>
                            <TableCell>{measurement.measurement.id}</TableCell>
                            <TableCell>{measurement.measurement.type.name}</TableCell>
                            <TableCell>{measurement.measurement.data_type.name}</TableCell>
                            <TableCell>{measurement.measurement.value_type.name}</TableCell>
                            <TableCell>{measurement.measurement.metric.name}</TableCell>
                            <TableCell>
                              {measurement.measurement.datasource.name || 'N/A'}
                            </TableCell>
                            <TableCell>{measurement.measurement.location.name || 'N/A'}</TableCell>
                            <TableCell>{measurement.measurement.meter_factor ?? 'N/A'}</TableCell>
                            <TableCell>{measurement.measurement.description || 'N/A'}</TableCell>
                          </TableRow>
                        ))
                      ) : (
                        <TableRow>
                          <TableCell colSpan={9} align="center">
                            No measurements found.
                          </TableCell>
                        </TableRow>
                      )}
                    </TableBody>
                  </Table>
                </TableContainer>
              </Box>
            )}
          </Box>
        }
        dialogActions={
          <Box sx={{ display: 'flex', justifyContent: 'space-between', width: '100%' }}>
            <Button variant="outlined" onClick={() => setImportData(false)}>
              Close
            </Button>
            <Button
              type="submit"
              variant="contained"
              color="primary"
              onClick={handleSubmit(onSubmit)}
              disabled={!isValidFile}
            >
              Submit
            </Button>
          </Box>
        }
        maxWidth="md"
      />
    </>
  );
};

export default AssetTemplateFileImporter;
