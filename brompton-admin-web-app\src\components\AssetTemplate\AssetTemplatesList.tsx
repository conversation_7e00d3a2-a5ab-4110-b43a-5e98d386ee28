import {
  CheckCircleOutline,
  ContentCopy,
  Edit,
  FileDownload,
  RadioButtonUnchecked,
} from '@mui/icons-material';
import FileUploadIcon from '@mui/icons-material/FileUpload';
import { Card, CardContent, Grid, IconButton, Tooltip, Typography } from '@mui/material';
import { useRouter } from 'next/navigation';
import { Dispatch, SetStateAction, useState } from 'react';
import { useSelector } from 'react-redux';
import { useHasAdminAccess } from '~/hooks/useHasAdminAccess';

import {
  createAssetTemplateDataWithId,
  createAssetTemplateDataWIthMeasureId,
  Datasource,
  DataType,
  MeasurementLocation,
  MeasurementType,
  ValueType,
} from '~/measurements/domain/types';
import { getActiveCustomer } from '~/redux/selectors/customerSelectors';
import { AlertSnackbar } from '~/shared/snackbars/components/AlertSnackbar';
import { useSnackbar } from '~/shared/snackbars/snackbar-hooks';
import { AssetTypeMetrics } from '~/types/asset';
import { hexToRgbA } from '~/utils/utils';
import CloneAssetTemplateForm from './CloneAssetTemplateForm';
type ExtendedCreateAssetTemplateData = createAssetTemplateDataWIthMeasureId & {
  customer?: number | null;
  asset_type_id?: number;
  id?: number;
};

type AssetTemplatesListProps = {
  showMeasure?: boolean;
  assetTemplate: ExtendedCreateAssetTemplateData;
  valueTypesList: ValueType[];
  dataSourceList: Datasource[];
  measurementLocations: MeasurementLocation[];
  dataTypeList: DataType[];
  measurementTypeList: MeasurementType[];
  assetTypeMetrics: AssetTypeMetrics[];
  selectedTemplate?: createAssetTemplateDataWithId | null;
  setSelectedTemplet?: Dispatch<SetStateAction<createAssetTemplateDataWithId | null>>;
  selectedAssetType:
    | {
        id: string;
        label: string;
      }
    | null
    | undefined;
};
const AssetTemplatesList = ({
  selectedAssetType,
  showMeasure,
  assetTemplate,
  dataSourceList,
  dataTypeList,
  valueTypesList,
  measurementLocations,
  measurementTypeList,
  assetTypeMetrics,
  selectedTemplate,
  setSelectedTemplet,
}: AssetTemplatesListProps) => {
  const router = useRouter();
  const { globalAdmin, admin } = useHasAdminAccess();
  const [openCloneDialog, setOpenCloneDialog] = useState<boolean>(false);
  const [snackbarState, showSuccessAlert, showErrorAlert] = useSnackbar();
  const activeCustomer = useSelector(getActiveCustomer);

  const exportAssetTemplate = (assetTemplate: ExtendedCreateAssetTemplateData) => {
    if (!assetTemplate?.measurements) {
      console.warn('No measurements found for export.');
      return;
    }

    const relatedEntities = assetTemplate.measurements.map((measurement, i) => ({
      entityType: 'measurement',
      version: 1,
      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      //@ts-ignore
      entityId: measurement.id,
      measurement: {
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        //@ts-ignore
        id: measurement?.id,
        metric: {
          name: assetTypeMetrics.find((metric) => metric.id === measurement.type_id)?.name ?? 'N/A',
          id: measurement.type_id ?? null,
        },
        type: {
          name: measurementTypeList.find((type) => type.id === measurement.type_id)?.name ?? 'N/A',
          id: measurement.type_id ?? null,
        },
        data_type: {
          name: dataTypeList.find((dt) => dt.id === measurement.data_type_id)?.name ?? 'N/A',
          id: measurement.data_type_id ?? null,
        },
        value_type: {
          name: valueTypesList.find((vt) => vt.id === measurement.value_type_id)?.name ?? 'N/A',
          id: measurement.value_type_id ?? null,
        },
        datasource: {
          name: dataSourceList.find((ds) => ds.id === measurement.datasource_id)?.name ?? 'N/A',
          id: measurement.datasource_id ?? null,
        },
        location: {
          name:
            measurementLocations.find((loc) => loc.id === measurement.location_id)?.name ?? 'N/A',
          id: measurement.location_id ?? null,
        },
        meter_factor: measurement.meter_factor ?? null,
        description: measurement.description ?? '',
      },
    }));
    const exportData = {
      metaInfo: {
        assetTemplate: {
          manufacturer: assetTemplate.manufacturer,
          model_number: assetTemplate.model_number,
          selectedAsset: {
            id: selectedAssetType?.id,
            name: selectedAssetType?.label,
          },
        },
        ...(assetTemplate.customer
          ? {
              customer: {
                name: activeCustomer?.name,
                id: activeCustomer?.id,
                nameId: activeCustomer?.nameId,
              },
            }
          : {
              customer: null,
            }),
      },
      relatedEntities,
    };

    const blob = new Blob([JSON.stringify(exportData, null, 2)], {
      type: 'application/json',
    });
    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = `asset_template_export_${assetTemplate.id}.json`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    showSuccessAlert('Asset template exported successfully');
  };

  return (
    <>
      <AlertSnackbar {...snackbarState} />
      {assetTemplate.asset_type_id && assetTemplate.id && (
        <CloneAssetTemplateForm
          open={openCloneDialog}
          setOpenCloneDialog={setOpenCloneDialog}
          assetTypeId={assetTemplate.asset_type_id}
          templateId={assetTemplate.id}
          defaultModelNumber={assetTemplate.model_number}
          defaultManufacturer={assetTemplate.manufacturer}
        />
      )}
      <Card
        sx={{
          mt: 2,
          pl: 1,
          pr: 1,
          backgroundColor: (theme) =>
            selectedTemplate?.id === assetTemplate.id
              ? `rgba(${hexToRgbA(theme.palette.primary.main)}, 0.2)`
              : undefined,
        }}
      >
        <CardContent
          sx={{
            pb: 0,
            ':last-child': {
              pb: 2,
            },
          }}
        >
          <Grid container alignItems="center">
            <Grid item xs={setSelectedTemplet ? 1 : 0} sm={setSelectedTemplet ? 1 : 0}>
              {setSelectedTemplet ? (
                <Tooltip title="Select Template">
                  <IconButton
                    color={selectedTemplate?.id === assetTemplate.id ? 'primary' : 'default'}
                    onClick={() => setSelectedTemplet(assetTemplate)}
                  >
                    {selectedTemplate?.id === assetTemplate.id ? (
                      <CheckCircleOutline />
                    ) : (
                      <RadioButtonUnchecked />
                    )}
                  </IconButton>
                </Tooltip>
              ) : // <Tooltip title="Select Template">
              //   <IconButton
              //     color="default"
              //     onClick={() => {
              //       router.push(
              //         `/create-asset-template-instance?assetType=${assetTemplate.asset_type_id}&assetTemplate=${assetTemplate.id}`,
              //       );
              //     }}
              //   >
              //     <RadioButtonUnchecked />
              //   </IconButton>
              // </Tooltip>
              null}
            </Grid>
            <Grid item xs={12} sm={setSelectedTemplet ? 4 : 5}>
              <Typography variant="h5" component="div">
                Model Number : <b>{assetTemplate.model_number}</b>
              </Typography>
            </Grid>
            <Grid item xs={12} sm={4}>
              <Typography variant="h5" component="div">
                Manufacturer : <b>{assetTemplate.manufacturer}</b>
              </Typography>
            </Grid>
            <Grid item xs={12} sm={3} sx={{ display: 'flex', justifyContent: 'end' }}>
              {!assetTemplate?.customer && (globalAdmin || admin) && (
                <Tooltip title="Clone to Customer">
                  <IconButton
                    color="default"
                    onClick={() => {
                      setOpenCloneDialog(true);
                    }}
                  >
                    <ContentCopy />
                  </IconButton>
                </Tooltip>
              )}
              {!assetTemplate?.customer && globalAdmin && (
                <Tooltip title="Edit Template">
                  <IconButton
                    color="secondary"
                    onClick={() =>
                      router.push(
                        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
                        //@ts-ignore
                        `/asset-templates/edit?assetType=${assetTemplate.asset_type_id}&assetTemplate=${assetTemplate.id}`,
                      )
                    }
                  >
                    <Edit />
                  </IconButton>
                </Tooltip>
              )}
              {assetTemplate?.customer && admin && (
                <Tooltip title="Edit Template">
                  <IconButton
                    color="secondary"
                    onClick={() =>
                      router.push(
                        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
                        //@ts-ignore
                        `/asset-templates/edit?assetType=${assetTemplate.asset_type_id}&assetTemplate=${assetTemplate.id}`,
                      )
                    }
                  >
                    <Edit />
                  </IconButton>
                </Tooltip>
              )}
              <Tooltip title="Export">
                <IconButton onClick={() => exportAssetTemplate(assetTemplate)} color="primary">
                  <FileUploadIcon />
                </IconButton>
              </Tooltip>
            </Grid>
          </Grid>
          {showMeasure ? (
            <>
              <Typography variant="h5" mt={3} ml={2}>
                Measurements
              </Typography>
              <Grid container spacing={3}>
                {assetTemplate.measurements?.map((measurement, i) => (
                  <Grid item xs={12} sm={6} md={4} key={i}>
                    <Card
                      sx={{
                        m: 1,
                        p: 3,
                        // width is now controlled by the grid item, so you might not need this
                      }}
                    >
                      <Typography variant="h6">Measurement #{i + 1}</Typography>
                      <Typography mt={1}>
                        Asset Type :
                        <b>
                          {assetTypeMetrics.find(
                            (assetTypeMetric) => assetTypeMetric.id === measurement.type_id,
                          )?.name ?? 'N/A'}
                        </b>
                      </Typography>
                      <Typography mt={1}>
                        Measurement Type :
                        <b>
                          {measurementTypeList.find(
                            (measurementType) => measurementType.id === measurement.type_id,
                          )?.name ?? 'N/A'}
                        </b>
                      </Typography>
                      <Typography mt={1}>
                        Data Source :
                        <b>
                          {dataSourceList.find((ds) => ds.id === measurement.datasource_id)?.name ??
                            'N/A'}
                        </b>
                      </Typography>
                      <Typography mt={1}>
                        Data Type :
                        <b>
                          {dataTypeList.find((dataType) => dataType.id === measurement.data_type_id)
                            ?.name ?? 'N/A'}
                        </b>
                      </Typography>
                      <Typography mt={1}>
                        Measurement Location :
                        <b>
                          {measurementLocations.find(
                            (location) => location.id === measurement.location_id,
                          )?.name ?? 'N/A'}
                        </b>
                      </Typography>
                      <Typography mt={1}>
                        Meter Factor : <b>{measurement.meter_factor}</b>
                      </Typography>
                      <Typography mt={1}>
                        Description : <b>{measurement.description}</b>
                      </Typography>
                    </Card>
                  </Grid>
                ))}
              </Grid>
            </>
          ) : null}
        </CardContent>
      </Card>
    </>
  );
};

export default AssetTemplatesList;
