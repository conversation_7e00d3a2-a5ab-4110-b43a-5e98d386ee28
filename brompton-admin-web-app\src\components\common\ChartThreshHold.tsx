import {
  Checkbox,
  Divider,
  FormControlLabel,
  Grid,
  MenuItem,
  Select,
  SelectChangeEvent,
  TextField,
  Typography,
} from '@mui/material';
import { ChangeEvent } from 'react';
import { MultiMeasureChartWidgets, setMultiMeasureChartSettings } from '~/types/widgets';
type ChartThreshHoldProps = {
  settings: MultiMeasureChartWidgets;
  setSettings: setMultiMeasureChartSettings;
};
const ChartThreshHold = ({ settings, setSettings }: ChartThreshHoldProps) => {
  const handleShowThreshholdVisibility = (
    event: ChangeEvent<HTMLInputElement>,
    checked: boolean,
  ) => {
    setSettings((prevState) => ({
      ...prevState,
      showThreshold: event.target.checked,
    }));
  };
  const handleColorChange = (event: ChangeEvent<HTMLInputElement>) => {
    setSettings((prevState) => ({
      ...prevState,
      treshdold: {
        ...prevState.treshdold,
        [event.target.name]: event.target.value,
      },
    }));
  };
  const handleThresholdStyleChange = (event: SelectChangeEvent<'solid' | 'dash' | 'dot'>) => {
    setSettings((prevState) => ({
      ...prevState,
      treshdold: {
        ...prevState.treshdold,
        [event.target.name]: event.target.value,
      },
    }));
  };
  const handleThresholdNameChange = (event: ChangeEvent<HTMLInputElement>) => {
    setSettings((prevState) => ({
      ...prevState,
      treshdold: {
        ...prevState.treshdold,
        [event.target.name]: event.target.value,
      },
    }));
  };
  return (
    <>
      <Divider />
      <Typography variant="h5" mt={2} mb={1}>
        Configure Threshold
      </Typography>
      <FormControlLabel
        control={
          <Checkbox
            checked={settings?.showThreshold}
            onChange={handleShowThreshholdVisibility}
            name="isVisible"
          />
        }
        label="Show Threshold Line"
      />
      {settings?.showThreshold ? (
        <Grid container spacing={2}>
          <Grid item xs={12} sm={6}>
            <TextField
              name="thresholdName"
              label="Threshold Name"
              onChange={handleThresholdNameChange}
              value={settings.treshdold?.thresholdName}
              variant="outlined"
              margin="normal"
              fullWidth
            />
          </Grid>
          <Grid item xs={12} sm={6}>
            <TextField
              name="thresholdColor"
              type="color"
              label="Threshold Color"
              onChange={handleColorChange}
              value={settings.treshdold?.thresholdColor}
              variant="outlined"
              margin="normal"
              fullWidth
              InputLabelProps={{ shrink: true }}
            />
          </Grid>
          <Grid item xs={12} sm={6}>
            <TextField
              name="thresholdValue"
              type="number"
              sx={{ m: 0 }}
              label="Threshold Value"
              onChange={handleColorChange}
              value={settings.treshdold?.thresholdValue}
              variant="outlined"
              margin="normal"
              fullWidth
            />
          </Grid>
          <Grid item xs={12} sm={6}>
            <Select
              name="thresholdStyle"
              onChange={handleThresholdStyleChange}
              value={settings.treshdold?.thresholdStyle}
              variant="outlined"
              fullWidth
              displayEmpty
            >
              <MenuItem value="solid">Solid</MenuItem>
              <MenuItem value="dash">Dash</MenuItem>
              <MenuItem value="dot">Dot</MenuItem>
            </Select>
          </Grid>
        </Grid>
      ) : null}
    </>
  );
};
export default ChartThreshHold;
