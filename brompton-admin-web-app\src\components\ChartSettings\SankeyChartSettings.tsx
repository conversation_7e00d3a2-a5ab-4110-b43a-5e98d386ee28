import {
  Box,
  Button,
  Checkbox,
  FormControl,
  FormControlLabel,
  FormLabel,
  Input,
  Stack,
} from '@mui/material';
import { ChangeEvent, useEffect, useState } from 'react';
import { SankeyChartWidget } from '~/types/widgets';
import DataWidgetSettingsContainer from '../common/DataWidgetSettingsContainer/DataWidgetSettingsContainer';
import SankeySettingsConnector from '../common/SankeySettngsHelper/SankeySettingsConnector';
import SankeySettingsHelper from '../common/SankeySettngsHelper/SankeySettngsHelper';

type SankeyChartSettingsProps = {
  settings: SankeyChartWidget;
  handleSettings: (
    value: ((prevState: SankeyChartWidget) => SankeyChartWidget) | SankeyChartWidget,
  ) => void;
};

export type Label = {
  sourceFrom: 'Calculated' | 'Default';
  sourceName: string;
  sourceLabel: string;
  aggBy?: number;
  sourceAssetMeasure?: {
    assetId: string;
    measureId: string;
  };
  linkedDashboard?: {
    type: 'dashboard' | 'template';
    id: number;
    title: string;
    openInNewTab?: boolean;
  };
  assetOrAssetType?: number;
};
export type Connection = {
  source: string;
  destination: string;
  color?: string;
};
const SankeyChartSettings = ({ settings, handleSettings }: SankeyChartSettingsProps) => {
  const [labels, setLabels] = useState<Label[]>([]);
  const [connections, setConnections] = useState<Connection[]>([]);
  useEffect(() => {
    setLabels(
      settings.Label?.map((label) => {
        return {
          ...label,
          aggBy: label?.aggBy ?? 1,
        };
      }),
    );
    setConnections(settings.connections);
  }, [settings.Label, settings.connections]);
  useEffect(() => {
    if (settings.mode === 'dashboard' && !settings.title.isVisible) {
      const titles = settings.Label.filter((label) => label.sourceFrom === 'Default')
        .map((label) => label.sourceLabel)
        .join(' Vs.');
      handleSettings((prevState) => ({
        ...prevState,
        title: {
          ...prevState.title,
          value: prevState.title.isVisible ? prevState.title.value : titles,
        },
      }));
    }
    if (settings.mode === 'template' && !settings.title.isVisible) {
      const titles = settings.Label.filter((label) => label.sourceFrom === 'Default')
        .map((label) => label.sourceLabel)
        .join(' Vs.');
      handleSettings((prevState) => ({
        ...prevState,
        title: {
          ...prevState.title,
          value: prevState.title.isVisible ? prevState.title.value : titles,
        },
      }));
    }
  }, [settings.mode, settings.Label, settings.title.isVisible]);
  return (
    <Stack gap={1}>
      <DataWidgetSettingsContainer
        settings={settings}
        setSettings={handleSettings}
        dataTabChildren={
          <>
            <Box>
              {labels.map((label, index) => (
                <SankeySettingsHelper
                  key={index}
                  mode={settings.mode}
                  settings={settings}
                  index={index}
                  label={label}
                  labels={labels}
                  setLabels={setLabels}
                  handleSettings={handleSettings}
                />
              ))}
              {labels.length === 0 && (
                <Box>
                  <Button
                    onClick={() => {
                      setLabels([
                        ...labels,
                        { sourceFrom: 'Default', sourceName: '', sourceLabel: '' },
                      ]);
                    }}
                  >
                    Add Label
                  </Button>
                </Box>
              )}
              {connections.length === 0 && (
                <Box>
                  <Button
                    onClick={() => {
                      setConnections([...connections, { source: '', destination: '' }]);
                    }}
                  >
                    Add Connection
                  </Button>
                </Box>
              )}
              {connections.map((conn, index) => (
                <SankeySettingsConnector
                  lables={labels}
                  index={index}
                  key={index}
                  mode={settings.mode}
                  singleConnection={conn}
                  connection={connections}
                  setConnections={setConnections}
                  handleSettings={handleSettings}
                />
              ))}
            </Box>
          </>
        }
        feelTabChidren={
          <>
            <Box>
              <FormControlLabel
                control={
                  <Checkbox
                    checked={settings.showColor}
                    onChange={() => {
                      handleSettings((prevState) => ({
                        ...prevState,
                        showColor: !prevState.showColor,
                      }));
                    }}
                    name="showColor"
                  />
                }
                label="Show Connection Color"
              />
            </Box>
            {settings.showColor &&
              connections.map((connection, index) => (
                <Box key={index}>
                  <FormControl fullWidth>
                    <FormLabel sx={{ p: 2, pb: 0 }}>Connection Color #{index + 1}</FormLabel>
                    <Input
                      type="color"
                      fullWidth
                      sx={{
                        p: 2,
                        pt: 0,
                      }}
                      name={`connection.${index}.color`}
                      value={connection.color ?? '#DEDEDE'}
                      onChange={(e: ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
                        handleSettings((prevState) => {
                          const newConnections = [
                            ...JSON.parse(JSON.stringify(prevState.connections)),
                          ];
                          newConnections[index].color = e.target.value;
                          return {
                            ...prevState,
                            connections: newConnections,
                          };
                        });
                        setConnections((prevState) => {
                          const newConnections = [...JSON.parse(JSON.stringify(prevState))];
                          newConnections[index].color = e.target.value;
                          return newConnections;
                        });
                      }}
                    />
                  </FormControl>
                </Box>
              ))}
          </>
        }
      />
    </Stack>
  );
};

export default SankeyChartSettings;
