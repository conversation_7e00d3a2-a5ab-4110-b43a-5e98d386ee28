import { useEffect, useState } from 'react';
import { useGetAllBackOfficeAssetTypesQuery } from '~/redux/api/assetsApi';
import { AssetTypeOption } from '~/types/asset';
import { assetTypePathMapper } from '~/utils/mappers/asset-type-mapper';

const useGetAssetTypesWithPath = () => {
  const [assetTypesWithPath, setAssetTypesWithPath] = useState<AssetTypeOption[]>([]);
  const { data: assetTypeListData, isSuccess: isSuccessfullBackOffieAssetTypes } =
    useGetAllBackOfficeAssetTypesQuery();

  useEffect(() => {
    if (isSuccessfullBackOffieAssetTypes && assetTypeListData) {
      setAssetTypesWithPath(
        assetTypePathMapper(
          assetTypeListData.map((item) => ({
            ...item,
            name: `${item.name}`,
            id: item.id,
          })),
        ),
      );
    }
  }, [assetTypeListData, isSuccessfullBackOffieAssetTypes]);
  return {
    assetTypesWithPath,
    assetTypeListData,
  };
};

export default useGetAssetTypesWithPath;
