import { dia } from '@joint/core';
import ReactDOM from 'react-dom/client';
import Plot from 'react-plotly.js';

export const BarChartElement = (label: string): dia.Element => {
  const BarChartElement = dia.Element.define(
    'custom.BarChartElement',
    {
      attrs: {
        body: {
          refWidth: '100%',
          refHeight: '100%',
        },
      },
    },
    {
      markup: `
        <g class="rotatable">
          <rect class="body" />
          <foreignObject width="100%" height="100%">
            <div xmlns="http://www.w3.org/1999/xhtml" class="bar-chart-container">
              <div class="bar-chart-root" style="width: 100%; height: 100%;"></div>
            </div>
          </foreignObject>
        </g>
      `,
    },
  );

  const element = new BarChartElement({
    size: { width: 300, height: 200 },
    attrs: {
      body: { fill: 'transparent' },
    },
  });

  // Delay rendering to ensure DOM is attached
  setTimeout(() => {
    const container = document.querySelector('.bar-chart-container .bar-chart-root');
    if (container) {
      const root = ReactDOM.createRoot(container);
      root.render(
        <Plot
          data={[
            {
              x: ['Category A', 'Category B', 'Category C', 'Category D'],
              y: [20, 14, 23, 17],
              type: 'bar',
              marker: { color: ['blue', 'orange', 'green', 'red'] },
            },
          ]}
          config={{
            displayModeBar: false,
          }}
          layout={{
            width: 300,
            height: 200,
            title: label,
            xaxis: {
              title: 'Categories',
            },
            yaxis: {
              title: 'Values',
            },
          }}
        />,
      );
    } else {
      console.warn('Bar chart container not found!');
    }
  }, 500);

  return element;
};
