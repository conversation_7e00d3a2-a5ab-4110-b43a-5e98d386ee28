import {
  Box,
  Card,
  Checkbox,
  Divider,
  FormControl,
  FormControlLabel,
  FormGroup,
  FormLabel,
  Input,
  Typography,
} from '@mui/material';
import { useSelector } from 'react-redux';
import { getMetricsIdToName } from '~/redux/selectors/dashboardSelectors';
import { BarChartWidget, ScatterChartWidget } from '~/types/widgets';

type MeasureColorSelectorProps = {
  selectedDbMeasureIdToName: { [key: string]: string };
  selectedMeasureNames: string[];
  settings: BarChartWidget | ScatterChartWidget;
  handleBarColorChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  overrideGlobalBarColor: boolean;
  setOverRideSettings: (event: React.ChangeEvent<HTMLInputElement>, checked: boolean) => void;
};
const MeasureColorSelector = ({
  selectedDbMeasureIdToName,
  selectedMeasureNames,
  settings,
  handleBarColorChange,
  overrideGlobalBarColor,
  setOverRideSettings,
}: MeasureColorSelectorProps) => {
  const metricsIdToName = useSelector(getMetricsIdToName);
  return (
    <>
      <Divider />
      <Typography variant="h5" mt={2} mb={1}>
        Chart Color(s)
      </Typography>
      <Box>
        <FormGroup>
          <FormControlLabel
            control={
              <Checkbox
                checked={overrideGlobalBarColor}
                onChange={setOverRideSettings}
                name="overrideGlobalBarColor"
              />
            }
            label="Override Chart Color"
          />
        </FormGroup>
      </Box>
      {overrideGlobalBarColor ? (
        <>
          {settings.mode === 'dashboard' && (
            <>
              {Object.entries(selectedDbMeasureIdToName).map(
                ([dbMeasureId, name]: [string, string]) => (
                  <>
                    {selectedMeasureNames.includes(dbMeasureId) ? (
                      <FormControl fullWidth>
                        <FormLabel sx={{ p: 2, pb: 0 }}>Bar Color ({name})</FormLabel>
                        <Input
                          type="color"
                          fullWidth
                          name={dbMeasureId}
                          value={
                            settings.barColors?.find((color) => color.measureId === dbMeasureId)
                              ?.color
                          }
                          onChange={handleBarColorChange}
                        />
                      </FormControl>
                    ) : null}
                  </>
                ),
              )}
            </>
          )}
          {settings.mode === 'template' && (
            <>
              {Object.entries(metricsIdToName)
                .filter(([dbMeasureId]) => selectedMeasureNames.includes(dbMeasureId))
                .map(([dbMeasureId, name]: [string, string], index: number) => (
                  <FormControl fullWidth key={index}>
                    <FormLabel sx={{ p: 2, pb: 0 }}>Bar Color ({name})</FormLabel>
                    <Input
                      type="color"
                      fullWidth
                      sx={{
                        p: 2,
                        pt: 0,
                      }}
                      name={dbMeasureId}
                      value={
                        settings.barColors?.find((color) => color.measureId === dbMeasureId)?.color
                      }
                      onChange={handleBarColorChange}
                    />
                  </FormControl>
                ))}
            </>
          )}
        </>
      ) : null}
    </>
  );
};

export default MeasureColorSelector;
