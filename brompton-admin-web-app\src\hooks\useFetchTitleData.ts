import { useEffect, useState } from 'react';
import { UnitOfMeasure } from '~/measurements/domain/types';
import { AssetMeasurementDetails } from '~/types/measures';
import {
  AssetMeasurementDetailsWithLastFetchTime,
  SingleScatterTimeSeriesData,
} from '~/types/timeseries';
import { TitleWidget } from '~/types/widgets';
import { roundNumber } from '~/utils/utils';
import { useGetMeasuresTsData } from './useGetMeasuresTsData';

type TitleData = {
  value: string;
  unit?: string;
  data?: AssetMeasurementDetailsWithLastFetchTime;
};

type TrendResult = {
  isError: boolean;
  error?: string;
  tsData: SingleScatterTimeSeriesData;
  measureData: AssetMeasurementDetails;
  unitOfMeasures: UnitOfMeasure[];
  lastFetchTime: number;
};

function getValueFromMeasurement(result: TrendResult | null): TitleData {
  const measureData = result?.measureData;
  const lastFetchTime = result?.lastFetchTime || 0;

  if (!result || !result.tsData) {
    return {
      data: {
        ...measureData,
        lastFetchTime,
        partialFailed: false,
      } as AssetMeasurementDetailsWithLastFetchTime,
      value: '',
      unit: undefined,
    };
  }

  const { 'ts,val': values, error } = result.tsData;
  if (error || !values || values.length === 0) {
    return {
      data: {
        ...measureData,
        lastFetchTime,
        partialFailed: false,
      } as AssetMeasurementDetailsWithLastFetchTime,
      value: '',
      unit: undefined,
    };
  }

  const unit = result.unitOfMeasures.find((data) => data.id === result.measureData.unitOfMeasureId);

  // Get the last value from the time series
  const current = values.length > 0 ? roundNumber(values[values.length - 1][1] ?? 0) || '' : '';
  return {
    value: current,
    unit: unit?.name,
    data: {
      ...measureData,
      lastFetchTime,
      partialFailed: false,
    } as AssetMeasurementDetailsWithLastFetchTime,
  };
}

export function useFetchTitleData(widgetId: string, state: TitleWidget) {
  const [titleResult, setTitleResult] = useState<TrendResult | undefined>(undefined);
  const [selectedMeasureId, setSelectedMeasureId] = useState<string[]>([]);

  // Set up the selected measurement ID based on widget settings
  useEffect(() => {
    if (
      (state.valueMode === 'measurement' || state.valueMode === undefined) &&
      state.measurementId
    ) {
      setSelectedMeasureId([state.measurementId.toString()]);
    }

    if (state.mode === 'template') {
      if (state.mode === 'template') {
        if (state.selectedDbMeasureId !== '') {
          setSelectedMeasureId([state.selectedDbMeasureId]);
        } else {
          setSelectedMeasureId([]);
        }
      }
    }
  }, [state.valueMode, state.measurementId]);

  // Initialize the data state
  const [titleData, setTitleData] = useState<{
    isLoading: boolean;
    data: TitleData | null;
    isError: boolean;
  }>({
    isLoading: true,
    data: null,
    isError: false,
  });

  // Fetch measurement data using the existing hook with DataWidget properties
  const {
    data: measureData,
    isLoading: isMeasureDataLoading,
    successAndFailedMeasurements,
    isError,
  } = useGetMeasuresTsData({
    selectedTitles: selectedMeasureId,
    dataFetchSettings: state, // Use state directly as it now extends DataWidget
    assetMeasure:
      state.assetId && state.measurementId
        ? [{ assetId: state.assetId.toString(), measureId: [state.measurementId.toString()] }]
        : [],
  });

  // Update title result when measurement data changes
  useEffect(() => {
    if (isMeasureDataLoading) {
      setTitleResult(undefined);
      setTitleData((prev) => ({
        ...prev,
        isLoading: true,
        isError: false,
      }));
    } else if (measureData && measureData.length > 0) {
      setTitleResult(measureData[0] as TrendResult);
    } else if (isError) {
      setTitleResult(undefined);
      setTitleData((prev) => ({
        ...prev,
        isError: true,
        isLoading: false,
      }));
    }
  }, [isMeasureDataLoading, measureData, isError]);

  // Process the measurement data to get the title value
  useEffect(() => {
    if (state.valueMode === undefined || state.valueMode === 'fixed') {
      setTitleData({
        isLoading: false,
        data: {
          value: state.fixedValue || state.title.value || '',
          unit: '',
        },
        isError: false,
      });
    } else if (titleResult) {
      const data = getValueFromMeasurement(titleResult);
      setTitleData({
        isLoading: false,
        data,
        isError: false,
      });
    }
  }, [titleResult, state.valueMode, state.fixedValue, state.title.value]);

  return { ...titleData, successAndFailedMeasurements };
}
