import { Box, Container } from '@mui/material';
import HomeButton from '~/components/common/Home/HomeButton';
import PageName from '~/components/common/PageName/PageName';
import CustomerMeasurementBrowser from '~/components/MeasurementBrowser/CustomerMeasurementBrowser';

const MeasurementBrowser = () => {
  return (
    <Container
      sx={{
        mt: 2,
        maxWidth: '100%',
        width: '100%',
        '@media (min-width: 1200px)': { width: '100%', maxWidth: '100%' },
      }}
    >
      <Box pl={0} pb={0} pt={0} sx={{ display: 'flex' }}>
        <Box sx={{ flexGrow: 1, display: 'flex', alignItems: 'center' }}>
          <PageName name="Measurement Browser" />
        </Box>
      </Box>
      <CustomerMeasurementBrowser />
    </Container>
  );
};

export default MeasurementBrowser;
