name: Backend Build and Deploy

on:
  workflow_dispatch:
      inputs:
        environment:
            description: 'Deployment Environment'
            required: true
            default: 'dev'
            type: choice
            options:
            - dev
            - test
            - prod
        backend-release-tag:
            description: 'Backend Release Tag to deploy - Required for New Deployment'
            required: true
            default: ''

#Set ENV variables
env:
    AWS_REGION: 'us-east-1'  # Set your AWS region
    ECR_REGISTRY: '067172429169.dkr.ecr.us-east-1.amazonaws.com'

jobs:
  job_1:
    name: Tag Validation
    runs-on: ubuntu-latest
    steps:
      - name: Set ECR Repo Environment Variable
        run: |
          ENVIRONMENT=${{ github.event.inputs.environment }}
          if [ "$ENVIRONMENT" == "dev" ]; then
            echo "ECR_REPO_NAME=brompton-energy/backend" >> $GITHUB_ENV
            echo "EKS_CLUSTER_NAME=brompton-energy-eks-dev" >> $GITHUB_ENV
          elif [ "$ENVIRONMENT" == "test" ]; then
            echo "ECR_REPO_NAME=brompton-energy/backend" >> $GITHUB_ENV
            echo "EKS_CLUSTER_NAME=brompton-energy-eks-test" >> $GITHUB_ENV
          elif [ "$ENVIRONMENT" == "prod" ]; then
            echo "ECR_REPO_NAME=brompton-energy/backend" >> $GITHUB_ENV
            echo "EKS_CLUSTER_NAME=brompton-energy-eks-dev" >> $GITHUB_ENV
          fi

  job_2:
    name: Backend Build
    needs: job_1
    runs-on: ubuntu-latest
    steps:
      - name: Current Folder
        run: |
           pwd
           ls -al 

      - name: Checkout backend repository
        uses: actions/checkout@v2
        with:
          repository: ARG-Technologies/brompton-admin-api
          ref: ${{ github.event.inputs.backend-release-tag }}
          token: ****************************************    
          fetch-depth: 0  

      - uses: actions/setup-node@v4
        with:
            node-version: '16.15.0'
          
      - name: Install NextJS
        run: |
          npm i @nestjs/core
          node --version
          pwd
          ls -al 
      
      - name: Backend Build
        run: |
          npm run build

      - name: Navigate to DIST
        run: |
          echo "Current Working Directory"
          pwd
          echo "Listing contents of current Directory"
          ls -al 
          echo "Navigating into DIST folder "
          cd dist
          echo "Listing contents of DIST folder"
          mkdir client
          ls -al

      - name: Create Package.json
        run: |
          cd dist
          cat << 'EOF' > package.json
          {
            "name": "brompton-admin-api",
            "version": "0.2.0",
            "description": "",
            "author": "",
            "private": true,
            "license": "UNLICENSED",
            "engines": {
              "node": "16.15"
            },
            "scripts": {
              "build": "nest build",
              "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"",
              "start": "NODE_ENV=production node main",
              "deploy:dev": "NODE_ENV=development node main",
              "deploy:test": "NODE_ENV=test node main",
              "start:dev": "NODE_ENV=development nest start --watch",
              "start:test": "NODE_ENV=test nest start --watch",
              "start:debug": "NODE_ENV=development nest start --debug --watch",
              "start:test:debug": "NODE_ENV=test nest start --debug --watch",
              "start:prod": "NODE_ENV=production node dist/main",
              "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix",
              "test": "jest",
              "test:watch": "jest --watch",
              "test:cov": "jest --coverage",
              "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand",
              "test:e2e": "NODE_ENV=integration-test jest -i --config ./test/jest-e2e.json",
              "test:e2e:debug": "NODE_ENV=integration-test node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest -i --config ./test/jest-e2e.json --runInBand --testTimeout=100000000",
              "migrations:up:dev": "NEW_DB=true MIKRO_ORM_ENV=.development.env mikro-orm migration:up",
              "migrations:reset:dev": "NEW_DB=true MIKRO_ORM_ENV=.development.env mikro-orm migration:fresh",
              "migrations:up:test": "MIKRO_ORM_ENV=.test.env mikro-orm migration:up",
              "migrations:up:prod": "MIKRO_ORM_ENV=.production.env mikro-orm migration:up",
              "clear-cache": "mikro-orm cache:clear"
            },
            "dependencies": {
              "@mikro-orm/cli": "^5.9.0",
              "@mikro-orm/core": "^5.9.0",
              "@mikro-orm/entity-generator": "^5.9.0",
              "@mikro-orm/migrations": "^5.9.0",
              "@mikro-orm/nestjs": "^5.1.6",
              "@mikro-orm/postgresql": "^5.9.0",
              "@mikro-orm/reflection": "^5.9.0",
              "@nestjs/axios": "^3.0.1",
              "@nestjs/cli": "^9.0.0",
              "@nestjs/common": "^9.0.0",
              "@nestjs/config": "^2.3.0",
              "@nestjs/core": "^9.3.2",
              "@nestjs/event-emitter": "^2.0.2",
              "@nestjs/jwt": "^10.0.1",
              "@nestjs/passport": "^9.0.1",
              "@nestjs/platform-express": "^9.0.0",
              "@nestjs/serve-static": "^4.0.0",
              "@nestjs/swagger": "^6.2.1",
              "argon2": "^0.30.3",
              "axios": "^1.6.0",
              "class-transformer": "^0.5.1",
              "class-validator": "^0.14.0",
              "cookie-parser": "^1.4.6",
              "node-gyp": "^9.3.1",
              "passport": "^0.6.0",
              "passport-jwt": "^4.0.1",
              "passport-local": "^1.0.0",
              "redis": "^4.6.6",
              "rxjs": "^7.2.0"
            },
            "devDependencies": {
              "@nestjs/schematics": "^9.0.0",
              "@nestjs/testing": "^9.3.2",
              "@types/cookie-parser": "^1.4.3",
              "@types/express": "^4.17.13",
              "@types/jest": "29.2.4",
              "@types/node": "18.11.18",
              "@types/passport-jwt": "^3.0.8",
              "@types/passport-local": "^1.0.35",
              "@types/supertest": "^2.0.11",
              "@typescript-eslint/eslint-plugin": "^5.0.0",
              "@typescript-eslint/parser": "^5.0.0",
              "eslint": "^8.0.1",
              "eslint-config-prettier": "^8.3.0",
              "eslint-plugin-prettier": "^4.0.0",
              "jest": "29.3.1",
              "prettier": "^2.3.2",
              "source-map-support": "^0.5.20",
              "supertest": "^6.1.3",
              "ts-jest": "29.0.3",
              "ts-loader": "^9.2.3",
              "ts-node": "^10.0.0",
              "tsconfig-paths": "4.1.1",
              "typescript": "^4.7.4"
            },
            "jest": {
              "moduleFileExtensions": [
                "js",
                "json",
                "ts"
              ],
              "rootDir": ".",
              "testRegex": ".*\\.spec\\.ts$",
              "transform": {
                "^.+\\.(t|j)s$": "ts-jest"
              },
              "modulePaths": [
                "node_modules",
                "<rootDir>"
              ],
              "collectCoverageFrom": [
                "**/*.(t|j)s"
              ],
              "coverageDirectory": "../coverage",
              "testEnvironment": "node"
            },
            "mikro-orm": {
              "useTsNode": true,
              "configPaths": [
                "./src/mikro-orm.config.ts",
                "./dist/mikro-orm.config.js"
              ]
            }
          }
          EOF


  job_3:
    name: Build and Push Image
    needs: job_2
    runs-on: ubuntu-latest
    environment: ${{ github.event.inputs.environment }}
    steps:
      - name: Current Folder
        run: |
           pwd
           ls -al 
           mkdir backend
           cd fullstack
           pwd
           ls -al 

      - name: Remove dist from orm file
        run: |
          pwd
          ls -al 
          sed -i "s|'dist/\*\*/\*.entity.js'|'**/\*.entity.js'|g" mikro-orm.config.js
          echo "++++++++++++++++++++++UPDATED FILE++++++++++++++++++++++"
          cat mikro-orm.config.js

      - name: Create Dockerfile for PROD environment
        if: github.event.inputs.environment == 'prod'
        run: |
          cat << 'EOF' > Dockerfile
          FROM node:16.15.0-slim

          # Set the working directory in the container
          WORKDIR /backend

          COPY package.json /backend/

          RUN npm install

          # Copy the current directory contents into the container
          COPY . /backend

          # Make port 8080 available to the world outside this container
          EXPOSE 8080

          # Run app.py when the container launches
          CMD ["npm", "run", "start"]
          EOF
      
      - name: Create Dockerfile  for DEV environment
        if: github.event.inputs.environment == 'dev'
        run: |
          cat << 'EOF' > Dockerfile
          FROM node:16.15.0-slim

          # Set the working directory in the container
          WORKDIR /backend

          COPY package.json /backend/

          RUN npm install

          # Copy the current directory contents into the container
          COPY . /backend

          # Make port 8080 available to the world outside this container
          EXPOSE 8080

          # Run app.py when the container launches
          CMD ["npm", "run", "deploy:dev"]
          EOF

      - name: Create Dockerfile  for TEST environment
        if: github.event.inputs.environment == 'test'
        run: |
          cat << 'EOF' > Dockerfile
          FROM node:16.15.0-slim

          # Set the working directory in the container
          WORKDIR /backend

          COPY package.json /backend/

          RUN npm install

          # Copy the current directory contents into the container
          COPY . /backend

          # Make port 8080 available to the world outside this container
          EXPOSE 8080

          # Run app.py when the container launches
          CMD ["npm", "run", "deploy:test"]
          EOF

      - name: Print inputs
        run: |
          ls -al
          cat Dockerfile
          echo "Image Tag: ${{ github.event.inputs.backend-release-tag }}"
          echo "Environment: ${{ github.event.inputs.environment }}"

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v1
        with:
          aws-access-key-id: ********************
          aws-secret-access-key: 01/AcOp92YqS6SD6YFXEmlRRE9NRlKHX0qTKvD7J
          aws-region: ${{ env.AWS_REGION }}

      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v1

      - name: Set ECR Repo Environment Variable
        run: |
          ENVIRONMENT=${{ github.event.inputs.environment }}
          if [ "$ENVIRONMENT" == "dev" ]; then
            echo "ECR_REPO_NAME=brompton-energy/backend" >> $GITHUB_ENV
            echo "EKS_CLUSTER_NAME=brompton-energy-eks-dev" >> $GITHUB_ENV
          elif [ "$ENVIRONMENT" == "test" ]; then
            echo "ECR_REPO_NAME=brompton-energy/backend" >> $GITHUB_ENV
            echo "EKS_CLUSTER_NAME=brompton-energy-eks-test" >> $GITHUB_ENV
          elif [ "$ENVIRONMENT" == "prod" ]; then
            echo "ECR_REPO_NAME=brompton-energy/backend" >> $GITHUB_ENV
            echo "EKS_CLUSTER_NAME=brompton-energy-eks-dev" >> $GITHUB_ENV
          fi

      - name: Build, tag, and push image to Amazon ECR
        run: |
          IMAGE_TAG=${{ github.event.inputs.backend-release-tag }}
          echo "IMAGE_TAG: $IMAGE_TAG"
          echo "DOCKERFILE=Dockerfile"
          echo "DOCKER FILE : $DOCKERFILE"
          pwd
          ls -al
          docker build -t $ECR_REPO_NAME:$IMAGE_TAG -f Dockerfile .
          docker tag $ECR_REPO_NAME:$IMAGE_TAG $ECR_REGISTRY/$ECR_REPO_NAME:$IMAGE_TAG
          docker push $ECR_REGISTRY/$ECR_REPO_NAME:$IMAGE_TAG

  job_5:
    name: Deploy on EKS 
    needs: job_3
    runs-on: ubuntu-latest
    environment: ${{ github.event.inputs.environment }}
    steps:
      - name: Current Folder
        run: |
           pwd
           ls -al 
           mkdir fullstack
           cd fullstack

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v1
        with:
            aws-access-key-id: ********************
            aws-secret-access-key: 01/AcOp92YqS6SD6YFXEmlRRE9NRlKHX0qTKvD7J
            aws-region: ${{ env.AWS_REGION }}

      - name: Add Deployment file - DEV
        if: github.event.inputs.environment == 'dev'
        run: |
          cat << 'EOF' > deployment.yaml
          apiVersion: apps/v1
          kind: Deployment
          metadata:
            name: be-backend
            namespace: admin
            labels:
              app: be-backend #deployment label
          spec:
            selector:
              matchLabels:
                app: be-backend # has to match .spec.template.metadata.labels
            replicas: 1 # by default is 1
            minReadySeconds: 10 # by default is 0
            template:
              metadata:
                labels:
                  app: be-backend # has to match .spec.selector.matchLabels
              spec:
                #updateStrategy: rollingUpdate
                volumes:
                  - name: db
                    persistentVolumeClaim:
                      claimName: be-fullstack-v1-claim
                terminationGracePeriodSeconds: 10
                imagePullSecrets:
                  - name: ecr-registry-helper-secrets
                containers:
                - name: be-backend
                  image:  067172429169.dkr.ecr.us-east-1.amazonaws.com/brompton-energy/backend:{{ replaceImageTagHere }}  #repo/image
                  imagePullPolicy: Always
                  ports:
                  - containerPort: 8080
                    name: be-backend
                  volumeMounts:
                  - name: db
                    mountPath: /data
                  env:
                  - name: SERVER_PORT
                    value: "8080"
                  - name: DB_NAME
                    value: "dataloggerdev"
                  - name: DB_HOST
                    value: "bromptonenergy.io"
                  - name: DB_PORT
                    value: "22632"
                  - name: DATABASE_PORT
                    value: "26379"
                  - name: DB_USER
                    value: "postgres"
                  - name: DB_PASSWORD
                    value: "Br0mpt0n!0T"
                  - name: DB_SSL
                    value: "false"
                  - name: REDIS_HOST
                    value: "bromptonenergy.io"
                  - name: REDIS_PORT
                    value: "6379"
                  - name: AUTH_JWT_SECRET
                    value: "this is a very secret secret"
                  - name: AUTH_SESSION_DURATION_MIN
                    value: "120"
                  - name: SECURITY_CORS_ORIGIN_URL
                    value: "http://localhost:3000"
                  - name: TS_API_HOST
                    value: "bromptonenergy.io"
                  - name: TS_API_PORT
                    value: "28666"
                  - name: TS_API_VERSION
                    value: "v1_0"
                  - name: TS_API_SSL
                    value: "false"
                  - name: NODE_TLS_REJECT_UNAUTHORIZED
                    value: "0"
                  - name: TEMPEST_WEATHER_API_TOKEN
                    value: "16f1648e-fd77-4fd4-ba09-b711e7de8733"
                  - name: TEMPEST_WEATHER_API_URL
                    value: "https://swd.weatherflow.com/swd/rest" 
                  - name: NEXT_PUBLIC_BE_ADMIN_API_URL
                    value: "test-api.brompton.ai/v0"
                  resources:
                    requests:
                      memory: "64Mi"  # You can adjust these values based on your application's requirements
                      cpu: "250m"
                    limits:
                      memory: "500Mi"  # You can adjust these values based on your application's requirements
                      cpu: "1"
                affinity: 
                  nodeAffinity:
                    requiredDuringSchedulingIgnoredDuringExecution:
                      nodeSelectorTerms:
                        - matchExpressions:
                            - key: application
                              operator: In
                              values:
                                - fullstack
                            - key: company
                              operator: In
                              values:
                                - brompton-energy
          EOF

      - name: Add Deployment file - TEST
        if: github.event.inputs.environment == 'test'
        run: |
          cat << 'EOF' > deployment.yaml
          apiVersion: apps/v1
          kind: Deployment
          metadata:
            name: be-backend
            namespace: admin
            labels:
              app: be-backend #deployment label
          spec:
            selector:
              matchLabels:
                app: be-backend # has to match .spec.template.metadata.labels
            replicas: 1 # by default is 1
            minReadySeconds: 10 # by default is 0
            template:
              metadata:
                labels:
                  app: be-backend # has to match .spec.selector.matchLabels
              spec:
                #updateStrategy: rollingUpdate
                volumes:
                  - name: db
                    persistentVolumeClaim:
                      claimName: be-fullstack-v1-claim
                terminationGracePeriodSeconds: 10
                imagePullSecrets:
                  - name: ecr-registry-helper-secrets
                containers:
                - name: be-backend
                  image:  067172429169.dkr.ecr.us-east-1.amazonaws.com/brompton-energy/backend:{{ replaceImageTagHere }}  #repo/image
                  imagePullPolicy: Always
                  ports:
                  - containerPort: 8080
                    name: be-backend
                  volumeMounts:
                  - name: db
                    mountPath: /data
                  env:
                  - name: SERVER_PORT
                    value: "8080"
                  - name: DB_NAME
                    value: "dataloggertest"
                  - name: DB_HOST
                    value: "postgres-service"
                  - name: DB_PORT
                    value: "5432"
                  - name: DATABASE_PORT
                    value: "26379"
                  - name: DB_USER
                    value: "postgres"
                  - name: DB_PASSWORD
                    value: "test@123"
                  - name: DB_SSL
                    value: "false"
                  - name: REDIS_HOST
                    value: "redis-service"
                  - name: REDIS_PORT
                    value: "26379"
                  - name: AUTH_JWT_SECRET
                    value: "this is a very secret secret"
                  - name: AUTH_SESSION_DURATION_MIN
                    value: "120"
                  - name: SECURITY_CORS_ORIGIN_URL
                    value: "https://test.brompton.ai"
                  - name: TS_API_HOST
                    value: "af1d2a7a722c247aa8aeeecba8d1d245-191a79e48c6c3408.elb.us-east-1.amazonaws.com"
                  - name: TS_API_PORT
                    value: "80"
                  - name: TS_API_VERSION
                    value: "v1_0"
                  - name: TS_API_SSL
                    value: "false"
                  - name: NODE_TLS_REJECT_UNAUTHORIZED
                    value: "0"
                  - name: TEMPEST_WEATHER_API_TOKEN
                    value: "16f1648e-fd77-4fd4-ba09-b711e7de8733"
                  - name: TEMPEST_WEATHER_API_URL
                    value: "https://swd.weatherflow.com/swd/rest"
                  - name: AUTH_COOKIE_DOMAIN
                    value: "brompton.ai"
                  resources:
                    requests:
                      memory: "64Mi"  # You can adjust these values based on your application's requirements
                      cpu: "250m"
                    limits:
                      memory: "500Mi"  # You can adjust these values based on your application's requirements
                      cpu: "1"
                affinity: 
                  nodeAffinity:
                    requiredDuringSchedulingIgnoredDuringExecution:
                      nodeSelectorTerms:
                        - matchExpressions:
                            - key: application
                              operator: In
                              values:
                                - fullstack
                            - key: company
                              operator: In
                              values:
                                - brompton-energy
          EOF

      - name: Add Deployment file - PROD
        if: github.event.inputs.environment == 'prod'
        run: |
          cat << 'EOF' > deployment.yaml
          apiVersion: apps/v1
          kind: StatefulSet
          metadata:
            name: admin-app
            namespace: admin
            labels:
              app: admin-app #deployment label
          spec:
            selector:
              matchLabels:
                app: admin-app # has to match .spec.template.metadata.labels
            serviceName: "admin-app-service"
            replicas: 1 # by default is 1
            minReadySeconds: 10 # by default is 0
            template:
              metadata:
                labels:
                  app: admin-app # has to match .spec.selector.matchLabels
              spec:
                #updateStrategy: rollingUpdate
                volumes:
                  - name: db
                    persistentVolumeClaim:
                      claimName: admin-app-pvc
                terminationGracePeriodSeconds: 10
                imagePullSecrets:
                  - name: ecr-registry-helper-secrets
                containers:
                - name: admin-app
                  # image: sahil2898/adminapp:v9 #repo/image
                  image: 067172429169.dkr.ecr.us-east-1.amazonaws.com/brompton-energy/backend:{{ replaceImageTagHere }} #repo/image
                  ports:
                  - containerPort: 8080
                    name: admin-app-port
                  volumeMounts:
                  - name: db
                    mountPath: /data
                  env:
                  - name: SERVER_PORT
                    value: "8080"
                  - name: DB_NAME
                    value: "dataloggeraws"
                  - name: DB_HOST
                    value: "dataloggeraws.clclbj3j3ehf.us-east-1.rds.amazonaws.com"
                  - name: DB_PORT
                    value: "5432"
                  - name: DATABASE_PORT
                    value: "26379"
                  - name: DB_USER
                    value: "postgres"
                  - name: DB_PASSWORD
                    value: "Br0mpt0n!0T"
                  - name: DB_SSL
                    value: "true"
                  - name: REDIS_HOST
                    value: "bromptonenergy.io"
                  - name: REDIS_PORT
                    value: "6379"
                  - name: AUTH_JWT_SECRET
                    value: "this is a very secret secret"
                  - name: AUTH_SESSION_DURATION_MIN
                    value: "120"
                  - name: SECURITY_CORS_ORIGIN_URL
                    value: "http://localhost:3000"
                  - name: TS_API_HOST
                    value: "bromptonenergy.io"
                  - name: TS_API_PORT
                    value: "28666"
                  - name: TS_API_VERSION
                    value: "v1_0"
                  - name: TS_API_SSL
                    value: "false"
                  - name: NODE_TLS_REJECT_UNAUTHORIZED
                    value: "0"
                  - name: TEMPEST_WEATHER_API_TOKEN
                    value: "16f1648e-fd77-4fd4-ba09-b711e7de8733"
                  - name: TEMPEST_WEATHER_API_URL
                    value: "https://swd.weatherflow.com/swd/rest"
                  - name: NEXT_PUBLIC_BE_ADMIN_API_URL
                    value: "/v0"
                  resources:
                    requests:
                      memory: "64Mi"  # You can adjust these values based on your application's requirements
                      cpu: "250m"
                    limits:
                      memory: "500Mi"  # You can adjust these values based on your application's requirements
                      cpu: "1"
                affinity: 
                  nodeAffinity:
                    requiredDuringSchedulingIgnoredDuringExecution:
                      nodeSelectorTerms:
                        - matchExpressions:
                            - key: application
                              operator: In
                              values:
                                - fullstack
                            - key: company
                              operator: In
                              values:
                                - brompton-energy
          EOF

      - name: Replace Image Tag
        run: |
          DEPLOYMENT_FILE=deployment.yaml
          echo $DEPLOYMENT_FILE
          pwd
          ls -al
          sed -i "s|{{ replaceImageTagHere }}|${{ github.event.inputs.backend-release-tag }}|g" $DEPLOYMENT_FILE
          cat $DEPLOYMENT_FILE
    
      - name: Setup kubectl
        uses: azure/setup-kubectl@v1
        with:
            version: 'latest'
      
      - name: Set ECR Repo Environment Variable
        run: |
          ENVIRONMENT=${{ github.event.inputs.environment }}
          if [ "$ENVIRONMENT" == "dev" ]; then
            echo "ECR_REPO_NAME=brompton-energy/backend" >> $GITHUB_ENV
            echo "EKS_CLUSTER_NAME=brompton-energy-eks-dev" >> $GITHUB_ENV
          elif [ "$ENVIRONMENT" == "test" ]; then
            echo "ECR_REPO_NAME=brompton-energy/backend" >> $GITHUB_ENV
            echo "EKS_CLUSTER_NAME=brompton-energy-eks-test" >> $GITHUB_ENV
          elif [ "$ENVIRONMENT" == "prod" ]; then
            echo "ECR_REPO_NAME=brompton-energy/backend" >> $GITHUB_ENV
            echo "EKS_CLUSTER_NAME=brompton-energy-eks-dev" >> $GITHUB_ENV
          fi
        
      - name: Update kubectl config
        run: |
          kubectl --help
          echo $EKS_CLUSTER_NAME
          aws eks update-kubeconfig --name $EKS_CLUSTER_NAME --region ${{ env.AWS_REGION }}
    
      - name: Deploy to EKS
        run: |
          DEPLOYMENT_FILE=deployment.yaml
          kubectl apply -f deployment.yaml --validate=false

            
