import { Box, Button, TextField } from '@mui/material';
import CustomDialog from '~/components/common/CustomDialog';
import { Weather, Widget, Widgets } from '~/types/widgets';
import CancelIcon from '@mui/icons-material/Cancel';
import SaveAsIcon from '@mui/icons-material/SaveAs';
import { useState } from 'react';
import { useDispatch } from 'react-redux';
import { dashboardSlice } from '~/redux/slices/dashboardSlice';
import OverRideGlobalSettings, { setSettings } from '~/components/common/OverRideGlobalSettings';

type WeatherWidgetDialogProps = {
  open: boolean;
  onClose: () => void;
  id: string;
  settings: Weather;
};
const WeatherWidgetDialog = ({
  id,
  onClose,
  open,
  settings: weatherSetting,
}: WeatherWidgetDialogProps) => {
  const dispatch = useDispatch();
  const [settings, setSettings] = useState<Weather>(weatherSetting);
  const handleStationIdChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSettings({ ...settings, stationId: event.target.value });
  };
  const onUpdateClick = () => {
    dispatch(
      dashboardSlice.actions.setCurrentWidgetSettings({
        id: id,
        type: 'Weather',
        settings: settings,
      } as Widget),
    );
  };
  return (
    <CustomDialog
      key={id}
      open={open}
      content={
        <>
          <Box>
            <TextField
              name="stationId"
              onChange={handleStationIdChange}
              value={settings.stationId}
              label="Station ID"
              variant="outlined"
              margin="normal"
              fullWidth
            />
            <OverRideGlobalSettings
              setSettings={setSettings as unknown as setSettings}
              settings={settings as unknown as Widgets}
            />
          </Box>
        </>
      }
      dialogActions={
        <>
          <Button
            autoFocus
            sx={{ mr: 'auto' }}
            variant="outlined"
            size="medium"
            startIcon={<CancelIcon />}
            onClick={() => {
              onClose();
              setSettings(weatherSetting);
            }}
          >
            Cancel
          </Button>
          <Button
            variant="contained"
            size="medium"
            startIcon={<SaveAsIcon />}
            onClick={() => {
              onUpdateClick();
              onClose();
            }}
          >
            Update
          </Button>
        </>
      }
      onClose={() => {
        onClose();
        setSettings(weatherSetting);
      }}
      title="Weather Widget Settings"
    />
  );
};

export default WeatherWidgetDialog;
