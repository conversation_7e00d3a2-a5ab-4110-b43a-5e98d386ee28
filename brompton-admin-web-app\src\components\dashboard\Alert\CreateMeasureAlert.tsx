import { yupResolver } from '@hookform/resolvers/yup';
import {
  Alert,
  Autocomplete,
  Box,
  Button,
  Checkbox,
  FormControl,
  FormControlLabel,
  FormLabel,
  Grid,
  Radio,
  RadioGroup,
  TextField,
  Typography,
} from '@mui/material';
import { AnyAction, ThunkDispatch } from '@reduxjs/toolkit';
import { forwardRef, useEffect, useImperativeHandle, useMemo, useState } from 'react';
import { Controller, useFieldArray, useForm } from 'react-hook-form';
import { useDispatch, useSelector } from 'react-redux';
import Loader from '~/components/common/Loader';
import { CustomError } from '~/errors/CustomerErrorResponse';
import {
  AssetMeasurement,
  MeasureAlertsValidationData,
  measureAlertsValidationSchema,
} from '~/measurements/domain/types';
import {
  useCreateAlertMutation,
  useGetAggregationPeriodsQuery,
  useGetConditionsQuery,
  useGetPeriodQuery,
  useGetThresholdTypesQuery,
} from '~/redux/api/alertApi';
import { useGetAllAssetQuery } from '~/redux/api/assetsApi';
import { useGetCustomerUsersByIdMutation } from '~/redux/api/customersApi';
import { measuresApi } from '~/redux/api/measuresApi';
import { getActiveCustomer } from '~/redux/selectors/customerSelectors';
import { getSelectedViewMeasureId } from '~/redux/selectors/treeSelectors';
import { RootState } from '~/redux/store';
import { ControlledAutocomplete } from '~/shared/forms/components/ControlledAutocomplete';
import { ControlledTextField } from '~/shared/forms/components/ControlledTextField';
import { AlertMessage } from '~/shared/forms/types';
import { assetsPathMapper } from '~/utils/utils';

interface CreateMeasureAlertProps {
  setStatus?: (status: { isCreateLoading: boolean; isSuccess: boolean }) => void;
}

const CreateMeasureAlert = forwardRef((props: CreateMeasureAlertProps, ref) => {
  const dispatch = useDispatch<ThunkDispatch<RootState, any, AnyAction>>();
  const activeCustomer = useSelector(getActiveCustomer);
  const SelectedViewMeasureId = useSelector(getSelectedViewMeasureId);
  const customerId = activeCustomer ? Number(activeCustomer.id) : 0;
  const measurementId = SelectedViewMeasureId.split(':');
  const [alertMessage, setAlertMessage] = useState<AlertMessage | undefined>(undefined);
  const [measureData, setMeasureData] = useState<AssetMeasurement[]>([]);
  const [loadingMeasure, setLoadingMeasure] = useState<boolean>(false);

  const { data: thresholds } = useGetThresholdTypesQuery();
  const { data: aggPeriods } = useGetAggregationPeriodsQuery();
  const { data: conditions } = useGetConditionsQuery();
  const { data: periods } = useGetPeriodQuery();
  const [getCustomerUser, { isLoading, data }] = useGetCustomerUsersByIdMutation();

  const [createAlert, { isLoading: isCreateLoading, isSuccess, isError, error }] =
    useCreateAlertMutation();
  const {
    data: assetData,
    isLoading: isAssetLoading,
    isFetching: isAssetReloading,
  } = useGetAllAssetQuery(
    { customerId, parentIds: [] },
    {
      skip: !customerId,
      refetchOnMountOrArgChange: true,
    },
  );
  const assetTypesWithPath = useMemo(() => assetsPathMapper(assetData ?? []), [assetData]);

  useEffect(() => {
    getCustomerUser({
      customer_id: activeCustomer?.id.toString() ?? '',
    });
  }, [activeCustomer]);

  const {
    control,
    handleSubmit,
    watch,
    reset,
    setValue,
    getValues,
    clearErrors,
    setError,
    formState: { errors },
  } = useForm<MeasureAlertsValidationData>({
    defaultValues: {
      asset: undefined,
      aggregate: undefined,
      aggregatePeriod: undefined,
      comparison: undefined,
      measurementId: undefined,
      customerId: activeCustomer?.id.toString() ?? '',
      resetDeadband: undefined,
      thresholdType: undefined,
      thresholdValue: undefined,
      learningPeriod: undefined,
      includeVelocity: undefined,
      includeMomentum: undefined,
      contactNumbers: [{ user: '', notificationType: 'both' }],
    },
    resolver: yupResolver(measureAlertsValidationSchema),
  });
  const asset = watch('asset');
  const aggregate = watch('aggregate');
  const aggregatePeriod = watch('aggregatePeriod');
  useEffect(() => {
    if (aggregate?.label === 'None') {
      clearErrors('aggregatePeriod');
      setValue('aggregatePeriod', undefined);
    } else {
      if (getValues('aggregate') && !getValues('aggregatePeriod')) {
        setError('aggregatePeriod', {
          type: 'required',
          message: 'Aggregate period is required',
        });
      } else {
        clearErrors('aggregatePeriod');
      }
    }
  }, [aggregate, aggregatePeriod, getValues, setError, clearErrors, setValue]);

  useEffect(() => {
    const fetchMeasure = async () => {
      setLoadingMeasure(true);
      const { data: newMeasureData, isSuccess: isMeasureDataSuccess } = await dispatch(
        measuresApi.endpoints?.getAllMeasurements.initiate({
          customerId,
          assetId: Number(asset) ?? 0,
        }),
      );
      setLoadingMeasure(false);
      if (isMeasureDataSuccess) {
        setMeasureData(newMeasureData);
      }
    };
    if (asset) {
      setValue('measurementId', '');
      fetchMeasure();
    }
  }, [asset]);

  useEffect(() => {
    if (isSuccess) {
      setAlertMessage({
        message: `Alert created successfully!`,
        severity: 'success',
      });
      reset();
      reset({
        description: '',
        resetDeadband: undefined,
        thresholdValue: undefined,
        contactNumbers: [
          {
            user: undefined,
            notificationType: undefined,
          },
        ],
      });
    }
    if (isError && error) {
      const err = error as CustomError;
      setAlertMessage({ message: err.data.message ?? 'Server error', severity: 'error' });
    }
  }, [error, isError, isSuccess]);

  useEffect(() => {
    if (props.setStatus) props.setStatus({ isCreateLoading, isSuccess });
  }, [isCreateLoading, isSuccess]);

  const { fields, append, remove } = useFieldArray({
    control,
    name: 'contactNumbers',
  });

  useEffect(() => {
    if (measurementId[2]) {
      setValue('measurementId', measurementId[2]);
    }
  }, [measurementId[2]]);

  const thresholdValue = watch('thresholdValue');
  const resetDeadband = watch('resetDeadband');
  const comparison = watch('comparison');
  const thresholdType = watch('thresholdType');

  let alertMessageText = '';
  if (
    thresholdValue !== undefined &&
    resetDeadband !== undefined &&
    comparison &&
    comparison !== undefined
  ) {
    const condition = conditions?.items.find(
      (cond) => cond.id.toString() === comparison.toString(),
    )?.condition;
    switch (condition) {
      case 'LT':
      case 'LE':
        alertMessageText = `Alert will reset at threshold value reaches to ${
          Number(thresholdValue) + Number(resetDeadband)
        }`;
        break;
      case 'GT':
      case 'GE':
        alertMessageText = `Alert will reset at threshold value reaches to ${
          Number(thresholdValue) - Number(resetDeadband)
        }`;
        break;
      default:
        alertMessageText = ``;
        break;
    }
  }

  useImperativeHandle(ref, () => ({
    submitForm: handleSubmit(async (data) => {
      try {
        await createAlert({
          agg: data.aggregate.value,
          period: data.aggregatePeriod,
          asset: data.asset,
          customerId: activeCustomer?.id.toString() ?? '',
          measurement: data.measurementId,
          condition: data.comparison ?? 0,
          resetDeadband: data.resetDeadband ?? 0,
          thresholdType: data.thresholdType,
          learningPeriod: `${data.learningPeriod} days`,
          includeVelocity: data.includeVelocity,
          includeMomentum: data.includeMomentum,
          thresholdValue: data.thresholdValue ?? 0,
          description: data.description,
          users: data.contactNumbers.map((contact) => ({
            id: Number(contact.user),
            notificationType:
              contact.notificationType === 'email' ? 1 : contact.notificationType === 'sms' ? 2 : 3,
          })),
        });
      } catch (error) {
        console.error(error);
      }
    }),
  }));

  return (
    <Box pl={3} width={'100%'}>
      <Typography variant="h4">Create New Alert</Typography>
      {isAssetLoading && <Loader />}
      {!isAssetLoading && (
        <>
          {/* <Box>
            <Autocomplete
              id="asset-box-demo"
              loading={isAssetReloading}
              options={
                !isAssetReloading
                  ? assetTypesWithPath?.map((asset) => ({
                      label: asset.label,
                      id: Number(asset.value),
                    })) ?? []
                  : []
              }
              getOptionLabel={(option) => option?.label ?? ''}
              onChange={(
                e: React.SyntheticEvent<Element, Event>,
                value: {
                  label: string;
                  id: number;
                }[],
              ) => {
                handleChangeAsset(e, value);
              }}
              value={
                assetData
                  ?.filter((asset) => multipleAssets.includes(asset.id.toString()))
                  .map((asset) => ({ label: asset.tag, id: asset.id })) ?? []
              }
              multiple
              sx={{ my: 2 }}
              renderInput={(params) => <TextField {...params} label="Asset" />}
            />
          </Box> */}
          <Box width={'100%'}>
            <form>
              <ControlledAutocomplete
                control={control}
                fieldName="asset"
                label="Asset"
                loading={isAssetReloading}
                options={
                  !isAssetReloading && assetTypesWithPath
                    ? assetTypesWithPath?.map((asset) => ({
                        label: asset.label,
                        id: asset.value.toString(),
                      })) ?? []
                    : []
                }
              />
              <ControlledAutocomplete
                control={control}
                fieldName="measurementId"
                label="Measurement"
                loading={loadingMeasure}
                options={
                  asset
                    ? measureData?.map((measure) => ({
                        id: measure.measurementId.toString(),
                        label: measure.tag,
                      })) ?? []
                    : []
                }
              />

              <ControlledTextField
                control={control}
                fieldName="description"
                label="Description"
                loading={isAssetLoading}
              />
              <Controller
                name="aggregate"
                control={control}
                render={({ field }) => {
                  const selectedOption = field.value
                    ? aggPeriods?.items
                        ?.filter((aggPeriod) => aggPeriod.label !== 'Total')
                        ?.find((agg) => String(agg.id) === String(field.value?.value)) // Ensure correct value check
                    : null;
                  return (
                    <Autocomplete
                      freeSolo
                      options={
                        aggPeriods?.items
                          ?.filter((aggPeriod) => aggPeriod.label !== 'Total')
                          ?.map((agg) => ({
                            value: agg.id.toString(),
                            label: agg.label,
                          })) || []
                      }
                      getOptionLabel={(option) =>
                        typeof option === 'string' ? option : String(option.label)
                      }
                      isOptionEqualToValue={(option, value) =>
                        option && value ? String(option.value) === String(value?.value) : false
                      }
                      value={selectedOption}
                      onChange={(_, newValue) => {
                        field.onChange(newValue || null); // Ensures controlled behavior
                      }}
                      renderInput={(params) => (
                        <TextField
                          {...params}
                          label="Aggregate"
                          fullWidth
                          margin="normal"
                          error={!!errors?.aggregate?.value || !!errors?.aggregate}
                          helperText={
                            errors?.aggregate?.value?.message || errors?.aggregate?.message || ''
                          }
                          required
                        />
                      )}
                    />
                  );
                }}
              />
              {aggregate?.label !== 'None' ? (
                <ControlledAutocomplete
                  control={control}
                  fieldName="aggregatePeriod"
                  label="Aggregate Period"
                  loading={isAssetLoading}
                  options={
                    periods?.items?.map((period) => ({
                      id: period.id.toString(),
                      label: period.label,
                    })) || []
                  }
                />
              ) : null}
              <ControlledAutocomplete
                control={control}
                fieldName="thresholdType"
                label="Threshold Type"
                loading={isAssetLoading}
                helperText={
                  String(thresholdType) === '3' ? (
                    <Typography variant="caption" sx={{ fontStyle: 'italic' }}>
                      Triggers if the measurement has not changed for the specified number of
                      minutes.
                    </Typography>
                  ) : String(thresholdType) === '4' ? (
                    <Typography variant="caption" sx={{ fontStyle: 'italic' }}>
                      Triggers if no new value has been received for the specified number of
                      minutes.
                    </Typography>
                  ) : null
                }
                options={
                  thresholds?.items.map((type) => ({
                    id: type.id.toString(),
                    label: type.threshold,
                  })) || []
                }
              />
              {thresholdType === '2' && (
                <>
                  <ControlledAutocomplete
                    control={control}
                    fieldName="learningPeriod"
                    label="Learning Days"
                    loading={isAssetLoading}
                    options={[
                      { id: '7', label: '7' },
                      { id: '30', label: '30' },
                      { id: '60', label: '60' },
                      { id: '90', label: '90' },
                      { id: '180', label: '180' },
                      { id: '365', label: '365' },
                    ]}
                  />

                  <Controller
                    name="includeVelocity"
                    control={control}
                    render={({ field }) => (
                      <FormControlLabel
                        control={<Checkbox {...field} checked={!!field.value} />}
                        label="Include Velocity"
                      />
                    )}
                  />

                  <Controller
                    name="includeMomentum"
                    control={control}
                    render={({ field }) => (
                      <FormControlLabel
                        control={<Checkbox {...field} checked={!!field.value} />}
                        label="Include Momentum"
                      />
                    )}
                  />
                </>
              )}

              {thresholdType !== '2' && (
                <>
                  <ControlledAutocomplete
                    control={control}
                    fieldName="comparison"
                    label="Comparision condition"
                    loading={isAssetLoading}
                    options={
                      conditions?.items
                        ?.filter((cond) =>
                          thresholdType === '4' || thresholdType === '3'
                            ? cond.condition === 'GT' || cond.condition === 'GE'
                            : true,
                        )
                        ?.map((cond) => ({
                          id: cond.id.toString(),
                          label: cond.condition,
                        })) || []
                    }
                  />

                  <ControlledTextField
                    control={control}
                    fieldName="thresholdValue"
                    label={
                      thresholdType === '4' || thresholdType === '3'
                        ? 'Duration in Minutes'
                        : 'Threshold Value'
                    }
                    loading={isAssetLoading}
                    type="number"
                  />

                  {thresholdType !== '3' && thresholdType !== '4' && (
                    <ControlledTextField
                      control={control}
                      fieldName="resetDeadband"
                      label="Reset Deadband Value"
                      loading={isAssetLoading}
                    />
                  )}
                </>
              )}

              {thresholdValue !== undefined &&
              resetDeadband !== undefined &&
              comparison !== undefined ? (
                <Alert variant="outlined" severity="info">
                  {alertMessageText}
                </Alert>
              ) : null}
              <Box display={'flex'} justifyContent={'end'} pr={3}>
                <Button
                  variant="contained"
                  onClick={() => append({ user: '', notificationType: 'both' })}
                  sx={{ mb: 2, mt: 2 }}
                >
                  Add Contact
                </Button>
              </Box>
              {fields.map((field, index) => (
                <Grid container spacing={2} key={field.id} alignItems="center">
                  <Grid item xs={12} sm={6} md={4}>
                    <ControlledAutocomplete
                      control={control}
                      fieldName={`contactNumbers.${index}.user`}
                      label={`User ${index + 1}`}
                      loading={isLoading || isAssetLoading}
                      options={
                        data?.items?.map((item) => ({
                          id: item.id.toString(),
                          label: item.username,
                        })) || []
                      }
                    />
                  </Grid>
                  <Grid item xs={12} sm={6} md={4}>
                    <FormControl component="fieldset">
                      <FormLabel component="legend">Notification Type</FormLabel>
                      <Controller
                        name={`contactNumbers.${index}.notificationType`}
                        control={control}
                        render={({ field }) => (
                          <RadioGroup row {...field}>
                            <FormControlLabel value="email" control={<Radio />} label="Email" />
                            <FormControlLabel value="sms" control={<Radio />} label="SMS" />
                            <FormControlLabel value="both" control={<Radio />} label="Both" />
                          </RadioGroup>
                        )}
                      />
                    </FormControl>
                  </Grid>
                  <Grid item xs={12} sm={12} md={4}>
                    <Button
                      variant="outlined"
                      color="error"
                      disabled={fields.length === 1}
                      onClick={() => remove(index)}
                    >
                      Remove
                    </Button>
                  </Grid>
                </Grid>
              ))}
            </form>
          </Box>
        </>
      )}
      {alertMessage && (
        <Alert severity={alertMessage.severity} sx={{ mt: 3 }}>
          {alertMessage.message}
        </Alert>
      )}
    </Box>
  );
});

CreateMeasureAlert.displayName = 'CreateMeasureAlert';
export default CreateMeasureAlert;
