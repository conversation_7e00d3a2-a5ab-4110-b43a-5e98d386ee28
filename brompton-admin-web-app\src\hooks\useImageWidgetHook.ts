import { useSelector } from 'react-redux';
import { getDbMeasureIdToName } from '~/redux/selectors/treeSelectors';
import { ImageWidgetSettings } from '~/types/widgets';
import { ImageWidgetFontsProps } from '~/types/widgets';

type ImageWidgetProps = ImageWidgetFontsProps<ImageWidgetSettings>;
export const useImageWidgetHook = ({ settings, setSettings }: ImageWidgetProps) => {
  const selectedDbMeasureIdToName = useSelector(getDbMeasureIdToName);
  const handleIsEditableChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSettings((prevState) => ({
      ...prevState,
      isEditable: event.target.checked,
    }));
  };
  const handleChangeMeasure = (e: string[]) => {
    setSettings((prevState) => {
      const { selectedTitles, labelAndUnits, ...rest } = prevState;
      const updatedTitles = Array.from(new Set([...selectedTitles, ...e])).filter((title) =>
        e.includes(title),
      );

      const labels: { id: string; name: string }[] = [];
      Object.entries(selectedDbMeasureIdToName).map(([dbMeasureId, name]) => {
        if (updatedTitles.includes(dbMeasureId)) {
          labels.push({ id: dbMeasureId, name: name });
        }
      });
      // pass this lables in labelAndUnits with key as measureId and label as label
      const updatedLabelAndUnits = labels.reduce((acc, label) => {
        return {
          ...acc,
          [label.id]: {
            label: label.name,
            unit: '',
          },
        };
      }, {});
      return {
        ...rest,
        selectedTitles: updatedTitles,
        labelAndUnits: updatedLabelAndUnits,
      };
    });
  };
  return { handleIsEditableChange, handleChangeMeasure };
};
