import { yupResolver } from '@hookform/resolvers/yup';
import {
  Box,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
} from '@mui/material';
import { useForm } from 'react-hook-form';
import { ControlledTextField } from '~/shared/forms/components/ControlledTextField';
import { AssetTemplate, newAssetTemplateSchema } from '~/types/asset';

const NewAssetTemplateContainer = () => {
  const { control, handleSubmit } = useForm<AssetTemplate>({
    defaultValues: {
      tag: '',
      type: '',
      template: '',
      unit_group: '',
      total_output_pressure: 0,
      total_pressure_unit: '',
      total_energy_consumption: 0,
    },
    resolver: yupResolver(newAssetTemplateSchema),
  });
  return (
    <>
      <form
        noValidate
        onSubmit={handleSubmit(async (data) => {
          try {
            console.log(data);
            // await onValidSubmit(data);
            // reset();
          } catch (err) {}
        })}
      >
        <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
          <ControlledTextField
            control={control}
            fieldName="tag"
            label="Tag"
            required
            loading={false}
          />
          <ControlledTextField
            control={control}
            fieldName="type"
            label="Type"
            required
            loading={false}
          />
          <ControlledTextField
            control={control}
            fieldName="template"
            label="Template"
            required
            loading={false}
          />
          <ControlledTextField
            control={control}
            fieldName="unit_group"
            label="Unit Group"
            required
            loading={false}
          />
          <TableContainer
            // component={Paper}
            sx={{
              '& .MuiTableHead-root': {
                backgroundColor: '#f5f5f5',
              },
              '.MuiTableCell-root': {
                borderBottom: 'none',
              },
            }}
          >
            <Table sx={{ minWidth: 650 }} aria-label="simple table">
              <TableHead>
                <TableRow>
                  <TableCell align="center">Metric</TableCell>
                  <TableCell align="center">Unit of Measure</TableCell>
                  <TableCell align="center">Meter Factor</TableCell>
                  <TableCell align="center">Tag</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                <TableRow sx={{ '&:last-child td, &:last-child th': { border: 0 } }}>
                  <TableCell scope="row" align="center">
                    Total Output Pressure
                  </TableCell>
                  <TableCell align="center">
                    <ControlledTextField
                      control={control}
                      fieldName="total_output_pressure"
                      label=""
                      type="number"
                      required
                      loading={false}
                    />
                  </TableCell>
                  <TableCell align="center">1.0</TableCell>
                  <TableCell align="center">
                    <ControlledTextField
                      control={control}
                      fieldName="total_pressure_unit"
                      label=""
                      required
                      loading={false}
                    />
                  </TableCell>
                </TableRow>
                <TableRow sx={{ '&:last-child td, &:last-child th': { border: 0 } }}>
                  <TableCell component="th" scope="row" align="center">
                    Total Energy Consumption
                  </TableCell>
                  <TableCell align="center">
                    <ControlledTextField
                      control={control}
                      fieldName="total_energy_consumption"
                      label=""
                      required
                      loading={false}
                    />
                  </TableCell>
                  <TableCell align="center">N/A</TableCell>
                  <TableCell align="center">
                    <ControlledTextField
                      control={control}
                      fieldName="na_total_energy"
                      label=""
                      required
                      loading={false}
                    />
                  </TableCell>
                </TableRow>
              </TableBody>
            </Table>
          </TableContainer>
        </Box>
        <Button
          type="submit"
          variant="contained"
          size="large"
          sx={{ mt: 2, width: 200, float: 'right' }}
        >
          Submit
        </Button>
      </form>
    </>
  );
};

export default NewAssetTemplateContainer;
