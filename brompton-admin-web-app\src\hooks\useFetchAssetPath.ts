import { ThunkDispatch } from '@reduxjs/toolkit';
import { useDispatch, useSelector } from 'react-redux';
import { Asset } from '~/types/asset';
import { RootState } from '~/redux/store';
import { useEffect, useState } from 'react';
import { assetsApi } from '~/redux/api/assetsApi';
import { getCustomerId } from '~/redux/selectors/customerSelectors';
const useFetchAssetPath = (parentAsset: Asset) => {
  const dispatch = useDispatch<ThunkDispatch<RootState, any, any>>();
  const customerId = useSelector(getCustomerId);
  const [assetPath, setAssetPath] = useState<string>('');
  useEffect(() => {
    const fetchData = async () => {
      const assetsPath = [];
      const path = [];
      if (parentAsset.parent_ids) {
        let parentIDs = parentAsset.parent_ids.filter((id) => id !== undefined) as number[];
        while (parentIDs.length > 0) {
          for (const parentId of parentIDs) {
            const { data, isError } = await dispatch(
              assetsApi.endpoints.getAssetById.initiate({
                customerId: customerId,
                assetId: parentId.toString(),
              }),
            );
            if (!data || isError) return;
            parentIDs = data.parent_ids.filter((id) => id !== undefined) as number[];
            assetsPath.push(data);
          }
        }
        for (const asset of assetsPath.reverse()) {
          path.push(asset.tag);
        }
        path.push(parentAsset.tag);
        setAssetPath(path.join('\\'));
      }
    };
    fetchData();
  }, [parentAsset]);
  return assetPath;
};

export default useFetchAssetPath;
