import CancelIcon from '@mui/icons-material/Cancel';
import EditIcon from '@mui/icons-material/Edit';
import {
  Box,
  Button,
  Divider,
  IconButton,
  Tab,
  Tabs,
  TextField,
  Typography,
  useMediaQuery,
  useTheme,
} from '@mui/material';
import { useRouter } from 'next/router';
import { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useHasAdminAccess } from '~/hooks/useHasAdminAccess';
import { useHasPowerUserAccess } from '~/hooks/useHasPowerUserAccess';
import { Role, useRolePermission } from '~/hooks/useRolePermission';
import { authApi, useLogoutUserMutation } from '~/redux/api/authApi';
import { useEditDashboardMutation } from '~/redux/api/dashboardApi';
import { getActiveCustomer } from '~/redux/selectors/customerSelectors';
import {
  getCurrentDashboardId,
  getMainPanel,
  isDashboardDirty,
  selectDashboardState,
} from '~/redux/selectors/dashboardSelectors';
import { getDeletedWidgets, getWidgets } from '~/redux/selectors/widgetSelectors';
import { dashboardSlice } from '~/redux/slices/dashboardSlice';
import { AlertSnackbar } from '~/shared/snackbars/components/AlertSnackbar';
import { useSnackbar } from '~/shared/snackbars/snackbar-hooks';
import { DashboardCollection, DashboardState } from '~/types/dashboard';
import { hexToRgbA } from '~/utils/utils';
import CustomDialog from '../CustomDialog';

type FavoriteTabsProps = {
  value: number;
  handleChange: (event: React.SyntheticEvent, value: number) => void;
  dashboardId: number;
  dashboardList: DashboardCollection | undefined;
  currentDashboardTitle: string;
};
const FavoriteTabs = ({
  currentDashboardTitle,
  dashboardId,
  dashboardList,
  handleChange,
  value,
}: FavoriteTabsProps) => {
  const theme = useTheme();
  const mobile = useMediaQuery('@media (max-width: 600px)');
  const [logoutUser] = useLogoutUserMutation();
  const { logout } = dashboardSlice.actions;
  const dispatch = useDispatch();
  const dashboardState = useSelector(selectDashboardState);
  const isDashboardStateDirty = useSelector(isDashboardDirty);
  const widgets = useSelector(getWidgets);
  const deleteWidgets = useSelector(getDeletedWidgets);
  const mainPanel = useSelector(getMainPanel);
  const activeCustomer = useSelector(getActiveCustomer);
  const currentDashboard = useSelector(getCurrentDashboardId);
  const [confirm, setConfirm] = useState(false);
  const [navigation, setNavigation] = useState<string | null>(null);
  const [hoveredTab, setHoveredTab] = useState<number | null>(null);
  const [editingTabId, setEditingTabId] = useState<number | null>(null);
  const [editedTitles, setEditedTitles] = useState<Record<number, string>>({});
  const [snackbarState, showSuccessAlert, showErrorAlert] = useSnackbar();

  const router = useRouter();
  const { hasDashboardPermission } = useRolePermission();
  const { globalAdmin, admin } = useHasAdminAccess();
  const hasPowerUserAccess = useHasPowerUserAccess();
  const handleDashboardChange = () => {
    if (dashboardId === -2) {
      return false; // If the dashboardId is -2, do not show confirmation dialog
    }
    if (
      mainPanel === 'chart' &&
      activeCustomer &&
      (widgets.filter((widget) => widget.type !== 'chart' && widget.settings.isDirty).length > 0 ||
        deleteWidgets.length > 0 ||
        widgets.filter((widget) => widget.type === 'chart' && widget.settings?.settings.isDirty)
          .length > 0 ||
        isDashboardStateDirty) &&
      currentDashboard > -1 &&
      hasDashboardPermission('dashboard.update', Role.POWER_USER)
    ) {
      setConfirm(true);
      return true;
    }
    return false;
  };
  const handleChangeTab = (event: React.SyntheticEvent, value: number) => {
    if (handleDashboardChange()) {
      dispatch(dashboardSlice.actions.setCurrentDashboardId(value));
      dispatch(dashboardSlice.actions.setCurrentDashboardTitle(''));
      setNavigation(`/customer/${activeCustomer?.id}/dashboard/${value}`);
      return;
    }
    handleChange(event, value);
  };
  const [
    updateDashboard,
    {
      data: editDashboard,
      isSuccess: isEditSuccess,
      isError: isEditError,
      isLoading: isEditLoading,
      error,
    },
  ] = useEditDashboardMutation();

  useEffect(() => {
    if (isEditSuccess) {
      showSuccessAlert('Dashboard title updated');
    }
    if (isEditError) {
      if (error) {
        if ('data' in error) {
          showErrorAlert((error.data as any)?.exception ?? 'Error updating dashboard title');
        }
      }
      showErrorAlert('Error updating dashboard title');
    }
  }, [isEditSuccess, isEditError]);

  const handleEditClick = (dashboardId: number, currentTitle: string) => {
    setEditingTabId(dashboardId);
    setEditedTitles((prev) => ({
      ...prev,
      [dashboardId]: currentTitle, // Initialize the title for the selected dashboard
    }));
  };

  const handleInputChange = (dashboardId: number, value: string) => {
    const maxLength = 30;
    const sanitizedValue = value.replace(/[^a-zA-Z0-9\s]/g, ''); // Allow spaces, alphanumeric characters

    if (sanitizedValue.length > maxLength) {
      showErrorAlert('Title should not exceed 30 characters');
      return;
    }

    setEditedTitles((prev) => ({
      ...prev,
      [dashboardId]: sanitizedValue,
    }));
  };

  const handleInputBlur = (dashboardId: number) => {
    const updatedTitle = editedTitles[dashboardId]?.trim();
    const previousTitle = dashboardList?.items.find(
      (dashboard) => dashboard.id === dashboardId,
    )?.title;

    if (!updatedTitle || updatedTitle === previousTitle) {
      setEditingTabId(null);
      setEditedTitles((prev) => ({
        ...prev,
        [dashboardId]: previousTitle || '',
      }));
      return;
    }

    const dashboardStateToSave: DashboardState = {
      ...dashboardState,
      dashboardTitle: updatedTitle,
      isDirty: false,
      topPanel: {
        ...dashboardState.topPanel,
      },
      widget: {
        ...dashboardState.widget,
        deleteWidgets: [],
        widgets: widgets,
      },
      tree: {
        ...dashboardState.tree,
      },
    };

    updateDashboard({
      title: updatedTitle,
      data: dashboardStateToSave,
      id: dashboardId,
      description: '',
      customerId: activeCustomer?.id ?? 0,
    });

    setEditingTabId(null);
  };

  const handleInputKeyDown = (e: React.KeyboardEvent, dashboardId: number) => {
    if (e.key === 'Enter') {
      handleInputBlur(dashboardId);
    } else if (e.key === 'Escape') {
      setEditedTitles((prev) => ({
        ...prev,
        [dashboardId]: '',
      }));
      setEditingTabId(null);
    }
  };

  return (
    <>
      {dashboardId > 0 && (
        <>
          <AlertSnackbar {...snackbarState} />
          <Tabs
            value={value}
            onChange={handleChangeTab}
            variant="scrollable"
            indicatorColor="secondary"
            aria-label="wrapped label tabs example"
            sx={{
              minHeight: 40,
              '& .MuiTabs-indicator': {
                backgroundColor: theme.palette.primary.main,
              },
              '& .Mui-selected': {
                backgroundColor: `rgba(${hexToRgbA(theme.palette.primary.main)}, 0.08)`,
                color: theme.palette.primary.main,
                borderRadius: '1px',
              },
              '& .MuiTab-root': {
                padding: 0,
                paddingLeft: 1,
                paddingRight: 1,
                height: 40,
                minHeight: 40,
                gap: 1,
                textTransform: 'none',
              },
              '& .MuiButtonBase-root': {
                padding: '8px 20px',
              },
              '& .MuiTabs-scrollButtons.Mui-disabled': {
                display: 'none',
              },
            }}
          >
            {dashboardList &&
              dashboardList.items
                .filter((dashboard) => dashboard.favorite || dashboard.default)
                .find((dashboard) => dashboard.id === dashboardId) === undefined && (
                <Tab
                  value={dashboardId}
                  label={
                    <Box display="flex" alignItems="center">
                      {editingTabId === dashboardId ? (
                        <TextField
                          value={editedTitles[dashboardId] || currentDashboardTitle}
                          onChange={(e) => handleInputChange(dashboardId, e.target.value)}
                          onBlur={() => handleInputBlur(dashboardId)}
                          onKeyDown={(e) => handleInputKeyDown(e, dashboardId)}
                          autoFocus
                          size="small"
                          variant="standard"
                          sx={{ width: '100%', zIndex: 2 }}
                        />
                      ) : (
                        <Typography variant="body2" noWrap>
                          {editedTitles[dashboardId] || currentDashboardTitle}
                        </Typography>
                      )}

                      {hoveredTab === dashboardId && editingTabId !== dashboardId && !mobile && (
                        <IconButton
                          size="small"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleEditClick(dashboardId, currentDashboardTitle);
                          }}
                        >
                          <EditIcon fontSize="small" />
                        </IconButton>
                      )}
                    </Box>
                  }
                  sx={{
                    borderWidth: 1,
                    borderStyle: 'solid',
                    borderColor: theme.palette.divider,
                  }}
                  onMouseEnter={() => {
                    if (globalAdmin || admin || hasPowerUserAccess) {
                      setHoveredTab(dashboardId);
                    }
                  }}
                  onMouseLeave={() => setHoveredTab(null)}
                  disableRipple
                />
              )}

            {dashboardList &&
              dashboardList.items
                .filter((dashboard) => dashboard.default)
                .map((dashboard) => (
                  <Tab
                    key={dashboard.id}
                    value={dashboard.id}
                    label={
                      <Box display="flex" alignItems="center">
                        {editingTabId === dashboard.id ? (
                          <TextField
                            value={editedTitles[dashboard.id] || dashboard.title}
                            onChange={(e) => handleInputChange(dashboard.id, e.target.value)}
                            onBlur={() => handleInputBlur(dashboard.id)}
                            onKeyDown={(e) => handleInputKeyDown(e, dashboard.id)}
                            autoFocus
                            size="small"
                            variant="standard"
                            sx={{ width: '100%', zIndex: 2 }}
                          />
                        ) : (
                          <Typography variant="body2" noWrap>
                            {editingTabId === null && isEditError
                              ? dashboard.title
                              : editedTitles[dashboard.id] || dashboard.title}
                          </Typography>
                        )}

                        {hoveredTab === dashboard.id &&
                          editingTabId !== dashboard.id &&
                          !mobile && (
                            <IconButton
                              size="small"
                              onClick={(e) => {
                                e.stopPropagation();
                                handleEditClick(dashboard.id, dashboard.title);
                              }}
                            >
                              <EditIcon fontSize="small" />
                            </IconButton>
                          )}
                      </Box>
                    }
                    disableRipple
                    onMouseEnter={() => {
                      if (globalAdmin || admin || hasPowerUserAccess) {
                        setHoveredTab(dashboard.id);
                      }
                    }}
                    onMouseLeave={() => setHoveredTab(null)}
                    sx={{
                      borderWidth: 1,
                      borderStyle: 'solid',
                      borderColor: theme.palette.divider,
                    }}
                  />
                ))}

            {dashboardList &&
              dashboardList.items
                .filter((dashboard) => dashboard.favorite && !dashboard.default)
                .map((dashboard) => (
                  <Tab
                    disableRipple
                    key={dashboard.id}
                    value={dashboard.id}
                    label={
                      <Box display="flex" alignItems="center">
                        {editingTabId === dashboard.id ? (
                          <TextField
                            value={editedTitles[dashboard.id] || dashboard.title}
                            onChange={(e) => handleInputChange(dashboard.id, e.target.value)}
                            onBlur={() => handleInputBlur(dashboard.id)}
                            onKeyDown={(e) => handleInputKeyDown(e, dashboard.id)}
                            autoFocus
                            size="small"
                            variant="standard"
                            sx={{ width: '100%', zIndex: 2 }}
                          />
                        ) : (
                          <Typography variant="body2" noWrap>
                            {editingTabId === null && isEditError
                              ? dashboard.title
                              : editedTitles[dashboard.id] || dashboard.title}
                          </Typography>
                        )}

                        {hoveredTab === dashboard.id &&
                          editingTabId !== dashboard.id &&
                          !mobile && (
                            <IconButton
                              size="small"
                              onClick={(e) => {
                                e.stopPropagation();
                                handleEditClick(dashboard.id, dashboard.title);
                              }}
                            >
                              <EditIcon fontSize="small" />
                            </IconButton>
                          )}
                      </Box>
                    }
                    onMouseEnter={() => {
                      if (globalAdmin || admin || hasPowerUserAccess) {
                        setHoveredTab(dashboard.id);
                      }
                    }}
                    onMouseLeave={() => setHoveredTab(null)}
                    sx={{
                      borderWidth: 1,
                      borderStyle: 'solid',
                      borderColor: theme.palette.divider,
                    }}
                  />
                ))}
          </Tabs>

          <Divider
            sx={{
              ml: 5,
            }}
          />
        </>
      )}
      <CustomDialog
        title="You have unsaved changes."
        content={<Typography color={'error'}>Do you want to still proceed?</Typography>}
        dialogActions={
          <>
            <Button
              onClick={() => {
                setConfirm(false);
                setNavigation(null);
              }}
              variant="outlined"
              startIcon={<CancelIcon />}
            >
              Cancel
            </Button>
            <Button
              onClick={() => {
                setConfirm(false);
                router.push(navigation as string);
                if (navigation === '/login') {
                  router.push('/login');
                  logoutUser();
                  dispatch(logout());
                  dispatch(authApi.util.resetApiState());
                }
                setNavigation(null);
              }}
              variant="contained"
              color="error"
            >
              Proceed
            </Button>
          </>
        }
        onClose={() => setConfirm(false)}
        open={confirm}
      />
    </>
  );
};
export default FavoriteTabs;
