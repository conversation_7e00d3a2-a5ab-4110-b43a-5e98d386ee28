import {
  Alert,
  Box,
  Button,
  Card,
  Checkbox,
  FormControl,
  FormControlLabel,
  FormLabel,
  MenuItem,
  Select,
  SelectChangeEvent,
  Typography,
} from '@mui/material';
import { DatePicker } from '@mui/x-date-pickers';
import dayjs from 'dayjs';
import { SetStateAction, useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import Loader from '~/components/common/Loader';
import { CustomError } from '~/errors/CustomerErrorResponse';
import { EditAssetMeasurementForm, NewAssetMeasurement } from '~/measurements/domain/types';
import { assetsApi } from '~/redux/api/assetsApi';
import {
  useCreateTimeVaringFactorScheduleMutation,
  useEditTimeVaringFactorScheduleMutation,
  useGetFactorByMeasureIdQuery,
  useGetFactorsQuery,
} from '~/redux/api/factorApi';
import {
  measuresApi,
  useCreateMeasurementMutation,
  useEditMeasureMutation,
} from '~/redux/api/measuresApi';
import { getActiveCustomer } from '~/redux/selectors/customerSelectors';
import { AlertMessage } from '~/shared/forms/types';
import { Asset } from '~/types/asset';
import { factorScheduleTime } from '~/types/factor';
import DayTime from './DayTime';
import { getDateTimeFormat } from '~/redux/selectors/userPreferences';

type MeasureTimeVaringFactorProps = {
  parentAsset: Asset;
  measure: NewAssetMeasurement | EditAssetMeasurementForm;
  assetPath: string;
  isEditFlow?: boolean;
  setFactor: (value: SetStateAction<boolean>) => void;
};
const MeasureTimeVaringFactor = ({
  measure,
  parentAsset,
  assetPath,
  isEditFlow,
  setFactor,
}: MeasureTimeVaringFactorProps) => {
  const dispatch = useDispatch();
  const activeCustomer = useSelector(getActiveCustomer);
  const dateTimeFormat = useSelector(getDateTimeFormat);
  const [selectedAssetType, setSelectedAssetType] = useState<number | null>(null);
  const [isSeasonal, setIsSeasonal] = useState<boolean>(false);
  const [alertMessage, setAlertMessage] = useState<AlertMessage | undefined>(undefined);
  const [varingMessage, setVaringMessage] = useState<AlertMessage | undefined>(undefined);
  const [createMeasurement, { data: assetMeasurement, isSuccess, error, isError, isLoading }] =
    useCreateMeasurementMutation();
  const [effective_dates, setEffectiveDates] = useState<
    {
      effectiveDate: string;
      rows: { day: number; time: string; value: number }[];
    }[]
  >([]);
  const { isFetching, data: factors } = useGetFactorsQuery(undefined, {
    refetchOnMountOrArgChange: true,
  });
  const [
    createTimeVaringFactor,
    { data, isSuccess: isVaringSuccess, error: varingError, isLoading: varingLoading },
  ] = useCreateTimeVaringFactorScheduleMutation();
  const [
    editTimeVaringFactor,
    { data: editData, isSuccess: isEditSuccess, error: editError, isLoading: editFactorLoading },
  ] = useEditTimeVaringFactorScheduleMutation();
  const [
    editMeasurement,
    {
      data: editAssetMeasurement,
      isSuccess: successFull,
      error: errorData,
      isError: haveError,
      isLoading: editLoading,
    },
  ] = useEditMeasureMutation();
  const { data: timeVaringFactorToUpdate } = useGetFactorByMeasureIdQuery(
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    //@ts-ignore
    measure?.measurement_id ?? 0,
    {
      skip: !isEditFlow,
      refetchOnMountOrArgChange: true,
    },
  );
  useEffect(() => {
    if (timeVaringFactorToUpdate && isEditFlow) {
      setSelectedAssetType(timeVaringFactorToUpdate.factorType);
      setIsSeasonal(timeVaringFactorToUpdate.seasonal);
      const dates =
        timeVaringFactorToUpdate.factorTimeOfDayValue
          ?.filter((time) => time.factorTimeOfDayValue.length > 0)
          ?.map((time) => {
            return {
              effectiveDate: new Date(time.effectiveDate).toISOString().split('T')[0],
              factorTimeOfDayValue: time.factorTimeOfDayValue,
            };
          }) ?? [];
      const effectiveDates = dates?.map((curr: any) => {
        const effectiveDate = curr.effectiveDate;
        const rows =
          curr.factorTimeOfDayValue.map((time: factorScheduleTime) => {
            return {
              effectiveDate,
              time: time.timeOfDay,
              day: time.weekday,
              value: time.value,
            };
          }) ?? [];
        return {
          effectiveDate,
          rows,
        };
      });
      setEffectiveDates(effectiveDates);
    }
  }, [timeVaringFactorToUpdate, isEditFlow]);

  useEffect(() => {
    if (successFull) {
      setAlertMessage({
        message: 'Measure updated successfully!',
        severity: 'success',
      });
      const factorSchedule = effective_dates.map((date) => {
        const localDate = new Date(date.effectiveDate);
        const year = localDate.getFullYear();
        const month = String(localDate.getMonth() + 1).padStart(2, '0'); // Add 1 because months are 0-indexed
        const day = String(localDate.getDate()).padStart(2, '0');
        return {
          effectiveDate: `${year}-${month}-${day}`, // Format as YYYY-MM-DD
        };
      });
      const timeVaryingFactor = {
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        //@ts-ignore
        measurement: measure.measurement_id,
        seasonal: isSeasonal,
        factorType: selectedAssetType as number,
      };
      const factorTimeOfDayValue = effective_dates.flatMap((date) => {
        return date.rows.flatMap((row: { day: number; time: string; value: number }) => {
          const localDate = new Date(date.effectiveDate);
          const year = localDate.getFullYear();
          const month = String(localDate.getMonth() + 1).padStart(2, '0'); // Months are 0-indexed, so add 1
          const day = String(localDate.getDate()).padStart(2, '0');
          return {
            effectiveDate: `${year}-${month}-${day}`, // Format as YYYY-MM-DD
            timeOfDay: row.time,
            weekday: row.day,
            value: row.value,
          };
        });
      });
      editTimeVaringFactor({
        id: timeVaringFactorToUpdate?.id ?? 0,
        factorSchedule,
        factorTimeOfDayValue,
        timeVaryingFactor,
      });
    }
    if (editError) {
      const err = editError as CustomError;
      setAlertMessage({ message: err.data.message ?? 'Server error', severity: 'error' });
    }
  }, [editAssetMeasurement, editError, haveError, parentAsset.id]);

  useEffect(() => {
    if (isEditSuccess) {
      setVaringMessage({
        message: 'Factor schedule updated successfully!',
        severity: 'success',
      });
    }
    if (editError) {
      const err = editError as CustomError;
      setVaringMessage({ message: err.data.message ?? 'Server error', severity: 'error' });
    }
  }, [editData, isEditSuccess, editError]);
  useEffect(() => {
    if (isVaringSuccess) {
      setVaringMessage({
        message: 'Factor schedule created successfully!',
        severity: 'success',
      });
    }
    if (varingError) {
      const err = varingError as CustomError;
      setVaringMessage({ message: err.data.message ?? 'Server error', severity: 'error' });
    }
  }, [data, isVaringSuccess, varingError, varingLoading]);
  const onSave = () => {
    if (isEditFlow) {
      editMeasurement({
        assetId: parentAsset.id.toString(),
        customerId: activeCustomer?.id ?? 0,
        measId: (measure as EditAssetMeasurementForm)?.id.toString() ?? '',
        editAssetMeasurement: {
          ...measure,
          tag: (assetPath === '' ? parentAsset.tag : assetPath) + '\\' + measure.tag,
        } as EditAssetMeasurementForm,
      });
    } else {
      createMeasurement({
        assetId: parentAsset.id,
        assetMeasurement: {
          ...measure,
          tag: (assetPath === '' ? parentAsset.tag : assetPath) + '\\' + measure.tag,
        } as NewAssetMeasurement,
        customerId: activeCustomer?.id ?? 0,
      });
    }
  };
  useEffect(() => {
    if (isSuccess && assetMeasurement) {
      setAlertMessage({
        message: `Asset measurement "${assetMeasurement.tag}" is created successfully!`,
        severity: 'success',
      });
      dispatch(assetsApi.util.invalidateTags([{ type: 'Asset', id: parentAsset.id }]));
      dispatch(assetsApi.util.invalidateTags([{ type: 'Asset' }]));
      dispatch(measuresApi.util.invalidateTags(['Measure']));
      const factorSchedule = effective_dates.map((date) => {
        const localDate = new Date(date.effectiveDate);
        const year = localDate.getFullYear();
        const month = String(localDate.getMonth() + 1).padStart(2, '0'); // Add 1 because months are 0-indexed
        const day = String(localDate.getDate()).padStart(2, '0');
        return {
          effectiveDate: `${year}-${month}-${day}`, // Format as YYYY-MM-DD
        };
      });
      const timeVaryingFactor = {
        measurement: assetMeasurement.measurementId,
        seasonal: isSeasonal,
        factorType: selectedAssetType as number,
      };
      const factorTimeOfDayValue = effective_dates.flatMap((date) => {
        return date.rows.flatMap((row: { day: number; time: string; value: number }) => {
          const localDate = new Date(date.effectiveDate);
          const year = localDate.getFullYear();
          const month = String(localDate.getMonth() + 1).padStart(2, '0'); // Months are 0-indexed, so add 1
          const day = String(localDate.getDate()).padStart(2, '0');
          return {
            effectiveDate: `${year}-${month}-${day}`, // Format as YYYY-MM-DD
            timeOfDay: row.time,
            weekday: row.day,
            value: row.value,
          };
        });
      });
      createTimeVaringFactor({
        factorSchedule,
        factorTimeOfDayValue,
        timeVaryingFactor,
      });
    }
    if (isError && error) {
      const err = error as CustomError;
      setAlertMessage({ message: err.data.message ?? 'Server error', severity: 'error' });
    }
  }, [assetMeasurement, error, isError, isSuccess, parentAsset.id]);
  const isUniqueEffectiveDates =
    new Set(effective_dates.map((date) => date.effectiveDate)).size === effective_dates.length;
  return (
    <Box pl={3}>
      <Box sx={{ display: 'flex', width: '100%', justifyContent: 'space-between' }}>
        <Box>
          <Typography>Measure Name :{measure.tag}</Typography>
          <Typography>Parent :{parentAsset.tag}</Typography>
        </Box>
      </Box>
      {isFetching ? (
        <Loader />
      ) : (
        <>
          <Box display={'flex'} justifyContent={'space-between'}>
            <FormControl
              sx={{
                mt: 2,
                minWidth: 600,
              }}
            >
              <Select
                label="Factor Type"
                sx={{
                  width: '100%',
                  p: 0.3,
                  '& fieldset': {
                    '& legend': {
                      maxWidth: '100%',
                      height: 'auto',
                      '& span': {
                        opacity: 1,
                      },
                    },
                  },
                  '.MuiInputBase-input': {
                    padding: '16.5px 14px',
                  },
                }}
                value={selectedAssetType?.toString()}
                onChange={(e: SelectChangeEvent<string>) =>
                  setSelectedAssetType(parseInt(e.target.value))
                }
              >
                {factors?.items?.map((factor) => (
                  <MenuItem key={factor.id} value={factor.id}>
                    {factor.name}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
            <FormControlLabel
              control={
                <Checkbox checked={isSeasonal} onChange={(e) => setIsSeasonal(e.target.checked)} />
              }
              label="Is Seasonal ?"
            />
          </Box>
          <Box mt={2} display={'flex'} justifyContent={'end'}>
            <Button
              variant="contained"
              color="primary"
              onClick={() => {
                setEffectiveDates([
                  ...effective_dates,
                  {
                    effectiveDate: '',
                    rows: [
                      {
                        day: 1,
                        time: '',
                        value: 0,
                      },
                    ],
                  },
                ]);
              }}
            >
              Add Effective Date
            </Button>
          </Box>
          {effective_dates.map((date, index) => (
            <>
              <Box key={index} pt={3} p={2} mt={2} component={Card}>
                <Box display={'flex'} alignItems={'center'} justifyContent={'space-between'}>
                  <Box display={'flex'} alignItems={'center'}>
                    <FormLabel>Effective Date : </FormLabel>

                    <DatePicker
                      sx={{
                        '.MuiInputBase-input': {
                          padding: '16.5px 14px',
                        },
                        ml: 2,
                      }}
                      label="Date"
                      value={date.effectiveDate ? dayjs(date.effectiveDate) : null}
                      format={dateTimeFormat.split(' ')[0]}
                      onChange={(newValue) => {
                        setEffectiveDates((prev) => {
                          return prev.map((d, index) =>
                            index === effective_dates.indexOf(date)
                              ? {
                                  effectiveDate: newValue ? newValue.toISOString() : '',
                                  rows: d.rows,
                                }
                              : d,
                          );
                        });
                      }}
                    />
                  </Box>
                  <Button
                    variant="contained"
                    color="error"
                    onClick={() => {
                      setEffectiveDates((prev) => {
                        return prev.filter((_, i) => i !== index);
                      });
                    }}
                  >
                    Delete
                  </Button>
                </Box>
                <DayTime
                  index={index}
                  effective_dates={effective_dates}
                  rows={date.rows}
                  setEffectiveDates={setEffectiveDates}
                />
              </Box>
            </>
          ))}
          {effective_dates.length > 0 && !isUniqueEffectiveDates ? (
            <Alert severity="error">
              Effective dates should be unique. Please remove duplicate effective dates.
            </Alert>
          ) : null}
          <Box mt={5} sx={{ display: 'flex', gap: 2 }}>
            <Button color="primary" variant="outlined" onClick={() => setFactor(false)}>
              Previous
            </Button>
            <Button
              variant="contained"
              onClick={onSave}
              disabled={
                effective_dates.length === 0 ||
                effective_dates.some((date) =>
                  date.rows.some(
                    (row) =>
                      row.time === '' ||
                      row.value === 0 ||
                      row.value === null ||
                      row.value === undefined ||
                      row.day === -1 ||
                      row.day === null,
                  ),
                ) ||
                !isUniqueEffectiveDates ||
                editLoading ||
                varingLoading ||
                editFactorLoading ||
                isLoading ||
                isSuccess ||
                isVaringSuccess ||
                !selectedAssetType ||
                !effective_dates.length
              }
            >
              Submit
            </Button>
          </Box>
          {alertMessage && (
            <Alert severity={alertMessage.severity} sx={{ mt: 3, mb: 3 }}>
              {alertMessage.message}
            </Alert>
          )}
          {varingMessage && (
            <Alert severity={varingMessage.severity} sx={{ mt: 3, mb: 3 }}>
              {varingMessage.message}
            </Alert>
          )}
        </>
      )}
    </Box>
  );
};

export default MeasureTimeVaringFactor;
