import { Autocomplete, TextField, TextFieldProps } from '@mui/material';
import { Control, Controller, FieldPath, FieldValues } from 'react-hook-form';

type AutocompleteOption = {
  id: string;
  label: string;
};

type ControlledAutocompleteProps<
  TFieldValues extends FieldValues,
  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,
> = {
  control: Control<TFieldValues>;
  loading: boolean;
  fieldName: TName;
  options: AutocompleteOption[];
  disabled?: boolean;
} & TextFieldProps;

export const ControlledAutocomplete = <TFieldValues extends FieldValues>(
  props: ControlledAutocompleteProps<TFieldValues>,
) => {
  const { control, fieldName, options, loading, disabled, ...rest } = props;
  return (
    <Controller
      name={fieldName}
      control={control}
      render={({ field: { onChange, onBlur, value }, fieldState }) => (
        <>
          <Autocomplete
            options={options}
            renderInput={(params) => (
              <TextField
                {...params}
                error={!!fieldState.error}
                helperText={fieldState.error?.message}
                {...rest}
              />
            )}
            onChange={(_, value, reason) => {
              if (reason === 'clear') {
                onChange(null);
                return;
              }
              onChange(value?.id);
            }}
            onBlur={onBlur}
            value={
              options.find((option) => option.id === value || option.id === value?.toString()) ??
              null
            }
            sx={{ mt: 2, mb: 1 }}
            disabled={loading || disabled}
            fullWidth
          />
        </>
      )}
    />
  );
};
