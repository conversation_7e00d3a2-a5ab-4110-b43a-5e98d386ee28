{"compilerOptions": {"target": "es5", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "strict": true, "forceConsistentCasingInFileNames": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "baseUrl": ".", "paths": {"~/*": ["./src/*"], "@/*": ["./src/*"]}}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", "tests/test.spec.js", "tests/url.spec.js", "tests/UITestcases/Login.spec.js", "tests/UITestcases/Login.spec.js", "tests/UITestcases/page.spec.js", "tests/Log.spec.js", "POM1/pagedetails.js", "tests/POM1/pagedetails.spec.js"], "exclude": ["node_modules"]}