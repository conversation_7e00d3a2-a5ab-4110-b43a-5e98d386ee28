import { Box, Button, Container, Typography } from '@mui/material';
import Link from 'next/link';
import { CalcEngineList } from '~/components/CalcEngine/CalcEngineList';
import PageName from '~/components/common/PageName/PageName';
import { useRolePermission } from '~/hooks/useRolePermission';

const CalcEnginePage: React.FC = () => {
  const { hasPermission } = useRolePermission();
  return (
    <Container
      sx={{
        pt: 3,
        maxWidth: '100%',
        width: '100%',
        '@media (min-width: 1200px)': { width: '100%', maxWidth: '100%' },
      }}
    >
      <Box pb={0} pt={0} sx={{ display: 'flex' }}>
        <Box
          sx={{
            flexGrow: 1,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
          }}
        >
          <PageName name="Expression Templates" />
          <Box sx={{ display: 'flex', gap: 2 }}>
            {hasPermission('calculation-engine.create') ? (
              <Box display={'flex'} justifyContent={'end'}>
                <Typography component={Link} href={'./expression-template'}>
                  <Button variant="contained" color="primary" sx={{ float: 'right', mb: 1 }}>
                    Add Expression Template
                  </Button>
                </Typography>
              </Box>
            ) : null}
          </Box>
        </Box>
      </Box>

      <CalcEngineList />
    </Container>
  );
};

export default CalcEnginePage;
