import React, { Component } from 'react';
import PropTypes from 'prop-types';
import DateRange from './DateRange';
import DefinedRange from './DefinedRange';
import { findNextRangeIndex, generateStyles } from '../utils.js';
import classnames from 'classnames';
import coreStyles from '../styles';

class DateRangePicker extends Component {
  constructor(props) {
    super(props);
    this.state = {
      focusedRange: [findNextRangeIndex(props.ranges), 0],
    };
    this.styles = generateStyles([coreStyles, props.classNames]);
  }
  render() {
    const focusedRangeIndex = this.state.focusedRange[0];
    return (
      <div className={classnames(this.styles.dateRangePickerWrapper, this.props.className)}>
        <DefinedRange
          {...this.props}
          range={this.props.ranges[focusedRangeIndex] || {}}
          onPreviewChange={value => {
            this.dateRange.updatePreview(value);
          }}
          focusedRangeIndex={focusedRangeIndex}
          className={undefined}
        />
        <DateRange
          {...this.props}
          ref={t => {
            this.dateRange = t;
          }}
          onRangeFocusChange={focusedRange => this.setState({ focusedRange })}
          className={undefined}
        />
      </div>
    );
  }
}

DateRangePicker.defaultProps = {};

DateRangePicker.propTypes = {
  ...DateRange.propTypes,
  ...DefinedRange.propTypes,
  className: PropTypes.string,
};

export default DateRangePicker;
