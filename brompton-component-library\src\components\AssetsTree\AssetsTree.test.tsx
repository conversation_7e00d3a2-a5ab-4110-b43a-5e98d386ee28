import { render, screen, within } from '@testing-library/react';

import { composeStories } from '@storybook/testing-react';
import * as stories from 'components/AssetsTree/AssetsTree.stories';
import React from 'react';
import userEvent from '@testing-library/user-event';

const {
  RootNodeTree,
  SingleChildTree,
  FirstLevelTree,
  SecondLevelTree,
  FirstLevelTreeWithUncheckedMetric,
  FirstLevelTreeWithCheckedMetric,
  NoCheckboxThreeLevelTree,
} = composeStories(stories);

describe('AssetsTree', () => {
  describe('no checkbox three level tree', () => {
    beforeEach(() => {
      render(<NoCheckboxThreeLevelTree />);
    });

    it('should not have checkboxes', () => {
      const checkbox = screen.queryByRole('checkbox');

      expect(checkbox).toBeNull();
    });
  });

  describe('single node tree root', () => {
    let root: HTMLElement;

    beforeEach(() => {
      render(<RootNodeTree />);
      root = screen.getByRole('tree');
    });

    it('should exist', () => {
      expect(root).not.toBeNull();
    });

    it('should have one child', () => {
      expect(root.childElementCount).toBe(1);
    });
  });

  describe('single child tree', () => {
    let root: HTMLElement;
    const nodeToggleHandler = jest.fn();

    beforeEach(() => {
      render(<SingleChildTree onNodeToggle={nodeToggleHandler} />);
      root = screen.getByRole('tree');
    });

    describe('root', () => {
      it('should have a child', () => {
        expect(root.childElementCount).toBe(1);
      });

      describe('first level child', () => {
        let child: HTMLElement;
        beforeEach(() => {
          child = within(root).getAllByRole('treeitem')[1];
        });

        it('should exist', () => {
          expect(child).not.toBeNull();
        });

        it('should have a label with its tag', () => {
          expect(within(child).getByText('child')).not.toBeNull();
        });

        describe('when clicked', () => {
          it('should call node toggle handler with the child id', async () => {
            await userEvent.click(within(child).getByRole('button'));

            expect(nodeToggleHandler).toBeCalled();
            expect(nodeToggleHandler.mock.lastCall[0]).toStrictEqual([
              'ID0',
              'ID',
            ]);
          });
        });
      });
    });
  });

  describe('two children tree', () => {
    let rootTreeItem: HTMLElement;

    beforeEach(() => {
      render(<FirstLevelTree />);
      rootTreeItem = screen.getAllByRole('treeitem')[0];
    });

    describe('root', () => {
      it('should have two children', () => {
        expect(within(rootTreeItem).getAllByRole('treeitem').length).toBe(2);
      });
    });
  });

  describe('expanded grandchild tree', () => {
    let rootTreeItem: HTMLElement;
    const nodeToggleHandler = jest.fn();

    beforeEach(() => {
      render(<SecondLevelTree onNodeToggle={nodeToggleHandler} />);
      rootTreeItem = screen.getAllByRole('treeitem')[0];
    });

    describe('root', () => {
      let child: HTMLElement;
      beforeEach(() => {
        child = within(rootTreeItem).getAllByRole('treeitem')[0];
      });
      it('should have one grandchild', () => {
        expect(within(child).getByRole('treeitem')).not.toBeNull();
      });

      describe('when child is collapsed', () => {
        it('should call node toggle handler without the child id', async () => {
          await userEvent.click(within(child).getAllByRole('button')[0]);

          expect(nodeToggleHandler).toBeCalled();
          expect(nodeToggleHandler.mock.lastCall[0]).toStrictEqual(['ID0']);
        });
      });
    });
  });

  describe('unchecked metric tree', () => {
    let rootTreeItem: HTMLElement;
    const selectHandler = jest.fn();

    beforeEach(() => {
      render(<FirstLevelTreeWithUncheckedMetric onSelect={selectHandler} />);
      rootTreeItem = screen.getAllByRole('treeitem')[0];
    });

    describe('root', () => {
      it('should have 2 children', () => {
        expect(rootTreeItem.childElementCount).toBe(2);
      });

      describe('metric child', () => {
        let sibling: HTMLElement;
        let child: HTMLElement;
        beforeEach(() => {
          sibling = within(rootTreeItem).getAllByRole('treeitem')[0];
          child = within(rootTreeItem).getAllByRole('treeitem')[1];
        });

        it('should exist', () => {
          expect(child).not.toBeNull();
        });

        it('should have a label with its tag', () => {
          expect(within(child).getByText('child 2')).not.toBeNull();
        });

        describe('when child clicked', () => {
          it('should call select handler with the child id and type', async () => {
            await userEvent.click(within(child).getByRole('checkbox'));

            expect(selectHandler).toBeCalled();
            expect(selectHandler.mock.lastCall[0]).toStrictEqual([
              { id: 'ID2', tag: 'child 2', type: 'metric' },
            ]);
          });
        });

        describe('when sibling clicked', () => {
          it('should call select handler with the sibling id and type', async () => {
            await userEvent.click(within(sibling).getByRole('checkbox'));

            expect(selectHandler).toBeCalled();
            expect(selectHandler.mock.lastCall[0]).toStrictEqual([
              { id: 'ID1', tag: 'child 1', type: 'activo' },
            ]);
          });
        });
      });
    });
  });

  describe('checked metric tree', () => {
    let rootTreeItem: HTMLElement;
    const selectHandler = jest.fn();

    beforeEach(() => {
      render(<FirstLevelTreeWithCheckedMetric onSelect={selectHandler} />);
      rootTreeItem = screen.getAllByRole('treeitem')[0];
    });

    describe('metric child', () => {
      let child: HTMLElement;
      beforeEach(() => {
        child = within(rootTreeItem).getAllByRole('treeitem')[1];
      });

      it('should exist', () => {
        expect(child).not.toBeNull();
      });

      describe('when clicked', () => {
        it('should call select handler without the child id', async () => {
          await userEvent.click(within(child).getByRole('checkbox'));

          expect(selectHandler).toBeCalled();
          expect(selectHandler.mock.lastCall[0]).toStrictEqual([]);
        });
      });
    });
  });
});
