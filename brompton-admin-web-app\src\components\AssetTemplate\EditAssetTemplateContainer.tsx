import { Alert, Box, Container } from '@mui/material';
import { SerializedError } from '@reduxjs/toolkit';
import { FetchBaseQueryError } from '@reduxjs/toolkit/query';
import PageName from '~/components/common/PageName/PageName';
import { getAssetTemplateDataWithMetricName } from '~/measurements/domain/types';
import Loader from '../common/Loader';
import EditAssetTemplateForm from './EditAssetTemplateForm';
type EditAssetTemplateProps = {
  error: FetchBaseQueryError | SerializedError | undefined;
  assetTemplate: string | undefined;
  data: getAssetTemplateDataWithMetricName | undefined;
  isFetching: boolean;
};
const EditASsetTemplateContainer = ({
  data,
  isFetching,
  assetTemplate,
  error,
}: EditAssetTemplateProps) => {
  return (
    <Container
      sx={{
        mt: 2,
      }}
      maxWidth="xl"
    >
      <Box p={2} pb={0} pt={0} sx={{ display: 'flex' }}>
        <Box
          sx={{
            flexGrow: 1,
            display: 'flex',
            alignItems: 'center',
          }}
        >
          <PageName name="Edit Asset template" />
        </Box>
      </Box>

      {error ? (
        <Alert severity="error" sx={{ mt: 2 }}>
          No Asset template found
        </Alert>
      ) : (
        <>
          {isFetching ? (
            <Loader />
          ) : (
            <EditAssetTemplateForm
              data={data}
              assetTemplate={assetTemplate}
              isFetching={isFetching}
            />
          )}
        </>
      )}
    </Container>
  );
};

export default EditASsetTemplateContainer;
