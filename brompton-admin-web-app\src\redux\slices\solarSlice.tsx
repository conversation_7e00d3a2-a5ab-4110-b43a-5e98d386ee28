import { createSlice } from '@reduxjs/toolkit';

export interface NavDrawerInitialState {
  isLoad: boolean;
  solarEnergy: number;
  buildingEnergy: number;
  jsonFile: string;
}

const initialState: NavDrawerInitialState = {
  isLoad: false,
  solarEnergy: 10,
  buildingEnergy: 10,
  jsonFile: '',
};

export const solarSlice = createSlice({
  name: 'solar',
  initialState: initialState,
  reducers: {
    setIsLoad(state, value) {
      state.isLoad = value.payload;
    },

    setSolarEnergy(state, value) {
      state.solarEnergy = value.payload;
    },
    setBuildingEnergy(state, value) {
      state.buildingEnergy = value.payload;
    },
    setJsonFile(state, value) {
      state.jsonFile = value.payload;
    },
  },
});

export const { setIsLoad, setSolarEnergy, setBuildingEnergy, setJsonFile } = solarSlice.actions;

// export default solarSlice.reducer;
