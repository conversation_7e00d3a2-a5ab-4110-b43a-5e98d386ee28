import { AssetMeasurement, MeasurementType } from '~/measurements/domain/types';
import { Asset } from '../../types/asset';
import { normalizeAsset } from './asset-normalizer';
import { AssetWithMeasurements } from './asset-tree-mapper';

describe('Asset normalizer', () => {
  const temperatureMeasurementType: MeasurementType = {
    id: 9,
    name: 'Temperature',
  };

  const lightMeasurementType: MeasurementType = {
    id: 11,
    name: 'Light',
  };

  const office: Asset = {
    id: 43,
    tag: 'Office',
    parentIds: [],
    childrenIds: [],
    assetTypeId: 34,
  };

  const officeTemperatureMeasurement: AssetMeasurement = {
    id: 34,
    tag: 'Office temperature',
    description: 'temperature in the room',
    dataTypeId: 45,
    valueTypeId: 25,
    measurementId: 8,
    typeId: 9,
  };

  const measurementTypes: Map<number, string> = new Map([
    [lightMeasurementType.id, lightMeasurementType.name],
    [temperatureMeasurementType.id, temperatureMeasurementType.name],
  ]);
  let normalizedAsset: AssetWithMeasurements;

  describe('given asset and no measurements', () => {
    beforeAll(() => {
      normalizedAsset = normalizeAsset(measurementTypes, office, []);
    });

    it('should normalize with an empty list of measurement groups', () => {
      expect(normalizedAsset.measurementGroups.length).toBe(0);
    });

    it('should have asset data', () => {
      expect(normalizedAsset.id).toBe(43);
      expect(normalizedAsset.tag).toBe('Office');
      expect(normalizedAsset.parentIds).toStrictEqual([]);
      expect(normalizedAsset.childrenIds).toStrictEqual([]);
      expect(normalizedAsset.assetTypeId).toBe(34);
    });
  });

  describe('given asset with one measurement', () => {
    beforeAll(() => {
      normalizedAsset = normalizeAsset(measurementTypes, office, [officeTemperatureMeasurement]);
    });

    it('should normalize with one measurement group', () => {
      expect(normalizedAsset.measurementGroups.length).toBe(1);
    });

    it('should contain a temperature measurement group', () => {
      expect(normalizedAsset.measurementGroups[0].type).toBe('Temperature');
    });

    it('should contain one measurement inside temperature group', () => {
      expect(normalizedAsset.measurementGroups[0].measurements.length).toBe(1);
    });

    it('should contain office temperature measurement', () => {
      expect(normalizedAsset.measurementGroups[0].measurements[0]).toBe(
        officeTemperatureMeasurement,
      );
    });
  });

  describe('given asset with two measurement belonging to same group', () => {
    const outdoorsOfficeTemperatureMeasurement: AssetMeasurement = {
      id: 37,
      tag: 'Outdoors Office temperature',
      description: 'temperature outside the room',
      dataTypeId: 45,
      valueTypeId: 25,
      measurementId: 8,
      typeId: 9,
    };

    beforeAll(() => {
      normalizedAsset = normalizeAsset(measurementTypes, office, [
        officeTemperatureMeasurement,
        outdoorsOfficeTemperatureMeasurement,
      ]);
    });

    it('should normalize with one measurement group', () => {
      expect(normalizedAsset.measurementGroups.length).toBe(1);
    });

    it('should contain a temperature measurement group', () => {
      expect(normalizedAsset.measurementGroups[0].type).toBe('Temperature');
    });

    it('should contain two measurements inside temperature group', () => {
      expect(normalizedAsset.measurementGroups[0].measurements.length).toBe(2);
    });

    it('should contain inside and outdoors office temperature measurements', () => {
      expect(normalizedAsset.measurementGroups[0].measurements[0]).toBe(
        officeTemperatureMeasurement,
      );
      expect(normalizedAsset.measurementGroups[0].measurements[1]).toBe(
        outdoorsOfficeTemperatureMeasurement,
      );
    });
  });

  describe('given asset with two measurement belonging to different groups', () => {
    const officeLightMeasurement: AssetMeasurement = {
      id: 37,
      tag: 'Office light',
      description: 'light in room',
      dataTypeId: 45,
      valueTypeId: 25,
      measurementId: 22,
      typeId: 11,
    };

    beforeAll(() => {
      normalizedAsset = normalizeAsset(measurementTypes, office, [
        officeTemperatureMeasurement,
        officeLightMeasurement,
      ]);
    });

    it('should normalize with two measurement groups', () => {
      expect(normalizedAsset.measurementGroups.length).toBe(2);
    });

    it('should contain a temperature and light measurement group', () => {
      expect(normalizedAsset.measurementGroups[0].type).toBe('Temperature');
      expect(normalizedAsset.measurementGroups[1].type).toBe('Light');
    });

    it('should contain light and temperature office measurements in each group', () => {
      expect(normalizedAsset.measurementGroups[0].measurements[0]).toBe(
        officeTemperatureMeasurement,
      );
      expect(normalizedAsset.measurementGroups[1].measurements[0]).toBe(officeLightMeasurement);
    });
  });
});
