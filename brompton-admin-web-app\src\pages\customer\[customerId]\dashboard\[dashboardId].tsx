import {
  ChevronLeft as ChevronLeftIcon,
  ChevronRight as ChevronRightIcon,
} from '@mui/icons-material';
import CancelIcon from '@mui/icons-material/Cancel';
import NoEncryptionIcon from '@mui/icons-material/NoEncryption';
import {
  Box,
  Button,
  IconButton,
  Paper,
  Stack,
  Typography,
  useMediaQuery,
  useTheme,
} from '@mui/material';
import { useRouter } from 'next/router';
import React, { useEffect, useMemo, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import AssetToTemplate from '~/components/AssetToTemplate/AssetToTemplate';
import CustomDialog from '~/components/common/CustomDialog';
import DashboardsCrumb from '~/components/common/DashboardsCrumb/DashboardCrumbs';
import FavoriteTabs from '~/components/common/FavouriteTabs/FavouriteTabs';
import Loader from '~/components/common/Loader';
import RightSideDrawer from '~/components/common/RightSideDrawer';
import CreateMeasureAlert from '~/components/dashboard/Alert/CreateMeasureAlert';
import MeasureAlerts from '~/components/dashboard/Alert/MeasureAlerts';
import AssetDashboardTemplate from '~/components/dashboard/AssetDashboardTemplate';
import AssetTemplateInstance from '~/components/dashboard/AssetTemplateInstance';
import DashboardWidgetsIcons from '~/components/dashboard/DashboardWidgetsIcons';
import { LeftPanel } from '~/components/dashboard/LeftPanel';
import { MainAssetDetails } from '~/components/dashboard/MainAssetDetails';
import { MainCreateNewAsset } from '~/components/dashboard/MainCreateNewAsset';
import { MainCreateNewMeasure } from '~/components/dashboard/MainCreateNewMeasure';
import { MainMeasureDetails } from '~/components/dashboard/MainMeasureDetails';
import { TopPanel } from '~/components/dashboard/TopPanel';
import { CustomError } from '~/errors/CustomerErrorResponse';
import ErrorBoundary from '~/errors/ErrorBoundry';
import { Role, useRolePermission } from '~/hooks/useRolePermission';
import { WidgetContainer } from '~/layout/containers/WidgetContainer';
import { useGetEventQuery } from '~/redux/api/alertApi';
import { useGetCustomersQuery } from '~/redux/api/customersApi';
import {
  useGetDashboardByCustomerIdQuery,
  useGetUserDashboardDetailsQuery,
} from '~/redux/api/dashboardApi';
import { getActiveCustomer } from '~/redux/selectors/customerSelectors';
import {
  getDashboardCrumb,
  getIsFullScreen,
  getIsLeftPanelOpen,
  getIsRightPanelOpen,
  getMainPanel,
  getTopPanelVisibility,
} from '~/redux/selectors/dashboardSelectors';
import { getWidgets, getWidgetsLayout } from '~/redux/selectors/widgetSelectors';
import { dashboardSlice, DEFAULT_SCATTER_CHART_SETTINGS } from '~/redux/slices/dashboardSlice';
import { Customer } from '~/types/customers';
import { AggByOptions, DashboardCollection, SamplePeriodOptions } from '~/types/dashboard';
import { WidgetType } from '~/types/widgets';
import { findDeleteMeasures, getMeasurementsFromTree } from '~/utils/utils';
const Dashboard: React.FC = () => {
  const dispatch = useDispatch();
  const isLeftPanelOpen = useSelector(getIsLeftPanelOpen);
  const activeCustomer = useSelector(getActiveCustomer);
  const fullScreen = useSelector(getIsFullScreen);
  const mainPanel = useSelector(getMainPanel);
  const isTopPanel = useSelector(getTopPanelVisibility);
  const rightPanelOpen = useSelector(getIsRightPanelOpen);
  const crumbs = useSelector(getDashboardCrumb);
  const router = useRouter();
  const { manageAsset } = router.query;
  const [confirm, setConfirm] = useState<boolean>(false);
  const [customer, setCustomer] = useState<Customer | null>(null);
  const { hasDashboardPermission } = useRolePermission();
  const theme = useTheme();
  const {
    dashboardId: currentDashboardId,
    gtr: globalTimeRange,
    s: startDate,
    e: endDate,
    sample_period: url_sample_period,
    event_id,
  } = router.query;
  const dashboardId = parseInt(currentDashboardId as string);
  const widgets = useSelector(getWidgets);
  const widgetLayout = useSelector(getWidgetsLayout);
  const [value, setValue] = useState<number>(dashboardId);
  useEffect(() => {
    setValue(dashboardId);
  }, [dashboardId]);
  const heightLeft = useMemo(() => {
    if (!isTopPanel) {
      return 'calc(100vh - 25px)';
    }
    return 'calc(100vh - 25px)';
  }, [isTopPanel]);

  const heightMain = useMemo(() => {
    if (!isTopPanel) {
      return 'calc(100vh - 25px)';
    }
    return 'calc(100vh - 25px)';
  }, [isTopPanel]);

  const {
    data: customerList,
    isSuccess: isCustomerListSuccess,
    isLoading: isCustomerListLoading,
  } = useGetCustomersQuery({});

  const {
    data: dashboardList,
    isLoading: isLoadingDashboards,
    isFetching: isFetchingDashboards,
  } = useGetDashboardByCustomerIdQuery(
    {
      customerId: activeCustomer?.id ?? 0,
      search: null,
    },
    {
      skip: !activeCustomer?.id,
      refetchOnMountOrArgChange: true,
    },
  );
  const {
    data: eventsData,
    error: eventsError,
    isError: isEventsError,
  } = useGetEventQuery(
    {
      eventID: event_id as string,
    },
    {
      skip: !event_id || event_id === undefined,
    },
  );
  const {
    isLoading: isDashboardDetailsLoading,
    isSuccess: isDashboardDetailsSuccess,
    data: dashboardDetails,
    isError: isDashboardDetailsError,
    isFetching: isDashboardDetailsFetching,
  } = useGetUserDashboardDetailsQuery(
    {
      dashboardId: dashboardId,
      customerId: activeCustomer?.id ?? 0,
    },
    {
      skip:
        dashboardId <= 0 ||
        isNaN(dashboardId) ||
        dashboardList?.items.find((d) => d.id === dashboardId) === undefined ||
        mainPanel === 'dashboard-template',
      refetchOnMountOrArgChange: true,
    },
  );

  useEffect(() => {
    if (eventsError) {
      const err = eventsError as CustomError;
      if ('statusCode' in err.data) {
        if (err.data['statusCode'] === 401 || err.data['statusCode'] === 403) {
          router.push(`/unauthorized`);
        }
        if (err.data['statusCode'] === 404) {
          router.push(`/not-found`);
        }
      }
    }
  }, [eventsError, isEventsError]);

  const isSmallScreen = useMediaQuery('(max-width: 768px)');
  const isShortHeight = useMediaQuery('(max-height: 500px)');
  const isTouchDevice =
    typeof window !== 'undefined' && ('ontouchstart' in window || navigator.maxTouchPoints > 0);

  const isMobile = (isSmallScreen || isShortHeight) && isTouchDevice;

  useEffect(() => {
    if (event_id && eventsData && customerList) {
      if (!customerList.find((c) => c.id === eventsData.alert_id.customerId)) {
        router.push(`/unauthorized`);
        return;
      }
      const timeData = new Date(eventsData.timestamp).getTime();
      const endDate = timeData + 1000 * 60 * 60 * 1;
      const startDate = timeData - 1000 * 60 * 60 * 4;
      dispatch(dashboardSlice.actions.setNewDashboard());
      dispatch(
        dashboardSlice.actions.setTimeRange({
          timeRangeType: Number(0),
          startDate: Number(startDate),
          endDate: Number(endDate),
        }),
      );
      dispatch(
        dashboardSlice.actions.addWidget({
          widgetMode: 'dashboard',
          type: 'chart-Scatter' as WidgetType,
          layout: [
            {
              i: '0',
              x: 0,
              y: 0,
              h: 6,
              w: 10,
              isBounded: true,
              isDraggable: true,
              isResizable: true,
            },
          ],
          layoutItem: {
            h: 6,
            i: '0',
            w: 10,
            x: 0,
            y: 0,
            isBounded: true,
            isDraggable: true,
            isResizable: true,
          },
        }),
      );
      let condition;
      switch (eventsData.comparator.condition) {
        case 'GT':
          condition = '(>)';
          break;
        case 'GE':
          condition = '(>=)';
          break;
        case 'LT':
          condition = '(<)';
          break;
        case 'LE':
          condition = '(<=)';
          break;
        default:
          condition = '';
          break;
      }
      const agg = AggByOptions.find(
        (agg) => agg.label.toUpperCase() === eventsData.aggregate.label?.toUpperCase(),
      );
      dispatch(
        dashboardSlice.actions.setCurrentWidgetSettings({
          id: '1',
          type: 'chart',
          settings: {
            chartType: 'scatter',
            settings: {
              ...DEFAULT_SCATTER_CHART_SETTINGS,
              selectedTitles: [
                ...DEFAULT_SCATTER_CHART_SETTINGS.selectedTitles,
                eventsData?.asset_measurement?.id?.toString() ?? '',
              ],
              assetMeasure: [
                {
                  assetId: eventsData.asset_id.id.toString(),
                  measureId: [eventsData?.asset_measurement?.id?.toString() ?? ''],
                },
              ],
              // aggBy: AggByOptions[eventsData.aggregate.id].value,
              aggBy: agg?.value ?? 0,
              samplePeriod: SamplePeriodOptions[eventsData.period.id].value,
              showThreshold: true,
              treshdold: {
                ...DEFAULT_SCATTER_CHART_SETTINGS.treshdold,
                thresholdName: `Limit${condition !== '' ? condition : ''}`,
                thresholdValue: eventsData.limit,
              },
            },
          },
        }),
      );
      dispatch(
        dashboardSlice.actions.selectCheckbox({
          assetId: eventsData.asset_id.id.toString(),
          metricId: eventsData?.asset_measurement?.id?.toString() ?? '',

          metricName: (eventsData?.asset_measurement?.tag?.toString() as string) ?? '',
        }),
      );
      dispatch(
        dashboardSlice.actions.setExpandedNodeIds([
          '-1',
          eventsData.asset_id.id.toString(),
          ...(eventsData.asset_id.parent_ids
            ? eventsData.asset_id.parent_ids.map((id) => id.toString())
            : []),
          'm:' +
            eventsData.asset_id.id.toString() +
            ':' +
            eventsData?.asset_measurement?.id?.toString(),
        ]),
      );
      dispatch(
        dashboardSlice.actions.setSelectedNodeIds([
          'm:' +
            eventsData.asset_id.id.toString() +
            ':' +
            eventsData?.asset_measurement?.id?.toString(),
        ]),
      );
      dispatch(dashboardSlice.actions.setAssetTz(false));
      router.push(`/customer/${activeCustomer?.id}/dashboard/${dashboardId}`);
    } else if (dashboardId === 0) {
      dispatch(dashboardSlice.actions.setNewDashboard());
    } else if (
      isDashboardDetailsSuccess &&
      dashboardDetails &&
      dashboardId === dashboardDetails.id
    ) {
      const {
        isLeftPanelOpen,
        widget,
        tree,
        topPanel,
        chart,
        rightSideBar,
        rightSideBarActiveTab,
        template,
        fullScreen: isStateFullScreen,
        kisok,
        desktopMobile,
        responsiveLayouts,
      } = dashboardDetails.data;
      const dashboardTitle = dashboardDetails.title;
      dispatch(dashboardSlice.actions.setDesktopMobileMode(desktopMobile ?? 0));
      dispatch(
        dashboardSlice.actions.setResponsiveLayouts({
          desktop: {
            widgetLayout: responsiveLayouts?.desktop?.widgetLayout ?? [],
            widgets: responsiveLayouts?.desktop?.widgets ?? [],
          },
          mobile: {
            widgetLayout: responsiveLayouts?.mobile?.widgetLayout ?? [],
            widgets: responsiveLayouts?.mobile?.widgets ?? [],
          },
        }),
      );
      dispatch(dashboardSlice.actions.setCurrentDashboardId(dashboardDetails.id));
      dispatch(dashboardSlice.actions.setCurrentDashboardTitle(dashboardTitle));
      if (globalTimeRange) {
        dispatch(
          dashboardSlice.actions.setTimeRange({
            timeRangeType: Number(globalTimeRange),
            startDate: Number(startDate),
            endDate: Number(endDate),
          }),
        );
      } else {
        dispatch(dashboardSlice.actions.setChartStartDate(new Date(chart.startDate)));
        dispatch(dashboardSlice.actions.setChartEndDate(new Date(chart.endDate)));
        dispatch(dashboardSlice.actions.setTimeRangeType(topPanel.timeRangeType));
      }
      if (url_sample_period) {
        dispatch(dashboardSlice.actions.setSamplePeriod(Number(url_sample_period)));
      } else {
        dispatch(dashboardSlice.actions.setSamplePeriod(topPanel.samplePeriod));
      }
      dispatch(
        dashboardSlice.actions.setTemplate({
          metrics: template.metrics,
          idToName: template.idToName,
          assetType: template.assetType,
          assetTemplate: template.assetTemplate,
        }),
      );
      dispatch(dashboardSlice.actions.setRefreshTimeInterval(topPanel.refreshInterval));
      dispatch(dashboardSlice.actions.setTopPanelVisibility(topPanel.isVisible));
      dispatch(dashboardSlice.actions.setAssetTz(isMobile ? false : topPanel.assetTz));
      dispatch(dashboardSlice.actions.setCurrentSelectedNodeId(tree.currentSelectedNodeId));
      dispatch(dashboardSlice.actions.setSelectedNodeIds([...tree.selectedNodeIds]));
      dispatch(dashboardSlice.actions.setExpandedNodeIds([...tree.expandedNodeIds]));
      dispatch(dashboardSlice.actions.unSelectAllCheckbox());
      dispatch(dashboardSlice.actions.setIsDirty(false));
      Object.entries(tree.dbMeasureIdToName).map(([measureId, measureName]) => {
        dispatch(
          dashboardSlice.actions.selectCheckbox({
            assetId: '',
            metricId: measureId,
            metricName: measureName,
          }),
        );
      });
      dispatch(dashboardSlice.actions.setIsLeftPanelOpen(isLeftPanelOpen));
      dispatch(dashboardSlice.actions.setRightSideBar(rightSideBar));
      dispatch(dashboardSlice.actions.setRightSideBarActiveTab(rightSideBarActiveTab));
      // dispatch(dashboardSlice.actions.setFullScreen(isStateFullScreen));
      dispatch(dashboardSlice.actions.setWidget(widget));
      dispatch(dashboardSlice.actions.setIsDirty(false));
      const selectedNodesList = getMeasurementsFromTree(tree.selectedNodeIds);
      findDeleteMeasures(widget.widgets, selectedNodesList).map((measure) => {
        if (measure)
          dispatch(dashboardSlice.actions.unselectCheckbox({ assetId: '', metricId: measure }));
      });
    }
  }, [
    dashboardDetails,
    isDashboardDetailsSuccess,
    isDashboardDetailsLoading,
    isDashboardDetailsError,
    dashboardId,
    globalTimeRange,
    startDate,
    endDate,
    eventsData,
    customerList,
    // mobile,
  ]);

  useEffect(() => {
    const handleEsc = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        // user need to press twice to exit full screen
        dispatch(dashboardSlice.actions.setTopPanelVisibility(true));
        dispatch(dashboardSlice.actions.setFullScreen(false));
      }
    };
    window.addEventListener('keydown', handleEsc);
    return () => {
      window.removeEventListener('keydown', handleEsc);
    };
  }, []);
  const toggleLeftPanel = () => {
    dispatch(dashboardSlice.actions.setIsLeftPanelOpen(!isLeftPanelOpen));
  };

  if (isCustomerListLoading || isLoadingDashboards)
    return (
      <Stack
        gap={0.5}
        p={1}
        width="100%"
        height={heightLeft}
        display="flex"
        sx={{ flexGrow: 1 }}
        overflow={'auto'}
      >
        <Loader style={{ height: '100%' }} />
      </Stack>
    );

  const handleChange = (event: React.SyntheticEvent, newValue: number) => {
    setValue(newValue);
    dispatch(dashboardSlice.actions.setCurrentDashboardId(newValue));
    dispatch(dashboardSlice.actions.setCurrentDashboardTitle(''));
    router.push(`/customer/${activeCustomer?.id}/dashboard/${newValue}`);
  };

  return (
    <>
      <Stack
        gap={0.5}
        width="100%"
        display="flex"
        sx={{
          pt: fullScreen ? 0 : 1,
          pb: 0,
          pl: 1,
          pr: 1,
          flexGrow: 1,
        }}
        overflow={'auto'}
      >
        <Paper elevation={3} sx={{ boxShadow: 0, pt: 1 }}>
          {activeCustomer && (
            <Box
              display="flex"
              flexDirection="row"
              p={0}
              sx={{
                '@media (max-width: 600px)': {
                  pl: 0,
                },
              }}
              overflow={'hidden'}
              width={'100%'}
              height={heightLeft}
              maxHeight={heightLeft}
            >
              {!fullScreen ? (
                <ErrorBoundary>
                  {isLeftPanelOpen && manageAsset && (
                    <Box
                      alignSelf="flex-start"
                      width={isLeftPanelOpen ? 'auto' : '0%'}
                      height={'100%'}
                      maxHeight={heightLeft}
                      overflow="auto"
                      sx={{
                        transition: 'all .2s',
                        visibility: isLeftPanelOpen ? 'visible' : 'hidden',
                        '&::-webkit-scrollbar': {
                          background: 'trasnparent',
                          width: 0,
                        },
                        '@media (max-width: 600px)': {
                          display: 'none',
                        },
                      }}
                    >
                      <LeftPanel
                        dashboardList={
                          dashboardList ?? ({ items: [], total: 0 } as DashboardCollection)
                        }
                        isLoadingDashboards={isFetchingDashboards}
                        isCustomerListSuccess={isCustomerListSuccess}
                        isCustomerListLoading={isCustomerListLoading}
                        customerList={customerList ?? ([] as Customer[])}
                      />
                    </Box>
                  )}
                  {manageAsset && (
                    <IconButton
                      sx={{
                        height: '25px',
                        width: '25px',
                        position: 'absolute',
                        left: isLeftPanelOpen
                          ? hasDashboardPermission('dashboard.create', Role.POWER_USER)
                            ? 395
                            : 50
                          : 100,
                        transition: 'all 0.2s',
                        top: '50%',
                        backgroundColor: '#fff',
                        zIndex: 1200,
                        background: !isLeftPanelOpen ? theme.palette.primary.main : undefined,
                        borderColor: !isLeftPanelOpen ? theme.palette.primary.main : undefined,
                        boxShadow: '1px 1px 3px 1px #A39B9B40',
                        '@media (max-width: 600px)': {
                          left: isLeftPanelOpen ? '100%' : 0,
                          display: 'none',
                        },
                      }}
                      color="primary"
                      aria-label="toggle left panel"
                      onClick={toggleLeftPanel}
                    >
                      {isLeftPanelOpen ? (
                        <ChevronLeftIcon color="primary" />
                      ) : (
                        <ChevronRightIcon
                          sx={{
                            color: theme.palette.background.default,
                            '&:hover': {
                              color: theme.palette.primary.main,
                              borderColor: theme.palette.primary.main,
                            },
                          }}
                        />
                      )}
                    </IconButton>
                  )}
                </ErrorBoundary>
              ) : null}

              <Box
                pl={1}
                flexGrow={1}
                width={isLeftPanelOpen ? '25%' : '100%'}
                height={heightMain}
                maxHeight={heightMain}
                overflow="auto"
                sx={{
                  '@media (max-width: 600px)': {
                    pl: 0,
                  },
                }}
              >
                <ErrorBoundary>
                  {mainPanel === 'chart' && (
                    <Box
                      position={'absolute'}
                      zIndex={100}
                      sx={{
                        background: (theme) => theme.palette.background.paper,
                        width: '-webkit-fill-available',
                      }}
                    >
                      {!fullScreen && !isMobile && isTopPanel ? (
                        <Paper
                          elevation={0}
                          sx={{
                            boxShadow: 0,
                            '@media (max-width: 600px)': {
                              display: 'none',
                            },
                          }}
                        >
                          <Box
                            display="flex"
                            sx={{
                              flexGrow: 1,
                              overflow: 'auto',
                              backgroundColor: '#f6f6f6',
                              height: (theme) => theme.spacing(8),
                              borderRadius: 2,
                              '&::-webkit-scrollbar': {
                                background: 'transparent',
                                width: 0,
                              },
                            }}
                            p={1.5}
                            gap={1}
                          >
                            <TopPanel
                              dashboardList={
                                dashboardList ?? ({ items: [], total: 0 } as DashboardCollection)
                              }
                              isLoadingDashboards={isLoadingDashboards}
                              isCustomerListSuccess={isCustomerListSuccess}
                              isCustomerListLoading={isCustomerListLoading}
                              customerList={customerList ?? ([] as Customer[])}
                              isSamplePeriod={true}
                              isRefreshInterval={true}
                            />
                          </Box>
                        </Paper>
                      ) : null}
                      {crumbs.length > 1 &&
                        !isDashboardDetailsFetching &&
                        !isFetchingDashboards && (
                          <Paper elevation={3} sx={{ boxShadow: 0 }}>
                            <Box p={1} pl={2}>
                              <DashboardsCrumb />
                            </Box>
                          </Paper>
                        )}
                      {/* {fullScreen ? (
                        <Box
                          display={'flex'}
                          justifyContent={'end'}
                          position={'absolute'}
                          right={10}
                          top={0}
                        >
                          <Tooltip title="Full Screen" placement="top" sx={{ mr: 1 }}>
                            <IconButton
                              sx={{
                                borderRadius: '50%',
                                border: '1px solid',
                                borderColor: theme.palette.primary.main,
                                backgroundColor: theme.palette.background.paper,
                              }}
                              onClick={() => {
                                if (!fullScreen) {
                                  setDashboardFullScreen();
                                } else {
                                  removeDashboardFullScreen();
                                }
                              }}
                            >
                              {!fullScreen ? (
                                <OpenInFullOutlinedIcon fontSize="small" />
                              ) : (
                                <CloseFullscreenOutlinedIcon fontSize="small" />
                              )}
                            </IconButton>
                          </Tooltip>
                        </Box>
                      ) : null} */}
                      <Box display="flex">
                        <Box
                          sx={{
                            display: 'flex',
                            width: '100%',
                            justifyContent: 'start',
                            alignSelf: 'center',
                          }}
                        >
                          <FavoriteTabs
                            dashboardId={dashboardId}
                            currentDashboardTitle={
                              dashboardList?.items?.find(
                                (dashboard) => dashboard.id === dashboardId,
                              )?.title ?? ''
                            }
                            dashboardList={dashboardList ?? undefined}
                            handleChange={handleChange}
                            value={value}
                          />
                        </Box>
                      </Box>
                    </Box>
                  )}
                  {isDashboardDetailsFetching ? (
                    <Loader style={{ height: '100%' }} />
                  ) : (
                    <Box
                      height="100%"
                      mt={
                        mainPanel !== 'chart'
                          ? 3
                          : fullScreen
                          ? crumbs.length > 1
                            ? 10
                            : 5
                          : isMobile
                          ? 2
                          : crumbs.length > 1
                          ? 18
                          : 13
                      }
                      sx={{
                        '@media (max-width:600px)': {
                          mt: 0,
                        },
                      }}
                    >
                      {mainPanel === 'chart' && (
                        <>
                          {(isFetchingDashboards || isFetchingDashboards) && <Loader />}
                          {!isDashboardDetailsFetching &&
                          !isFetchingDashboards &&
                          dashboardId !== -2 &&
                          dashboardId !== 0 &&
                          dashboardList?.items.find((d) => d.id === dashboardId) === undefined ? (
                            <Box
                              sx={{
                                display: 'flex',
                                justifyContent: 'center',
                                alignItems: 'center',
                                flexDirection: 'column',
                                height: '100%',
                                textAlign: 'center',
                                px: 2, // padding for responsiveness
                              }}
                            >
                              <NoEncryptionIcon sx={{ fontSize: 80, color: 'error.main', mb: 2 }} />
                              <Typography variant="h6" color="error.main">
                                Access to this dashboard is restricted.
                              </Typography>
                              <Typography
                                variant="body2"
                                color="text.secondary"
                                sx={{ mt: 1, maxWidth: 400 }}
                              >
                                Please contact your administrator if you believe you should have
                                access.
                              </Typography>
                            </Box>
                          ) : (
                            <WidgetContainer
                              isLoading={isDashboardDetailsFetching}
                              isError={isDashboardDetailsError}
                              dashboardId={dashboardId}
                              isDashboardDetailsSuccess={isDashboardDetailsSuccess}
                              widgets={widgets}
                              widgetLayout={widgetLayout}
                            />
                          )}
                        </>
                      )}
                      {mainPanel === 'create-alert' && <CreateMeasureAlert />}
                      {mainPanel == 'view-alerts' && <MeasureAlerts />}
                      {mainPanel === 'newAsset' && <MainCreateNewAsset />}
                      {mainPanel === 'editAsset' && <MainCreateNewAsset />}
                      {mainPanel === 'assetDetails' && <MainAssetDetails />}
                      {mainPanel === 'newMeasure' && <MainCreateNewMeasure />}
                      {mainPanel === 'editMeasure' && <MainCreateNewMeasure />}
                      {mainPanel === 'MeasureDetails' && <MainMeasureDetails />}
                      {mainPanel === 'addAssetTemplate' && <AssetTemplateInstance />}
                      {mainPanel === 'dashboard-template' && <AssetDashboardTemplate />}
                      {mainPanel === 'newAssetTemplate' && <AssetToTemplate />}
                    </Box>
                  )}
                </ErrorBoundary>
              </Box>
              <RightSideDrawer open={rightPanelOpen} />
              <DashboardWidgetsIcons />
            </Box>
          )}
        </Paper>
      </Stack>
      <CustomDialog
        title="You have unsaved changes."
        content={<Typography color={'error'}>Do you want to still proceed?</Typography>}
        dialogActions={
          <>
            <Button
              onClick={() => {
                setConfirm(false);
              }}
              variant="outlined"
              startIcon={<CancelIcon />}
            >
              Cancel
            </Button>
            <Button
              onClick={() => {
                setConfirm(false);
                dispatch(dashboardSlice.actions.selectMainPanel('chart'));
                if (customer) {
                  dispatch(dashboardSlice.actions.setCurrentDashboardTitle(''));
                  dispatch(dashboardSlice.actions.resetDashboardCrumb());
                  router.push(`/customer/${customer.id}`);
                  dispatch(dashboardSlice.actions.setActiveCustomer(customer));
                  setCustomer(null);
                }
                // if (dashboardCustomer === 'Dashboard' && dashboard) {
                //   dispatch(dashboardSlice.actions.resetDashboardCrumb());
                //   dispatch(dashboardSlice.actions.setCurrentDashboardTitle(dashboard.title));
                //   router.push(`/customer/${activeCustomer?.id}/dashboard/${dashboard.id}`);
                //   setDashboard(null);
                // }
              }}
              variant="contained"
              color="error"
            >
              Proceed
            </Button>
          </>
        }
        onClose={() => setConfirm(false)}
        open={confirm}
      />
    </>
  );
};

export default Dashboard;
