import { Data } from 'plotly.js';
import { useEffect, useState } from 'react';
import { useGetMeasuresTsData } from '~/hooks/useGetMeasuresTsData';
import { MultiPlotWidget } from '~/types/widgets';
import { formatMetricLabel } from '~/utils/utils';
import useFetchMultiForecast from './useFetchMultiForecast';

export interface SubplotTrace {
  subplotId: number;
  traces: Data[];
}

interface MultiPlotDataResult {
  data: SubplotTrace[];
  isLoading: boolean;
}

export type AssetMeasureOptions = {
  assetId: string;
  measureId: string[];
};

export function useFetchMultiPlotData(
  settings: MultiPlotWidget,
  metricsIdToName: Record<string, string>,
): MultiPlotDataResult {
  const [multiPlotData, setMultiPlotData] = useState<SubplotTrace[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [assetMeasures, setAssetMeasures] = useState<AssetMeasureOptions[]>([]);
  const [selectedMeasures, setSelectedMeasures] = useState<string[]>([]);

  const { data: fetchedData, isLoading: loading } = useGetMeasuresTsData({
    selectedTitles: selectedMeasures,
    dataFetchSettings: settings,
    assetMeasure: assetMeasures,
  });
  const { forcastedData: MultiplotForecastData } = useFetchMultiForecast({
    subplots: settings.subplots,
  });
  useEffect(() => {
    if (settings.mode === 'dashboard') {
      const measures = settings.subplots.flatMap((subplot) =>
        subplot.assetMeasures
          .filter((am) => am.measureId && am.measureId.length > 0 && am.assetId)
          .map((am) => ({
            assetId: am.assetId,
            measureId: am.measureId,
          })),
      );
      setAssetMeasures(measures);
      setSelectedMeasures(
        measures.map((measure) => measure.measureId).flatMap((measure) => measure),
      );
    }
    if (settings.mode === 'template') {
      setSelectedMeasures([]);
    }
  }, [settings.subplots, settings.mode]);

  useEffect(() => {
    if (settings.mode === 'template') {
      const allResults: SubplotTrace[] = [];

      for (const subplot of settings.subplots) {
        const traces: Data[] = [];

        for (const assetMeasure of subplot.assetMeasures) {
          if (!assetMeasure.selectedDbMeasureId) continue;

          const chartType = assetMeasure.chartType || 'bar';

          const x = Array.from({ length: 10 }, (_, i) => {
            const now = Date.now();
            return new Date(now + i * 60000).toISOString();
          });

          const y = Array.from({ length: 10 }, () => Math.floor(Math.random() * 100));

          const trace: Data = {
            type: chartType === 'bar' ? 'bar' : 'scatter',
            x: x.length > 0 ? x : [new Date().toISOString()],
            y: y.length > 0 ? y : [['']],
            name: `${metricsIdToName[assetMeasure.selectedDbMeasureId] || 'Unknown Metric'} `,
            showlegend: true,
          };

          if (chartType === 'trend') {
            trace.mode = 'lines';
            trace.hoverinfo = 'x+y';
          }

          traces.push(trace);
        }

        allResults.push({ subplotId: subplot.id, traces });
      }

      setMultiPlotData(allResults);
      setIsLoading(false);
      return;
    }

    const hasValidAssetMeasures = settings.subplots.some((subplot) =>
      subplot.assetMeasures.some((am) => am.measureId.length > 0 && am.assetId),
    );

    if (!hasValidAssetMeasures) {
      setMultiPlotData([]);
      setIsLoading(false);
      return;
    }

    setIsLoading(loading); // bind directly to the inner fetch

    if (!loading && fetchedData) {
      const allResults: SubplotTrace[] = [];

      for (const subplot of settings.subplots) {
        const validAssetMeasures = subplot.assetMeasures.filter(
          (am) => am.measureId?.length && am.assetId,
        );

        const shouldShowForecast = validAssetMeasures.length === 1;
        const traces: Data[] = [];

        validAssetMeasures.forEach((assetMeasure) => {
          const matched = fetchedData.find(
            (entry) => entry.measureData.id === Number(assetMeasure.measureId[0]),
          );

          if (matched && matched.tsData?.['ts,val']) {
            const x = matched.tsData['ts,val'].map(([ts]) => new Date(ts).toISOString());
            const y = matched.tsData['ts,val'].map(([, val]) => val);
            const uom = matched.unitOfMeasures.find(
              (u) => u.id === matched.measureData.unitOfMeasureId,
            )?.name;

            const chartType = assetMeasure.chartType || 'bar';
            const isTrend = chartType === 'trend';
            const showArea = subplot.showArea === true && isTrend;
            const isSparkline = isTrend && subplot.showSparkline === true;

            const trace: Data = {
              type: chartType === 'bar' ? 'bar' : 'scatter',
              x: x.length > 0 ? x : [''],
              y: y.length > 0 ? y : [''],
              name: matched.measureData.tag,
              fill: showArea && isTrend ? 'tozeroy' : 'none',
              showlegend: !isSparkline,
            };

            // ✅ Handle trend-specific modes and hover styles
            if (isTrend) {
              trace.mode = 'lines';
              trace.hoverinfo = isSparkline ? 'skip' : 'x+y';
            }

            // Override chart color if specified
            if (assetMeasure.overrideChartColor) {
              if (chartType === 'bar') {
                trace.marker = { color: assetMeasure.chartColor ?? '#000000' };
              } else if (chartType === 'trend') {
                trace.line = { color: assetMeasure.chartColor ?? '#000000' };
              }
            }

            trace.hovertemplate = `${formatMetricLabel(matched.measureData.tag ?? '')}: %{y:.2f} ${
              uom ?? ''
            }<br>Time: %{x}<extra></extra>`;
            traces.push(trace);

            if (
              shouldShowForecast &&
              assetMeasure.ForecastSettings?.showForecast &&
              MultiplotForecastData
            ) {
              const primaryMeasureId = assetMeasure.measureId?.[0]?.toString();
              if (!primaryMeasureId) return;

              const forecasted = MultiplotForecastData.find(
                (entry) => entry.measureData.id?.toString() === primaryMeasureId,
              );

              const measurementId = forecasted?.measureData?.measurementId?.toString();
              const forecastTSData = (forecasted?.tsData as Record<string, any>)?.[
                measurementId ?? ''
              ];
              if (forecastTSData?.['ts,val']) {
                const forecastX = forecastTSData['ts,val'].map(([ts]: [string, any]) =>
                  new Date(ts).toISOString(),
                );
                const forecastY = forecastTSData['ts,val'].map(([, val]: [string, number]) => val);

                const forecastTrace: Data = {
                  type: chartType === 'bar' ? 'bar' : 'scatter',
                  x: forecastX,
                  y: forecastY,
                  name: `${matched.measureData.tag} - Forecast`,
                  mode: 'lines',
                  hovertemplate: `${formatMetricLabel(
                    matched.measureData.tag ?? '',
                  )} Forecast: %{y:.2f} ${uom ?? ''}<br>Time: %{x}<extra></extra>`,
                };

                if (chartType === 'bar') {
                  forecastTrace.marker = {
                    color: assetMeasure.ForecastSettings.forecastColor ?? '#000000',
                  };
                } else if (chartType === 'trend') {
                  forecastTrace.line = {
                    color: assetMeasure.ForecastSettings.forecastColor ?? '#000000',
                    dash: 'dot',
                  };
                }
                traces.push(forecastTrace);
              } else {
                console.warn(
                  '[Forecast] No forecast ts,val data found for measureId:',
                  primaryMeasureId,
                );
              }
            }
          }
        });

        allResults.push({ subplotId: subplot.id, traces });
      }

      setMultiPlotData(allResults);
      setIsLoading(false);
    }
  }, [settings, fetchedData, loading, MultiplotForecastData]);

  return {
    data: multiPlotData,
    isLoading,
  };
}
