import { test, expect, Locator } from '@playwright/test';
test.beforeEach(async ({ page }) => {
  await page.goto('https://test.pivotol.ai/login');
});
// test('has title', async ({ page }) => {
//   await page.goto('https://playwright.dev/');

//   // Expect a title "to contain" a substring.
//   await expect(page).toHaveTitle(/Playwright/);
// });

// test('get started link', async ({ page }) => {
//   await page.goto('https://playwright.dev/');

//   // Click the get started link.
//   await page.getByRole('link', { name: 'Get started' }).click();

//   // Expects page to have a heading with the name of Installation.
//   await expect(page.getByRole('heading', { name: 'Installation' })).toBeVisible();
// });

test('login', async ({ page }) => {
  await page.goto('https://test.pivotol.ai/login');
  // Fill in the login form
  await page.fill('input[type="text"]', 'test');
  await page.fill('input[type="password"]', 'asdfasdf');
  // Submit the form
  await page.click('button[type="submit"]');
  // Expect the page to navigate to the dashboard
  await page.waitForURL('https://test.pivotol.ai/dashboard');
});
test('login with invalid credentials', async ({ page }) => {
  await page.goto('https://test.pivotol.ai/login');
  // Fill in the login form with invalid credentials
  await page.fill('input[type="text"]', 'invalida');
  await page.fill('input[type="password"]', 'invald');
  // Submit the form
  await page.click('button[type="submit"]');
  // Expect an error message to be displayed
  // await expect(page).toHaveText('Invalid username or password');
  // await expect(page.getByRole('alert', { name: 'Installation' })).toBe('Invalid login');
});

test('login with valid credentials', async ({ page }) => {
  await page.goto('https://test.pivotol.ai/login');
  // Fill in the login form with valid credentials
  await page.fill('input[type="text"]', 'test');
  await page.fill('input[type="password"]', 'asdfasdf');
  // Submit the form
  await page.click('button[type="submit"]');
  // Expect the page to navigate to the dashboard
  // await page.waitForURL('http://localhost:3000/dashboard');
  await expect(page).toHaveURL('https://test.pivotol.ai/customer/8/dashboard/110', {
    timeout: 100000,
  });
  await page.getByLabel('Select Customer').click();
  await page.getByRole('combobox', { name: 'Select Customer' }).click();
  await page.getByRole('option', { name: 'Customer', exact: true }).click();
  await page.getByLabel('Select Dashboard').click();
  await page.getByLabel('Select Dashboard').click(); // Ensure the dropdown is open
  await page.getByRole('option', { name: 'DASHBOARD_TO_TEST' }).waitFor({ state: 'visible' });

  await page.locator('widgets-speed-dialer').hover();
  // await page.getByLabel('Close').click();
});
