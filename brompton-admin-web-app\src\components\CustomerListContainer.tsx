import CancelIcon from '@mui/icons-material/Cancel';
import { Autocomplete, Box, Button, TextField, Typography, useTheme } from '@mui/material';
import { useRouter } from 'next/router';
import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { getActiveCustomer } from '~/redux/selectors/customerSelectors';
import {
  getCurrentDashboardId,
  getCurrentDashboardIdTitle,
  isDashboardDirty,
} from '~/redux/selectors/dashboardSelectors';
import { getDefaultCustomer } from '~/redux/selectors/userPreferences';
import { getDeletedWidgets, getWidgets } from '~/redux/selectors/widgetSelectors';
import { dashboardSlice } from '~/redux/slices/dashboardSlice';
import { Customer } from '~/types/customers';
import { DashboardCollection } from '~/types/dashboard';
import CustomDialog from './common/CustomDialog';
import { Role, useRolePermission } from '~/hooks/useRolePermission';

type CustomerListContainerProps = {
  dashboardList: DashboardCollection;
  isLoadingDashboards: boolean;
  customerList: Customer[];
  isCustomerListLoading: boolean;
  isCustomerListSuccess: boolean;
};
export function CustomerListContainer({
  dashboardList,
  isLoadingDashboards,
  customerList,
  isCustomerListLoading,
  isCustomerListSuccess,
}: CustomerListContainerProps): JSX.Element {
  const dispatch = useDispatch();
  const router = useRouter();
  const activeCustomer = useSelector(getActiveCustomer);
  const currentDashboardDetails = useSelector(getCurrentDashboardIdTitle);
  const currDashboardId = useSelector(getCurrentDashboardId);
  const { setActiveCustomer } = dashboardSlice.actions;
  const widgets = useSelector(getWidgets);
  const deleteWidgets = useSelector(getDeletedWidgets);
  const isDashboardStateDirty = useSelector(isDashboardDirty);
  const [confirm, setConfirm] = useState<boolean>(false);
  const [dashboardCustomer, setDashboardCustomer] = useState<'Dashboard' | 'Customer' | null>(null);
  const [customer, setCustomer] = useState<Customer | null>(null);
  const [dashboard, setDashboard] = useState<{ id: number; title: string } | null>(null);
  const defaultCustomer = useSelector(getDefaultCustomer);
  const theme = useTheme();
  const { hasDashboardPermission } = useRolePermission();
  const isUnsavedDashboardChange = () => {
    if (
      activeCustomer &&
      (widgets.filter((widget) => widget.type !== 'chart' && widget.settings.isDirty).length > 0 ||
        deleteWidgets.length > 0 ||
        widgets.filter((widget) => widget.type === 'chart' && widget.settings?.settings.isDirty)
          .length > 0 ||
        isDashboardStateDirty) &&
      currDashboardId > -1 &&
      hasDashboardPermission('dashboard.update', Role.POWER_USER)
    ) {
      setConfirm(true);
      return true;
    }
    return false;
  };
  const handleChangeDashboard = (
    e: React.SyntheticEvent,
    value: { id: number; title: string } | null,
  ) => {
    if (!value) return;
    if (isUnsavedDashboardChange()) {
      setDashboardCustomer('Dashboard');
      setDashboard(value);
    } else {
      setDashboardCustomer(null);
      dispatch(dashboardSlice.actions.selectMainPanel('chart'));
      dispatch(dashboardSlice.actions.setCurrentDashboardTitle(value.title));
      dispatch(dashboardSlice.actions.resetDashboardCrumb());
      router.push(`/customer/${activeCustomer?.id}/dashboard/${value.id}`);
    }
  };

  useEffect(() => {
    if (isCustomerListSuccess && customerList && !activeCustomer) {
      if (customerList.length === 1) {
        dispatch(setActiveCustomer(customerList[0]));
        dispatch(dashboardSlice.actions.setCurrentDashboardId(-1));
        dispatch(dashboardSlice.actions.setCurrentDashboardTitle(''));
      } else if (customerList.length > 1) {
        dispatch(
          setActiveCustomer(
            customerList.find((customer) => customer?.id.toString() === defaultCustomer) ??
              customerList[0],
          ),
        );
        dispatch(dashboardSlice.actions.setCurrentDashboardId(-1));
        dispatch(dashboardSlice.actions.setCurrentDashboardTitle(''));
      }
    }
  }, [
    customerList,
    isCustomerListSuccess,
    isCustomerListLoading,
    activeCustomer,
    setActiveCustomer,
    defaultCustomer,
  ]);

  return (
    <>
      <Box display="flex">
        <Autocomplete
          id="combo-box-demo"
          options={
            dashboardList?.items?.map((dashboard) => {
              return { id: dashboard.id, title: dashboard.title };
            }) ?? []
          }
          loading={isLoadingDashboards}
          getOptionLabel={(option) => option.title}
          onChange={handleChangeDashboard}
          size="small"
          sx={{
            width: 300,
            ml: 1,
            backgroundColor: theme.palette.background.paper,
          }}
          isOptionEqualToValue={(option, value) => option.id === value.id}
          value={currentDashboardDetails ?? null}
          renderInput={(params) => (
            <TextField
              {...params}
              // label="Select Dashboard"
            />
          )}
        />
      </Box>
      <CustomDialog
        title="You have unsaved changes."
        content={<Typography color={'error'}>Do you want to still proceed?</Typography>}
        dialogActions={
          <>
            <Button
              onClick={() => {
                setConfirm(false);
              }}
              variant="outlined"
              startIcon={<CancelIcon />}
            >
              Cancel
            </Button>
            <Button
              onClick={() => {
                setConfirm(false);
                dispatch(dashboardSlice.actions.selectMainPanel('chart'));
                if (dashboardCustomer === 'Customer' && customer) {
                  dispatch(dashboardSlice.actions.setCurrentDashboardTitle(''));
                  dispatch(dashboardSlice.actions.resetDashboardCrumb());
                  router.push(`/customer/${customer.id}`);
                  dispatch(setActiveCustomer(customer));
                  setCustomer(null);
                }
                if (dashboardCustomer === 'Dashboard' && dashboard) {
                  dispatch(dashboardSlice.actions.resetDashboardCrumb());
                  dispatch(dashboardSlice.actions.setCurrentDashboardTitle(dashboard.title));
                  router.push(`/customer/${activeCustomer?.id}/dashboard/${dashboard.id}`);
                  setDashboard(null);
                }
              }}
              variant="contained"
              color="error"
            >
              Proceed
            </Button>
          </>
        }
        onClose={() => setConfirm(false)}
        open={confirm}
      />
    </>
  );
}
