const { test, expect } = require('@playwright/test');

/**
 * Authenticate and retrieve tokens
 */
async function authenticate(request) {
  const authResponse = await request.post('https://test.brompton.ai/api/v0/sessions', {
    headers: { 'Content-Type': 'application/json' },
    data: { username: 'test', password: 'asdfasdf' },
  });
  // const expectedStatus = 200;
  const authData = await authResponse.json();
  const accessToken = authData.access_token;
  const csrfToken = authData.csrf_token;

  if (!accessToken || !csrfToken) {
    throw new Error('Authentication failed: No access or CSRF token returned');
  }

  return { accessToken, csrfToken };
}

/**
 * Helper function for generic API requests with retry on 401 Unauthorized
 */
async function makeApiRequest(
  request,
  authTokens,
  { method, url, headers = {}, body },
  expectedStatus,
  expectedProps,
) {
  let response = await request[method.toLowerCase()](url, {
    headers: {
      ...headers,
      'BE-CSRFToken': authTokens.csrfToken,
      Cookie: `BE-AccessToken=${authTokens.accessToken}`,
    },
    data: body,
  });

  if (response.status() === 401) {
    // Re-authenticate and retry request
    console.warn('401 Unauthorized - Re-authenticating and retrying request...');
    authTokens = await authenticate(request);
    response = await request[method.toLowerCase()](url, {
      headers: {
        ...headers,
        'BE-CSRFToken': authTokens.csrfToken,
        Cookie: `BE-AccessToken=${authTokens.accessToken}`,
      },
      data: body,
    });
  }

  console.log(`Status: ${response.status()}`);
  const responseBody = await response.text();
  console.log(`Response Body: ${responseBody}`);

  // Assert response status
  expect(response.status()).toBe(expectedStatus);

  // Parse response and verify expected properties
  const jsonResponse = JSON.parse(responseBody);
  for (const [key, value] of Object.entries(expectedProps)) {
    expect(jsonResponse).toHaveProperty(key, value);
  }

  return jsonResponse;
}

test.describe('API Test Suite', () => {
  let authTokens;

  // Authenticate before running tests
  test.beforeAll(async ({ request }) => {
    authTokens = await authenticate(request);
  });

  test('POST /sessions - Create a session', async ({ request }) => {
    const response = await makeApiRequest(
      request,
      authTokens,
      {
        method: 'POST',
        url: 'https://test.brompton.ai/api/v0/sessions',
        headers: {
          'Content-Type': 'application/json',
        },
        body: { username: 'test', password: 'asdfasdf' },
      },
      201, // Expected status code
      { access_token: expect.any(String), csrf_token: expect.any(String) }, // Expected properties
    );
  });

  test('GET /users/me - Retrieve user info', async ({ request }) => {
    const response = await makeApiRequest(
      request,
      authTokens,
      {
        method: 'GET',
        url: 'https://test.brompton.ai/api/v0/users/me',
      },
      200, // Expected status code
      { username: 'test', email: expect.any(String) }, // Expected properties
    );
  });

  test('POST /customers/84/dashboards - Create/update dashboard', async ({ request }) => {
    const response = await makeApiRequest(
      request,
      authTokens,
      {
        method: 'POST',
        url: 'https://test.brompton.ai/apiv0/customers/84/dashboards',
        headers: {
          'Content-Type': 'application/json',
        },
        body: {
          title: 'Overview',
          data: '{"currentDashboardId":89,"dashboardTitle":"Overview"}',
        },
      },
      200, // Expected status code
      { id: expect.any(Number), title: 'Overview' }, // Expected properties
    );
  });

  test('PATCH /customers/8/dashboards/58 - Update dashboard', async ({ request }) => {
    const response = await makeApiRequest(
      request,
      authTokens,
      {
        method: 'PATCH',
        url: 'https://test.brompton.ai/api/v0/customers/8/dashboards/58',
        headers: {
          'Content-Type': 'application/json',
        },
        body: {
          data: {
            currentDashboardId: 65,
            dashboardTitle: 'animated dashboard',
          },
          title: 'animated dashboard',
        },
      },
      200, // Expected status code
      { id: expect.any(Number), title: 'animated dashboard' }, // Expected properties
    );
  });

  test('GET /customers/85/logo - Retrieve customer logo', async ({ request }) => {
    const response = await makeApiRequest(
      request,
      authTokens,
      {
        method: 'GET',
        url: 'https://test.brompton.ai/api/v0/customers/85/logo',
        headers: {
          Authorization: 'Basic Og==',
        },
      },
      200, // Expected status code
      {}, // No specific properties expected in the response
    );
  });
});
