import { Al<PERSON>, Box, Snackbar, Typography } from '@mui/material';
import { useEffect, useRef, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import Loader from '~/components/common/Loader';
import { images, ImageWidgetDialog } from '~/components/ImageWidget/ImageWidgetDialog';
import useImageWidgetContainerHelper from '~/hooks/useImageWidgetContainerHelper';
import { useGetCustomerImagesQuery, useGetImageQuery } from '~/redux/api/customersApi';
import { getActiveCustomer } from '~/redux/selectors/customerSelectors';
import { dashboardSlice } from '~/redux/slices/dashboardSlice';
import { ImageWidget, ImageWidgetType } from '~/types/widgets';
import CommonWidgetContainer from '../../components/common/CommonWidgetContainer';
type ImageWidgetContainerProps = {
  id: string;
  settings: ImageWidget;
  imageType: ImageWidgetType;
};
const ImageWidgetContainer = ({ id, settings, imageType }: ImageWidgetContainerProps) => {
  const dispatch = useDispatch();
  const divRef = useRef(null);
  const activeCustomer = useSelector(getActiveCustomer);
  const { data: customerImages, isFetching: loadingImages } = useGetCustomerImagesQuery(
    {
      id: activeCustomer?.id ?? 0,
    },
    {
      refetchOnMountOrArgChange: true,
      skip: activeCustomer == null || activeCustomer?.id === 0,
    },
  );
  const { data: image, isFetching: fetchingImage } = useGetImageQuery(
    {
      id: activeCustomer?.id ?? 0,
      imageId: settings.image ?? '',
    },
    {
      refetchOnMountOrArgChange: true,
      skip:
        activeCustomer == null ||
        activeCustomer?.id === 0 ||
        settings.image === null ||
        settings.image === '' ||
        images.includes(settings.image),
    },
  );
  const [imageBackground, setImageBackground] = useState<string>(settings.image ?? '');
  useEffect(() => {
    const isExistingImage = images.includes(settings.image!);
    if (settings.image && !isExistingImage && customerImages?.items?.length) {
      const foundImage = customerImages.items.find((img) => img.id.toString() === settings.image);
      if (foundImage?.logo) {
        setImageBackground(foundImage.logo); // ✅ Use `logo` from `id` match (new logic)
      } else {
        const foundLogo = customerImages.items.find(
          (img) => img.logo?.toString() === settings.image,
        );
        if (foundLogo?.logo) {
          setImageBackground(foundLogo.logo); // ✅ Use `logo` from `logo` match (old logic)
          dispatch(
            dashboardSlice.actions.setCurrentWidgetSettings({
              id,
              type: 'image',
              settings: {
                ...settings,
                image: foundLogo.id.toString(),
              },
            }),
          ); // ✅  use `id` from logo to retain as per new logic
        } else {
          setImageBackground(settings.image); // ✅ Fallback to `settings.image`
        }
      }
    } else if (
      settings.image &&
      !isExistingImage &&
      image?.logo &&
      !customerImages?.items?.length
    ) {
      // ✅ Fallback if no customerImages are returned, but `image` API has valid logo
      setImageBackground(image.logo);
    } else {
      setImageBackground(settings.image ?? '');
    }
    if (settings.image && !isExistingImage && image?.logo) {
      // ✅ Fallback if no customerImages are returned, but `image` API has valid logo
      setImageBackground(image.logo);
    } else {
      setImageBackground(settings.image ?? '');
    }
  }, [settings.image, customerImages, imageType, dispatch, image, id]);

  // const { data, isLoading, measureIdUOM } = useFetchImageWidget(id, settings);
  const { openSnackBar, setOpenSnackBar, onImageDashboardLinkClick } =
    useImageWidgetContainerHelper({ settings });

  return (
    <>
      <Snackbar
        open={openSnackBar.open}
        onClose={() => {
          setOpenSnackBar({ open: false, message: '', severity: 'info' });
        }}
        autoHideDuration={3000}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert severity={openSnackBar.severity}>{openSnackBar.message}</Alert>
      </Snackbar>
      <CommonWidgetContainer
        id={id}
        widgetType={imageType}
        widgetName="Image"
        settings={settings}
        settingsDialog={ImageWidgetDialog}
        widgetContent={
          <Box
            sx={{
              height: '100%',
              width: '100%',
            }}
          >
            {settings.image === null ? (
              <Box
                style={{
                  width: '100%',
                  height: '100%',
                  display: 'flex',
                  justifyContent: 'center',
                  alignItems: 'center',
                }}
              >
                <Typography>Please select image from Widget settings.</Typography>
              </Box>
            ) : (
              <>
                {loadingImages || fetchingImage ? (
                  <Loader />
                ) : (
                  <>
                    <Box
                      sx={{
                        height: '100%',
                        width: '100%',
                        display: 'flex',
                        flexDirection: 'column',
                        alignItems: 'center',
                        overflow: 'hidden',
                      }}
                    >
                      {/* Title */}
                      {settings.title.isVisible && (
                        <Box
                          sx={{
                            flexShrink: 0, // don’t shrink title
                            textAlign: 'center',
                            padding: '4px 8px',
                          }}
                        >
                          <Typography
                            variant="inherit"
                            sx={{
                              fontSize: `${settings.title.fontSize}px`,
                              fontWeight: settings.title.fontWeight,
                              color: settings.title.color,
                              wordBreak: 'break-word',
                              lineHeight: 1.2,
                            }}
                          >
                            {settings.title.value}
                          </Typography>
                        </Box>
                      )}

                      {/* Image */}
                      <Box
                        sx={{
                          flexGrow: 1, // image takes remaining space
                          width: '100%',
                          display: 'flex',
                          justifyContent: 'center',
                          alignItems: 'center',
                          minHeight: '40px', // guarantees image always has some space
                          paddingLeft: settings.margin?.l,
                          paddingTop: settings.margin?.t,
                          paddingRight: settings.margin?.r,
                          paddingBottom: settings.margin?.b,
                        }}
                      >
                        <Box
                          component="img"
                          src={imageBackground}
                          alt="widget-image"
                          sx={{
                            width: '100%',
                            height: '100%',
                            maxHeight: '100%',
                            objectFit: 'contain',
                            aspectRatio: '1 / 1',
                          }}
                        />
                      </Box>
                    </Box>

                    {settings.image === null ? (
                      <Box
                        style={{
                          width: '100%',
                          height: '100%',
                          display: 'flex',
                          justifyContent: 'center',
                          alignItems: 'center',
                        }}
                      >
                        <Typography>Please select image from Widget settings.</Typography>
                      </Box>
                    ) : (
                      <div
                        ref={divRef}
                        onClick={() => {
                          onImageDashboardLinkClick();
                        }}
                        style={{
                          width: '100%',
                          height: '100%',
                          position: 'absolute',
                          overflow: 'hidden',
                          top: 0,
                        }}
                      >
                        {/* {imageTextDetailsList.map((imageTextDetails, index) => {
                          return (
                            <Draggable
                              key={index}
                              disabled={!settings.isEditable}
                              data-measureId={imageTextDetails.id}
                              defaultPosition={{
                                x: imageTextDetails.positionX,
                                y: imageTextDetails.positionY,
                              }}
                              bounds="parent"
                              onStop={(e, data) => handleDragStop(imageTextDetails.id, data)}
                            >
                              <span
                                style={{
                                  position: 'absolute',
                                  fontSize: settings.font?.size + 'px',
                                  fontWeight: settings.font?.weight,
                                  color: settings.font?.color,
                                  cursor: 'pointer',
                                  userSelect: 'none',
                                }}
                                onClick={() => {
                                  onImageTextDashboardLinkClick(imageTextDetails);
                                }}
                              >
                                {isLoading ? (
                                  <Box display="flex" alignItems="center" justifyContent="center">
                                    <Loader style={{ fontSize: 16 }} />
                                  </Box>
                                ) : (
                                  <>
                                    {imageTextDetails.label} {imageTextDetails.value}{' '}
                                    {imageTextDetails.unit}
                                  </>
                                )}
                              </span>
                            </Draggable>
                          );
                        })} */}
                      </div>
                    )}
                  </>
                )}
              </>
            )}
          </Box>
        }
      />
    </>
  );
};
export default ImageWidgetContainer;
