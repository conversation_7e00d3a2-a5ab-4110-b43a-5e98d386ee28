import React, { useEffect, useMemo, useState, Dispatch, SetStateAction } from 'react';
import { Autocomplete, Box, CircularProgress, FormControl, TextField } from '@mui/material';
import { useSelector } from 'react-redux';
import { useGetAllAssetQuery } from '~/redux/api/assetsApi';
import { useGetAllMeasurementsQuery } from '~/redux/api/measuresApi';
import { getCustomerId } from '~/redux/selectors/customerSelectors';
import { MapAssetMeasures, MapWidget } from '~/types/widgets';
import { assetsPathMapper, formatMetricLabel } from '~/utils/utils';

type AssetOption = {
  label: string;
  id: number;
};

type MeasureOption = {
  label: string;
  id: string;
};

type MapMeasureSelectProps = {
  assetMeasure: MapAssetMeasures;
  handleSettingsChange: Dispatch<SetStateAction<MapWidget>>;
  currentSettings: number;
  index: number;
};

const MapMeasureSelect = ({
  assetMeasure,
  handleSettingsChange,
  currentSettings,
  index,
}: MapMeasureSelectProps) => {
  const customerId = useSelector(getCustomerId);
  const [selectedAsset, setSelectedAsset] = useState<AssetOption | null>(null);
  const [selectedMeasure, setSelectedMeasure] = useState<MeasureOption | null>(null);

  // Fetch assets
  const {
    data: assetData,
    isLoading: isAssetLoading,
    isFetching: isAssetReloading,
  } = useGetAllAssetQuery(
    { customerId, parentIds: [] },
    {
      skip: !customerId,
      refetchOnMountOrArgChange: true,
    },
  );

  const assetTypesWithPath = useMemo(() => assetsPathMapper(assetData ?? []), [assetData]);

  // Fetch measurements based on selectedAsset
  const { data: measurementData, isLoading: isMeasurementLoading } = useGetAllMeasurementsQuery(
    { customerId, assetId: selectedAsset ? selectedAsset.id : 0 },
    {
      skip: !selectedAsset || selectedAsset.id === 0,
      refetchOnMountOrArgChange: true,
    },
  );

  const measurementOptions: MeasureOption[] = useMemo(() => {
    if (!measurementData) return [];
    return measurementData.map((measure) => ({
      label: measure.tag,
      id: String(measure.id),
    }));
  }, [measurementData]);

  // Set default selectedAsset based on assetMeasure.assetId
  useEffect(() => {
    if (assetMeasure.assetId && assetTypesWithPath.length > 0) {
      const assetOption = assetTypesWithPath.find(
        (asset) => asset.id === Number(assetMeasure.assetId),
      );
      if (assetOption) {
        setSelectedAsset(assetOption);
      }
    }
  }, [assetMeasure.assetId, assetTypesWithPath]);

  // Set default selectedMeasure based on assetMeasure.measureId
  useEffect(() => {
    if (assetMeasure.measureId && measurementOptions.length > 0) {
      const measureOption = measurementOptions.find(
        (measure) => measure.id === assetMeasure.measureId,
      );
      if (measureOption) {
        setSelectedMeasure(measureOption);
      }
    }
  }, [assetMeasure.measureId, measurementOptions]);

  // Handle Asset Change
  const handleAssetChange = (event: any, newValue: AssetOption | null) => {
    setSelectedAsset(newValue);

    if (newValue) {
      handleSettingsChange(({ markers, ...rest }) => {
        const updatedMarkers = markers.map((marker, markerIndex) => {
          if (markerIndex === currentSettings) {
            const updatedAssetMeasures = marker.assetMeasures.map((assetMeasureItem, i) => {
              if (i === index) {
                return {
                  ...assetMeasureItem,
                  assetId: String(newValue.id), // Convert number to string
                };
              }
              return assetMeasureItem;
            });
            return { ...marker, assetMeasures: updatedAssetMeasures };
          }
          return marker;
        });
        return { ...rest, markers: updatedMarkers };
      });
    }
    setSelectedMeasure(null); // Clear measure selection when asset changes
  };

  // Handle Measurement Change
  const handleMeasurementChange = (event: any, newValue: MeasureOption | null) => {
    setSelectedMeasure(newValue);

    if (newValue) {
      handleSettingsChange(({ markers, ...rest }) => {
        const updatedMarkers = markers.map((marker, markerIndex) => {
          if (markerIndex === currentSettings) {
            const updatedAssetMeasures = marker.assetMeasures.map((assetMeasureItem, i) => {
              if (i === index) {
                return {
                  ...assetMeasureItem,
                  measureId: newValue.id,
                };
              }
              return assetMeasureItem;
            });

            // Add/update the label and units for the new measureId
            const updatedLabelAndUnits = {
              ...marker.labelAndUnits,
              [newValue.id]: {
                label: newValue.label,
                unit: '',
                value: '',
              },
            };

            return {
              ...marker,
              assetMeasures: updatedAssetMeasures,
              labelAndUnits: updatedLabelAndUnits,
            };
          }
          return marker;
        });

        return { ...rest, markers: updatedMarkers };
      });
    }
  };

  return (
    <Box sx={{ display: 'flex', gap: 2, width: '100%' }}>
      {/* Asset Autocomplete */}
      <FormControl fullWidth>
        <Autocomplete
          fullWidth
          id={`asset-autocomplete-${index}`}
          loading={isAssetReloading}
          options={assetTypesWithPath.map((asset) => ({
            label: asset.label,
            id: asset.id,
          }))}
          getOptionLabel={(option) => option.label}
          onChange={handleAssetChange}
          value={selectedAsset}
          renderInput={(params) => (
            <TextField
              {...params}
              label="Select Asset"
              variant="outlined"
              InputProps={{
                ...params.InputProps,
                endAdornment: (
                  <>
                    {isAssetLoading ? <CircularProgress color="inherit" size={20} /> : null}
                    {params.InputProps.endAdornment}
                  </>
                ),
              }}
            />
          )}
        />
      </FormControl>

      {/* Measurement Autocomplete */}
      {selectedAsset && (
        <FormControl fullWidth>
          <Autocomplete
            fullWidth
            id={`measurement-autocomplete-${index}`}
            loading={isMeasurementLoading}
            options={measurementOptions}
            getOptionLabel={(option) => formatMetricLabel(option.label)}
            onChange={handleMeasurementChange}
            value={selectedMeasure}
            renderInput={(params) => (
              <TextField
                {...params}
                label="Select Measurement"
                variant="outlined"
                InputProps={{
                  ...params.InputProps,
                  endAdornment: (
                    <>
                      {isMeasurementLoading ? <CircularProgress color="inherit" size={20} /> : null}
                      {params.InputProps.endAdornment}
                    </>
                  ),
                }}
              />
            )}
          />
        </FormControl>
      )}
    </Box>
  );
};

export default MapMeasureSelect;
