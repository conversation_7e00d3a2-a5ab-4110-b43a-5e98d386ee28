import DeleteIcon from '@mui/icons-material/Delete';
import {
  Autocomplete,
  Box,
  CircularProgress,
  FormControl,
  IconButton,
  MenuItem,
  Select,
  SelectChangeEvent,
  TextField,
  Tooltip,
} from '@mui/material';
import React, { useMemo } from 'react';
import { useSelector } from 'react-redux';
import { useGetAllAssetQuery } from '~/redux/api/assetsApi';
import { useGetAllMeasurementsQuery } from '~/redux/api/measuresApi';
import { getCustomerId } from '~/redux/selectors/customerSelectors';
import { getMetricsIdToName } from '~/redux/selectors/dashboardSelectors';
import { MultiPlotWidget } from '~/types/widgets';
import { assetsPathMapper, formatMetricLabel } from '~/utils/utils';

type Props = {
  subplotIndex: number;
  measureIndex: number;
  measure: MultiPlotWidget['subplots'][number]['assetMeasures'][number];
  settings: MultiPlotWidget;
  handleSettingsChange: (
    value: MultiPlotWidget | ((prev: MultiPlotWidget) => MultiPlotWidget),
  ) => void;
};

type AssetOption = { id: number; label: string };
type MeasureOption = { id: string; label: string };

const AssetMeasureSelector: React.FC<Props> = ({
  subplotIndex,
  measureIndex,
  measure,
  handleSettingsChange,
  settings,
}) => {
  const customerId = useSelector(getCustomerId);
  const metricsIdToName = useSelector(getMetricsIdToName);
  const assetIdNumber = Number(measure.assetId);

  const {
    data: assetData,
    isLoading: isAssetLoading,
    isFetching: isAssetFetching,
  } = useGetAllAssetQuery({ customerId, parentIds: [] }, { skip: !customerId });

  const { data: measurementData, isLoading: isMeasurementLoading } = useGetAllMeasurementsQuery(
    { customerId, assetId: assetIdNumber || 0 },
    { skip: !customerId || !assetIdNumber },
  );

  const assetOptions: AssetOption[] = useMemo(() => assetsPathMapper(assetData ?? []), [assetData]);

  const measurementOptions: MeasureOption[] = useMemo(() => {
    if (!measurementData) return [];
    return measurementData.map((m) => ({
      id: String(m.id),
      label: formatMetricLabel(m.tag),
    }));
  }, [measurementData]);

  const handleAssetChange = (_: any, newValue: AssetOption | null) => {
    handleSettingsChange((prev) => {
      const updated = { ...prev };
      const updatedSubplots = [...updated.subplots];
      const updatedAssetMeasures = [...updatedSubplots[subplotIndex].assetMeasures];
      updatedAssetMeasures[measureIndex] = {
        ...updatedAssetMeasures[measureIndex],
        assetId: newValue ? String(newValue.id) : '',
        measureId: [],
      };
      updatedSubplots[subplotIndex] = {
        ...updatedSubplots[subplotIndex],
        assetMeasures: updatedAssetMeasures,
      };
      updated.subplots = updatedSubplots;
      return updated;
    });
  };

  const handleMeasurementChange = (_: any, newValue: MeasureOption | null) => {
    handleSettingsChange((prev) => {
      const updated = { ...prev };
      updated.subplots = updated.subplots.map((subplot, sIdx) => {
        if (sIdx !== subplotIndex) return subplot;
        return {
          ...subplot,
          assetMeasures: subplot.assetMeasures.map((measure, mIdx) => {
            if (mIdx !== measureIndex) return measure;
            return {
              ...measure,
              measureId: newValue ? [String(newValue.id)] : [],
            };
          }),
        };
      });

      // Also update label mapping
      if (newValue) {
        updated.dbMeasureIdToName = {
          ...updated.dbMeasureIdToName,
          [newValue.id]: newValue.label,
        };
      }

      return updated;
    });
  };

  const handleDelete = () => {
    handleSettingsChange((prev) => {
      const updated = { ...prev };
      const updatedSubplots = [...updated.subplots];
      updatedSubplots[subplotIndex] = {
        ...updatedSubplots[subplotIndex],
        assetMeasures: updatedSubplots[subplotIndex].assetMeasures.filter(
          (_, idx) => idx !== measureIndex,
        ),
      };
      updated.subplots = updatedSubplots;
      return updated;
    });
  };

  const handleMeasureChange = (event: SelectChangeEvent<string>) => {
    const selectedDbMeasureId = event.target.value as string;

    handleSettingsChange((prev) => {
      const updated = { ...prev };
      updated.subplots = updated.subplots.map((subplot, sIdx) => {
        if (sIdx !== subplotIndex) return subplot;
        return {
          ...subplot,
          assetMeasures: subplot.assetMeasures.map((am, mIdx) => {
            if (mIdx !== measureIndex) return am;
            return {
              ...am,
              selectedDbMeasureId: selectedDbMeasureId,
            };
          }),
        };
      });
      return updated;
    });
  };

  return (
    <Box display="flex" gap={2} alignItems="center">
      {/* Asset Dropdown */}
      {settings.mode === 'dashboard' ? (
        <>
          <FormControl fullWidth>
            <Autocomplete
              id={`asset-dropdown-${subplotIndex}-${measureIndex}`}
              options={assetOptions}
              disabled={isAssetLoading}
              loading={isAssetFetching}
              getOptionLabel={(option) => option.label}
              value={assetOptions.find((a) => String(a.id) === measure.assetId) || null}
              onChange={handleAssetChange}
              renderInput={(params) => (
                <TextField
                  {...params}
                  label="Select Asset"
                  variant="outlined"
                  InputProps={{
                    ...params.InputProps,
                    endAdornment: (
                      <>
                        {isAssetLoading && <CircularProgress color="inherit" size={20} />}
                        {params.InputProps.endAdornment}
                      </>
                    ),
                  }}
                />
              )}
            />
          </FormControl>

          {/* Measurement Dropdown */}
          <FormControl fullWidth>
            <Autocomplete
              id={`measurement-dropdown-${subplotIndex}-${measureIndex}`}
              options={measurementOptions}
              loading={isMeasurementLoading}
              getOptionLabel={(option) => option.label}
              value={measurementOptions.find((m) => m.id === measure.measureId?.[0]) || null}
              onChange={handleMeasurementChange}
              renderInput={(params) => (
                <TextField
                  {...params}
                  label="Select Measurement"
                  variant="outlined"
                  InputProps={{
                    ...params.InputProps,
                    endAdornment: (
                      <>
                        {isMeasurementLoading && <CircularProgress color="inherit" size={20} />}
                        {params.InputProps.endAdornment}
                      </>
                    ),
                  }}
                />
              )}
              disabled={!measure.assetId || isMeasurementLoading}
            />
          </FormControl>
        </>
      ) : (
        <>
          <FormControl sx={{ pt: 2, pb: 2 }} fullWidth>
            <Select
              sx={{
                width: '100%',
                p: 0.3,
                '& fieldset': {
                  '& legend': {
                    maxWidth: '100%',
                    height: 'auto',
                    '& span': {
                      opacity: 1,
                    },
                  },
                },
              }}
              value={measure.selectedDbMeasureId || ''}
              onChange={handleMeasureChange}
              label="Selected Metric"
            >
              {Object.keys(metricsIdToName).map((dbMeasureId) => {
                return (
                  <MenuItem key={dbMeasureId} value={dbMeasureId}>
                    {metricsIdToName[dbMeasureId]}
                  </MenuItem>
                );
              })}
            </Select>
          </FormControl>
        </>
      )}

      {/* Delete Button */}
      <Tooltip arrow title="Delete Asset Measure">
        <IconButton color="error" onClick={handleDelete}>
          <DeleteIcon />
        </IconButton>
      </Tooltip>
    </Box>
  );
};

export default AssetMeasureSelector;
