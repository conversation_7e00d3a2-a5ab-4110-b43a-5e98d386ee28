const { test, expect, request } = require('@playwright/test');

test.describe('API Test Suite', () => {
  test('GET /assets-backoffice/asset-types retrieves asset types successfully', async ({
    request,
  }) => {
    // Set headers
    const headers = {
      'BE-CsrfToken': 'dIG2dvjtXio+q/2bJ778ZhXwxCWVvfMDreE1BKwx7I8=',
      Cookie:
        'BE-AccessToken=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VybmFtZSI6InRlc3QiLCJzdWIiOjIsInJvbGVzIjp7IkFETUlOIjpbMTI4LDgsMTIxLDExOSwxMDYsMTE4LDEwOCw4NiwxMTEsMTEyLDg1LDEwNyw4NCwxMTMsMTE0LDExNywxMjAsMTI3LDEyMywxMjYsMTI0LDEwOSwxMjUsMTIyLDExMCw5LDgyXSwiVVNFUiI6WzEyOCw4LDEyMSwxMTksMTA2LDExOCwxMDgsODYsMTExLDExMiw4NSwxMDcsODQsMTEzLDExNCwxMTcsMTIwLDEyNywxMjMsMTI2LDEyNCwxMDksMTI1LDEyMiwxMTAsOSw4Ml0sIlBPV0VSX1VTRVIiOlsxMjgsOCwxMjEsMTE5LDEwNiwxMTgsMTA4LDg2LDExMSwxMTIsODUsMTA3LDg0LDExMywxMTQsMTE3LDEyMCwxMjcsMTIzLDEyNiwxMjQsMTA5LDEyNSwxMjIsMTEwLDksODJdfSwiaWF0IjoxNzMxNTY2Nzc2LCJleHAiOjE3MzE1NzM5NzZ9.5MH3lpO8v7D9-_qR7M0_VVDZsqWkk2gofOzLmVEf3zg; BE-CSRFToken=dIG2dvjtXio%2Bq%2F2bJ778ZhXwxCWVvfMDreE1BKwx7I8%3D',
    };

    // Make GET request
    const response = await request.get(
      'https://test.brompton.ai/api/v0/assets-backoffice/asset-types',
      {
        headers: headers,
      },
    );

    // Check response status
    expect(response.status()).toBe(200);

    // Verify response body if needed
    const responseBody = await response.json();
    console.log(responseBody);

    // Perform assertions on the response data
    expect(responseBody).toHaveProperty('items'); // Check that 'items' exists
    expect(Array.isArray(responseBody.items)).toBe(true); // Check if 'items' is an array
    expect(responseBody).toHaveProperty('total'); // Check that 'total' exists in the response
  });
});
