import { ThunkDispatch } from '@reduxjs/toolkit';
import { useRouter } from 'next/router';
import { useCallback, useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { measuresApi } from '~/redux/api/measuresApi';
import { timeseriesApi } from '~/redux/api/timeseriesApi';
import { getCustomerId } from '~/redux/selectors/customerSelectors';
import {
  getAssetTz,
  getGlobalSamplePeriod,
  getGlobalTimeRangeType,
} from '~/redux/selectors/topPanleSelectors';
import { getDbMeasureIdToAssetIdMap } from '~/redux/selectors/treeSelectors';
import { getGlobalEndDate, getGlobalStartDate } from '~/redux/selectors/widgetSelectors';
import { RootState } from '~/redux/store';
import { AggByOptions } from '~/types/dashboard';
import { AssetMeasurementDetails } from '~/types/measures';
import { SingleScatterTimeSeriesData } from '~/types/timeseries';
import { KpiCurrentWidget } from '~/types/widgets';

export const useFetchKPICurrentWidget = (settings: KpiCurrentWidget) => {
  const dispatch = useDispatch<ThunkDispatch<RootState, any, any>>();
  const router = useRouter();
  const [selectedTitles, setSelectedTitles] = useState<string[]>([]);
  useEffect(() => {
    if (
      settings.mode === 'dashboard' &&
      settings.assetMeasure.assetId !== '' &&
      settings.assetMeasure.measureId.some((measure) => measure !== '')
    ) {
      setSelectedTitles(settings.assetMeasure.measureId);
    }
    if (settings.mode === 'template') {
      setSelectedTitles([settings.selectedDbMeasureId]);
    }
  }, [settings.assetMeasure, settings.selectedDbMeasureId]);
  const customerId = useSelector(getCustomerId);
  const globalStartDate = useSelector(getGlobalStartDate);
  const globalEndDate = useSelector(getGlobalEndDate);
  const globalTimeRange = useSelector(getGlobalTimeRangeType);
  const globalSamplePeriod = useSelector(getGlobalSamplePeriod);
  const assetTz = useSelector(getAssetTz);
  const {
    startDate: selectedStartDate,
    endDate: selectedEndDate,
    aggBy: selectedAggBy,
    samplePeriod: selectedSamplePeriod,
    timeRange: selectedTimeRange,
    globalSamplePeriod: isGlobalSamplePeriodOverridden,
    overrideGlobalSettings: isGlobalTimeRangeSettingsOverridden,
    overrideAssetTz: isOverrideAssetTz,
    overrideAssetTzValue,
  } = settings;
  const [results, setResults] = useState<{
    isLoading: boolean;
    isError: boolean;
    data: SingleScatterTimeSeriesData[];
  }>({
    isLoading: true,
    isError: false,
    data: [],
  });
  const [startDate, setStartDate] = useState(globalStartDate);
  const [endDate, setEndDate] = useState(globalEndDate);
  const [aggBy, setAggBy] = useState(selectedAggBy);
  const [samplePeriod, setSamplePeriod] = useState(globalSamplePeriod);
  const [timeRange, setTimeRange] = useState(globalTimeRange);
  const [assetTzOverride, setAssetTzOverride] = useState(isOverrideAssetTz);
  const [assetTzOverrideValue, setAssetTzOverrideValue] = useState(overrideAssetTzValue);
  const dbMeasureIdToAssetIdMap = useSelector(getDbMeasureIdToAssetIdMap);
  const [measureData, setMeasureData] = useState<AssetMeasurementDetails | null>(null);
  const [unitOfMeasure, setUnitOfMeasure] = useState('');

  useEffect(() => {
    setAggBy(selectedAggBy);
    if (isGlobalTimeRangeSettingsOverridden) {
      setStartDate(selectedStartDate);
      setEndDate(selectedEndDate);
      setTimeRange(selectedTimeRange);
    } else {
      setStartDate(globalStartDate);
      setEndDate(globalEndDate);
      setTimeRange(globalTimeRange);
    }

    if (isGlobalSamplePeriodOverridden) {
      setSamplePeriod(selectedSamplePeriod);
    } else {
      setSamplePeriod(globalSamplePeriod);
    }
    if (isOverrideAssetTz) {
      setAssetTzOverride(true);
      setAssetTzOverrideValue(overrideAssetTzValue);
    } else {
      setAssetTzOverride(false);
    }
  }, [
    selectedStartDate,
    selectedEndDate,
    selectedAggBy,
    selectedSamplePeriod,
    selectedTimeRange,
    globalStartDate,
    globalEndDate,
    globalTimeRange,
    globalSamplePeriod,
    isGlobalSamplePeriodOverridden,
    isGlobalTimeRangeSettingsOverridden,
    isOverrideAssetTz,
    overrideAssetTzValue,
  ]);

  const fetchMeasureData = useCallback(
    async (measureId: string) => {
      if (!dbMeasureIdToAssetIdMap[measureId]) {
        throw new Error(`No assetId found for ${measureId}`);
      }
      if (!measureId) {
        throw new Error(`Error invalid measureId: ${measureId}`);
      }
      const { data: measureData, isSuccess: isMeasureDataSuccess } = await dispatch(
        measuresApi.endpoints?.getMeasurementById.initiate({
          customerId,
          assetId: dbMeasureIdToAssetIdMap[measureId],
          measId: measureId,
        }),
      );

      if (!isMeasureDataSuccess || !measureData) {
        throw new Error(`Error fetching measure data for measureId: ${measureId}`);
      }

      return measureData;
    },
    [customerId, dbMeasureIdToAssetIdMap, dispatch],
  );

  const fetchUnitOfMeasure = useCallback(
    async (typeId: number) => {
      const { data: unitOfMeasure, isSuccess: isUnitOfMeasureSuccess } = await dispatch(
        measuresApi.endpoints?.getUnitsOfMeasure.initiate({ measurementTypeId: typeId }),
      );
      if (!isUnitOfMeasureSuccess || !unitOfMeasure) {
        throw new Error(`Error fetching unit of measure for typeId: ${typeId}`);
      }
      return unitOfMeasure;
    },
    [dispatch],
  );
  const fetchTimeseriesData = useCallback(
    async (
      tsDbMeasureIds: number[],
      agg: {
        meas_id: number;
        start: number;
        end: number;
        agg: string;
        agg_period: string;
        use_asset_tz: boolean;
        browser_tz: boolean;
      },
    ): Promise<{ error: boolean; tsData: Record<number, SingleScatterTimeSeriesData> }> => {
      const { data: tsData, isSuccess: isTsSuccess } = await dispatch(
        timeseriesApi.endpoints.getMultiMeasurementSeries.initiate({
          customerId,
          ...agg,
          measId: tsDbMeasureIds.filter(Boolean).sort().join(','),
          timeRangeType: timeRange,
          assetTz: isOverrideAssetTz ? assetTzOverrideValue : assetTz,
        }),
      );

      if (!isTsSuccess || !tsData) {
        return {
          error: true,
          tsData: {},
        };
      }
      return { error: false, tsData };
    },
    [
      dispatch,
      customerId,
      startDate,
      endDate,
      aggBy,
      timeRange,
      isOverrideAssetTz,
      assetTzOverrideValue,
      settings.selectedDbMeasureId,
      settings.samples,
      assetTz,
      settings.period,
      settings.samples,
    ],
  );
  const fetchMeasureDataByAsset = useCallback(
    async (assetId: string, measureId: string) => {
      if (!assetId || assetId === '') {
        throw new Error(`No assetId found for ${measureId}`);
      }
      if (!measureId) {
        throw new Error(`Error invalid measureId: ${measureId}`);
      }
      const { data: measureData, isSuccess: isMeasureDataSuccess } = await dispatch(
        measuresApi.endpoints?.getMeasurementById.initiate({
          customerId,
          assetId: assetId,
          measId: measureId,
        }),
      );
      if (!isMeasureDataSuccess || !measureData) {
        throw new Error(`Error fetching measure data for measureId: ${measureId}`);
      }

      return measureData;
    },
    [customerId, dbMeasureIdToAssetIdMap, dispatch, selectedTitles],
  );
  useEffect(() => {
    const fetchData = async () => {
      try {
        setResults({
          isLoading: true,
          isError: false,
          data: [],
        });

        const allResults = await Promise.all(
          settings.assetMeasure.measureId
            .filter((measure) => measure !== '' && measure !== null)
            .map(async (measure) => {
              const measureData = await fetchMeasureDataByAsset(
                settings.assetMeasure.assetId,
                measure,
              );
              const unitOfMeasures = await fetchUnitOfMeasure(measureData.typeId);
              const uom =
                unitOfMeasures.find((unit) => unit.id === measureData.unitOfMeasureId)?.name ?? '';
              setUnitOfMeasure(uom);
              const aggs = settings.samples
                .map((sample) => {
                  const endDate = new Date(Date.now()).getTime();
                  let startDate = new Date(Date.now() - 24 * 60 * 60 * 1000).getTime();
                  if (sample?.toUpperCase() === '1HR') {
                    startDate = new Date(Date.now() - 3 * 60 * 60 * 1000).getTime();
                  }
                  if (sample?.toUpperCase() === 'DAILY') {
                    startDate = new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).getTime();
                  }
                  if (sample?.toUpperCase() === 'WEEKLY') {
                    startDate = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).getTime();
                  }
                  if (sample?.toUpperCase() === 'MONTHLY') {
                    startDate = new Date(Date.now() - 60 * 24 * 60 * 60 * 1000).getTime();
                  }
                  return {
                    meas_id: measureData.measurementId,
                    start: startDate,
                    end: endDate,
                    agg: AggByOptions[aggBy].serverValue,
                    agg_period: sample?.toUpperCase(),
                    use_asset_tz: assetTzOverride,
                    browser_tz: assetTzOverrideValue,
                  };
                })
                .filter(Boolean);

              const response = await Promise.all(
                aggs.map(async (agg) => {
                  const { error, tsData } = await fetchTimeseriesData([measureData.measurementId], {
                    meas_id: measureData.measurementId,
                    start: agg.start,
                    end: agg.end,
                    agg: AggByOptions[aggBy].serverValue,
                    agg_period: agg.agg_period as string,
                    use_asset_tz: isOverrideAssetTz ? assetTzOverrideValue : assetTz,
                    browser_tz: assetTzOverrideValue,
                  });
                  if (error) {
                    return {
                      tsData: null,
                      error: true,
                      isLoading: false,
                    };
                  }
                  return {
                    tsData: tsData[measureData.measurementId],
                    error: false,
                    isLoading: false,
                  };
                }),
              );

              return response;
            }),
        );

        const data = (await Promise.all(allResults)).flat();
        setResults({
          isLoading: false,
          isError: false,
          data: data.map((d) => d.tsData).filter(Boolean) as SingleScatterTimeSeriesData[],
        });
      } catch (e) {
        setResults({
          isLoading: false,
          isError: true,
          data: [],
        });
      }
    };

    if (
      router.pathname !== '/dashboard-template' &&
      (settings.selectedDbMeasureId !== '' || selectedTitles.length > 0)
    ) {
      fetchData();
    }
    if (
      router.pathname === '/dashboard-template' &&
      (settings.selectedDbMeasureId !== '' || selectedTitles.length > 0)
    ) {
      const generateSampleData = (period: string): [number, number][] => {
        switch (period) {
          case 'WEEKLY':
            return [
              [1733316092821, 59.47849759244248],
              [1733920892821, 59.482032505203755],
              [1734525692821, 62.15974544039153],
              [1735130492821, 63.92969161677074],
              [1735735292821, 60.82888999210145],
            ];
          case 'DAILY':
            return [
              [1735648892821, 65.44460733872704],
              [1735735292821, 62.30591060555181],
              [1735821692821, 57.44129808790034],
            ];
          case 'MONTHLY':
            return [
              [1730419200000, 57.162665628292956],
              [1733011200000, 61.18203836360079],
            ];
          default:
            return [];
        }
      };

      const sampleData = settings.samples
        .filter((sample) => sample !== null && sample !== undefined)
        .map((sample) => {
          if (sample !== null && sample !== undefined) {
            return {
              tag: 18577,
              period: sample.toUpperCase(),
              'ts,val': generateSampleData(sample.toUpperCase()),
              tag_meta: {
                uom: 'volts',
              },
            };
          }
          return null;
        })
        .filter((data): data is NonNullable<typeof data> => data !== null);

      setResults({
        isLoading: false,
        isError: false,
        data: sampleData,
      });
    }
  }, [
    measureData,
    selectedTitles,
    settings.selectedDbMeasureId,
    settings.samples,
    settings.period,
    startDate,
    endDate,
    aggBy,
    timeRange,
    assetTzOverride,
    assetTzOverrideValue,
  ]);
  return {
    unitOfMeasure,
    results,
  };
};
