import { useSelector } from 'react-redux';
import { getActiveCustomer } from '~/redux/selectors/customerSelectors';

const allowedSolarWidgetCustomers = ['all'];
const disallowedSolarWidgetCustomers = [] as string[];
const allowedGasWidgetCustomers = ['all'];
const disallowedGasWidgetCustomers = [] as string[];

type FeatureName = 'solar_panel' | 'static';
const useShowBetaFeatures = (featureName: FeatureName) => {
  const activeCustomer = useSelector(getActiveCustomer);
  if (activeCustomer == null) {
    return false;
  }

  let enabledIds = [] as string[];
  let disabledIds = [] as string[];
  if (featureName === 'solar_panel') {
    enabledIds = allowedSolarWidgetCustomers;
    disabledIds = disallowedSolarWidgetCustomers;
  } else if (featureName === 'static') {
    enabledIds = allowedGasWidgetCustomers;
    disabledIds = disallowedGasWidgetCustomers;
  }

  if (disabledIds.includes('all')) {
    return false;
  } else if (disabledIds.includes(activeCustomer.nameId)) {
    return false;
  } else if (enabledIds.includes(activeCustomer.nameId)) {
    return true;
  } else if (enabledIds.includes('all')) {
    return true;
  }

  return false;
};
export default useShowBetaFeatures;
