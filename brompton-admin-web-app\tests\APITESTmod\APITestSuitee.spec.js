const { test, expect } = require('@playwright/test');
const { CustomerTestCases } = require('./modules/customers');
const { userTestCases } = require('./modules/user');
const { DashboardTestCases } = require('./modules/dashboard');
const { MeasurementsTestCases } = require('./modules/measurements');
const { assetTestCasess } = require('./modules/assets');
/**
 * Authenticate and retrieve tokens
 */
async function authenticate(request) {
  try {
    const authResponse = await request.post('https://test.pivotol.ai/api/v0/sessions', {
      headers: { 'Content-Type': 'application/json' },
      data: { username: 'test', password: 'Br0mpt0n!0T' },
    });

    const status = authResponse.status();
    const responseBody = await authResponse.text();

    console.log('Authentication Response:', { status, responseBody });

    if (![200, 201].includes(status)) {
      throw new Error(`Authentication failed. Status: ${status}, Response: ${responseBody}`);
    }

    const authData = JSON.parse(responseBody);
    if (!authData.access_token || !authData.csrf_token) {
      throw new Error('Authentication tokens are missing in the response.');
    }

    return {
      accessToken: `BE-AccessToken=${authData.access_token}; BE-CSRFToken=${authData.csrf_token}`,
      csrfToken: authData.csrf_token,
    };
  } catch (error) {
    console.error('Error during authentication:', error.message);
    throw error;
  }
}

/**
 * Generic API request function with retry on 401 Unauthorized
 */
/*
async function makeApiRequest(request, authTokens, { method, url, headers = {}, body = null }, expectedStatus) {
    if (!authTokens || !authTokens.csrfToken || !authTokens.accessToken) {
        console.error('authTokens is invalid or undefined. Check authentication.');
        throw new Error('authTokens is missing or invalid. Ensure authentication succeeded.');
    }

    const requestOptions = {
        headers: {
            ...headers,
            "BE-CSRFToken": authTokens.csrfToken,
            "Cookie": authTokens.accessToken,
        },
    };

    if (method.toUpperCase() !== 'GET' && body) {
        requestOptions.data = body;
    }

    try {
        let response = await request[method.toLowerCase()](url, requestOptions);

        if (response.status() === 401) {
            console.warn('401 Unauthorized - Re-authenticating...');
            authTokens = await authenticate(request); // Refresh tokens
            console.log('Re-authenticated successfully. New Tokens:', authTokens);

            // Update headers with refreshed tokens
            requestOptions.headers["BE-CSRFToken"] = authTokens.csrfToken;
            requestOptions.headers["Cookie"] = authTokens.accessToken;

            response = await request[method.toLowerCase()](url, requestOptions);
        }
        
        const status = response.status();
        const responseBody = await response.text();
        console.log(`Response for ${method} ${url}:`, { status, responseBody });

        if (status === 409 || (status === 400 && responseBody.includes('User already exists'))) {
            console.warn(`Conflict detected at ${url}. Response: ${responseBody}`);
            const conflictData = JSON.parse(responseBody);
            return { conflict: true, conflictData };
        }
        if (status !== expectedStatus) {
            console.error(`Expected status ${expectedStatus}, got ${status}. Response: ${responseBody}`);
            throw new Error(`Test failed for ${url}: Expected status ${expectedStatus}, got ${status}.`);
        }
        if (status === 403) {
            console.warn(`403 Forbidden: Ensure proper permissions for ${url}`);
        }
``
        return responseBody ? JSON.parse(responseBody) : null;
    } catch (error) {
        console.error('Error during API request:', error.message);
        throw error;
    }
}
    */
async function makeApiRequest(
  request,
  authTokens,
  { method, url, headers = {}, body = null },
  expectedStatus,
) {
  if (!authTokens || !authTokens.csrfToken || !authTokens.accessToken) {
    console.error('authTokens is invalid or undefined. Check authentication.');
    return { error: 'authTokens is missing or invalid. Ensure authentication succeeded.' };
  }

  const requestOptions = {
    headers: {
      ...headers,
      'BE-CSRFToken': authTokens.csrfToken,
      Cookie: authTokens.accessToken,
      Authorization: `Bearer ${authTokens}`,
    },
  };

  if (method.toUpperCase() !== 'GET' && body) {
    requestOptions.data = body;
  }

  try {
    let response = await request[method.toLowerCase()](url, requestOptions);

    if (response.status() === 401) {
      console.warn('401 Unauthorized - Re-authenticating...');
      authTokens = await authenticate(request); // Refresh tokens
      console.log('Re-authenticated successfully. New Tokens:', authTokens);

      // Update headers with refreshed tokens
      requestOptions.headers['BE-CSRFToken'] = authTokens.csrfToken;
      requestOptions.headers['Cookie'] = authTokens.accessToken;

      response = await request[method.toLowerCase()](url, requestOptions);
    }

    const status = response.status();
    const responseBody = await response.text();
    console.log(`Response for ${method} ${url}:`, { status, responseBody });

    if (status === 409 || (status === 400 && responseBody.includes('User already exists'))) {
      console.warn(`Conflict detected at ${url}. Response: ${responseBody}`);
      const conflictData = JSON.parse(responseBody);
      return { conflict: true, conflictData };
    }

    if (status !== expectedStatus) {
      console.log(`Received unexpected status ${status} for ${url}. Response: ${responseBody}`);
    }

    if (status === 403) {
      console.warn(`403 Forbidden: Ensure proper permissions for ${url}`);
    }

    return responseBody ? JSON.parse(responseBody) : null;
  } catch (error) {
    console.error('Error during API request:', error.message);
    return { error: error.message };
  }
}

/**
 * Data-driven test cases
 */
const testCases = [
  ...userTestCases,
  ...CustomerTestCases,
  ...DashboardTestCases,
  ...MeasurementsTestCases,
  ...assetTestCasess,
];

/**
 * Test suite
 */

test.describe('Generic API Test Suite', () => {
  let authTokens;

  test.beforeAll(async ({ request }) => {
    authTokens = await authenticate(request);
    console.log('Authentication successful. Tokens:', authTokens);
  });

  for (const testCase of testCases) {
    test(testCase.description, async ({ request }) => {
      const response = await makeApiRequest(
        request,
        authTokens,
        testCase.requestConfig,
        testCase.expectedStatus,
      );
      await testCase.validate(response, request, authTokens);
    });
  }
});
