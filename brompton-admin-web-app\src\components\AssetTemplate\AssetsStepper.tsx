import EditIcon from '@mui/icons-material/Edit';
import {
  Alert,
  Autocomplete,
  Card,
  CardContent,
  CardHeader,
  Checkbox,
  FormControl,
  FormControlLabel,
  FormHelperText,
  FormLabel,
  Grid,
  Paper,
  Radio,
  RadioGroup,
  Stack,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Typography,
} from '@mui/material';
import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import Step from '@mui/material/Step';
import StepLabel from '@mui/material/StepLabel';
import Stepper from '@mui/material/Stepper';
import TextField from '@mui/material/TextField';
import { useEffect, useState } from 'react';
import { Controller } from 'react-hook-form';
import { CustomError } from '~/errors/CustomerErrorResponse';
import useCreateAssetTemplateHelper from '~/hooks/useCreateAssetTemplateHelper';
import { useHasAdminAccess } from '~/hooks/useHasAdminAccess';
import { measurementSchemaData } from '~/measurements/domain/types';
import { useCreateAssetTemplateMultiMutation } from '~/redux/api/assetsApi';
import { ControlledAutocomplete } from '~/shared/forms/components/ControlledAutocomplete';
import { AlertMessage } from '~/shared/forms/types';
import { AssetTypeOption } from '~/types/asset';
import { calc_engine_template, poll_period } from '~/types/calc_engine';
import { assetTypePathMapper } from '~/utils/mappers/asset-type-mapper';
import ExpressionTemplateDetails from '../CalcEngine/ExpressionTemplateDetails';
import MeasureTable from './MeasureTable';

export default function AssetsStepper() {
  const [activeStep, setActiveStep] = useState(0);
  const [measurements, setMeasurements] = useState<measurementSchemaData[]>([]);
  const [assetTypesWithPath, setAssetTypesWithPath] = useState<AssetTypeOption[]>([]);
  const [currentMeausreIndex, setCurrentMeasureIndex] = useState<number>(-1);
  const [currentCalcMeasureIndex, setCurrentCalcMeasureIndex] = useState<number>(-1);
  const [isEdit, setIsEdit] = useState<boolean>(false);
  const [isFirst, setIsFirst] = useState<boolean>(true);
  const { globalAdmin, admin } = useHasAdminAccess();

  const {
    isSuccessfullBackOffieAssetTypes,
    isFetchingBackOfficeAssetTypes,
    assetTypeListData,
    valueTypeOptions,
    datasourceOptions,
    locationsListOption,
    dataTypesListOptions,
    measurementTypeListOptions,
    steps,
    calculatedMeasures,
    isLastStep,
    assetTypeMetricsListOptions,
    hasDuplicates,
    handleBack,
    calculationSource,
    fetchingExpressionTemplates,
    expressionTemplates,
    pollPeriods,
    calcMeasurements,
    setCalcMeasurements,
    firstStep: { control, getValues, handleSubmit, reset },
    measurements: {
      getMeasureValues,
      measureErrors,
      measurementSubmit,
      measurementsControl,
      resetMeasure,
      setMeasureValue,
    },
    calculationMeasurement: {
      calcMeasurementController,
      calcMeasureHandleSubmit,
      calcMeasureError,
      calcMeasureWatch,
      resetCalcMeasureValues,
      calcMeasureSetValue,

      fields,
    },
  } = useCreateAssetTemplateHelper({
    activeStep,
    measurements,
    currentMeausreIndex,
    setActiveStep,
  });
  const variableInputs = calcMeasureWatch('variable_inputs');
  const [
    createAssetTemplateMulti,
    {
      isError: isMultiError,
      isSuccess: isMultiSuccess,
      data: createAssetTemplateMultiData,
      error: multiError,
      isLoading: isMultiLoading,
    },
  ] = useCreateAssetTemplateMultiMutation();
  useEffect(() => {
    if (isMultiSuccess && createAssetTemplateMultiData) {
      setAlertMessage({
        message: `Asset Template "${createAssetTemplateMultiData.id}" created successfully!`,
        severity: 'success',
      });
      const timeout = setTimeout(() => {
        setActiveStep(0);
        resetCalcMeasureValues();
        reset();
        setMeasureValue('data_type_id', null);
        setMeasureValue('value_type_id', null);
        setMeasureValue('type_id', null);
        setMeasureValue('metric_id', null);
        setMeasureValue('description', '');
        setMeasureValue('location_id', null);
        setMeasureValue('datasource_id', null);
        setMeasureValue('meter_factor', undefined);
        setMeasurements([]);
        setCalcMeasurements([]);
        setCurrentMeasureIndex(-1);
        setCurrentCalcMeasureIndex(-1);
        setIsEdit(false);
        setIsFirst(true);
        resetMeasure();
        resetCalcMeasureValues();
      }, 5000);

      return () => clearTimeout(timeout);
    }

    if (isMultiError && multiError) {
      const err = multiError as CustomError;
      setAlertMessage({
        message: err.data?.exception ?? 'Server error',
        severity: 'error',
      });
    }
  }, [isMultiSuccess, createAssetTemplateMultiData, isMultiError, multiError]);

  const [alertMessage, setAlertMessage] = useState<AlertMessage | undefined>(undefined);
  const expressionTemplate = calcMeasureWatch('expression_template_id');
  const [expression, setExpression] = useState<calc_engine_template | null>(null);

  useEffect(() => {
    if (!expressionTemplate || !expressionTemplates) {
      setExpression(null);
      calcMeasureSetValue('variable_inputs', []);
      return;
    }

    const expressionTemplateToFind = expressionTemplates.items.find(
      (template) => template.id === expressionTemplate,
    );

    if (!expressionTemplateToFind) {
      setExpression(null);
      calcMeasureSetValue('variable_inputs', []);
      return;
    }

    setExpression(expressionTemplateToFind);
    calcMeasureSetValue('expression_template_id', expressionTemplateToFind.id);

    const variableMatches = expressionTemplateToFind.expression.match(/\$[A-Za-z0-9_]+/g);
    const uniqueVariables = Array.from(new Set(variableMatches ?? []));
    const defaultVariableInputs = uniqueVariables.map((variable) => ({
      variable,
      type: 'measurement' as const,
      metric_id: null,
      constant_value: null,
      comment: '',
    }));

    const currentMetricId = measurements[currentCalcMeasureIndex]?.metric_id?.toString();

    const matchedCalc = calcMeasurements.find((calc) => calc.metric_id === currentMetricId);

    const updatedVariableInputs = defaultVariableInputs.map((input) => {
      const existing = matchedCalc?.calcMeasurementData.variable_inputs.find(
        (v) => v.variable === input.variable,
      );
      return {
        ...input,
        type: existing?.type ?? input.type,
        metric_id: existing?.metric_id ?? null,
        constant_value: existing?.constant_value ?? null,
        comment: existing?.comment ?? '',
      };
    });

    // Set form state
    calcMeasureSetValue('variable_inputs', updatedVariableInputs);

    // Update calcMeasurements for this specific metric
    setCalcMeasurements((prev) =>
      prev.map((calc) => {
        if (calc.metric_id === currentMetricId) {
          return {
            ...calc,
            variable: updatedVariableInputs,
            calcMeasurementData: {
              ...calc.calcMeasurementData,
              variable_inputs: updatedVariableInputs,
            },
          };
        }
        return calc;
      }),
    );
  }, [expressionTemplate, expressionTemplates]);
  const handleNext = handleSubmit(async () => {
    if (isLastStep) {
      const stringMetrics = measurements.filter((measure) => isNaN(Number(measure.metric_id)));
      createAssetTemplateMulti({
        metrics: stringMetrics
          .map((measure) => measure.metric_id?.toString())
          .filter((id): id is string => id !== undefined),
        newTemplate: {
          ...getValues(),
          measurements: measurements.map((measure) => ({
            ...measure,

            datasource_id:
              measure.datasource_id !== undefined && measure.datasource_id !== null
                ? measure.datasource_id
                : undefined,
          })),
        },
        expressionInstance: calcMeasurements.map((calc) => ({
          templateId: calc.calcMeasurementData.expression_template_id ?? null,
          ispersisted: calc.calcMeasurementData.is_persisted ?? false,
          pollPeriod: calc.calcMeasurementData.poll_period?.id ?? undefined,
          metricId: calc.metric_id?.toString() ?? '',
          iswriteback: calc.calcMeasurementData.writeback ?? false,
          variables: calc.calcMeasurementData.variable_inputs.map((vars) => ({
            inputLabel: vars.variable,
            metric: vars.metric_id?.toString() ?? undefined,
            constantType: vars.constant_value ? 'number' : undefined,
            constantValue: vars.constant_value?.toString() ?? undefined, // Ensure constantValue is a string
            comment: vars.comment ?? undefined,
          })),
        })),
      });
    } else {
      setActiveStep((prevActiveStep) => prevActiveStep + 1);
    }
  });

  const handleMeasure = measurementSubmit((data) => {
    resetMeasure();
    const metricIdStr = data.metric_id?.toString() ?? '';
    const isCalcMeasurement = data.datasource_id === calculationSource?.id;
    const addCalcMeasurement = () => {
      setCalcMeasurements((prev) => [
        ...prev,
        {
          metric_id: metricIdStr,
          calcMeasurementData: {
            expression_template_id: null,
            is_persisted: false,
            poll_period: null,
            writeback: false,
            variable_inputs: [],
          },
        },
      ]);
    };
    if (!isEdit) {
      setIsFirst(false);
      setMeasurements((prev) => [...prev, { ...data }]);

      if (isCalcMeasurement) {
        addCalcMeasurement();
      } else {
        setCalcMeasurements((prev) => prev.filter((calc) => calc.metric_id !== metricIdStr));
      }
    } else {
      resetMeasure({
        type_id: undefined,
        data_type_id: undefined,
        value_type_id: undefined,
        metric_id: undefined,
        description: '',
        location_id: undefined,
        datasource_id: undefined,
        meter_factor: undefined,
      });
      setIsEdit(false);

      setMeasurements((prev) =>
        prev.map((item, idx) => (idx === currentMeausreIndex ? { ...item, ...data } : item)),
      );
      if (isCalcMeasurement) {
        const existingCalcMeasurement = calcMeasurements.find(
          (calc) => calc.metric_id === metricIdStr,
        );

        if (existingCalcMeasurement) {
          // Update variable_inputs for existing
          setCalcMeasurements((prev) =>
            prev.map((calc) =>
              calc.metric_id === metricIdStr
                ? {
                    ...calc,
                    calcMeasurementData: {
                      ...calc.calcMeasurementData,
                      variable_inputs: [
                        ...calc.calcMeasurementData.variable_inputs,
                        {
                          variable: '',
                          type: 'measurement',
                          metric_id: null,
                          constant_value: null,
                          comment: '',
                        },
                      ],
                    },
                  }
                : calc,
            ),
          );
        } else {
          addCalcMeasurement();
        }
      } else {
        setCalcMeasurements((prev) => prev.filter((calc) => calc.metric_id !== metricIdStr));
      }
    }
    setCurrentMeasureIndex(-1);
  });

  useEffect(() => {
    if (measurements.length === 0) {
      setIsFirst(true);
    }
  }, [measurements]);

  useEffect(() => {
    if (isSuccessfullBackOffieAssetTypes && assetTypeListData) {
      setAssetTypesWithPath(
        assetTypePathMapper(
          assetTypeListData.map((item) => ({
            ...item,
            name: `${item.name}`,
            id: item.id,
          })),
        ),
      );
    }
  }, [assetTypeListData, isSuccessfullBackOffieAssetTypes]);
  let hasInvalidVariable = false;

  calcMeasurements.forEach((measure) => {
    if (measure.calcMeasurementData.variable_inputs.length === 0) {
      hasInvalidVariable = true;
    }
    measure.calcMeasurementData.variable_inputs.forEach((varis) => {
      if (varis.type === 'measurement') {
        if (varis.metric_id === null || varis.metric_id === undefined) {
          hasInvalidVariable = true;
        }
      } else if (varis.type === 'constant') {
        if (varis.constant_value === null || varis.constant_value === undefined) {
          hasInvalidVariable = true;
        }
      } else {
        hasInvalidVariable = true; // invalid type
      }
    });
  });
  return (
    <>
      <Stepper activeStep={activeStep} alternativeLabel>
        {steps.filter(Boolean).map((label) => (
          <Step key={label}>
            <StepLabel>{label}</StepLabel>
          </Step>
        ))}
      </Stepper>
      <Box sx={{ mt: 2 }}>
        {activeStep === 0 ? (
          <>
            <form onSubmit={(e) => e.preventDefault()}>
              <Controller
                name="assetTypeId"
                control={control}
                render={({ field: { onChange, onBlur, value }, fieldState }) => (
                  <Autocomplete
                    options={assetTypesWithPath}
                    loading={isFetchingBackOfficeAssetTypes}
                    renderInput={(params) => (
                      <TextField
                        {...params}
                        label="Asset type"
                        error={!!fieldState.error}
                        helperText={fieldState.error?.message}
                      />
                    )}
                    onChange={(_, value) => onChange(value?.value ?? null)}
                    onBlur={onBlur}
                    value={
                      assetTypesWithPath.find((assetType) => assetType.value === value) ?? null
                    }
                    sx={{ mt: 2, mb: 1 }}
                    fullWidth
                  />
                )}
              />
              <Controller
                name="manufacturer"
                control={control}
                render={({ field, fieldState: { error } }) => (
                  <TextField
                    {...field}
                    label="Manufacturer"
                    error={!!error}
                    helperText={error?.message}
                    fullWidth
                    margin="normal"
                  />
                )}
              />
              <Controller
                name="model_number"
                control={control}
                render={({ field, fieldState: { error } }) => (
                  <TextField
                    {...field}
                    label="Model Number"
                    error={!!error}
                    helperText={error?.message}
                    fullWidth
                    margin="normal"
                  />
                )}
              />
              <Controller
                name="save_as_global_asset_template"
                control={control}
                render={({ field }) => (
                  <FormControlLabel
                    control={
                      <Checkbox
                        {...field}
                        disabled={!globalAdmin}
                        checked={field.value || false}
                        onChange={(e) => field.onChange(e.target.checked)}
                      />
                    }
                    label="Save as Global Asset Template"
                  />
                )}
              />
            </form>
          </>
        ) : null}
        {activeStep === 1 ? (
          <>
            {hasDuplicates() ? (
              <Typography color="red">Metric can not be duplicate</Typography>
            ) : null}
            <form onSubmit={(e) => e.preventDefault()}>
              <Card>
                <CardHeader
                  title={
                    isEdit ? 'Edit Measurement #' + (currentMeausreIndex + 1) : 'Add Measurement'
                  }
                />
                <CardContent>
                  <Box sx={{ display: 'flex', flexDirection: 'row', mb: 2, gap: 4 }}>
                    <Grid container spacing={2}>
                      <Grid item xs={12} sm={4}>
                        <ControlledAutocomplete
                          // eslint-disable-next-line @typescript-eslint/ban-ts-comment
                          //@ts-ignore
                          control={measurementsControl}
                          value={getMeasureValues().type_id ?? null}
                          fieldName={`type_id`}
                          label="Type Id"
                          options={measurementTypeListOptions}
                          required
                        />
                      </Grid>
                      <Grid item xs={12} sm={4}>
                        <ControlledAutocomplete
                          // eslint-disable-next-line @typescript-eslint/ban-ts-comment
                          //@ts-ignore
                          control={measurementsControl}
                          value={getMeasureValues()?.data_type_id}
                          fieldName={`data_type_id`}
                          label="Data Type"
                          options={dataTypesListOptions}
                          required
                        />
                      </Grid>
                      <Grid item xs={12} sm={4}>
                        <ControlledAutocomplete
                          // eslint-disable-next-line @typescript-eslint/ban-ts-comment
                          //@ts-ignore
                          control={measurementsControl}
                          value={getMeasureValues()?.value_type_id}
                          fieldName={`value_type_id`}
                          label="Select value type"
                          options={valueTypeOptions}
                          required
                        />
                      </Grid>
                      <Grid item xs={12} sm={4}>
                        <Controller
                          name="metric_id"
                          control={measurementsControl}
                          render={({ field }) => {
                            const selectedOption = assetTypeMetricsListOptions.find(
                              (option) => String(option.id) === String(field.value),
                            );
                            return (
                              <Autocomplete
                                freeSolo
                                options={assetTypeMetricsListOptions}
                                getOptionLabel={(option) =>
                                  typeof option === 'string' ? option : String(option.label)
                                }
                                isOptionEqualToValue={(option, value) =>
                                  typeof option === 'string'
                                    ? option === value
                                    : String(option.id) === String(value.id)
                                }
                                value={
                                  selectedOption ||
                                  (field.value ? { id: field.value, label: field.value } : null)
                                }
                                onChange={(_, newValue) => {
                                  if (typeof newValue === 'string') {
                                    field.onChange(newValue);
                                  } else if (newValue && typeof newValue.id === 'string') {
                                    field.onChange(newValue.id);
                                  } else {
                                    field.onChange(null);
                                  }
                                }}
                                renderInput={(params) => (
                                  <TextField
                                    {...params}
                                    label="Metric"
                                    fullWidth
                                    value={
                                      selectedOption ||
                                      (field.value ? { id: field.value, label: field.value } : null)
                                    }
                                    margin="normal"
                                    onChange={(event) => {
                                      const newValue = event.target.value as
                                        | string
                                        | {
                                            id: string;
                                            label: string;
                                          };
                                      if (typeof newValue === 'string') {
                                        field.onChange(newValue);
                                      } else if (newValue && typeof newValue.id === 'string') {
                                        field.onChange(newValue.id);
                                      } else {
                                        field.onChange(null);
                                      }
                                    }}
                                    error={!!measureErrors?.metric_id}
                                    helperText={measureErrors?.metric_id?.message || ''}
                                    required
                                  />
                                )}
                              />
                            );
                          }}
                        />
                      </Grid>

                      <Grid item xs={12} sm={4}>
                        <Controller
                          name={`description`}
                          control={measurementsControl}
                          render={({ field }) => (
                            <TextField
                              sx={{
                                mt: 2,
                              }}
                              defaultValue={
                                getMeasureValues().description
                                // ? getMeasureValues()?.description
                                // : ''
                              }
                              value={
                                getMeasureValues().description
                                // ? getMeasureValues()?.description
                                // : ''
                              }
                              label="Description"
                              error={!!measureErrors?.description}
                              helperText={measureErrors?.description?.message || ''}
                              fullWidth
                              onChange={(e) => field.onChange(e.target.value)}
                            />
                          )}
                        />
                      </Grid>
                      <Grid item xs={12} sm={4}>
                        <ControlledAutocomplete
                          // eslint-disable-next-line @typescript-eslint/ban-ts-comment
                          //@ts-ignore
                          control={measurementsControl}
                          value={getMeasureValues()?.location_id}
                          fieldName={`location_id`}
                          label="Location"
                          options={locationsListOption}
                        />
                      </Grid>
                      <Grid item xs={12} sm={4}>
                        <ControlledAutocomplete
                          // eslint-disable-next-line @typescript-eslint/ban-ts-comment
                          //@ts-ignore
                          control={measurementsControl}
                          value={getMeasureValues()?.datasource_id}
                          fieldName={`datasource_id`}
                          label="Data Source"
                          options={datasourceOptions}
                        />
                      </Grid>
                      <Grid item xs={12} sm={4}>
                        <Controller
                          name={`meter_factor`}
                          control={measurementsControl}
                          render={({ field }) => (
                            <TextField
                              sx={{
                                mt: 2,
                              }}
                              {...field}
                              label="Meter Factor"
                              defaultValue={
                                getMeasureValues()?.meter_factor
                                  ? getMeasureValues()?.meter_factor
                                  : ''
                              }
                              value={
                                getMeasureValues()?.meter_factor
                                  ? getMeasureValues()?.meter_factor
                                  : ''
                              }
                              type="number"
                              error={!!measureErrors.meter_factor}
                              helperText={measureErrors.meter_factor?.message || ''}
                              fullWidth
                              onChange={(e) => field.onChange(e.target.value)}
                            />
                          )}
                        />
                      </Grid>
                    </Grid>
                  </Box>
                </CardContent>
                <Button
                  type="submit"
                  sx={{ ml: 3, mb: 3 }}
                  variant="contained"
                  onClick={handleMeasure}
                >
                  {isEdit ? 'Update' : 'Add'}
                </Button>
                <Button
                  sx={{ ml: 3, mb: 3 }}
                  variant="outlined"
                  onClick={() => {
                    setCurrentMeasureIndex(-1);
                    setIsEdit(false);
                    resetMeasure();
                  }}
                >
                  Cancel
                </Button>
              </Card>
            </form>
            {!isFirst ? (
              <MeasureTable
                fields={measurements}
                remove={(i) => {
                  setMeasurements(measurements.filter((_, idx) => idx !== i));
                  setCurrentMeasureIndex(-1);
                  setIsEdit(false);
                }}
                setCurrentMeasureIndex={(i) => {
                  setCurrentMeasureIndex(i);
                  setIsEdit(true);
                }}
                valueTypeOptions={valueTypeOptions}
                datasourceOptions={datasourceOptions}
                locationsListOption={locationsListOption}
                dataTypesListOptions={dataTypesListOptions}
                measurementTypeListOptions={measurementTypeListOptions}
                assetTypeMetricsListOptions={assetTypeMetricsListOptions}
                setMeasureValue={setMeasureValue}
              />
            ) : null}
          </>
        ) : null}
        {activeStep === 2 && calculatedMeasures.length > 0 ? (
          <>
            <Typography variant="h6" my={2}>
              Calculated Measurements Mapping
            </Typography>
            <TableContainer component={Paper}>
              <Table sx={{ minWidth: 650 }} aria-label="simple table">
                <TableHead>
                  <TableRow>
                    <TableCell align="center">Type </TableCell>
                    <TableCell align="center">Data Type</TableCell>
                    <TableCell align="center">Value Type</TableCell>
                    <TableCell align="center">Metric</TableCell>
                    <TableCell align="center">Location</TableCell>
                    <TableCell align="center">Data Source</TableCell>
                    <TableCell align="center">Description</TableCell>
                    <TableCell align="center">Meter Factor</TableCell>
                    <TableCell align="center">Actions</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {measurements.map((row, i) => {
                    if (
                      calculationSource === undefined ||
                      row.datasource_id !== calculationSource?.id
                    )
                      return null;
                    return (
                      <TableRow
                        key={i} // Use unique `id` if available from useFieldArray
                        sx={{ '&:last-child td, &:last-child th': { border: 0 } }}
                      >
                        <TableCell component="th" scope="row" align="center">
                          {measurementTypeListOptions.find(
                            (measure) => measure.id === row.type_id?.toString(),
                          )?.label ?? 'N/A'}
                        </TableCell>
                        <TableCell component="th" scope="row" align="center">
                          {dataTypesListOptions.find(
                            (dataType) => dataType.id === row.data_type_id?.toString(),
                          )?.label ?? 'N/A'}
                        </TableCell>
                        <TableCell component="th" scope="row" align="center">
                          {valueTypeOptions.find(
                            (valueType) => valueType.id === row.value_type_id?.toString(),
                          )?.label ?? 'N/A'}
                        </TableCell>
                        <TableCell component="th" scope="row" align="center">
                          {(() => {
                            const metric = row.metric_id;
                            if (!metric) return null;
                            const metricString = metric.toString();
                            const foundMetric = assetTypeMetricsListOptions.find(
                              (asset) => asset.id === metricString,
                            );
                            return foundMetric ? foundMetric.label : metricString;
                          })()}
                        </TableCell>
                        <TableCell component="th" scope="row" align="center">
                          {locationsListOption.find(
                            (location) => location.id === row.location_id?.toString(),
                          )?.label ?? 'N/A'}
                        </TableCell>
                        <TableCell component="th" scope="row" align="center">
                          {datasourceOptions.find(
                            (source) => source.id === row.datasource_id?.toString(),
                          )?.label ?? 'N/A'}
                        </TableCell>
                        <TableCell component="th" scope="row" align="center">
                          {row.description ?? 'N/A'}
                        </TableCell>
                        <TableCell component="th" scope="row" align="center">
                          {row.meter_factor ?? 'N/A'}
                        </TableCell>
                        <TableCell component="th" scope="row" align="center">
                          <Box
                            display={'flex'}
                            justifyContent={'space-around'}
                            sx={{ cursor: 'pointer' }}
                          >
                            <EditIcon
                              onClick={() => {
                                if (currentCalcMeasureIndex !== i) {
                                  setCurrentCalcMeasureIndex(i);
                                  const findCalcMeasure = calcMeasurements.find(
                                    (calc) => calc.metric_id === row.metric_id?.toString(),
                                  );
                                  if (findCalcMeasure) {
                                    calcMeasureSetValue(
                                      'expression_template_id',
                                      findCalcMeasure.calcMeasurementData.expression_template_id,
                                    );
                                    calcMeasureSetValue(
                                      'variable_inputs',
                                      findCalcMeasure.calcMeasurementData.variable_inputs,
                                    );
                                    calcMeasureSetValue(
                                      'poll_period',
                                      findCalcMeasure.calcMeasurementData.poll_period,
                                    );
                                    calcMeasureSetValue(
                                      'writeback',
                                      findCalcMeasure.calcMeasurementData.writeback,
                                    );
                                    calcMeasureSetValue(
                                      'is_persisted',
                                      findCalcMeasure.calcMeasurementData.is_persisted,
                                    );
                                  } else {
                                    calcMeasureSetValue('expression_template_id', null);
                                    calcMeasureSetValue('variable_inputs', []);
                                    calcMeasureSetValue('poll_period', null);
                                    calcMeasureSetValue('writeback', false);
                                    calcMeasureSetValue('is_persisted', false);
                                  }
                                } else {
                                  setCurrentCalcMeasureIndex(-1);
                                }
                              }}
                            />
                          </Box>
                        </TableCell>
                      </TableRow>
                    );
                  })}
                </TableBody>
              </Table>
            </TableContainer>
            {hasInvalidVariable && (
              <Alert color="error">Each variable must have a valid metric or constant value.</Alert>
            )}
            {currentCalcMeasureIndex !== -1 &&
              measurements[currentCalcMeasureIndex] &&
              (() => {
                return (
                  <form
                    onSubmit={calcMeasureHandleSubmit((data) => {
                      const measuremetToCalcMetric =
                        measurements[currentCalcMeasureIndex]?.metric_id;
                      if (measuremetToCalcMetric) {
                        setCalcMeasurements(
                          calcMeasurements.map((calc) => {
                            if (calc.metric_id === measuremetToCalcMetric?.toString()) {
                              return {
                                ...calc,
                                calcMeasurementData: {
                                  expression_template_id: data.expression_template_id,
                                  is_persisted: data.is_persisted,
                                  poll_period: data.poll_period,
                                  writeback: data.writeback,
                                  variable_inputs: data.variable_inputs.map((vars) => ({
                                    ...vars,
                                    metric_id: vars?.metric_id,
                                    constant_value: vars?.constant_value,
                                    comment: vars?.comment,
                                  })),
                                },
                              };
                            }
                            return calc;
                          }),
                        );
                        resetCalcMeasureValues();
                        setCurrentCalcMeasureIndex(-1);
                      }
                    })}
                  >
                    <Box component={Card} sx={{ mt: 2, p: 3 }}>
                      <Typography variant="subtitle1" gutterBottom>
                        Editing Calculation Measurement: {currentCalcMeasureIndex + 1}
                      </Typography>

                      <Typography variant="body2" sx={{ mb: 2 }}>
                        Metric:{' '}
                        {(() => {
                          const metric = measurements[currentCalcMeasureIndex]?.metric_id;
                          if (!metric) return null;
                          const metricString = metric.toString();
                          const foundMetric = assetTypeMetricsListOptions.find(
                            (asset) => asset.id === metricString,
                          );
                          return foundMetric ? foundMetric.label : metricString;
                        })()}
                      </Typography>

                      <Stack spacing={2}>
                        <Controller
                          name="expression_template_id"
                          control={calcMeasurementController}
                          render={({ field }) => {
                            const selectedTemplate =
                              expressionTemplates?.items?.find((item) => item.id === field.value) ??
                              null;
                            return (
                              <Autocomplete
                                fullWidth
                                loading={fetchingExpressionTemplates}
                                options={expressionTemplates?.items || []}
                                getOptionLabel={(option) => option.name || ''}
                                value={selectedTemplate}
                                onChange={(_, value) => field.onChange(value?.id ?? null)}
                                renderInput={(params) => (
                                  <TextField
                                    {...params}
                                    label="Expression Template"
                                    error={!!calcMeasureError.expression_template_id}
                                    helperText={calcMeasureError.expression_template_id?.message}
                                    variant="outlined"
                                  />
                                )}
                              />
                            );
                          }}
                        />

                        <ExpressionTemplateDetails selectedTemplate={expression} />
                        <Box sx={{ my: 2 }}>
                          {variableInputs && variableInputs.length > 0 && (
                            <Box>
                              <Typography variant="subtitle1" sx={{ mb: 2 }}>
                                Variable Inputs
                              </Typography>

                              {fields.map((item, index) => (
                                <Box
                                  key={item.id}
                                  sx={{
                                    mb: 3,
                                    p: 2,
                                    border: '1px solid #ccc',
                                    borderRadius: 1,
                                  }}
                                >
                                  <Typography variant="body2" sx={{ mb: 2 }}>
                                    Variable: <strong>{item.variable}</strong>
                                  </Typography>

                                  <Grid container spacing={2}>
                                    <Grid item xs={12} md={4}>
                                      <Controller
                                        name={`variable_inputs.${index}.type`}
                                        control={calcMeasurementController}
                                        render={({ field }) => (
                                          <FormControl
                                            component="fieldset"
                                            fullWidth
                                            error={
                                              !!calcMeasureError?.variable_inputs?.[index]?.type
                                            }
                                          >
                                            <FormLabel component="legend">Type</FormLabel>
                                            <RadioGroup row {...field}>
                                              <FormControlLabel
                                                value="measurement"
                                                control={<Radio />}
                                                label="Measurement"
                                              />
                                              <FormControlLabel
                                                value="constant"
                                                control={<Radio />}
                                                label="Constant"
                                              />
                                            </RadioGroup>
                                            {calcMeasureError?.variable_inputs?.[index]?.type && (
                                              <FormHelperText>
                                                {calcMeasureError.variable_inputs[
                                                  index
                                                ]?.type?.toString()}
                                              </FormHelperText>
                                            )}
                                          </FormControl>
                                        )}
                                      />
                                    </Grid>

                                    {/* METRIC ID or CONSTANT VALUE */}
                                    <Grid item xs={12} md={4}>
                                      {variableInputs[index]?.type === 'measurement' ? (
                                        <Controller
                                          name={`variable_inputs.${index}.metric_id`}
                                          control={calcMeasurementController}
                                          render={({ field }) => {
                                            const options = assetTypeMetricsListOptions.filter(
                                              (metric) =>
                                                !calculatedMeasures.some(
                                                  (cm) => cm.metric_id?.toString() === metric.id,
                                                ) &&
                                                measurements.some(
                                                  (cm) => cm.metric_id?.toString() === metric.id,
                                                ),
                                            );
                                            const selectedOption =
                                              options.find(
                                                (opt) =>
                                                  opt.id?.toString() === field.value?.toString(),
                                              ) ?? null;
                                            return (
                                              <Autocomplete
                                                fullWidth
                                                options={options}
                                                getOptionLabel={(option) => option.label}
                                                isOptionEqualToValue={(option, value) =>
                                                  option.id === value?.id
                                                }
                                                value={selectedOption}
                                                onChange={(_, newValue) =>
                                                  field.onChange(newValue?.id ?? null)
                                                }
                                                renderInput={(params) => (
                                                  <TextField
                                                    {...params}
                                                    label="Metric"
                                                    error={
                                                      !!calcMeasureError?.variable_inputs?.[index]
                                                        ?.metric_id
                                                    }
                                                    helperText={
                                                      calcMeasureError?.variable_inputs?.[index]
                                                        ?.metric_id?.message
                                                    }
                                                  />
                                                )}
                                              />
                                            );
                                          }}
                                        />
                                      ) : (
                                        <Controller
                                          name={`variable_inputs.${index}.constant_value`}
                                          control={calcMeasurementController}
                                          render={({ field }) => (
                                            <TextField
                                              {...field}
                                              label="Constant Value"
                                              fullWidth
                                              error={
                                                !!calcMeasureError?.variable_inputs?.[index]
                                                  ?.constant_value
                                              }
                                              helperText={
                                                calcMeasureError?.variable_inputs?.[index]
                                                  ?.constant_value?.message
                                              }
                                            />
                                          )}
                                        />
                                      )}
                                    </Grid>

                                    {/* COMMENT */}
                                    <Grid item xs={12} md={4}>
                                      <Controller
                                        name={`variable_inputs.${index}.comment`}
                                        control={calcMeasurementController}
                                        render={({ field }) => (
                                          <TextField
                                            {...field}
                                            label="Comment"
                                            fullWidth
                                            error={
                                              !!calcMeasureError?.variable_inputs?.[index]?.comment
                                            }
                                            helperText={
                                              calcMeasureError?.variable_inputs?.[index]?.comment
                                                ?.message
                                            }
                                          />
                                        )}
                                      />
                                    </Grid>
                                  </Grid>
                                </Box>
                              ))}
                            </Box>
                          )}
                        </Box>

                        <Controller
                          name="is_persisted"
                          control={calcMeasurementController}
                          render={({ field }) => (
                            <FormControlLabel
                              control={
                                <Checkbox {...field} checked={!!field.value} color="primary" />
                              }
                              label="Is Persisted"
                            />
                          )}
                        />
                        {calcMeasureWatch('is_persisted') ? (
                          <>
                            <Controller
                              name="writeback"
                              control={calcMeasurementController}
                              render={({ field }) => (
                                <FormControlLabel
                                  control={
                                    <Checkbox {...field} checked={!!field.value} color="primary" />
                                  }
                                  label="Writeback"
                                />
                              )}
                            />

                            <Controller
                              name="poll_period"
                              control={calcMeasurementController}
                              render={({ field }) => (
                                <Autocomplete<poll_period>
                                  fullWidth
                                  options={(pollPeriods?.items || []) as poll_period[]}
                                  getOptionLabel={(option) => String(option.value)}
                                  isOptionEqualToValue={(option, value) => option.id === value?.id}
                                  // eslint-disable-next-line @typescript-eslint/ban-ts-comment
                                  //@ts-ignore
                                  value={field.value ?? null}
                                  onChange={(_, value) => field.onChange(value)}
                                  renderInput={(params) => (
                                    <TextField
                                      {...params}
                                      label="Poll Period"
                                      error={!!calcMeasureError.poll_period}
                                      helperText={calcMeasureError.poll_period?.message}
                                      variant="outlined"
                                    />
                                  )}
                                />
                              )}
                            />
                          </>
                        ) : null}
                      </Stack>
                      <Button type="submit" color="primary" variant="contained" sx={{ mt: 2 }}>
                        Save
                      </Button>
                      <Button
                        type="reset"
                        color="primary"
                        variant="outlined"
                        sx={{ mt: 2, ml: 1.5 }}
                        onClick={() => {
                          resetCalcMeasureValues();
                          setCurrentCalcMeasureIndex(-1);
                        }}
                      >
                        cancel
                      </Button>
                    </Box>
                  </form>
                );
              })()}
          </>
        ) : null}
        <Box sx={{ display: 'flex', flexDirection: 'row', pt: 2 }}>
          <Button
            color="inherit"
            disabled={activeStep === 0}
            onClick={() => {
              handleBack();
              setCurrentCalcMeasureIndex(-1);
            }}
            sx={{ mr: 1 }}
          >
            Back
          </Button>
          <Box sx={{ flex: '1 1 auto' }} />
          <Button
            type="submit"
            variant="contained"
            onClick={handleNext}
            disabled={
              isMultiLoading ||
              isMultiSuccess ||
              (activeStep === 1 &&
                (hasDuplicates() ||
                  isEdit ||
                  Object.keys(measureErrors).length > 0 ||
                  measurements.length === 0))
            }
          >
            {activeStep === steps.filter(Boolean).length - 1 ? 'Save & Finish' : 'Next'}
          </Button>
        </Box>
        {alertMessage && (
          <Alert severity={alertMessage.severity} sx={{ mt: 3 }}>
            {alertMessage.message}
          </Alert>
        )}
      </Box>
    </>
  );
}
