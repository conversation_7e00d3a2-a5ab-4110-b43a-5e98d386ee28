import CancelIcon from '@mui/icons-material/Cancel';
import SaveAsIcon from '@mui/icons-material/SaveAs';
import {
  Alert,
  Box,
  Button,
  FormControl,
  InputLabel,
  MenuItem,
  Select,
  TextField,
  Typography,
} from '@mui/material';
import dayjs from 'dayjs';
import { Annotations } from 'plotly.js';
import React, { useEffect } from 'react';
import { useSelector } from 'react-redux';
import CustomDialog from '~/components/common/CustomDialog';
import { CustomError } from '~/errors/CustomerErrorResponse';
import { CustomAnnotation } from '~/measurements/domain/types';
import { useCreateAnnotationMutation } from '~/redux/api/annotation';
import { getCurrentDashboardId } from '~/redux/selectors/dashboardSelectors';
import { getDateTimeFormat } from '~/redux/selectors/userPreferences';
import { formatChartDateToAssetTz, formatDate } from '~/utils/utils';
type ChartAnnotationProps = {
  id: string;
  annotation: {
    showAnnotation: boolean;
    measurementId: string | null;
    time: number | null;
    value: number | null;
    settings: Annotations | null;
  };
  setAnnotation: (
    value: React.SetStateAction<{
      showAnnotation: boolean;
      measurementId: string | null;
      time: number | null;
      value: number | null;
      settings: Annotations | null;
    }>,
  ) => void;
  showSuccessAlert: (message: string) => void;
  showErrorAlert: (message: string) => void;
  setMeasureIdAnnotations: React.Dispatch<
    React.SetStateAction<Partial<Annotations | CustomAnnotation>[]>
  >;
};
const ChartAnnotation = ({
  id,
  annotation,
  setAnnotation,
  showErrorAlert,
  showSuccessAlert,
  setMeasureIdAnnotations,
}: ChartAnnotationProps) => {
  const dateTimeFormat = useSelector(getDateTimeFormat);
  const dashboard = useSelector(getCurrentDashboardId);
  const [createAnnotation, { isError, isSuccess, error, data: annotationData }] =
    useCreateAnnotationMutation();

  useEffect(() => {
    if (isError) {
      const err = error as CustomError;
      showErrorAlert(err?.data?.message ?? 'Error adding annotation');
    }
    if (isSuccess && annotationData) {
      showSuccessAlert('Annotation added successfully');
      if (annotation?.settings && annotation?.time !== null && annotation?.value !== null) {
        setMeasureIdAnnotations((prevState) => [
          ...prevState,
          {
            ...annotation.settings, // Spread existing settings
            // x: formatChartDateToAssetTz(new Date(Number(annotation.time))),
            x: formatDate(new Date(Number(annotation.time)), 'YYYY-MM-DD HH:mm:ss'),
            y: Number(annotation.value),
            captureevents: true,
            arrowsize: 20,
            arrowwidth: 20,
            arrowhead: 4,
            text: '',
            showarrow: false,
            visible: true,
            width: 3, // Set a default value
            height: 3, // Set a default value
            opacity: 1, // Set a default value
            measurement_id: annotation.measurementId,
            id: annotationData.id,
          },
        ]);
        setAnnotation({
          measurementId: null,
          showAnnotation: false,
          time: null,
          value: null,
          settings: null,
        });
      }
    }
  }, [isError, isSuccess, error, annotationData]);
  return (
    <>
      <CustomDialog
        open={annotation.showAnnotation}
        title={<Typography variant="h6">Add Annotation</Typography>}
        maxWidth="md"
        onClose={() => {
          setAnnotation({
            measurementId: null,
            showAnnotation: false,
            time: null,
            value: null,
            settings: null,
          });
        }}
        dialogActions={
          <Box sx={{ width: '100%', display: 'flex', justifyContent: 'space-between' }}>
            <Button
              variant="outlined"
              startIcon={<CancelIcon />}
              onClick={() => {
                setAnnotation({
                  ...annotation,
                  measurementId: null,
                  showAnnotation: false,
                  time: null,
                  value: null,
                });
              }}
              color="primary"
            >
              Cancel
            </Button>
            <Button
              startIcon={<SaveAsIcon />}
              disabled={dashboard === 0 || annotation.settings === null}
              onClick={() => {
                let settings: Annotations | null = null;
                if (annotation.settings !== null) {
                  const defaultSettings = {
                    arrowhead: 7,
                    bgcolor: '#000000',
                    hovertext: '',
                    visible: true,
                    hoverlabel: {
                      bgcolor: '#ffffff',
                      font: {
                        color: '#000000',
                        size: 13,
                      },
                    },
                  };

                  settings = {
                    ...defaultSettings,
                    ...annotation.settings,
                    hoverlabel: {
                      ...defaultSettings.hoverlabel,
                      ...annotation.settings.hoverlabel,
                      font: {
                        ...defaultSettings.hoverlabel.font,
                        ...(annotation.settings.hoverlabel?.font || {}),
                      },
                    },
                  };
                }
                setAnnotation({
                  ...annotation,
                  settings,
                  showAnnotation: true,
                });
                createAnnotation({
                  dashboard: dashboard,
                  widget_id: Number(id),
                  description: annotation.settings?.hovertext ?? '',
                  measurement_id: Number(annotation.measurementId), // Ensure it's a number
                  time_of_annotation: annotation.time ? annotation.time : 0, // Handle undefined time
                  value: annotation.value ? annotation.value : 0, // Handle undefined value
                  settings: settings ? JSON.stringify(settings) : '{}', // Ensure settings is a valid JSON string
                });
              }}
              color="primary"
              variant="contained"
            >
              Save
            </Button>
          </Box>
        }
        content={
          <>
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
              <TextField
                sx={{ mt: 2 }}
                label="Value"
                disabled
                variant="outlined"
                value={annotation.value || ''}
                fullWidth
              />
              <TextField
                label={'Timestamp'}
                disabled
                type={'datetime-local ' + annotation.time}
                variant="outlined"
                value={
                  annotation.time ? dayjs(new Date(annotation.time)).format(dateTimeFormat) : ''
                }
                fullWidth
                InputLabelProps={{
                  shrink: true,
                }}
              />
              <TextField
                label={'Label'}
                variant="outlined"
                value={annotation.settings?.hovertext}
                fullWidth
                onChange={(event) => {
                  setAnnotation({
                    ...annotation,
                    settings: {
                      ...(annotation.settings || {}),
                      hovertext: event.target.value,
                      visible: annotation.settings?.visible ?? true, // Set default visible if null
                    } as Annotations,
                  });
                }}
                InputLabelProps={{
                  shrink: true,
                }}
              />
              <TextField
                label={'Background'}
                type="color"
                variant="outlined"
                value={annotation.settings?.bgcolor ?? '#000000'}
                fullWidth
                onChange={(event) => {
                  setAnnotation({
                    ...annotation,
                    settings: {
                      ...(annotation.settings || {}),
                      bgcolor: event.target.value,
                      visible: annotation.settings?.visible ?? true, // Set default visible if null
                    } as Annotations,
                  });
                }}
                InputLabelProps={{
                  shrink: true,
                }}
              />
              <Box sx={{ display: 'flex', flexDirection: 'column' }}>
                <FormControl fullWidth>
                  <InputLabel id="shape-select-label">Shape</InputLabel>
                  <Select
                    labelId="shape-select-label"
                    value={annotation.settings?.arrowhead ?? 7}
                    onChange={(event) => {
                      setAnnotation({
                        ...annotation,
                        settings: {
                          ...(annotation.settings || {}),
                          arrowhead: Number(event.target.value), // Set the shape in settings
                        } as Annotations,
                      });
                    }}
                    label="Shape"
                  >
                    <MenuItem value={7}>Rectangle</MenuItem>
                    <MenuItem value={6}>Circle</MenuItem>
                    <MenuItem value={4}>Arrow</MenuItem>
                  </Select>
                </FormControl>
              </Box>

              <TextField
                label={'Font Color'}
                variant="outlined"
                type="color" // Add this to allow color selection
                value={annotation.settings?.hoverlabel?.font?.color ?? '#000000'} // Default to black if not set
                fullWidth
                onChange={(event) => {
                  setAnnotation({
                    ...annotation,
                    settings: {
                      ...(annotation.settings || {}),
                      hoverlabel: {
                        ...(annotation.settings?.hoverlabel || {}),
                        font: {
                          ...(annotation.settings?.hoverlabel?.font || {}),
                          color: event.target.value, // Update font color here
                        },
                        visible: annotation.settings?.visible ?? true, // Set default visible if null
                      },
                    } as Annotations,
                  });
                }}
                InputLabelProps={{
                  shrink: true,
                }}
              />
              <TextField
                label={'Font Size'}
                variant="outlined"
                type="number" // Add this to allow color selection
                value={annotation.settings?.hoverlabel?.font?.size ?? 13} // Default to black if not set
                fullWidth
                onChange={(event) => {
                  setAnnotation({
                    ...annotation,
                    settings: {
                      ...(annotation.settings || {}),
                      hoverlabel: {
                        ...(annotation.settings?.hoverlabel || {}),
                        font: {
                          ...(annotation.settings?.hoverlabel?.font || {}),
                          size: Number(event.target.value), // Update font color here
                        },
                      },
                      visible: annotation.settings?.visible ?? true, // Set default visible if null
                    } as Annotations,
                  });
                }}
                InputLabelProps={{
                  shrink: true,
                }}
              />
              <TextField
                label={'Description Background'}
                variant="outlined"
                type="color"
                value={annotation.settings?.hoverlabel?.bgcolor ?? '#ffffff'} // Default to white if not set
                fullWidth
                onChange={(event) => {
                  const backgroundColor = event.target.value; // Get the selected color

                  setAnnotation({
                    ...annotation,
                    settings: {
                      ...(annotation.settings || {}),
                      hoverlabel: {
                        ...(annotation.settings?.hoverlabel || {}),
                        bgcolor: backgroundColor, // Set the new background color
                      },
                      visible: annotation.settings?.visible ?? true, // Set default visible if null
                    } as Annotations, // Ensure the type is correct here
                  });
                }}
                InputLabelProps={{
                  shrink: true,
                }}
              />
            </Box>

            {dashboard === 0 && (
              <Alert severity="error" sx={{ mt: 2, mb: 2 }}>
                To create annotations, please save/create the dashboard first.
              </Alert>
            )}
          </>
        }
      />
    </>
  );
};

export default ChartAnnotation;
