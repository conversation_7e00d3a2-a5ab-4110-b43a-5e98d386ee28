import CloseIcon from '@mui/icons-material/Close';
import {
  Avatar,
  Box,
  Button,
  Card,
  CardContent,
  Container,
  Dialog,
  DialogContent,
  DialogTitle,
  Divider,
  Grid,
  IconButton,
  Tab,
  Tabs,
  Typography,
} from '@mui/material';
import { DataGrid, GridColDef } from '@mui/x-data-grid';
import { ThunkDispatch } from '@reduxjs/toolkit';
import { useRouter } from 'next/router';
import { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import AlertStatsData from '~/components/AlertStats/AlertStatsData';
import Loader from '~/components/common/Loader';
import PageName from '~/components/common/PageName/PageName';
import TestDashboards from '~/components/TestDahboard/TestDashboards';
import { useHasAdminAccess } from '~/hooks/useHasAdminAccess';
import { useHasPowerUserAccess } from '~/hooks/useHasPowerUserAccess';
import { useRolePermission } from '~/hooks/useRolePermission';
import { Alerts } from '~/measurements/domain/types';
import { useGetAllAlertsQuery, useGetEventsByAlertQuery } from '~/redux/api/alertApi';
import { useGetCustomerUsersByIdMutation } from '~/redux/api/customersApi';
import { useGetAllMeasurementsQuery } from '~/redux/api/measuresApi';
import { getDateTimeFormat, getThousandSeparator } from '~/redux/selectors/userPreferences';
import { RootState } from '~/redux/store';
import { AlertSnackbar } from '~/shared/snackbars/components/AlertSnackbar';
import { useSnackbar } from '~/shared/snackbars/snackbar-hooks';
import { formatNumber, fortmatUTCDate, roundNumber } from '~/utils/utils';
interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

const AlertDetail = () => {
  const router = useRouter();
  const thousandSeparator = useSelector(getThousandSeparator);
  const dispatch = useDispatch<ThunkDispatch<RootState, any, any>>();
  const [snackbarState, showSuccessAlert, showErrorAlert] = useSnackbar();

  const { alertId } = router.query;
  const { hasPermission } = useRolePermission();
  const hasPowerUserAccess = useHasPowerUserAccess();
  const { globalAdmin, admin } = useHasAdminAccess();
  const dateTimeFormat = useSelector(getDateTimeFormat);
  const [alertDetails, setAlertDetails] = useState<Alerts | undefined>(undefined);
  const [tabValue, setTabValue] = useState<number>(0);
  const url = new URL(window.location.href);
  const [openTrendModal, setOpenTrendModal] = useState<boolean>(false);

  const {
    isFetching,
    data: alerts,
    isError,
  } = useGetAllAlertsQuery(undefined, {
    refetchOnMountOrArgChange: true,
  });
  const [getCustomerUser, { data }] = useGetCustomerUsersByIdMutation();

  useEffect(() => {
    getCustomerUser({});
  }, []);
  useEffect(() => {
    if (alerts) {
      const alert = alerts?.items.find((alert) => alert.id.toString() === (alertId as string));
      setAlertDetails(alert);
    }
  }, [alerts]);

  const { data: measurements, isSuccess } = useGetAllMeasurementsQuery(
    {
      customerId: alertDetails?.customerId ?? 0,
      assetId: alertDetails?.asset?.id ?? 0,
    },
    {
      skip: !alertDetails?.customerId || !alertDetails?.asset?.id,
      refetchOnMountOrArgChange: true,
    },
  );
  const updateUrlParams = async (
    assetId: string,
    measurementId: string,
    startDate: number,
    endDate: number,
    customerId: number,
    limit: number,
    condition: string,
  ) => {
    const measureID = measurements?.find(
      (measure) => measure.measurementId?.toString() === measurementId,
    );
    if (!measureID) {
      showErrorAlert('Measurement not found');
      return;
    }
    setOpenTrendModal(true);
    url.searchParams.set('asset_id', assetId);
    url.searchParams.set('measurement_id', measureID?.id.toString() ?? '');
    url.searchParams.set('start_date', startDate.toString());
    url.searchParams.set('end_date', endDate.toString());
    url.searchParams.set('measurement_trend', 'true');
    url.searchParams.set('limit', limit.toString());
    url.searchParams.set('condition', condition);
    router.push(url, undefined, { shallow: true });
  };

  const deleteUrlParams = () => {
    const { pathname, searchParams } = url;

    const paramsToRemove = [
      'asset_id',
      'measurement_id',
      'start_date',
      'end_date',
      'measurement_trend',
      'limit',
      'condition',
    ];

    // Remove only the specified parameters
    paramsToRemove.forEach((param) => searchParams.delete(param));

    // Construct updated query parameters
    const updatedQuery = Object.fromEntries(searchParams.entries());

    // Update URL with the remaining query parameters
    router.push({ pathname, query: updatedQuery }, undefined, { shallow: true });
  };
  const {
    data: eventsData,
    isError: eventsError,
    isFetching: eventsFetching,
  } = useGetEventsByAlertQuery(
    { alertId: alertId as string },
    { refetchOnMountOrArgChange: true, skip: !alertId },
  );
  const columns: GridColDef[] = [
    { field: 'id', headerName: 'ID', flex: 1 },
    {
      field: 'timestamp',
      headerName: 'Timestamp (UTC)',
      flex: 2,
      renderCell: (params) => {
        return <div>{fortmatUTCDate(params.row.timestamp, dateTimeFormat)} </div>;
      },
    },
    { field: 'deadband', headerName: 'Deadband', flex: 1 },
    {
      field: 'aggregate',
      headerName: 'Aggregate',
      flex: 1,
      renderCell: (params) => {
        return <div>{params.row.aggregate?.label ?? 'N/A'}</div>;
      },
    },
    {
      field: 'comparator',
      headerName: 'Comparator',
      flex: 1,
      renderCell: (params) => {
        return <div>{params.row.comparator?.condition ?? 'N/A'}</div>;
      },
    },
    {
      field: 'period',
      headerName: 'Period',
      flex: 1,
      renderCell: (params) => {
        return <div>{params.row.period?.label ?? 'N/A'}</div>;
      },
    },
    { field: 'limit', headerName: 'Limit', flex: 1 },
    {
      field: 'state',
      headerName: 'State',
      flex: 1,
      renderCell(params) {
        if (params.row.state === 'EXCEEDED' || params.row.state === '1') {
          return <>EXCEEDED</>;
        }
        if (params.row.state === 'NORMAL' || params.row.state === '0') {
          return <>NORMAL</>;
        }
        return <>{params.row.state}</>;
      },
    },
    { field: 'input_value', headerName: 'Input value', flex: 1 },
    {
      field: 'output_value',
      headerName: 'Reference',
      flex: 1,
      renderCell: (params) => {
        return (
          <Box
            onClick={() => {
              if (alertDetails) {
                const timeData = new Date(params.row.timestamp).getTime();
                const endDate = timeData + 1000 * 60 * 60 * 1;
                const startDate = timeData - 1000 * 60 * 60 * 4;

                updateUrlParams(
                  alertDetails?.asset!.id?.toString(),
                  alertDetails?.measurement.id?.toString(),
                  new Date(startDate).getTime(),
                  new Date(endDate).getTime(),
                  alertDetails?.customerId,
                  params.row.limit,
                  params.row.comparator?.condition ?? 'N/A',
                );
              }
            }}
            sx={{
              cursor: 'pointer',
              color: 'blue',
              textDecoration: 'underline',
            }}
          >
            Trend
          </Box>
        );
      },
    },
  ];

  function CustomTabPanel(props: TabPanelProps) {
    const { children, value, index, ...other } = props;

    return (
      <div
        role="tabpanel"
        hidden={value !== index}
        id={`simple-tabpanel-${index}`}
        aria-labelledby={`simple-tab-${index}`}
        {...other}
      >
        {value === index && <Box sx={{ p: 3, pl: 0.5, pr: 0.5 }}>{children}</Box>}
      </div>
    );
  }

  function a11yProps(index: number) {
    return {
      id: `simple-tab-${index}`,
      'aria-controls': `simple-tabpanel-${index}`,
    };
  }

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  return (
    <>
      <AlertSnackbar {...snackbarState} />

      <Container sx={{ mt: 2 }} maxWidth="xl">
        <Box p={2} pb={0} pt={0} sx={{ display: 'flex', justifyContent: 'space-between' }}>
          <PageName name="Alert Details" />
        </Box>
        {isFetching ? (
          <Loader />
        ) : (
          <>
            {alertDetails && (
              <Box p={2}>
                <Card sx={{ mb: 4, p: 2 }}>
                  <CardContent>
                    <Box
                      sx={{
                        display: 'flex',
                        justifyContent: 'space-between',
                        alignItems: 'center',
                      }}
                    >
                      {/* Alert Details */}
                      <Typography variant="h5" fontWeight="bold" gutterBottom>
                        <strong>Alert ID:</strong> {alertDetails.id}
                      </Typography>

                      {/* Edit Button */}
                      {hasPermission('alert.update') &&
                        (globalAdmin || admin || hasPowerUserAccess) && (
                          <Button
                            variant="contained"
                            color="primary"
                            onClick={() => router.push(`/alerts/edit/${alertDetails.id}`)}
                            sx={{ px: 3, py: 0.5, fontSize: '14px', textTransform: 'none' }}
                          >
                            Edit
                          </Button>
                        )}
                    </Box>

                    <Divider sx={{ my: 2 }} />

                    <Grid container spacing={2} wrap="wrap">
                      <Grid item xs={12}>
                        <Typography variant="body1">
                          <strong>Description:</strong> {alertDetails.description}
                        </Typography>
                      </Grid>
                      <Grid item xs={6}>
                        <Typography variant="body1">
                          <strong>Status:</strong> {alertDetails.enabled ? 'Enabled' : 'Disabled'}
                        </Typography>
                      </Grid>
                      <Grid item xs={6}>
                        {alertDetails.measurement && (
                          <Typography variant="body1">
                            <strong>Measurement:</strong> {alertDetails.measurement.tag}
                          </Typography>
                        )}
                      </Grid>
                      <Grid item xs={6}>
                        {alertDetails.agg && (
                          <Typography variant="body1">
                            <strong>Aggregate:</strong> {alertDetails.agg.label}
                          </Typography>
                        )}
                      </Grid>
                      <Grid item xs={6}>
                        {alertDetails.period && (
                          <Typography variant="body1">
                            <strong>Period:</strong> {alertDetails.period.label}
                          </Typography>
                        )}
                      </Grid>
                      <Grid item xs={6}>
                        <Typography variant="body1">
                          <strong>Threshold Type:</strong>{' '}
                          {alertDetails.thresholdType.threshold === 'NOMINAL'
                            ? alertDetails.thresholdType.threshold
                            : 'N/A'}
                        </Typography>
                      </Grid>
                      {alertDetails.thresholdType.threshold === 'NOMINAL' && (
                        <>
                          <Grid item xs={6}>
                            <Typography variant="body1">
                              <strong>Condition:</strong>{' '}
                              {alertDetails.condition ? alertDetails.condition.condition : 'N/A'}
                            </Typography>
                          </Grid>
                          <Grid item xs={6}>
                            <Typography variant="body1">
                              <strong>Threshold Value:</strong> {alertDetails.thresholdValue}
                            </Typography>
                          </Grid>
                          <Grid item xs={6}>
                            <Typography variant="body1">
                              <strong>Reset Deadband:</strong> {alertDetails.resetDeadband}
                            </Typography>
                          </Grid>
                        </>
                      )}

                      {alertDetails.createdAt && (
                        <Grid item xs={6}>
                          <Typography variant="body1">
                            <strong>Created at (UTC): </strong>
                            {fortmatUTCDate(new Date(alertDetails.createdAt!), dateTimeFormat)}
                          </Typography>
                        </Grid>
                      )}
                      {alertDetails.updatedAt && (
                        <Grid item xs={6}>
                          <Typography variant="body1">
                            <strong>Updated at (UTC): </strong>
                            {fortmatUTCDate(new Date(alertDetails.updatedAt!), dateTimeFormat)}
                          </Typography>
                        </Grid>
                      )}
                    </Grid>

                    {/* Contact List */}
                    <Typography variant="h6" mt={3} fontWeight="bold">
                      Contact(s)
                    </Typography>
                    <Divider sx={{ my: 2 }} />

                    {alertDetails.alertUsers && alertDetails.alertUsers.length > 0 ? (
                      <Grid container spacing={2} wrap="wrap">
                        {alertDetails.alertUsers.map((user) => {
                          const username =
                            data?.items.find((item) => item.id === user.user)?.username ||
                            'Unknown';
                          let notificationType = 'N/A';
                          if (user.notificationtype === 1) notificationType = 'Email';
                          if (user.notificationtype === 2) notificationType = 'SMS';
                          if (user.notificationtype === 3) notificationType = 'Both';

                          return (
                            <Grid item xs={12} sm={4} key={user.id}>
                              <Box
                                sx={{
                                  display: 'flex',
                                  alignItems: 'center',
                                  p: 2,
                                  borderColor: 'grey.300',
                                  borderRadius: 1,
                                }}
                                border={1}
                              >
                                <Avatar>{username.charAt(0).toUpperCase()}</Avatar>
                                <Box sx={{ ml: 2 }}>
                                  <Typography variant="body1" fontWeight="bold">
                                    {username}
                                  </Typography>
                                  <Typography variant="body2">
                                    Notification Type: {notificationType}
                                  </Typography>
                                </Box>
                              </Box>
                            </Grid>
                          );
                        })}
                      </Grid>
                    ) : (
                      <Typography variant="body2" color="text.secondary" mt={2}>
                        No contacts available
                      </Typography>
                    )}
                  </CardContent>
                </Card>

                <Card sx={{ p: 1, pl: 0, pr: 0 }}>
                  <CardContent>
                    <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
                      <Tabs
                        value={tabValue}
                        onChange={handleTabChange}
                        aria-label="basic tabs example"
                      >
                        <Tab sx={{ textTransform: 'none' }} label="Events Data" {...a11yProps(0)} />
                        <Tab
                          sx={{ textTransform: 'none' }}
                          label="Alert Stats Data"
                          {...a11yProps(1)}
                        />
                      </Tabs>
                    </Box>

                    <CustomTabPanel value={tabValue} index={0}>
                      {eventsError ? (
                        <Typography variant="h6">Error fetching events</Typography>
                      ) : null}
                      {eventsFetching ? <Loader /> : null}
                      {eventsData && eventsData.items.length > 0 ? (
                        <Box sx={{ height: 680, width: '100%' }}>
                          <DataGrid
                            sx={{ p: 1 }}
                            rows={eventsData.items
                              .slice() // prevent mutating original data
                              .sort(
                                (a, b) =>
                                  new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime(),
                              )
                              .map((item) => ({
                                ...item,
                                input_value: thousandSeparator
                                  ? formatNumber(Number(item.input_value))
                                  : roundNumber(Number(item.input_value)),
                              }))}
                            columns={columns}
                            autoPageSize
                          />
                        </Box>
                      ) : (
                        <Typography variant="body2" color="text.secondary">
                          No data available
                        </Typography>
                      )}
                    </CustomTabPanel>
                    <CustomTabPanel value={tabValue} index={1}>
                      <AlertStatsData />
                    </CustomTabPanel>
                  </CardContent>
                </Card>
              </Box>
            )}
            {alertDetails === undefined && !isError && (
              <Typography variant="body2" color="text.secondary">
                No data available
              </Typography>
            )}
          </>
        )}
        {openTrendModal && (
          <Dialog
            fullWidth
            sx={{
              width: '100%',
              maxWidth: '100%',
              minWidth: '600px', // Set a minimum width
              minHeight: '400px', // Set a minimum height
              '& .MuiDialog-paper': {
                // Target the inner Dialog content
                minWidth: '90%',
                minHeight: '85%',
              },
            }}
            open={openTrendModal}
            onClose={() => {
              deleteUrlParams();
              setOpenTrendModal(false);
            }}
          >
            <DialogTitle id="customized-dialog-title">Measurement Trend</DialogTitle>
            <IconButton
              aria-label="close"
              onClick={() => {
                deleteUrlParams();
                setOpenTrendModal(false);
              }}
              sx={(theme) => ({
                position: 'absolute',
                right: 8,
                top: 8,
                color: theme.palette.grey[500],
              })}
            >
              <CloseIcon />
            </IconButton>
            <DialogContent dividers>
              <TestDashboards />
            </DialogContent>
          </Dialog>
        )}
      </Container>
    </>
  );
};

export default AlertDetail;
