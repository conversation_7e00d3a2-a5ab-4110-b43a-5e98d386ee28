import { test, expect } from '@playwright/test';

test('test', async ({ page }) => {
  await page.goto('https://test.pivotol.ai/login');
  await page.getByLabel('Username *').click();
  await page.getByLabel('Username *').fill('test');
  await page.getByLabel('Password *').click();
  await page.getByLabel('Password *').fill('Br0mpt0n!0T');
  await page.getByRole('button', { name: 'Log in' }).click();
  await page.getByRole('button', { name: 'Alerts' }).click();
  await page.getByRole('menuitem', { name: 'Analytics' }).click();
  await page.getByLabel('Interval').click();
  await page.getByRole('option', { name: 'Daily' }).click();
  await page.getByLabel('Select Asset').click();
  await page.getByRole('combobox', { name: 'Select Asset' }).fill('mqtt');
  await page.getByRole('option', { name: 'Brenes > MAINPANEL_MQTT', exact: true }).click();
  await page.getByLabel('Select Measurement').click();
  await page.getByRole('option', { name: 'Averagecurrent', exact: true }).click();
  await page.locator('.nsewdrag').first().click();
  await page.close();
});
