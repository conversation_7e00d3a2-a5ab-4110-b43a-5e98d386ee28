import { TreeView } from '@mui/lab';
import React from 'react';
import { NodeType, Tree, TreeNode } from './tree';
import { NodeConfig } from './AssetNode';

import { AssetTreeItem } from './AssetTreeItem';

type NodeTypeConfigs = {
  customer: NodeConfig;
  asset: NodeConfig;
  measurement: NodeConfig;
  metric: NodeConfig;
};

export type AssetsTreeProps = {
  tree: Tree;
  expandedNodeIds?: string[];
  selectedNodeIds?: string[];
  onNodeToggle: (nodes: string[]) => void;
  onSelect: (selectedNodes: TreeNode[]) => void;
  nodeTypeConfigs: NodeTypeConfigs;
  checkboxSelection: boolean;
};

const mapType = (nodeType: NodeType): keyof NodeTypeConfigs => {
  if (nodeType === 'company') {
    return 'customer';
  } else if (nodeType === 'activo') {
    return 'asset';
  } else if (nodeType === 'medicion') {
    return 'measurement';
  } else {
    return 'metric';
  }
};

const renderTree = (
  node: Tree,
  nodeExpandToggleHandler: (nodeId: string) => void,
  nodeTypeConfigs: NodeTypeConfigs,
  checkboxSelection: boolean
) => (
  <AssetTreeItem
    key={node.id}
    nodeId={node.id}
    label={node.tag}
    onExpandToggle={nodeExpandToggleHandler}
    nodeConfig={nodeTypeConfigs[mapType(node.type)]}
    checkboxSelection={checkboxSelection}
  >
    {Array.isArray(node.children)
      ? node.children.map((node) =>
          renderTree(
            node,
            nodeExpandToggleHandler,
            nodeTypeConfigs,
            checkboxSelection
          )
        )
      : null}
  </AssetTreeItem>
);

const AssetsTree: React.FC<AssetsTreeProps> = (props) => {
  const {
    tree,
    expandedNodeIds = [],
    selectedNodeIds = [],
    onNodeToggle,
    onSelect,
    nodeTypeConfigs,
    checkboxSelection,
  } = props;

  const nodeExpandToggleHandler = (nodeId: string) => {
    let newExpandedNodeIds = [];
    if (expandedNodeIds.includes(nodeId)) {
      newExpandedNodeIds = expandedNodeIds.filter((id) => id !== nodeId);
    } else {
      newExpandedNodeIds = [...expandedNodeIds, nodeId];
    }
    onNodeToggle(newExpandedNodeIds);
  };

  const nodeSelectionHanlder = (_: unknown, nodeIds: string[]) => {
    let newSelectedNodeIds = [];
    const nodeId = nodeIds[0];
    if (selectedNodeIds.includes(nodeId)) {
      newSelectedNodeIds = selectedNodeIds.filter((id) => id !== nodeId);
    } else {
      newSelectedNodeIds = [...selectedNodeIds, nodeId];
    }
    onSelect(
      newSelectedNodeIds
        .map((id) => tree.find(id))
        .filter((treeNode) => treeNode !== null) as TreeNode[] // We're filtering null values
    );
  };

  return (
    <>
      <TreeView
        multiSelect={true}
        expanded={expandedNodeIds}
        selected={selectedNodeIds}
        onNodeSelect={nodeSelectionHanlder}
      >
        {renderTree(
          tree,
          nodeExpandToggleHandler,
          nodeTypeConfigs,
          checkboxSelection
        )}
      </TreeView>
    </>
  );
};

export default AssetsTree;
