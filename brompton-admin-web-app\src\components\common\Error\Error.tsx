import { Box, Typography } from '@mui/material';
import { CSSProperties } from 'react';

type ErrorProps = {
  message?: string;
  style?: CSSProperties;
};
const Error = ({ message, style }: ErrorProps) => {
  return (
    <Box
      sx={{
        display: 'flex',
        justifyContent: 'center',
        height: '100%',
        alignItems: 'center',
        ...style,
      }}
    >
      <Typography color={'error'}>{message ? message : <>Error while fetching data</>}</Typography>
    </Box>
  );
};

export default Error;
