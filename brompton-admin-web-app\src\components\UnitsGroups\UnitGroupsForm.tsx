import { yupResolver } from '@hookform/resolvers/yup';
import EditIcon from '@mui/icons-material/Edit';
import {
  Autocomplete,
  Box,
  Button,
  Checkbox,
  FormControlLabel,
  IconButton,
  TextField,
  Tooltip,
  Typography,
} from '@mui/material';
import { DataGrid } from '@mui/x-data-grid';
import { useEffect, useState } from 'react';
import { Controller, SubmitHandler, useForm } from 'react-hook-form';

import {
  useCreateUnitOfMeasureMutation,
  useGetAllMeasureTypesQuery,
  useGetAllUnitOfGroupsQuery,
  useGetAllUnitsOfMeasureQuery,
  useGetUnitGroupsUnitsQuery,
  useSetDefaultUnitOfMeasureMutation,
  useUpdateUnitOfMeasureMutation,
} from '~/redux/api/measuresApi';

import { AlertSnackbar } from '~/shared/snackbars/components/AlertSnackbar';
import { useSnackbar } from '~/shared/snackbars/snackbar-hooks';
import CustomDialog from '../common/CustomDialog';
import Loader from '../common/Loader';

// Your existing Yup schema & form types:
import { unitsofGropsUnitvalidationSchema, UnitsOfGroupUnitFormValues } from './domains/type';
import { CustomError } from '~/errors/CustomerErrorResponse';
type UnitsGroupsFormProps = {
  openForm: boolean;
  setOpenForm: (state: boolean) => void;
  editItem: any;
  setEditItem: (item: any) => void;
};
const UnitsGroupsForm = ({
  editItem,
  openForm,
  setEditItem,
  setOpenForm,
}: UnitsGroupsFormProps) => {
  const [snackbarState, showSuccessAlert, showErrorAlert] = useSnackbar();

  // Fetch existing data for the DataGrid
  const {
    data: unitsGroups,
    isFetching: loadingUnitsGroups,
    refetch,
  } = useGetUnitGroupsUnitsQuery();

  // Queries to populate Autocomplete fields
  const { data: measurementTypes } = useGetAllMeasureTypesQuery();
  const { isFetching: isFetchingUOM, data: uom } = useGetAllUnitOfGroupsQuery();
  const {
    control,
    handleSubmit,
    reset,
    formState: { errors },
    setValue,
    watch,
  } = useForm<UnitsOfGroupUnitFormValues>({
    resolver: yupResolver(unitsofGropsUnitvalidationSchema),
    defaultValues: {
      measurementId: '',
      unitsGroup: '',
      unitOfMeasure: '',
      is_default: false,
    },
  });
  const measurementId = watch('measurementId');

  const { data: allUnitOfMeasures } = useGetAllUnitsOfMeasureQuery(
    { measurementTypeId: Number(measurementId) },
    {
      skip:
        measurementId === undefined ||
        measurementId === null ||
        measurementId === '' ||
        measurementId === '0',
    },
  );

  const [setDefault, setSetDefault] = useState<{
    id: number;
    unitOfMeasureId: number;
    unitsGroupId: number;
    measurementTypeId: number;
  } | null>(null);
  const [setDefaultUnitOfMeasure, { isError, isLoading, isSuccess }] =
    useSetDefaultUnitOfMeasureMutation();

  useEffect(() => {
    if (isSuccess) {
      showSuccessAlert('Unit of measure has been set successfully');
      setSetDefault(null);
      refetch();
    }
    if (isError) {
      showErrorAlert('Failed to set unit of measure');
      setSetDefault(null);
    }
  }, [isError, isLoading, isSuccess]);

  // Mutations
  const [createUnitGroupsUnits] = useCreateUnitOfMeasureMutation();
  const [updateUnitOfMeasure] = useUpdateUnitOfMeasureMutation();

  const onSubmit: SubmitHandler<UnitsOfGroupUnitFormValues> = async (values) => {
    try {
      if (editItem) {
        await updateUnitOfMeasure({
          unitsofUnitId: editItem.id, // or however your update API identifies the record
          measurementTypeId: Number(values.measurementId),
          unitsGroupId: Number(values.unitsGroup),
          unitOfMeasureId: Number(values.unitOfMeasure),
          is_default: values.is_default ?? false,
        }).unwrap();
        showSuccessAlert('Unit has been updated successfully');
      } else {
        await createUnitGroupsUnits({
          measurementTypeId: Number(values.measurementId),
          unitsGroupId: Number(values.unitsGroup),
          unitOfMeasureId: Number(values.unitOfMeasure),
          is_default: values.is_default ?? false,
        }).unwrap();
        showSuccessAlert('Unit has been created successfully');
      }

      reset();
      setOpenForm(false);
      setEditItem(null);
      refetch();
    } catch (error) {
      const err = error as CustomError;
      if (editItem) {
        showErrorAlert(err.data.exception ?? 'Failed to update unit');
      } else {
        showErrorAlert(err.data.exception ?? 'Failed to create unit');
      }
    }
  };

  // Helper to open the dialog in "edit" mode
  const handleEdit = (row: any) => {
    setEditItem(row);
    setOpenForm(true);

    // If your data is shaped differently, adapt accordingly.
    setValue('measurementId', String(row.unitOfMeasure.measurement_type_id.id));
    setValue('unitsGroup', String(row.unitsGroup.id));
    setValue('unitOfMeasure', String(row.unitOfMeasure.id));
    setValue('is_default', row.is_default);
  };

  // Helper to open the dialog in "create" mode
  const handleCreate = () => {
    setEditItem(null);
    reset();
    setOpenForm(true);
  };

  const sortedMeasurementUnits = [...(unitsGroups ?? [])].sort((a, b) => {
    const aType = a.unitOfMeasure?.measurement_type_id?.name?.toLowerCase() ?? '';
    const bType = b.unitOfMeasure?.measurement_type_id?.name?.toLowerCase() ?? '';

    const typeCompare = aType.localeCompare(bType);
    if (typeCompare !== 0) return typeCompare;

    const aGroup = a.unitsGroup?.name?.toLowerCase() ?? '';
    const bGroup = b.unitsGroup?.name?.toLowerCase() ?? '';

    return aGroup.localeCompare(bGroup);
  });

  return (
    <>
      <AlertSnackbar {...snackbarState} />

      {/* The same dialog for both create & edit */}
      <CustomDialog
        dialogActions={null}
        open={openForm}
        onClose={() => {
          setOpenForm(false);
          reset();
          setEditItem(null);
        }}
        title={
          <Typography fontWeight={'bold'} variant="h4">
            {editItem ? 'Edit Unit' : 'Add New Unit'}
          </Typography>
        }
        content={
          <Box component="form" onSubmit={handleSubmit(onSubmit)}>
            {/* measurementId */}
            <Controller
              name="measurementId"
              control={control}
              render={({ field }) => {
                const mappedOptions =
                  measurementTypes?.map((type) => ({
                    value: String(type.id),
                    label: type.name,
                  })) ?? [];

                const selectedOption =
                  mappedOptions.find((option) => option.value === field.value) || null;

                return (
                  <Autocomplete
                    options={mappedOptions}
                    getOptionLabel={(option) => option.label}
                    isOptionEqualToValue={(option, value) => option.value === value.value}
                    value={selectedOption}
                    onChange={(_, newValue) => {
                      field.onChange(newValue?.value ?? '');
                    }}
                    onBlur={field.onBlur}
                    renderInput={(params) => (
                      <TextField
                        {...params}
                        label="Measurement Type"
                        fullWidth
                        margin="normal"
                        error={!!errors.measurementId}
                        helperText={errors.measurementId?.message}
                      />
                    )}
                  />
                );
              }}
            />

            <Controller
              name="unitsGroup"
              control={control}
              render={({ field }) => {
                const mappedOptions =
                  uom?.items?.map((item) => ({
                    value: String(item.id),
                    label: item.name,
                  })) ?? [];

                const selectedOption =
                  mappedOptions.find((option) => option.value === field.value) || null;

                return (
                  <Autocomplete
                    loading={isFetchingUOM}
                    options={mappedOptions}
                    getOptionLabel={(option) => option.label}
                    isOptionEqualToValue={(option, value) => option.value === value.value}
                    value={selectedOption}
                    onChange={(_, newValue) => {
                      field.onChange(newValue?.value ?? '');
                    }}
                    onBlur={field.onBlur}
                    renderInput={(params) => (
                      <TextField
                        {...params}
                        label="Unit of Group"
                        fullWidth
                        margin="normal"
                        error={!!errors.unitsGroup}
                        helperText={errors.unitsGroup?.message}
                      />
                    )}
                  />
                );
              }}
            />

            {/* unitOfMeasure */}
            <Controller
              name="unitOfMeasure"
              control={control}
              render={({ field }) => {
                // If you want to filter by measurementId, you can watch that here
                // Then skip or modify the query for `allUnitOfMeasures`
                const mappedOptions =
                  allUnitOfMeasures?.map((item) => ({
                    value: String(item.id),
                    label: item.name,
                  })) ?? [];

                const selectedOption =
                  mappedOptions.find((option) => option.value === field.value) || null;

                return (
                  <Autocomplete
                    options={mappedOptions}
                    disabled={false /* or isFetching if needed */}
                    getOptionLabel={(option) => option.label}
                    isOptionEqualToValue={(option, value) => option.value === value.value}
                    value={selectedOption}
                    onChange={(_, newValue) => {
                      field.onChange(newValue?.value ?? '');
                    }}
                    onBlur={field.onBlur}
                    renderInput={(params) => (
                      <TextField
                        {...params}
                        label="Unit of Measure"
                        fullWidth
                        margin="normal"
                        error={!!errors.unitOfMeasure}
                        helperText={errors.unitOfMeasure?.message}
                      />
                    )}
                  />
                );
              }}
            />

            <Controller
              name="is_default"
              control={control}
              render={({ field }) => (
                <FormControlLabel
                  control={
                    <Checkbox
                      {...field}
                      checked={!!field.value}
                      onChange={(e) => field.onChange(e.target.checked)}
                    />
                  }
                  label="Is Default"
                />
              )}
            />

            <Box mt={2} sx={{ display: 'flex', justifyContent: 'flex-end', gap: 2 }}>
              <Button
                variant="outlined"
                onClick={() => {
                  setOpenForm(false);
                  reset();
                  setEditItem(null);
                }}
              >
                Cancel
              </Button>
              <Button type="submit" variant="contained" color="primary">
                {editItem ? 'Update' : 'Save'}
              </Button>
            </Box>
          </Box>
        }
      />

      {/* Existing Data Grid */}
      {loadingUnitsGroups ? (
        <Loader />
      ) : (
        <Box sx={{ height: 'calc(100vh - 80px)', width: '100%' }}>
          <DataGrid
            columns={[
              { field: 'id', headerName: 'ID', flex: 1 },
              {
                field: 'unitOfMeasure',
                headerName: 'Unit of Measure',
                flex: 1,
                valueGetter: (params) => params.row.unitOfMeasure.name,
              },
              {
                field: 'measurementType',
                headerName: 'Measurement Type',
                flex: 1,
                valueGetter: (params) => params.row.unitOfMeasure.measurement_type_id.name,
              },
              {
                field: 'unitsGroup',
                headerName: 'Units Group',
                flex: 1,
                valueGetter: (params) => params.row.unitsGroup.name,
              },
              {
                field: 'is_default',
                headerName: 'Is Default',
                flex: 1,
                type: 'boolean',
                renderCell: (params) => (
                  <Checkbox
                    checked={params.value}
                    onChange={() => {
                      setSetDefault({
                        id: params.row.id,
                        unitOfMeasureId: params.row.unitOfMeasure.id,
                        unitsGroupId: params.row.unitsGroup.id,
                        measurementTypeId: params.row.unitOfMeasure.measurement_type_id.id,
                      });
                    }}
                  />
                ),
              },
              {
                field: 'actions',
                headerName: 'Actions',
                flex: 1,
                renderCell: (params) => (
                  <Tooltip title="Edit" arrow>
                    <IconButton onClick={() => handleEdit(params.row)}>
                      <EditIcon />
                    </IconButton>
                  </Tooltip>
                ),
              },
            ]}
            autoPageSize
            rows={sortedMeasurementUnits ?? []}
          />
        </Box>
      )}

      {/* Confirmation dialog for setting default */}
      <CustomDialog
        open={setDefault !== null}
        content={
          <Typography>Are you sure you want to set this unit of measure as default?</Typography>
        }
        dialogActions={
          <>
            <Button autoFocus sx={{ mr: 'auto' }} size="medium" onClick={() => setSetDefault(null)}>
              Cancel
            </Button>
            <Button
              variant="contained"
              color="primary"
              size="medium"
              onClick={async () => {
                if (setDefault !== null) {
                  await setDefaultUnitOfMeasure({
                    unitsofUnitId: setDefault.id,
                    unitOfMeasureId: setDefault.unitOfMeasureId,
                    unitsGroupId: setDefault.unitsGroupId,
                    measurementTypeId: setDefault.measurementTypeId,
                  });
                }
                setSetDefault(null);
              }}
            >
              Set Default
            </Button>
          </>
        }
        title={
          <Typography fontWeight={'bold'} variant="h4">
            Set Default Unit of Measure
          </Typography>
        }
        onClose={() => setSetDefault(null)}
      />
    </>
  );
};

export default UnitsGroupsForm;
