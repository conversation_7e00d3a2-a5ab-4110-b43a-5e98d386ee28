import { ThunkDispatch } from '@reduxjs/toolkit';
import { useRouter } from 'next/router';
import { useCallback, useEffect, useRef, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { measuresApi } from '~/redux/api/measuresApi';
import { getCustomerId } from '~/redux/selectors/customerSelectors';
import { getDbMeasureIdToAssetIdMap, getDbMeasureIdToName } from '~/redux/selectors/treeSelectors';
import { RootState } from '~/redux/store';
import { RealTimeChart, Widget } from '~/types/widgets';
import { formatMetricLabel } from '~/utils/utils';

export const useRealTimeChartHook = (
  measureId: string,
  widgets: Widget[],
  retention: number,
  settings: RealTimeChart,
) => {
  const router = useRouter();
  const dbMeasureIdToName = useSelector(getDbMeasureIdToName);
  const [data, setData] = useState<{ x: number[]; y: number[] }>({ x: [], y: [] });
  const [revision, setRevision] = useState<number>(0);
  const [sseEventSource, setSetsseEventSource] = useState<EventSource | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const previousMeasureIdsRef = useRef<string[]>([]);
  const eventSourceRef = useRef<EventSource | null>(null);
  const realTimeChartName = formatMetricLabel(dbMeasureIdToName[measureId]);
  const dbMeasureIdToAssetIdMap = useSelector(getDbMeasureIdToAssetIdMap);
  const dispatch = useDispatch<ThunkDispatch<RootState, any, any>>();
  const customerId = useSelector(getCustomerId);
  const measureIdMapping = useRef<{ [key: string]: number }>({});

  const fetchMeasureData = useCallback(
    async (measureId: string, assetId: string) => {
      // if (!dbMeasureIdToAssetIdMap[measureId]) {
      //   throw new Error(`No assetId found for ${measureId}`);
      // }
      if (!measureId) {
        throw new Error(`Error invalid measureId: ${measureId}`);
      }
      const { data: measureData, isSuccess: isMeasureDataSuccess } = await dispatch(
        measuresApi.endpoints?.getMeasurementById.initiate({
          customerId,
          assetId: assetId,
          measId: measureId,
        }),
      );
      if (!isMeasureDataSuccess || !measureData) {
        throw new Error(`Error fetching measure data for measureId: ${measureId}`);
      }
      // Store the mapping between measureId and fetched measurementId
      measureIdMapping.current[measureId] = measureData.measurementId;
      return measureData;
    },
    [customerId, dbMeasureIdToAssetIdMap, dispatch],
  );

  useEffect(() => {
    const measureAssetPairs = widgets
      .filter(({ type }) => type === 'real-time')
      .map(({ settings }) => {
        const realTimeChart = settings as RealTimeChart;
        return {
          measureId: realTimeChart.assetMeasure?.measureId?.[0] || '',
          assetId: realTimeChart.assetMeasure?.assetId || '',
        };
      })
      .filter(({ measureId, assetId }) => (measureId && assetId) !== ''); // Filter out entries with empty measureId

    const uniqueMeasureAssetPairs = Array.from(
      new Set(
        measureAssetPairs.map(({ measureId, assetId }) => JSON.stringify({ measureId, assetId })),
      ),
    ).map((pair) => JSON.parse(pair));

    if (
      uniqueMeasureAssetPairs.length > 0 &&
      JSON.stringify(uniqueMeasureAssetPairs) !== JSON.stringify(previousMeasureIdsRef.current)
    ) {
      previousMeasureIdsRef.current = uniqueMeasureAssetPairs;

      // Close previous EventSource if it's already open
      if (eventSourceRef.current) {
        eventSourceRef.current.close();
        eventSourceRef.current = null;
      }

      // Async function to handle fetching data and opening SSE
      const initializeSSE = async () => {
        try {
          const fetchPromises = uniqueMeasureAssetPairs.map(
            async ({ measureId, assetId }: { measureId: string; assetId: string }) => {
              const measureDetails = await fetchMeasureData(measureId, assetId);
              return measureDetails;
            },
          );
          const measures = await Promise.all(fetchPromises);
          const metricNamesParam = measures.map((me) => me.measurementId).join(',');
          const es = new EventSource(
            // `${process.env.NEXT_PUBLIC_BE_ADMIN_API_URL}/timeseries/api/v1_0/realtime_data?measurement_ids=${metricNamesParam}`,
            `${process.env.NEXT_PUBLIC_BE_ADMIN_API_URL}/timeseries/api/v1_0/realtime_data?measurement_ids=${metricNamesParam}`,
            // `http://localhost:8089/api/v1_0/realtime_data?measurement_ids=${metricNamesParam}`,
            //  keeping this for local use.
            {
              withCredentials: true, // Optional, only if you need to send cookies along
            },
          );

          eventSourceRef.current = es;
          setSetsseEventSource(es);
        } catch (err) {
          console.error('Error initializing SSE:', err);
        }
      };
      initializeSSE(); // Call the async function
    }
  }, [widgets, settings.assetMeasure, fetchMeasureData]);

  // Handle SSE events
  useEffect(() => {
    const handleEvent = (event: MessageEvent) => {
      const parsedData = JSON.parse(event.data);

      const expectedMeasurementId = measureIdMapping.current[measureId];

      if (parsedData.measurementId === expectedMeasurementId?.toString()) {
        const { timestamp, value } = parsedData;

        setData((prevData) => {
          const newData = {
            x: [...prevData.x, Number(timestamp)],
            y: [...prevData.y, value],
          };
          if (retention > 0) {
            const latestTimestamp = Date.now();
            const retentionTime = latestTimestamp - retention * 60000;
            // newData.x = newData.x.filter((time) => time >= retentionTime);
            newData.x = newData.x.filter((time) => time);
            newData.y = newData.y.slice(newData.x.length * -1);
          }
          setIsLoading(false);
          return newData;
        });
        setRevision((prev) => prev + 1);
      }
    };

    if (sseEventSource) {
      sseEventSource.addEventListener('message', handleEvent);
    }

    return () => {
      if (sseEventSource) {
        sseEventSource.removeEventListener('message', handleEvent);
        sseEventSource.close(); // Ensure SSE is properly closed
      }
    };
  }, [sseEventSource, measureId, retention, settings.assetMeasure]);

  useEffect(() => {
    let intervalId: NodeJS.Timeout | null = null;

    const scheduleNextInterval = () => {
      const randomInterval = Math.floor(Math.random() * (3000 - 500 + 1)) + 500;

      intervalId = setTimeout(() => {
        setData((prevData) => {
          const incrementedX = [...prevData.x, Date.now()];
          const incrementedY = [...prevData.y, Math.random() * 100];

          return {
            x: incrementedX.slice(-prevData.x.length - 1),
            y: incrementedY.slice(-prevData.y.length - 1),
          };
        });

        scheduleNextInterval();
      }, randomInterval);
    };

    if (router.pathname === '/dashboard-template' && settings.selectedDbMeasureId !== '') {
      const initialX = [Date.now()];
      const initialY = [Math.random() * 100];
      setData({ x: initialX, y: initialY });

      scheduleNextInterval();
    }

    return () => {
      if (intervalId) {
        clearTimeout(intervalId);
      }
    };
  }, [router.pathname, settings.selectedDbMeasureId]);

  // Handle retention updates
  useEffect(() => {
    if (retention > 0) {
      setData((prevData) => {
        const latestTimestamp = Date.now();
        const retentionTime = latestTimestamp - retention * 60000;
        // Filter based on retention
        const filteredX = prevData.x.filter((time) => time >= retentionTime);
        const filteredY = prevData.y.slice(filteredX.length * -1);
        return { x: filteredX, y: filteredY };
      });
      setRevision((prev) => prev + 1);
    }
  }, [retention]);

  return { data, revision, isLoading, realTimeChartName };
};
