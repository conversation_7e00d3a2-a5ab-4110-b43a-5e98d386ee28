import { test, expect } from '@playwright/test';

test('asset deatils', async ({ page }) => {
  // Opening the login page
  await page.goto('https://test.pivotol.ai/login');

  // Fill in Username and Password
  await page.getByLabel('Username *').fill('test');
  await page.getByLabel('Password *').fill('Br0mpt0n!0T');

  // Click on Login Button
  await page.click('#__next > div > div > form > div > button');
  await page.waitForTimeout(3000); // Wait for login to process

  // Navigate to Assets -> Manage Assets
  await page.getByRole('button', { name: 'Assets' }).click();
  await page.waitForTimeout(3000);
  await page.getByRole('menuitem', { name: 'Manage Assets' }).click();
  await page.waitForTimeout(10000);
  // Ensure the asset tree is loaded before proceeding
  //await page.waitForSelector('.MuiTreeItem-content', { timeout: 10000 });
  await page
    .locator('div.MuiBox-root.css-c36nl0:has-text("MAINPANEL_MQTT")')
    .click({ button: 'right' });
  await page.waitForTimeout(8000);

  await page.getByRole('menuitem', { name: 'Details' }).click();
  await page.waitForTimeout(3000);

  // Click the first "Trend" link (if multiple exist)
  //await page.locator('#basic-menu div').first().click();
  await page
    .getByRole('row', {
      name: 'Brenes\\MAINPANEL_MQTT\\ActivePowerDemand 2025-03-05 23:26:02 -0.0466 (watts)',
    })
    .locator('a')
    .click();
  await page.getByLabel('Time Range').click();
  await page.getByRole('button', { name: 'Last 90 days' }).click();
  await page.getByRole('button', { name: 'Apply' }).click();
  await page.getByRole('checkbox').check();
  await page.getByRole('button', { name: 'Submit' }).click();
  await page.waitForTimeout(3000);
  // Close the page
  await page.close();
});
