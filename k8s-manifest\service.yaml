apiVersion: v1
kind: Service
metadata:
  name: admin-ui-${ENVIRONMENT}
  namespace: application
  labels:
    app.kubernetes.io/instance: admin-ui-${ENVIRONMENT}
    app.kubernetes.io/name: admin-ui-${ENVIRONMENT}
spec:
  type: ClusterIP
  ports:
    - port: 80
      targetPort: 8080
      protocol: TCP
      name: http
  selector:
    app.kubernetes.io/instance: admin-ui-${ENVIRONMENT}
    app.kubernetes.io/name: admin-ui-${ENVIRONMENT}