FROM node:16.15.0-slim as build

RUN mkdir -p /opt/brompton-admin-web-app && mkdir -p .yarn/releases

WORKDIR /opt/brompton-admin-web-app

COPY .yarn/releases ./.yarn/releases

COPY yarn.lock package.json .yarnrc.yml ./

RUN yarn install --frozen-lockfile

COPY ./public ./

COPY ./src ./

RUN yarn build

COPY .next/ ./

# runtime stage
FROM node:16.15.0-slim as production

RUN mkdir -p /opt/brompton-admin-web-app && mkdir -p /etc/brompton-admin-web-app
WORKDIR /opt/brompton-admin-web-app

COPY --from=build /opt/brompton-admin-web-app/.next/standalone .
COPY --from=build /opt/brompton-admin-web-app/.next/static .next/static
COPY --from=build /opt/brompton-admin-web-app/public public

CMD node server.js