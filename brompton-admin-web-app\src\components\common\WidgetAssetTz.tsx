import { Card } from '@material-ui/core';
import { Checkbox, FormControlLabel, FormGroup } from '@mui/material';
import { OverrideAssetTzWidgets } from '~/types/widgets';

type WidgetAssetTzProps<T extends OverrideAssetTzWidgets> = {
  settings: T;
  setSettings: (value: ((prevState: T) => T) | T) => void;
};
const WidgetAssetTz = ({ settings, setSettings }: WidgetAssetTzProps<OverrideAssetTzWidgets>) => {
  const setOverriderAssetTz = (event: React.ChangeEvent<HTMLInputElement>, checked: boolean) => {
    setSettings({
      ...settings,
      overrideAssetTz: checked,
    });
  };
  const setOverriderAssetTzValue = (
    event: React.ChangeEvent<HTMLInputElement>,
    checked: boolean,
  ) => {
    setSettings({
      ...settings,
      overrideAssetTzValue: checked,
    });
  };
  return (
    <Card>
      <FormGroup>
        <FormControlLabel
          control={
            <Checkbox
              sx={{
                p: 2,
              }}
              checked={settings.overrideAssetTz}
              onChange={setOverriderAssetTz}
              name="overrideAssetTz"
            />
          }
          label="Override Asset Timezone"
        />
      </FormGroup>
      {settings.overrideAssetTz ? (
        <FormGroup>
          <FormControlLabel
            control={
              <Checkbox
                sx={{
                  p: 2,
                }}
                checked={settings.overrideAssetTzValue}
                onChange={setOverriderAssetTzValue}
                name="overrideAssetTzValue"
              />
            }
            label="Use Asset Timezone?"
          />
        </FormGroup>
      ) : null}
    </Card>
  );
};
export default WidgetAssetTz;
