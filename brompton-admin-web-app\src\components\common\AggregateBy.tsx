import { Box, MenuItem, OutlinedInput, Select } from '@mui/material';
import { AggByOptions } from '~/types/dashboard';
import FormControl from '@mui/material/FormControl';
import React from 'react';
import { SelectChangeEvent } from '@mui/material/Select';

export type AggregateByProps = {
  id: string;
  value: number;
  handleChange: (e: SelectChangeEvent<number>) => void;
};

export function AggregateBy({ id, value, handleChange }: AggregateByProps) {
  return (
    <FormControl fullWidth>
      <Box display="flex" alignItems="center" width="100%" gap={1}>
        <Select
          fullWidth
          labelId={'aggregate-by-label-' + id}
          id={'aggregate-by-select-' + id}
          label="Aggregate By"
          value={value}
          input={
            <OutlinedInput
              label="Aggregate By"
              sx={{
                p: 0.2,
                '& legend': {
                  maxWidth: '100%',
                  height: 'fit-content',
                  '& span': {
                    opacity: 1,
                  },
                },
              }}
            />
          }
          onChange={handleChange}
        >
          {AggByOptions.map((option) => (
            <MenuItem key={option.value} value={option.value}>
              {option.label}
            </MenuItem>
          ))}
        </Select>
      </Box>
    </FormControl>
  );
}
