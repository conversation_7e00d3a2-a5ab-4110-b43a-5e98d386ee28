# Dashboard Slice Test Suite

This document describes the comprehensive test suite for the `dashboardSlice.ts` Redux slice, which manages the entire dashboard state in the Brompton Admin Web App.

## Overview

The dashboard slice is the core state management module that handles:
- Dashboard metadata (ID, title, customer)
- User authentication and preferences
- Widget management (creation, deletion, layout)
- Tree navigation and node selection
- Template management
- Responsive layout functionality
- Chart and top panel configurations

## Test File Structure

```
src/redux/slices/__tests__/
├── dashboardSlice.test.ts              # Main comprehensive test suite
├── dashboardSlice.responsive.test.ts   # Responsive layout specific tests
└── README.dashboardSlice-tests.md      # This documentation
```

## Test Categories

### 1. Initial State Tests
- ✅ Verifies correct default values for all state properties
- ✅ Ensures proper initialization of nested objects (widget, tree, template, etc.)
- ✅ Validates responsive layout default structure

### 2. Customer Actions Tests
- ✅ `setActiveCustomer` - Setting and switching customers
- ✅ Dashboard reset behavior when switching customers
- ✅ Customer persistence when setting same customer

### 3. Dashboard State Actions Tests
- ✅ `setCurrentDashboardId` - Dashboard ID management
- ✅ `setDashboardTitle` - Dashboard title updates
- ✅ `setMainPanel` - Panel switching (chart/tree)
- ✅ `setIsLeftPanelOpen` - Left panel visibility
- ✅ `setFullScreen` - Full screen mode
- ✅ `setRightSideBar` - Right sidebar visibility
- ✅ `setWidgetsZoom` - Widget zoom functionality
- ✅ `setDateFormat` - Date format preferences
- ✅ `setNewMeasureId` - Measure ID tracking

### 4. User Actions Tests
- ✅ `setUserToken` - Authentication token management
- ✅ `resetUserToken` - Token cleanup
- ✅ `setUserPreferences` - User preference storage
- ✅ `setRightSideBarActiveTab` - Active tab tracking

### 5. Tree Actions Tests
- ✅ `setCurrentSelectedNodeId` - Node selection
- ✅ `setSelectedNodeIds` - Multiple node selection
- ✅ `removeSelectedNodeId` - Node deselection
- ✅ `setExpandedNodeIds` - Tree expansion state
- ✅ `setSelectedViewMeasureId` - Measure selection

### 6. Checkbox Actions Tests
- ✅ `selectCheckbox` - Metric selection with dirty state
- ✅ `unSelectAllCheckbox` - Clear all selections
- ✅ `unselectCheckbox` - Individual metric deselection

### 7. Widget Layout Actions Tests
- ✅ `setWidgetsLayout` - Layout configuration
- ✅ `updateLayout` - Layout modifications with dirty state
- ✅ `setStaticLayout` - Static widget positioning

### 8. Responsive Layout Actions Tests
- ✅ `setResponsiveLayouts` - Responsive layout configuration
- ✅ `setDesktopMobileMode` - Mode switching (desktop ↔ mobile)
- ✅ Layout synchronization between modes
- ✅ Responsive layout updates during widget modifications

### 9. Widget Actions Tests
- ✅ `setWidget` - Widget data configuration
- ✅ `addWidget` - Widget creation (stats, chart, table)
- ✅ `cloneWidget` - Widget duplication
- ✅ `setCurrentWidgetSettings` - Widget configuration updates
- ✅ `deleteWidget` - Individual widget removal
- ✅ `deleteAllWidgets` - Bulk widget deletion

### 10. Dashboard Crumb Actions Tests
- ✅ `setDashboardCrumb` - Breadcrumb navigation
- ✅ `removeDashboardCrumb` - Breadcrumb removal
- ✅ `resetDashboardCrumb` - Breadcrumb reset

### 11. Template Actions Tests
- ✅ `setTemplate` - Template configuration
- ✅ `setAssetType` - Asset type selection
- ✅ `setAssetTemplate` - Template selection
- ✅ `setTemplateName` - Template naming
- ✅ `setTemplateId` - Template ID management
- ✅ `setMetrics` - Metric configuration
- ✅ `setMetricsIdToName` - Metric name mapping
- ✅ `unsetMetricsIdToName` - Metric name removal
- ✅ `createNewDashboardTemplate` - Template reset
- ✅ Template panel configurations (sample period, timezone, etc.)

### 12. Chart Date Actions Tests
- ✅ `setChartStartDate` - Chart date range start
- ✅ `setChartEndDate` - Chart date range end

### 13. Top Panel Actions Tests
- ✅ `setSamplePeriod` - Data sampling configuration
- ✅ `setAssetTz` - Asset timezone settings
- ✅ `setRefreshInterval` - Auto-refresh configuration
- ✅ `setTimeRangeType` - Time range selection
- ✅ `setTopPanelVisibility` - Panel visibility

### 14. New Dashboard Action Tests
- ✅ `setNewDashboard` - Complete dashboard reset
- ✅ State cleanup and initialization

### 15. Widget Title Management Tests
- ✅ `removeTemplateSpecificTitleFromWidget` - Selective title removal
- ✅ `removeWidgetTitles` - Bulk title cleanup

### 16. Edge Cases and Error Handling Tests
- ✅ Non-existent widget operations
- ✅ Invalid node ID operations
- ✅ Missing responsive layout handling
- ✅ Graceful error handling for all edge cases

### 17. State Immutability Tests
- ✅ State mutation prevention
- ✅ Array reference integrity
- ✅ Object immutability verification

## Test Data and Mocks

### Mock Customer
```typescript
const mockCustomer: Customer = {
  id: 1,
  name: 'Test Customer',
  nameId: 'test-customer',
  address: '123 Test Street',
  logo: 'test-logo.png',
};
```

### Mock Layouts
```typescript
const desktopLayout: Layout[] = [
  { i: '1', x: 0, y: 0, w: 6, h: 4 },
  { i: '2', x: 6, y: 0, w: 6, h: 4 },
];

const mobileLayout: Layout[] = [
  { i: '1', x: 0, y: 0, w: 12, h: 4 },
  { i: '2', x: 0, y: 4, w: 12, h: 4 },
];
```

### Mock User Preferences
```typescript
const preferences: UserPreferences = {
  DATE_FORMAT: 'DD-MM-YYYY HH:mm:ss',
  DEFAULT_CUSTOMER: 'test-customer',
  THOUSAND_SEPARATOR: 'enabled',
};
```

## Running the Tests

### Run All Dashboard Slice Tests
```bash
npm test -- dashboardSlice.test.ts
```

### Run Specific Test Categories
```bash
# Run only widget tests
npm test -- --testNamePattern="Widget"

# Run only responsive layout tests
npm test -- --testNamePattern="Responsive"

# Run only template tests
npm test -- --testNamePattern="Template"
```

### Run with Coverage
```bash
npm test -- --coverage dashboardSlice.test.ts
```

### Watch Mode for Development
```bash
npm test -- --watch dashboardSlice.test.ts
```

## Test Assertions

### State Verification
- Property value assertions
- Array length and content verification
- Object structure validation
- Dirty state tracking

### Behavior Verification
- Action dispatch effects
- State transitions
- Side effect validation
- Error handling

### Immutability Verification
- Reference equality checks
- State mutation prevention
- Array/object copying validation

## Coverage Goals

- **Line Coverage**: 100%
- **Branch Coverage**: 100%
- **Function Coverage**: 100%
- **Statement Coverage**: 100%

## Key Testing Patterns

### 1. Store Setup
```typescript
beforeEach(() => {
  store = configureStore({
    reducer: { dashboard: dashboardSlice.reducer },
  });
});
```

### 2. Action Testing
```typescript
store.dispatch(dashboardSlice.actions.actionName(payload));
const state = store.getState().dashboard;
expect(state.property).toBe(expectedValue);
```

### 3. State Comparison
```typescript
const initialState = store.getState().dashboard;
// ... perform actions
const newState = store.getState().dashboard;
expect(newState).not.toBe(initialState);
```

## Maintenance Guidelines

### Adding New Tests
1. Follow existing naming conventions
2. Group related tests in describe blocks
3. Use descriptive test names
4. Include both positive and negative cases
5. Test edge cases and error conditions

### Updating Tests
1. Update tests when modifying slice actions
2. Ensure backward compatibility
3. Add new test cases for new functionality
4. Maintain test data consistency

### Best Practices
1. Use `beforeEach` for store setup
2. Test one action per test case
3. Verify both state changes and side effects
4. Include immutability checks
5. Test error handling and edge cases

## Dependencies

- **Jest**: Test runner and assertion library
- **Redux Toolkit**: State management testing utilities
- **TypeScript**: Type checking and compilation
- **React Grid Layout**: Layout type definitions

## Common Issues and Solutions

### Type Errors
- Ensure mock data matches interface requirements
- Use proper type assertions for complex objects
- Import correct types from the codebase

### State Mutations
- Always check state immutability
- Verify new object/array references
- Test state isolation between tests

### Async Operations
- Use appropriate async testing patterns
- Handle promise resolution/rejection
- Test loading states and error conditions
