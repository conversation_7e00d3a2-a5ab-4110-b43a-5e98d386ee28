const {test, expect} = require('@playwright/test');


test('Login1',async ({page})=> {

    await page.goto('https://test.brompton.ai/login');

   // await page.goto('http://localhost:3000/login?redirect=/&');
  await page.getByLabel('Username *').click();
  await page.getByLabel('Username *').fill('test');
  await page.getByLabel('Password *').click();
  await page.getByLabel('Password *').fill('asdfasdf');
  await page.getByRole('button', { name: 'Log in' }).click();
  await page.locator('#widgets-icon').click();
  await page.locator('#stats').dragTo(page.locator('.react-grid-layout.layout'));
  await page.locator('#title').dragTo(page.locator('.react-grid-layout.layout'));
  await page.locator('#table').dragTo(page.locator('.react-grid-layout.layout'));
    //const pagetitle=page.title();
    //console.log('Page Title is:',pagetitle);
/*
    await page.locator('id=:r1:').fill('test');
    await page.locator('id=:r1:').click();
    await page.locator('id=:r2:').fill('test');
    await page.locator('id=:r2:').click();

   // await page.locator('autocomplete=current-password').click();
   // await page.locator('autocomplete=current-password').fill('asdfasdf');
    await page.locator('type=submit').click();
*/

    await page.close();


});

