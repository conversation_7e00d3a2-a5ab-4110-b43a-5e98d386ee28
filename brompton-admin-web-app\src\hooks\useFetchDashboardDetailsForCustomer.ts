import { useEffect, useState } from 'react';
import {
  useGetDashboardByCustomerIdQuery,
  useGetUserDashboardDetailsQuery,
} from '~/redux/api/dashboardApi';
import { DashboardDetails, DashboardState } from '~/types/dashboard';
import { initialState } from '~/redux/slices/dashboardSlice';
import { useSelector } from 'react-redux';
import { getCurrentDashboardId } from '~/redux/selectors/dashboardSelectors';

const defaultDashboardState: DashboardDetails = {
  id: 0,
  title: '',
  customerId: 0,
  description: '',
  data: {
    tree: { ...initialState.tree },
    widget: { ...initialState.widget },
    topPanel: { ...initialState.topPanel },
    chart: { ...initialState.chart },
    isLeftPanelOpen: initialState.isLeftPanelOpen,
  } as DashboardState,
};
export const useFetchDashboardDetailsForCustomer = (customerId: number, dashId: number) => {
  const [dashboardId, setDashboardId] = useState<number>(dashId);
  const [dashboardDetails, setDashboardDetails] = useState<DashboardDetails | undefined>(undefined);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [isError, setIsError] = useState<boolean>(false);
  const [isSuccess, setIsSuccess] = useState<boolean>(false);
  const currentDashboardId = useSelector(getCurrentDashboardId);
  const {
    data: dashboardList,
    isLoading: isDashboardListLoading,
    isError: isDashboardListError,
    isSuccess: isDashboardListSuccess,
  } = useGetDashboardByCustomerIdQuery(
    {
      customerId: customerId,
      search: null,
    },
    {
      skip: !customerId && currentDashboardId !== 0,
      // refetchOnMountOrArgChange: true,
    },
  );

  const {
    data: dashboardData,
    isLoading: isDashboardDetailsLoading,
    isError: isDashboardDetailsError,
    isSuccess: isDashboardDetailsSuccess,
  } = useGetUserDashboardDetailsQuery(
    { dashboardId, customerId },
    {
      skip: !dashboardId || dashboardId <= 0 || isNaN(dashboardId),
      // skip: !dashboardId || !customerId || dashboardId <= 0,
    },
  );

  useEffect(() => {
    if (customerId === 0) {
      setDashboardId(0);
      setDashboardDetails(undefined);
      setIsLoading(false);
      setIsError(false);
      setIsSuccess(false);
    } else {
      setDashboardDetails(undefined);
      setIsLoading(isDashboardListLoading);
      setIsError(isDashboardListError);
      setIsSuccess(isDashboardListSuccess);

      if (dashboardList?.items) {
        if (dashboardList.items.length > 0) {
          const dashboards = [...dashboardList.items].sort((a, b) => a.id - b.id);
          setDashboardId(
            dashboards.find((d) => d.id === currentDashboardId)?.id || dashboards[0].id,
          );
          setDashboardDetails(dashboardData);
          setIsLoading(isDashboardDetailsLoading);
          setIsError(isDashboardDetailsError);
          setIsSuccess(isDashboardDetailsSuccess);
        } else {
          setDashboardId(0);
          setDashboardDetails(defaultDashboardState);
          setIsLoading(false);
          setIsError(false);
          setIsSuccess(true);
        }
      }
    }
  }, [
    customerId,
    dashboardData,
    dashboardList?.items,
    dashboardId,
    isDashboardDetailsError,
    isDashboardDetailsLoading,
    isDashboardDetailsSuccess,
    isDashboardListError,
    isDashboardListLoading,
    isDashboardListSuccess,
    currentDashboardId,
  ]);

  return { isLoading, dashboardDetails, isSuccess, isError, dashboardId };
};
