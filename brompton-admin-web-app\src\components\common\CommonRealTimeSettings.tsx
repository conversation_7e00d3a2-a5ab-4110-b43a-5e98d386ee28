import {
  Box,
  Card,
  Checkbox,
  FormControl,
  FormControlLabel,
  FormGroup,
  InputLabel,
  MenuItem,
  Select,
  SelectChangeEvent,
} from '@mui/material';
import { NonRealTimeWidgets } from '~/types/widgets';

type CommonRealTimeSettingsProps<T extends NonRealTimeWidgets> = {
  settings: T;
  setSettings: (value: ((prevState: T) => T) | T) => void;
};
const CommonRealTimeSettings = ({
  settings,
  setSettings,
}: CommonRealTimeSettingsProps<NonRealTimeWidgets>) => {
  const handleCheckboxChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSettings({
      ...settings,
      isRealTime: event.target.checked,
    });
  };
  const handleRetainPeriodChange = (event: SelectChangeEvent<number>, child: React.ReactNode) => {
    setSettings({
      ...settings,
      retainPeriod: Number(event.target.value),
    });
  };
  return (
    <Card>
      <Box>
        <FormControlLabel
          control={
            <Checkbox
              sx={{ p: 2 }}
              checked={settings.isRealTime}
              onChange={handleCheckboxChange}
              name="realTime"
              color="primary"
            />
          }
          label="Real Time"
        />
      </Box>
      {settings.isRealTime && (
        <Box>
          <FormGroup sx={{ p: 2, pb: 2, pt: 0 }}>
            <FormControl variant="outlined" fullWidth>
              <InputLabel id="retain-period-label">Visible period (in minutes)</InputLabel>
              <Select
                labelId="retain-period-label"
                name="retainPeriod"
                value={settings.retainPeriod}
                onChange={handleRetainPeriodChange}
                label="Visible period (in minutes)"
              >
                <MenuItem value={1}>1 minute</MenuItem>
                <MenuItem value={2}>2 minutes</MenuItem>
                <MenuItem value={5}>5 minutes</MenuItem>
                <MenuItem value={10}>10 minutes</MenuItem>
                <MenuItem value={20}>20 minutes</MenuItem>
                <MenuItem value={30}>30 minutes</MenuItem>
              </Select>
            </FormControl>
          </FormGroup>
        </Box>
      )}
    </Card>
  );
};
export default CommonRealTimeSettings;
