# Contributing Guide

Make sure your issue or feature doesn't have any related issue at [react-date-range repo](https://github.com/Adphorus/react-date-range/issues). If it didn't exist already, create an issue.

## Getting Started

1. First install [Node.js](https://nodejs.org/en/download) and [Yarn](https://yarnpkg.com/lang/en/).

2. Fork the project, clone your local and create a new branch which named like `fix/{bug-description}` or `feature/{feature-description}`.

3. Run `yarn` to install the dependencies.

4. Run `yarn dev` to start development server.

## Building

. Run `run test` and `run lint` for make sure tests passes and linter doesn't throw any error.

. Run `yarn build` compile the library and demo source.

. Push your changes and create a PR and apply code review decisions.

