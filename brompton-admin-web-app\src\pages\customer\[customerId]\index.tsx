import AddIcon from '@mui/icons-material/Add';
import { Autocomplete, Box, Button, Paper, Stack, TextField, Typography } from '@mui/material';
import { useRouter } from 'next/router';
import { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import CustomerList from '~/components/common/TopPanel/CustomerList';
import { useHasAdminAccess } from '~/hooks/useHasAdminAccess';
import { useGetCustomersQuery } from '~/redux/api/customersApi';
import { useGetDashboardByCustomerIdQuery } from '~/redux/api/dashboardApi';
import { getActiveCustomer } from '~/redux/selectors/customerSelectors';
import { getTopPanelVisibility } from '~/redux/selectors/dashboardSelectors';
import { dashboardListSlice } from '~/redux/slices/dashboardListSlice';
import { dashboardSlice } from '~/redux/slices/dashboardSlice';
import { Customer } from '~/types/customers';
const SelectDashboardPage = () => {
  const router = useRouter();
  const dispatch = useDispatch();
  const isTopPanel = useSelector(getTopPanelVisibility);
  const activeCustomer = useSelector(getActiveCustomer);
  const { globalAdmin, admin } = useHasAdminAccess();
  const { setActiveCustomer } = dashboardSlice.actions;
  const [currentDashboard, setCurrentDashboard] = useState<{ id: number; title: string } | null>(
    null,
  );
  const { data: dashboardList, isLoading: isLoadingDashboards } = useGetDashboardByCustomerIdQuery(
    {
      customerId: Number(router.query?.customerId) ?? 0,
      search: null,
    },
    {
      refetchOnMountOrArgChange: true,
    },
  );

  const { data: customerList, isLoading: isCustomerListLoading } = useGetCustomersQuery({});

  useEffect(() => {
    if (router.query.customerId && customerList) {
      const selectedCustomer = customerList.find(
        (customer) => customer.id === Number(router.query.customerId),
      );
      if (selectedCustomer) {
        dispatch(setActiveCustomer(selectedCustomer));
        router.push(`/customer/${router.query?.customerId}`);
      }
    }
  }, [customerList, dispatch, router.query?.customerId]);

  useEffect(() => {
    if (dashboardList?.items && activeCustomer?.id === Number(router.query?.customerId)) {
      const defaultDashboard = dashboardList.items.find((dashboard) => dashboard.default);
      if (defaultDashboard) {
        setCurrentDashboard({ id: defaultDashboard.id, title: defaultDashboard.title });
        dispatch(dashboardSlice.actions.setCurrentDashboardTitle(defaultDashboard.title));
        dispatch(dashboardSlice.actions.resetDashboardCrumb());
        dispatch(dashboardSlice.actions.selectMainPanel('chart'));
        router.push(`/customer/${activeCustomer?.id}/dashboard/${defaultDashboard.id}`);
      }
    }
    if (dashboardList?.items && dashboardList?.items.length === 0) {
      router.push('/dashboard-list');
    }
  }, [dashboardList?.items, dispatch, router, activeCustomer?.id, dashboardSlice.actions]);
  const handleChangeCustomer = (e: React.SyntheticEvent, value: Customer | null) => {
    if (value === null) {
      return;
    }
    dispatch(dashboardSlice.actions.selectMainPanel('chart'));
    dispatch(dashboardSlice.actions.setCurrentDashboardTitle(''));
    dispatch(dashboardSlice.actions.resetDashboardCrumb());
    dispatch(setActiveCustomer(value));
    router.push(`/customer/${value.id}`);
  };

  const handleChangeDashboard = (
    e: React.SyntheticEvent,
    value: { id: number; title: string } | null,
  ) => {
    if (!value) return;
    setCurrentDashboard(value);
    dispatch(dashboardSlice.actions.selectMainPanel('chart'));
    dispatch(dashboardSlice.actions.setCurrentDashboardTitle(value.title));
    dispatch(dashboardSlice.actions.resetDashboardCrumb());
    router.push(`/customer/${activeCustomer?.id}/dashboard/${value.id}`);
  };
  return (
    <>
      <Box p={2} pb={0} sx={{ display: 'flex', pl: 3, pr: 3 }}>
        <Box
          sx={{
            flexGrow: 1,
            display: 'flex',
            alignItems: 'center',
          }}
        >
          <Typography variant="h4" fontWeight={'bold'}>
            Dashboard
          </Typography>
        </Box>
        <Stack
          gap={0.5}
          p={1}
          width="100%"
          display="flex"
          sx={{ p: 3, pt: 2, pb: 0, flexGrow: 1 }}
          overflow={'auto'}
        >
          <CustomerList
            customerList={customerList}
            activeCustomer={activeCustomer}
            handleChangeCustomer={handleChangeCustomer}
            isCustomerListLoading={isCustomerListLoading}
          />
        </Stack>
        {globalAdmin || admin ? (
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <Button
              startIcon={<AddIcon />}
              variant="contained"
              sx={{ width: 'max-content' }}
              onClick={() => {
                dispatch(dashboardListSlice.actions.setCurrentDashboardId(-1));
                dispatch(dashboardSlice.actions.setNewDashboard());
                router.push(`/customer/${activeCustomer?.id}/dashboard/0`);
              }}
            >
              Add Dashboard
            </Button>
          </Box>
        ) : null}
      </Box>
      <Stack
        gap={0.5}
        p={1}
        width="100%"
        display="flex"
        sx={{ p: 3, pt: 2, pb: 0, flexGrow: 1 }}
        overflow={'auto'}
      >
        <Paper elevation={3}>
          <Box
            display="flex"
            sx={{
              flexGrow: 1,
              overflow: 'auto',
              display: isTopPanel ? 'flex' : 'none',
              backgroundColor: '#f0f0f0',
            }}
            p={1}
            gap={1}
          >
            <Autocomplete
              id="combo-box-demo"
              size="small"
              options={
                dashboardList?.items?.map((dashboard) => {
                  return { id: dashboard.id, title: dashboard.title };
                }) ?? []
              }
              loading={isLoadingDashboards}
              getOptionLabel={(option) => option.title}
              onChange={handleChangeDashboard}
              sx={{ width: 300, ml: 1 }}
              isOptionEqualToValue={(option, value) => option.id === value.id}
              value={currentDashboard ?? null}
              renderInput={(params) => <TextField {...params} label="Select Dashboard" />}
            />
          </Box>
        </Paper>
      </Stack>
    </>
  );
};

export default SelectDashboardPage;
