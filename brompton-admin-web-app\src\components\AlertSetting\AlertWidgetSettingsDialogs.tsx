import { <PERSON>comple<PERSON>, Box, Divider, TextField, Typography } from '@mui/material';
import { useEffect, useMemo, useState } from 'react';
import { useGetAllBackOfficeAssetTypesQuery } from '~/redux/api/assetsApi';
import { useGetAllMeasureTypesQuery } from '~/redux/api/measuresApi';
import { AssetTypeOption } from '~/types/asset';
import { AlertWidget, setMultiMeasureWidgetSettings } from '~/types/widgets';
import { assetTypePathMapper } from '~/utils/mappers/asset-type-mapper';
import DataWidgetSettingsContainer from '../common/DataWidgetSettingsContainer/DataWidgetSettingsContainer';
import MultiMeasureSelection from '../common/MultiMeasureSelection';
import MultiMeasureSelectionMenu from '../common/MultiMeasureSelectionMenu';

type AlertWidgetSettingsDialogsProps = {
  settings: AlertWidget;
  handleSettingsChange: (value: ((prevState: AlertWidget) => AlertWidget) | AlertWidget) => void;
};
const AlertWidgetSettingsDialogs = ({
  settings,
  handleSettingsChange,
}: AlertWidgetSettingsDialogsProps) => {
  const [assetTypesWithPath, setAssetTypesWithPath] = useState<AssetTypeOption[]>([]);
  const [errorAlerts, setErrorAlerts] = useState<{
    asset: boolean;
    assetType: boolean;
    measureType: boolean;
    first: boolean;
  }>({
    asset: false,
    assetType: false,
    measureType: false,
    first: false,
  });
  const { data: measurementTypes } = useGetAllMeasureTypesQuery();
  const {
    data: assetTypeListData,
    isLoading: isAssetTypeLoading,
    isSuccess: isSuccessfullBackOffieAssetTypes,
  } = useGetAllBackOfficeAssetTypesQuery();
  useEffect(() => {
    if (
      errorAlerts.first &&
      settings.assetTypes.length === 0 &&
      settings.measurementTypes.length === 0 &&
      (settings.assetMeasure.length === 0 ||
        settings.assetMeasure.filter((assetMeasurement) => assetMeasurement?.assetId.trim() !== '')
          .length === 0)
    ) {
      setErrorAlerts({
        asset: true,
        assetType: true,
        measureType: true,
        first: true,
      });
      handleSettingsChange({
        ...settings,
        isValid: false,
      });
    } else {
      setErrorAlerts({
        ...errorAlerts,
        asset: false,
        assetType: false,
        measureType: false,
        first: true,
      });
      handleSettingsChange({
        ...settings,
        isValid: true,
      });
    }
  }, [settings.assetMeasure, settings.assetTypes, settings.measurementTypes]);

  useEffect(() => {
    if (isSuccessfullBackOffieAssetTypes && assetTypeListData) {
      setAssetTypesWithPath(
        assetTypePathMapper(
          assetTypeListData.map((item) => ({
            ...item,
            name: item.name,
            id: item.id,
          })),
        ),
      );
    }
  }, [assetTypeListData, isSuccessfullBackOffieAssetTypes]);

  const memoizedMultiMeasureSelectionMenu = useMemo(() => {
    return (
      <MultiMeasureSelectionMenu
        mode={settings.mode}
        settings={settings}
        setSettings={handleSettingsChange as setMultiMeasureWidgetSettings}
        required={errorAlerts.asset}
      />
    );
  }, [settings.mode, settings, handleSettingsChange, errorAlerts.asset]);
  return (
    <DataWidgetSettingsContainer
      settings={settings}
      setSettings={handleSettingsChange}
      exculdedSettings={{
        aggBy: true,
        samplePeriod: true,
        globalSamplePeriod: true,
        overrideAssetTz: true,
        // timeRange: true,
      }}
      hideSettings={{
        aggBy: true,
        realtime: true,
        globalSamplePeriod: true,
        overrideAssetTz: true,
        // timeContext: true,
      }}
      dataTabChildren={
        <>
          {settings.mode === 'dashboard' ? (
            // <MultiMeasureSelectionMenu
            //   mode={settings.mode}
            //   settings={settings}
            //   setSettings={handleSettingsChange as setMultiMeasureWidgetSettings}
            //   required={errorAlerts.asset}
            // />
            memoizedMultiMeasureSelectionMenu
          ) : (
            <MultiMeasureSelection
              mode={settings.mode}
              handleChangeMeasure={(e) => {
                handleSettingsChange((prevState) => {
                  const { selectedTitles, ...rest } = prevState;
                  const updatedTitles = Array.from(new Set([...selectedTitles, ...e])).filter(
                    (title) => e.includes(title),
                  );
                  return {
                    ...rest,
                    selectedTitles: updatedTitles,
                    isValid: updatedTitles.length > 0 ? true : false,
                  };
                });
              }}
              selectedMeasureNames={settings.selectedTitles}
            />
          )}
          <Box sx={{ display: 'flex', alignItems: 'center', width: '100%', my: 2 }}>
            <Divider sx={{ flex: 1 }} />
            <Typography variant="h6" sx={{ px: 2, whiteSpace: 'nowrap' }}>
              OR
            </Typography>
            <Divider sx={{ flex: 1 }} />
          </Box>
          <Autocomplete
            disablePortal
            multiple
            id={`combo-box`}
            value={assetTypesWithPath.filter((option) =>
              settings.assetTypes?.includes(option.value.toString()),
            )}
            options={assetTypesWithPath ?? []}
            isOptionEqualToValue={(option, value) => option.value === value.value}
            loading={isAssetTypeLoading}
            sx={{ width: '100%' }}
            onChange={(event, value) => {
              handleSettingsChange((prev) => {
                return { ...prev, assetTypes: value.map((ids) => ids.value.toString()) };
              });
            }}
            renderInput={(params) => (
              <TextField
                {...params}
                required={errorAlerts.assetType}
                error={errorAlerts.assetType}
                helperText={errorAlerts.assetType ? 'Asset Type is required.' : ''}
                label="Asset Type"
              />
            )}
          />
          <Box sx={{ display: 'flex', alignItems: 'center', width: '100%', my: 2 }}>
            <Divider sx={{ flex: 1 }} />
            <Typography variant="h6" sx={{ px: 2, whiteSpace: 'nowrap' }}>
              OR
            </Typography>
            <Divider sx={{ flex: 1 }} />
          </Box>
          <Autocomplete
            multiple
            options={
              measurementTypes?.map((type) => ({
                value: String(type.id),
                label: type.name,
              })) || []
            }
            getOptionLabel={(option) => option?.label}
            isOptionEqualToValue={(option, value) => option?.value === value?.value}
            value={(
              measurementTypes?.map((type) => ({
                value: String(type.id),
                label: type.name,
              })) ?? []
            ).filter((type) => settings.measurementTypes?.includes(type.value))}
            onChange={(event, value) => {
              handleSettingsChange((prev) => ({
                ...prev,
                measurementTypes: value.map((item) => item.value),
              }));
            }}
            renderInput={(params) => (
              <TextField
                {...params}
                label="Measurement Type"
                required={errorAlerts.measureType}
                error={errorAlerts.measureType}
                helperText={errorAlerts.measureType ? 'Measure Type is required.' : ''}
                fullWidth
                margin="normal"
              />
            )}
          />
        </>
      }
    />
  );
};

export default AlertWidgetSettingsDialogs;
