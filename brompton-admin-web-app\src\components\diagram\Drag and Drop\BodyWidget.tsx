import * as React from 'react';
import { TrayWidget } from './TragWidget';
import Application from '../Application';
import { TrayItemWidget } from './TragItemWidget';
import { DiagramModel } from '@projectstorm/react-diagrams';
import { CanvasWidget } from '@projectstorm/react-canvas-core';
import { action } from '@storybook/addon-actions';

import Box from '@mui/material/Box';
import { styled } from '@mui/system';
import { DemoCanvasWidget } from './DemoCanvasWidget';
import { DemoButton, DemoWorkspaceWidget } from './DemoWorkspaceWIdget';

import { CustomNodeModel } from '../model/CustomNodeModel';
import { DefaultState } from '../New Link/DefaultState';
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from '~/redux/store';
import { CustomPortModel } from '../model/CustomPortModel';
import { setIsLoad } from '~/redux/slices/solarSlice';
import { useRouter } from 'next/router';

export interface BodyWidgetProps {
  app: Application;
  value?: number;
  loading?: boolean;
  model?: any;
}

const Body = styled('div')({
  flexGrow: 1,
  display: 'flex',
  flexDirection: 'column',
  minHeight: '100%',
  overflowY: 'hidden',
});

const Header = styled('div')({
  display: 'flex',
  background: 'rgb(0, 0, 0)',
  flexGrow: 0,
  flexShrink: 0,
  color: 'white',
  fontFamily: 'Helvetica, Arial, sans-serif',
  padding: '10px',
  alignItems: 'center',
});

const Content = styled('div')({
  display: 'flex',
  flexGrow: 1,
});

const Layer = styled('div')({
  position: 'relative',
  flexGrow: 1,
});

const initialNodeCounts = {
  building: 0,
  solarPanel: 0,
  grid: 0,
  battery: 0,
  Chamber: 0,
};

const BodyWidget = (props: BodyWidgetProps) => {
  const model = new DiagramModel();
  const isLoad = useSelector((state: RootState) => state.solar.isLoad);
  const [nodeCounts, setNodeCounts] = React.useState(initialNodeCounts);
  const [, updateState] = React.useState<boolean>(false); // Assuming you're using a boolean state
  const forceUpdate = React.useCallback(() => updateState((prevState) => !prevState), []);
  const dispatch = useDispatch();
  const router = useRouter();
  const currentUrl = router.asPath;
  React.useEffect(() => {
    if (currentUrl === '/diagram') {
      dispatch(setIsLoad(false));
    } else {
      dispatch(setIsLoad(true));
    }
  }, [currentUrl]);
  return (
    <Box sx={{ height: '100vh', width: '100%' }}>
      {!isLoad && (
        <Body>
          <Header></Header>
          <Content>
            <TrayWidget>
              <TrayItemWidget model={{ type: 'building' }} name="Building" color="rgb(192,255,0)" />
              <TrayItemWidget
                model={{ type: 'solarPanel' }}
                name="Solar Panel"
                color="rgb(0,192,255)"
              />
              <TrayItemWidget model={{ type: 'grid' }} name="Grid" color="rgb(192,255,0)" />
              <TrayItemWidget model={{ type: 'battery' }} name="Battery" color="rgb(0,192,255)" />
              <TrayItemWidget
                model={{ type: 'CHP' }}
                name="Combined Heat and Power (CHP)"
                color="rgb(192,255,0)"
              />
            </TrayWidget>
            <Layer
              className="mx-auto  "
              onDrop={(event) => {
                const data = JSON.parse(event.dataTransfer.getData('storm-diagram-node'));

                let node: CustomNodeModel | null = null;
                let nodeName: string | null = null;
                const value: number | null = 0;
                let portModel: any = null; // Define the port model variable

                // Determine the type of node and update the count accordingly
                switch (data.type) {
                  case 'building':
                    nodeName = 'BuildingNode' + ++nodeCounts.building;
                    portModel = new CustomPortModel('building', 'building');
                    break;
                  case 'solarPanel':
                    nodeName = 'SolarNode' + ++nodeCounts.solarPanel;
                    portModel = new CustomPortModel('solarPanel', 'solarPanel');
                    break;
                  case 'grid':
                    nodeName = 'GridNode' + ++nodeCounts.grid;
                    portModel = new CustomPortModel('grid', 'grid');
                    break;
                  case 'battery':
                    nodeName = 'BatteryNode' + ++nodeCounts.battery;
                    portModel = new CustomPortModel('battery', 'battery');
                    break;
                  case 'CHP':
                    nodeName = 'ChamberNode' + ++nodeCounts.Chamber;
                    portModel = new CustomPortModel('CHP', 'CHP');
                  default:
                    break;
                }

                if (nodeName !== null && portModel !== null) {
                  node = new CustomNodeModel(nodeName, data.type, value);
                  node.addPort(portModel); // Add the port model to the node
                }

                if (node !== null) {
                  const point = props.app.getDiagramEngine().getRelativeMousePoint(event);
                  node.setPosition(point);
                  props.app.getDiagramEngine().getModel().addNode(node);
                  setNodeCounts({ ...nodeCounts });
                  // forceUpdate();

                  props.app.getDiagramEngine().getStateMachine().pushState(new DefaultState());
                  forceUpdate();
                }
              }}
              onDragOver={(event) => {
                event.preventDefault();
              }}
            >
              <DemoWorkspaceWidget
                buttons={
                  <div>
                    <DemoButton
                      onClick={() => {
                        action('Serialized Graph')(
                          props.app.getDiagramEngine().getModel().serialize(),
                        );
                        const newModel = props.app.getDiagramEngine().getModel();
                        const data = newModel.serialize();

                        // Convert JSON data to a string
                        const jsonData = JSON.stringify(data, null, 2);

                        // Create a Blob containing the JSON data
                        const blob = new Blob([jsonData], {
                          type: 'application/json',
                        });

                        // Create a link element
                        const link = document.createElement('a');

                        // Set the link's attributes
                        link.href = window.URL.createObjectURL(blob);
                        link.download = 'widget.json';

                        // Append the link to the document
                        document.body.appendChild(link);

                        // Trigger a click on the link to initiate download
                        link.click();

                        // Remove the link from the document
                        document.body.removeChild(link);
                      }}
                    >
                      Download State
                    </DemoButton>
                    <DemoButton
                      onClick={() => {
                        props.app
                          .getActiveDiagram()
                          .getLinks()
                          .map((link) => {
                            link.remove();
                          });
                        forceUpdate();
                      }}
                    >
                      Remove All Link
                    </DemoButton>
                    <DemoButton
                      onClick={(event) => {
                        const inputElement = document.createElement('input');
                        inputElement.type = 'file';
                        inputElement.accept = '.json';
                        inputElement.onchange = (e) => {
                          const file = (e.target as HTMLInputElement).files?.[0];
                          if (file) {
                            const fileReader = new FileReader();
                            fileReader.onload = (e) => {
                              try {
                                const fileContent = e.target?.result as string;
                                const parsedContent = JSON.parse(fileContent);
                                // Assuming `props.app` and `props.app.getDiagramEngine()` are accessible
                                props.app
                                  .getActiveDiagram()
                                  .deserializeModel(parsedContent, props.app.getDiagramEngine());

                                props.app
                                  .getDiagramEngine()
                                  .getStateMachine()
                                  .pushState(new DefaultState());
                                forceUpdate();
                              } catch (error) {
                                console.error('Error parsing JSON:', error);
                                // Handle error (e.g., show error message to the user)
                              }
                            };
                            fileReader.readAsText(file);
                          }
                        };
                        inputElement.click(); // Trigger file input dialog
                      }}
                    >
                      Upload Json
                    </DemoButton>
                  </div>
                }
              >
                <DemoCanvasWidget>
                  <CanvasWidget engine={props.app.getDiagramEngine()} />
                </DemoCanvasWidget>
              </DemoWorkspaceWidget>
            </Layer>
          </Content>
        </Body>
      )}
      {isLoad && (
        <DemoCanvasWidget>
          <CanvasWidget engine={props.app.getDiagramEngine()} />
        </DemoCanvasWidget>
      )}
    </Box>
  );
};

export default BodyWidget;
