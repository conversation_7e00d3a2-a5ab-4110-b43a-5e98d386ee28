import EditIcon from '@mui/icons-material/Edit';
import SaveIcon from '@mui/icons-material/Save';
import {
  Autocomplete,
  Box,
  Button,
  Card,
  CardContent,
  CardMedia,
  Container,
  IconButton,
  TextField,
  Typography,
} from '@mui/material';
import { useRouter } from 'next/router';
import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { CustomError } from '~/errors/CustomerErrorResponse';
import { useHasAdminAccess } from '~/hooks/useHasAdminAccess';
import {
  useCreateCustomerMutation,
  useEditCustomerByIdMutation,
  useGetCustomersQuery,
} from '~/redux/api/customersApi';
import { useGetDashboardByCustomerIdQuery } from '~/redux/api/dashboardApi';
import { getActiveCustomer } from '~/redux/selectors/customerSelectors';
import { dashboardSlice } from '~/redux/slices/dashboardSlice';
import { AlertMessage } from '~/shared/forms/types';
import { AlertSnackbar } from '~/shared/snackbars/components/AlertSnackbar';
import { useSnackbar } from '~/shared/snackbars/snackbar-hooks';
import { Customer, EditCustomer } from '~/types/customers';
import CustomDialog from '../common/CustomDialog';
import Loader from '../common/Loader';
import EditCustomerForm from '../customer/EditCustomerForm';
import NewCustomerForm from '../customer/NewCustomerForm';
const ActiveCustomers = () => {
  const dispatch = useDispatch();
  const router = useRouter();
  const [createCustomer, setCreateCustomer] = useState<boolean>(false);
  const { globalAdmin } = useHasAdminAccess();

  const [alertMessage, setAlertMessage] = useState<AlertMessage | undefined>(undefined);
  const {
    data,
    isFetching: loading,
    refetch,
  } = useGetCustomersQuery({
    is_logo: true,
  });
  const [snackbarState, showSuccessAlert, showErrorAlert] = useSnackbar();
  const ActiveCustomer = useSelector(getActiveCustomer);
  const [customerState, setCustomerState] = useState<Customer | null>(ActiveCustomer);
  const [isEditOrDetails, setIsEditOrDetails] = useState<boolean>(false);
  const handleChangeCustomer = (e: React.SyntheticEvent, value: Customer | null) => {
    if (value === null) {
      return;
    }
    setCustomerState(value);
  };
  const {
    data: dashboardList,
    isLoading: isLoadingDashboards,
    isFetching: isFetchingDashboards,
  } = useGetDashboardByCustomerIdQuery(
    {
      customerId: customerState?.id ?? 0,
      search: null,
    },
    {
      skip: !customerState?.id,
      refetchOnMountOrArgChange: true,
    },
  );
  const [
    createNewCustomer,
    {
      data: customer,
      isLoading: createLoading,
      isSuccess: isCreateSuccess,
      error,
      isError: createIsError,
    },
  ] = useCreateCustomerMutation();
  const [
    updateCustomer,
    { error: updateError, isError: updateIsError, data: updateData, isSuccess: updateIsSuccess },
  ] = useEditCustomerByIdMutation();
  useEffect(() => {
    if (data && ActiveCustomer) {
      const currentCustomer = data?.find((customer) => customer.id === ActiveCustomer.id);
      if (currentCustomer) {
        setCustomerState((prevState) => ({
          ...prevState,
          id: currentCustomer.id,
          name: currentCustomer.name || '', // Ensure string is provided
          address: currentCustomer.address || '', // Ensure string is provided
          nameId: currentCustomer.nameId || '', // Ensure string is provided
          logo: currentCustomer.logo ?? '', // Provide a default value for logo
          enabled: typeof currentCustomer.enabled === 'boolean' ? currentCustomer.enabled : false, // Ensure boolean
        }));
      }
    }
  }, [ActiveCustomer, data]);
  const handleActiveCustomer = () => {
    if (isFetchingDashboards) {
      return;
    }
    if (dashboardList?.items.length === 0) {
      if (customerState) {
        dispatch(dashboardSlice.actions.setActiveCustomer(customerState));
        dispatch(dashboardSlice.actions.setCurrentDashboardId(0));
        dispatch(dashboardSlice.actions.setCurrentDashboardTitle(''));
        router.push(`/customer/${customerState.id}/dashboard/0`);
      }
    }
    if (customerState) {
      dispatch(dashboardSlice.actions.setActiveCustomer(customerState));
      dispatch(dashboardSlice.actions.setCurrentDashboardId(0));
      dispatch(dashboardSlice.actions.setCurrentDashboardTitle(''));
      router.push(`/customer/${customerState.id}`);
    }
  };

  useEffect(() => {
    if (updateIsSuccess) {
      showSuccessAlert(`customer updated successfully!`);
      setIsEditOrDetails(false);
      refetch();
    }

    if (updateIsError && updateError) {
      showErrorAlert(updateError && 'Error on update customer');
    }
  }, [updateIsError, updateError, updateData, updateIsSuccess]);
  useEffect(() => {
    if (createIsError && error) {
      const err = error as CustomError;
      showErrorAlert(err.data.exception ?? 'Server error');
    }
    if (isCreateSuccess && customer) {
      showSuccessAlert(`Customer "${customer.nameId}" created successfully!!`);
      setCreateCustomer(false);
      refetch();
    }
  }, [isCreateSuccess, createIsError, error, customer]);
  return (
    <Container maxWidth="xl" sx={{ mt: 2 }}>
      {globalAdmin ? (
        <Typography
          mt={2}
          ml={2}
          onClick={() => {
            setCreateCustomer(true);
          }}
        >
          <Button variant="contained" color="primary" sx={{ float: 'right', mt: 1, mb: 1 }}>
            Add new Customer
          </Button>
        </Typography>
      ) : null}
      <Typography variant="h4" mt={2} mb={1}>
        Active Customer
      </Typography>
      <Typography variant="body1" sx={{ mb: 4 }}>
        Active Customer allow you to select your preferred customer accross the dashboard.
      </Typography>

      <Autocomplete<Customer>
        id="combo-box-demo"
        options={data ?? []}
        loading={loading}
        renderOption={(props, option) => (
          <Box component="li" {...props} sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            <img
              src={(option.logo as any) || '/placeholder.jpg'}
              alt={option.name}
              width={40}
              height={40}
              style={{ borderRadius: '50%' }}
            />
            {option.name}
          </Box>
        )}
        getOptionLabel={(option) => option.name}
        onChange={handleChangeCustomer}
        value={customerState ?? null}
        renderInput={(params) => <TextField {...params} label="Customer" />}
        sx={{ mb: 4 }}
      />
      {loading ? (
        <Loader />
      ) : (
        <>
          <Box width={'100%'} display={'flex'} justifyContent={'end'} mb={2}>
            <Button
              onClick={handleActiveCustomer}
              variant="contained"
              color="primary"
              disabled={isFetchingDashboards}
              startIcon={<SaveIcon />}
            >
              Save
            </Button>
          </Box>

          {customerState && !isEditOrDetails && (
            <Card>
              {globalAdmin ? (
                <Box sx={{ width: '100%', display: 'flex', justifyContent: 'end' }}>
                  <IconButton
                    onClick={() => {
                      setIsEditOrDetails(true);
                    }}
                  >
                    <EditIcon />
                  </IconButton>
                </Box>
              ) : null}
              <Box sx={{ display: 'flex', alignItems: 'center', p: 2, mb: 2 }}>
                <Box>
                  <CardMedia
                    component="img"
                    sx={{ width: 80, height: 80, borderRadius: '50%', mr: 2 }}
                    image={(customerState.logo as any) || '/placeholder.jpg'}
                    alt={customerState.name}
                  />
                  {customerState.logo === null && (
                    <Typography variant="body1" textAlign={'center'} color={'error'}>
                      No Logo
                    </Typography>
                  )}
                </Box>
                <CardContent>
                  <Typography variant="h6">
                    <strong>Customer Name : </strong>
                    {customerState.name}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    <strong>Address:</strong> {customerState.address || 'N/A'}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    <strong>Name:</strong> {customerState.name || 'N/A'}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    <strong>NameId:</strong> {customerState.nameId || 'N/A'}
                  </Typography>
                </CardContent>
              </Box>
            </Card>
          )}

          {isEditOrDetails && customerState && (
            <EditCustomerForm
              loading={loading}
              customer={
                (() => {
                  const found = data?.find((customer) => customer.id === customerState.id);
                  if (!found) return undefined;
                  const { logo, nameId, name, address } = found;
                  return {
                    logo: logo ?? null,
                    nameId: nameId ?? '',
                    name: name ?? '',
                    address: address ?? '',
                  };
                })() as EditCustomer | undefined
              }
              onValidSubmit={(customer: EditCustomer) => {
                if (customerState!.id) {
                  updateCustomer({ customerId: customerState.id, userDetails: customer });
                }
              }}
              onCancel={() => {
                setIsEditOrDetails(false);
              }}
            />
          )}
        </>
      )}

      <CustomDialog
        title="Add Customer"
        content={
          <>
            <NewCustomerForm
              loading={createLoading}
              alertMessage={alertMessage}
              onValidSubmit={createNewCustomer}
              onClose={() => {
                setCreateCustomer(false);
              }}
            />
          </>
        }
        open={createCustomer}
        dialogActions={<></>}
        onClose={() => {
          setCreateCustomer(false);
        }}
      />
      <AlertSnackbar {...snackbarState} />
    </Container>
  );
};

export default ActiveCustomers;
