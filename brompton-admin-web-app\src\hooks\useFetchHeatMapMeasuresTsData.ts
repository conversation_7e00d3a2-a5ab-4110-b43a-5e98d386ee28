import { useDispatch, useSelector } from 'react-redux';
import { getCustomerId } from '~/redux/selectors/customerSelectors';
import { useCallback, useEffect, useState } from 'react';
import { ThunkDispatch } from 'redux-thunk';
import { RootState } from '~/redux/store';
import { timeseriesApi } from '~/redux/api/timeseriesApi';
import { AssetMeasurementDetails } from '~/types/measures';
import { measuresApi } from '~/redux/api/measuresApi';
import { UnitOfMeasure } from '~/measurements/domain/types';
import { SingleHeatMapTimeSeriesData } from '~/types/timeseries';
import { AggByOptions, SamplePeriodOptions } from '~/types/dashboard';
import { getDbMeasureIdToAssetIdMap } from '~/redux/selectors/treeSelectors';
import { AnyAction } from 'redux';
import { HeatmapChartWidget } from '~/types/widgets';
import { getGlobalEndDate, getGlobalStartDate } from '~/redux/selectors/widgetSelectors';
import {
  getAssetTz,
  getGlobalSamplePeriod,
  getGlobalTimeRangeType,
} from '~/redux/selectors/topPanleSelectors';
import { useRouter } from 'next/router';

type MeasuresData = {
  isLoading: boolean;
  isError: boolean;
  error: string;
  tsData: SingleHeatMapTimeSeriesData;
  measureData: AssetMeasurementDetails;
  unitOfMeasures: UnitOfMeasure[];
};

type Params = {
  selectedTitles: string[];
  dataFetchSettings: HeatmapChartWidget;
};

type ApiExtraArg = {
  measuresApi: typeof measuresApi;
  timeseriesApi: typeof timeseriesApi;
};

export function useFetchHeatMapMeasuresTsData({ selectedTitles, dataFetchSettings }: Params) {
  const dispatch = useDispatch<ThunkDispatch<RootState, ApiExtraArg, AnyAction>>();
  const customerId = useSelector(getCustomerId);
  const router = useRouter();
  const dbMeasureIdToAssetIdMap = useSelector(getDbMeasureIdToAssetIdMap);
  const [state, setState] = useState<{
    data: undefined | MeasuresData[];
    isLoading: boolean;
    isError: boolean;
  }>({
    data: undefined,
    isLoading: true,
    isError: false,
  });

  const globalStartDate = useSelector(getGlobalStartDate);
  const globalEndDate = useSelector(getGlobalEndDate);
  const globalTimeRange = useSelector(getGlobalTimeRangeType);
  const globalSamplePeriod = useSelector(getGlobalSamplePeriod);
  const assetTz = useSelector(getAssetTz);

  const {
    startDate: selectedStartDate,
    endDate: selectedEndDate,
    aggBy: selectedAggBy,
    samplePeriod: selectedSamplePeriod,
    timeRange: selectedTimeRange,
    globalSamplePeriod: isGlobalSamplePeriodOverridden,
    overrideGlobalSettings: isGlobalTimeRangeSettingsOverridden,
    overrideAssetTz: isOverrideAssetTz,
    overrideAssetTzValue,
    groupX,
    groupY,
  } = dataFetchSettings;

  const [startDate, setStartDate] = useState(globalStartDate);
  const [endDate, setEndDate] = useState(globalEndDate);
  const [aggBy, setAggBy] = useState(selectedAggBy);
  const [samplePeriod, setSamplePeriod] = useState(globalSamplePeriod);
  const [timeRange, setTimeRange] = useState(globalTimeRange);
  const [assetTzOverride, setAssetTzOverride] = useState(isOverrideAssetTz);
  const [assetTzOverrideValue, setAssetTzOverrideValue] = useState(overrideAssetTzValue);

  useEffect(() => {
    setAggBy(selectedAggBy);

    if (isGlobalTimeRangeSettingsOverridden) {
      setStartDate(selectedStartDate);
      setEndDate(selectedEndDate);
      setTimeRange(selectedTimeRange);
    } else {
      setStartDate(globalStartDate);
      setEndDate(globalEndDate);
      setTimeRange(globalTimeRange);
    }

    if (isGlobalSamplePeriodOverridden) {
      setSamplePeriod(selectedSamplePeriod);
    } else {
      setSamplePeriod(globalSamplePeriod);
    }
    if (isOverrideAssetTz) {
      setAssetTzOverride(true);
      setAssetTzOverrideValue(overrideAssetTzValue);
    } else {
      setAssetTzOverride(false);
    }
  }, [
    globalEndDate,
    globalSamplePeriod,
    globalStartDate,
    globalTimeRange,
    isGlobalSamplePeriodOverridden,
    isGlobalTimeRangeSettingsOverridden,
    selectedAggBy,
    selectedEndDate,
    selectedSamplePeriod,
    selectedStartDate,
    selectedTimeRange,
    isOverrideAssetTz,
    overrideAssetTzValue,
  ]);

  const fetchMeasureData = useCallback(
    async (measureId: string) => {
      if (!dbMeasureIdToAssetIdMap[measureId]) {
        throw new Error(`No assetId found for ${measureId}`);
      }
      if (!measureId) {
        throw new Error(`Error invalid measureId: ${measureId}`);
      }
      const { data: measureData, isSuccess: isMeasureDataSuccess } = await dispatch(
        measuresApi.endpoints?.getMeasurementById.initiate({
          customerId,
          assetId: dbMeasureIdToAssetIdMap[measureId],
          measId: measureId,
        }),
      );

      if (!isMeasureDataSuccess || !measureData) {
        throw new Error(`Error fetching measure data for measureId: ${measureId}`);
      }

      return measureData;
    },
    [customerId, dbMeasureIdToAssetIdMap, dispatch],
  );

  const fetchTimeseriesData = useCallback(
    async (
      tsDbMeasureIds: number[],
    ): Promise<{ error: boolean; tsData: Record<number, SingleHeatMapTimeSeriesData> }> => {
      const { data: tsData, isSuccess: isTsSuccess } = await dispatch(
        timeseriesApi.endpoints.getHeampMeasurementSeries.initiate({
          customerId,
          measId: tsDbMeasureIds.filter(Boolean).sort().join(','),
          start: startDate,
          end: endDate,
          agg: AggByOptions[aggBy].serverValue,
          agg_period: SamplePeriodOptions[samplePeriod].serverValue,
          timeRangeType: timeRange,
          groupX,
          groupY,
          assetTz: isOverrideAssetTz ? assetTzOverrideValue : assetTz,
        }),
      );
      if (!isTsSuccess || !tsData) {
        return {
          error: true,
          tsData: {},
        };
      }
      return { error: false, tsData };
    },
    [
      aggBy,
      customerId,
      dispatch,
      endDate,
      samplePeriod,
      startDate,
      timeRange,
      assetTz,
      groupX,
      groupY,
      isOverrideAssetTz,
      assetTzOverrideValue,
    ],
  );

  const fetchUnitOfMeasure = useCallback(
    async (assetMeasurementTypeId: number) => {
      const { data: unitOfMeasures, isSuccess: isUnitOfMeasureSuccess } = await dispatch(
        measuresApi.endpoints?.getUnitsOfMeasure.initiate({
          measurementTypeId: assetMeasurementTypeId,
        }),
      );

      if (!isUnitOfMeasureSuccess || !unitOfMeasures) {
        throw new Error('Error fetching unit of measure data');
      }

      return unitOfMeasures;
    },
    [dispatch],
  ); // include assetMeasurementTypeId in the dependency array
  const generateRandomData = (
    numPoints: number,
    startTime: number,
    interval: number,
    minValue: number,
    maxValue: number,
  ) => {
    const days = ['SUN', 'MON', 'TUE', 'WED', 'THU', 'FRI', 'SAT'];
    const data: [string, string, number][] = [];
    let currentTime = startTime;

    for (let i = 0; i < numPoints; i++) {
      const hour = String(currentTime % 24).padStart(2, '0');
      const day = days[Math.floor(currentTime / 24) % days.length];
      const value = Math.random() * (maxValue - minValue) + minValue;
      data.push([hour, day, value]);
      currentTime += interval;
    }
    return data;
  };

  useEffect(() => {
    const fetchMeasuresData = async () => {
      setState({ data: undefined, isLoading: true, isError: false });

      const apiResults = selectedTitles
        .filter((measureId) => measureId && measureId !== '')
        .map(async (measureId) => {
          try {
            const measureData = await fetchMeasureData(measureId);
            const unitOfMeasures = await fetchUnitOfMeasure(measureData.typeId);
            return {
              isLoading: false,
              isError: false,
              error: '',
              measureData,
              unitOfMeasures,
            } as MeasuresData;
          } catch (error) {
            return {
              isLoading: false,
              isError: true,
              error: error,
            } as MeasuresData;
          }
        });
      const results = await Promise.all(apiResults);
      const tsMeasureIds = results.map((result) => result.measureData?.measurementId);
      const { error, tsData } = await fetchTimeseriesData(tsMeasureIds);
      if (error) {
        setState({ data: undefined, isLoading: false, isError: true });
        return;
      }
      results.forEach((result) => {
        const seriesData = tsData[result.measureData.measurementId];
        if (seriesData?.error) {
          result.isLoading = false;
          result.isError = true;
          result.error = seriesData.error;
        } else {
          result.isLoading = false;
          result.tsData = seriesData;
        }
      });
      setState({ data: results, isLoading: false, isError: false });
    };
    if (router.pathname === '/dashboard-template') {
      setState({
        data: [
          {
            isLoading: false,
            isError: false,
            error: '',
            measureData: {
              id: 11284,
              // assetId: 257,
              measurementId: 18578,
              tag: 'Brenes\\MAINPANEL\\PhaseBVoltage',
              // metricId: null,
              description: 'PHASEB:VOLTAGE',
              meterFactor: null,
              typeId: 23,
              dataTypeId: 2,
              valueTypeId: 1,
              unitOfMeasureId: 70,
              locationId: null,
              datasourceId: null,
            },
            unitOfMeasures: [
              {
                id: 115,
                name: '%',
              },
              {
                id: 69,
                name: 'mV',
              },
              {
                id: 70,
                name: 'volts',
              },
            ],
            tsData: {
              tag: 18578,
              'gb0,gb1,val': generateRandomData(24, 1727626648247, 60000, 20, 100),
            },
          },
        ],
        isLoading: false,
        isError: false,
      });
    } else if (selectedTitles.length > 0) fetchMeasuresData();
  }, [
    aggBy,
    customerId,
    dbMeasureIdToAssetIdMap,
    dispatch,
    endDate,
    fetchMeasureData,
    fetchTimeseriesData,
    fetchUnitOfMeasure,
    samplePeriod,
    selectedTitles,
    startDate,
    timeRange,
    assetTz,
    isOverrideAssetTz,
    assetTzOverrideValue,
  ]);
  return state;
}
