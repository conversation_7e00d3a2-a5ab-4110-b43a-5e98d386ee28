import Table from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';
import TableCell from '@mui/material/TableCell';
import TableContainer from '@mui/material/TableContainer';
import TableHead from '@mui/material/TableHead';
import TableRow from '@mui/material/TableRow';
import Paper from '@mui/material/Paper';
import { FieldArrayWithId, UseFieldArrayRemove } from 'react-hook-form';
import {
  createAssetTemplateData,
  createAssetTemplateSecondStepData,
} from '~/measurements/domain/types';
import EditIcon from '@mui/icons-material/Edit';
import { Dispatch, SetStateAction } from 'react';
import { Box } from '@mui/material';
import DeleteIcon from '@mui/icons-material/Delete';

type AssetTemplateTableProps = {
  setCurrentMeasureIndex: Dispatch<SetStateAction<number>>;
  fields:
    | FieldArrayWithId<createAssetTemplateData, 'measurements', 'id'>[]
    | FieldArrayWithId<createAssetTemplateSecondStepData, 'measurements', 'id'>[];

  valueTypeOptions: {
    id: string;
    label: string;
  }[];
  datasourceOptions: {
    id: string;
    label: string;
  }[];
  locationsListOption: {
    id: string;
    label: string;
  }[];
  dataTypesListOptions: {
    id: string;
    label: string;
  }[];
  measurementTypeListOptions: {
    id: string;
    label: string;
  }[];
  assetTypeMetricsListOptions: {
    id: string;
    label: string;
  }[];
  remove: UseFieldArrayRemove;
};
const AssetTemplateTable = ({
  fields,
  valueTypeOptions,
  datasourceOptions,
  locationsListOption,
  measurementTypeListOptions,
  dataTypesListOptions,
  setCurrentMeasureIndex,
  assetTypeMetricsListOptions,
  remove,
}: AssetTemplateTableProps) => {
  return (
    <>
      <TableContainer component={Paper}>
        <Table sx={{ minWidth: 650 }} aria-label="simple table">
          <TableHead>
            <TableRow>
              <TableCell align="center">TypeId </TableCell>
              <TableCell align="center">Data Type</TableCell>
              <TableCell align="center">Value Type</TableCell>
              <TableCell align="center">Metric</TableCell>
              <TableCell align="center">Location</TableCell>
              <TableCell align="center">Description</TableCell>
              <TableCell align="center">Meter Factor</TableCell>
              <TableCell align="center">Actions</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {fields.map((row, i) => (
              <TableRow key={row.id} sx={{ '&:last-child td, &:last-child th': { border: 0 } }}>
                <TableCell component="th" scope="row" align="center">
                  {measurementTypeListOptions.find(
                    (measure) => measure.id === row.metric_id?.toString(),
                  )?.label ?? 'N/A'}
                </TableCell>
                <TableCell component="th" scope="row" align="center">
                  {dataTypesListOptions.find(
                    (dataType) => dataType.id === row.data_type_id?.toString(),
                  )?.label ?? 'N/A'}
                </TableCell>
                <TableCell component="th" scope="row" align="center">
                  {valueTypeOptions.find(
                    (valueType) => valueType.id === row.value_type_id?.toString(),
                  )?.label ?? 'N/A'}
                </TableCell>
                <TableCell component="th" scope="row" align="center">
                  {
                    assetTypeMetricsListOptions.find(
                      (asset) => asset.id === row.metric_id?.toString(),
                    )?.label
                  }
                </TableCell>
                <TableCell component="th" scope="row" align="center">
                  {locationsListOption.find(
                    (location) => location.id === row.location_id?.toString(),
                  )?.label ?? 'N/A'}
                </TableCell>
                <TableCell component="th" scope="row" align="center">
                  {row.description ?? 'N/A'}
                </TableCell>
                <TableCell component="th" scope="row" align="center">
                  {row.meter_factor ?? 'N/A'}
                </TableCell>
                <TableCell component="th" scope="row" align="center">
                  <Box display={'flex'} justifyContent={'space-around'}>
                    <EditIcon onClick={() => setCurrentMeasureIndex(i)} />
                    <DeleteIcon color="error" onClick={() => remove(i)} />
                  </Box>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>
    </>
  );
};

export default AssetTemplateTable;
