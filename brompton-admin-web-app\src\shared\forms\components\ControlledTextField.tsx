import { TextField, TextFieldProps } from '@mui/material';
import { Control, Controller, FieldPath, FieldValues } from 'react-hook-form';

type ControlledTextFieldProps<
  TFieldValues extends FieldValues,
  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,
> = {
  control: Control<TFieldValues>;
  loading: boolean;
  fieldName: TName;
  label: string;
} & TextFieldProps;

export const ControlledTextField = <TFieldValues extends FieldValues>(
  props: ControlledTextFieldProps<TFieldValues>,
): JSX.Element => {
  const { control, loading, fieldName, ...rest } = props;
  return (
    <Controller
      name={fieldName}
      control={control}
      render={({ field: { onChange, onBlur, value }, fieldState }) => (
        <TextField
          error={!!fieldState.error}
          helperText={fieldState.error?.message}
          onChange={onChange}
          onBlur={onBlur}
          value={value}
          variant="outlined"
          margin="normal"
          disabled={loading}
          fullWidth
          InputLabelProps={{ shrink: true }}
          {...rest}
        />
      )}
    />
  );
};
