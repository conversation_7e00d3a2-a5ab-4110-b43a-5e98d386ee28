import {
  ChevronLeft as ChevronLeftIcon,
  ChevronRight as ChevronRightIcon,
} from '@mui/icons-material';
import AddIcon from '@mui/icons-material/Add';
import SettingsOutlinedIcon from '@mui/icons-material/SettingsOutlined';
import {
  Autocomplete,
  Box,
  Button,
  Divider,
  FormLabel,
  IconButton,
  Paper,
  Stack,
  TextField,
  Tooltip,
} from '@mui/material';
import { useRouter } from 'next/router';
import { useEffect, useMemo, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import ErrorBoundary from '~/errors/ErrorBoundry';
import {
  useGetAllAssetTemplatedByAssetTypeQuery,
  useGetAllBackOfficeAssetTypesMetricsQuery,
  useGetAllBackOfficeAssetTypesQuery,
} from '~/redux/api/assetsApi';
import {
  useGetDashboardTemplateDetailsQuery,
  useGetDashboardTemplatesQuery,
} from '~/redux/api/dashboardTemplate';
import {
  useGetAllDatasourcesQuery,
  useGetAllDataTypesQuery,
  useGetAllMeasureTypesQuery,
} from '~/redux/api/measuresApi';
import {
  getCurrentAssetTemplate,
  getCurrentAssetType,
  getDashboardTemplateId,
  getDashboardTemplateName,
} from '~/redux/selectors/dashboardSelectors';
import { getWidgets } from '~/redux/selectors/widgetSelectors';
import { dashboardSlice } from '~/redux/slices/dashboardSlice';
import { AlertSnackbar } from '~/shared/snackbars/components/AlertSnackbar';
import { useSnackbar } from '~/shared/snackbars/snackbar-hooks';
import { AssetTypeOption } from '~/types/asset';
import { DashboardState } from '~/types/dashboard';
import { assetTypePathMapperFilterTemplates } from '~/utils/mappers/asset-type-mapper';
import Loader from '../common/Loader';
import DashboardWidgetsIcons from '../dashboard/DashboardWidgetsIcons';
import { TopPanel } from '../dashboard/TopPanel';
import MetricsList from './MetricsList';
import WidgetLayoutDrawer from './WidgetLayoutDrawer';
const DashboadTemplates = () => {
  const [removedIds, setRemovedIds] = useState<number[]>([]);
  const router = useRouter();
  const dispatch = useDispatch();
  const [isLeftPanelOpen, setIsLeftPanelOpen] = useState<boolean>(true);
  const [snackbarState, showSuccessAlert, showErrorAlert] = useSnackbar();
  const {
    setAssetType,
    setAssetTemplate,
    setTemplateId,
    setTemplateName,
    setWidget,
    setWidgetsLayout,
    removeWidgetTitles,
    createNewDashboardTemplate,
    setDesktopMobileMode,
    setResponsiveLayouts,
  } = dashboardSlice.actions;
  const [assetTypesWithPath, setAssetTypesWithPath] = useState<AssetTypeOption[]>([]);
  const [assetTypesMetricsMap, setAssetTypesMetricsMap] = useState<Record<number, string>>({});
  const assetType = useSelector(getCurrentAssetType);
  const assetTemplate = useSelector(getCurrentAssetTemplate);
  const dashboardTemplateId = useSelector(getDashboardTemplateId);
  const dashboardTemplateName = useSelector(getDashboardTemplateName);
  const widgets = useSelector(getWidgets);

  const { data: dashboardTemplates, isFetching: fetchingTemplates } = useGetDashboardTemplatesQuery(
    { assetTypeId: assetType },
    { skip: !assetType },
  );
  const { data: datasourceList } = useGetAllDatasourcesQuery({});
  const { data: dataTypeList } = useGetAllDataTypesQuery();
  const { data: measurementTypeList } = useGetAllMeasureTypesQuery();
  const {
    data: assetTypeListData,
    isLoading: isAssetTypeLoading,
    isSuccess: isSuccessfullBackOffieAssetTypes,
  } = useGetAllBackOfficeAssetTypesQuery();
  const { data: assetTemplates, isLoading } = useGetAllAssetTemplatedByAssetTypeQuery(
    {
      assetTypeId: assetType !== undefined && assetType !== null ? assetType?.toString() : '',
    },
    {
      refetchOnMountOrArgChange: true,
      skip: assetType === null || assetType === undefined,
    },
  );
  const heightLeft = useMemo(() => {
    return 'calc(100vh - 25px)';
  }, []);
  const { data: assetTypeMetrics } = useGetAllBackOfficeAssetTypesMetricsQuery(
    {
      assetId: assetType?.toString(),
    },
    {
      refetchOnMountOrArgChange: true,
      skip: assetType === null || assetType === undefined,
    },
  );
  const {
    data: templateData,
    isSuccess: isTemplateSuccess,
    isFetching: fetchingTemplateData,
  } = useGetDashboardTemplateDetailsQuery(dashboardTemplateId, {
    skip:
      dashboardTemplateId <= 0 ||
      dashboardTemplateId === undefined ||
      dashboardTemplateId === null ||
      dashboardTemplates?.items?.find((template) => template.id === dashboardTemplateId) ===
        undefined,
    refetchOnMountOrArgChange: true,
  });
  useEffect(() => {
    if (isTemplateSuccess && templateData) {
      if (templateData.data) {
        const templateDetails = JSON.parse(templateData.data) as {
          widget: DashboardState['widget'];
          topPanel: DashboardState['template']['topPanel'];
          chart: DashboardState['template']['chart'];
          desktopMobile: DashboardState['desktopMobile'];
          responsiveLayouts: DashboardState['responsiveLayouts'];
        };
        if (templateDetails) {
          const { desktopMobile, responsiveLayouts } = templateDetails;
          templateDetails.widget.widgets.map((widget) => {
            if (widget.type === 'chart') {
              widget.settings.settings.dashboardOrTemplate =
                widget.settings.settings.dashboardOrTemplate ?? 'template';
              widget.settings.settings.assetOrAssetType =
                widget.settings.settings.assetOrAssetType ?? null;
              return widget;
            }
            widget.settings.dashboardOrTemplate = widget.settings.dashboardOrTemplate ?? 'template';
            widget.settings.assetOrAssetType = widget.settings.assetOrAssetType ?? null;
            return widget;
          });
          dispatch(dashboardSlice.actions.setDesktopMobileMode(desktopMobile ?? 0));
          dispatch(
            dashboardSlice.actions.setResponsiveLayouts({
              desktop: {
                widgetLayout: responsiveLayouts?.desktop?.widgetLayout ?? [],
                widgets: responsiveLayouts?.desktop?.widgets ?? [],
              },
              mobile: {
                widgetLayout: responsiveLayouts?.mobile?.widgetLayout ?? [],
                widgets: responsiveLayouts?.mobile?.widgets ?? [],
              },
            }),
          );
          dispatch(
            setWidget({
              widgets: templateDetails.widget.widgets,
              deleteWidgets: [],
              widgetLayout: templateDetails.widget.widgetLayout,
              lastWidgetId: templateDetails.widget.lastWidgetId,
            }),
          );
          dispatch(setWidgetsLayout(templateDetails.widget.widgetLayout));
          dispatch(dashboardSlice.actions.setTemplateAssetTz(templateDetails.topPanel.assetTz));
          dispatch(
            dashboardSlice.actions.setTemplateSamplePeriod(templateDetails.topPanel.samplePeriod),
          );
          dispatch(
            dashboardSlice.actions.setTemplateRefreshInterval(
              templateDetails.topPanel.refreshInterval,
            ),
          );
          dispatch(
            dashboardSlice.actions.setTemplateChartStartDate(
              new Date(templateDetails.chart.startDate),
            ),
          );
          dispatch(
            dashboardSlice.actions.setTemplateChartEndDate(new Date(templateDetails.chart.endDate)),
          );
        }
      }
      setRemovedIds([]);
      setAssetTypesMetricsMap({});
      dispatch(setAssetType(templateData.asset_template.assetType.id));
      dispatch(setAssetTemplate(templateData.asset_template.id));
    }
  }, [isTemplateSuccess, templateData, setRemovedIds]);

  useEffect(() => {
    if (fetchingTemplateData) {
      setRemovedIds([]);
    }
    if (templateData && isTemplateSuccess) {
      if (templateData.data) {
        const templateDetails = JSON.parse(templateData.data) as {
          widget: DashboardState['widget'];
          topPanel: DashboardState['template']['topPanel'];
          chart: DashboardState['template']['chart'];
        };
        if (templateDetails) {
          const existingMetrics = templateData.asset_template.measurements.map(
            (measure) => measure.metric.id,
          );
          const metricIds: number[] = [];
          widgets.forEach((widget) => {
            if (widget.type === 'chart') {
              if (widget.settings.chartType === 'scatter' || widget.settings.chartType === 'bar') {
                widget.settings.settings.selectedTitles.map((title) => {
                  if (!metricIds.includes(Number(title))) {
                    metricIds.push(Number(title));
                  }
                });
              }
              if (
                widget.settings.chartType === 'heatmap' ||
                widget.settings.chartType === 'indicator' ||
                widget.settings.chartType === 'bullet'
              ) {
                const widgetMetrics = widget.settings.settings.selectedDbMeasureId;
                if (!metricIds.includes(Number(widgetMetrics))) {
                  metricIds.push(Number(widgetMetrics));
                }
              }
            }
            if (widget.type === 'table') {
              widget.settings.selectedTitles.map((title) => {
                if (!metricIds.includes(Number(title))) {
                  metricIds.push(Number(title));
                }
              });
            }
            if (widget.type === 'stats') {
              const widgetMetrics = widget.settings.selectedDbMeasureId;
              if (!metricIds.includes(Number(widgetMetrics))) {
                metricIds.push(Number(widgetMetrics));
              }
            }
          });
          // const deleted = templateData.asset_template.measurements
          //   .filter((measurement) => !metricIds.includes(Number(measurement.metric.id)))
          //   .map((measurement) => Number(measurement.metric.id));
          const difference = metricIds.filter((metric) => !existingMetrics.includes(metric));
          setRemovedIds([...difference]);
        }
      }
    }
  }, [templateData, fetchingTemplateData, isTemplateSuccess]);

  useEffect(() => {
    if (removedIds.length > 0 && assetTypeMetrics) {
      const removedMetrics = assetTypeMetrics.items.filter((metric) =>
        removedIds.includes(metric.id),
      );
      const metricsMap = removedMetrics.reduce((acc, metric) => {
        return {
          ...acc,
          [metric.id]: metric.name,
        };
      }, {});
      setAssetTypesMetricsMap(metricsMap);
    }
  }, [assetTypeMetrics, removedIds]);

  useEffect(() => {
    if (isSuccessfullBackOffieAssetTypes && assetTypeListData) {
      setAssetTypesWithPath(
        assetTypePathMapperFilterTemplates(
          assetTypeListData.map((item) => ({
            ...item,
            name: item.name,
            id: item.id,
          })),
        ),
      );
    }
  }, [assetTypeListData, isSuccessfullBackOffieAssetTypes]);
  const toggleLeftPanel = () => {
    setIsLeftPanelOpen(!isLeftPanelOpen);
  };
  const handleChangeAssetType = (
    event: React.SyntheticEvent,
    value: {
      id: string;
      label: string;
    } | null,
  ) => {
    dispatch(setAssetType(Number(value?.id ?? 0)));
    dispatch(removeWidgetTitles());
  };
  const handleChangeAssetTemplate = (
    event: React.SyntheticEvent,
    value: {
      id: string;
      label: string;
    } | null,
  ) => {
    dispatch(setAssetTemplate(Number(value?.id ?? 0)));
    dispatch(removeWidgetTitles());
  };
  return (
    <>
      <AlertSnackbar {...snackbarState} />
      <Stack
        gap={0.5}
        width="100%"
        display="flex"
        sx={{
          pt: 1,
          pb: 0,
          pl: 1,
          pr: 1,
          flexGrow: 1,
          flexDirection: 'row',
        }}
        overflow={'auto'}
      >
        <IconButton
          sx={{
            height: '25px',
            width: '25px',
            position: 'absolute',
            left: isLeftPanelOpen ? 490 : 100,
            transition: 'all 0.2s',
            top: '50%',
            backgroundColor: '#fff',
            zIndex: 1200,
            background: (theme) => (!isLeftPanelOpen ? theme.palette.primary.main : undefined),
            borderColor: (theme) => (!isLeftPanelOpen ? theme.palette.primary.main : undefined),
            boxShadow: '1px 1px 3px 1px #A39B9B40',
            '@media (max-width: 600px)': {
              left: isLeftPanelOpen ? '100%' : 0,
              display: 'none',
            },
          }}
          color="primary"
          aria-label="toggle left panel"
          onClick={toggleLeftPanel}
        >
          {isLeftPanelOpen ? (
            <ChevronLeftIcon color="primary" />
          ) : (
            <ChevronRightIcon
              sx={{
                color: (theme) => theme.palette.background.default,
                '&:hover': {
                  color: (theme) => theme.palette.primary.main,
                  borderColor: (theme) => theme.palette.primary.main,
                },
              }}
            />
          )}
        </IconButton>
        <Paper
          elevation={3}
          sx={{ boxShadow: 0, pt: 1, maxWidth: 380, display: isLeftPanelOpen ? 'block' : 'none' }}
        >
          <ErrorBoundary>
            <Box
              alignSelf="flex-start"
              width={'auto'}
              height={'100%'}
              maxHeight={heightLeft}
              overflow="auto"
              sx={{
                transition: 'all .2s',
                visibility: 'visible',
                '&::-webkit-scrollbar': {
                  background: 'trasnparent',
                  width: 0,
                },
                '@media (max-width: 600px)': {
                  display: 'none',
                },
              }}
            >
              <Box sx={{ display: 'flex', mt: 2 }}>
                <Autocomplete
                  disablePortal
                  id="combo-box-demo"
                  options={assetTypesWithPath.map((item) => {
                    return {
                      id: item.value.toString(),
                      label: item.label,
                    };
                  })}
                  value={
                    assetTypesWithPath
                      .map((item) => {
                        return {
                          id: item.value.toString(),
                          label: item.label,
                        };
                      })
                      .find((item) => item.id === assetType?.toString()) ?? null
                  }
                  loading={isAssetTypeLoading}
                  sx={{ width: 380 }}
                  size="small"
                  onChange={(event, value) => handleChangeAssetType(event, value)}
                  renderInput={(params) => <TextField {...params} label="Asset Type" />}
                />
              </Box>
              <Box sx={{ display: 'flex', mt: 2 }}>
                <Autocomplete
                  disablePortal
                  id="combo-box-demo"
                  disabled={isLoading}
                  size="small"
                  options={
                    assetTemplates?.items?.map((item) => {
                      return {
                        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
                        //@ts-ignore
                        id: item.id.toString(),
                        label: item.model_number + ' - ' + item.manufacturer,
                      };
                    }) ?? []
                  }
                  value={
                    assetTemplates?.items
                      .map((item) => {
                        return {
                          // eslint-disable-next-line @typescript-eslint/ban-ts-comment
                          //@ts-ignore
                          id: item.id.toString(),
                          label: item.model_number + ' - ' + item.manufacturer,
                        };
                      })
                      .find((item) => item.id === assetTemplate?.toString()) ?? null
                  }
                  loading={isAssetTypeLoading}
                  sx={{ width: 380 }}
                  onChange={(event, value) => handleChangeAssetTemplate(event, value)}
                  renderInput={(params) => <TextField {...params} label="Asset Template" />}
                />
              </Box>
              <Box width={'100%'} pl={2} display={'flex'} mt={2} gap={1} alignItems={'center'}>
                <FormLabel>Dashboard Template </FormLabel>
                <Tooltip title="Manage Dashboard Template" placement="bottom">
                  <SettingsOutlinedIcon
                    color="primary"
                    sx={{ cursor: 'pointer', fontSize: '1rem' }}
                    onClick={() => router.push('/dashboard-template/list')}
                  />
                </Tooltip>
              </Box>
              <Box sx={{ display: 'flex', mt: 1 }}>
                <Autocomplete
                  disablePortal
                  id="combo-box-demo"
                  size="small"
                  options={
                    dashboardTemplates?.items?.map((item) => {
                      return {
                        id: item.id.toString(),
                        label: item.title,
                      };
                    }) ?? []
                  }
                  sx={{ width: 380 }}
                  value={
                    dashboardTemplateId > 0
                      ? { id: dashboardTemplateId.toString(), label: dashboardTemplateName }
                      : null
                  }
                  onChange={(event, value) => {
                    setRemovedIds([]);
                    dispatch(setTemplateId(Number(value?.id ?? 0)));
                    dispatch(setTemplateName(value?.label ?? ''));
                  }}
                  renderInput={(params) => <TextField {...params} label="Dashboard Template" />}
                />
                <Button
                  startIcon={
                    <Tooltip title="Add Dashboard Template" arrow>
                      <AddIcon sx={{ fontWeight: 'bold' }} fontSize="large" />
                    </Tooltip>
                  }
                  onClick={() => {
                    dispatch(setTemplateId(Number(0)));
                    dispatch(setTemplateName(''));
                    dispatch(
                      setWidget({
                        widgets: [],
                        deleteWidgets: [],
                        widgetLayout: [],
                        lastWidgetId: 0,
                      }),
                    );
                    dispatch(setWidgetsLayout([]));
                    dispatch(setAssetTemplate(0));
                    dispatch(setAssetType(0));
                    dispatch(createNewDashboardTemplate());
                    dispatch(setDesktopMobileMode(0));
                    dispatch(
                      setWidget({
                        widgets: [],
                        deleteWidgets: [],
                        widgetLayout: [],
                        lastWidgetId: 0,
                      }),
                    );
                    dispatch(setWidgetsLayout([]));
                    dispatch(
                      setResponsiveLayouts({
                        desktop: { widgetLayout: [], widgets: [] },
                        mobile: { widgetLayout: [], widgets: [] },
                      }),
                    );
                    setRemovedIds([]);
                  }}
                  variant="contained"
                  sx={{
                    borderRadius: 2,
                    width: 'max-content',
                    '& .MuiButton-startIcon': {
                      mr: 0,
                      '& .MuiSvgIcon-root': {
                        fontSize: '25px',
                      },
                    },
                  }}
                />
              </Box>
              <Box sx={{ height: '75%', mt: 2, overflowY: 'auto' }}>
                {fetchingTemplates && dashboardTemplates === undefined ? (
                  <Loader />
                ) : (
                  <MetricsList
                    assetTemplate={
                      assetTemplates?.items?.find(
                        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
                        // @ts-ignore
                        (item) => item.id.toString() === assetTemplate?.toString(),
                      ) ?? undefined
                    }
                    assetTypeMetrics={assetTypeMetrics?.items ?? []}
                    dataSourceList={datasourceList?.items ?? []}
                    dataTypeList={dataTypeList ?? []}
                    measurementTypeList={measurementTypeList ?? []}
                  />
                )}
              </Box>
            </Box>
          </ErrorBoundary>
        </Paper>
        {isLeftPanelOpen ? (
          <Divider orientation="vertical" variant="middle" sx={{ height: 'auto' }} />
        ) : null}
        <Box
          flexGrow={1}
          width={'25%'}
          height={heightLeft}
          maxHeight={heightLeft}
          overflow="auto"
          sx={{
            '@media (max-width: 600px)': {
              pl: 0,
            },
          }}
        >
          <ErrorBoundary>
            <Paper
              elevation={0}
              sx={{
                boxShadow: 0,
                '@media (max-width: 600px)': {
                  display: 'none',
                },
              }}
            >
              <Box
                display="flex"
                sx={{
                  flexGrow: 1,
                  overflow: 'auto',
                  display: 'flex',
                  backgroundColor: '#f6f6f6',
                  height: (theme) => theme.spacing(8),
                  borderRadius: 2,
                  '&::-webkit-scrollbar': {
                    background: 'trasnparent',
                    width: 0,
                  },
                }}
                pl={0}
                p={1.5}
                gap={1}
              >
                <TopPanel
                  dashboardList={{
                    items: [],
                    total: 0,
                  }}
                  isLoadingDashboards={false}
                  isCustomerListSuccess={true}
                  isCustomerListLoading={false}
                  customerList={[]}
                  isSamplePeriod={true}
                  isRefreshInterval={true}
                />
              </Box>
            </Paper>
          </ErrorBoundary>
          <ErrorBoundary>
            <Box height={'100%'} sx={{ mt: 1 }}>
              {fetchingTemplateData ? <Loader /> : <WidgetLayoutDrawer />}
            </Box>
          </ErrorBoundary>
        </Box>
        <DashboardWidgetsIcons />
      </Stack>
      {/* <CustomDialog
        open={removedIds.length > 0 && Object.keys(assetTypesMetricsMap).length > 0}
        content={
          <>
            {assetTypesMetricsMap && Object.keys(assetTypesMetricsMap).length > 0 && (
              <Box>
                {Object.keys(assetTypesMetricsMap).map((key) => {
                  return <Typography key={key}>{assetTypesMetricsMap[Number(key)]}</Typography>;
                })}
              </Box>
            )}
          </>
        }
        dialogActions={
          <Button variant="contained" color="primary" onClick={onOkPerforms}>
            Ok
          </Button>
        }
        title={<>Metrics are removed from Asset template</>}
        onClose={() => {
          // setRemovedIds([]);
        }}
      /> */}
    </>
  );
};
export default DashboadTemplates;
