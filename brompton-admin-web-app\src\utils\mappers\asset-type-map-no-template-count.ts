import { AssetType, AssetTypeOption } from '~/types/asset';

const buildPath = (assetTypesMap: Map<number, AssetType>, assetType: AssetType): string => {
  if (assetType.parentType === null) {
    return assetType.name;
  } else {
    const parentAssetType = assetTypesMap.get(assetType.parentType);

    if (!parentAssetType) {
      throw new Error('Asset parent type not found');
    }

    return `${buildPath(assetTypesMap, parentAssetType)} > ${assetType.name}`;
  }
};

export const assetTypeNoCountsPathMapper = (assetTypes: AssetType[]): AssetTypeOption[] => {
  const assetTypesMap: Map<number, AssetType> = new Map();

  assetTypes.forEach((assetType) => assetTypesMap.set(assetType.id, assetType));

  return assetTypes.map((assetType) => ({
    value: assetType.id,
    label: buildPath(assetTypesMap, assetType),
  }));
};
