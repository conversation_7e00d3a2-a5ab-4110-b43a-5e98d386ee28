import { test, expect } from '@playwright/test';

test('drag and drop elements and connect two ports on canvas', async ({ page }) => {
  await page.goto('https://test.pivotol.ai/login');

  // Login
  await page.getByLabel('Username *').fill('test');
  await page.getByLabel('Password *').fill('Br0mpt0n!0T');
  await page.getByRole('button', { name: 'Log in' }).click();
  await page.waitForLoadState('networkidle');

  // Navigate to Diagram Builder
  await page.getByRole('button', { name: 'D', exact: true }).click();
  await page.getByText('Diagram Builder').click();
  await page.waitForLoadState('networkidle');

  const canvasLocator = page
    .locator('div[class*="joint-paper"], div[class*="canvas"], div[class*="grid"]')
    .first();
  await canvasLocator.waitFor({ timeout: 10000 });

  const canvasBox = await canvasLocator.boundingBox();
  if (!canvasBox) throw new Error('Canvas bounding box not found');

  const elements = [
    { alt: 'Compressor', offsetX: 100, offsetY: 100 },
    { alt: 'Gate Valve', offsetX: 250, offsetY: 100 },
    { alt: 'Hydraulic Motor Valve White', offsetX: 100, offsetY: 200 },
    { alt: 'Outset Field Bordered', offsetX: 250, offsetY: 200 },
    { alt: 'Pump', offsetX: 175, offsetY: 300 },
  ];

  for (const el of elements) {
    const wrapperLocator = page.locator(`img[alt="${el.alt}"]`).locator('..'); // go to parent div
    await wrapperLocator.scrollIntoViewIfNeeded();

    try {
      await expect(wrapperLocator).toBeVisible({ timeout: 5000 });

      const box = await wrapperLocator.boundingBox();
      if (!box) {
        console.warn(`⚠️ No bounding box for ${el.alt}`);
        continue;
      }

      const startX = box.x + box.width / 2;
      const startY = box.y + box.height / 2;
      const endX = canvasBox.x + el.offsetX;
      const endY = canvasBox.y + el.offsetY;

      await page.mouse.move(startX, startY);
      await page.mouse.down();
      await page.mouse.move(endX, endY, { steps: 20 });
      await page.mouse.up();

      console.log(`✅ "${el.alt}" dropped to canvas`);

      await page.waitForTimeout(1000);
    } catch (error) {
      console.error(`❌ Failed to drag "${el.alt}":`, error);
    }
  }

  // 👉 Now connect ports via drag
  const port1 = page.locator('circle#v-6306'); // Source port
  const port2 = page.locator('circle#v-3668'); // Target port

  await expect(port1).toBeVisible({ timeout: 3000 });
  await expect(port2).toBeVisible({ timeout: 3000 });

  const box1 = await port1.boundingBox();
  const box2 = await port2.boundingBox();

  if (box1 && box2) {
    const port1CenterX = box1.x + box1.width / 2;
    const port1CenterY = box1.y + box1.height / 2;
    const port2CenterX = box2.x + box2.width / 2;
    const port2CenterY = box2.y + box2.height / 2;

    await page.mouse.move(port1CenterX, port1CenterY);
    await page.mouse.down();
    await page.mouse.move(port2CenterX, port2CenterY, { steps: 10 });
    await page.mouse.up();

    console.log(`🔗 Connected ports v-6306 → v-3668`);
  } else {
    console.warn('❌ One or both ports not found on canvas');
  }

  await page.waitForTimeout(3000);
  await page.close();
});
