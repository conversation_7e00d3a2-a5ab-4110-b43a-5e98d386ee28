import { assetSchema } from './asset';

describe('Asset validation', () => {
  test('full asset should be valid', () => {
    const result = assetSchema.validateSync({
      parentIds: [],
      tag: 'Engine',
      latitude: 32.455,
      longitude: -32.45,
      description: 'Just an engine',
      assetTypeId: 43,
      isCustPrimary: true,
    });

    expect(result.tag).toBe('Engine');
  });

  test('asset missing tag should throw an error', () => {
    expect(() =>
      assetSchema.validateSync({
        parentIds: [],
        latitude: 32.455,
        longitude: -32.45,
        description: 'Just an engine',
        assetTypeId: 43,
        isCustPrimary: true,
      }),
    ).toThrowError('Please enter a tag');
  });

  test('asset with latitude but no longitude should throw an error', () => {
    expect(() =>
      assetSchema.validateSync({
        parentIds: [],
        tag: 'Engine',
        latitude: 32.455,
        description: 'Just an engine',
        assetTypeId: 43,
        isCustPrimary: true,
      }),
    ).toThrowError('Please enter longitude');
  });

  test('asset with longitude but no latitude should throw an error', () => {
    expect(() =>
      assetSchema.validateSync({
        parentIds: [],
        tag: 'Engine',
        longitude: -32.455,
        description: 'Just an engine',
        assetTypeId: 43,
        isCustPrimary: true,
      }),
    ).toThrowError('Please enter latitude');
  });

  test('full asset with no position should be valid', () => {
    const result = assetSchema.validateSync({
      parentIds: [],
      tag: 'Engine',
      description: 'Just an engine',
      assetTypeId: 43,
      isCustPrimary: true,
    });

    expect(result.tag).toBe('Engine');
  });

  test('full asset with null position should be valid', () => {
    const result = assetSchema.validateSync({
      parentIds: [],
      tag: 'Engine',
      latitude: null,
      longitude: null,
      description: 'Just an engine',
      assetTypeId: 43,
      isCustPrimary: true,
    });

    expect(result.tag).toBe('Engine');
  });
});
