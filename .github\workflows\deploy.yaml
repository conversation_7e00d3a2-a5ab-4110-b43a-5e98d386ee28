name: Deployment

on:
  workflow_dispatch:
    inputs:
      environment:
        description: "Deployment Environment"
        required: true
        default: "dev"
        type: choice
        options:
          - dev
          - prod
      release-tag:
        description: "Frontend Release Tag to deploy - Required for prod"
        required: false
        default: ""

# Set ENV variables
env:
  AWS_REGION: "us-east-1" # Set your AWS region
  ECR_REGISTRY: "067172429169.dkr.ecr.us-east-1.amazonaws.com" 

permissions:
    id-token: write   # This is required for requesting the JWT
    contents: read    # This is required for actions/checkout

jobs:
  build:
    name: Frontend Build
    runs-on: on-prem
    steps:
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 18.17.0
  
      - name: Checkout frontend repository
        uses: actions/checkout@v2
        with:
          repository: bromptonenergy/BromptonUI
          ref: ${{ github.event.inputs.release-tag || github.sha }}
          token: ${{ secrets.GHA_TOKEN }}
          fetch-depth: 0

      - name: react-date-time-range-picker Build
        run: |
          cd react-date-time-range-picker
          npm install --global yarn --loglevel verbose
          echo "Running YARN Install....."
          yarn install
          yarn run build
          ls -al
          pwd

      - name: Change node version
        uses: actions/setup-node@v4
        with:
          node-version: 16.15.0
  
      - name: brompton-admin-web-app Build
        run: |
          cd brompton-admin-web-app
          npm install --global yarn --loglevel verbose
          echo "Running YARN Install....."
          yarn install
          ls -al
          pwd

      - name: brompton-admin-web-app Yarn Format
        run: |
          cd brompton-admin-web-app
          echo "Running RUN Format for ${{ github.event.inputs.environment }}"
          yarn run format
          echo "Running YARN RUN BUILD........"
          if [ "${{ github.event.inputs.environment }}" = "prod" ]; then
            echo "Building for production..."
            yarn run build:prod
          elif [ "${{ github.event.inputs.environment }}" = "dev" ]; then
            echo "Building for development..."
            yarn run build:dev
          else
            echo "Unknown environment: ${{ github.event.inputs.environment }}"
            exit 1
          fi

      - name: Create Dockerfile
        run: |
          cat << 'EOF' > Dockerfile
          FROM node:16.15.0-slim

          # Set the working directory in the container
          WORKDIR /adminapp

          ADD ../brompton-admin-web-app/ .

          # Make port 8080 available to the world outside this container
          EXPOSE 8080

          # Run app.py when the container launches
          CMD ["yarn", "run", "start"]
          EOF

      - name: Print inputs
        run: |
          ls -al
          cat Dockerfile
          echo "Image Tag:${{ github.event.inputs.release-tag || github.sha }}"
          echo "Environment: ${{ github.event.inputs.environment }}"

      - name: configure aws credentials
        uses: aws-actions/configure-aws-credentials@v1.7.0
        with:
          role-to-assume: ${{ secrets.PIPELINE_ROLE }} 
          role-session-name: GitHub_to_AWS_via_FederatedOIDC
          aws-region: ${{ env.AWS_REGION }}
  
      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v1

      - name: Validate release tag for prod
        run: |
          if [ "${{ github.event.inputs.environment }}" == "prod" ] && [ -z "${{ github.event.inputs.release-tag || github.sha }}" ]; then
            echo "Release tag is required for prod deployment!"
            exit 1
          fi  

      - name: Set ECR Repo Environment Variable
        run: |
          ENVIRONMENT=${{ github.event.inputs.environment }}
          if [ "$ENVIRONMENT" == "dev" ]; then
            echo "ECR_REPO_NAME=brompton-energy/frontend-dev" >> $GITHUB_ENV
            echo "CLUSTER_NAME=k3s" >> $GITHUB_ENV
          elif [ "$ENVIRONMENT" == "prod" ]; then
            echo "ECR_REPO_NAME=brompton-energy/frontend-prod" >> $GITHUB_ENV
            echo "CLUSTER_NAME=brompton-energy-eks-prod" >> $GITHUB_ENV
          fi

      - name: Build, tag, and push image to Amazon ECR
        run: |
          IMAGE_TAG=${{ github.event.inputs.release-tag || github.sha }}
          echo "IMAGE_TAG: $IMAGE_TAG"
          echo "DOCKERFILE=Dockerfile"
          echo "DOCKER FILE : $DOCKERFILE"
          pwd
          ls -al
          echo $ECR_REPO_NAME
          echo $CLUSTER_NAME
          docker build -t $ECR_REPO_NAME:$IMAGE_TAG -f Dockerfile .
          docker tag $ECR_REPO_NAME:$IMAGE_TAG $ECR_REGISTRY/$ECR_REPO_NAME:$IMAGE_TAG
          docker push $ECR_REGISTRY/$ECR_REPO_NAME:$IMAGE_TAG

  deploy:
    name: Deploy on Kubernetes
    needs: build
    runs-on: on-prem
    environment: ${{ github.event.inputs.environment }}
    steps:
      # - name: configure aws credentials
      #   uses: aws-actions/configure-aws-credentials@v1.7.0
      #   with:
      #     role-to-assume: arn:aws:iam::067172429169:role/github-actions-eks-ecr 
      #     role-session-name: GitHub_to_AWS_via_FederatedOIDC
      #     aws-region: ${{ env.AWS_REGION }}

      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v1

      - name: Checkout repository for Kubernetes manifests
        uses: actions/checkout@v2
        with:
          fetch-depth: 1

      - name: Set environment variables
        run: |
          ENVIRONMENT="${{ github.event.inputs.environment }}"
          echo "ENVIRONMENT=$ENVIRONMENT" >> $GITHUB_ENV
          echo "Environment set to: $ENVIRONMENT"
          
          IMAGE_TAG="${{ github.event.inputs.release-tag || github.sha }}"
          echo "IMAGE_TAG=$IMAGE_TAG" >> $GITHUB_ENV
          echo "Image tag set to: $IMAGE_TAG"
          
          if [ "$ENVIRONMENT" == "dev" ]; then
            echo "ECR_REPO_NAME=brompton-energy/frontend-dev" >> $GITHUB_ENV
            echo "CLUSTER_NAME=k3s" >> $GITHUB_ENV
          elif [ "$ENVIRONMENT" == "prod" ]; then
            echo "ECR_REPO_NAME=brompton-energy/frontend-prod" >> $GITHUB_ENV
            echo "CLUSTER_NAME=brompton-energy-eks-prod" >> $GITHUB_ENV
          else
            echo "Error: Unknown environment '$ENVIRONMENT'"
            exit 1
          fi
          
          # Set full image URI
          echo "IMAGE_URI=${{ env.ECR_REGISTRY }}/$ECR_REPO_NAME:$IMAGE_TAG" >> $GITHUB_ENV
          
          echo "ECR repo name set to: $ECR_REPO_NAME"
          echo "EKS cluster name set to: $CLUSTER_NAME"
          echo "Full image URI: ${{ env.ECR_REGISTRY }}/$ECR_REPO_NAME:$IMAGE_TAG"
      
      - name: Debug environment variables
        run: |
          echo "Checking environment variables:"
          echo "ENVIRONMENT: $ENVIRONMENT"
          echo "IMAGE_TAG: $IMAGE_TAG"
          echo "ECR_REPO_NAME: $ECR_REPO_NAME"
          echo "CLUSTER_NAME: $CLUSTER_NAME"
          echo "IMAGE_URI: $IMAGE_URI"
          
          # Verify IMAGE_URI is correctly formatted
          if [[ "$IMAGE_URI" != *"$ECR_REPO_NAME"* ]]; then
            echo "ERROR: IMAGE_URI does not contain repository name!"
            echo "Expected format: [registry]/[repo-name]:[tag]"
            echo "Fixing IMAGE_URI..."
            IMAGE_URI="${{ env.ECR_REGISTRY }}/$ECR_REPO_NAME:$IMAGE_TAG"
            echo "IMAGE_URI=$IMAGE_URI" >> $GITHUB_ENV
            echo "Fixed IMAGE_URI: $IMAGE_URI"
          fi
          
          echo "GITHUB_ENV file contents:"
          cat $GITHUB_ENV

      - name: Setup kubectl
        if: ${{ github.event.inputs.environment != 'dev' }}
        uses: azure/setup-kubectl@v1
        with:
          version: "latest"

      - name: Update or Check Kubernetes Context
        run: |
          echo "Target environment: $ENVIRONMENT"
          echo "Cluster Name: $CLUSTER_NAME"
          
          if [[ "$ENVIRONMENT" == "prod" ]]; then
            echo "Updating kubeconfig for production..."
            aws eks update-kubeconfig --name "$CLUSTER_NAME" --region "${{ env.AWS_REGION }}"
          elif [[ "$ENVIRONMENT" == "dev" ]]; then
            echo "Checking current kubectl context for dev..."
            kubectl config current-context
          else
            echo "Unknown environment: $ENVIRONMENT"
            exit 1
          fi

      - name: Verify image exists in ECR
        run: |
          echo "Verifying image exists in ECR: $IMAGE_URI"
          aws ecr describe-images --repository-name $ECR_REPO_NAME --image-ids imageTag=$IMAGE_TAG || {
            echo "ERROR: Image $IMAGE_URI does not exist in ECR!"
            echo "Available images in repository:"
            aws ecr describe-images --repository-name $ECR_REPO_NAME --query 'imageDetails[*].imageTags' --output text
            exit 1
          }

      - name: Apply Kubernetes manifests
        run: |
          echo "Applying Kubernetes manifests with environment variable substitution..."
          
          # Install envsubst if not available
          if ! command -v envsubst &> /dev/null; then
            echo "Installing envsubst..."
            apt-get update && apt-get install -y gettext-base || sudo apt-get update && sudo apt-get install -y gettext-base
          fi
          
          # Apply ConfigMap first
          echo "Applying ConfigMap for environment: $ENVIRONMENT"
          kubectl apply -f k8s-manifest/environments/$ENVIRONMENT/configmap.yaml
          
          # Apply deployment with variable substitution
          echo "Applying deployment.yaml with IMAGE_URI: $IMAGE_URI and ENVIRONMENT: $ENVIRONMENT"
          export IMAGE_URI=$IMAGE_URI
          export ENVIRONMENT=$ENVIRONMENT
          envsubst < k8s-manifest/deployment.yaml | kubectl apply -f -
          
          # Apply service with variable substitution
          echo "Applying service.yaml with ENVIRONMENT: $ENVIRONMENT"
          envsubst < k8s-manifest/service.yaml | kubectl apply -f -
          
          # Verify the resources were created
          echo "Verifying deployment was created:"
          kubectl get deployment admin-ui-$ENVIRONMENT -n application
          
          echo "Verifying service was created:"
          kubectl get service admin-ui-$ENVIRONMENT -n application

  notification:
    name: Teams Notification
    needs: [build, deploy]
    if: always()   # Ensures job3 runs regardless of the previous job outcomes.
    runs-on: on-prem
    steps:
      - name: Checkout Code
        uses: actions/checkout@v2
        with:
          # If 'release-tag' is set, use it; otherwise use the SHA from the push event
          ref: ${{ github.event.inputs.release-tag || github.sha }}
          fetch-depth: 0
          
      - name: Set variables
        id: set_vars
        run: |
          # If branch_name is empty (not set via workflow_dispatch), derive from push context
          if [ -z "${{ github.event.inputs.branch_name }}" ]; then
            BN="${GITHUB_REF#refs/heads/}"
          else
            BN="${{ github.event.inputs.branch_name }}"
          fi

          # If release-tag is empty, fallback to the current commit SHA
          if [ -z "${{ github.event.inputs.release-tag }}" ]; then
            RT="${GITHUB_SHA}"
          else
            RT="${{ github.event.inputs.release-tag }}"
          fi

          # Grab the latest commit message and replace newlines with spaces
          CM="$(git log -1 --pretty=%B | tr '\n' ' ' | sed 's/  */ /g')"

          echo "BRANCH_NAME=$BN" >> "$GITHUB_ENV"
          echo "RELEASE_TAG=$RT" >> "$GITHUB_ENV"
          echo "COMMIT_MESSAGE=$CM" >> "$GITHUB_ENV"  

      - name: Get current time (IST)
        id: current_time
        run: echo "time=$(TZ=Asia/Kolkata date)" >> $GITHUB_OUTPUT

      - name: Send Teams Card on Success
        if: ${{ needs.build.result == 'success' && needs.deploy.result == 'success' }}
        run: |
          echo "Sending success card to Teams..."
          cat <<EOF > card_payload.json
          {
            "@type": "MessageCard",
            "@context": "http://schema.org/extensions",
            "themeColor": "00FF00",
            "summary": "Build Succeeded",
            "sections": [
              {
                "activityTitle": "✅ Build Succeeded",
                "facts": [
                  {
                    "name": "Branch Name",
                    "value": "${BRANCH_NAME}"
                  },
                  {
                    "name": "Repository",
                    "value": "${GITHUB_REPOSITORY}"
                  },
                  {
                    "name": "Tag/Commit",
                    "value": "${RELEASE_TAG}"
                  },
                  {
                    "name": "Commit Link",
                    "value": "[${GITHUB_SHA}](${GITHUB_SERVER_URL}/${GITHUB_REPOSITORY}/commit/${GITHUB_SHA})"
                  },
                  {
                    "name": "Commit Message",
                    "value": "${COMMIT_MESSAGE}"
                  },
                  {
                    "name": "Time (IST)",
                    "value": "${{ steps.current_time.outputs.time }}"
                  },
                  {
                    "name": "Author",
                    "value": "${GITHUB_ACTOR}"
                  }
                ],
                "markdown": true
              }
            ],
            "potentialAction": [
              {
                "@type": "OpenUri",
                "name": "View Workflow Run",
                "targets": [
                  {
                    "os": "default",
                    "uri": "${GITHUB_SERVER_URL}/${GITHUB_REPOSITORY}/actions/runs/${GITHUB_RUN_ID}"
                  }
                ]
              }
            ]
          }
          EOF
      
          # Send to the first webhook
          curl -H "Content-Type: application/json" \
                -X POST \
                -d @card_payload.json \
                "${{ secrets.GH_TEAMS_INTEGRATION }}"
          
          # Send to the second webhook
          curl -H "Content-Type: application/json" \
                -X POST \
                -d @card_payload.json \
                "${{ secrets.GH_TEAMS_INTEGRATION_ARGS }}"

      # Send a Failure Card if either build or deploy failed.
      - name: Send Teams Card on Failure
        if: ${{ needs.build.result == 'failure' || needs.deploy.result == 'failure' }}
        run: |
          echo "Sending failure card to Teams..."
          cat <<EOF > card_payload.json
          {
            "@type": "MessageCard",
            "@context": "http://schema.org/extensions",
            "themeColor": "FF0000",
            "summary": "Build Failed",
            "sections": [
              {
                "activityTitle": "❌ Build Failed",
                "facts": [
                  {
                    "name": "Branch Name",
                    "value": "${BRANCH_NAME}"
                  },
                  {
                    "name": "Tag/Commit",
                    "value": "${RELEASE_TAG}"
                  },
                  {
                    "name": "Repository",
                    "value": "${GITHUB_REPOSITORY}"
                  },
                  {
                    "name": "Commit Link",
                    "value": "[${GITHUB_SHA}](${GITHUB_SERVER_URL}/${GITHUB_REPOSITORY}/commit/${GITHUB_SHA})"
                  },
                  {
                    "name": "Commit Message",
                    "value": "${COMMIT_MESSAGE}"
                  },
                  {
                    "name": "Time (IST)",
                    "value": "${{ steps.current_time.outputs.time }}"
                  },
                  {
                    "name": "Author",
                    "value": "${GITHUB_ACTOR}"
                  }
                ],
                "markdown": true
              }
            ],
            "potentialAction": [
              {
                "@type": "OpenUri",
                "name": "View Workflow Run",
                "targets": [
                  {
                    "os": "default",
                    "uri": "${GITHUB_SERVER_URL}/${GITHUB_REPOSITORY}/actions/runs/${GITHUB_RUN_ID}"
                  }
                ]
              }
            ]
          }
          EOF

          curl -H "Content-Type: application/json" \
                -X POST \
                -d @card_payload.json \
                "${{ secrets.GH_TEAMS_INTEGRATION }}"
          
          curl -H "Content-Type: application/json" \
                -X POST \
                -d @card_payload.json \
                "${{ secrets.GH_TEAMS_INTEGRATION_ARGS }}"
        


