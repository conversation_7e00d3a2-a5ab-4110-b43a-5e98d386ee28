import {
  Autocomplete,
  Box,
  Button,
  Checkbox,
  Container,
  FormControlLabel,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TextField,
  Typography,
} from '@mui/material';
import { useRouter } from 'next/router';
import { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { CustomError } from '~/errors/CustomerErrorResponse';
import { useGetAllBackOfficeAssetTypesQuery, useGetAssetByIdQuery } from '~/redux/api/assetsApi';
import { useCreateDashboardMutation } from '~/redux/api/dashboardApi';
import {
  useGetDashboardTemplateDetailsQuery,
  useGetDashboardTemplatesQuery,
} from '~/redux/api/dashboardTemplate';
import { useGetAllMeasurementsQuery } from '~/redux/api/measuresApi';
import { getActiveCustomer, getCustomerId } from '~/redux/selectors/customerSelectors';
import {
  getCurrentAssetType,
  getDashboardTemplateId,
  getDashboardTemplateName,
  getIsUserLoggedIn,
} from '~/redux/selectors/dashboardSelectors';
import { getCurrentSelectedAssetId, getExpandedAssetIds } from '~/redux/selectors/treeSelectors';
import { dashboardSlice, initialState } from '~/redux/slices/dashboardSlice';
import { AlertSnackbar } from '~/shared/snackbars/components/AlertSnackbar';
import { useSnackbar } from '~/shared/snackbars/snackbar-hooks';
import { AssetDo, AssetTypeOption } from '~/types/asset';
import { DashboardState } from '~/types/dashboard';
import { elementVariable } from '~/types/diagram';
import { ImageTextDetails } from '~/types/widgets';
import { assetTypePathMapper } from '~/utils/mappers/asset-type-mapper';
import { formatMetricLabel } from '~/utils/utils';

const AssetDashboardTemplate = () => {
  const dispatch = useDispatch();
  const [assetTypesWithPath, setAssetTypesWithPath] = useState<AssetTypeOption[]>([]);
  const [dashboardName, setDashboardName] = useState('');
  const [favourite, setFavourite] = useState(false);
  const { setTemplateId, setTemplateName, setAssetType } = dashboardSlice.actions;
  const activeCustomer = useSelector(getActiveCustomer);
  const assetId = useSelector(getCurrentSelectedAssetId);
  const assetType = useSelector(getCurrentAssetType);
  const loggedInuser = useSelector(getIsUserLoggedIn);
  const dashboardTemplateId = useSelector(getDashboardTemplateId);
  const dashboardTemplateName = useSelector(getDashboardTemplateName);
  const customerId = useSelector(getCustomerId);
  const expandedNodeIds = useSelector(getExpandedAssetIds);
  const router = useRouter();
  const [widgets, setWidgets] = useState<DashboardState['widget']['widgets']>([]);
  const [supportedMetrics, setSupportedMetrics] = useState<number[]>([]);
  const [filteredAssetMeasurements, setFilteredAssetMeasurements] = useState<
    { key: number; metricName: string; measurements: string }[]
  >([]);
  const [assetMeasurementsListMap, setAssetMeasurementsListMap] = useState<
    Map<
      number,
      {
        id: number;
        tag: string;
      }[]
    >
  >(new Map());
  const {
    isFetching: isFetchingDashboardTemplates,
    data: templateData,
    isSuccess: isTemplateSuccess,
    isError: isTemplateError,
  } = useGetDashboardTemplateDetailsQuery(dashboardTemplateId, {
    skip: dashboardTemplateId === 0,
  });
  const { data: assetMeasurements, isFetching: isMeasurementsFetching } =
    useGetAllMeasurementsQuery({
      assetId: Number(assetId),
      customerId: customerId,
    });

  const { data: dashboardTemplates, isFetching: fetchingTemplates } = useGetDashboardTemplatesQuery(
    { assetTypeId: assetType },
    { skip: !assetType },
  );

  const { data: asset } = useGetAssetByIdQuery(
    { customerId: activeCustomer?.id ?? 0, assetId: assetId },
    {
      skip: !activeCustomer?.id, // In RTK Query, the option is `skip` instead of `enabled`
    },
  );

  const [createDashboard, { data: dashboard, isSuccess, isError, error: createError }] =
    useCreateDashboardMutation();
  const [snackbarState, showSuccessAlert, showErrorAlert] = useSnackbar();

  const {
    data: assetTypeListData,
    isLoading: isAssetLoading,
    isSuccess: isSuccessfullBackOffieAssetTypes,
  } = useGetAllBackOfficeAssetTypesQuery();
  const assetTypeOption = assetTypePathMapper(
    assetTypeListData?.map((item) => ({
      ...item,
      name: item.name,
      id: item.id,
    })) ?? [],
  ).find((assetTypeOption) => assetTypeOption.value === (asset as AssetDo)?.type_id);
  useEffect(() => {
    if (assetTypeOption) {
      dispatch(dashboardSlice.actions.setAssetType(assetTypeOption.value));
    }
  }, [assetTypeOption]);
  useEffect(() => {
    if (isSuccessfullBackOffieAssetTypes && assetTypeListData) {
      setAssetTypesWithPath(
        assetTypePathMapper(
          assetTypeListData.map((item) => ({
            ...item,
            name: item.name,
            id: item.id,
          })),
        ),
      );
    }
  }, [assetTypeListData, isSuccessfullBackOffieAssetTypes]);
  useEffect(() => {
    if (isMeasurementsFetching === true || assetId) {
      setAssetMeasurementsListMap(new Map());
    }
    if (assetMeasurements && isMeasurementsFetching === false) {
      const assetMeasurementsMap = new Map<
        number,
        {
          id: number;
          tag: string;
        }[]
      >(new Map());
      assetMeasurements?.forEach((item) => {
        if (item.metric_id) {
          if (assetMeasurementsMap.has(item.metric_id)) {
            assetMeasurementsMap.set(item.metric_id, [
              ...(assetMeasurementsMap.get(item.metric_id) ?? []),
              {
                id: item.id,
                tag: item.tag,
              },
            ]);
          } else {
            assetMeasurementsMap.set(item.metric_id, [
              {
                id: item.id,
                tag: item.tag,
              },
            ]);
          }
        }
      });
      setAssetMeasurementsListMap(assetMeasurementsMap);
    }
  }, [assetMeasurements, isMeasurementsFetching, assetId]);

  useEffect(() => {
    if (templateData && templateData.data) {
      const templateDetailsData = JSON.parse(templateData.data) as {
        widget: DashboardState['widget'];
        topPanel: DashboardState['template']['topPanel'];
        chart: DashboardState['template']['chart'];
      };
      const metricsToShow: number[] = [];
      const widgets = templateDetailsData.widget.widgets.map((widget) => {
        switch (widget.type) {
          case 'title':
          case 'Weather': {
            widget.settings.mode = 'dashboard';
            return widget;
          }
          case 'stats': {
            if (!metricsToShow.includes(Number(widget.settings.selectedDbMeasureId))) {
              metricsToShow.push(Number(widget.settings.selectedDbMeasureId));
            }
            const measures =
              assetMeasurementsListMap.get(Number(widget.settings.selectedDbMeasureId)) !==
                undefined &&
              assetMeasurementsListMap.get(Number(widget.settings.selectedDbMeasureId));
            if (measures) {
              widget.settings.selectedDbMeasureId = measures[0].id.toString();
              widget.settings.assetMeasure = {
                assetId: assetId.toString(),
                measureId: measures.map((measure) => measure.id.toString()),
              };
            } else {
              widget.settings.selectedDbMeasureId = '';
              widget.settings.assetMeasure = {
                assetId: assetId.toString(),
                measureId: [],
              };
            }
            widget.settings.mode = 'dashboard';
            return widget;
          }
          case 'map': {
            widget.settings.mode = 'dashboard';
            widget.settings.markers = widget.settings.markers.map((marker) => {
              if (marker.selectedTitles) {
                marker.selectedTitles.forEach((title) => {
                  if (!metricsToShow.includes(Number(title))) {
                    metricsToShow.push(Number(title));
                  }
                });
                const measures = marker.selectedTitles.flatMap((title) => {
                  const measureList = assetMeasurementsListMap.get(Number(title));
                  return measureList
                    ? measureList.map((measure) => ({
                        assetId: assetId.toString(),
                        measureId: measure.id.toString(),
                      }))
                    : [];
                });
                marker.assetMeasures = measures.length > 0 ? measures : [];
                marker.labelAndUnits = marker.selectedTitles.reduce((acc, title) => {
                  const measureList = assetMeasurementsListMap.get(Number(title));
                  if (measureList) {
                    measureList.forEach((measure) => {
                      acc[measure.id.toString()] = {
                        label: measure.tag,
                        unit: marker.labelAndUnits[measure.id.toString()]?.unit || '',
                        value: '',
                      };
                    });
                  }
                  return acc;
                }, {} as Record<string, { label: string; unit: string; value: string }>);
                marker.selectedTitles = measures.map((measure) => measure.measureId);
              } else {
                marker.assetMeasures = [];
                marker.labelAndUnits = {};
              }
              return marker;
            });
            return widget;
          }
          case 'table': {
            widget.settings.selectedTitles.map((title) => {
              if (!metricsToShow.includes(Number(title))) {
                metricsToShow.push(Number(title));
              }
            });
            const measures = widget.settings.selectedTitles
              .map((title) => {
                const measurements: number[] = [];
                if (assetMeasurementsListMap.get(Number(title)) !== undefined) {
                  const measureIds =
                    assetMeasurementsListMap.get(Number(title))?.map((title) => title.id) ?? [];
                  measurements.push(...measureIds);
                }
                return measurements;
              })
              .flat();
            widget.settings.mode = 'dashboard';
            widget.settings.selectedTitles = measures.map((measure) => measure.toString());
            widget.settings.assetMeasure = [
              {
                assetId: assetId.toString(),
                measureId: measures.map((measure) => measure.toString()),
              },
            ];
            return widget;
          }
          case 'alert-widget': {
            widget.settings.selectedTitles.map((title) => {
              if (!metricsToShow.includes(Number(title))) {
                metricsToShow.push(Number(title));
              }
            });
            const measures = widget.settings.selectedTitles
              .map((title) => {
                const measurements: number[] = [];
                if (assetMeasurementsListMap.get(Number(title)) !== undefined) {
                  const measureIds =
                    assetMeasurementsListMap.get(Number(title))?.map((title) => title.id) ?? [];
                  measurements.push(...measureIds);
                }
                return measurements;
              })
              .flat();
            widget.settings.selectedTitles = measures.map((measure) => measure.toString());
            widget.settings.assetMeasure = [
              {
                assetId: assetId.toString(),
                measureId: measures.map((measure) => measure.toString()),
              },
            ];
            widget.settings.mode = 'dashboard';
            return widget;
          }
          case 'chart': {
            const chartType = widget.settings.chartType;
            widget.settings.settings.mode = 'dashboard';
            if (chartType === 'scatter' || chartType === 'bar') {
              widget.settings.settings.selectedTitles.map((title) => {
                if (!metricsToShow.includes(Number(title))) {
                  metricsToShow.push(Number(title));
                }
              });
              const measures = widget.settings.settings.selectedTitles
                .map((title) => {
                  const measurements: number[] = [];
                  if (assetMeasurementsListMap.get(Number(title)) !== undefined) {
                    const measureIds =
                      assetMeasurementsListMap.get(Number(title))?.map((title) => title.id) ?? [];
                    measurements.push(...measureIds);
                  }
                  return measurements;
                })
                .flat();
              widget.settings.settings.selectedTitles = measures.map((measure) =>
                measure.toString(),
              );
              const titles: number[] = [];
              if (widget.settings.settings.overrideGlobalBarColor) {
                widget.settings.settings.barColors = widget.settings.settings.barColors.map(
                  (bar) => {
                    const { color, measureId } = bar;
                    const measurement = assetMeasurementsListMap.get(Number(measureId));
                    const measureToMap: { id: number | null } = { id: null }; // Changed to `const`
                    if (measurement && measurement.length > 0) {
                      for (const measure of measurement) {
                        if (measures.includes(measure.id) && !titles.includes(measure.id)) {
                          titles.push(measure.id);
                          measureToMap.id = measure.id;
                        }
                      }
                    }
                    return {
                      color,
                      measureId: measureToMap.id?.toString() ?? '', // Use the valid id or fallback to an empty string
                    };
                  },
                );
              }
              widget.settings.settings.dbMeasureIdToName = Array.from(
                assetMeasurementsListMap.entries(),
              ).reduce((acc: Record<string, string>, [_, measurements]) => {
                measurements.forEach((measurement) => {
                  acc[measurement.id.toString()] = measurement.tag;
                });
                return acc;
              }, {});
              if (
                chartType === 'scatter' &&
                widget.settings.settings.showStacked &&
                widget.settings.settings.selectedSparkMeasure?.measureId &&
                widget.settings.settings.selectedSparkMeasure?.measureId !== ''
              ) {
                if (
                  widget.settings.settings.showStacked &&
                  widget.settings.settings.selectedSparkMeasure?.measureId &&
                  widget.settings.settings.selectedSparkMeasure?.measureId !== ''
                ) {
                  if (
                    !metricsToShow.includes(
                      Number(widget.settings.settings.selectedSparkMeasure?.measureId),
                    )
                  ) {
                    metricsToShow.push(
                      Number(widget.settings.settings.selectedSparkMeasure?.measureId),
                    );
                  }
                  const measures = assetMeasurementsListMap.get(
                    Number(widget.settings.settings.selectedSparkMeasure?.measureId),
                  );
                  widget.settings.settings.selectedSparkMeasure.assetId = assetId.toString();
                  widget.settings.settings.selectedSparkMeasure.measureId =
                    measures?.[0].id.toString() ?? '';
                } else {
                  widget.settings.settings.showSparkLine = false;
                  widget.settings.settings.selectedSparkMeasure.measureId = '';
                  widget.settings.settings.selectedSparkMeasure.assetId = '';
                }
              }
              widget.settings.settings.assetMeasure = [
                {
                  assetId: assetId.toString(),
                  measureId: measures.map((measure) => measure.toString()),
                },
              ];
              return widget;
            }
            if (chartType === 'bullet' || chartType === 'indicator' || chartType === 'heatmap') {
              if (!metricsToShow.includes(Number(widget.settings.settings.selectedDbMeasureId))) {
                metricsToShow.push(Number(widget.settings.settings.selectedDbMeasureId));
              }
              const measures =
                assetMeasurementsListMap.get(
                  Number(widget.settings.settings.selectedDbMeasureId),
                ) !== undefined &&
                assetMeasurementsListMap.get(Number(widget.settings.settings.selectedDbMeasureId));
              if (measures) {
                widget.settings.settings.selectedDbMeasureId = measures[0].id.toString();
                widget.settings.settings.assetMeasure = {
                  assetId: assetId.toString(),
                  measureId: measures.map((measure) => measure.id.toString()),
                };
              } else {
                widget.settings.settings.selectedDbMeasureId = '';
                widget.settings.settings.assetMeasure = {
                  assetId: assetId.toString(),
                  measureId: [],
                };
              }
            }
            if (chartType === 'sankey') {
              const metrics = widget.settings.settings.Label.filter(
                (metric) => metric.sourceFrom === 'Default',
              ).map((metric) => metric.sourceName);
              metrics.map((title) => {
                if (!metricsToShow.includes(Number(title))) {
                  metricsToShow.push(Number(title));
                }
              });
              widget.settings.settings.Label = widget.settings.settings.Label.map((label) => {
                if (label.sourceFrom === 'Default') {
                  let measureToMap: {
                    id: number | null;
                    tag: string | null;
                  } = {
                    id: null,
                    tag: null,
                  };
                  const measure =
                    assetMeasurementsListMap.get(Number(label.sourceAssetMeasure?.measureId)) ??
                    assetMeasurementsListMap.get(Number(label.sourceName));

                  if (measure && measure.length > 0) {
                    const selectedMeasure = measure.at(0);
                    measureToMap = {
                      id: selectedMeasure?.id ?? 0,
                      tag: selectedMeasure?.tag.toString() ?? '',
                    };
                  }
                  return {
                    ...label,
                    sourceLabel: formatMetricLabel(measureToMap?.tag ?? ''),
                    sourceName: measureToMap?.id?.toString() ?? '',
                    sourceAssetMeasure: {
                      ...label.sourceAssetMeasure,
                      assetId: assetId.toString(),
                      measureId: measureToMap?.id?.toString() ?? '',
                    },
                  };
                }
                return label;
              });
            }
            return widget;
          }
          case 'kpi-bar-chart': {
            if (!metricsToShow.includes(Number(widget.settings.selectedDbMeasureId))) {
              metricsToShow.push(Number(widget.settings.selectedDbMeasureId));
            }
            const measures =
              assetMeasurementsListMap.get(Number(widget.settings.selectedDbMeasureId)) !==
                undefined &&
              assetMeasurementsListMap.get(Number(widget.settings.selectedDbMeasureId));
            if (measures) {
              widget.settings.selectedDbMeasureId = measures[0].id.toString();
              widget.settings.assetMeasure = {
                assetId: assetId.toString(),
                measureId: measures.map((measure) => measure.id.toString()),
              };
            } else {
              widget.settings.selectedDbMeasureId = '';
              widget.settings.assetMeasure = {
                assetId: assetId.toString(),
                measureId: [],
              };
            }
            const dbMeasureIdToName: Record<string, string> = {};
            assetMeasurementsListMap.forEach((measures) => {
              measures.forEach((measure) => {
                if (widget.settings.selectedDbMeasureId === measure.id.toString()) {
                  dbMeasureIdToName[measure.id] = measure.tag;
                }
              });
            });
            widget.settings.dbMeasureIdToName = dbMeasureIdToName;
            widget.settings.mode = 'dashboard';
            return widget;
          }
          case 'kpi-percentage': {
            if (!metricsToShow.includes(Number(widget.settings.selectedDbMeasureId))) {
              metricsToShow.push(Number(widget.settings.selectedDbMeasureId));
            }
            const measures =
              assetMeasurementsListMap.get(Number(widget.settings.selectedDbMeasureId)) !==
                undefined &&
              assetMeasurementsListMap.get(Number(widget.settings.selectedDbMeasureId));
            if (measures) {
              widget.settings.selectedDbMeasureId = measures[0].id.toString();
              widget.settings.assetMeasure = {
                assetId: assetId.toString(),
                measureId: measures.map((measure) => measure.id.toString()),
              };
            } else {
              widget.settings.selectedDbMeasureId = '';
              widget.settings.assetMeasure = {
                assetId: assetId.toString(),
                measureId: [],
              };
            }
            widget.settings.mode = 'dashboard';
            return widget;
          }
          case 'kpi-table': {
            widget.settings.selectedTitles.map((title) => {
              if (!metricsToShow.includes(Number(title))) {
                metricsToShow.push(Number(title));
              }
            });
            const measures = widget.settings.selectedTitles
              .map((title) => {
                const measurements: number[] = [];
                if (assetMeasurementsListMap.get(Number(title)) !== undefined) {
                  const measureIds =
                    assetMeasurementsListMap.get(Number(title))?.map((title) => title.id) ?? [];
                  measurements.push(...measureIds);
                }
                return measurements;
              })
              .flat();
            widget.settings.mode = 'dashboard';
            widget.settings.selectedTitles = measures.map((measure) => measure.toString());
            widget.settings.assetMeasure = [
              {
                assetId: assetId.toString(),
                measureId: measures.map((measure) => measure.toString()),
              },
            ];
            return widget;
          }
          case 'kpi-color-box': {
            if (!metricsToShow.includes(Number(widget.settings.selectedDbMeasureId))) {
              metricsToShow.push(Number(widget.settings.selectedDbMeasureId));
            }
            const measures =
              assetMeasurementsListMap.get(Number(widget.settings.selectedDbMeasureId)) !==
                undefined &&
              assetMeasurementsListMap.get(Number(widget.settings.selectedDbMeasureId));
            if (measures) {
              widget.settings.selectedDbMeasureId = measures[0].id.toString();
              widget.settings.assetMeasure = {
                assetId: assetId.toString(),
                measureId: measures.map((measure) => measure.id.toString()),
              };
            } else {
              widget.settings.selectedDbMeasureId = '';
              widget.settings.assetMeasure = {
                assetId: assetId.toString(),
                measureId: [],
              };
            }
            widget.settings.mode = 'dashboard';
            return widget;
          }
          case 'image-stats':
          case 'kpi-value-indicator':
          case 'kpi-sparkline': {
            if (!metricsToShow.includes(Number(widget.settings.selectedDbMeasureId))) {
              metricsToShow.push(Number(widget.settings.selectedDbMeasureId));
            }
            const measures =
              assetMeasurementsListMap.get(Number(widget.settings.selectedDbMeasureId)) !==
                undefined &&
              assetMeasurementsListMap.get(Number(widget.settings.selectedDbMeasureId));
            if (measures) {
              widget.settings.selectedDbMeasureId = measures[0].id.toString();
              widget.settings.assetMeasure = {
                assetId: assetId.toString(),
                measureId: measures.map((measure) => measure.id.toString()),
              };
            } else {
              widget.settings.selectedDbMeasureId = '';
              widget.settings.assetMeasure = {
                assetId: assetId.toString(),
                measureId: [],
              };
            }
            widget.settings.mode = 'dashboard';
            return widget;
          }
          case 'Diagram': {
            Object.keys(widget.settings.elementIdVariabels).map((ids) => {
              const variables = widget.settings.elementIdVariabels[ids];
              return variables.map((vars) => {
                const title = vars.measurementId;
                if (!metricsToShow.includes(Number(title))) {
                  metricsToShow.push(Number(title));
                }
                return {
                  measurementId: vars.measurementId, // Returning the measurementId for each variable
                };
              });
            });

            widget.settings.elementIdVariabels = Object.keys(
              widget.settings.elementIdVariabels,
            ).reduce(
              (acc, ids) => {
                const variables = widget.settings.elementIdVariabels[ids];

                // Assign measurementId to each variable and push to the accumulator
                const updatedVariables = variables.map((vars) => {
                  const measures = assetMeasurementsListMap.get(Number(vars.measurementId));
                  const measurementId = measures ? measures[0].id.toString() : ''; // Take the first measure's id
                  return {
                    ...vars, // Spread the original variable properties
                    assetId: assetId.toString(),
                    measurementId: measurementId, // Add the measurementId to each variable
                  };
                });

                // Accumulate the updated variables under the corresponding ids
                acc[ids] = updatedVariables;
                return acc;
              },
              {} as Record<string, elementVariable[]>, // Initialize the accumulator with the correct type
            );
            widget.settings.elementVariable = widget.settings.elementVariable.map((variable) => {
              const measures = assetMeasurementsListMap.get(Number(variable.measurementId));
              const measurementId = measures ? measures[0].id.toString() : '';
              return {
                ...variable,
                assetId: assetId.toString(),
                measurementId: measurementId,
              };
            });
            widget.settings.mode = 'dashboard';
            return widget;
          }
          case 'image': {
            widget.settings.selectedTitles.map((title) => {
              if (!metricsToShow.includes(Number(title))) {
                metricsToShow.push(Number(title));
              }
            });

            const measures = widget.settings.selectedTitles
              .map((title) => {
                const measurements: number[] = [];
                if (assetMeasurementsListMap.get(Number(title)) !== undefined) {
                  const measureIds =
                    assetMeasurementsListMap.get(Number(title))?.map((title) => title.id) ?? [];
                  measurements.push(...measureIds);
                }
                return measurements;
              })
              .flat();
            widget.settings.mode = 'dashboard';
            widget.settings.selectedTitles = measures.map((measure) => measure.toString());
            widget.settings.assetMeasure = [
              {
                assetId: assetId.toString(),
                measureId: measures.map((measure) => measure.toString()),
              },
            ];

            const measureIdImageText: Record<string, ImageTextDetails> = {};
            Array.from(assetMeasurementsListMap.keys()).forEach((metric) => {
              const imageToText = widget.settings.measureIdToImageTextDetails[metric.toString()];
              if (imageToText) {
                const measurement = assetMeasurementsListMap.get(metric)?.map((measures) => {
                  return measures;
                });
                measurement?.forEach((measure) => {
                  measureIdImageText[measure.id.toString()] = {
                    ...imageToText,
                    id: measure.id.toString(),
                    label: measure.tag,
                  };
                });
              }
            });
            const dbMeasureIdToName: Record<string, string> = {};
            assetMeasurementsListMap.forEach((measures) => {
              measures.forEach((measure) => {
                dbMeasureIdToName[measure.id] = measure.tag;
              });
            });
            widget.settings.dbMeasureIdToName = dbMeasureIdToName;
            widget.settings.measureIdToImageTextDetails = measureIdImageText;
            return widget;
          }
        }
        return widget;
      });
      setWidgets(widgets);
      setSupportedMetrics(metricsToShow);
    }
  }, [templateData]);
  useEffect(() => {
    if (isSuccess && dashboard) {
      showSuccessAlert('Dashboard saved');
      dispatch(dashboardSlice.actions.setCurrentDashboardId(dashboard.id));
      router.push(`/customer/${customerId}/dashboard/${dashboard.id}`);
      dispatch(dashboardSlice.actions.setCurrentDashboardTitle(dashboard.title));
      dispatch(dashboardSlice.actions.setIsDirty(false));
      dispatch(dashboardSlice.actions.setIsDirty(false));
      dispatch(dashboardSlice.actions.setWidgetDirty(false));
      dispatch(dashboardSlice.actions.selectMainPanel('chart'));
    }
    if (isError) {
      const error = createError as CustomError;
      if (error?.data?.message?.toUpperCase() === 'CONFLICT OCCURRED.') {
        showErrorAlert(error?.data?.exception ?? 'Error saving dashboard');
      } else {
        showErrorAlert(error?.data?.message ?? 'Error saving dashboard');
      }
    }
  }, [isSuccess, dashboard, isError, createError]);

  const getFilteredAssetMeasurements = (
    assetMeasurementsListMap: Map<number, { id: number; tag: string }[]>,
    supportedMetrics: number[],
    templateData: any,
  ) => {
    return Array.from(assetMeasurementsListMap.keys())
      .filter((key) => supportedMetrics.includes(key))
      .map((key) => ({
        key,
        metricName:
          templateData.asset_template.measurements?.find(
            (measure: any) => measure.metric.id === key,
          )?.metric.name ?? 'N/A',
        measurements:
          assetMeasurementsListMap
            .get(key)
            ?.map((measure) => measure.id + '-' + measure.tag)
            .join(', ') || 'N/A',
      }));
  };

  useEffect(() => {
    if (assetMeasurementsListMap && supportedMetrics && templateData) {
      const filteredAssetMeasurements = getFilteredAssetMeasurements(
        assetMeasurementsListMap,
        supportedMetrics,
        templateData,
      );
      setFilteredAssetMeasurements(filteredAssetMeasurements);
    }
  }, [assetMeasurementsListMap, supportedMetrics, templateData]);

  return (
    <>
      <AlertSnackbar {...snackbarState} />
      {!loggedInuser?.scoped_roles?.find((role) => role.role === 'USER')?.role ? (
        <Box mt={2} sx={{ width: '100%', display: 'flex', justifyContent: 'end' }}>
          <Button
            sx={{ ml: 'auto' }}
            onClick={() => {
              dispatch(dashboardSlice.actions.selectMainPanel('chart'));
            }}
          >
            Back
          </Button>
        </Box>
      ) : null}

      <Container maxWidth={'lg'} component={'div'} style={{ float: 'left' }}>
        <Typography variant="h4">Dashboard Template instance creation</Typography>
        <Box mt={2} mb={2}>
          Asset Type
          <Typography variant="h6">{assetTypeOption?.label}</Typography>
        </Box>
        <Box>
          <TextField
            name="dashboardName"
            label="Dashboard Name"
            size="small"
            onChange={(e) => setDashboardName(e.target.value)}
            sx={{ mb: 2, width: 400 }}
          />
        </Box>
        <Box mb={2}>
          <FormControlLabel
            control={<Checkbox name="favourite" onChange={(e) => setFavourite(e.target.checked)} />}
            label="Mark Dashboard as Favourite"
          />
        </Box>

        <Box>
          <Autocomplete
            disablePortal
            disabled
            id="combo-box-demo"
            options={assetTypesWithPath.map((item) => {
              return {
                id: item.value.toString(),
                label: item.label,
              };
            })}
            value={
              assetTypesWithPath
                .map((item) => {
                  return {
                    id: item.value.toString(),
                    label: item.label,
                  };
                })
                .find((item) => item.id === assetType?.toString()) ?? null
            }
            loading={isAssetLoading}
            sx={{ width: 380 }}
            size="small"
            onChange={(event, value) => {
              dispatch(setAssetType(Number(value?.id ?? 0)));
            }}
            renderInput={(params) => <TextField {...params} label="Asset Type" />}
          />
        </Box>
        <Box sx={{ display: 'flex', mt: 2 }}>
          <Autocomplete
            disablePortal
            id="combo-box-demo"
            disabled={fetchingTemplates}
            size="small"
            options={
              dashboardTemplates?.items?.map((item) => {
                return {
                  id: item.id.toString(),
                  label: item.title,
                };
              }) ?? []
            }
            // loading={isAssetTypeLoading}
            sx={{ width: 380 }}
            value={
              dashboardTemplateId > 0
                ? { id: dashboardTemplateId.toString(), label: dashboardTemplateName }
                : null
            }
            onChange={(event, value) => {
              dispatch(setTemplateId(Number(value?.id ?? 0)));
              dispatch(setTemplateName(value?.label ?? ''));
            }}
            renderInput={(params) => <TextField {...params} label="Dashboard Template" />}
          />
        </Box>
        <Button
          sx={{ mt: 2 }}
          variant="contained"
          disabled={
            dashboardTemplateId === 0 ||
            isFetchingDashboardTemplates ||
            isTemplateError ||
            dashboardName === '' ||
            isMeasurementsFetching
          }
          onClick={async () => {
            if (isTemplateSuccess && templateData) {
              if (templateData.data) {
                const templateDetailsData = JSON.parse(templateData.data) as {
                  widget: DashboardState['widget'];
                  topPanel: DashboardState['template']['topPanel'];
                  chart: DashboardState['template']['chart'];
                };
                if (assetId) {
                  const expandedNodeIdList = Array.from(new Set(expandedNodeIds));
                  const measuresToSelect: Map<number, string> = new Map();
                  assetMeasurementsListMap.forEach((measurements) => {
                    measurements.forEach((measure) => {
                      measuresToSelect.set(measure.id, measure.tag);
                    });
                  });
                  const ids = Array.from(measuresToSelect.keys()).map(
                    (metricId) => `m:${assetId}:${metricId}`,
                  );
                  const dashboard = {
                    ...initialState,
                    customer: activeCustomer,
                    tree: {
                      ...initialState.tree,
                      selectedNodeIds: [...ids],
                      expandedNodeIds: expandedNodeIdList.map((node) => node.toString()),
                      currentSelectedNodeId: `m:${assetId}`,
                      dbMeasureIdToName: Object.fromEntries(measuresToSelect),
                    },
                    widget: {
                      widgets: widgets,
                      deleteWidgets: [],
                      widgetLayout: templateDetailsData.widget.widgetLayout,
                      lastWidgetId: widgets.length,
                    },
                    chart: {
                      startDate: templateDetailsData.chart.startDate,
                      endDate: templateDetailsData.chart.endDate,
                    },
                    topPanel: {
                      ...initialState.topPanel,
                      ...templateDetailsData.topPanel,
                    },
                    dashboardTitle: dashboardName,
                  } as DashboardState;
                  await createDashboard({
                    customerId: activeCustomer?.id ?? 0,
                    data: JSON.stringify(dashboard, null),
                    title: dashboardName,
                    description: '',
                    asset_id: Number(assetId),
                    dashboard_template_id: dashboardTemplateId,
                    favourite,
                  });
                }
              }
            }
          }}
        >
          Create dashboard
        </Button>
        <Box mt={2}>
          {templateData ? (
            <>
              {isMeasurementsFetching && <Typography>Loading measurements...</Typography>}
              <Typography variant="h5" mb={2}>
                Measurements mapped with Widget:
              </Typography>
              {Array.from(assetMeasurementsListMap.keys()).filter((key) =>
                supportedMetrics.includes(key),
              ).length === 0 &&
                !isMeasurementsFetching && <Typography>No measurements found</Typography>}
              {/* {assetMeasurementsListMap && assetMeasurementsListMap.size > 0 && (
                <Box mt={2}>
                  {Array.from(assetMeasurementsListMap.keys())
                    .filter((key) => supportedMetrics.includes(key))
                    .map((key) => (
                      <Box key={key}>
                        <Typography variant="h6">
                          {
                            templateData.asset_template.measurements?.find(
                              (measure) => measure.metric.id === key,
                            )?.metric.name
                          }
                          {assetMeasurementsListMap
                            .get(key)
                            ?.map((measure) => {
                              return measure.id + '-' + measure.tag;
                            })
                            .join(', ')}
                        </Typography>
                      </Box>
                    ))}
                </Box>
              )} */}
              {assetMeasurementsListMap && assetMeasurementsListMap.size > 0 && (
                <Box mt={2}>
                  <TableContainer component={Paper}>
                    <Table>
                      <TableHead>
                        <TableRow>
                          <TableCell>Metric Name</TableCell>
                          <TableCell>Measurements</TableCell>
                        </TableRow>
                      </TableHead>
                      <TableBody>
                        {/* {Array.from(assetMeasurementsListMap.keys())
                          .filter((key) => supportedMetrics.includes(key))
                          .map((key) => (
                            <TableRow key={key}>
                              <TableCell>
                                {templateData.asset_template.measurements?.find(
                                  (measure) => measure.metric.id === key,
                                )?.metric.name ?? 'N/A'}
                              </TableCell>
                              <TableCell>
                                {assetMeasurementsListMap
                                  .get(key)
                                  ?.map((measure) => {
                                    return measure.id + '-' + measure.tag;
                                  })
                                  .join(', ')}
                              </TableCell>
                            </TableRow>
                          ))} */}
                        {filteredAssetMeasurements.map(
                          ({
                            key,
                            metricName,
                            measurements,
                          }: {
                            key: number;
                            metricName: string;
                            measurements: string;
                          }) => (
                            <TableRow key={key}>
                              <TableCell>{metricName}</TableCell>
                              <TableCell>{measurements}</TableCell>
                            </TableRow>
                          ),
                        )}
                      </TableBody>
                    </Table>
                  </TableContainer>
                </Box>
              )}
            </>
          ) : null}
        </Box>
      </Container>
    </>
  );
};

export default AssetDashboardTemplate;
