import { Box, Button, CircularProgress, TextField } from '@mui/material';
import { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useGetCustomersQuery } from '~/redux/api/customersApi';
import { getActiveCustomer } from '~/redux/selectors/customerSelectors';
import { getSearchedValue } from '~/redux/selectors/dashboardListSelector';
import { dashboardListSlice } from '~/redux/slices/dashboardListSlice';
import { dashboardSlice } from '~/redux/slices/dashboardSlice';
import { Customer } from '~/types/customers';

const SearchDashbaord: React.FC = () => {
  const { data, isSuccess, isLoading } = useGetCustomersQuery({});
  const currentCustomer = useSelector(getActiveCustomer);
  const currSearchValue = useSelector(getSearchedValue);
  const dispatch = useDispatch();
  const [activeCustomer, setActiveCustomer] = useState<Customer | null>(currentCustomer);
  const [searchValue, setSearchValue] = useState<string | null>(currSearchValue);

  useEffect(() => {
    if (activeCustomer) {
      setSearchValue(null);
      setActiveCustomer(activeCustomer);
    }
  }, [activeCustomer]);

  const handleChange = (e: React.SyntheticEvent, value: Customer | null) => {
    if (value === null) {
      return;
    }
    setSearchValue(null);
    setActiveCustomer(value);
  };
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchValue(e.target.value);
  };
  const onSearch = () => {
    if (activeCustomer === null) {
      return;
    }
    dispatch(dashboardListSlice.actions.setCustomer(activeCustomer));
    dispatch(dashboardSlice.actions.setActiveCustomer(activeCustomer));
    dispatch(dashboardSlice.actions.setCurrentDashboardId(0));
    dispatch(dashboardListSlice.actions.setSearchValue(searchValue));
  };
  return (
    <Box sx={{ my: 3 }}>
      {/* {!isLoading && isSuccess && data && (
          <Autocomplete<Customer>
            id="combo-box-demo"
            options={data}
            fullWidth
            getOptionLabel={(option) => option.name}
            onChange={handleChange}
            value={activeCustomer ?? null}
            renderInput={(params) => <TextField {...params} label="Select Customer" />}
          />
        )} */}

      <Box sx={{ display: 'flex', alignItems: 'center' }}>
        <TextField
          label="Search Dashboard"
          variant="outlined"
          size="small"
          value={searchValue ?? ''}
          onChange={handleSearchChange}
        />
        <Button variant="contained" sx={{ ml: 2 }} onClick={() => onSearch()}>
          Search
        </Button>
      </Box>
    </Box>
  );
};

export default SearchDashbaord;
