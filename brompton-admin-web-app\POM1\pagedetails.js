module.exports.pagedetails = class pagedetails {
  constructor(page) {
    this.page = page;
    this.username = 'Username *';
    this.password = 'Password *';
    this.loginbutton = '#__next > div > div > form > div > button';
    this.forgot = 'Forgot password?';
    this.useremail = 'Username or Email';
    this.submit = 'Submit';
  }
  //launch url
  async lauchURL() {
    await this.page.goto('https://test.pivotol.ai/login');
  }
  //login with valid credentilas
  async login(username1, password1) {
    await this.page.getByLabel(this.username).fill(username1);
    await this.page.getByLabel(this.password).fill(password1);
    await this.page.locator(this.loginbutton).click();
  }
  //login with invalid credentilas
  async invalidlogin(username2, password2) {
    await this.page.getByLabel(this.username).fill(username2);
    await this.page.getByLabel(this.password).fill(password2);
    await this.page.locator(this.loginbutton).click();
  }
  //forgot password
  async forgotpassword(useremail1) {
    await this.page.getByText(this.forgot).click();
    await this.page.getByLabel(this.useremail).fill(useremail1);
    await this.page.getByText(this.submit).click();
  }
};
