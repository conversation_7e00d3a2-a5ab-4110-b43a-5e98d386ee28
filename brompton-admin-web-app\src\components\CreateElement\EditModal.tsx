import AddIcon from '@mui/icons-material/Add';
import CancelIcon from '@mui/icons-material/Cancel';
import {
  Box,
  Button,
  Chip,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  IconButton,
  TextField,
} from '@mui/material';
import React, { useState } from 'react';

interface EditModalProps {
  open: boolean;
  onClose: () => void;
  onSave: (variables: string[]) => void;
}

const EditModal: React.FC<EditModalProps> = ({ open, onClose, onSave }) => {
  const [newVariable, setNewVariable] = useState('');
  const [variables, setVariables] = useState<string[]>([]);
  const [variableError, setVariableError] = useState('');

  const handleAddVariable = () => {
    if (newVariable.trim() === '') {
      setVariableError('Variable name cannot be empty.');
    } else if (variables.includes(newVariable.trim())) {
      setVariableError('Variable already exists.');
    } else {
      setVariables([...variables, newVariable.trim()]);
      setNewVariable('');
      setVariableError('');
    }
  };

  const handleRemoveVariable = (index: number) => {
    setVariables(variables.filter((_, i) => i !== index));
  };

  const handleSaveClick = () => {
    onSave(variables);
    onClose();
    setVariables([]);
  };

  const handleModalClose = () => {
    onClose();
    setVariables([]);
    setNewVariable('');
  };

  return (
    <Dialog open={open} onClose={handleModalClose}>
      <DialogTitle>Edit Variables</DialogTitle>

      <DialogContent>
        <DialogContentText gutterBottom>
          Feel free to add or remove variables below. Once you&#39;re satisfied with your changes,
          click &apos;Save&apos; to apply them.
        </DialogContentText>

        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <TextField
            autoFocus
            required
            margin="dense"
            id="variable"
            name="variable"
            label="Add Variable"
            variant="standard"
            value={newVariable}
            onChange={(e) => setNewVariable(e.target.value)}
            fullWidth
            error={!!variableError}
            helperText={variableError}
          />
          <IconButton
            onClick={handleAddVariable}
            sx={{
              color: '#fff',
              backgroundColor: (theme) => theme.palette.primary.main,
              '&:hover': {
                backgroundColor: (theme) => theme.palette.primary.light,
              },
              ml: 3,
            }}
          >
            <AddIcon />
          </IconButton>
        </Box>
        <Box
          style={{
            marginTop: '20px',
            display: 'flex',
            flexWrap: 'wrap',
            gap: '10px',
            justifyContent: 'flex-start',
          }}
        >
          {variables.map((variable, index) => (
            <Chip
              key={index}
              label={variable}
              onDelete={() => handleRemoveVariable(index)}
              deleteIcon={<CancelIcon />}
              style={{
                backgroundColor: '#e0e0e0',
                fontSize: '14px',
                maxWidth: '100%',
              }}
            />
          ))}
        </Box>
      </DialogContent>

      <DialogActions>
        <Button onClick={handleModalClose}>Cancel</Button>
        <Button onClick={handleSaveClick}>Save</Button>
      </DialogActions>
    </Dialog>
  );
};

export default EditModal;
