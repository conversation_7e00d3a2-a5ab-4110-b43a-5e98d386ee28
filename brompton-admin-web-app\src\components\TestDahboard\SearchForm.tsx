import {
  <PERSON>ert,
  Autocomplete,
  Box,
  Button,
  Divider,
  FormControl,
  FormLabel,
  Grid,
  MenuItem,
  OutlinedInput,
  Popover,
  Select,
  SelectChangeEvent,
  TextField,
  Typography,
  useTheme,
} from '@mui/material';
import { AnyAction, ThunkDispatch } from '@reduxjs/toolkit';
import dayjs from 'dayjs';
import { useRouter } from 'next/router';
import React, { useEffect, useMemo, useState } from 'react';
import 'react-date-range/dist/styles.css'; // main style file
import 'react-date-range/dist/theme/default.css'; // theme css file
import { useDispatch, useSelector } from 'react-redux';
import { AssetMeasurement } from '~/measurements/domain/types';
import { useGetAllAssetQuery } from '~/redux/api/assetsApi';
import { measuresApi } from '~/redux/api/measuresApi';
import { getActiveCustomer } from '~/redux/selectors/customerSelectors';
import { getDateTimeFormat } from '~/redux/selectors/userPreferences';
import { RootState } from '~/redux/store';
import { AggByOptions, SamplePeriodOptions, TimeRangeOptions } from '~/types/dashboard';
import { assetsPathMapper, formatMetricLabel, getPreviousDate } from '~/utils/utils';
import IOSSwitch from '../Switch/Switch';
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
//@ts-ignore
import { DateRangePicker } from 'react-date-time-range-picker';
import { TimeRangeRefreshOptions } from '../dashboard/TopPanel';

type SearchFormProps = {
  start: Date;
  end: Date;
  searchSubmit: (
    selectedMeasuresByAssetId: Record<string, string[]>,
    selectedMeasures: string[],
    customerId: string,
    assetId: string,
    startDate: Date,
    endDate: Date,
    chartType: 'scatter' | 'bar',
    aggregation: number,
    samplePeriod: number,
    refreshInterval: number,
    selectionTimeRange: number,
    rangeSlider: boolean,
  ) => void;
};

const SearchForm = ({ searchSubmit, start, end }: SearchFormProps) => {
  const ActiveCustomer = useSelector(getActiveCustomer);
  const dateTimeFormat = useSelector(getDateTimeFormat);
  const [showPicker, setShowPicker] = useState(false);
  const dispatch = useDispatch<ThunkDispatch<RootState, any, AnyAction>>();
  const router = useRouter();
  const theme = useTheme();
  const [samplePeriod, setSamplePeriod] = useState<number>(1);
  const [aggregation, setAggregation] = useState<number>(1);
  const [chartType, setChartType] = useState<'bar' | 'scatter'>('scatter');
  const [autoSubmitted, setAutoSubmitted] = useState(false);
  const [selectedCustomer, setSelectedCustomer] = useState<string | null>(null);
  const [loadingMeasure, setLoadingMeasure] = useState<boolean>(false);
  const [selectedAsset, setSelectedAsset] = useState<string | null>(null);
  const [selectedMeasure, setSelectedMeasure] = useState<string | null>(null);
  const [measureData, setMeasureData] = useState<AssetMeasurement[]>([]);
  const [refreshInterval, setRefreshTimeInterval] = useState<number>(-1);
  const [rangeSlider, setRangeSlider] = useState<boolean>(true);
  const today = new Date();
  const [startDate, setStartDate] = useState<Date>(
    new Date(
      today.getFullYear(),
      today.getMonth(),
      today.getDate() - 7,
      today.getHours(),
      today.getMinutes(),
      today.getSeconds(),
    ),
  );
  const [endDate, setEndDate] = useState<Date>(new Date());
  const [selectedTimeRangeIndex, setSelectedTimeRangeIndex] = useState<number>(0);
  const [selectionTimeRange, setSelectionTimeRange] = useState<{
    startDate: Date;
    endDate: Date;
    key: string;
    selectedIndex: number;
  }>({
    startDate: startDate,
    endDate: endDate,
    key: 'selection',
    selectedIndex: selectedTimeRangeIndex,
  });
  const customerId = selectedCustomer ? Number(selectedCustomer) : 0;
  const noneAggWithTime =
    aggregation === 0 && (endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60) > 6;
  const { data: assetData, isFetching: isAssetReloading } = useGetAllAssetQuery(
    { customerId, parentIds: [] },
    { skip: !customerId, refetchOnMountOrArgChange: true },
  );
  useEffect(() => {
    setStartDate(start);
    setEndDate(end);
  }, [start, end]);
  useEffect(() => {
    if (ActiveCustomer) {
      setSelectedCustomer(String(ActiveCustomer.id));
    }
  }, [ActiveCustomer]);

  useEffect(() => {
    if (!router.isReady) return;

    const { asset_id, measurement_id, start_date, end_date } = router.query;

    if (asset_id) {
      // asset_id can be a comma-separated string or an array.
      const assetParam = Array.isArray(asset_id) ? asset_id[0] : asset_id;
      const assetIds = assetParam.split(',');

      // Set the first asset as the selected asset
      setSelectedAsset(assetIds[0] || null);
    }

    if (measurement_id) {
      const measureParam = Array.isArray(measurement_id) ? measurement_id[0] : measurement_id;
      const measureIds = measureParam.split(',');

      // Set the first measure as the selected measure
      setSelectedMeasure(measureIds[0] || null);
    }

    if (start_date) {
      const startTimestamp = Array.isArray(start_date) ? start_date[0] : start_date;
      const parsedStart = new Date(Number(startTimestamp));
      setStartDate(parsedStart);
    }

    if (end_date) {
      const endTimestamp = Array.isArray(end_date) ? end_date[0] : end_date;
      const parsedEnd = new Date(Number(endTimestamp));
      setEndDate(parsedEnd);
    }
  }, [router.isReady, router.query]);

  const handleChangeAsset = async (
    _e: React.SyntheticEvent<Element, Event>,
    value: string | null,
  ) => {
    setSelectedAsset(value);
    setSelectedMeasure(null); // Reset measurement selection when asset changes

    if (value) {
      setLoadingMeasure(true);
      const { data: newMeasureData, isSuccess } = await dispatch(
        measuresApi.endpoints.getAllMeasurements.initiate({
          customerId,
          assetId: Number(value),
        }),
      );
      setLoadingMeasure(false);

      if (isSuccess && newMeasureData) {
        setMeasureData(
          newMeasureData.map((measure) => ({
            ...measure,
            tag: formatMetricLabel(measure.tag),
          })),
        );
      }
    } else {
      setMeasureData([]);
    }
  };
  useEffect(() => {
    const fetchMeasure = async () => {
      setLoadingMeasure(true);
      const { data: newMeasureData, isSuccess: isMeasureDataSuccess } = await dispatch(
        measuresApi.endpoints.getAllMeasurements.initiate({
          customerId,
          assetId: Number(selectedAsset) ?? 0,
        }),
      );
      setLoadingMeasure(false);
      if (isMeasureDataSuccess && newMeasureData) {
        const formattedMeasureData = newMeasureData.map((measure) => ({
          ...measure,
          tag: formatMetricLabel(measure.tag),
        }));
        setMeasureData((prevMeasureData) => {
          const combined = [...prevMeasureData, ...(formattedMeasureData ?? [])];

          const uniqueById = Array.from(new Map(combined.map((item) => [item.id, item])).values());

          return uniqueById;
        });
      }
    };
    if (selectedAsset) {
      fetchMeasure();
    }
  }, [selectedAsset]);

  const handleChangeMeasure = (_e: React.SyntheticEvent<Element, Event>, value: string | null) => {
    setSelectedMeasure(value);
  };

  const assetTypesWithPath = useMemo(() => assetsPathMapper(assetData ?? []), [assetData]);

  const submit = () => {
    if (selectedCustomer && selectedAsset && selectedMeasure) {
      const selectedMeasuresByAssetId: Record<string, string[]> = {
        [selectedAsset]: [selectedMeasure],
      };
      searchSubmit(
        selectedMeasuresByAssetId,
        [selectedMeasure],
        selectedCustomer,
        selectedAsset,
        startDate,
        endDate,
        chartType,
        aggregation,
        samplePeriod,
        refreshInterval,
        selectionTimeRange.selectedIndex,
        rangeSlider,
      );
    }
  };

  useEffect(() => {
    if (
      router.isReady &&
      !autoSubmitted &&
      selectedAsset &&
      selectedMeasure &&
      measureData.length > 0 &&
      startDate &&
      endDate
    ) {
      const { asset_id, measurement_id, start_date, end_date } = router.query;

      // Ensure at least one of the query parameters is present before auto-submitting
      if (asset_id && measurement_id && start_date && end_date) {
        submit();
        setAutoSubmitted(true);
      }
      // }
      //   submit();
      //   setAutoSubmitted(true);
    }
  }, [
    router.isReady,
    autoSubmitted,
    selectedAsset,
    selectedMeasure,
    startDate,
    endDate,
    measureData,
  ]);
  const onSave = () => {
    setShowPicker(false);
    setStartDate(selectionTimeRange.startDate);
    setEndDate(selectionTimeRange.endDate);
    setSelectedTimeRangeIndex(selectionTimeRange.selectedIndex);
    if (selectionTimeRange.selectedIndex === 0) {
      setRefreshTimeInterval(-1);
    }
    const timeRange = TimeRangeOptions[selectionTimeRange.selectedIndex];
    if ((timeRange.value >= 1 && timeRange.value <= 8) || timeRange.value === 13) {
      setSamplePeriod(1);
    }
    if (timeRange.value === 9 || timeRange.value === 10) {
      setSamplePeriod(2);
    }
    if (timeRange.value === 11 || timeRange.value === 12 || timeRange.value === 14) {
      setSamplePeriod(6);
    }
    if (timeRange.value === 15) {
      setSamplePeriod(12);
    }
    if (timeRange.value === 16) {
      setSamplePeriod(13);
    }
  };
  const onCancel = () => {
    setShowPicker(false);
    setSelectionTimeRange({
      startDate: new Date(startDate), // Revert changes
      endDate: new Date(endDate),
      key: 'selection',
      selectedIndex: selectedTimeRangeIndex, // Reset to the previous index
    });
  };
  const handleSelection = (ranges: any) => {
    if (ranges.selection.selectedIndex === -1) {
      ranges.selection.selectedIndex = 0;
    }
    setSelectionTimeRange(ranges.selection);
  };
  return (
    <>
      <Popover
        open={showPicker}
        onClose={() => onCancel()}
        sx={{
          height: '650px',
          position: 'absolute',
          zIndex: 1300,
          top: 100,
          left: 180,
        }}
      >
        <DateRangePicker
          months={2}
          ranges={[selectionTimeRange]}
          onChange={handleSelection}
          maxDate={new Date()}
          direction="horizontal"
          showTime
          color={theme.palette.primary.main}
          rangeColors={[theme.palette.primary.main]}
          staticRanges={[
            ...TimeRangeOptions.map((option, index) => {
              return {
                label: option.label,
                key: option.label,
                range: () => ({
                  startDate: new Date(getPreviousDate(option.serverValue)),
                  endDate: new Date(),
                }),
                isSelected() {
                  return selectionTimeRange.selectedIndex === index;
                },
              };
            }),
          ]}
        />
        <Divider />
        <Box p={2} gap={2} display={'flex'} justifyContent={'end'}>
          <Button color="primary" variant="outlined" onClick={() => onCancel()}>
            Cancel
          </Button>
          <Button color="primary" variant="contained" onClick={onSave}>
            Apply
          </Button>
        </Box>
      </Popover>
      <Box mb={3} display="flex" flexDirection="column" gap={3}>
        {/* <Box display="flex" gap={3} alignItems="center" flexWrap="wrap"> */}
        <Grid container spacing={2}>
          <Grid item xs={12} xl={3} md={6} sm={12}>
            <FormControl
              size="small"
              fullWidth
              sx={{
                width: '100%',
                cursor: 'pointer',
                backgroundColor: theme.palette.background.paper,
              }}
            >
              <TextField
                label="Time Range"
                fullWidth
                onClick={() => setShowPicker(true)}
                contentEditable={false}
                sx={{
                  cursor: 'pointer',
                  width: '100%',
                  mt: 0.5,
                  '& input': {
                    p: 2,
                  },
                }}
                value={
                  startDate && endDate && selectionTimeRange.selectedIndex === 0
                    ? dayjs(startDate).format(dateTimeFormat) +
                      ' To ' +
                      dayjs(endDate).format(dateTimeFormat)
                    : TimeRangeOptions[selectionTimeRange.selectedIndex].label
                }
              />
            </FormControl>
          </Grid>
          <Grid item xs={12} xl={3} md={6} sm={12}>
            <FormControl
              sx={{ width: '100%', backgroundColor: (theme) => theme.palette.background.paper }}
            >
              <Select
                labelId="sample-period-select-label"
                id="sample-period-range-select"
                value={samplePeriod}
                onChange={(event: SelectChangeEvent<number | null>) =>
                  setSamplePeriod(Number(event.target.value))
                }
                input={
                  <OutlinedInput
                    label="Sample Period"
                    sx={{
                      p: 0.2,
                      '& legend': {
                        maxWidth: '100%',
                        height: 'fit-content',
                        '& span': { opacity: 1 },
                      },
                    }}
                  />
                }
              >
                {SamplePeriodOptions.map((option, index) =>
                  index > 0 ? (
                    <MenuItem key={option.value} value={index}>
                      {option.label}
                    </MenuItem>
                  ) : null,
                )}
              </Select>
            </FormControl>
          </Grid>

          <Grid item xs={12} xl={3} md={6} sm={12}>
            <FormControl
              sx={{ width: '100%', backgroundColor: (theme) => theme.palette.background.paper }}
            >
              <Select
                labelId="aggregation-select-label"
                id="aggregation-select"
                value={aggregation}
                onChange={(event: SelectChangeEvent<number>) =>
                  setAggregation(Number(event.target.value))
                }
                input={
                  <OutlinedInput
                    label="Aggregation"
                    sx={{
                      p: 0.2,
                      '& legend': {
                        maxWidth: '100%',
                        height: 'fit-content',
                        '& span': { opacity: 1 },
                      },
                    }}
                  />
                }
              >
                {AggByOptions.map((option, index) => (
                  <MenuItem key={index} value={option.value}>
                    {option.label}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12} xl={3} md={6} sm={12}>
            <FormControl
              sx={{
                width: '100%',
                mb: '5px',
                backgroundColor: theme.palette.background.paper,
              }}
            >
              <Select
                labelId="time-range-select-label"
                id="time-range-select"
                value={refreshInterval}
                disabled={selectionTimeRange.selectedIndex === 0}
                label="Refresh Interval"
                onChange={(event: SelectChangeEvent<number>) => {
                  setRefreshTimeInterval(Number(event.target.value));
                }}
                input={
                  <OutlinedInput
                    label="Refresh Interval"
                    sx={{
                      p: 0.2,
                      '& legend': {
                        maxWidth: '100%',
                        height: 'fit-content',
                        '& span': { opacity: 1 },
                      },
                    }}
                  />
                }
              >
                {TimeRangeRefreshOptions.map(({ label, interval }) => (
                  <MenuItem key={interval} value={interval}>
                    {label}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>
        </Grid>
        {/* </Box> */}

        {/* Row 2: Asset, Measurement, Chart Type */}
        <Grid container spacing={2}>
          <Grid item xs={12} xl={3} md={6} sm={12}>
            <Autocomplete
              id="asset-box-demo"
              loading={isAssetReloading}
              options={
                !isAssetReloading
                  ? assetTypesWithPath?.map((asset) => String(asset.value)) ?? []
                  : []
              }
              getOptionLabel={(option) =>
                assetTypesWithPath.find((a) => String(a.value) === option)?.label ?? ''
              }
              onChange={handleChangeAsset}
              value={selectedAsset}
              sx={{ width: '100%' }}
              renderInput={(params) => <TextField {...params} label="Select Asset" />}
            />
          </Grid>
          <Grid item xs={12} xl={3} md={6} sm={12}>
            <Autocomplete
              id="measure-box-demo"
              fullWidth
              loading={loadingMeasure}
              options={selectedAsset ? measureData?.map((measure) => String(measure.id)) ?? [] : []}
              getOptionLabel={(option) =>
                measureData.find((m) => String(m.id) === option)?.tag ?? ''
              }
              onChange={handleChangeMeasure}
              value={selectedMeasure}
              sx={{ width: '100%' }}
              renderInput={(params) => <TextField {...params} label="Select Measure" />}
            />
          </Grid>
          <Grid item xs={12} xl={3} md={6} sm={12}>
            <Box display="flex" alignItems="center" gap={2}>
              <FormLabel>Chart Type:</FormLabel>
              <Box display="flex" alignItems="center" gap={1}>
                <Typography variant="body1">Trend</Typography>
                <IOSSwitch
                  sx={{ m: 1 }}
                  checked={chartType === 'bar'}
                  onChange={() => setChartType((prev) => (prev === 'scatter' ? 'bar' : 'scatter'))}
                />
                <Typography variant="body1">Bar</Typography>
              </Box>
            </Box>
          </Grid>
          <Grid item xs={12} sm={1}>
            <Box display="flex" alignItems="center" gap={2}>
              <FormLabel>Range Slider:</FormLabel>
              <IOSSwitch
                sx={{ m: 1 }}
                checked={rangeSlider}
                onChange={() => setRangeSlider((prev) => !prev)}
              />
            </Box>
          </Grid>
        </Grid>
      </Box>
      {aggregation === 0 && noneAggWithTime ? (
        <Alert variant="outlined" severity="error" sx={{ mt: 2, mb: 2 }}>
          Time range with more then <strong>6 hours</strong> time period is not allowed
        </Alert>
      ) : null}
      <Box>
        <Button
          color="primary"
          variant="contained"
          disabled={!selectedAsset || !selectedMeasure || noneAggWithTime}
          onClick={submit}
        >
          Submit
        </Button>
      </Box>
    </>
  );
};

export default SearchForm;
