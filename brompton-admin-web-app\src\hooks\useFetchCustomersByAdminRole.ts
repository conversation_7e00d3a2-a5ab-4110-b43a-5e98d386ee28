import { useGetCurrentUserDetailsQuery } from '~/redux/api/usersApi';
import { useGetCustomersQuery } from '~/redux/api/customersApi';
import { useEffect, useState } from 'react';
import { UserDetailsResponse } from '~/types/users';
import { Customer } from '~/types/customers';

export const useFetchCustomersByAdminRole = () => {
  const { data: userDetails, isLoading: isUserDetailLoading } = useGetCurrentUserDetailsQuery();
  const { data: customers, isLoading: isCustomerDetailLoading } = useGetCustomersQuery({});
  const [customerList, setCustomerList] = useState<Customer[]>([]);
  useEffect(() => {
    if (!!customers && !!userDetails) {
      if (userDetails?.global_role === 'ADMIN') {
        setCustomerList(customers);
      } else {
        const fc: Customer[] = [];
        userDetails.scoped_roles.forEach((scopedRole) => {
          if (scopedRole.role === 'ADMIN') {
            const filteredCustomers = customers.filter((customer) =>
              scopedRole.customer_ids.includes(customer.id),
            );
            fc.push(...filteredCustomers);
          }
        });
        setCustomerList(fc);
      }
    }
  }, [customers, userDetails]);

  return customerList;
};
