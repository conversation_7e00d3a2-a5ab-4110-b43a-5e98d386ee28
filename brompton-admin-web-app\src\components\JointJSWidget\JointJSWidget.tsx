import { dia } from '@joint/core';
import EditIcon from '@mui/icons-material/Edit';
import { Box, MenuItem, Typography } from '@mui/material';
import dynamic from 'next/dynamic';
import { Dispatch, SetStateAction, useEffect, useRef, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import Loader from '~/components/common/Loader';
import useDiagramWidgetData from '~/hooks/useDiagramWidgetData';
import { useGetDiagramDetailsQuery } from '~/redux/api/diagramApi';
import { getZoomEnabled } from '~/redux/selectors/dashboardSelectors';
import { getDbMeasureIdToName } from '~/redux/selectors/treeSelectors';
import { dashboardSlice } from '~/redux/slices/dashboardSlice';
import { setIsLoad } from '~/redux/slices/solarSlice';
import { TimeRangeOptions } from '~/types/dashboard';
import { cellNamespace, elementVariable } from '~/types/diagram';
import { DiagramWidget } from '~/types/widgets';
import { getPreviousDate } from '~/utils/utils';
import CommonWidgetContainer from '../common/CommonWidgetContainer';
import LiquidTank from '../CreateElement/Tank';
import { ConicTank } from '../JointJs/DiagramComponent/ConicalTank';
import { DiagramWidgetSettingsProps } from './DiagramWidgetSettings';

const DiagramWidgetSettings = dynamic<DiagramWidgetSettingsProps>(
  () => import('./DiagramWidgetSettings'),
  {
    ssr: false,
    loading() {
      return <Loader />;
    },
  },
);
const DiagramPaper = dynamic(() => import('./JointJsPaper'), {
  ssr: false,
});

type StatsWidgetContainerProps = {
  id: string;
  settings: DiagramWidget;
};

function DiagramWidgetPanel({ id, settings }: StatsWidgetContainerProps) {
  const dispatch = useDispatch();
  const graphRef = useRef<dia.Graph | null>(null);
  const enabledZoom = useSelector(getZoomEnabled);
  if (!graphRef.current) {
    graphRef.current = new dia.Graph({}, { cellNamespace: cellNamespace });
  }
  const { title } = settings;
  const { data: diagramData, isFetching } = useGetDiagramDetailsQuery(
    {
      id: settings.selectedDiagram?.id ?? 0,
    },
    {
      skip: !settings.selectedDiagram?.id,
      refetchOnMountOrArgChange: true,
    },
  );
  const selectedDbMeasureIdToName = useSelector(getDbMeasureIdToName);
  const [jsonError, setJsonError] = useState<string | null>(null);
  const [zoomLevel, setZoomLevel] = useState(1); // Track the current zoom level
  const paperInstanceRef = useRef<dia.Paper | null>(null);

  const {
    isLoading: loadingData,
    isError,
    data,
    successAndFailedMeasurements,
  } = useDiagramWidgetData(id, settings);

  useEffect(() => {
    if (diagramData?.data) {
      try {
        graphRef.current!.clear();
        const parsedData = JSON.parse(diagramData.data);
        const diagram = parsedData?.diagram || {};
        if (graphRef.current!.getCells().length > 0) {
          graphRef.current!.clear();
        }
        graphRef.current!.fromJSON(diagram);
        const elementVariables: elementVariable[] = [];
        const elementIdVariabels: Record<string, elementVariable[]> = {};
        const cellIds: string[] = [];
        diagram.cells.forEach((cell: any) => {
          if (cell) {
            const cellInfo = graphRef.current!.getCell(cell.id);
            const { range = [], variables = [], conditionalRule = [] } = cellInfo.get('data') || {};
            cellInfo.prop('data/range', range ?? []);
            cellInfo.prop('data/variables', variables ?? []);
            cellInfo.prop('data/conditionalRule', conditionalRule ?? []);
            if (cellInfo instanceof ConicTank) {
              const { maxCapacity } = cellInfo.get('data') ?? {};
              cellInfo.updateMaxCapacity(Number(maxCapacity ?? 100));
            }
            if (cellInfo instanceof LiquidTank) {
              const direction = cellInfo.get('data')?.direction ?? 'horizontal';
              cellInfo.startProgress(true);
              cellInfo.level = 10;
              cellInfo.updateDirection(direction);
              cellInfo.prop('data/range', cellInfo.get('data').range ?? []);
              // cellInfo.updateRangePicker(cellInfo.get('data').range ?? []);
              // cellInfo.updateMaxCapacity(cellInfo.get('data').maxCapacity ?? 100);
            }
          }
          if (cell.base64Image) {
            const element = graphRef.current!.getCell(cell.id) as dia.Element;
            if (element) {
              // injectPatternIntoDefs(cell.id, cell.base64Image);
              element.attr('body/fill', `url(#pattern-${cell.id})`);
            }
          }
        });
        graphRef
          .current!.getCells()
          .filter((cell) => cell.get('type') !== 'Pipe')
          .forEach((cell) => {
            const cellData = cell.get('data'); // Get 'data' object from cell
            const variables = (cellData?.variables ?? []) as elementVariable[];
            if (variables.length > 0) {
              elementVariables.push(...variables);
              elementIdVariabels[cell.id.toString()] = variables;
              cellIds.push(cell.id.toString());
            }
            return null;
          });

        const settingsElementIdVariables: Record<string, elementVariable[]> = {
          ...settings.elementIdVariabels,
        };
        cellIds.forEach((cell) => {
          if (
            elementIdVariabels.hasOwnProperty(cell) &&
            !settingsElementIdVariables.hasOwnProperty(cell)
          ) {
            settingsElementIdVariables[cell] = elementIdVariabels[cell];
          }
          if (
            settingsElementIdVariables.hasOwnProperty(cell) &&
            !elementIdVariabels.hasOwnProperty(cell)
          ) {
            delete settingsElementIdVariables[cell];
          }
          if (
            settingsElementIdVariables.hasOwnProperty(cell) &&
            elementIdVariabels.hasOwnProperty(cell) &&
            settingsElementIdVariables[cell].length !== elementIdVariabels[cell].length
          ) {
            const settingsVars = [...settingsElementIdVariables[cell]];
            const elementVars = [...elementIdVariabels[cell]];
            elementVars.forEach((varItem) => {
              if (
                !settingsVars.some(
                  (settingsVar) => settingsVar.variableName === varItem.variableName,
                )
              ) {
                settingsVars.push(varItem);
              }
            });
            settingsVars.forEach((varItem) => {
              if (
                !elementVars.some((elementVar) => elementVar.variableName === varItem.variableName)
              ) {
                elementVars.push(varItem);
              }
            });
            settingsElementIdVariables[cell] = settingsVars;
          }
        });
        Object.keys(settingsElementIdVariables).forEach((key) => {
          if (!cellIds.includes(key)) {
            delete settingsElementIdVariables[key];
          }
        });

        cellIds.forEach((cellId) => {
          if (!settingsElementIdVariables.hasOwnProperty(cellId)) {
            settingsElementIdVariables[cellId] = [];
          }
        });
        const uniqueVariables: elementVariable[] = Object.values(settingsElementIdVariables)
          .map((variable) => {
            return variable;
          })
          .flat();
        graphRef.current!.getCells().forEach((cell) => {
          const cellData = cell.get('data');
          const cellId = cell.id.toString();
          const variables = settingsElementIdVariables[cellId];

          if (variables) {
            cell.set('data', {
              ...cellData,
              variables, // Updated variables for the cell
            });
          }
        });
        const timeRange = 8;
        for (const [elementId, variables] of Object.entries(settingsElementIdVariables)) {
          settingsElementIdVariables[elementId] = variables.map((v) => ({
            ...v,
            aggBy: v?.aggBy,
            samplePeriod: v?.samplePeriod === undefined ? 1 : v?.samplePeriod,
            timeRange: v?.timeRange === undefined ? timeRange : v?.timeRange,
            startDate: getPreviousDate(
              TimeRangeOptions[v?.timeRange === undefined ? timeRange : v?.timeRange].serverValue,
            ),
            endDate: Date.now(),
            isRelativeToGlboalEndTime:
              v?.isRelativeToGlboalEndTime === undefined ? false : v.isRelativeToGlboalEndTime,
            globalSamplePeriod: v?.globalSamplePeriod === undefined ? false : v.globalSamplePeriod,
          }));
        }
        dispatch(
          dashboardSlice.actions.setCurrentWidgetSettings({
            id: id,
            type: 'Diagram',
            settings: {
              ...settings,
              elementIdVariabels: settingsElementIdVariables,
              elementVariable: uniqueVariables,
              jsonFile: JSON.stringify({ graph: graphRef.current }, null),
            },
          }),
        );
      } catch (error) {
        console.error('Error parsing diagram data:', error);
      }
    }
  }, [diagramData?.data, dispatch]);

  useEffect(() => {
    if (settings.jsonFile) {
      try {
        const parsedContent = JSON.parse(settings.jsonFile)?.graph;
        if (parsedContent) {
          setJsonError(null);
        } else {
          throw new Error("The JSON file is missing a 'cells' array.");
        }
      } catch (error: any) {
        console.error('Error parsing JSON:', error);
        setJsonError('Error parsing JSON: ' + error.message);
      }
    }
  }, [settings.jsonFile]);

  useEffect(() => {
    if (Object.keys(selectedDbMeasureIdToName).length > 0) {
      dispatch(setIsLoad(true));
      dispatch(
        dashboardSlice.actions.setCurrentWidgetSettings({
          id: id,
          type: 'Diagram',
          settings: {
            ...settings,
            selectedDbMeasureId: Object.keys(selectedDbMeasureIdToName)[1],
          },
        }),
      );
    }
  }, [settings.selectedDbMeasureId, selectedDbMeasureIdToName]);
  const setZoomLevelOnWidget = (zoomLevel: number) => {
    if (settings.mode === 'dashboard') {
      dispatch(
        dashboardSlice.actions.setCurrentWidgetSettings({
          id: id,
          type: 'Diagram',
          settings: {
            ...settings,
            zoomLevel: zoomLevel,
            isDirty: true,
          },
        }),
      );
    }
  };
  const resetZoom = () => {
    if (paperInstanceRef.current) {
      paperInstanceRef.current.scale(1, 1);
      setZoomLevel(1);
    }
  };

  const DiagramWidgetSettingsWrapper = ({
    settings,
    handleSettingsChange,
  }: {
    settings: DiagramWidget;
    handleSettingsChange: Dispatch<SetStateAction<DiagramWidget>>;
  }) => (
    <DiagramWidgetSettings
      graphRef={graphRef}
      diagramData={diagramData}
      isFetching={isFetching}
      settings={settings}
      handleSettingsChange={handleSettingsChange}
    />
  );

  return (
    <CommonWidgetContainer
      id={id}
      widgetType="Diagram"
      widgetName="Diagram"
      successAndFailedMeasurements={successAndFailedMeasurements}
      settings={settings}
      settingsDialog={DiagramWidgetSettingsWrapper}
      menuOptions={
        settings.selectedDiagram?.id && (
          <MenuItem
            onClick={() => {
              if (settings.selectedDiagram && settings.selectedDiagram.name) {
                sessionStorage.setItem('diagramId', settings.selectedDiagram.id.toString());
                sessionStorage.setItem('diagramName', settings.selectedDiagram.name.toString());
              }
              window.open('/diagram', '_blank');
            }}
          >
            <EditIcon sx={{ mr: 0.5 }} />
            Edit Diagram
          </MenuItem>
        )
      }
      widgetContent={
        <>
          {settings.selectedDiagram === null ? (
            <Box
              sx={{
                position: 'absolute',
                height: '100%',
                width: '100%',
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
              }}
            >
              <Typography>Please Select The Diagram</Typography>
            </Box>
          ) : settings.jsonFile === '' ? (
            <Box
              sx={{
                position: 'absolute',
                height: '100%',
                width: '100%',
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
              }}
            >
              <Typography>Loading diagram...</Typography>
            </Box>
          ) : (
            <>
              {zoomLevel !== 1 && !settings.disableZoom && !enabledZoom && (
                <Box
                  sx={{
                    position: 'absolute',
                    top: 10,
                    left: 10,
                    zIndex: 50,
                    cursor: 'pointer',
                  }}
                  component="a"
                  onClick={resetZoom}
                >
                  <Typography
                    variant="caption"
                    sx={{
                      backgroundColor: (theme) => theme.palette.primary.main,
                      color: '#fff',
                      px: 1,
                      py: 0.5,
                      borderRadius: 0.5,
                    }}
                  >
                    Reset Zoom
                  </Typography>
                </Box>
              )}

              <Box sx={{ height: '100%' }}>
                <Box
                  sx={{
                    display: 'flex',
                    height: '100%',
                    width: '100%',
                  }}
                >
                  <Box style={{ width: '100vw', height: '100%', overflow: 'hidden' }}>
                    <DiagramPaper
                      settings={settings}
                      setZoomLevel={setZoomLevel}
                      paperInstanceRef={paperInstanceRef}
                      setZoomLevelOnWidget={setZoomLevelOnWidget}
                    />
                  </Box>
                </Box>
                {title.isVisible && (
                  <Box
                    sx={{
                      width: '100%',
                      display: 'flex',
                      justifyContent: 'center',
                      marginY: '10px',
                    }}
                  >
                    <Typography
                      variant="h5"
                      sx={{
                        fontSize: '20px',
                        fontWeight: 500,
                        color: title.color,
                      }}
                      component="div"
                    >
                      {title.value}
                    </Typography>
                  </Box>
                )}
              </Box>
            </>
          )}
        </>
      }
    />
  );
}

export default DiagramWidgetPanel;
