// tests/api.test.js
const { test, expect, request } = require('@playwright/test');

test.describe('API Test Suite', () => {
  test('GET /assets-backoffice/asset-types/5/asset-templates retrieves asset templates successfully', async ({
    request,
  }) => {
    // Set headers
    const headers = {
      'BE-CsrfToken': 'iZI1KrHQ0zzbnd09vUb1hzeScCF+evBLkkQcMWUyYeE=',
      Cookie:
        'BE-AccessToken=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VybmFtZSI6InRlc3QiLCJzdWIiOjIsInJvbGVzIjp7IkFETUlOIjpbMTI4LDgsMTIxLDExOSwxMDYsMTE4LDEwOCw4NiwxMTEsMTEyLDg1LDEwNyw4NCwxMTMsMTE0LDExNywxMjAsMTI3LDEyMywxMjYsMTI0LDEwOSwxMjUsMTIyLDExMCw5LDgyXSwiVVNFUiI6WzEyOCw4LDEyMSwxMTksMTA2LDExOCwxMDgsODYsMTExLDExMiw4NSwxMDcsODQsMTEzLDExNCwxMTcsMTIwLDEyNywxMjMsMTI2LDEyNCwxMDksMTI1LDEyMiwxMTAsOSw4Ml0sIlBPV0VSX1VTRVIiOlsxMjgsOCwxMjEsMTE5LDEwNiwxMTgsMTA4LDg2LDExMSwxMTIsODUsMTA3LDg0LDExMywxMTQsMTE3LDEyMCwxMjcsMTIzLDEyNiwxMjQsMTA5LDEyNSwxMjIsMTEwLDksODJdfSwiaWF0IjoxNzMxNTczNjU1LCJleHAiOjE3MzE1ODA4NTV9.m7I3WYNPEyK159c8bJw6vuQ_L3G1MZwaSqOHL_EAnes; BE-CSRFToken=iZI1KrHQ0zzbnd09vUb1hzeScCF%2BevBLkkQcMWUyYeE%3D',
    };

    // Make GET request
    const response = await request.get(
      'https://test.brompton.ai/api/v0/assets-backoffice/asset-types/5/asset-templates',
      {
        headers: headers,
      },
    );

    // Check response status
    expect(response.status()).toBe(200);

    // Verify response body if needed
    const responseBody = await response.json();
    console.log(responseBody);

    // Perform assertions on the response data
    expect(responseBody).toHaveProperty('items'); // Check that 'items' exists
    expect(Array.isArray(responseBody.items)).toBe(true); // Check if 'items' is an array
    expect(responseBody).toHaveProperty('total'); // Check that 'total' exists in the response
    expect(responseBody.total).toBeGreaterThanOrEqual(0); // Check that 'total' is a non-negative integer
  });
});
