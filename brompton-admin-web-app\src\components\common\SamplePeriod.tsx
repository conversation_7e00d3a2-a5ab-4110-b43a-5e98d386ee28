import Select, { SelectChangeEvent } from '@mui/material/Select';
import { SamplePeriodOptions } from '~/types/dashboard';
import MenuItem from '@mui/material/MenuItem';
import FormControl from '@mui/material/FormControl';
import React, { ChangeEvent } from 'react';
import { Box, Card, Checkbox, FormControlLabel, FormGroup, OutlinedInput } from '@mui/material';

export type SamplePeriodProps = {
  id: string;
  value: number;
  handleChange: (e: SelectChangeEvent<number>) => void;
  globalSamplePeriod: boolean;
  setOverRideSettings: (event: ChangeEvent<HTMLInputElement>, checked: boolean) => void;
};

export function SamplePeriod({
  id,
  handleChange,
  value,
  globalSamplePeriod,
  setOverRideSettings,
}: SamplePeriodProps) {
  return (
    <Card>
      <FormGroup>
        <FormControlLabel
          control={
            <Checkbox
              sx={{
                p: 2,
              }}
              checked={globalSamplePeriod}
              onChange={setOverRideSettings}
              name="globalSamplePeriod"
            />
          }
          label="Override Global Sample Period"
        />
      </FormGroup>
      {globalSamplePeriod ? (
        <FormControl fullWidth>
          <Box display="flex" alignItems="center" width="100%" p={1} gap={1}>
            <Select
              labelId={'sample-period-label-' + id}
              id={'sample-period-select-' + id}
              value={value}
              label="Sample Period"
              input={
                <OutlinedInput
                  label="Sample Period"
                  sx={{
                    p: 0.2,
                    '& legend': {
                      maxWidth: '100%',
                      height: 'fit-content',
                      '& span': {
                        opacity: 1,
                      },
                    },
                  }}
                />
              }
              onChange={handleChange}
              fullWidth
            >
              {SamplePeriodOptions.map((option, index) =>
                index > 0 ? (
                  <MenuItem key={option.value} value={index}>
                    {option.label}
                  </MenuItem>
                ) : null,
              )}
            </Select>
          </Box>
        </FormControl>
      ) : null}
    </Card>
  );
}
