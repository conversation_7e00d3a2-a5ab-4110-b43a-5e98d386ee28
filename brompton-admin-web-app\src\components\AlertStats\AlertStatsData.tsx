import { Box, CircularProgress, Paper, Typography } from '@mui/material';
import { DataGrid, GridColDef, GridValueGetterParams } from '@mui/x-data-grid';
import { useRouter } from 'next/router';
import { useSelector } from 'react-redux';
import { useGetAlertStatsQuery } from '~/redux/api/alertApi';
import { getDateTimeFormat, getThousandSeparator } from '~/redux/selectors/userPreferences';
import { formatNumber, fortmatUTCDate, roundNumber } from '~/utils/utils';

const AlertStatsData = () => {
  const router = useRouter();
  const dateTimeFormat = useSelector(getDateTimeFormat);
  const thousandSeparator = useSelector(getThousandSeparator);

  const { alertId } = router.query;

  // Fetching alert stats data
  const { data: alertStatsData, isFetching: alertStatsFetching } = useGetAlertStatsQuery(
    {
      alertId: alertId as string,
    },
    {
      skip: !alertId || alertId === undefined,
      refetchOnMountOrArgChange: true,
    },
  );

  // Loading state
  if (alertStatsFetching) {
    return (
      <div style={{ display: 'flex', justifyContent: 'center', marginTop: '20px' }}>
        <CircularProgress />
      </div>
    );
  }

  // No data state
  if (!alertStatsData || alertStatsData.items.length === 0) {
    return (
      <Typography variant="body2" color="text.secondary">
        No data available
      </Typography>
    );
  }

  // Define columns for the DataGrid
  const columns: GridColDef[] = [
    { field: 'id', headerName: 'ID', width: 150 },
    {
      field: 'start_time',
      headerName: 'Start Time (UTC)',
      width: 230,
      valueGetter: (params: GridValueGetterParams) =>
        fortmatUTCDate(params.row.start_time, dateTimeFormat),
    },
    {
      field: 'end_time',
      headerName: 'End Time (UTC)',
      width: 230,
      valueGetter: (params: GridValueGetterParams) =>
        fortmatUTCDate(params.row.end_time, dateTimeFormat),
    },
    { field: 'duration', headerName: 'Duration', width: 150 },
    { field: 'max_value', headerName: 'Max Value', width: 150 },
    { field: 'min_value', headerName: 'Min Value', width: 150 },
    { field: 'avg_value', headerName: 'Avg Value', width: 150 },
  ];

  // Map data to be displayed in the DataGrid
  const rows = alertStatsData.items
    .slice() // prevent mutating original data
    .sort((a, b) => new Date(b.end_time).getTime() - new Date(a.end_time).getTime())
    .map((item) => ({
      id: item.id,
      alert_id: item.alert_id,
      start_time: item.start_time,
      end_time: item.end_time,
      duration: item.formattedTimeDuration,
      max_value: thousandSeparator ? formatNumber(item.max_value) : roundNumber(item.max_value),
      min_value: thousandSeparator ? formatNumber(item.min_value) : roundNumber(item.min_value),
      avg_value: thousandSeparator ? formatNumber(item.avg_value) : roundNumber(item.avg_value),
    }));

  return (
    <Box sx={{ height: 680, width: '100%' }}>
      <DataGrid
        sx={{ p: 1 }}
        rows={rows}
        columns={columns}
        pagination
        pageSizeOptions={[10, 20, 50, 100]}
        initialState={{
          pagination: {
            paginationModel: {
              pageSize: 10,
            },
          },
        }}
      />
    </Box>
  );
};

export default AlertStatsData;
