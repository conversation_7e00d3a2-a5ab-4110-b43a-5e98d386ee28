import { useRouter } from 'next/router';
import { useEffect, useMemo, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useGetAllAssetQuery } from '~/redux/api/assetsApi';
import {
  useGetDashboardTemplateDetailsQuery,
  useGetDashboardTemplatesQuery,
} from '~/redux/api/dashboardTemplate';
import { useGetAllMeasurementsQuery } from '~/redux/api/measuresApi';
import { getActiveCustomer } from '~/redux/selectors/customerSelectors';
import { getCurrentDashboardId, isDashboardDirty } from '~/redux/selectors/dashboardSelectors';
import { getDeletedWidgets, getWidgets } from '~/redux/selectors/widgetSelectors';
import { dashboardSlice } from '~/redux/slices/dashboardSlice';
import { DashboardState, TimeRangeOptions } from '~/types/dashboard';
import { WidgetCommonSettings } from '~/types/widgets';
import { assetsPathMapper, getPreviousDate } from '~/utils/utils';
import { Role, useRolePermission } from './useRolePermission';

type useCommonWidgetHelperProps<T extends WidgetCommonSettings> = {
  id: string;
  settings: T;
};

const useCommonWidgetHelper = <T extends WidgetCommonSettings>({
  id,
  settings,
}: useCommonWidgetHelperProps<T>) => {
  const [confirm, setConfirm] = useState<boolean>(false);
  const dashboardLink = settings as T & {
    dashboard: { id: number; title: string } | null;
  };
  const dispatch = useDispatch();
  const { hasDashboardPermission } = useRolePermission();
  const dashboardId = useSelector(getCurrentDashboardId);
  const widgets = useSelector(getWidgets);
  const deleteWidgets = useSelector(getDeletedWidgets);
  const isDashboardStateDirty = useSelector(isDashboardDirty);
  const activeCustomer = useSelector(getActiveCustomer);
  const router = useRouter();
  const [assetToAssetType, seAssetToAssetType] = useState<number | null>(null);

  const { data: assetData, isFetching: isAssetReloading } = useGetAllAssetQuery(
    { customerId: activeCustomer?.id ?? 0, parentIds: [] },
    {
      skip: !activeCustomer || settings.mode === 'template',
      refetchOnMountOrArgChange: true,
    },
  );
  const assetTypesWithPathForAsset = useMemo(() => assetsPathMapper(assetData ?? []), [assetData]);
  useEffect(() => {
    if (
      assetData &&
      settings.mode === 'dashboard' &&
      settings.dashboardOrTemplate === 'template' &&
      settings.assetOrAssetType !== null
    ) {
      const assetToType = assetData?.find(
        (asset) => asset.id === settings.assetOrAssetType,
      )?.assetTypeId;
      seAssetToAssetType(assetToType ?? null);
    }
  }, [settings.assetOrAssetType, settings.mode, assetData, settings.dashboardOrTemplate]);
  const skipTemplates =
    !settings.isChildWidget && (assetToAssetType === null || assetToAssetType <= 0);
  const { data: dashboardTemplates, isLoading: isLoadingDashboardTemplatesList } =
    useGetDashboardTemplatesQuery(
      {
        assetTypeId: assetToAssetType ?? 0,
      },
      {
        skip: skipTemplates,
      },
    );
  const {
    isLoading: isLoadingDashboardTemplates,
    data: templateData,
    isSuccess: isTemplateSuccess,
    isError: isTemplateError,
  } = useGetDashboardTemplateDetailsQuery(settings.dashboard?.id ?? 0, {
    skip:
      settings.dashboardOrTemplate === 'dashboard' ||
      !settings.dashboard ||
      settings.dashboard?.id === 0 ||
      dashboardTemplates?.items?.find((template) => template.id === settings?.dashboard?.id) ===
        undefined ||
      router.pathname === '/measurement-browser',
    refetchOnMountOrArgChange: true,
  });

  const { data: assetMeasurements, isFetching: isMeasurementsFetching } =
    useGetAllMeasurementsQuery(
      {
        assetId: settings.assetOrAssetType ?? 0,
        customerId: activeCustomer?.id ?? 0,
      },
      {
        skip:
          settings.mode === 'template' ||
          settings.assetOrAssetType === null ||
          settings.assetOrAssetType <= 0 ||
          router.pathname === '/measurement-browser',
      },
    );

  const setDashboardTemplateToDashboard = () => {
    if (templateData && templateData?.data && assetMeasurements) {
      const templateDetailsData = JSON.parse(templateData.data) as {
        widget: DashboardState['widget'];
        topPanel: DashboardState['template']['topPanel'];
        chart: DashboardState['template']['chart'];
      };
      const metricToMeasurementMap: Record<string, string[]> = {};
      assetMeasurements?.forEach((measurement) => {
        if (measurement.metric_id !== null) {
          const metricIdStr = measurement.metric_id.toString();
          const measurementIdStr = measurement.id.toString();

          if (!metricToMeasurementMap[metricIdStr]) {
            metricToMeasurementMap[metricIdStr] = [];
          }

          metricToMeasurementMap[metricIdStr].push(measurementIdStr);
        }
      });
      templateDetailsData.widget.widgets.map((widget) => {
        if (widget.type === 'chart') {
          widget.settings.settings.dashboardOrTemplate =
            widget.settings.settings.dashboardOrTemplate ?? 'template';
          widget.settings.settings.assetOrAssetType = settings.assetOrAssetType ?? null;
          widget.settings.settings.mode = 'dashboard';
          if ('assetMeasure' in widget.settings.settings && widget.settings.settings.assetMeasure) {
            if (Array.isArray(widget.settings.settings.assetMeasure)) {
              const measureIds: string[] = [];
              if ('selectedTitles' in widget.settings.settings) {
                widget.settings.settings.selectedTitles.forEach((title) => {
                  if (metricToMeasurementMap[title]) {
                    measureIds.push(...metricToMeasurementMap[title]); // <-- Fix here
                  }
                });
                widget.settings.settings.selectedTitles = [];
              }
              widget.settings.settings.assetMeasure.push({
                assetId: settings.assetOrAssetType?.toString() ?? '',
                measureId: measureIds,
              });
            } else {
              const measureIds: string[] = [];
              if (
                'selectedDbMeasureId' in widget.settings.settings &&
                metricToMeasurementMap[widget.settings.settings.selectedDbMeasureId]
              ) {
                measureIds.push(
                  ...metricToMeasurementMap[widget.settings.settings.selectedDbMeasureId],
                );
                widget.settings.settings.selectedDbMeasureId = '';
              }
              widget.settings.settings.assetMeasure.assetId =
                settings.assetOrAssetType?.toString() ?? '';
              widget.settings.settings.assetMeasure.measureId = measureIds;
            }
          }
          return widget;
        }

        widget.settings.dashboardOrTemplate = widget.settings.dashboardOrTemplate ?? 'template';
        widget.settings.assetOrAssetType = settings.assetOrAssetType ?? null;
        widget.settings.mode = 'dashboard';
        if (widget.type === 'dashboard-widget') {
          const assetPath = assetTypesWithPathForAsset.find(
            (assetType) => assetType.value === settings.assetOrAssetType,
          );
          widget.settings.assetOption = {
            id: settings.assetOrAssetType ?? 0,
            label: assetPath?.label ?? '',
          };
        }
        if (widget.type === 'map') {
          widget.settings.markers = widget.settings.markers.map((marker) => {
            if (marker.selectedTitles.length > 0) {
              const measureIds: string[] = [];
              marker.selectedTitles.forEach((title) => {
                if (metricToMeasurementMap[title]) {
                  measureIds.push(...metricToMeasurementMap[title]); // <-- Fix here
                }
              });
              measureIds.forEach((measureId) => {
                marker.assetMeasures.push({
                  assetId: settings.assetOrAssetType?.toString() ?? '',
                  measureId: measureId,
                });
              });
              let labelUnits = {};
              measureIds.forEach((measureId) => {
                const measure = assetMeasurements?.find(
                  (measure) => measure.id === Number(measureId),
                );

                labelUnits = {
                  ...labelUnits,
                  [measureId]: {
                    label: measure?.tag ?? '',
                    unit: '',
                    value: '',
                  },
                };
              });
              marker.labelAndUnits = labelUnits;
            }
            marker.selectedTitles = [];
            return marker;
          });
        } else {
          if ('assetMeasure' in widget.settings && widget.settings.assetMeasure) {
            if (Array.isArray(widget.settings.assetMeasure)) {
              const measureIds: string[] = [];
              if ('selectedTitles' in widget.settings) {
                widget.settings.selectedTitles.forEach((title) => {
                  if (metricToMeasurementMap[title]) {
                    measureIds.push(...metricToMeasurementMap[title]); // <-- Fix here
                  }
                });
                widget.settings.selectedTitles = [];
              }
              if (widget.type === 'image') {
                // const selectedDbMeasureIdToName: {
                //   [key: string]: string;
                // } = {};
                // measureIds.forEach((measureId) => {
                //   const measureName = assetMeasurements?.find(
                //     (measure) => measure.id === Number(measureId),
                //   )?.tag;
                //   selectedDbMeasureIdToName[measureId] = measureName ?? '';
                // });
                // widget.settings.dbMeasureIdToName = selectedDbMeasureIdToName;
                // const measureIdToImageTextDetails: Record<string, ImageTextDetails> = {};
                // measureIds.forEach((measureId) => {
                //   const measureName = assetMeasurements?.find(
                //     (measure) => measure.id === Number(measureId),
                //   );
                //   const existingTextDetails =
                //     widget.settings.measureIdToImageTextDetails[measureName?.metric_id ?? ''];
                //   measureIdToImageTextDetails[measureId] = {
                //     label: measureName?.tag ?? '',
                //     unit: existingTextDetails?.unit ?? '',
                //     id: measureId,
                //     positionX: existingTextDetails?.positionX ?? 100,
                //     positionY: existingTextDetails?.positionY ?? 100,
                //     value: existingTextDetails?.value ?? '',
                //     dashboard: existingTextDetails?.dashboard ?? null,
                //     openDashboardInNewTab: existingTextDetails?.openDashboardInNewTab ?? false,
                //     dashboardOrTemplate: existingTextDetails?.dashboardOrTemplate ?? 'template',
                //     assetOrAssetType: existingTextDetails?.assetOrAssetType ?? null,
                //   };
                // });
                // widget.settings.measureIdToImageTextDetails = measureIdToImageTextDetails;
                widget.settings.dashboardOrTemplate =
                  widget.settings.dashboardOrTemplate ?? 'template';
                widget.settings.assetOrAssetType = settings.assetOrAssetType ?? null;
                widget.settings.imgDashboard = {
                  ...widget.settings.imgDashboard,
                  assetOrAssetType: settings.assetOrAssetType ?? null,
                  dashboard: settings.dashboard ?? null,
                  openDashboardInNewTab: settings.openDashboardInNewTab,
                  dashboardOrTemplate: settings.dashboardOrTemplate ?? 'dashboard',
                };
                widget.settings.mode = 'dashboard';
              }
              widget.settings.assetMeasure.push({
                assetId: settings.assetOrAssetType?.toString() ?? '',
                measureId: measureIds,
              });
            } else {
              const measureIds: string[] = [];
              if (
                'selectedDbMeasureId' in widget.settings &&
                metricToMeasurementMap[widget.settings.selectedDbMeasureId]
              ) {
                measureIds.push(...metricToMeasurementMap[widget.settings.selectedDbMeasureId]);
                widget.settings.selectedDbMeasureId = '';
              }
              if ('dbMeasureIdToName' in widget.settings) {
                const selectedDbMeasureIdToName: {
                  [key: string]: string;
                } = {};
                measureIds.forEach((measureId) => {
                  const measureName = assetMeasurements?.find(
                    (measure) => measure.id === Number(measureId),
                  )?.tag;
                  selectedDbMeasureIdToName[measureId] = measureName ?? '';
                });
                widget.settings.dbMeasureIdToName = selectedDbMeasureIdToName;
              }
              widget.settings.assetMeasure.assetId = settings.assetOrAssetType?.toString() ?? '';
              widget.settings.assetMeasure.measureId = measureIds;
            }
          }
        }
        if (widget.type === 'Diagram') {
          widget.settings.mode = 'dashboard';
          Object.keys(widget.settings.elementIdVariabels).forEach((elementId) => {
            const variables = widget.settings.elementIdVariabels[elementId];

            variables.forEach((variable) => {
              const measurementKey = variable.measurementId;
              variable.aggBy = variable.aggBy ?? 1;
              // Only replace if there's a mapping available
              if (metricToMeasurementMap[measurementKey]) {
                const mappedIds = metricToMeasurementMap[measurementKey];

                // Only assign the first mapped ID (if single select expected), or modify to handle arrays
                variable.assetId = settings.assetOrAssetType?.toString() ?? '';
                variable.measurementId = mappedIds[0];

                // Optional: clear label or other fields if needed
                // variable.label = '';
              }
            });
          });
          widget.settings.elementVariable = widget.settings.elementVariable.map((variable) => {
            const measurementKey = variable.measurementId;
            // Only replace if there's a mapping available
            if (metricToMeasurementMap[measurementKey]) {
              const mappedIds = metricToMeasurementMap[measurementKey];

              // Only assign the first mapped ID (if single select expected), or modify to handle arrays
              variable.assetId = settings.assetOrAssetType?.toString() ?? '';
              variable.measurementId = mappedIds[0];

              // Optional: clear label or other fields if needed
              // variable.label = '';
            }
            variable.aggBy = variable.aggBy ?? 1;
            return variable;
          });
        }
        return widget;
      });
      dispatch(
        dashboardSlice.actions.setDashboardCrumb({
          dashboardId: -2,
          title:
            dashboardLink?.dashboard?.title + '-' + templateData?.asset_template?.assetType?.name,
          templateId: templateData?.id ?? 0,
          assetId: settings.assetOrAssetType ?? undefined,
          assetType: assetToAssetType ?? undefined,
        }),
      );
      router.push(`/customer/${activeCustomer?.id}/dashboard/-2`);
      dispatch(dashboardSlice.actions.setCurrentDashboardId(-2));
      dispatch(
        dashboardSlice.actions.setCurrentDashboardTitle(
          dashboardLink?.dashboard?.title + '-' + templateData?.asset_template?.assetType?.name,
        ),
      );
      dispatch(
        dashboardSlice.actions.setWidget({
          widgets: templateDetailsData.widget.widgets,
          deleteWidgets: [],
          widgetLayout: templateDetailsData.widget.widgetLayout,
          lastWidgetId: templateDetailsData.widget.lastWidgetId,
        }),
      );
      dispatch(dashboardSlice.actions.setWidgetsLayout(templateDetailsData.widget.widgetLayout));
      dispatch(dashboardSlice.actions.setSamplePeriod(templateDetailsData.topPanel.samplePeriod));
      dispatch(
        dashboardSlice.actions.setRefreshTimeInterval(templateDetailsData.topPanel.refreshInterval),
      );
      dispatch(dashboardSlice.actions.setTimeRangeType(templateDetailsData.topPanel.timeRangeType));
      const minutes: number =
        TimeRangeOptions[templateDetailsData.topPanel.timeRangeType ?? 6].serverValue;
      const start = getPreviousDate(minutes);
      if (templateDetailsData.topPanel.timeRangeType !== 0) {
        dispatch(dashboardSlice.actions.setChartStartDate(new Date(start)));
        dispatch(dashboardSlice.actions.setChartEndDate(new Date()));
      } else {
        dispatch(
          dashboardSlice.actions.setChartStartDate(new Date(templateDetailsData.chart.startDate)),
        );
        dispatch(
          dashboardSlice.actions.setChartEndDate(new Date(templateDetailsData.chart.endDate)),
        );
      }
    }
  };
  const handleDashboardChange = () => {
    if (dashboardId === -2) {
      return false;
    }
    if (!hasDashboardPermission('dashboard.update', Role.POWER_USER)) {
      return false;
    }
    if (
      widgets.filter((widget) => widget.type !== 'chart' && widget.settings.isDirty).length > 0 ||
      deleteWidgets.length > 0 ||
      widgets.filter((widget) => widget.type === 'chart' && widget.settings?.settings.isDirty)
        .length > 0 ||
      isDashboardStateDirty
    ) {
      setConfirm(true);
      return true;
    }
    return false;
  };
  const openDashboard = () => {
    if (!settings.isChildWidget && handleDashboardChange()) return;
    const dashboard = dashboardLink.dashboard;
    const mode = settings.mode;
    const dashboardOrTemplate = settings.dashboardOrTemplate;
    if (mode === 'dashboard' && dashboard !== null) {
      // if (settings.openDashboardInNewTab) {
      //   console.log('settings.openDashboardInNewTab', settings.openDashboardInNewTab)
      //   window.open(
      //     `${router.basePath}/customer/${activeCustomer?.id}/dashboard/${dashboard.id}`,
      //     '_blank',
      //   );
      //   return;
      // }
      if (dashboardOrTemplate === 'dashboard') {
        dispatch(
          dashboardSlice.actions.setDashboardCrumb({
            dashboardId: dashboard.id,
            title: dashboard.title,
          }),
        );
        dispatch(dashboardSlice.actions.setCurrentDashboardId(dashboard.id));
        dispatch(dashboardSlice.actions.setCurrentDashboardTitle(dashboard.title));
        dispatch(
          dashboardSlice.actions.setWidget({
            widgets: [],
            deleteWidgets: [],
            lastWidgetId: 0,
            widgetLayout: [],
          }),
        );
        router.push(`/customer/${activeCustomer?.id}/dashboard/${dashboard.id}`);
      }
      if (dashboardOrTemplate === 'template') {
        setDashboardTemplateToDashboard();
      }
    }
    if (mode === 'template' && dashboard !== null) {
      if (dashboardOrTemplate === 'dashboard') {
        dispatch(
          dashboardSlice.actions.setDashboardCrumb({
            dashboardId: dashboard.id,
            title: dashboard.title,
          }),
        );
        dispatch(dashboardSlice.actions.setCurrentDashboardId(dashboard.id));
        dispatch(dashboardSlice.actions.setCurrentDashboardTitle(dashboard.title));
        router.push(`/customer/${activeCustomer?.id}/dashboard/${dashboard.id}`);
      }
      if (dashboardOrTemplate === 'template') {
        dispatch(dashboardSlice.actions.setCurrentDashboardId(0));
        dispatch(dashboardSlice.actions.setCurrentDashboardTitle(dashboard.title));
        dispatch(
          dashboardSlice.actions.setWidget({
            widgets: [],
            deleteWidgets: [],
            widgetLayout: [],
            lastWidgetId: 0,
          }),
        );
      }
    }
  };
  const setDashboard = () => {
    if (settings.dashboardOrTemplate === 'dashboard') {
      dispatch(
        dashboardSlice.actions.setDashboardCrumb({
          dashboardId: dashboardLink?.dashboard?.id ?? 0,
          title: dashboardLink?.dashboard?.title ?? '',
        }),
      );
      if (settings.openDashboardInNewTab) {
        window.open(
          `${router.basePath}/customer/${activeCustomer?.id}/dashboard/${dashboardLink.dashboard?.id}`,
          '_blank',
        );
        return;
      }
      router.push(`/customer/${activeCustomer?.id}/dashboard/${dashboardLink.dashboard?.id ?? 0}`);
      dispatch(dashboardSlice.actions.setCurrentDashboardId(dashboardLink.dashboard?.id ?? 0));
      dispatch(
        dashboardSlice.actions.setCurrentDashboardTitle(dashboardLink.dashboard?.title ?? ''),
      );
    }
    if (
      settings.dashboardOrTemplate === 'template' &&
      templateData &&
      templateData?.data &&
      assetMeasurements
    ) {
      if (settings.openDashboardInNewTab) {
        window.open(`${router.basePath}/customer/${activeCustomer?.id}/dashboard/-2`, '_blank');
        return;
      }
      //   dispatch(
      //     dashboardSlice.actions.setDashboardCrumb({
      //       dashboardId: -2,
      //       title:
      //         dashboardLink?.dashboard?.title + '-' + templateData?.asset_template?.assetType?.name,
      //     }),
      //   );
      // router.push(`/customer/${activeCustomer?.id}/dashboard/-2`);
      setDashboardTemplateToDashboard();
    }
  };
  return {
    confirm,
    setConfirm,
    setDashboard,
    openDashboard,
    isMeasurementsFetching,
    isLoadingDashboardTemplates,
    isTemplateError,
    isLoadingDashboardTemplatesList,
    dashboardTemplateExists:
      dashboardTemplates?.items?.find((item) => item.id === settings.dashboard?.id)?.id ?? 0,
  };
};

export default useCommonWidgetHelper;
