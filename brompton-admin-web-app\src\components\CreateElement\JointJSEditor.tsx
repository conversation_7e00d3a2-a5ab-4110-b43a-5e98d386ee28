import { dia, shapes } from '@joint/core';
import EditIcon from '@mui/icons-material/Edit';
import { Box, Button, IconButton } from '@mui/material';
import React, { useEffect, useRef, useState } from 'react';
import LiquidTank from '~/components/JointJs/DiagramComponent/LiquidTank';
import { theme } from '~/pages/_app';
import { cellNamespace } from '~/types/diagram';
import { hexToRgbA } from '~/utils/utils';
import Pump from '../JointJs/DiagramComponent/Pump';
import Battery from './Battery';
import EditModal from './EditModal';

class DraggableLabel extends shapes.standard.TextBlock {
  constructor(text: string, x: number, y: number) {
    super();
    this.position(x, y);
    this.resize(80, 30);
    this.attr({
      body: {
        fill: 'skyblue',
        stroke: '#000',
        strokeWidth: 1,
        rx: 5,
        ry: 5,
      },
      label: {
        text,
        textAnchor: 'middle',
        fontSize: 14,
        fontFamily: 'sans-serif',
        fill: '#000',
        cursor: 'pointer',
      },
    });
  }

  makeDraggable(paper: dia.Paper) {
    const labelView = this.findView(paper);
    if (labelView) {
      let isDragging = false;
      let offsetX = 0;
      let offsetY = 0;

      labelView.on('pointerdown', (evt: dia.Event, x: number, y: number) => {
        isDragging = true;
        offsetX = x - this.position().x;
        offsetY = y - this.position().y;
      });

      paper.on('cell:pointermove', (cellView, evt, x, y) => {
        if (isDragging && cellView === labelView) {
          this.position(x - offsetX, y - offsetY);
        }
      });

      paper.on('cell:pointerup', () => {
        isDragging = false;
      });
    }
  }
}

const DiagramCanvas: React.FC = () => {
  const canvasRef = useRef<HTMLDivElement | null>(null);
  const graphRef = useRef<dia.Graph | null>(null);
  const paperRef = useRef<dia.Paper | null>(null);
  const [selectedElements, setSelectedElements] = useState<dia.Element[]>([]);
  const [hoveredElement, setHoveredElement] = useState<dia.Element | null>(null);
  const [showEditIcon, setShowEditIcon] = useState(false);
  const [editModalOpen, setEditModalOpen] = useState(false);
  const [batteryModalOpen, setBatteryModalOpen] = useState<boolean>(false);
  const [currentBattery, setCurrentBattery] = useState<Battery | null>(null);
  const editIconTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    const handleShiftPan = (paper: dia.Paper) => {
      paper.on('blank:pointerdown', (evt) => {
        if (evt.shiftKey) {
          const startPosition = { x: evt.clientX ?? 0, y: evt.clientY ?? 0 };
          const initialOrigin = paper.translate();

          const onPointerMove = (moveEvt: any) => {
            const dx = moveEvt.clientX - startPosition.x;
            const dy = moveEvt.clientY - startPosition.y;
            paper.translate(initialOrigin.tx + dx, initialOrigin.ty + dy);
          };

          const onPointerUp = () => {
            document.removeEventListener('mousemove', onPointerMove);
            document.removeEventListener('mouseup', onPointerUp);
          };

          document.addEventListener('mousemove', onPointerMove);
          document.addEventListener('mouseup', onPointerUp);
        }
      });
    };
    if (!canvasRef.current) return;

    const graph = new dia.Graph({}, { cellNamespace: cellNamespace });
    graphRef.current = graph;

    const paper = new dia.Paper({
      el: canvasRef.current,
      model: graph,
      width: '93vw',
      height: '100vh',
      gridSize: 10,
      drawGrid: true,
      cellViewNamespace: shapes,
    });
    paperRef.current = paper;
    handleShiftPan(paper);

    paper.on('blank:mousewheel', (event, x, y, delta) => {
      const oldScale = paper.scale().sx;
      const newScale = delta > 0 ? oldScale * 1.1 : oldScale / 1.1;
      paper.scale(newScale, newScale);
    });

    paper.on('element:pointerclick', (elementView) => {
      const model = elementView.model;
      if (model instanceof Battery) {
        setCurrentBattery(model);
        setBatteryModalOpen(true);
      }
    });

    // Bounding box selection tool manually implemented
    const selectionBox = document.createElement('div');
    selectionBox.style.position = 'absolute';
    selectionBox.style.border = '1px dashed #333';
    selectionBox.style.display = 'none';
    canvasRef.current?.appendChild(selectionBox);

    let startX = 0;
    let startY = 0;

    paper.on('blank:pointerdown', (evt) => {
      if (evt.shiftKey) return;
      startX = evt.clientX ?? 0;
      startY = evt.clientY ?? 0;
      selectionBox.style.left = `${startX}px`;
      selectionBox.style.top = `${startY}px`;
      selectionBox.style.width = '0px';
      selectionBox.style.height = '0px';
      selectionBox.style.display = 'block';
    });

    paper.on('blank:pointermove', (evt) => {
      const currentX = evt.clientX ?? 0;
      const currentY = evt.clientY ?? 0;

      const width = Math.abs(currentX - startX);
      const height = Math.abs(currentY - startY);

      selectionBox.style.left = `${Math.min(startX, currentX)}px`;
      selectionBox.style.top = `${Math.min(startY, currentY)}px`;
      selectionBox.style.width = `${width}px`;
      selectionBox.style.height = `${height}px`;
    });

    paper.on('blank:pointerup', () => {
      const bbox = selectionBox.getBoundingClientRect();
      const selected =
        graphRef.current?.getElements().filter((element) => {
          const elementView = paper.findViewByModel(element);
          if (!elementView) return false;
          const elementBox = elementView.el.getBoundingClientRect();
          return (
            elementBox.left >= bbox.left &&
            elementBox.right <= bbox.right &&
            elementBox.top >= bbox.top &&
            elementBox.bottom <= bbox.bottom
          );
        }) || [];

      setSelectedElements(selected);
      selectionBox.style.display = 'none';
    });

    paper.on('blank:pointerdown', () => {
      setSelectedElements([]);
    });

    paper.on('element:pointermove', (elementView, evt, x, y) => {
      const model = elementView.model;
      if (model.get('type') === 'groupElement') {
        const deltaX = x - model.position().x;
        const deltaY = y - model.position().y;
        model.translate(deltaX, deltaY);
      }
    });

    paper.on('element:mouseenter', (elementView) => {
      const model = elementView.model;
      if (model instanceof DraggableLabel) return;
      setHoveredElement(model);
      if (editIconTimeoutRef.current) {
        clearTimeout(editIconTimeoutRef.current);
      }
      setShowEditIcon(true);
    });

    paper.on('element:mouseleave', () => {
      if (editIconTimeoutRef.current) {
        clearTimeout(editIconTimeoutRef.current);
      }
      editIconTimeoutRef.current = setTimeout(() => {
        setShowEditIcon(false);
        setHoveredElement(null);
      }, 600);
    });
  }, []);

  useEffect(() => {
    if (graphRef.current) {
      // Remove any existing selection highlight boxes
      graphRef.current.getElements().forEach((element) => {
        if (element.get('type') === 'selectionHighlight') {
          element.remove();
        }
      });

      selectedElements.forEach((element) => {
        const bbox = element.getBBox();
        const highlight = new shapes.standard.Rectangle();
        highlight.position(bbox.x - 5, bbox.y - 5);
        highlight.resize(bbox.width + 10, bbox.height + 10);
        highlight.attr({
          body: {
            fill: 'rgba(0, 0, 255, 0.1)',
            stroke: '#0000ff',
            strokeWidth: 1,
          },
        });
        highlight.set('type', 'selectionHighlight');
        highlight.addTo(graphRef.current!);
      });
    }
  }, [selectedElements]);

  const addLiquidTank = () => {
    if (!graphRef.current) return;
    const graph = graphRef.current;

    const tank = new LiquidTank();
    tank.position(100, 50);
    tank.attr({
      label: {
        text: '',
      },
    });
    tank.addTo(graph);
  };

  const addPump = () => {
    if (!graphRef.current) return;
    const graph = graphRef.current;

    const pump = new Pump();
    pump.position(100, 50);
    pump.addTo(graph);
  };

  const addBattery = () => {
    if (!graphRef.current) return;
    const graph = graphRef.current;

    const battery = new Battery({
      blockCount: 5, // Number of blocks inside the battery
    });
    battery.position(150, 100); // Set the position on the canvas

    battery.attr({
      // Optionally set or adjust attributes as needed
    });
    battery.addTo(graph); // Add the battery to the graph
  };

  const groupSelectedElements = () => {
    if (!graphRef.current || selectedElements.length === 0) return;
    if (selectedElements.length === 1) {
      alert('Cannot create a group with only one element. Please select more than one element.');
      return;
    }
    const graph = graphRef.current;

    // Create a valid group container
    const groupElement = new shapes.standard.Rectangle();
    groupElement.resize(1, 1);
    groupElement.position(100, 50);
    groupElement.attr({
      body: {
        fill: `rgba(${hexToRgbA(theme.palette.primary.main)}, 0.08)`,
        stroke: theme.palette.primary.main,
        strokeWidth: 1,
        strokeDasharray: 4,
      },
      label: {
        text: '',
      },
    });
    groupElement.set('type', 'groupElement');
    groupElement.addTo(graph);

    selectedElements.forEach((element) => {
      groupElement.embed(element);
    });
    groupElement.fitEmbeds({ padding: 15 });

    setSelectedElements([groupElement]);
  };

  const downloadDiagram = () => {
    if (graphRef.current) {
      const json = graphRef.current.toJSON();
      const jsonString = JSON.stringify(json);
      const blob = new Blob([jsonString], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = 'diagram.json';
      link.click();
      URL.revokeObjectURL(url);
    }
  };

  const uploadDiagram = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file && graphRef.current) {
      const reader = new FileReader();
      reader.onload = (e) => {
        const jsonString = e.target?.result;
        if (typeof jsonString === 'string' && graphRef.current) {
          const json = JSON.parse(jsonString);
          graphRef.current.fromJSON(json);
        }
      };
      reader.readAsText(file);
    }
  };

  const handleEditIconClick = () => {
    setEditModalOpen(true);
  };

  const handleSave = (variables: string[]) => {
    if (!graphRef.current || !paperRef.current) return;
    const graph = graphRef.current;
    const paper = paperRef.current;

    variables.forEach((variable, index) => {
      const label = new DraggableLabel(variable, 150, 100 + index * 40);
      label.addTo(graph);
      label.makeDraggable(paper);
    });

    setEditModalOpen(false);
  };

  return (
    <Box style={{ position: 'relative', width: '100%', height: '100%' }}>
      <Box ref={canvasRef} style={{ width: '100%', height: '100%', position: 'absolute' }} />

      {hoveredElement && showEditIcon && (
        <IconButton
          style={{
            position: 'absolute',
            top: hoveredElement.position().y - 40,
            left: hoveredElement.position().x + 120,
            zIndex: 10,
          }}
          onClick={handleEditIconClick}
        >
          <EditIcon />
        </IconButton>
      )}

      <EditModal open={editModalOpen} onClose={() => setEditModalOpen(false)} onSave={handleSave} />

      <Box
        style={{
          position: 'absolute',
          top: 10,
          right: 10,
          zIndex: 10,
          display: 'flex',
          flexDirection: 'column',
        }}
      >
        <Button variant="contained" onClick={addLiquidTank} style={{ marginBottom: '10px' }}>
          Add Liquid Tank
        </Button>

        <Button variant="contained" onClick={addPump} style={{ marginBottom: '10px' }}>
          Add Pump
        </Button>

        <Button variant="contained" onClick={addBattery} style={{ marginBottom: '10px' }}>
          Add Battery
        </Button>

        <Button
          variant="contained"
          onClick={groupSelectedElements}
          style={{ marginBottom: '10px' }}
        >
          Group Selected Elements
        </Button>

        <Button variant="outlined" onClick={downloadDiagram} style={{ marginBottom: '10px' }}>
          Download JSON
        </Button>

        <Button variant="outlined" component="label">
          Upload JSON
          <input type="file" accept=".json" hidden onChange={uploadDiagram} />
        </Button>
      </Box>
    </Box>
  );
};
export default DiagramCanvas;
