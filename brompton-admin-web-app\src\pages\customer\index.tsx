import { Box, Paper, Stack } from '@mui/material';
import { useRouter } from 'next/router';
import { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import PageName from '~/components/common/PageName/PageName';
import CustomerList from '~/components/common/TopPanel/CustomerList';
import { useGetCustomersQuery } from '~/redux/api/customersApi';
import { getActiveCustomer } from '~/redux/selectors/customerSelectors';
import { getTopPanelVisibility } from '~/redux/selectors/dashboardSelectors';
import { getDefaultCustomer } from '~/redux/selectors/userPreferences';
import { dashboardSlice } from '~/redux/slices/dashboardSlice';
import { Customer } from '~/types/customers';

const SelectCustomer: React.FC = () => {
  const { data: customerList, isLoading: isCustomerListLoading } = useGetCustomersQuery({});
  const dispatch = useDispatch();
  const activeCustomer = useSelector(getActiveCustomer);
  const { setActiveCustomer } = dashboardSlice.actions;
  const isTopPanel = useSelector(getTopPanelVisibility);
  const router = useRouter();
  const defaultCustomer = useSelector(getDefaultCustomer);

  const handleChangeCustomer = (e: React.SyntheticEvent, value: Customer | null) => {
    if (value === null) {
      return;
    }
    dispatch(dashboardSlice.actions.selectMainPanel('chart'));
    dispatch(dashboardSlice.actions.setCurrentDashboardTitle(''));
    dispatch(dashboardSlice.actions.resetDashboardCrumb());
    dispatch(dashboardSlice.actions.setCurrentDashboardId(-1));
    dispatch(setActiveCustomer(value));
    router.push(`/customer/${value.id}`);
  };
  useEffect(() => {
    if (customerList) {
      if (customerList.length === 1) {
        dispatch(setActiveCustomer(customerList[0]));
        router.push(`/customer/${customerList[0].id}`);
      }
      const selectedCustomer = customerList.find(
        (customer) => customer.id === Number(defaultCustomer),
      );
      if (selectedCustomer) {
        dispatch(dashboardSlice.actions.setActiveCustomer(selectedCustomer));
        router.push(`/customer/${selectedCustomer.id}`);
      }
    }
  }, [customerList, dispatch, defaultCustomer]);
  return (
    <Box
      p={2}
      pb={0}
      pt={0}
      sx={{ display: 'flex', height: (theme) => theme.spacing(8), pl: 3, pr: 3 }}
    >
      <Box
        sx={{
          flexGrow: 1,
          display: 'flex',
          alignItems: 'center',
        }}
      >
        <PageName name="Dashboard" />
      </Box>
      <Stack
        gap={0.5}
        p={1}
        width="100%"
        display="flex"
        sx={{
          p: 2,
          pt: 1,
          pb: 0,
          flexGrow: 1,
        }}
        overflow={'auto'}
      >
        <Paper elevation={3} sx={{ boxShadow: 0 }}>
          <Box
            display="flex"
            sx={{
              flexGrow: 1,
              overflow: 'auto',
              display: isTopPanel ? 'flex' : 'none',
            }}
            p={1}
            gap={1}
          >
            <Box display="flex">
              <CustomerList
                customerList={customerList}
                activeCustomer={activeCustomer}
                handleChangeCustomer={handleChangeCustomer}
                isCustomerListLoading={isCustomerListLoading}
              />
            </Box>
          </Box>
        </Paper>
      </Stack>
    </Box>
  );
};
export default SelectCustomer;
