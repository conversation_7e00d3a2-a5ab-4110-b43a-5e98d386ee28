import { Box, Button } from '@mui/material';
import { FC, useRef, useState } from 'react';
import CreateMeasureAlert from '~/components/dashboard/Alert/CreateMeasureAlert';
import CustomDialog from '../CustomDialog';

interface IAddAlertDialoge {
  open: boolean;
  close: () => void;
}
const AddAlertDialoge: FC<IAddAlertDialoge> = ({ open, close }) => {
  const alertFormRef = useRef<{ submitForm: () => void }>(null);
  const [status, setStatus] = useState({ isCreateLoading: false, isSuccess: false });

  const handleUpdate = () => {
    if (alertFormRef.current) {
      alertFormRef.current.submitForm();
    }
  };

  return (
    <CustomDialog
      maxWidth="md"
      open={open}
      onClose={close}
      title={<></>}
      content={
        <>
          <CreateMeasureAlert ref={alertFormRef} setStatus={setStatus} />
        </>
      }
      dialogActions={
        <Box sx={{ width: '100%', display: 'inherit', justifyContent: 'space-between' }}>
          <Button variant="outlined" onClick={close} color="primary">
            Cancel
          </Button>

          <Button
            disabled={status.isCreateLoading || status.isSuccess}
            variant="contained"
            onClick={handleUpdate}
            color="primary"
          >
            Submit
          </Button>
        </Box>
      }
    />
  );
};

export default AddAlertDialoge;
