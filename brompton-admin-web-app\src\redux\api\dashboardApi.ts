import { authApi } from '~/redux/api/authApi';
import {
  CreateDashboardParam,
  DashboardCollection,
  DashboardDetails,
  DashboardParams,
  TimeRangeOptions,
} from '~/types/dashboard';
import {
  DataWidget,
  isAlertWidgetType,
  isChartWidgetType,
  isDiagramWidgetType,
  isImageStatsType,
  isImageWidgetType,
  isKPIBarChartWidgetType,
  isKPIColorBoxWidgetType,
  isKPIPercentageWidgetType,
  isKPITableWidgetType,
  isKPIValueIndicatorWidgetType,
  isMapWidgetType,
  isRealTimeChartWidgetType,
  isStatsWidgetType,
  isTableWidgetType,
  isTitleWidgetType,
  isWeatherWidgetType,
  WidgetCommonSettings,
} from '~/types/widgets';
import { getPreviousDate, getPreviousDateRelativeToEndTime } from '~/utils/utils';
import { DEFAULT_LEGEND } from '../slices/dashboardSlice';

const setDefaultCommonWidgetSettings = <T extends WidgetCommonSettings>(widget: T) => {
  if (widget.isValid === undefined) {
    widget.isValid = true;
  }
  if (widget.dashboardOrTemplate === undefined) {
    widget.dashboardOrTemplate = 'dashboard';
  }
  if (widget.assetOrAssetType === undefined) {
    widget.assetOrAssetType = null;
  }
};
const setOverridenSettings = <T extends DataWidget>(widget: T, globalendDate: number) => {
  widget.overrideGlobalSettings = widget.overrideGlobalSettings ?? false;
  widget.isRelativeToGlboalEndTime = widget.isRelativeToGlboalEndTime ?? false;
  if (widget.overrideGlobalSettings) {
    widget.timeRange = widget.timeRange ?? 6;
    const timeRange = widget.timeRange;

    if (timeRange !== 0) {
      const minutes = TimeRangeOptions[timeRange].serverValue;

      // Use the appropriate function based on `isRelativeToGlboalEndTime`
      if (widget.isRelativeToGlboalEndTime) {
        widget.startDate = getPreviousDateRelativeToEndTime(minutes, globalendDate);
        widget.endDate = globalendDate;
      } else {
        widget.startDate = getPreviousDate(minutes);
        widget.endDate = new Date().getTime();
      }
    }
  }
};
export const dashboardApi = authApi
  .enhanceEndpoints({
    addTagTypes: ['Dashboard', 'Customer'],
  })
  .injectEndpoints({
    endpoints: (builder) => ({
      createDashboard: builder.mutation<DashboardParams, CreateDashboardParam>({
        query: ({
          customerId,
          data,
          title,
          description,
          asset_id,
          dashboard_template_id,
          favourite,
        }) => {
          return {
            url: `/v0/customers/${customerId}/dashboards`,
            method: 'POST',
            body: {
              customerId,
              data,
              title,
              description,
              asset_id,
              dashboard_template_id,
              favourite,
            },
          };
        },
        invalidatesTags: (result, error, { customerId }) => [{ type: 'Dashboard' }],
      }),
      getDashboardByCustomerId: builder.query<
        DashboardCollection,
        { customerId: number; search: string | null }
      >({
        // query: ({ customerId, search }) => `/customers/${customerId}/dashboards`,
        query: ({ customerId, search }) => {
          if (search === null) {
            return {
              url: `/v0/customers/${customerId}/dashboards`,
            };
          } else {
            return {
              url: `/v0/customers/${customerId}/dashboards`,
              params: {
                search,
              },
            };
          }
        },
        transformResponse(response: DashboardCollection) {
          return response;
        },
        providesTags: (result, error, { customerId }) => [
          { type: 'Dashboard' },
          { type: 'Customer', id: customerId },
        ],
      }),
      getUserDashboardDetails: builder.query<
        DashboardDetails,
        { dashboardId: number; customerId: number }
      >({
        query: ({ dashboardId, customerId }) =>
          `/v0/customers/${customerId}/dashboards/${dashboardId}`,
        transformResponse(response: DashboardDetails) {
          response.data = JSON.parse(response.data as unknown as string);
          response.data.topPanel = {
            assetTz: response.data.topPanel.assetTz ?? true,
            isVisible: response.data.topPanel.isVisible || true,
            samplePeriod: response.data.topPanel.samplePeriod || 2,
            timeRangeType: response.data.topPanel.timeRangeType ?? 6,
            refreshInterval: response.data.topPanel.refreshInterval || -1,
          };
          response.data.isDirty = false;
          response.data.tree = {
            ...response.data.tree,
          };
          response.data.chart = {
            ...response.data.chart,
          };
          response.data.dateFormat = response.data.dateFormat || 0;
          response.data.rightSideBar = response.data.rightSideBar || false;
          response.data.rightSideBarActiveTab =
            response.data.rightSideBarActiveTab || '/icons/alerts.svg';
          response.data.fullScreen = response.data.fullScreen || false;
          response.data.kisok = response.data.kisok || false;
          response.data.widget.deleteWidgets = response.data.widget.deleteWidgets || [];
          response.data.enableZoom = response.data.enableZoom ?? false;

          // Handle responsive layouts
          response.data.desktopMobile = response.data.desktopMobile ?? 0; // Default to desktop
          if (response.data.responsiveLayouts) {
            // If responsive layouts exist, use the appropriate layout based on desktopMobile
            const currentMode = response.data.desktopMobile === 0 ? 'desktop' : 'mobile';
            response.data.widget.widgetLayout =
              response.data.responsiveLayouts[currentMode]?.widgetLayout ||
              response.data.widget.widgetLayout ||
              [];
            response.data.widget.widgets =
              response.data.responsiveLayouts[currentMode]?.widgets ||
              response.data.widget.widgets ||
              [];
          } else {
            // If no responsive layouts, create them from current widgetLayout for backward compatibility
            const currentLayout = response.data.widget.widgetLayout || [];
            const currentWidget = response.data.widget.widgets || [];
            response.data.responsiveLayouts = {
              desktop: { widgetLayout: [...currentLayout], widgets: [...currentWidget] },
              mobile: { widgetLayout: [...currentLayout], widgets: [...currentWidget] },
            };
          }
          response.data.widget.widgets.forEach((widget) => {
            if (isChartWidgetType(widget)) {
              widget.settings.settings.mode = widget.settings.settings.mode || 'dashboard';
              setDefaultCommonWidgetSettings(widget.settings.settings);
              setOverridenSettings(widget.settings.settings, new Date().getTime());
              widget.settings.settings.isRealTime = widget.settings.settings.isRealTime ?? false;
              widget.settings.settings.retainPeriod = widget.settings.settings.retainPeriod ?? 1;
              widget.settings.settings.refreshInterval =
                widget.settings.settings.refreshInterval ?? 10;
              widget.settings.settings.openDashboardInNewTab =
                widget.settings.settings?.openDashboardInNewTab ?? false;
            } else {
              widget.settings.mode = widget.settings.mode || 'dashboard';
              widget.settings.openDashboardInNewTab =
                widget.settings?.openDashboardInNewTab ?? false;
              if (!isRealTimeChartWidgetType(widget)) {
                setDefaultCommonWidgetSettings(widget.settings);
                widget.settings.isRealTime = widget.settings.isRealTime ?? false;
                widget.settings.retainPeriod = widget.settings.retainPeriod ?? 1;
                widget.settings.refreshInterval = widget.settings.refreshInterval ?? 10;
              }
              if (!isTitleWidgetType(widget) && !isWeatherWidgetType(widget)) {
                setOverridenSettings(widget.settings, new Date().getTime());
                widget.settings.dashboard = widget.settings.dashboard ?? null;
              }
            }
            if (isRealTimeChartWidgetType(widget)) {
              widget.settings.dbMeasureIdToName = widget.settings?.dbMeasureIdToName ?? {};
              widget.settings.assetMeasure = {
                assetId: widget.settings.assetMeasure?.assetId ?? '',
                measureId: widget.settings.assetMeasure?.measureId ?? [],
              };
            }
            if (isImageWidgetType(widget) && !widget.settings.measureIdToImageTextDetails) {
              widget.settings.measureIdToImageTextDetails = {};
              const { labelAndUnits, svgTexts } = widget.settings;
              svgTexts.forEach((svgText: { id: any; position: any }) => {
                const { id, position } = svgText;
                if (labelAndUnits) {
                  const { label = '', unit = '' } = labelAndUnits[id];
                  widget.settings.measureIdToImageTextDetails[id] = {
                    id,
                    value: '',
                    label,
                    unit,
                    positionX: position.x,
                    positionY: position.y,
                    dashboard: null,
                    openDashboardInNewTab: false,
                    dashboardOrTemplate: 'dashboard',
                    assetOrAssetType: null,
                  };
                }
              });
              if (widget.settings.selectedTitles === undefined) {
                widget.settings.selectedTitles = [];
              }
              widget.settings.dashboard = widget.settings.dashboard || null;
            }
            if (isImageStatsType(widget)) {
              widget.settings.samples = widget.settings.samples ?? [];
              widget.settings.placement = widget.settings.placement ?? 'Image-Center';
              widget.settings.imageSize = widget.settings.imageSize ?? 'Medium';
              widget.settings.dbMeasureIdToName = widget.settings?.dbMeasureIdToName ?? {};
            }
            if (isKPIBarChartWidgetType(widget)) {
              widget.settings.dbMeasureIdToName = widget.settings?.dbMeasureIdToName ?? {};
              widget.settings.selectedDbMeasureId = widget.settings.selectedDbMeasureId ?? null;
              widget.settings.overrideBarColor = widget.settings.overrideBarColor ?? false;
              widget.settings.barColor = widget.settings.barColor ?? '#000000';
              widget.settings.showPrevious = widget.settings.showPrevious ?? false;
              widget.settings.title = {
                fontSize: widget.settings.title?.fontSize || 12,
                fontWeight: widget.settings.title?.fontWeight || 'bolder',
                color: widget.settings.title?.color || '#000000',
                isVisible: widget.settings.title?.isVisible || false,
                value: widget.settings.title?.value || '',
              };
              widget.settings.selectedSamplePeriod = widget.settings.selectedSamplePeriod ?? 'D';
            }
            if (isImageWidgetType(widget)) {
              widget.settings.measureIdToImageTextDetails =
                widget.settings.measureIdToImageTextDetails ?? {};
              // const { labelAndUnits, svgTexts } = widget.settings;
              // svgTexts.forEach((svgText: { id: any; position: any }) => {
              //   const { id, position } = svgText;
              //   if (labelAndUnits) {
              //     const { label = '', unit = '' } = labelAndUnits[id];
              //     widget.settings.measureIdToImageTextDetails[id] = {
              //       id,
              //       value: '',
              //       label,
              //       unit,
              //       positionX: position.x,
              //       positionY: position.y,
              //     };
              //   }
              // });
              widget.settings.allowUpload = widget.settings?.allowUpload ?? false;
              widget.settings.uploadedImage = widget.settings?.uploadedImage ?? null;
              widget.settings.imgDashboard = {
                dashboard: widget.settings?.imgDashboard?.dashboard ?? null,
                openDashboardInNewTab:
                  widget.settings?.imgDashboard?.openDashboardInNewTab ?? false,
                dashboardOrTemplate:
                  widget.settings?.imgDashboard?.dashboardOrTemplate ?? 'template',
                assetOrAssetType: widget.settings?.imgDashboard?.assetOrAssetType ?? null,
              };
              widget.settings.assetMeasure = [...(widget.settings?.assetMeasure ?? [])];
              widget.settings.dbMeasureIdToName = widget.settings?.dbMeasureIdToName ?? {};
              widget.settings.isDirty = widget.settings.isDirty || false;
              widget.settings.samplePeriod = widget.settings.samplePeriod || 2;
              widget.settings.overrideAssetTz = widget.settings.overrideAssetTz || false;
              widget.settings.overrideAssetTzValue = widget.settings.overrideAssetTzValue || false;
              widget.settings.isDirty = widget.settings.isDirty || false;
              widget.settings.aggBy = widget.settings.aggBy || 1;
              widget.settings.globalSamplePeriod = widget.settings.globalSamplePeriod || false;
              widget.settings.overrideGlobalSettings =
                widget.settings.overrideGlobalSettings || false;
              widget.settings.dashboard = widget.settings.dashboard || null;
            }
            if (isStatsWidgetType(widget)) {
              widget.settings.assetMeasure = {
                assetId: widget.settings.assetMeasure?.assetId ?? '',
                measureId: widget.settings.assetMeasure?.measureId ?? [],
              };
              widget.settings.min = {
                label: widget.settings.min?.label || 'Min',
                fontWeight: widget.settings.min?.fontWeight || 'bolder',
                fontSize: widget.settings.min?.fontSize || 12,
                color: widget.settings.min?.color || '#000000',
              };
              widget.settings.max = {
                label: widget.settings.max?.label || 'Max',
                fontWeight: widget.settings.max?.fontWeight || 'bolder',
                fontSize: widget.settings.max?.fontSize || 12,
                color: widget.settings?.max?.color || '#000000',
              };
              widget.settings.current = {
                label: widget.settings.current?.label || 'Last Value',
                fontWeight: widget.settings.current?.fontWeight || 'bolder',
                fontSize: widget.settings.current?.fontSize || 12,
                color: widget.settings.current?.color || '#000000',
              };
              widget.settings.delta = {
                label: widget.settings.delta?.label || 'Delta',
                fontWeight: widget.settings.delta?.fontWeight || 'bolder',
                fontSize: widget.settings.delta?.fontSize || 12,
                color: widget.settings.delta?.color || '#000000',
              };
              (widget.settings.sumBorder = widget.settings.sumBorder ?? false),
                (widget.settings.sum = {
                  label: widget.settings.sum?.label || 'Sum',
                  fontWeight: widget.settings.sum?.fontWeight || 'bolder',
                  fontSize: widget.settings.sum?.fontSize || 12,
                  color: widget.settings.sum?.color || '#000000',
                });
              widget.settings.total = {
                label: widget.settings.total?.label || 'Total',
                fontWeight: widget.settings.total?.fontWeight || 'bolder',
                fontSize: widget.settings.total?.fontSize || 12,
                color: widget.settings.total?.color || '#000000',
              };
              widget.settings.avg = {
                label: widget.settings.avg?.label || 'Avg',
                fontWeight: widget.settings.avg?.fontWeight || 'bolder',
                fontSize: widget.settings.avg?.fontSize || 12,
                color: widget.settings.avg?.color || '#000000',
              };
              widget.settings.fontWeight = widget.settings.fontWeight || 'bolder';
              widget.settings.fontSize = widget.settings.fontSize || 12;
              widget.settings.samplePeriod = widget.settings.samplePeriod || 2;
              widget.settings.selectedAssetId = widget.settings.selectedAssetId ?? '';
              widget.settings.selectedDbMeasureId = widget.settings.selectedDbMeasureId ?? '';
              widget.settings.overrideAssetTz = widget.settings.overrideAssetTz || false;
              widget.settings.overrideAssetTzValue = widget.settings.overrideAssetTzValue || false;
              widget.settings.isDirty = widget.settings.isDirty || false;
              widget.settings.dashboard = widget.settings.dashboard || null;
            }
            if (isMapWidgetType(widget)) {
              widget.settings.overrideAssetTz = widget.settings.overrideAssetTz || false;
              widget.settings.overrideAssetTzValue = widget.settings.overrideAssetTzValue || false;
              widget.settings.isDirty = widget.settings.isDirty || false;
              widget.settings.zoomLevel = widget.settings.zoomLevel || 5;
              widget.settings.changeMapCenter = widget.settings.changeMapCenter || false;
              widget.settings.aggBy = widget.settings.aggBy || 1;
              widget.settings.markers = widget.settings.markers || [];
              widget.settings.mapCenter = widget.settings.mapCenter || {
                lat: -0.257195,
                lon: -51.581861,
              };
              widget.settings.globalSamplePeriod = widget.settings.globalSamplePeriod || false;
              widget.settings.samplePeriod = widget.settings.samplePeriod || 2;
              widget.settings.overrideGlobalSettings =
                widget.settings.overrideGlobalSettings || false;
              widget.settings.dashboard = widget.settings.dashboard || null;
            }
            if (isChartWidgetType(widget)) {
              widget.settings.settings.isDirty = widget.settings.settings.isDirty || false;
              if (widget.settings.chartType === 'bar') {
                widget.settings.settings.showStacked = {
                  show: widget.settings.settings.showStacked?.show || false,
                };
              }
              if (widget.settings.chartType === 'scatter' || widget.settings.chartType === 'bar') {
                widget.settings.settings.showRangeSlider =
                  widget.settings.settings?.showRangeSlider ?? false;
                widget.settings.settings.dbMeasureIdToAnnotation =
                  widget.settings.settings?.dbMeasureIdToAnnotation ?? {};
                widget.settings.settings.dbMeasureIdToName =
                  widget.settings.settings?.dbMeasureIdToName ?? {};
                widget.settings.settings.showDelta = widget.settings.settings.showDelta ?? false;
                widget.settings.settings.deltaLabel =
                  widget.settings.settings.deltaLabel ?? 'Delta';
                widget.settings.settings.showSum = widget.settings.settings.showSum ?? false;
                widget.settings.settings.sumLabel = widget.settings.settings.sumLabel ?? 'Sum';
                widget.settings.settings.assetMeasure = [
                  ...(widget.settings.settings?.assetMeasure ?? []),
                ];
                // if (
                //   (widget.settings.chartType === 'scatter' ||
                //     widget.settings.chartType === 'bar') &&
                //   !widget.settings.settings.title.isVisible
                // ) {
                //   const assetMeasureTitles = widget.settings.settings.assetMeasure
                //     .flatMap((assetMeas) => assetMeas.measureId)
                //     .filter((measure) => measure.trim() !== '');
                //   // if ('dbMeasureIdToName' in widget.settings.settings) {
                //   //   const titles = Object.keys(widget.settings.settings.dbMeasureIdToName)
                //   //     .filter(
                //   //       (title) =>
                //   //         // eslint-disable-next-line @typescript-eslint/ban-ts-comment
                //   //         //@ts-ignore
                //   //         widget.settings.settings.dbMeasureIdToName[title] &&
                //   //         assetMeasureTitles.includes(title),
                //   //     )
                //   //     .filter((title) => title !== '')
                //   //     .map((title) =>
                //   //       // eslint-disable-next-line @typescript-eslint/ban-ts-comment
                //   //       //@ts-ignore
                //   //       formatMetricTag(widget.settings.settings.dbMeasureIdToName[title]),
                //   //     )
                //   //     .join(' Vs.');
                //   //   widget.settings.settings.title.value = titles;
                //   // }
                // }
                if (widget.settings.chartType === 'scatter') {
                  widget.settings.settings.selectedSparkTitle =
                    widget.settings.settings.selectedSparkTitle ?? undefined;
                  const { selectedSparkMeasure } = widget.settings.settings;
                  widget.settings.settings.selectedSparkMeasure =
                    selectedSparkMeasure !== undefined
                      ? {
                          assetId: selectedSparkMeasure.assetId ?? '',
                          measureId: selectedSparkMeasure.measureId ?? '',
                        }
                      : undefined;

                  widget.settings.settings.showStacked =
                    widget.settings.settings.showStacked ?? false;

                  widget.settings.settings.period = widget.settings.settings.period ?? '24hr';
                  widget.settings.settings.showForecast =
                    widget.settings.settings.showForecast ?? false;
                  widget.settings.settings.forecastColor =
                    widget.settings.settings.forecastColor ?? '#000000';

                  widget.settings.settings.meanColor =
                    widget.settings.settings.meanColor ?? '#000000';
                  widget.settings.settings.meanName = widget.settings.settings.meanName ?? 'Mean';
                  widget.settings.settings.meanStyle =
                    widget.settings.settings.meanStyle ?? 'solid';
                  widget.settings.settings.showMean = widget.settings.settings.showMean ?? false;
                  widget.settings.settings.meanName = widget.settings.settings.meanName ?? '';
                  widget.settings.settings.meanStyle =
                    widget.settings.settings.meanStyle ?? 'solid';
                  widget.settings.settings.meanColor =
                    widget.settings.settings.meanColor ?? '#000000';
                }
                widget.settings.settings.legendY =
                  widget.settings.settings.legendY || DEFAULT_LEGEND;
                widget.settings.settings.samplePeriod = widget.settings.settings.samplePeriod || 2;
                widget.settings.settings.overrideAssetTz =
                  widget.settings.settings.overrideAssetTz || false;
                widget.settings.settings.overrideAssetTzValue =
                  widget.settings.settings.overrideAssetTzValue || false;
                widget.settings.settings.dashboard = widget.settings.settings.dashboard || null;
              }
              if (widget.settings.chartType === 'sankey') {
                widget.settings.settings.showColor = widget.settings.settings.showColor ?? false;
                if (widget.settings.settings.showColor) {
                  widget.settings.settings.connections = widget.settings.settings.connections.map(
                    (connection) => {
                      connection.color = connection.color ? connection.color : '#DEDEDE';
                      return connection;
                    },
                  );
                }
              }
              if (widget.settings.chartType === 'indicator') {
                widget.settings.settings.margin = {
                  t: widget.settings.settings.margin?.t || 20,
                  r: widget.settings.settings.margin?.r || 40,
                  b: widget.settings.settings.margin?.b || 20,
                  l: widget.settings.settings.margin?.l || 20,
                };
                widget.settings.settings.overrideAssetTz =
                  widget.settings.settings.overrideAssetTz || false;
                widget.settings.settings.overrideAssetTzValue =
                  widget.settings.settings.overrideAssetTzValue || false;
              }
              if (widget.settings.chartType === 'bullet') {
                widget.settings.settings.assetMeasure = {
                  assetId: widget.settings.settings.assetMeasure?.assetId ?? '',
                  measureId: widget.settings.settings?.assetMeasure?.measureId ?? [],
                };
                widget.settings.settings.dbMeasureIdToName =
                  widget.settings?.settings?.dbMeasureIdToName ?? {};
                widget.settings.settings.margin = {
                  t: widget.settings.settings.margin?.t || 50,
                  r: widget.settings.settings.margin?.r || 40,
                  b: widget.settings.settings.margin?.b || 20,
                  l: widget.settings.settings.margin?.l || 20,
                };
                widget.settings.settings.overrideAssetTz =
                  widget.settings.settings.overrideAssetTz || false;
                widget.settings.settings.overrideAssetTzValue =
                  widget.settings.settings.overrideAssetTzValue || false;
              }
              if (widget.settings.chartType === 'heatmap') {
                widget.settings.settings.dbMeasureIdToName =
                  widget.settings?.settings?.dbMeasureIdToName ?? {};
                widget.settings.settings.overrideAssetTz =
                  widget.settings.settings.overrideAssetTz || false;
                widget.settings.settings.overrideAssetTzValue =
                  widget.settings.settings.overrideAssetTzValue || false;
                widget.settings.settings.pastelColor =
                  widget.settings.settings.pastelColor ?? 'Jet';
              }
            }
            if (isKPIColorBoxWidgetType(widget)) {
              widget.settings.assetMeasure = {
                assetId: widget.settings.assetMeasure?.assetId ?? '',
                measureId: widget.settings.assetMeasure?.measureId ?? [],
              };
              widget.settings.dbMeasureIdToName = widget.settings?.dbMeasureIdToName ?? {};
              widget.settings.title = {
                fontSize: widget.settings.title?.fontSize || 12,
                fontWeight: widget.settings.title?.fontWeight || 'bolder',
                color: widget.settings.title?.color || '#000000',
                isVisible: widget.settings.title?.isVisible || false,
                value: widget.settings.title?.value || '',
              };
              widget.settings.forecastColor = widget.settings.forecastColor ?? '#000000';
              widget.settings.showForecast = widget.settings.showForecast ?? false;
              widget.settings.showMean = widget.settings.showMean ?? false;
              widget.settings.meanColor = widget.settings.meanColor ?? '#000000';
              widget.settings.meanName = widget.settings.meanName ?? 'Mean';
              widget.settings.meanStyle = widget.settings.meanStyle ?? 'solid';

              widget.settings.period = widget.settings.period || '24hr';
              widget.settings.showForecast = widget.settings.showForecast || false;
              widget.settings.positive = {
                backgroundColor: widget.settings.positive?.backgroundColor || '#ffffff',
                font: {
                  color: widget.settings.positive?.font?.color || '#58ba54',
                },
              };
              widget.settings.negative = {
                backgroundColor: widget.settings.negative?.backgroundColor || '#ffffff',
                font: {
                  color: widget.settings.negative?.font?.color || '#FF0000',
                },
              };
              widget.settings.overrideAssetTz = widget.settings.overrideAssetTz || false;
              widget.settings.overrideAssetTzValue = widget.settings.overrideAssetTzValue || false;
              widget.settings.isDirty = widget.settings.isDirty || false;
              widget.settings.dashboard = widget.settings.dashboard || null;
              widget.settings.menuOption = widget.settings.menuOption ?? 'Stats';
              widget.settings.sparkLineColor = widget.settings.sparkLineColor ?? '#5959d1';
            }

            if (isKPIPercentageWidgetType(widget)) {
              widget.settings.dbMeasureIdToName = widget.settings?.dbMeasureIdToName ?? {};
              widget.settings.positiveColor = widget.settings.positiveColor || '#008000';
              widget.settings.negativeColor = widget.settings.negativeColor || '#FF0000';
              widget.settings.overrideAssetTz = widget.settings.overrideAssetTz || false;
              widget.settings.overrideAssetTzValue = widget.settings.overrideAssetTzValue || false;
              widget.settings.isDirty = widget.settings.isDirty || false;
              widget.settings.dashboard = widget.settings.dashboard || null;
              widget.settings.assetMeasure = {
                assetId: widget.settings.assetMeasure?.assetId ?? '',
                measureId: widget.settings.assetMeasure?.measureId ?? [],
              };
            }
            if (isKPITableWidgetType(widget)) {
              widget.settings.dbMeasureIdToName = widget.settings?.dbMeasureIdToName ?? {};
              widget.settings.assetMeasure = [...(widget.settings?.assetMeasure ?? [])];
              // if (!widget.settings.title.isVisible) {
              //   const assetMeasureTitles = widget.settings.assetMeasure
              //     .flatMap((assetMeas) => assetMeas.measureId)
              //     .filter((measure) => measure.trim() !== '');
              //   const titles = Object.keys(widget.settings.dbMeasureIdToName)
              //     .map((title) => {
              //       if (
              //         !widget.settings.dbMeasureIdToName[title] ||
              //         !assetMeasureTitles.includes(title)
              //       )
              //         return '';
              //       return formatMetricLabel(widget.settings.dbMeasureIdToName[title]);
              //     })
              //     .filter((title) => title !== '')
              //     .join(' Vs.');
              //   widget.settings.title.value = titles;
              // }
              widget.settings.overrideAssetTz = widget.settings.overrideAssetTz || false;
              widget.settings.overrideAssetTzValue = widget.settings.overrideAssetTzValue || false;
              widget.settings.isDirty = widget.settings.isDirty || false;
              widget.settings.dashboard = widget.settings.dashboard || null;
              const { max, min, mean, delta, sum, current } = widget.settings;
              if (max)
                widget.settings.max = {
                  label: max.label ?? 'Max',
                  show: max.show ?? false,
                };
              if (min)
                widget.settings.min = {
                  label: min.label ?? 'Min',
                  show: min.show ?? false,
                };
              if (mean)
                widget.settings.mean = {
                  label: mean.label ?? 'Mean',
                  show: mean.show ?? false,
                };
              if (delta)
                widget.settings.delta = {
                  label: delta.label ?? 'Delta',
                  show: delta.show ?? false,
                };
              if (sum)
                widget.settings.sum = {
                  label: sum.label ?? 'Sum',
                  show: sum.show ?? false,
                };
              if (current)
                widget.settings.current = {
                  label: current.label ?? 'Current',
                  show: current.show ?? false,
                };
            }
            if (isTableWidgetType(widget)) {
              widget.settings.dbMeasureIdToName = widget.settings?.dbMeasureIdToName ?? {};
              widget.settings.assetMeasure = [...(widget.settings?.assetMeasure ?? [])];
              // if (!widget.settings.title.isVisible) {
              //   const assetMeasureTitles = widget.settings.assetMeasure
              //     .flatMap((assetMeas) => assetMeas.measureId)
              //     .filter((measure) => measure.trim() !== '');
              //   const titles = Object.keys(widget.settings.dbMeasureIdToName)
              //     .map((title) => {
              //       if (
              //         !widget.settings.dbMeasureIdToName[title] ||
              //         !assetMeasureTitles.includes(title)
              //       )
              //         return '';
              //       return widget.settings.dbMeasureIdToName[title];
              //     })
              //     .filter((title) => title !== '')
              //     .join(' Vs.');
              //   widget.settings.title.value = titles;
              // }
              widget.settings.overrideAssetTz = widget.settings.overrideAssetTz || false;
              widget.settings.overrideAssetTzValue = widget.settings.overrideAssetTzValue || false;
              widget.settings.isDirty = widget.settings.isDirty || false;
              widget.settings.dashboard = widget.settings.dashboard || null;
            }
            if (isAlertWidgetType(widget)) {
              widget.settings.assetTypeMetrics = [...(widget.settings?.assetTypeMetrics ?? [])];
              widget.settings.assetTypes = [...(widget.settings?.assetTypes ?? [])];
              widget.settings.measurementTypes = [...(widget.settings?.measurementTypes ?? [])];
              widget.settings.assetMeasure = [...(widget.settings?.assetMeasure ?? [])];
              widget.settings.selectedTitles = [...(widget.settings?.selectedTitles ?? [])];
              widget.settings.isDirty = widget.settings.isDirty || false;
              widget.settings.dashboard = widget.settings.dashboard || null;
            }
            if (isDiagramWidgetType(widget)) {
              widget.settings.zoomLevel = widget.settings?.zoomLevel ?? 1;
              widget.settings.jsonFile = widget.settings.jsonFile ?? '';
              widget.settings.measureVariables = widget.settings.measureVariables ?? [];
              widget.settings.elementIdVariabels = widget.settings.elementIdVariabels ?? {};
              widget.settings.elementVariable = widget.settings.elementVariable ?? [];
              widget.settings.selectedDiagram = widget.settings.selectedDiagram ?? null;
              widget.settings.disableZoom = widget.settings.disableZoom ?? false;
            }
            if (isKPIValueIndicatorWidgetType(widget)) {
              widget.settings.assetMeasure = {
                assetId: widget.settings.assetMeasure?.assetId ?? '',
                measureId: widget.settings.assetMeasure?.measureId ?? [],
              };
              widget.settings.dbMeasureIdToName = widget.settings?.dbMeasureIdToName ?? {};
              widget.settings.overrideAssetTz = widget.settings.overrideAssetTz || false;
              widget.settings.overrideAssetTzValue = widget.settings.overrideAssetTzValue || false;
              widget.settings.isDirty = widget.settings.isDirty || false;
              widget.settings.dashboard = widget.settings.dashboard || null;
            }
            if (!response.data.topPanel.samplePeriod) {
              response.data.topPanel.samplePeriod = 2;
            }
          });
          if (!response.data.template) {
            response.data.template = {
              metrics: [],
              idToName: {},
              assetTemplate: 0,
              assetType: 0,
              templateId: 0,
              templateName: '',
              topPanel: {
                timeRangeType: 6,
                refreshInterval: -1,
                samplePeriod: 2,
                assetTz: true,
              },
              chart: {
                startDate: new Date(new Date().getTime() - 360 * 60000).getTime(),
                endDate: new Date().getTime(),
              },
            };
          } else {
            response.data.template = {
              ...response.data.template,
            };
          }
          return response;
        },
        providesTags: (result, error, { dashboardId }) => [
          { type: 'Dashboard' },
          { type: 'Dashboard', id: dashboardId },
        ],
      }),
      editDashboard: builder.mutation<void, DashboardDetails>({
        query: ({ id, data, title, customerId }) => {
          return {
            url: `/v0/customers/${customerId}/dashboards/${id}`,
            method: 'PATCH',
            body: {
              data,
              title,
            },
          };
        },
        invalidatesTags: (result, error, { id }) => [
          { type: 'Dashboard' },
          { type: 'Dashboard', id },
        ],
      }),
      markDefaultDashboard: builder.mutation<
        void,
        { dashboard_id: number; customerId: number; user_id: number; status: boolean }
      >({
        query: ({ dashboard_id, customerId, user_id, status }) => {
          return {
            url: `/v0/customers/${customerId}/dashboards/default/`,
            method: 'POST',
            body: {
              dashboard_id,
              user_id,
              status,
            },
          };
        },
        invalidatesTags: (result, error, { dashboard_id }) => [
          { type: 'Dashboard' },
          { type: 'Dashboard', dashboard_id },
        ],
      }),
      markFavoriteDashboard: builder.mutation<
        void,
        { dashboard_id: number; customerId: number; user_id: number; status: boolean }
      >({
        query: ({ dashboard_id, customerId, status }) => {
          return {
            url: `/v0/customers/${customerId}/dashboards/favorite`,
            method: 'POST',
            body: {
              dashboard_id,
              status: status,
            },
          };
        },
        invalidatesTags: (result, error, { dashboard_id }) => [
          { type: 'Dashboard' },
          { type: 'Dashboard', dashboard_id },
        ],
      }),
      deleteDashboard: builder.mutation<void, { dashboardId: number; customerId: number }>({
        query: ({ dashboardId, customerId }) => {
          return {
            url: `/v0/customers/${customerId}/dashboards/${dashboardId}`,
            method: 'DELETE',
          };
        },
        invalidatesTags: (result, error, { dashboardId }) => [
          { type: 'Dashboard' },
          { type: 'Dashboard', dashboardId },
        ],
      }),
    }),
  });

export const {
  useGetDashboardByCustomerIdQuery,
  useCreateDashboardMutation,
  useEditDashboardMutation,
  useGetUserDashboardDetailsQuery,
  useDeleteDashboardMutation,
  useMarkDefaultDashboardMutation,
  useMarkFavoriteDashboardMutation,
} = dashboardApi;
