apiVersion: apps/v1
kind: Deployment
metadata:
  name: admin-ui-${ENVIRONMENT}
  namespace: application
  labels:
    app.kubernetes.io/instance: admin-ui-${ENVIRONMENT}
    app.kubernetes.io/name: admin-ui-${ENVIRONMENT}
spec:
  selector:
    matchLabels:
      app.kubernetes.io/instance: admin-ui-${ENVIRONMENT}
      app.kubernetes.io/name: admin-ui-${ENVIRONMENT}
  replicas: 1
  template:
    metadata:
      labels:
        app.kubernetes.io/instance: admin-ui-${ENVIRONMENT}
        app.kubernetes.io/name: admin-ui-${ENVIRONMENT}
      annotations:
        instrumentation.opentelemetry.io/inject-nodejs: "openobserve-collector/openobserve-nodejs"  
    spec:
      containers:
      - name: admin-ui-${ENVIRONMENT}
        image: "${IMAGE_URI}"
        imagePullPolicy: Always
        ports:
        - containerPort: 8080
          name: http
        env:
        - name: NEXT_PUBLIC_ENVIRONMENT
          valueFrom:
            configMapKeyRef:
              name: admin-ui-${ENVIRONMENT}-config
              key: NEXT_PUBLIC_ENVIRONMENT
        - name: NEXT_PUBLIC_BE_ADMIN_API_URL
          valueFrom:
            configMapKeyRef:
              name: admin-ui-${ENVIRONMENT}-config
              key: NEXT_PUBLIC_BE_ADMIN_API_URL
        - name: NEXT_PUBLIC_TS_API_ENDPOINT
          valueFrom:
            configMapKeyRef:
              name: admin-ui-${ENVIRONMENT}-config
              key: NEXT_PUBLIC_TS_API_ENDPOINT
        - name: NEXT_PUBLIC_TS_API_ENDPOINT_V2
          valueFrom:
            configMapKeyRef:
              name: admin-ui-${ENVIRONMENT}-config
              key: NEXT_PUBLIC_TS_API_ENDPOINT_V2
        - name: NEXT_PUBLIC_OPENOBSERVE_APPLICATION_ID
          valueFrom:
            configMapKeyRef:
              name: admin-ui-${ENVIRONMENT}-config
              key: NEXT_PUBLIC_OPENOBSERVE_APPLICATION_ID
        - name: NEXT_PUBLIC_OPENOBSERVE_SERVICE
          valueFrom:
            configMapKeyRef:
              name: admin-ui-${ENVIRONMENT}-config
              key: NEXT_PUBLIC_OPENOBSERVE_SERVICE
        - name: NEXT_PUBLIC_OPENOBSERVE_VERSION
          valueFrom:
            configMapKeyRef:
              name: admin-ui-${ENVIRONMENT}-config
              key: NEXT_PUBLIC_OPENOBSERVE_VERSION
        - name: NEXT_PUBLIC_OPENOBSERVE_SITE
          valueFrom:
            configMapKeyRef:
              name: admin-ui-${ENVIRONMENT}-config
              key: NEXT_PUBLIC_OPENOBSERVE_SITE
        - name: NEXT_PUBLIC_OPENOBSERVE_ENV
          valueFrom:
            configMapKeyRef:
              name: admin-ui-${ENVIRONMENT}-config
              key: NEXT_PUBLIC_OPENOBSERVE_ENV
        - name: NEXT_PUBLIC_OPENOBSERVE_CLIENT_TOKEN
          valueFrom:
            configMapKeyRef:
              name: admin-ui-${ENVIRONMENT}-config
              key: NEXT_PUBLIC_OPENOBSERVE_CLIENT_TOKEN
        - name: NEXT_PUBLIC_OPENOBSERVE_ORGANIZATION_IDENTIFIER
          valueFrom:
            configMapKeyRef:
              name: admin-ui-${ENVIRONMENT}-config
              key: NEXT_PUBLIC_OPENOBSERVE_ORGANIZATION_IDENTIFIER
        - name: NEXT_PUBLIC_OPENOBSERVE_INSECURE_HTTP
          valueFrom:
            configMapKeyRef:
              name: admin-ui-${ENVIRONMENT}-config
              key: NEXT_PUBLIC_OPENOBSERVE_INSECURE_HTTP
        - name: NEXT_PUBLIC_OPENOBSERVE_API_VERSION
          valueFrom:
            configMapKeyRef:
              name: admin-ui-${ENVIRONMENT}-config
              key: NEXT_PUBLIC_OPENOBSERVE_API_VERSION
        resources:
          requests:
            memory: "256Mi"
            cpu: "100m"
          limits:
            memory: "500Mi"
            cpu: "250m"
        readinessProbe:
          httpGet:
            path: /
            port: 8080
          initialDelaySeconds: 10
          periodSeconds: 5
        livenessProbe:
          httpGet:
            path: /
            port: 8080
          initialDelaySeconds: 20
          periodSeconds: 15



