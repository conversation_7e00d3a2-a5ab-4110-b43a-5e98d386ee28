import { ThunkDispatch } from '@reduxjs/toolkit';
import { useRouter } from 'next/router';
import { Data, Layout } from 'plotly.js';
import { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { UnitOfMeasure } from '~/measurements/domain/types';
import { measuresApi } from '~/redux/api/measuresApi';
import { timeseriesApi } from '~/redux/api/timeseriesApi';
import { getAssetTz } from '~/redux/selectors/topPanleSelectors';
import { getDbMeasureIdToName } from '~/redux/selectors/treeSelectors';
import { getDateTimeFormat } from '~/redux/selectors/userPreferences';
import { RootState } from '~/redux/store';
import { AggByOptions, SamplePeriodOptions } from '~/types/dashboard';
import { AssetMeasurementDetails } from '~/types/measures';
import { SingleScatterTimeSeriesData } from '~/types/timeseries';
import {
  formatChartDate,
  formatMetricLabel,
  formatMetricTag,
  showThresholdValue,
} from '~/utils/utils';

type TrendResult = {
  tsData: SingleScatterTimeSeriesData;
  measureData: AssetMeasurementDetails;
  unitOfMeasures: UnitOfMeasure[];
};

type ChartData = {
  data: Data[];
  layout: Partial<Layout>;
  tsData?: number;
};

type Stats = {
  min: string;
  max: string;
  avg: string;
  total: string;
  unit: UnitOfMeasure | undefined;
};
type SingleChartData = {
  layout: Partial<Layout>;
  stats: Stats;
};

const transformScatterData = (
  result: TrendResult,
  selectedMeasureName: string,
  color: 'blue' | 'red' | 'black' | 'green',
  dateFormats: string,
  chartType: 'scatter' | 'bar',
  thresholdLimit: string,
  condition: string,
  rangeSlider: boolean,
): ChartData => {
  const traces: Data[] = [];
  const layout: Partial<Layout> = {
    showlegend: true,
    // title: numberOfCharts == 1 ? 'Trends' : formatMetricLabel(results[0]?.measureData.tag),

    annotations: [],
    yaxis: {
      // title: 'Unit Values',
      side: 'left',
    },
    legend: {
      orientation: 'h',
      y: rangeSlider ? -0.75 : -0.25,
    },
    // yaxis2: {
    //   // title: 'Unit Values',
    //   side: 'left',
    //   overlaying: 'y',
    // },
  };

  let chartNumber = 1;

  if (result && result.tsData) {
    const seriesData = result.tsData;
    const measureData = result.measureData;

    const unitOfMeasures = result.unitOfMeasures;
    const values = seriesData['ts,val'];
    if (!values) {
      return {
        data: [],
        layout,
        tsData: 0,
      };
    }
    const x = values.map(([timestamp]) => formatChartDate(new Date(timestamp)));
    const y = values.map(([, value]) => value * 1);

    const unitsOfMeasure = unitOfMeasures.find(
      (data) => data.id == measureData.unitOfMeasureId,
    ) || { id: '', name: '' };

    const title = formatMetricLabel(measureData.tag);
    traces.push({
      type: chartType,
      x,
      y,
      hovertemplate: `${title}: %{y} ${unitsOfMeasure.name}<br>Time: %{x}<extra></extra>'`,
      name: `${formatMetricTag(measureData.tag)} (${unitsOfMeasure.name})`,
      yaxis: 'y',
      mode: 'lines',
    });

    chartNumber++;
  }

  const singleChartData: SingleChartData = {
    stats: { avg: '', max: '', min: '', total: '', unit: undefined },
    layout: {
      annotations: [],
      shapes: [],
    },
  };
  if (thresholdLimit) {
    const limit = parseFloat(thresholdLimit);
    switch (condition) {
      case 'GT':
        condition = '(>)';
        break;
      case 'GE':
        condition = '(>=)';
        break;
      case 'LT':
        condition = '(<)';
        break;
      case 'LE':
        condition = '(<=)';
        break;
      default:
        condition = '';
        break;
    }
    const threshold = showThresholdValue(singleChartData, {
      thresholdColor: 'black',
      thresholdValue: limit,
      thresholdName: `Limit${condition}`,
      thresholdStyle: 'solid',
    });
    singleChartData.layout = threshold.layout;
    // if (!singleChartData.layout.annotations) {
    //   singleChartData.layout.annotations = [];
    // }

    // singleChartData.layout.annotations.push({
    //   xref: 'paper',
    //   yref: 'y',
    //   x: 1,
    //   xanchor: 'right',
    //   bgcolor: 'rgba(255, 255, 255)',
    //   y: limit,
    //   yanchor: 'bottom',
    //   text: `Limit${condition !== '' ? condition : ''}`,
    //   // text: `Limit: ${limit}`,

    //   showarrow: false,
    // });
    // if (!singleChartData.layout.shapes) {
    //   singleChartData.layout.shapes = [];
    // }
    // singleChartData.layout.shapes.push({
    //   type: 'line',
    //   x0: 0,
    //   x1: 1,
    //   xref: 'paper',
    //   y0: limit,
    //   y1: limit,
    //   yref: 'y',
    //   line: {
    //     color: 'black',
    //     width: 2,
    //     dash: 'solid',
    //   },
    // });
  }
  return {
    data: traces,
    layout: { ...layout, ...singleChartData.layout },
  };
};
type UseFetchScatterTestProps = {
  customerId: string;
  assetId: string;
  measureId: string;
  startDate: number;
  endDate: number;
  samplePeriod: number;
  aggBy: number;
  timeRangeType: number;
  color: 'blue' | 'red' | 'black' | 'green';
  chartType: 'scatter' | 'bar';
  rangeSlider: boolean;
};
export function useFetchScatterTest({
  customerId,
  assetId,
  measureId,
  startDate,
  endDate,
  samplePeriod,
  aggBy,
  timeRangeType,
  color,
  chartType,
  rangeSlider,
}: UseFetchScatterTestProps) {
  const dispatch = useDispatch<ThunkDispatch<RootState, any, any>>();
  const [allDataFetched, setAllDataFetched] = useState({
    chartData: [] as Data[],
    tsData: undefined as number | undefined,
    isLoading: true,
    isError: false,
    layoutData: {
      showlegend: true,
      title: 'Chart',
    } as Partial<Layout>,
  });
  const assetTz = useSelector(getAssetTz);
  const dateFormats = useSelector(getDateTimeFormat);
  const dbMeasureIdToName = useSelector(getDbMeasureIdToName);
  const router = useRouter();
  const [chartResults, setChartResults] = useState<TrendResult | undefined>(undefined);
  useEffect(() => {
    const fetchGaugeChartData = async () => {
      setAllDataFetched((prev) => {
        return {
          ...prev,
          isError: false,
          isLoading: true,
        };
      });
      const {
        data: measureData,
        isSuccess: isMeasureSuccess,
        isError: isMeasureError,
      } = await dispatch(
        measuresApi.endpoints?.getMeasurementById.initiate({
          customerId: Number(customerId),
          assetId,
          measId: measureId,
        }),
      );
      if (isMeasureError) {
        setAllDataFetched((prev) => {
          return {
            ...prev,
            isError: true,
            isLoading: false,
          };
        });
      }
      if (isMeasureSuccess && measureData) {
        const {
          data: tsData,
          isSuccess: isTsSuccess,
          isError: isTsError,
        } = await dispatch(
          timeseriesApi.endpoints.getMeasurementSeries.initiate({
            customerId: Number(customerId),
            measId: measureData.measurementId.toString(),
            start: startDate,
            end: endDate,
            agg: AggByOptions[aggBy].serverValue,
            agg_period: SamplePeriodOptions[samplePeriod].serverValue,
            timeRangeType: timeRangeType,
            assetTz: assetTz,
          }),
        );
        if (isTsError) {
          setAllDataFetched((prev) => {
            return {
              ...prev,
              isError: true,
              isLoading: false,
            };
          });
        }
        if (isTsSuccess && tsData) {
          const {
            data: unitOfMeasures,
            isSuccess: isUotSuccess,
            isError: isUotError,
          } = await dispatch(
            measuresApi.endpoints?.getUnitsOfMeasure.initiate({
              measurementTypeId: measureData.typeId,
            }),
          );
          if (isUotError) {
            setAllDataFetched((prev) => {
              return {
                ...prev,
                isError: true,
                isLoading: false,
              };
            });
          }
          if (isUotSuccess && unitOfMeasures) {
            setChartResults({
              tsData: tsData,
              measureData,
              unitOfMeasures,
            } as TrendResult);
          }
        }
      } else {
        return null;
      }
    };
    if (!measureId) return;

    fetchGaugeChartData();
  }, [
    customerId,
    assetId,
    measureId,
    startDate,
    endDate,
    samplePeriod,
    aggBy,
    timeRangeType,
    assetTz,
  ]);

  useEffect(() => {
    if (chartResults) {
      const chartData = transformScatterData(
        chartResults,
        dbMeasureIdToName[measureId],
        color,
        dateFormats,
        chartType,
        router.query?.limit as string,
        router.query?.condition as string,
        rangeSlider,
      );
      setAllDataFetched({
        chartData: chartData.data,
        isLoading: false,
        layoutData: chartData.layout,
        tsData: chartData.tsData,
        isError: false,
      });
    } else {
      if (router.query?.limit !== undefined) {
        return;
      } else {
        setAllDataFetched((prev) => {
          return {
            ...prev,
            isLoading: false,
            isError: true,
          };
        });
      }
    }
  }, [
    chartResults,
    dateFormats,
    chartType,
    router.query?.limit,
    router.query?.condition,
    rangeSlider,
  ]);
  return { ...allDataFetched, limit: router.query?.limit as string };
}
