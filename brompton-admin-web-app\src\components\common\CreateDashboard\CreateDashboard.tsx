import { Button, Tooltip, Typography } from '@mui/material';
import AddIcon from '@mui/icons-material/Add';
import { useDispatch, useSelector } from 'react-redux';
import {
  getCurrentDashboardId,
  getMainPanel,
  isDashboardDirty,
} from '~/redux/selectors/dashboardSelectors';
import { getDeletedWidgets, getWidgets } from '~/redux/selectors/widgetSelectors';
import { getActiveCustomer } from '~/redux/selectors/customerSelectors';
import { useRouter } from 'next/router';
import { useState } from 'react';
import { dashboardListSlice } from '~/redux/slices/dashboardListSlice';
import { dashboardSlice } from '~/redux/slices/dashboardSlice';
import CustomDialog from '../CustomDialog';
import CancelIcon from '@mui/icons-material/Cancel';
import { useHasAdminAccess } from '~/hooks/useHasAdminAccess';
import { Role, useRolePermission } from '~/hooks/useRolePermission';

const CreateDashboard = () => {
  const currentDashboardId = useSelector(getCurrentDashboardId);
  const isDashboardStateDirty = useSelector(isDashboardDirty);
  const widgets = useSelector(getWidgets);
  const deleteWidgets = useSelector(getDeletedWidgets);
  const mainPanel = useSelector(getMainPanel);
  const activeCustomer = useSelector(getActiveCustomer);
  const [confirm, setConfirm] = useState(false);
  const router = useRouter();
  const dispatch = useDispatch();
  const [navigation, setNavigation] = useState<string | null>(null);
  const { globalAdmin, admin } = useHasAdminAccess();
  const { hasDashboardPermission } = useRolePermission();
  const handleDashboardChange = () => {
    if (
      mainPanel === 'chart' &&
      activeCustomer &&
      (widgets.filter((widget) => widget.type !== 'chart' && widget.settings.isDirty).length > 0 ||
        deleteWidgets.length > 0 ||
        widgets.filter((widget) => widget.type === 'chart' && widget.settings?.settings.isDirty)
          .length > 0 ||
        isDashboardStateDirty) &&
      router.asPath.match(/\/dashboard\/\d+/) &&
      currentDashboardId > -1
    ) {
      setConfirm(true);
      return true;
    }
    return false;
  };
  return (
    <>
      {hasDashboardPermission('dashboard.create', Role.POWER_USER) ? (
        <Button
          startIcon={
            <Tooltip title="Add Dashboard" arrow>
              <AddIcon sx={{ fontWeight: 'bold' }} fontSize="large" />
            </Tooltip>
          }
          variant="contained"
          sx={{
            borderRadius: 2,
            width: 'max-content',
            '& .MuiButton-startIcon': {
              mr: 0,
              '& .MuiSvgIcon-root': {
                fontSize: '25px',
              },
            },
          }}
          onClick={() => {
            if (handleDashboardChange()) {
              setNavigation(`/customer/${activeCustomer?.id}/dashboard/0`);
            } else {
              dispatch(dashboardListSlice.actions.setCurrentDashboardId(-1));
              dispatch(dashboardSlice.actions.setNewDashboard());
              router.push(`/customer/${activeCustomer?.id}/dashboard/0`);
            }
          }}
        />
      ) : null}
      <CustomDialog
        title="You have unsaved changes."
        content={<Typography color={'error'}>Do you want to still proceed?</Typography>}
        dialogActions={
          <>
            <Button
              onClick={() => {
                setConfirm(false);
                setNavigation(null);
              }}
              variant="outlined"
              startIcon={<CancelIcon />}
            >
              Cancel
            </Button>
            <Button
              onClick={() => {
                setConfirm(false);
                router.push(navigation as string);
                setNavigation(null);
              }}
              variant="contained"
              color="error"
            >
              Proceed
            </Button>
          </>
        }
        onClose={() => setConfirm(false)}
        open={confirm}
      />
    </>
  );
};

export default CreateDashboard;
