import {
  Button,
  FormControl,
  FormHelperText,
  InputLabel,
  MenuItem,
  Select,
  SelectChangeEvent,
  Typography,
} from '@mui/material';
import CustomDialog from '../CustomDialog';
import CancelIcon from '@mui/icons-material/Cancel';
import SaveIcon from '@mui/icons-material/Save';
import { ReactNode, useState } from 'react';
import dayjs from 'dayjs';
import 'dayjs/locale/de';
import 'dayjs/locale/en-gb';
import 'dayjs/locale/zh-cn';
import { dateFormats } from '~/types/dashboard';
import { dashboardSlice } from '~/redux/slices/dashboardSlice';
import { getTimeFormat } from '~/redux/selectors/dashboardSelectors';
import { useDispatch, useSelector } from 'react-redux';
type UserPreferencesProps = {
  showUserPreferences: boolean;
  handleOpenUserPreferences: () => void;
};
const UserPreferences = ({
  showUserPreferences,
  handleOpenUserPreferences,
}: UserPreferencesProps) => {
  const currentTimeFormat = useSelector(getTimeFormat);
  const dispatch = useDispatch();
  const [selectedFormat, setSelectedFormat] = useState(currentTimeFormat);

  const handleChange = (event: SelectChangeEvent<number>, child: ReactNode) => {
    setSelectedFormat(event.target.value as number);
  };
  const handleUserPreference = () => {
    dispatch(dashboardSlice.actions.setDateFormat(selectedFormat));
    handleOpenUserPreferences();
  };
  return (
    <CustomDialog
      open={showUserPreferences}
      content={
        <>
          <FormControl fullWidth sx={{ mt: 2, mb: 2 }}>
            <InputLabel id="datetime-format-label">Select Date-Time Format</InputLabel>
            <Select
              labelId="datetime-format-label"
              id="datetime-format-select"
              value={selectedFormat}
              onChange={handleChange}
              label="Select Date-Time Format"
            >
              {dateFormats.sort().map((format, i) => (
                <MenuItem key={format} value={i}>
                  {format}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
          <FormHelperText>
            <Typography>
              Note: The selected format will be used for all date-time fields in the application.
            </Typography>
            <Typography fontWeight={'bold'}>
              Date will be like : {dayjs(new Date().getTime()).format(dateFormats[selectedFormat])}
            </Typography>
          </FormHelperText>
        </>
      }
      dialogActions={
        <>
          <Button onClick={handleOpenUserPreferences} variant="outlined" startIcon={<CancelIcon />}>
            Cancel
          </Button>
          <Button
            onClick={handleUserPreference}
            variant="contained"
            color="primary"
            startIcon={<SaveIcon />}
          >
            Save
          </Button>
        </>
      }
      title={'User Preferences'}
      onClose={handleOpenUserPreferences}
    />
  );
};

export default UserPreferences;
