import { shapes } from '@joint/core';

export default class Ellipse extends shapes.standard.Ellipse {
  rangePicker: Array<{ value: number; color: string }>;

  constructor(attributes = {}, options = {}) {
    // Call the base class constructor
    super(attributes, options);

    // Initialize the rangePicker property
    this.rangePicker = [];
  }
  defaults(): Partial<shapes.standard.EllipseAttributes> {
    return {
      ...super.defaults(),
    };
  }
  // Methods to interact with rangePicker
  addRange(value: number, color: string) {
    this.rangePicker.push({ value, color });
  }

  getRanges() {
    return this.rangePicker;
  }
}
