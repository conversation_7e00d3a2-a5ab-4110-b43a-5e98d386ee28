import { useGetCurrentUserDetailsQuery } from '~/redux/api/usersApi';
import { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';
import { getCustomerId } from '~/redux/selectors/customerSelectors';

export const useHasAdminAccess = () => {
  const { data, isLoading } = useGetCurrentUserDetailsQuery();
  const activeCustomerId = useSelector(getCustomerId);
  const [hasAdminAccess, setHasAdminAccess] = useState(false);
  const [hasGlobalAdminAccess, setHasGlobalAdminAccess] = useState(false);
  useEffect(() => {
    setHasAdminAccess(false);
    setHasGlobalAdminAccess(false);
    if (!isLoading && data) {
      if (data.global_role === 'ADMIN') {
        setHasAdminAccess(true);
        setHasGlobalAdminAccess(true);
      } else {
        const scopedRoles = data.scoped_roles;
        for (const scopedRole of scopedRoles) {
          if (scopedRole.role === 'ADMIN') {
            if (scopedRole.customer_ids.includes(activeCustomerId)) {
              setHasAdminAccess(true);
              setHasGlobalAdminAccess(false);
              break;
            }
          }
        }
      }
    }
  }, [activeCustomerId, data, isLoading]);

  return { admin: hasAdminAccess, globalAdmin: hasGlobalAdminAccess };
};
