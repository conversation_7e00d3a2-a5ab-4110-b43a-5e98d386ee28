import { dia, shapes as Jshapes } from '@joint/core';
import { ExpandLess, ExpandMore } from '@mui/icons-material';
import AddIcon from '@mui/icons-material/Add';
import {
  Autocomplete,
  Box,
  Button,
  Collapse,
  Divider,
  Grid,
  IconButton,
  Paper,
  TextField,
  Tooltip,
  Typography,
} from '@mui/material';
import React, { useEffect, useRef, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useGetAllDiagramsQuery } from '~/redux/api/diagramApi';
import { getDiagramId, getDiagramName } from '~/redux/selectors/diagramSelector';
import { diagramSlice } from '~/redux/slices/diagramSlice';
import { RootState } from '~/redux/store';
import NewElementDialog from '../DiagramComponent/DiagramComponentDialoge/NewElementDialog';
import Progress from '~/components/CreateElement/Progress';
import Pump from '../DiagramComponent/Pump';
import { HandValve } from '../DiagramComponent/HandValve';
import { ControlValve } from '../DiagramComponent/ControlValve';
import Battery from '~/components/CreateElement/Battery';
import LiquidTank from '~/components/CreateElement/Tank';
import { ConicTank } from '../DiagramComponent/ConicalTank';

export enum CATEGORY_E {
  BASIC = 'Basic',
  DOMAIN = 'Domain',
  DYNAMIC = 'Dynamic',
  CUSTOM = 'Custom',
  GENERAL = 'General',
}
// Define the element type
type Shape = {
  name: string;
  type: string;
  category: CATEGORY_E; // Category for the element
  imageUrl?: string | null;
};

export const generalShapesArray: Shape[] = [
  { name: 'Circle', type: 'circle', category: CATEGORY_E.BASIC },
  // { name: 'Circle', type: 'extended-circle', category: CATEGORY_E.BASIC },
  { name: 'Rectangle', type: 'rectangle', category: CATEGORY_E.BASIC },
  { name: 'Ellipse', type: 'ellipse', category: CATEGORY_E.BASIC },
  { name: 'Cylinder', type: 'cylinder', category: CATEGORY_E.BASIC },
  { name: 'Image', type: 'image', category: CATEGORY_E.BASIC },
  { name: 'Label', type: 'label', category: CATEGORY_E.BASIC },
  { name: 'Pump', type: 'pump', category: CATEGORY_E.DOMAIN },
  { name: 'Hand Valve', type: 'handValve', category: CATEGORY_E.DOMAIN },
  { name: 'Control Valve', type: 'controlValve', category: CATEGORY_E.DOMAIN },
  { name: 'Liquid Tank', type: 'liquidTank', category: CATEGORY_E.DOMAIN },
  { name: 'Conical Tank', type: 'conicalTank', category: CATEGORY_E.DOMAIN },
  // { name: 'Custom Rect', type: 'custom-Reactangle', category: CATEGORY_E.DOMAIN },
  // { name: 'Custom Circle', type: 'custom-circle', category: CATEGORY_E.DOMAIN },
  // { name: 'Custom Ellipse', type: 'custom-Ellipse', category: CATEGORY_E.DOMAIN },
  { name: 'Battery', type: 'battery', category: CATEGORY_E.DOMAIN },
  { name: 'Progress', type: 'progress', category: CATEGORY_E.DYNAMIC },
  {
    name: 'Compressor',
    type: 'compressor',
    category: CATEGORY_E.GENERAL,
    imageUrl: '/icons/compressor.svg',
  },
  {
    name: 'Flare Plain',
    type: 'flarePlain',
    category: CATEGORY_E.GENERAL,
    imageUrl: '/icons/flare-plain.svg',
  },
  {
    name: 'Gate Valve',
    type: 'gateValve',
    category: CATEGORY_E.GENERAL,
    imageUrl: '/icons/gate-valve.svg',
  },
  {
    name: 'Gray Text Box',
    type: 'grayTextBox',
    category: CATEGORY_E.GENERAL,
    imageUrl: '/icons/gray-text-box.svg',
  },
  {
    name: 'Hash Hut',
    type: 'hashHut',
    category: CATEGORY_E.GENERAL,
    imageUrl: '/icons/hash-hut.svg',
  },
  {
    name: 'Hydraulic Motor Valve',
    type: 'hydraulicMotorValve',
    category: CATEGORY_E.GENERAL,
    imageUrl: '/icons/hydraulic-motor-valve.svg',
  },
  {
    name: 'Hydraulic Motor Valve Dark',
    type: 'hydraulicMotorValveDark',
    category: CATEGORY_E.GENERAL,
    imageUrl: '/icons/hydraulic-motor-valve-dark.svg',
  },
  {
    name: 'Hydraulic Motor Valve White',
    type: 'hydraulicMotorValveWhite',
    category: CATEGORY_E.GENERAL,
    imageUrl: '/icons/hydraulic-motor-valve-white.svg',
  },
  {
    name: 'Inset Field Bordered',
    type: 'insetFieldBordered',
    category: CATEGORY_E.GENERAL,
    imageUrl: '/icons/inset-field-bordered.svg',
  },
  {
    name: 'Outset Field Bordered',
    type: 'outsetFieldBordered',
    category: CATEGORY_E.GENERAL,
    imageUrl: '/icons/outset-field-bordered.svg',
  },
  {
    name: 'Pid Arrow',
    type: 'pidArrow',
    category: CATEGORY_E.GENERAL,
    imageUrl: '/icons/pid-arrow.svg',
  },
  {
    name: 'Pneumatic Valve',
    type: 'pneumaticValve',
    category: CATEGORY_E.GENERAL,
    imageUrl: '/icons/pneumatic-valve.svg',
  },
  {
    name: 'Scrubber',
    type: 'scrubber',
    category: CATEGORY_E.GENERAL,
    imageUrl: '/icons/scrubber.svg',
  },
  { name: 'Tank', type: 'tank', category: CATEGORY_E.GENERAL, imageUrl: '/icons/tank.svg' },
  { name: 'Vru', type: 'vru', category: CATEGORY_E.GENERAL, imageUrl: '/icons/vru.svg' },
  {
    name: 'White Text Box',
    type: 'whiteTextBox',
    category: CATEGORY_E.GENERAL,
    imageUrl: '/icons/white-text-box.svg',
  },
];

const Palette: React.FC = () => {
  const dispatch = useDispatch();
  const diagramId = useSelector(getDiagramId);
  const diagramName = useSelector(getDiagramName);
  const customElements = useSelector((state: RootState) => state.diagram.customElements);
  const [openBasic, setOpenBasic] = useState(true);
  const [openDomain, setOpenDomain] = useState(true);
  const [openDynamic, setOpenDynamic] = useState(true);
  const [openCustom, setOpenCustom] = useState(true);
  const [createNewElement, setCreateNewElement] = useState(false);
  const [openGeneral, setOpenGeneral] = useState(true);
  const [hoveredShape, setHoveredShape] = useState<Shape | null>(null);
  const [hoverPosition, setHoverPosition] = useState({ x: 0, y: 0 });
  const panelRef = useRef<HTMLDivElement | null>(null);

  const { data: diagramsList, isFetching: fetchingDiagrams } = useGetAllDiagramsQuery();

  const { resetDiagram, setDiagramName, setDiagramId, setSelectedElement } = diagramSlice.actions;
  const handleToggle = (category: string) => {
    switch (category) {
      case CATEGORY_E.BASIC:
        setOpenBasic(!openBasic);
        break;

      case CATEGORY_E.DOMAIN:
        setOpenDomain(!openDomain);
        break;

      case CATEGORY_E.DYNAMIC:
        setOpenDynamic(!openDynamic);
        break;

      case CATEGORY_E.CUSTOM:
        setOpenCustom(!openCustom);

      case CATEGORY_E.GENERAL:
        setOpenGeneral(!openGeneral);

      default:
        break;
    }
  };

  useEffect(() => {
    const id = sessionStorage.getItem('diagramId');
    const name = sessionStorage.getItem('diagramName');
    if (id && name) {
      dispatch(setDiagramName(name));
      dispatch(setDiagramId(Number(id) ?? 0));
      dispatch(setSelectedElement(null));
    }
  }, []);

  useEffect(() => {
    if (panelRef.current) {
      const panelRect = panelRef.current.getBoundingClientRect();
      setHoverPosition((prev) => ({ ...prev, x: panelRect.right + 10 }));
    }
  }, []);

  const onDragStart = (event: React.DragEvent<HTMLDivElement>, shapeType: any) => {
    if (shapeType.category === CATEGORY_E.CUSTOM) {
      event.dataTransfer.setData('custom', JSON.stringify(shapeType.data));
    } else {
      event.dataTransfer.setData('shape', shapeType);
    }
  };

  const generateBasicElementSVG = (type: string): string | null => {
    const graph = new dia.Graph();
    const paper = new dia.Paper({
      el: document.createElement('div'),
      model: graph,
      interactive: false,
    });

    const elementSize = { width: 60, height: 60 };

    let element: dia.Element;

    switch (type) {
      // ✅ Basic Elements
      case 'circle':
        element = new Jshapes.standard.Circle({
          size: elementSize,
          position: { x: 20, y: 20 },
          attrs: { body: { fill: '#ccc', stroke: '#000' } },
        });
        break;

      case 'rectangle':
        element = new Jshapes.standard.Rectangle({
          size: { width: 80, height: 40 },
          position: { x: 10, y: 30 },
          attrs: { body: { fill: '#ccc', stroke: '#000' } },
        });
        break;

      case 'ellipse':
        element = new Jshapes.standard.Ellipse({
          size: { width: 80, height: 40 },
          position: { x: 10, y: 30 },
          attrs: { body: { fill: '#ccc', stroke: '#000' } },
        });
        break;

      case 'cylinder':
        element = new Jshapes.standard.Cylinder({
          size: elementSize,
          position: { x: 20, y: 20 },
          attrs: { body: { fill: '#ccc', stroke: '#000' } },
        });
        break;

      case 'label':
        element = new Jshapes.standard.TextBlock({
          size: { width: 80, height: 40 },
          position: { x: 10, y: 30 },
          attrs: { body: { fill: '#fff', stroke: '#000' } },
        });
        break;

      case 'image':
        element = new Jshapes.standard.Image({
          size: elementSize,
          position: { x: 20, y: 20 },
          attrs: { body: { fill: '#fff', stroke: '#000' } },
        });
        break;

      case 'progress':
        element = new Progress({
          size: { width: 50, height: 80 },
          position: { x: 25, y: 10 },
          attrs: { body: { fill: '#fff', stroke: '#000' } },
        });
        break;

      // ✅ Domain Elements
      case 'pump':
        element = new Pump({
          size: elementSize,
          position: { x: 20, y: 20 },
          attrs: { body: { fill: '#fff', stroke: '#000' } },
        });
        break;

      case 'handValve':
        element = new HandValve({
          size: { width: 100, height: 100 },
          position: { x: -8, y: 13 },
          attrs: { body: { fill: '#fff', stroke: '#000' } },
        });
        break;

      case 'controlValve':
        element = new ControlValve({
          size: { width: 75, height: 75 },
          position: { x: 6, y: 25 },
          attrs: { body: { fill: '#fff', stroke: '#000' } },
        });
        break;

      case 'liquidTank':
        element = new LiquidTank({
          size: elementSize,
          position: { x: 20, y: 20 },
          attrs: { body: { fill: '#fff', stroke: '#000' } },
        });
        break;

      case 'conicalTank':
        element = new ConicTank({
          size: elementSize,
          position: { x: 20, y: 20 },
          attrs: { body: { fill: '#fff', stroke: '#000' } },
        });
        break;

      case 'battery':
        element = new Battery({
          size: { width: 60, height: 80 },
          position: { x: 20, y: 8 },
          attrs: { body: { fill: '#fff', stroke: '#000' } },
        });
        break;

      default:
        return null;
    }

    graph.addCell(element);

    const elementView = paper.findViewByModel(element);
    if (!elementView) return null;

    const svgElement = elementView.vel.node.cloneNode(true) as SVGElement;

    const svgRoot = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
    svgRoot.setAttribute('xmlns', 'http://www.w3.org/2000/svg');
    svgRoot.setAttribute('width', '100');
    svgRoot.setAttribute('height', '100');
    svgRoot.appendChild(svgElement);

    const serializer = new XMLSerializer();
    return `data:image/svg+xml;base64,${btoa(serializer.serializeToString(svgRoot))}`;
  };

  const shapesArray: Shape[] = [
    // 🔹 Basic Elements
    {
      name: 'Circle',
      type: 'circle',
      category: CATEGORY_E.BASIC,
      imageUrl: generateBasicElementSVG('circle'),
    },
    {
      name: 'Rectangle',
      type: 'rectangle',
      category: CATEGORY_E.BASIC,
      imageUrl: generateBasicElementSVG('rectangle'),
    },
    {
      name: 'Ellipse',
      type: 'ellipse',
      category: CATEGORY_E.BASIC,
      imageUrl: generateBasicElementSVG('ellipse'),
    },
    {
      name: 'Cylinder',
      type: 'cylinder',
      category: CATEGORY_E.BASIC,
      imageUrl: generateBasicElementSVG('cylinder'),
    },
    {
      name: 'Label',
      type: 'label',
      category: CATEGORY_E.BASIC,
      imageUrl: generateBasicElementSVG('label'),
    },
    {
      name: 'Image',
      type: 'image',
      category: CATEGORY_E.BASIC,
      imageUrl: generateBasicElementSVG('image'),
    },
    {
      name: 'Progress',
      type: 'progress',
      category: CATEGORY_E.DYNAMIC,
      imageUrl: generateBasicElementSVG('progress'),
    },

    // 🔹 Domain Elements
    {
      name: 'Pump',
      type: 'pump',
      category: CATEGORY_E.DOMAIN,
      imageUrl: generateBasicElementSVG('pump'),
    },
    {
      name: 'Hand Valve',
      type: 'handValve',
      category: CATEGORY_E.DOMAIN,
      imageUrl: generateBasicElementSVG('handValve'),
    },
    {
      name: 'Control Valve',
      type: 'controlValve',
      category: CATEGORY_E.DOMAIN,
      imageUrl: generateBasicElementSVG('controlValve'),
    },
    {
      name: 'Liquid Tank',
      type: 'liquidTank',
      category: CATEGORY_E.DOMAIN,
      imageUrl: generateBasicElementSVG('liquidTank'),
    },
    {
      name: 'Conical Tank',
      type: 'conicalTank',
      category: CATEGORY_E.DOMAIN,
      imageUrl: generateBasicElementSVG('conicalTank'),
    },
    {
      name: 'Battery',
      type: 'battery',
      category: CATEGORY_E.DOMAIN,
      imageUrl: generateBasicElementSVG('battery'),
    },
  ];

  return (
    <Box
      sx={{
        width: '100%',
        height: '100vh',
        maxHeight: '100vh',
        overflow: 'auto',
      }}
      p={2}
    >
      <Typography variant="h5" color="primary" gutterBottom>
        Elements
      </Typography>
      <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
        <Autocomplete
          disablePortal
          id="combo-box-demo"
          options={
            diagramsList?.items?.map((item) => {
              return {
                id: item.id,
                label: item.name,
              };
            }) ?? []
          }
          value={
            diagramId
              ? {
                  id: diagramId,
                  label: diagramName,
                }
              : null
          }
          loading={fetchingDiagrams}
          sx={{ width: 380 }}
          size="small"
          onChange={(event, value) => {
            dispatch(setDiagramName(value?.label));
            dispatch(setDiagramId(value?.id ?? 0));
            dispatch(setSelectedElement(null));
          }}
          renderInput={(params) => <TextField {...params} label="Diagram" />}
        />
        <Button
          onClick={() => {
            dispatch(resetDiagram());
            dispatch(setSelectedElement(null));
          }}
          color="primary"
          variant="contained"
          sx={{
            borderRadius: 2,
            ml: 2,
            width: 'max-content',
            '& .MuiButton-startIcon': {
              mr: 0,
              '& .MuiSvgIcon-root': {
                fontSize: '25px',
              },
            },
          }}
          startIcon={
            <Tooltip title="Add Diagram" arrow>
              <AddIcon sx={{ fontWeight: 'bold' }} fontSize="large" />
            </Tooltip>
          }
        />
      </Box>

      <Box mb={2}>
        <Box sx={{ p: 2, pl: 0 }}>
          <Button
            color="primary"
            variant="contained"
            onClick={() => {
              setCreateNewElement(true);
            }}
          >
            Create Element
          </Button>
        </Box>

        <Box ref={panelRef}>
          <Box
            display="flex"
            alignItems="center"
            marginBottom={2}
            onClick={() => handleToggle(CATEGORY_E.GENERAL)}
          >
            <Typography variant="subtitle1" color="textSecondary">
              General Elements
            </Typography>
            <IconButton size="small">{openGeneral ? <ExpandLess /> : <ExpandMore />}</IconButton>
          </Box>

          <Collapse in={openGeneral}>
            <Grid container spacing={1}>
              {generalShapesArray
                .filter((shape) => shape.category === CATEGORY_E.GENERAL)
                .map((element) => (
                  <Grid item xs={6} sm={6} md={4} key={element.type}>
                    <Box
                      sx={{
                        cursor: 'grab',
                        display: 'flex',
                        justifyContent: 'center',
                        alignItems: 'center',
                        border: '1px solid #ccc',
                        borderRadius: '5px',
                        padding: '5px',
                      }}
                      draggable
                      onDragStart={(e) => onDragStart(e, element.type)}
                      onMouseEnter={(e) => {
                        const panelRect = panelRef.current?.getBoundingClientRect();
                        const elementRect = e.currentTarget.getBoundingClientRect();

                        if (panelRect) {
                          setHoverPosition({
                            x: panelRect.right + 30,
                            y: elementRect.top + window.scrollY,
                          });
                        }
                        setHoveredShape(element);
                      }}
                      onMouseLeave={() => setHoveredShape(null)}
                    >
                      {element.imageUrl ? (
                        <img
                          src={element.imageUrl}
                          alt={element.name}
                          style={{
                            aspectRatio: '4/3',
                            objectFit: 'contain',
                            width: 30,
                            height: 30,
                          }}
                        />
                      ) : (
                        <Typography variant="caption">{element.name}</Typography>
                      )}
                    </Box>
                  </Grid>
                ))}
            </Grid>
          </Collapse>
        </Box>

        <Divider sx={{ mb: 1, mt: 3 }} />

        <Box>
          <Box display="flex" alignItems="center" onClick={() => setOpenBasic(!openBasic)}>
            <Typography variant="subtitle1" color="textSecondary">
              Basic Elements
            </Typography>
            <IconButton size="small">{openBasic ? <ExpandLess /> : <ExpandMore />}</IconButton>
          </Box>

          <Collapse in={openBasic}>
            <Grid container spacing={1}>
              {shapesArray
                .filter((shape) => shape.category === CATEGORY_E.BASIC)
                .map((element) => (
                  <Grid item xs={6} sm={6} md={4} key={element.type}>
                    <Box
                      sx={{
                        cursor: 'grab',
                        display: 'flex',
                        justifyContent: 'center',
                        alignItems: 'center',
                        border: '1px solid #ccc',
                        borderRadius: '5px',
                        padding: '5px',
                      }}
                      draggable
                      onDragStart={(e) => onDragStart(e, element.type)}
                      onMouseEnter={(e) => {
                        const panelRect = panelRef.current?.getBoundingClientRect();
                        const elementRect = e.currentTarget.getBoundingClientRect();

                        if (panelRect) {
                          setHoverPosition({
                            x: panelRect.right + 30,
                            y: elementRect.top + window.scrollY,
                          });
                        }
                        setHoveredShape(element);
                      }}
                      onMouseLeave={() => setHoveredShape(null)}
                    >
                      {element.imageUrl ? (
                        <img
                          src={element.type !== 'image' ? element.imageUrl : '/placeholder-svg.svg'}
                          alt={element.name}
                          style={{
                            aspectRatio: '4/3',
                            objectFit: 'contain',
                            width: 30,
                            height: 30,
                          }}
                        />
                      ) : (
                        <Typography variant="caption">{element.name}</Typography>
                      )}
                    </Box>
                  </Grid>
                ))}
            </Grid>
          </Collapse>
        </Box>
      </Box>
      <Divider sx={{ my: 1 }} />
      {/* Domain Elements Section */}
      <Box>
        <Box display="flex" alignItems="center" onClick={() => handleToggle(CATEGORY_E.DOMAIN)}>
          <Typography variant="subtitle1" color="textSecondary">
            Domain Elements
          </Typography>

          <IconButton size="small">{openDomain ? <ExpandLess /> : <ExpandMore />}</IconButton>
        </Box>

        <Collapse in={openDomain}>
          <Grid container spacing={1}>
            {shapesArray
              .filter((shape) => shape.category === CATEGORY_E.DOMAIN)
              .map((element) => (
                <Grid item xs={6} sm={6} md={4} key={element.type}>
                  <Box
                    sx={{
                      cursor: 'grab',
                      display: 'flex',
                      justifyContent: 'center',
                      alignItems: 'center',
                      border: '1px solid #ccc',
                      borderRadius: '5px',
                      padding: '5px',
                    }}
                    draggable
                    onDragStart={(e) => onDragStart(e, element.type)}
                    onMouseEnter={(e) => {
                      const panelRect = panelRef.current?.getBoundingClientRect();
                      const elementRect = e.currentTarget.getBoundingClientRect();

                      if (panelRect) {
                        setHoverPosition({
                          x: panelRect.right + 30,
                          y: elementRect.top + window.scrollY,
                        });
                      }
                      setHoveredShape(element);
                    }}
                    onMouseLeave={() => setHoveredShape(null)}
                  >
                    {element.imageUrl ? (
                      <img
                        alt={element.name}
                        src={
                          element.type !== 'image'
                            ? element.imageUrl
                              ? element.type === 'conicalTank'
                                ? '/icons/conicalTank.png'
                                : element.imageUrl
                              : '/icons/conicalTank.png'
                            : '/placeholder-svg.svg'
                        }
                        style={{
                          aspectRatio: '4/3',
                          objectFit: 'contain',
                          width: 30,
                          height: 30,
                        }}
                      />
                    ) : (
                      <Typography variant="caption">{element.name}</Typography>
                    )}
                  </Box>
                </Grid>
              ))}
          </Grid>
        </Collapse>
      </Box>

      {customElements.length > 0 && (
        <>
          <Divider sx={{ my: 1 }} />
          {/* Custom Elements Section */}
          <Box>
            <Box display="flex" alignItems="center" onClick={() => handleToggle(CATEGORY_E.CUSTOM)}>
              <Typography variant="subtitle1" color="textSecondary">
                Custom Elements
              </Typography>
              <IconButton size="small">{openCustom ? <ExpandLess /> : <ExpandMore />}</IconButton>
            </Box>
            <Collapse in={openCustom}>
              <Grid container columnSpacing={1}>
                {customElements.map((element: any) => (
                  <Grid item xs={12} md={6} key={element.type}>
                    <Paper
                      elevation={1}
                      className="shape"
                      draggable
                      onDragStart={(e) => onDragStart(e, element)}
                      sx={{
                        marginBottom: '10px',
                        textAlign: 'center',
                        cursor: 'grab',
                        border: '1px solid #010101',
                      }}
                    >
                      <Typography variant="caption">{element.name}</Typography>
                    </Paper>
                  </Grid>
                ))}
              </Grid>
            </Collapse>
          </Box>
        </>
      )}

      <Divider sx={{ my: 1 }} />

      <Box>
        <Box display="flex" alignItems="center" onClick={() => handleToggle(CATEGORY_E.DYNAMIC)}>
          <Typography variant="subtitle1" color="textSecondary">
            Dynamic Elements
          </Typography>

          <IconButton size="small">{openDynamic ? <ExpandLess /> : <ExpandMore />}</IconButton>
        </Box>

        <Collapse in={openDynamic}>
          <Grid container spacing={1}>
            {shapesArray
              .filter((shape) => shape.category === CATEGORY_E.DYNAMIC)
              .map((element) => (
                <Grid item xs={6} sm={6} md={4} key={element.type}>
                  <Box
                    sx={{
                      cursor: 'grab',
                      display: 'flex',
                      justifyContent: 'center',
                      alignItems: 'center',
                      border: '1px solid #ccc',
                      borderRadius: '5px',
                      padding: '5px',
                    }}
                    draggable
                    onDragStart={(e) => onDragStart(e, element.type)}
                    onMouseEnter={(e) => {
                      const panelRect = panelRef.current?.getBoundingClientRect();
                      const elementRect = e.currentTarget.getBoundingClientRect();

                      if (panelRect) {
                        setHoverPosition({
                          x: panelRect.right + 30,
                          y: elementRect.top + window.scrollY,
                        });
                      }
                      setHoveredShape(element);
                    }}
                    onMouseLeave={() => setHoveredShape(null)}
                  >
                    {element.imageUrl ? (
                      <img
                        src={element.type !== 'image' ? element.imageUrl : '/placeholder-svg.svg'}
                        alt={element.name}
                        style={{
                          aspectRatio: '4/3',
                          objectFit: 'contain',
                          width: 30,
                          height: 30,
                        }}
                      />
                    ) : (
                      <Typography variant="caption">{element.name}</Typography>
                    )}
                  </Box>
                </Grid>
              ))}
          </Grid>
        </Collapse>
      </Box>

      {hoveredShape && hoveredShape.imageUrl && (
        <Box
          sx={{
            position: 'absolute',
            top: hoverPosition.y + 250 > window.innerHeight ? 'auto' : hoverPosition.y,
            bottom: hoverPosition.y + 250 > window.innerHeight ? 20 : 'auto',
            left: hoverPosition.x,
            backgroundColor: '#fff',
            borderRadius: '6px',
            boxShadow: '0px 2px 3px rgba(0,0,0,0.3)',
            zIndex: 1000,
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            border: '1px solid #ccc',
            maxWidth: '200px',
            width: '200px',
          }}
        >
          <img
            src={
              hoveredShape.type !== 'image'
                ? hoveredShape.imageUrl
                  ? hoveredShape.type === 'conicalTank'
                    ? '/icons/conicalTank.png'
                    : hoveredShape.imageUrl
                  : '/icons/conicalTank.png'
                : '/placeholder-svg.svg'
            }
            alt={hoveredShape.name}
            style={{
              width: '100%',
              height: '100%',
              objectFit: 'contain',
              padding: '10px',
              aspectRatio: 2 / 1,
            }}
          />

          <Box
            sx={{
              width: '100%',
              height: '1px',
              backgroundColor: '#ccc',
              marginTop: '8px',
            }}
          />

          <Typography
            variant="body2"
            color="black"
            sx={{
              textAlign: 'center',
              padding: '10px',
            }}
          >
            {hoveredShape.name}
          </Typography>
        </Box>
      )}

      <NewElementDialog
        createNewElement={createNewElement}
        setCreateNewElement={setCreateNewElement}
      />
    </Box>
  );
};

export default Palette;
