import { RootState } from '~/redux/store';
import { createSelector } from 'reselect';
import { getMeasurementsFromTree } from '~/utils/utils';

const selectDashboardState = (state: RootState) => state.dashboard;

export const selectTree = createSelector([selectDashboardState], (dashboard) => dashboard.tree);
export const getExpandedNodeIds = createSelector(
  [selectTree],
  (dashboard) => dashboard.expandedNodeIds,
);

export const getSelectedNodeIds = createSelector(
  [selectTree],
  (dashboard) => dashboard.selectedNodeIds,
);

export const getExpandedAssetIds = createSelector(
  [getExpandedNodeIds],
  (expandedNodeIds) => expandedNodeIds.map((id) => Number(id)).filter((id) => !isNaN(id)),
  // .filter((id) => id !== -1),
);

export const getCurrentSelectedNodeId = createSelector(
  [selectTree],
  (dashboard) => dashboard.currentSelectedNodeId,
);
export const getSelectedViewMeasureId = createSelector(
  [selectTree],
  (dashboard) => dashboard.selectedViewMeasureId,
);

export const getCurrentSelectedAssetId = createSelector(
  [getCurrentSelectedNodeId],
  (currentSelectedNodeId) => {
    if (currentSelectedNodeId.startsWith('m:')) {
      return currentSelectedNodeId.split(':')[1];
    }
    return currentSelectedNodeId;
  },
);

export const getAssetIdToDbMeasureIdList = createSelector(
  [getSelectedNodeIds],
  (selectedNodeIds) => {
    const assetIdToDbMeasureId: Record<string, string[]> = {};
    selectedNodeIds.map((id) => {
      if (id.startsWith('m:')) {
        const [_, assetId, dbMeasureId] = id.split(':');
        if (assetIdToDbMeasureId[assetId]) {
          assetIdToDbMeasureId[assetId].push(dbMeasureId);
        } else {
          assetIdToDbMeasureId[assetId] = [dbMeasureId];
        }
      }
    });
    return assetIdToDbMeasureId;
  },
);

export const getAssetIdByDbMeasureId = (dbMeasureId: string) =>
  createSelector(getAssetIdToDbMeasureIdList, (assetIdToDbMeasureIdList) => {
    for (const [assetId, dbMeasureIds] of Object.entries(assetIdToDbMeasureIdList)) {
      if (dbMeasureIds.includes(dbMeasureId)) {
        return assetId;
      }
    }
    return '';
  });

export const getDbMeasureIdToAssetIdMap = createSelector(
  [getSelectedNodeIds],
  (selectedNodeIds) => {
    const dbMeasureIdAssetId: Record<string, string> = {};
    selectedNodeIds.map((id) => {
      if (id.startsWith('m:')) {
        const [_, assetId, dbMeasureId] = id.split(':');
        dbMeasureIdAssetId[dbMeasureId] = assetId;
      }
    });
    return dbMeasureIdAssetId;
  },
);

// export const getDbMeasureIdToName = createSelector([selectTree], (tree) => tree.dbMeasureIdToName);
export const getDbMeasureIdToName = createSelector([selectTree], (tree) => {
  const selectedNodesList = getMeasurementsFromTree(tree.selectedNodeIds);
  const dbMeasureIdToName: Record<string, string> = Object.entries(tree.dbMeasureIdToName)
    .filter(([dbMeasureId]) => selectedNodesList.includes(dbMeasureId))
    .reduce((acc, [dbMeasureId, name]) => {
      acc[dbMeasureId] = name;
      return acc;
    }, {} as Record<string, string>);
  return dbMeasureIdToName;
});
