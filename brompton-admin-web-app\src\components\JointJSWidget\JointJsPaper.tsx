import { dia, shapes } from '@joint/core';
import { Box } from '@mui/material';
import { debounce } from 'lodash';
import React, {
  Dispatch,
  MutableRefObject,
  SetStateAction,
  useEffect,
  useRef,
  useState,
} from 'react';
import { useSelector } from 'react-redux';
import { getZoomEnabled } from '~/redux/selectors/dashboardSelectors';
import {
  cellNamespace,
  checkConditionalRules,
  elementVariable,
  LabelSettings,
} from '~/types/diagram';
import { DiagramWidget } from '~/types/widgets';
import Progress from '../CreateElement/Progress';
import { ConicTank } from '../JointJs/DiagramComponent/ConicalTank';
import { ControlValve } from '../JointJs/DiagramComponent/ControlValve';
import { HandValve } from '../JointJs/DiagramComponent/HandValve';
import LiquidTank from '../JointJs/DiagramComponent/LiquidTank';
import Pump from '../JointJs/DiagramComponent/Pump';

type DiagramPaperProps = {
  settings: DiagramWidget;
  setZoomLevel: Dispatch<SetStateAction<number>>;
  paperInstanceRef: MutableRefObject<dia.Paper | null>;
  setZoomLevelOnWidget: (zoomLevel: number) => void;
};

const ZOOM_STEP = 0.1;
const MAX_ZOOM = 2.5;
const MIN_ZOOM = 0.5;

const DiagramPaper: React.FC<DiagramPaperProps> = ({
  settings,
  setZoomLevel,
  paperInstanceRef,
  setZoomLevelOnWidget,
}) => {
  const zoomLevelRef = useRef(settings.zoomLevel);
  const paperRef = useRef<HTMLDivElement>(null);
  const graphRef = useRef<dia.Graph | null>(null);
  const enabledZoom = useSelector(getZoomEnabled);
  const [selectedElement, setSelectedElement] = useState<dia.Element | null>(null);
  const [labelText, setLabelText] = useState('');

  // const debouncedUpdateZoom = useRef(
  //   debounce((zoom) => {
  //     setZoomLevelOnWidget(zoom);
  //   }, 300), // Adjust delay as needed
  // ).current;

  useEffect(() => {
    if (paperInstanceRef.current && settings.zoomLevel !== 1) {
      setZoomLevel(settings.zoomLevel);
      paperInstanceRef.current?.scale(settings.zoomLevel, settings.zoomLevel);
    }
  }, [settings.zoomLevel]);
  const zoomIn = () => {
    if (paperInstanceRef.current && !settings.disableZoom) {
      const currentScale = paperInstanceRef.current.scale();
      const newScale = Math.min(currentScale.sx + ZOOM_STEP, MAX_ZOOM);
      paperInstanceRef.current.scale(newScale, newScale);
      setZoomLevel(newScale);
      zoomLevelRef.current = newScale;
      // debouncedUpdateZoom(newScale);
    }
  };

  const zoomOut = () => {
    if (paperInstanceRef.current && !settings.disableZoom) {
      const currentScale = paperInstanceRef.current.scale();
      const newScale = Math.max(currentScale.sx - ZOOM_STEP, MIN_ZOOM);
      paperInstanceRef.current.scale(newScale, newScale);
      setZoomLevel(newScale);
      zoomLevelRef.current = newScale;
      // debouncedUpdateZoom(newScale);
    }
  };

  useEffect(() => {
    const handleShiftPan = (
      paper: dia.Paper,
      paperRef: MutableRefObject<HTMLDivElement | null>,
    ) => {
      paper.on('blank:pointerdown', (evt) => {
        if (evt.shiftKey && paperRef.current) {
          const startPosition = { x: evt.clientX ?? 0, y: evt.clientY ?? 0 };
          const initialOrigin = paper.translate();

          // Change cursor to grabbing hand on the canvas
          paperRef.current.style.cursor = 'grabbing';

          const onPointerMove = (moveEvt: any) => {
            const dx = moveEvt.clientX - startPosition.x;
            const dy = moveEvt.clientY - startPosition.y;
            paper.translate(initialOrigin.tx + dx, initialOrigin.ty + dy);
          };

          const onPointerUp = () => {
            // Reset cursor to default on the canvas
            if (paperRef.current) {
              paperRef.current.style.cursor = 'default';
            }

            document.removeEventListener('mousemove', onPointerMove);
            document.removeEventListener('mouseup', onPointerUp);
          };

          document.addEventListener('mousemove', onPointerMove);
          document.addEventListener('mouseup', onPointerUp);
        }
      });
    };

    if (paperRef.current && !graphRef.current) {
      const graph = new dia.Graph(
        {},
        {
          cellNamespace: cellNamespace,
        },
      );
      graphRef.current = graph;

      const paper = new dia.Paper({
        el: paperRef.current,
        model: graph,
        width: '100%',
        height: '100%',
        gridSize: 10,
        drawGrid: true,
        cellViewNamespace: cellNamespace,
        interactive: false,
      });

      paperInstanceRef.current = paper;
      handleShiftPan(paper, paperRef);

      paper.on('element:pointerclick', (elementView) => {
        const element = elementView.model as dia.Element;
        setSelectedElement(element);
        const elementAttrs = element.attr();
        setLabelText(elementAttrs.label?.text || '');
      });
    }

    const handleWheelZoom = (event: WheelEvent) => {
      if (!enabledZoom) {
        if (event.deltaY < 0) {
          zoomIn();
        } else {
          zoomOut();
        }
      }
    };

    paperRef.current?.addEventListener('wheel', handleWheelZoom);

    return () => {
      paperRef.current?.removeEventListener('wheel', handleWheelZoom);
    };
  }, [settings.disableZoom, enabledZoom]);

  useEffect(() => {
    if (graphRef.current && settings.jsonFile !== '') {
      const parsedData = JSON.parse(settings.jsonFile)?.graph;
      if (parsedData) {
        graphRef.current.fromJSON(parsedData);
        graphRef.current.getCells().forEach((cellInfo) => {
          // Ensure the cell is an element before applying the logic
          if (!(cellInfo instanceof dia.Element)) {
            return;
          }
          const applyLogicToElement = (element: dia.Element) => {
            const level = Number(element.get('data')?.level ?? 0);
            const elementName = element.get('attrs')?.label?.text || 'Unnamed';
            const variables: elementVariable[] = element.get('data')?.variables || [];
            const variableLabels: dia.Cell.ID[] = element.get('data')?.variableLabels || [];
            if (variables.length > 0 && variableLabels.length > 0) {
              variables.forEach((variable, index) => {
                if (index < variableLabels.length) {
                  const labelEntry = variableLabels[index];

                  // Determine labelId based on type (old vs new format)
                  let labelId: string | undefined;
                  if (typeof labelEntry === 'string') {
                    // Type 1 (old): labelId is a direct string
                    labelId = labelEntry;
                  } else if (
                    labelEntry &&
                    typeof labelEntry === 'object' &&
                    'labelId' in labelEntry
                  ) {
                    // Type 2 (new): labelId is nested in object
                    labelId = (labelEntry as { labelId: string }).labelId;
                  }

                  if (labelId) {
                    const labelElement = graphRef.current?.getCell(labelId);

                    if (labelElement) {
                      const variableValue = variable.value ?? '';
                      const originalLabel = labelElement?.attributes?.attrs?.label?.text || '';
                      const labelWithoutValue = originalLabel.split(':')[0].trim();
                      const updatedLabel =
                        variableValue !== '' ? `${variableValue}` : labelWithoutValue;
                      labelElement.attr('label/text', updatedLabel);
                    } else {
                      console.warn(
                        `[DiagramPaper] Label element not found for labelId: ${labelId}`,
                      );
                    }
                  } else {
                    console.warn(`[DiagramPaper] Could not extract labelId for index ${index}`);
                  }
                }
              });
            }
            if (element instanceof Progress) {
              element.style = element.prop('data/style') ?? 'bar';
              const variableWithValue = variables.find((v) => v.value);
              if (variableWithValue) {
                const numericValue = parseFloat(variableWithValue.value.replace(/[^\d.-]/g, ''));
                const progressLevel = Math.max(0, Math.min(100, numericValue));
                element.level = progressLevel;
              }
              const ruleResults = checkConditionalRules(element);
              ruleResults.forEach((result, ruleIndex) => {
                if (result) {
                  const rule = element.get('data')?.conditionalRule?.[ruleIndex];
                  // element.colorToShow = rule.rules.color;
                  // element.attr('body/fill', '#ffffff');
                  if (rule) {
                    switch (rule.applicableTo) {
                      case 'border':
                        element.attr('body/stroke', rule.rules.borderColor);
                        element.attr('body/strokeWidth', rule.rules.borderWidth);
                        switch (rule.rules.borderStyle) {
                          case 'solid':
                            element.attr('body/strokeDasharray', '0');
                            break;
                          case 'dotted':
                            element.attr('body/strokeDasharray', '2,2');
                            break;
                          case 'dashed':
                            element.attr('body/strokeDasharray', '8,4');
                            break;
                          case 'none':
                            element.attr('body/stroke', 'none');
                            element.attr('body/strokeWidth', 0);
                            break;
                          default:
                            console.warn(`Unsupported border style: ${rule.rules.borderStyle}`);
                            break;
                        }
                        break;

                      case 'title':
                        element.attr('label/fill', rule.rules.titleColor);
                        element.attr('label/fontSize', rule.rules.fontSize);
                        element.attr('label/text', rule.rules.text || 'Progress');
                        break;

                      case 'background':
                      case 'color':
                        element.colorToShow = rule.rules.color; // Update internal state
                        element.attr('body/fill', '#ffffff'); // Ensure visual update
                        break;

                      default:
                        break;
                    }
                  }
                }
              });
            }
            if (element instanceof Pump) {
              const ruleResults = checkConditionalRules(element);
              ruleResults.forEach((result, ruleIndex) => {
                element.toggleRotation('off');
                if (result) {
                  const rule = element.get('data')?.conditionalRule?.[ruleIndex];
                  element.toggleRotation(rule.rules.rotate ? 'on' : 'off');
                  // element.colorToShow = rule.rules.color;
                }
              });
            }
            if (element instanceof ConicTank) {
              const { maxCapacity } = element.get('data') ?? {};
              element.updateMaxCapacity(Number(maxCapacity ?? 0));
              element.level = level;
            }
            if (element instanceof LiquidTank) {
              const {
                direction = 'horizontal',
                range = [],
                maxCapacity = 100,
              } = element.get('data') || {};
              element.startProgress(false);
              element.level = level;
              element.updateRangePicker(element.rangePicker);
              element.updateDirection(direction);
              element.prop('data/range', range);
              element.updateRangePicker(range);
              element.updateMaxCapacity(maxCapacity);
              element.attr('label/text', `${elementName}`);
            } else if (
              element instanceof ConicTank ||
              element instanceof HandValve ||
              element instanceof ControlValve
            ) {
              element.level = level;
            } else if (
              element instanceof shapes.standard.BorderedImage ||
              element instanceof shapes.standard.Cylinder ||
              element instanceof shapes.standard.Ellipse ||
              element instanceof shapes.standard.Circle
            ) {
              const rangePicker = element.get('data')?.range ?? [];
              let fillColor;
              for (let i = rangePicker.length - 1; i >= 0; i--) {
                const condition = rangePicker[i];
                if (level <= Number(condition.value)) {
                  fillColor = condition.color;
                } else {
                  break;
                }
              }
              if (!(element instanceof Progress)) {
                element.attr('body/fill', fillColor);
              }
            }
            if (!(element instanceof Progress)) {
              element.attr('body/fill', '#ffffff');
              const ruleResults = checkConditionalRules(element);
              ruleResults.forEach((result, ruleIndex) => {
                if (result) {
                  const rule = element.get('data')?.conditionalRule?.[ruleIndex];
                  if (rule) {
                    switch (rule.applicableTo) {
                      case 'border':
                        element.attr('body/stroke', rule.rules.borderColor);
                        element.attr('top/stroke', rule.rules.borderColor);
                        element.attr('bottom/stroke', rule.rules.borderColor);
                        element.attr('body/strokeWidth', rule.rules.borderWidth);
                        element.attr('top/strokeWidth', rule.rules.borderWidth);
                        element.attr('bottom/strokeWidth', rule.rules.borderWidth);
                        if (element.attributes.type === 'standard.BorderedImage') {
                          element.attr('border/stroke', rule.rules.borderColor);
                          element.attr('border/strokeWidth', rule.rules.borderWidth);
                        }
                        switch (rule.rules.borderStyle) {
                          case 'solid':
                            element.attr('body/strokeDasharray', '0');
                            element.attr('top/strokeDasharray', '0');
                            element.attr('bottom/strokeDasharray', '0');
                            element.attr('border/strokeDasharray', '0');
                            element.attr('body/filter', null);
                            break;
                          case 'dotted':
                            element.attr('body/strokeDasharray', '2,2');
                            element.attr('top/strokeDasharray', '2.2');
                            element.attr('bottom/strokeDasharray', '2.2');
                            element.attr('border/strokeDasharray', '2.2');
                            element.attr('body/filter', null);
                            break;
                          case 'dashed':
                            element.attr('body/strokeDasharray', '8,4');
                            element.attr('top/strokeDasharray', '8.4');
                            element.attr('bottom/strokeDasharray', '8.4');
                            element.attr('border/strokeDasharray', '8.4');
                            element.attr('body/filter', null);
                            break;
                          case 'none':
                            element.attr('body/stroke', 'none');
                            element.attr('top/strokeDasharray', 'none');
                            element.attr('bottom/strokeDasharray', 'none');
                            element.attr('body/strokeWidth', 0);
                            element.attr('border/strokeDasharray', 'none');
                            element.attr('border/stroke', 'none');
                            element.attr('body/filter', null);
                            break;
                          default:
                            console.warn(`Unsupported border style: ${rule.rules.borderStyle}`);
                            break;
                        }
                        break;
                      case 'title':
                        element.attr('label/fill', rule.rules.titleColor);
                        element.attr('label/fontSize', rule.rules.fontSize);
                        break;
                      case 'background':
                      case 'color':
                        element.attr('body/fill', rule.rules.color);
                        element.attr('background/fill', rule.rules.color);
                        break;
                      default:
                        break;
                    }
                  }
                }
              });
            }
          };
          if (cellInfo instanceof dia.Element) {
            const ports = cellInfo.getPorts();
            ports.forEach((port) => {
              if (port && port.id) {
                cellInfo.portProp(String(port.id), 'attrs/portBody/r', 0);
              }
            });
          }
          applyLogicToElement(cellInfo);
          if (cellInfo instanceof shapes.standard.Rectangle) {
            if (cellInfo.getEmbeddedCells().length > 0) {
              cellInfo.getEmbeddedCells().forEach((embeddedCell) => {
                if (embeddedCell instanceof dia.Element) {
                  applyLogicToElement(embeddedCell);
                }
              });
            } else {
              applyLogicToElement(cellInfo);
            }
          }
          if (cellInfo instanceof shapes.standard.TextBlock) {
            const data = cellInfo.get('data') as LabelSettings;
            if (data?.isLabel) {
              cellInfo.attr('label/style/color', data.fontColor);
              cellInfo.attr('label/style/fontSize', data.fontSize);
              cellInfo.attr('label/style/fontWeight', data.fontWeight);
              cellInfo.attr('body/fill', data.backgroundColor ?? '#5c4bdd');
              cellInfo.attr('body/fill-opacity', data.opacity);
            }
          }
        });
      }
    }
  }, [settings.jsonFile]);

  useEffect(() => {
    if (!graphRef.current) {
      console.warn('[Link Animation] Graph reference is null.');
      return;
    }

    const links = graphRef.current.getLinks();

    const animationIntervals: Record<string, NodeJS.Timeout> = {};

    links.forEach((link) => {
      const lineAttrs = link.attr('line');
      const hasAnimation =
        lineAttrs?.strokeDasharray &&
        lineAttrs?.strokeDasharray !== 'none' &&
        typeof lineAttrs.strokeDashoffset !== 'undefined';

      if (hasAnimation) {
        let dashOffset = 0;
        const animationSpeed = lineAttrs?.animationSpeed || 75; // Default speed if not provided
        const intervalTime = Math.max(10, 1000 - animationSpeed * 12);

        animationIntervals[link.id] = setInterval(() => {
          if (!graphRef.current) {
            clearInterval(animationIntervals[link.id]);
            delete animationIntervals[link.id];
            return;
          }

          const existingLink = graphRef.current.getCell(link.id);
          if (!existingLink) {
            clearInterval(animationIntervals[link.id]);
            delete animationIntervals[link.id];
            return;
          }

          dashOffset -= 2;
          link.attr('line/strokeDashoffset', dashOffset.toString());
        }, intervalTime);
      } else {
        console.warn(`[Link Animation] No animation applied for link ID: ${link.id}`);
      }
    });

    return () => {
      Object.values(animationIntervals).forEach(clearInterval);
    };
  }, [graphRef.current ? graphRef.current.getLinks() : []]);

  const handleLabelTextChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setLabelText(event.target.value);
  };

  const updateElementLabel = () => {
    if (selectedElement) {
      const elementAttrs = selectedElement.attr();
      const currentAttrs = elementAttrs.label || {};
      selectedElement.attr(
        'label/text',
        labelText || currentAttrs.text || currentAttrs.body?.name || '',
      );
    }
  };

  return (
    <Box sx={{ height: '100%' }}>
      <Box
        ref={paperRef}
        style={{
          width: '100%',
          height: '100%',
          overflow: 'hidden',
        }}
      />

      {selectedElement && (
        <Box style={{ marginTop: 20 }}>
          <input
            type="text"
            value={labelText}
            onChange={handleLabelTextChange}
            placeholder="Enter label text"
          />
          <button onClick={updateElementLabel}>Update Label</button>
        </Box>
      )}
    </Box>
  );
};

export default DiagramPaper;
