import { Box, Checkbox, FormControl, FormControlLabel, Grid, Input } from '@mui/material';
import { ChangeEvent } from 'react';
import { GaugeChartWidget, setSingleMeasureWidgetSettings } from '~/types/widgets';
import DataWidgetSettingsContainer from '../common/DataWidgetSettingsContainer/DataWidgetSettingsContainer';
import SingleMeasureSelect from '../common/SingleMeasureSelect';

type GaugeChartSettingsProps = {
  gaugeChartSettings: GaugeChartWidget;
  selectedDbMeasureIdToName: { [key: string]: string };
  handleGaugeChartSettingsUpdate: (
    value: ((prevState: GaugeChartWidget) => GaugeChartWidget) | GaugeChartWidget,
  ) => void;
};
const GaugeChartSettings = ({
  gaugeChartSettings,
  handleGaugeChartSettingsUpdate,
  selectedDbMeasureIdToName,
}: GaugeChartSettingsProps) => {
  const handleChangeIndicator = (e: ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    handleGaugeChartSettingsUpdate((prevState) => {
      return {
        ...prevState,
        [e.target.name]: e.target.value,
      };
    });
  };

  const handleMarginChange = (e: ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    handleGaugeChartSettingsUpdate((prevState) => {
      return {
        ...prevState,
        margin: {
          ...prevState.margin,
          [e.target.name]: Number(e.target.value),
        },
      };
    });
  };

  const handleCheckBoxChange = (e: ChangeEvent<HTMLInputElement>) => {
    handleGaugeChartSettingsUpdate((prevState) => {
      return {
        ...prevState,
        [e.target.name]: e.target.checked,
      };
    });
  };

  return (
    <DataWidgetSettingsContainer
      settings={gaugeChartSettings}
      setSettings={handleGaugeChartSettingsUpdate}
      dataTabChildren={
        <>
          <FormControl fullWidth>
            <Box width="100%" pl={0} pr={0}>
              <SingleMeasureSelect
                id={'gauge-chart'}
                settings={gaugeChartSettings}
                setSettings={handleGaugeChartSettingsUpdate as setSingleMeasureWidgetSettings}
              />
            </Box>
          </FormControl>
          <Grid container spacing={2} sx={{ mt: 2 }}>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <Box sx={{ fontSize: 14, mb: 0.5 }}>Min Value</Box>
                <Input
                  type="number"
                  value={gaugeChartSettings.minValue}
                  name="minValue"
                  onChange={handleChangeIndicator}
                />
              </FormControl>
            </Grid>

            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <Box sx={{ fontSize: 14, mb: 0.5 }}>Max Value</Box>
                <Input
                  type="number"
                  value={gaugeChartSettings.maxValue}
                  name="maxValue"
                  onChange={handleChangeIndicator}
                />
              </FormControl>
            </Grid>
          </Grid>
          <FormControlLabel
            sx={{
              mt: 2,
              width: '100%',
            }}
            control={
              <Checkbox
                value={gaugeChartSettings.showThreshHoldValue}
                name="showThreshHoldValue"
                checked={gaugeChartSettings.showThreshHoldValue}
                onChange={handleCheckBoxChange}
              />
            }
            label="Show Threshold"
          />
          {gaugeChartSettings.showThreshHoldValue ? (
            <>
              <FormControl
                fullWidth
                sx={{
                  mt: 1,
                }}
              >
                Threshold Value
                <Input
                  type="number"
                  fullWidth
                  value={gaugeChartSettings.threshHoldValue}
                  name="threshHoldValue"
                  onChange={handleChangeIndicator}
                />
              </FormControl>
            </>
          ) : null}
          <FormControlLabel
            sx={{
              mt: 2,
              width: '100%',
            }}
            control={
              <Checkbox
                value={gaugeChartSettings.showIndicator1}
                checked={gaugeChartSettings.showIndicator1}
                name="showIndicator1"
                onChange={handleCheckBoxChange}
              />
            }
            label="Show Indicator 1"
          />
          {gaugeChartSettings.showIndicator1 ? (
            <>
              <FormControl
                fullWidth
                sx={{
                  mt: 1,
                }}
              >
                Indicator 1 Value
                <Input
                  type="number"
                  fullWidth
                  value={gaugeChartSettings.indicator1Value}
                  name="indicator1Value"
                  onChange={handleChangeIndicator}
                />
              </FormControl>
              <FormControlLabel
                sx={{
                  mt: 1,
                  width: '100%',
                }}
                control={
                  <Checkbox
                    value={gaugeChartSettings.showIndicator2}
                    name="showIndicator2"
                    checked={gaugeChartSettings.showIndicator2}
                    onChange={handleCheckBoxChange}
                  />
                }
                label="Show Indicator 2"
              />
              {gaugeChartSettings.showIndicator2 ? (
                <FormControl
                  fullWidth
                  sx={{
                    mt: 1,
                  }}
                >
                  Indicator 2 Value
                  <Input
                    type="number"
                    fullWidth
                    value={gaugeChartSettings.indicator2Value}
                    name="indicator2Value"
                    onChange={handleChangeIndicator}
                  />
                </FormControl>
              ) : null}
            </>
          ) : null}
        </>
      }
      feelTabChidren={
        <>
          <Grid container spacing={2} sx={{ mt: 2 }}>
            {/* Bar Color */}
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <Box sx={{ fontSize: 14, mb: 0.5 }}>Bar Color</Box>
                <Input
                  type="color"
                  name="barColor"
                  value={gaugeChartSettings.barColor}
                  onChange={handleChangeIndicator}
                />
              </FormControl>
            </Grid>

            {/* Threshold Color */}
            {gaugeChartSettings.showThreshHoldValue && (
              <Grid item xs={12} sm={6}>
                <FormControl fullWidth>
                  <Box sx={{ fontSize: 14, mb: 0.5 }}>Threshold Color</Box>
                  <Input
                    type="color"
                    name="threshHoldColor"
                    value={gaugeChartSettings.threshHoldColor}
                    onChange={handleChangeIndicator}
                  />
                </FormControl>
              </Grid>
            )}

            {/* Indicator 1 Color */}
            {gaugeChartSettings.showIndicator1 && (
              <Grid item xs={12} sm={6}>
                <FormControl fullWidth>
                  <Box sx={{ fontSize: 14, mb: 0.5 }}>Indicator 1 Color</Box>
                  <Input
                    type="color"
                    name="indicator1Color"
                    value={gaugeChartSettings.indicator1Color}
                    onChange={handleChangeIndicator}
                  />
                </FormControl>
              </Grid>
            )}

            {/* Indicator 2 Color */}
            {gaugeChartSettings.showIndicator1 && gaugeChartSettings.showIndicator2 && (
              <Grid item xs={12} sm={6}>
                <FormControl fullWidth>
                  <Box sx={{ fontSize: 14, mb: 0.5 }}>Indicator 2 Color</Box>
                  <Input
                    type="color"
                    name="indicator2Color"
                    value={gaugeChartSettings.indicator2Color}
                    onChange={handleChangeIndicator}
                  />
                </FormControl>
              </Grid>
            )}
          </Grid>
          <Grid container spacing={2} sx={{ mt: 2 }}>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <Box sx={{ fontSize: 14, mb: 0.5 }}>Margin Bottom</Box>
                <Input
                  type="number"
                  value={gaugeChartSettings.margin.b}
                  name="b"
                  onChange={handleMarginChange}
                />
              </FormControl>
            </Grid>

            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <Box sx={{ fontSize: 14, mb: 0.5 }}>Margin Top</Box>
                <Input
                  type="number"
                  value={gaugeChartSettings.margin.t}
                  name="t"
                  onChange={handleMarginChange}
                />
              </FormControl>
            </Grid>

            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <Box sx={{ fontSize: 14, mb: 0.5 }}>Margin Left</Box>
                <Input
                  type="number"
                  value={gaugeChartSettings.margin.l}
                  name="l"
                  onChange={handleMarginChange}
                />
              </FormControl>
            </Grid>

            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <Box sx={{ fontSize: 14, mb: 0.5 }}>Margin Right</Box>
                <Input
                  type="number"
                  value={gaugeChartSettings.margin.r}
                  name="r"
                  onChange={handleMarginChange}
                />
              </FormControl>
            </Grid>
          </Grid>
        </>
      }
    />
  );
};

export default GaugeChartSettings;
