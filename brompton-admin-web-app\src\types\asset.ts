import * as yup from 'yup';
import { CollectionDto } from '~/infra/dto-types';

export const assetSchema = yup.object({
  parentIds: yup.array(yup.number()).required(),
  tag: yup
    .string()
    .required('Please enter a tag')
    .matches(
      /^[a-zA-Z_][a-zA-Z0-9_\/]*$/,
      'Only letters, numbers, underscores, forward slashes are allowed and must start with a letter or underscore',
    ),
  latitude: yup
    .number()
    .min(-90)
    .max(90)
    .nullable()
    .optional()
    .test('cond-require-lat', 'Please enter latitude', (value, testContext) =>
      testContext.parent.longitude ? !!value : true,
    ),
  longitude: yup
    .number()
    .min(-180)
    .max(180)
    .nullable()
    .optional()
    .test('cond-require-long', 'Please enter longitude', (value, testContext) =>
      testContext.parent.latitude ? !!value : true,
    ),
  assetTypeId: yup.number().integer().required('Please select an asset type'),
  timeZone: yup.string().optional(),
  description: yup.string().max(100).optional(),
});

export const editAssetSchema = yup.object({
  parent_ids: yup.array(yup.number()).required(),
  tag: yup
    .string()
    .required('Please enter a tag')
    .matches(
      /^[a-zA-Z_][a-zA-Z0-9_\/]*$/,
      'Only letters, numbers, underscores, forward slashes are allowed and must start with a letter or underscore',
    ),
  latitude: yup
    .number()
    .min(-90)
    .max(90)
    .nullable()
    .optional()
    .test('cond-require-lat', 'Please enter latitude', (value, testContext) =>
      testContext.parent.longitude ? !!value : true,
    ),
  longitude: yup
    .number()
    .min(-180)
    .max(180)
    .nullable()
    .optional()
    .test('cond-require-long', 'Please enter longitude', (value, testContext) =>
      testContext.parent.latitude ? !!value : true,
    ),
  type_id: yup.number().integer().required('Please select an asset type'),
  time_zone: yup.string().nullable().optional(),
  description: yup.string().max(100).optional(),
});

export type Asset = yup.InferType<typeof assetSchema> & {
  id: number;
  time_zone: string;
  childrenIds: number[];
  parent_ids: number[];
};

export type EditAssetDo = yup.InferType<typeof editAssetSchema> & {
  id: number;
  childrenIds: number[];
  assetTemplate: number | null;
};
export type AssetDo = yup.InferType<typeof editAssetSchema> & {
  id: number;
  childrenIds: number[];
  children_ids: number[];
  customer_id: number | null;
};
export type AssetTemplateResponse = {
  id: number;
  manufacturer: string;
  modelNumber: string;
  assetType: {
    id: number;
    parentType: number;
    name: string;
  };
};
export type AssetDoDetails = AssetDo & {
  assetTemplate: AssetTemplateResponse;
};
export type NewAsset = Omit<Asset, 'id' | 'childrenIds'>;

export type EditAsset = Omit<EditAssetDo, 'id'>;

export const AssetTypeSchema = yup.object({
  name: yup.string().required('Asset Type name is required.'),
  parent_type: yup
    .object({
      value: yup.number().nullable(),
      label: yup.string(),
    })
    .nullable()
    .transform((value) => (value && value.id ? value : null))
    .optional(),
});

export type AssetType = {
  id: number;
  parentType: number | null;
  name: string;
  asset_template_count: number;
};

export type AssetTypeOption = { value: number; label: string };

export type AssetTypeDto = Omit<AssetType, 'parentType'> & { parent_type_id: number };
export type AssetTypeDtoCollection = CollectionDto<AssetTypeDto>;
export type AssetTypeCollection = AssetType[];

export type TimeZoneDto = string;
export type TimeZoneCollection = CollectionDto<TimeZoneDto>;

export const newAssetTemplateSchema = yup.object({
  tag: yup.string().required('Please enter a tag'),
  type: yup.string(),
  template: yup.string(),
  unit_group: yup.string(),
  total_output_pressure: yup.number(),
  total_pressure_unit: yup.string().nullable().optional(),
  total_energy_consumption: yup.number(),
  na_total_energy: yup.string().nullable(),
});

export type AssetTemplate = yup.InferType<typeof newAssetTemplateSchema> & { id: number };

export type AssetTemplateDo = Omit<AssetTemplate, 'id'>;

export type AssetTypeMetrics = {
  id: number;
  name: string;
};

export type AssetTypeMetricsCollection = {
  items: AssetTypeMetrics[];
  total: number;
};
