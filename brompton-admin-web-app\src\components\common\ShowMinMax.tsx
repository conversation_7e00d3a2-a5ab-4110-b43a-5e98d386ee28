import { Checkbox, Divider, FormControlLabel, Typography } from '@mui/material';
import { ChangeEvent } from 'react';
import { MultiMeasureChartWidgets, setMultiMeasureChartSettings } from '~/types/widgets';
type ShowMinMaxProps = {
  selectedTitles: string[];
  settings: MultiMeasureChartWidgets;
  setSettings: setMultiMeasureChartSettings;
};
const ShowMinMax = ({ selectedTitles, settings, setSettings }: ShowMinMaxProps) => {
  const handleChangeshowMin = (event: ChangeEvent<HTMLInputElement>, checked: boolean) => {
    setSettings((prevState) => ({
      ...prevState,
      min: {
        show: event.target.checked,
      },
    }));
  };
  const handleChangeshowMax = (event: ChangeEvent<HTMLInputElement>, checked: boolean) => {
    setSettings((prevState) => ({
      ...prevState,
      max: {
        show: event.target.checked,
      },
    }));
  };
  const handleChangeshowAvg = (event: ChangeEvent<HTMLInputElement>, checked: boolean) => {
    setSettings((prevState) => ({
      ...prevState,
      avg: {
        show: event.target.checked,
      },
    }));
  };
  return (
    <>
      <Divider />
      <Typography variant="h5" mt={2} mb={1}>
        Min / Max / Avg Configuration
      </Typography>
      {settings.mode === 'dashboard' && selectedTitles.length === 1 ? (
        <>
          <FormControlLabel
            control={
              <Checkbox
                checked={settings?.min?.show}
                onChange={handleChangeshowMin}
                name="min.show"
              />
            }
            label="Show Min"
          />
          <FormControlLabel
            control={
              <Checkbox
                checked={settings?.avg?.show}
                onChange={handleChangeshowAvg}
                name="max.avg"
              />
            }
            label="Show Avg"
          />
          <FormControlLabel
            control={
              <Checkbox
                checked={settings?.max?.show}
                onChange={handleChangeshowMax}
                name="max.show"
              />
            }
            label="Show Max"
          />
        </>
      ) : null}
      {settings.mode === 'template' && settings.selectedTitles.length === 1 ? (
        <>
          <FormControlLabel
            control={
              <Checkbox
                checked={settings?.min?.show}
                onChange={handleChangeshowMin}
                name="min.show"
              />
            }
            label="Show Min"
          />
          <FormControlLabel
            control={
              <Checkbox
                checked={settings?.avg?.show}
                onChange={handleChangeshowAvg}
                name="max.avg"
              />
            }
            label="Show Avg"
          />
          <FormControlLabel
            control={
              <Checkbox
                checked={settings?.max?.show}
                onChange={handleChangeshowMax}
                name="max.show"
              />
            }
            label="Show Max"
          />
        </>
      ) : null}
    </>
  );
};

export default ShowMinMax;
