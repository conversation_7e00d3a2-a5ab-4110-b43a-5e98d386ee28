import { expect } from '@playwright/test';

export const DashboardTestCases = [
  //  1 description: 'Mark a dashboard as a favorite',

  {
    description: 'Mark a dashboard as a favorite',
    requestConfig: {
      method: 'POST',
      url: 'https://test.pivotol.ai/api/v0/customers/8/dashboards/favorite',
      headers: {
        'Content-Type': 'application/json',
      },
      body: {
        dashboard_id: 64,
        status: true,
      },
    },
    expectedStatus: 200,
    validate: (response) => {
      if (response.statusCode === 200) {
        // Success case: Dashboard successfully marked as favorite
        expect(response).toHaveProperty('dashboardId', 64);
        expect(response).toHaveProperty('status', true);
        console.log('Dashboard marked as favorite successfully:', response);
      } else if (response.statusCode === 409) {
        // Conflict case: Dashboard is already marked as favorite
        expect(response).toHaveProperty('conflict', true);
        expect(response.conflictData).toHaveProperty('statusCode', 409);
        expect(response.conflictData).toHaveProperty(
          'message',
          'Dashboard id 64 is already favorite for this user.',
        );
        console.warn(`Conflict detected: ${response.conflictData.message}`);
      } else if (response.conflict && response.conflictData) {
        // Handle other cases of conflict
        const { statusCode, message } = response.conflictData;
        console.warn(`Conflict detected: StatusCode: ${statusCode}, Message: ${message}`);
      } else {
        // Unexpected status code or response
        console.error('Unexpected status code or response:', response);
        throw new Error(`Unexpected response: ${JSON.stringify(response)}`);
      }
    },
  },

  // 2 description: 'Retrieve weather station data by ID',
  {
    description: 'Retrieve weather station data by ID',
    requestConfig: {
      method: 'GET',
      url: 'https://test.pivotol.ai/api/v0/weather/station/135222',
      headers: {
        //    "BE-CsrfToken": "your_csrf_token_here", // Replace with a valid token dynamically
        //   "Cookie": "BE-AccessToken=your_access_token_here", // Replace with a valid token dynamically
        'Content-Type': 'application/json',
      },
    },
    expectedStatus: 200,
    validate: (response) => {
      if (response.statusCode === 200) {
        // Success case: Validate the station data
        expect(response).toHaveProperty('id', 135222);
        expect(response).toHaveProperty('name');
        console.log('Station retrieved successfully:', response);
      } else if (response.statusCode === 404) {
        // Not Found case: Log the error for debugging
        expect(response.exception).toHaveProperty('name', 'NotFoundException');
        expect(response.exception.response).toHaveProperty('message', 'Station not found');
        console.warn('Station not found:', response.message);
      } else {
        // Unexpected status code
        throw new Error(`Unexpected response: ${JSON.stringify(response)}`);
      }
    },
  },
  //  3 30 description: 'Retrieve available time zones',
  {
    description: 'Retrieve available time zones',
    requestConfig: {
      method: 'GET',
      url: 'https://test.pivotol.ai/api/v0/assets-backoffice/time-zones',
      headers: {
        //    "BE-CsrfToken": "strsjoPhK+WYnHZNXVMRJWGCvzJ9KXdAsD6YLUWaV4E=",
        'Content-Type': 'application/json',
        //    "Cookie": "BE-AccessToken=your_token_here", // Replace with a valid token dynamically
      },
      body: null,
    },
    expectedStatus: 200,
    validate: (response) => {
      // Ensure the response contains the `items` property
      expect(response).toHaveProperty('items');

      // Validate that `items` is an array
      expect(response.items).toBeInstanceOf(Array);

      // Optionally, validate the `total` property
      expect(response).toHaveProperty('total', expect.any(Number));

      // Log the time zones for debugging
      console.log('Time zones retrieved successfully:', response.items);
    },
  },
  // 29 description: 'Retrieve data sources',
  {
    description: 'Retrieve data sources',
    requestConfig: {
      method: 'GET',
      url: 'https://test.pivotol.ai/api/v0/measurements-backoffice/datasources',
      headers: {
        //   "BE-CsrfToken": "strsjoPhK+WYnHZNXVMRJWGCvzJ9KXdAsD6YLUWaV4E=",
        'Content-Type': 'application/json',
        //   "Cookie": "BE-AccessToken=your_token_here", // Replace with a valid token dynamically
      },
      body: null,
    },
    expectedStatus: 200,
    validate: (response) => {
      // Ensure the response contains the `items` property
      expect(response).toHaveProperty('items');

      // Validate that `items` is an array
      expect(response.items).toBeInstanceOf(Array);

      // Optionally, validate the `total` property
      expect(response).toHaveProperty('total', expect.any(Number));

      // Log the response for debugging
      console.log('Data sources retrieved successfully:', response.items);
    },
  },
];
