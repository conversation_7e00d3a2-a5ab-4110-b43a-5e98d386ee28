import {
  FormControl,
  FormLabel,
  TextField,
  Tooltip,
  IconButton,
  InputAdornment,
  Typography,
  OutlinedInput,
  FormHelperText,
} from '@mui/material';

type ChartLegendsSettingsProps = {
  ledendY: number;
  handleLegendYChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
};
import InfoIcon from '@mui/icons-material/Info';

const ChartLegendsSettings = ({ ledendY, handleLegendYChange }: ChartLegendsSettingsProps) => {
  return (
    <FormControl sx={{ mb: 1 }} variant="outlined" fullWidth>
      <OutlinedInput
        id="outlined-adornment-weight"
        sx={{
          '& legend': {
            maxWidth: '100%',
            height: 'fit-content',
            '& span': {
              opacity: 1,
            },
          },
        }}
        endAdornment={
          <InputAdornment position="end">
            <Tooltip title="Legend position must be between 0 to -1" placement="top">
              <IconButton aria-label="tooltip">
                <InfoIcon />
              </IconButton>
            </Tooltip>
          </InputAdornment>
        }
        name="ledendY"
        type="number"
        label="Legend Position"
        value={ledendY}
        onChange={handleLegendYChange}
        aria-describedby="outlined-weight-helper-text"
        inputProps={{
          'aria-label': 'weight',
        }}
      />
    </FormControl>
  );
};

export default ChartLegendsSettings;
