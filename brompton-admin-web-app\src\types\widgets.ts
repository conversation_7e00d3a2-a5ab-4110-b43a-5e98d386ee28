import { Layout } from 'react-grid-layout';
import { elementVariable } from './diagram';

export type YAxisSide = 'left' | 'right';

export type WidgetTitle = {
  title: {
    value: string;
    isVisible: boolean;
    color: string;
    fontSize?: number;
    fontWeight?: string;
  };
};

export type WidgetMode = 'template' | 'dashboard';

export type RealtimeSettings = {
  isRealTime: boolean;
  retainPeriod: number;
  refreshInterval: number;
};
export type WidgetCommonSettings = WidgetTitle &
  RealtimeSettings & {
    isValid: boolean;
    isDirty: boolean;
    mode: WidgetMode;
    isChildWidget: boolean;
    dashboardOrTemplate: 'template' | 'dashboard';
    assetOrAssetType: number | null;
    dashboard: {
      id: number;
      title: string;
    } | null;
    openDashboardInNewTab: boolean;
  };

export type ChartWidget =
  | { chartType: 'bar'; settings: BarChartWidget }
  | {
      chartType: 'scatter';
      settings: ScatterChartWidget;
    }
  | { chartType: 'heatmap'; settings: HeatmapChartWidget }
  | { chartType: 'indicator'; settings: GaugeChartWidget }
  | { chartType: 'sankey'; settings: SankeyChartWidget }
  | { chartType: 'bullet'; settings: BulletChartWidget };

export type TableWidget = WidgetCommonSettings &
  OverriderAssetTz &
  DataWidget & {
    assetMeasure: AssetMeasureOptions[];
    dbMeasureIdToName: Record<string, string>;
    fontSize: number;
    fontWeight: string;
    selectedDbMeasureId: string;
    selectedTitles: string[];
  };

export type TitleWidget = WidgetCommonSettings &
  DataWidget & {
    fontSize: number;
    fontWeight: string;
    valueMode: 'fixed' | 'measurement';
    fixedValue?: string;
    assetId?: number;
    measurementId?: number;
    showUnit?: boolean;
    selectedDbMeasureId: string;
    timeContext?: {
      realTime: boolean;
      overrideTimezone: boolean;
      overrideSamplePeriod: boolean;
      overrideTimeRange: boolean;
    };
    // New properties for fixed value formatting
    fixedFontSize?: number;
    fixedFontWeight?: string;
    fixedColor?: string;
  };

export type ChartType = ChartWidget extends { chartType: infer T } ? T : never;
export type ChartSettings = ChartWidget extends { settings: infer T } ? T : never;
export type RealTimeChartSettings = ChartWidget extends { settings: infer T } ? T : never;

export type ChartMeasureSetting = {
  yAxisSide: YAxisSide;
  showSum: boolean;
};

export type addAnnotation = boolean;

export type MeasureColorPair = {
  measureId: string;
  color: string;
};
export type OverriderAssetTz = {
  overrideAssetTz: boolean;
  overrideAssetTzValue: boolean;
};
export type OverrideAssetTzWidgets =
  | StatsWidget
  | BarChartWidget
  | ScatterChartWidget
  | HeatmapChartWidget
  | SankeyChartWidget
  | GaugeChartWidget
  | KPIColorBox
  | KPIPercentage
  | KPIValueIndicator
  | KPISparkline
  | KPITable
  | TableWidget
  | KpiCurrentWidget
  | MapWidget
  | KPIBarChart
  | AlertWidget
  | DashboardWidget
  | BulletChartWidget;

export type setOverrideAssetTzWidgets = (
  value: ((prevState: OverrideAssetTzWidgets) => OverrideAssetTzWidgets) | OverrideAssetTzWidgets,
) => void;

export type DataWidget = OverriderAssetTz & {
  aggBy: number;
  samplePeriod: number;
  overrideGlobalSettings: boolean;
  isRelativeToGlboalEndTime: boolean;
  timeRange: number;
  startDate: number;
  endDate: number;
  globalSamplePeriod: boolean;
  showForecast?: boolean;
  period?: string;
};
export type Forecast = {
  showForecast: boolean;
  period: '24hr' | 'eom';
  forecastColor: string;
  showMean: boolean;
  meanName: string;
  meanColor: string;
  meanStyle: 'solid' | 'dash' | 'dot';
};
export const forecastPeriods = ['24hr', 'eom'];
export type ChartThreshold = {
  showThreshold: boolean;
  treshdold: {
    thresholdName: string;
    thresholdValue: number;
    thresholdColor: string;
    thresholdStyle: 'solid' | 'dash' | 'dot';
  };
};
export type ChartWidgetCommon = WidgetCommonSettings & DataWidget & OverriderAssetTz;
export type MultiMeasureChartWidgets = ScatterChartWidget | BarChartWidget;
export type setMultiMeasureChartSettings = (
  value:
    | ((prevState: MultiMeasureChartWidgets) => MultiMeasureChartWidgets)
    | MultiMeasureChartWidgets,
) => void;
export type SumDelta = {
  showSum: boolean;
  sumLabel: string;
  showDelta: boolean;
  deltaLabel: string;
};

export type AssetMeasureOptions = {
  assetId: string;
  measureId: string[];
};
export type BarChartWidget = ChartWidgetCommon &
  MinMaxAvg &
  Forecast &
  SumDelta &
  ChartThreshold & {
    showStacked: {
      show: boolean;
    };
    showRangeSlider: boolean;
    dbMeasureIdToAnnotation: Record<string, addAnnotation>;
    dbMeasureIdToSetting: Record<string, ChartMeasureSetting>;
    assetMeasure: AssetMeasureOptions[];
    dbMeasureIdToName: Record<string, string>;
    selectedTitles: string[];
    barColors: MeasureColorPair[];
    overrideGlobalBarColor: boolean;
    legendY: number;
  };
export type Label = {
  sourceFrom: 'Calculated' | 'Default';
  sourceName: string;
  sourceAssetMeasure?: {
    assetId: string;
    measureId: string;
  };
  aggBy?: number;
  sourceLabel: string;
  linkedDashboard?: {
    type: 'dashboard' | 'template';
    id: number;
    title: string;
    openInNewTab?: boolean;
  };
};
export type Connection = {
  source: string;
  destination: string;
  color?: string;
};
export type SankeyChartWidget = ChartWidgetCommon & {
  Label: Label[];
  connections: Connection[];
  showColor: boolean;
};
export type HeatMapPastelColor =
  | 'Jet'
  | 'BlackBody'
  | 'Blured'
  | 'Earth'
  | 'Electric'
  | 'Greens'
  | 'Greys'
  | 'Hot'
  | 'Picnic'
  | 'Portland'
  | 'RdBu'
  | 'YlGnBu'
  | 'YOrRed';
export const HeatMapPastelColorOptions = [
  'Jet',
  'BlackBody',
  'Blured',
  'Earth',
  'Electric',
  'Greens',
  'Greys',
  'Hot',
  'Picnic',
  'Portland',
  'RdBu',
  'YlGnBu',
  'YOrRed',
];
export type HeatmapChartWidget = ChartWidgetCommon & {
  selectedAssetId: string;
  assetMeasure: AssetMeasureOptions;
  selectedDbMeasureId: string;
  showTotal: boolean;
  groupX: string;
  groupY: string;
  pastelColor: HeatMapPastelColor;
  dbMeasureIdToName: Record<string, string>;
};
export type MinMaxAvg = {
  min: {
    show: boolean;
  };
  max: {
    show: boolean;
  };
  avg: {
    show: boolean;
  };
};

export type ScatterChartWidget = ChartWidgetCommon &
  MinMaxAvg &
  Forecast &
  SumDelta &
  ChartThreshold & {
    dbMeasureIdToSetting: Record<string, ChartMeasureSetting>;
    dbMeasureIdToAnnotation: Record<string, addAnnotation>;
    selectedTitles: string[];
    selectedSparkTitle?: string;
    selectedSparkMeasure?: {
      assetId: string;
      measureId: string;
    };
    showRangeSlider: boolean;
    showStacked?: boolean;
    showArea?: boolean;
    barColors: MeasureColorPair[];
    overrideGlobalBarColor: boolean;
    assetMeasure: AssetMeasureOptions[];
    dbMeasureIdToName: Record<string, string>;
    legendY: number;
    showSparkLine?: boolean;
  };
export type GaugeChartWidget = ChartWidgetCommon & {
  dbMeasureIdToSetting: Record<string, ChartMeasureSetting>;
  dbMeasureIdToName: Record<string, string>;
  selectedDbMeasureId: string;
  barColor: string;
  selectedAssetId: string;
  assetMeasure: AssetMeasureOptions;
  threshHoldColor: string;
  threshHoldValue: number;
  showThreshHoldValue: boolean;

  showIndicator1: boolean;
  indicator1Value: number;
  indicator1Color: string;

  indicator2Color: string;
  showIndicator2: boolean;
  indicator2Value: number;

  minValue: number;
  maxValue: number;
  margin: {
    t: number;
    b: number;
    l: number;
    r: number;
  };
};
export type BulletChartWidget = ChartWidgetCommon & {
  dbMeasureIdToSetting: Record<string, ChartMeasureSetting>;
  dbMeasureIdToName: Record<string, string>;
  selectedDbMeasureId: string;
  barColor: string;
  selectedAssetId: string;
  assetMeasure: AssetMeasureOptions;
  threshHoldColor: string;
  threshHoldValue: number;
  showThreshHoldValue: boolean;

  showIndicator1: boolean;
  indicator1Value: number;
  indicator1Color: string;

  indicator2Color: string;
  showIndicator2: boolean;
  indicator2Value: number;

  minValue: number;
  maxValue: number;
  margin: {
    t: number;
    b: number;
    l: number;
    r: number;
  };
};
export type StatsWidget = WidgetCommonSettings &
  OverriderAssetTz &
  DataWidget & {
    dbMeasureIdToName: Record<string, string>;
    fontSize: number;
    fontWeight: string;
    selectedAssetId: string;
    assetMeasure: AssetMeasureOptions;
    selectedDbMeasureId: string;
    showMin: boolean;
    showMinLable: boolean;
    minColor: string;
    minBorder: boolean;
    min: {
      label: string;
      fontSize: number;
      fontWeight: string;
      color: string;
    };
    showMax: boolean;
    showMaxLabel: boolean;
    maxColor: string;
    maxBorder: boolean;
    max: {
      label: string;
      fontSize: number;
      fontWeight: string;
      color: string;
    };
    showAvg: boolean;
    showAvgLabel: boolean;
    avgColor: string;
    avg: {
      label: string;
      fontSize: number;
      fontWeight: string;
      color: string;
    };
    avgBorder: boolean;
    showDelta: boolean;
    showDeltaLabel: boolean;
    deltaColor: string;
    deltaBorder: boolean;
    delta: {
      label: string;
      fontSize: number;
      fontWeight: string;
      color: string;
    };
    showSum: boolean;
    showSumLabel: boolean;
    sumColor: string;
    sumBorder: boolean;
    sum: {
      label: string;
      fontSize: number;
      fontWeight: string;
      color: string;
    };
    showTotal: boolean;
    total: {
      label: string;
      fontSize: number;
      fontWeight: string;
      color: string;
    };
    showCurrent: boolean;
    currentColor: string;
    showCurrentLabel: boolean;
    showCurrentBorder: boolean;
    current: {
      label: string;
      fontSize: number;
      fontWeight: string;
      color: string;
    };
    showTotalLabel: boolean;
    totalColor: string;
    totalBorder: boolean;
  };

export type TwoDimWidget = WidgetCommonSettings &
  DataWidget & {
    isEditable: boolean;
    selectedDbMeasureId: string;
    jsonFile: string;
    isIconWidget: boolean;
    showCurrent: boolean;
    currentColor: string;
    showCurrentLabel: boolean;
    showCurrentBorder: boolean;
    current: {
      fontSize: number;
      fontWeight: string;
      color: string;
    };
  };

export type DiagramWidget = WidgetCommonSettings &
  DataWidget & {
    isEditable: boolean;
    measureVariables: {
      cellId: string;
      variableName: string;
      measurementId: string;
      measurementValue: string;
    }[];
    selectedDiagram: {
      id: number;
      name: string;
    } | null;
    zoomLevel: number;
    disableZoom: boolean;
    elementIdVariabels: Record<string, elementVariable[]>;
    elementVariable: elementVariable[];
    selectedDbMeasureId: string;
    jsonFile: string;
    isIconWidget: boolean;
    showCurrent: boolean;
    currentColor: string;
    showCurrentLabel: boolean;
    showCurrentBorder: boolean;
    current: {
      fontSize: number;
      fontWeight: string;
      color: string;
    };
  };

export type SVGTexts = { title: string; position: { x: number; y: number }; id: string };

export type ImageWidget = WidgetCommonSettings &
  DataWidget & {
    isEditable: boolean;
    measureIdToImageTextDetails: Record<string, ImageTextDetails>;
    svgTexts: SVGTexts[];
    assetMeasure: AssetMeasureOptions[];
    dbMeasureIdToName: Record<string, string>;
    selectedTitles: string[];
    allowUpload: boolean;
    uploadedImage: string | null;
    image:
      | '/icons/flow-computer.svg'
      | '/icons/solar_panel.gif'
      | '/icons/Voltage_Optimizer.svg'
      | '/icons/VerticalWidget.svg'
      | '/icons/flow-meter.svg'
      | '/icons/gas-meter-shaded.svg'
      | '/icons/diamond.svg'
      | '/icons/power-pole.svg'
      | '/icons/water-meter-shaded.svg'
      | '/icons/solar-panel-front.svg'
      | '/icons/solar-2.svg'
      | '/icons/SLD_Temple.svg'
      | '/icons/SLD_Shree_Temple_2.svg'
      | '/icons/leaf.svg'
      | '/icons/cloud.png'
      | '/icons/co2.svg'
      | '/icons/electric_tower.svg'
      | '/icons/natural-gas.svg'
      | '/icons/solar-with-sun.svg'
      | '/icons/water-faucet.svg'
      | '/icons/white.svg'
      | '/icons/cloudy.svg'
      | '/icons/partly-cloudy.svg'
      | '/icons/tac-simple-sld.svg'
      | '/icons/temple-simple-sld.svg'
      | string
      | null;
    imgDashboard: {
      dashboardOrTemplate: 'dashboard' | 'template';
      assetOrAssetType: number | null;
      dashboard: {
        id: number;
        title: string;
      } | null;
      openDashboardInNewTab: boolean;
    };
    labelAndUnits: Record<string, { label: string; unit: string }>;
    font: {
      size: number;
      weight: string;
      color: string;
    };
    margin: {
      t: number;
      b: number;
      l: number;
      r: number;
    };
  };

export type ImageTextDetails = {
  id: string;
  value: string;
  label: string;
  unit: string;
  positionX: number;
  positionY: number;
  dashboard: {
    id: number;
    title: string;
  } | null;
  dashboardOrTemplate: 'dashboard' | 'template';
  assetOrAssetType: number | null;
  openDashboardInNewTab: boolean;
};
export type MapLocationColorLongLats = {
  measureId: string;
  color: string;
  measureName: string;
  location: {
    lat: number;
    lon: number;
  };
};
export type MapAssetMeasures = {
  assetId: string;
  measureId: string;
};
export type MapMarker = {
  markerName: string;
  location: {
    lat: number;
    lon: number;
  };
  overrideLocation?: boolean;
  color: string;
  selectedTitles: string[];
  assetMeasures: MapAssetMeasures[];
  labelAndUnits: Record<string, { label: string; unit: string; value: string }>;
  dashboard: {
    id: number;
    title: string;
  } | null;
};
export type MapWidget = WidgetCommonSettings &
  OverriderAssetTz &
  DataWidget & {
    zoomLevel: number;
    changeMapCenter: boolean;
    mapCenter: {
      lat: number;
      lon: number;
    };
    markers: MapMarker[];
  };
export type PrefixSuffix = {
  prefix: {
    isVisible: boolean;
    value: string;
  };
  suffix: {
    isVisible: boolean;
    value: string;
  };
};
export type KPIValueIndicator = WidgetCommonSettings &
  DataWidget &
  OverriderAssetTz &
  PrefixSuffix & {
    dbMeasureIdToName: Record<string, string>;
    selectedAssetId: string;
    assetMeasure: AssetMeasureOptions;
    selectedDbMeasureId: string;
    lineColor: string;
  };
export type KPIColorBox = WidgetCommonSettings &
  DataWidget &
  Forecast &
  OverriderAssetTz &
  PrefixSuffix & {
    selectedAssetId: string;
    assetMeasure: AssetMeasureOptions;
    dbMeasureIdToName: Record<string, string>;
    selectedDbMeasureId: string;
    positive: {
      backgroundColor: string;
      font: {
        color: string;
      };
    };
    negative: {
      backgroundColor: string;
      font: {
        color: string;
      };
    };
    sparkLineColor: string;
    menuOption: 'Stats' | 'Chart' | 'Both';
  };

export type KPISparkline = WidgetCommonSettings &
  DataWidget &
  OverriderAssetTz &
  PrefixSuffix & {
    selectedAssetId: string;
    assetMeasure: AssetMeasureOptions;
    selectedDbMeasureId: string;
    dbMeasureIdToName: Record<string, string>;
    dbMeasureIdToSetting: Record<string, ChartMeasureSetting>;
    showSparkLine: boolean;
    tooltip: string;
    sparkLineColor: string;
    value: {
      isOverRide: boolean;
      color: string;
      fontWeight: string;
      fontSize: number;
    };
  };
export type KPIBarChartSamplePeriod = 'D' | 'W' | 'M' | '6M' | 'Y';
export type KPIBarChart = WidgetCommonSettings &
  DataWidget & {
    selectedDbMeasureId: string;
    selectedAssetId: string;
    assetMeasure: AssetMeasureOptions;
    dbMeasureIdToName: Record<string, string>;
    selectedSamplePeriod: KPIBarChartSamplePeriod;
    overrideBarColor: boolean;
    barColor: string;
    showPrevious: boolean;
  };

export type KPIPercentage = WidgetCommonSettings &
  OverriderAssetTz &
  DataWidget & {
    selectedTitles: string[];
    selectedAssetId: string;
    dbMeasureIdToName: Record<string, string>;
    assetMeasure: AssetMeasureOptions;
    selectedDbMeasureId: string;
    dbMeasureIdToSetting: Record<string, ChartMeasureSetting>;
    positiveColor: string;
    negativeColor: string;
  };

export type KPITable = WidgetCommonSettings &
  OverriderAssetTz &
  DataWidget & {
    assetMeasure: AssetMeasureOptions[];
    dbMeasureIdToName: Record<string, string>;
    selectedTitles: string[];
    dbMeasureIdToSetting: Record<string, ChartMeasureSetting>;
    min: {
      label: string;
      show: boolean;
    };
    max: {
      label: string;
      show: boolean;
    };
    mean: {
      label: string;
      show: boolean;
    };
    delta: {
      label: string;
      show: boolean;
    };
    sum: {
      label: string;
      show: boolean;
    };
    current: {
      label: string;
      show: boolean;
    };
  };
export type Weather = WidgetCommonSettings & {
  stationId: string;
  overrideGlobalSettings: boolean;
  timeRange: number;
  startDate: number;
  endDate: number;
};
export type RealtimeExcludes = Exclude<
  WidgetCommonSettings,
  'isRealTime' | 'retainPeriod' | 'refreshInterval'
>;
export type RealTimeChart = RealtimeExcludes &
  OverriderAssetTz &
  DataWidget & {
    assetMeasure: AssetMeasureOptions;
    dbMeasureIdToName: Record<string, string>;
    fontSize: number;
    fontWeight: string;
    selectedDbMeasureId: string;
    retention: number;
    selectedTitles: string[];
  };

export type PeriodOrNull = 'Daily' | 'Weekly' | 'Monthly' | null;
export type KpiCurrentWidget = WidgetCommonSettings &
  DataWidget & {
    isEditable: boolean;
    selectedAssetId: string;
    assetMeasure: AssetMeasureOptions;
    selectedDbMeasureId: string;
    dbMeasureIdToName: Record<string, string>;
    image:
      | '/icons/flow-computer.svg'
      | '/icons/solar_panel.gif'
      | '/icons/Voltage_Optimizer.svg'
      | '/icons/VerticalWidget.svg'
      | '/icons/flow-meter.svg'
      | '/icons/gas-meter-shaded.svg'
      | '/icons/diamond.svg'
      | '/icons/power-pole.svg'
      | '/icons/water-meter-shaded.svg'
      | '/icons/solar-panel-front.svg'
      | '/icons/solar-2.svg'
      | '/icons/SLD_Temple.svg'
      | '/icons/SLD_Shree_Temple_2.svg'
      | '/icons/leaf.svg'
      | '/icons/cloud.png'
      | '/icons/co2.svg'
      | '/icons/electric_tower.svg'
      | '/icons/natural-gas.svg'
      | '/icons/solar-with-sun.svg'
      | '/icons/water-faucet.svg'
      | '/icons/white.svg'
      | '/icons/cloudy.svg'
      | '/icons/partly-cloudy.svg'
      | null;
    font: {
      size: number;
      weight: string;
      color: string;
    };
    placement: 'Image-Left' | 'Image-Right' | 'Image-Center';
    imageSize: 'Small' | 'Medium' | 'Large';
    samples: PeriodOrNull[];
  };
export type AlertWidget = WidgetCommonSettings &
  DataWidget & {
    assetMeasure: AssetMeasureOptions[];
    dbMeasureIdToName: Record<string, string>;
    selectedTitles: string[];
    assetTypes: string[];
    measurementTypes: string[];
    assetTypeMetrics: {
      assetType: string;
      metrics: string[];
    }[];
  };

export type DashboardWidget = WidgetCommonSettings &
  DataWidget & {
    assetMeasure: AssetMeasureOptions[];
    dbMeasureIdToName: Record<string, string>;
    selectedTitles: string[];
    assetTypes: string[];
    measurementTypes: string[];
    assetId: string;
    metricToMeasurementMap: Record<string, string>;
    dashboardTemplateData: {
      widgets: Widget[];
      deleteWidgets: string[];
      widgetLayout: Layout[];
      lastWidgetId: number;
    } | null;
    assetTypeMetrics: {
      assetType: string;
      metrics: string[];
    }[];
    assetOption: {
      label: string;
      id: number;
    };
    dashboardTemplateOption: {
      label: string;
      id: number;
    };
  };

export type MultiPlotWidget = WidgetCommonSettings &
  DataWidget & {
    assetMeasure: AssetMeasureOptions;
    dbMeasureIdToName: Record<string, string>;
    selectedTitles: string[];
    layoutType: 'grid' | 'horizontal' | 'vertical';
    plotsPerRow: number;
    legend: {
      y: number;
    };
    subplots: {
      id: number;
      type: string;
      showRangeSlider?: boolean;
      showArea?: boolean;
      showSparkline?: boolean;
      assetMeasures: {
        id: number;
        assetId: string;
        measureId: string[];
        chartType: string;
        aggBy: number;
        showMinLine: boolean;
        showMaxLine: boolean;
        showAvgLine: boolean;
        showThresholdLine: boolean;
        thresholdName: string;
        thresholdColor: string;
        thresholdValue: number;
        overrideChartColor: boolean;
        chartColor: string;
        thresholdStyle: 'solid' | 'dash' | 'dot';
        ForecastSettings: Forecast;
        selectedDbMeasureId: string;
      }[];
    }[];
  };

export type Widget =
  | { type: 'chart'; id: string; settings: ChartWidget }
  | { type: 'stats'; id: string; settings: StatsWidget }
  | { type: 'table'; id: string; settings: TableWidget }
  | { type: 'title'; id: string; settings: TitleWidget }
  | { type: 'static'; id: string; settings: ImageWidget }
  | { type: 'solar_panel'; id: string; settings: ImageWidget }
  | { type: 'voltage'; id: string; settings: ImageWidget }
  | { type: 'image'; id: string; settings: ImageWidget }
  | { type: 'vertical'; id: string; settings: ImageWidget }
  | { type: 'map'; id: string; settings: MapWidget }
  | { type: 'kpi-value-indicator'; id: string; settings: KPIValueIndicator }
  | { type: 'kpi-color-box'; id: string; settings: KPIColorBox }
  | { type: 'kpi-percentage'; id: string; settings: KPIPercentage }
  | { type: 'kpi-table'; id: string; settings: KPITable }
  | { type: 'image-stats'; id: string; settings: KpiCurrentWidget }
  | { type: 'kpi-sparkline'; id: string; settings: KPISparkline }
  | { type: 'kpi-bar-chart'; id: string; settings: KPIBarChart }
  | { type: 'Weather'; id: string; settings: Weather }
  | { type: 'real-time'; id: string; settings: RealTimeChart }
  | { type: 'alert-widget'; id: string; settings: AlertWidget }
  | { type: 'dashboard-widget'; id: string; settings: DashboardWidget }
  | { type: 'Diagram'; id: string; settings: DiagramWidget }
  | { type: 'multi-plot'; id: string; settings: MultiPlotWidget };
export type WidgetType = Widget extends { type: infer T } ? T : never;
export type NonRealTimeWidgets = Exclude<Widgets, RealTimeChart>;
export type WidgetSettings = Widget extends { settings: infer T } ? T : never;
export type WidgetName =
  | 'Image'
  | 'Value'
  | 'Chart'
  | 'Stats'
  | 'Table'
  | 'Map'
  | 'Circle'
  | 'KPI Trend'
  | 'KPI Sparkline'
  | 'KPI Percentage'
  | 'KPI Table'
  | 'Weather'
  | 'KPI Bar chart'
  | 'KPI Current'
  | 'real-time'
  | 'alert-widget'
  | 'dashboard-widget'
  | 'multi-plot'
  | 'Diagram';

export type Widgets =
  | ScatterChartWidget
  | BarChartWidget
  | MapWidget
  | HeatmapChartWidget
  | GaugeChartWidget
  | SankeyChartWidget
  | BulletChartWidget
  | StatsWidget
  | KpiCurrentWidget
  | KPIValueIndicator
  | KPISparkline
  | KPIColorBox
  | KPIPercentage
  | KPITable
  | ImageWidget
  | KPIBarChart
  | RealTimeChart
  | DiagramWidget
  | AlertWidget
  | DashboardWidget
  | MultiPlotWidget
  | TableWidget
  | TitleWidget;

export type ForecastWidget = ScatterChartWidget | BarChartWidget | KPIColorBox;

export type setForecastWidgetSettings = (
  value: ((prevState: ForecastWidget) => ForecastWidget) | ForecastWidget,
) => void;
export type setSettings = (value: ((prevState: Widgets) => Widgets) | Widgets) => void;

export type SumDeltaWidgets = ScatterChartWidget | BarChartWidget;
export type setSumDeltaWidgetSettings = (
  value: ((prevState: SumDeltaWidgets) => SumDeltaWidgets) | SumDeltaWidgets,
) => void;
export type SingleMeasureWidgets =
  | HeatmapChartWidget
  | GaugeChartWidget
  | BulletChartWidget
  | StatsWidget
  | KPIValueIndicator
  | KpiCurrentWidget
  | KPIColorBox
  | KPIPercentage
  | KPISparkline
  | KPIBarChart
  | RealTimeChart;
// | TableWidget;
export type setSingleMeasureWidgetSettings = (
  value: ((prevState: SingleMeasureWidgets) => SingleMeasureWidgets) | SingleMeasureWidgets,
) => void;

export type MultiMeasureWidgets =
  | ScatterChartWidget
  | BarChartWidget
  | KPITable
  | TableWidget
  | AlertWidget
  | ImageWidget;
export type setMultiMeasureWidgetSettings = (
  value: ((prevState: MultiMeasureWidgets) => MultiMeasureWidgets) | MultiMeasureWidgets,
) => void;
export type KPIWidgets = KPIValueIndicator | KPISparkline | KPIColorBox;
export type setKPIWidgetSettings = (
  value: ((prevState: KPIWidgets) => KPIWidgets) | KPIWidgets,
) => void;
export type ImageWidgetType = 'static' | 'solar_panel' | 'voltage' | 'image' | 'vertical';
export type ImageWidgetSettings = ImageWidget;
export type ImageWidgetFontsProps<T extends ImageWidgetSettings> = {
  settings: T;
  setSettings: (value: (prevState: T) => T) => void;
};

export function isImageWidgetType(
  widget: Widget,
): widget is Widget & { settings: ImageWidgetSettings } {
  const { type } = widget;
  return (
    type === 'static' ||
    type === 'solar_panel' ||
    type === 'voltage' ||
    type === 'image' ||
    type === 'vertical'
  );
}

export function isStatsWidgetType(widget: Widget): widget is Widget & { settings: StatsWidget } {
  return widget.type === 'stats';
}

export function isChartWidgetType(widget: Widget): widget is Widget & { settings: ChartWidget } {
  return widget.type === 'chart';
}

export function isRealTimeChartWidgetType(
  widget: Widget,
): widget is Widget & { settings: RealTimeChart } {
  return widget.type === 'real-time';
}

export function isKPIColorBoxWidgetType(
  widget: Widget,
): widget is Widget & { settings: KPIColorBox } {
  return widget.type === 'kpi-color-box';
}

export function isKPIPercentageWidgetType(
  widget: Widget,
): widget is Widget & { settings: KPIPercentage } {
  return widget.type === 'kpi-percentage';
}

export function isKPITableWidgetType(widget: Widget): widget is Widget & { settings: KPITable } {
  return widget.type === 'kpi-table';
}

export function isTableWidgetType(widget: Widget): widget is Widget & { settings: TableWidget } {
  return widget.type === 'table';
}

export function isAlertWidgetType(widget: Widget): widget is Widget & { settings: AlertWidget } {
  return widget.type === 'alert-widget';
}

export function isDashboardWidgetType(
  widget: Widget,
): widget is Widget & { settings: DashboardWidget } {
  return widget.type === 'dashboard-widget';
}

export function isMapWidgetType(widget: Widget): widget is Widget & { settings: MapWidget } {
  return widget.type === 'map';
}

export function isKPIBarChartWidgetType(
  widget: Widget,
): widget is Widget & { settings: KPIBarChart } {
  return widget.type === 'kpi-bar-chart';
}

export function isImageStatsType(
  widget: Widget,
): widget is Widget & { settings: KpiCurrentWidget } {
  return widget.type === 'image-stats';
}
export function isTitleWidgetType(widget: Widget): widget is Widget & { settings: TitleWidget } {
  return widget.type === 'title';
}
export function isWeatherWidgetType(widget: Widget): widget is Widget & { settings: Weather } {
  return widget.type === 'Weather';
}
export function isKPIValueIndicatorWidgetType(
  widget: Widget,
): widget is Widget & { settings: KPIValueIndicator } {
  return widget.type === 'kpi-value-indicator';
}
export function isDiagramWidgetType(
  widget: Widget,
): widget is Widget & { settings: DiagramWidget } {
  return widget.type === 'Diagram';
}

export const images: string[] = [
  '/icons/flow-computer.svg',
  '/icons/solar_panel.gif',
  '/icons/Voltage_Optimizer.svg',
  '/icons/VerticalWidget.svg',
  '/icons/flow-meter.svg',
  '/icons/gas-meter-shaded.svg',
  '/icons/diamond.svg',
  '/icons/power-pole.svg',
  '/icons/water-meter-shaded.svg',
  '/icons/solar-panel-front.svg',
  '/icons/solar-2.svg',
  '/icons/SLD_Temple.svg',
  '/icons/SLD_Shree_Temple_2.svg',
  '/icons/leaf.svg',
  '/icons/cloud.png',
  '/icons/co2.svg',
  '/icons/electric_tower.svg',
  '/icons/natural-gas.svg',
  '/icons/solar-with-sun.svg',
  '/icons/water-faucet.svg',
  '/icons/white.svg',
  '/icons/cloudy.svg',
  '/icons/partly-cloudy.svg',
];
