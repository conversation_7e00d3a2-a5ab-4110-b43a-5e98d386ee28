import { test, expect } from '@playwright/test';

test('test', async ({ page }) => {
  await page.goto('https://test.pivotol.ai/login');
  await page.getByLabel('Username *').click();
  await page.getByLabel('Username *').fill('test');
  await page.getByLabel('Password *').click();
  await page.getByLabel('Password *').fill('Br0mpt0n!0T');
  await page.getByRole('button', { name: 'Log in' }).click();
  await page.setViewportSize({ width: 1920, height: 1200 });
  await page.getByRole('button', { name: 'CalcEngine' }).click();
  await page.waitForTimeout(3000);
  await page.getByRole('menuitem', { name: 'Unit of Groups' }).click();
  await page.getByRole('button', { name: 'Add New Unit' }).click();
  await page.getByRole('combobox', { name: 'Measurement Type' }).click();
  await page.getByRole('option', { name: 'Acceleration' }).click();
  await page.getByLabel('Unit of Group').click();
  await page.getByRole('option', { name: 'us' }).click();
  await page.getByRole('combobox', { name: 'Unit of Measure' }).click();
  await page.getByRole('option', { name: 'mm/s²' }).click();
  await page.getByRole('checkbox', { name: 'Is Default' }).check();
  await page.getByRole('button', { name: 'Save' }).click();
  await page.close();
});
