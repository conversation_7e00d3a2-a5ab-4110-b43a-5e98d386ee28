import { test, expect, locator } from '@playwright/test';
import { LoginDetails } from '../../POM/LoginDetails';
import { waitForDebugger } from 'inspector';

test('loginpage', async ({ page }) => {
  //Login
  const Login1 = new LoginDetails(page);
  await Login1.lauchURL(); // launching the URL
  await Login1.login('test', 'asdfasdf'); // Valid <PERSON>

  // new dashboard click
  //await page.click('#__next > div > div.MuiStack-root.css-92qf02 > div > div > div.MuiBox-root.css-xy51px > div.MuiBox-root.css-9nra4q > button');

  // selct the customer
  await page.getByLabel('Select Customer').click();
  await page.getByRole('option', { name: 'Brompton Energy Inc.', exact: true }).click();

  /*
       const closeModal = page.locator('.MuiDialog-closeButton');
  try {
    await closeModal.isVisible() && await closeModal.click();
  } catch (error) {
    console.log('Close modal button not visible.');
  }*/

  // click on new dashboard
  await page.getByText('Add Dashboard').click();
  console.log('add dashboard clicked');
  await page.waitForTimeout(5000);

  const clicl = page.locator('div[role="button"].MuiTreeItem-iconContainer').nth(1); // Adjust the index as needed
  //await page.waitForTimeout(5000);
  await clicl.dblclick();
  console.log('brenes');

  // await page.waitForSelector('input[type="checkbox"]', { state: 'visible' });

  // Click the first checkbox
  await page.locator('input[type="checkbox"]').first().click();
  console.log('Clicked the first checkbox!');

  await page.locator('id=widgets-icon').click();
  await page.waitForTimeout(2000);

  //drag the widget
  await page.locator('#stats').dragTo(page.locator('.react-grid-layout.layout'));
  await page.waitForTimeout(2000);

  /* const element = page.locator('text=Please select a measure from widget settings.');
    // Ensure the element is both attached and visible
    await element.waitFor({ state: 'visible', timeout: 5000 });
      // Now try to focus on it
      await element.focus({ timeout: 8000 });

      await page.evaluate(() => {
        window.scrollBy(0, -8000); // Scroll up 1000px
      });*/

  const parentElement = page.locator('.MuiBox-root.css-1j35o1p').nth(1); // Adjust selector if necessary
  // Hover over the parent element to make the icon visible
  await parentElement.hover();
  // Now, locate the 'MoreVertIcon' options button, which should be visible after hovering
  const optionsIcon = page.locator('[data-testid="MoreVertIcon"]').nth(0);
  // Wait for the icon to become visible and then click it
  await optionsIcon.waitFor({ state: 'visible', timeout: 120000 });
  await optionsIcon.click();

  await page.locator('//*[@id="widget-settings-menu-1"]/div[3]/ul/li[2]').click();
  //*[@id="widget-settings-menu-1"]/div[3]/ul/li[2]

  await page.locator('#title').dragTo(page.locator('.react-grid-layout.layout'));
  await page.waitForTimeout(2000);
  await page.locator('#table').dragTo(page.locator('.react-grid-layout.layout'));
  await page.waitForTimeout(2000);
  await page.locator('id=widgets-icon').click();

  //click on widget setting
  //await page.click('//div[@class=MuiDialog-root MuiModal-root css-126xj0f]');

  await page.close();
});
