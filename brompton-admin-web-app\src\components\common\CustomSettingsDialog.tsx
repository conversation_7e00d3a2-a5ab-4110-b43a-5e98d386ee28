import React, { ReactNode, useState } from 'react';
import { Button } from '@mui/material';
import CancelIcon from '@mui/icons-material/Cancel';
import SaveAsIcon from '@mui/icons-material/SaveAs';
import CustomDialog from '~/components/common/CustomDialog';
import { WidgetSettings } from '~/types/widgets';

interface CustomSettingsDialogProps {
  open: boolean;
  widgetType: string;
  title: string | ReactNode;
  content: ReactNode;
  onClose: () => void;
  dialogActions: ReactNode;
  initialSettings: WidgetSettings;
  onUpdateClick: (settings: WidgetSettings) => void;
}

export const CustomSettingsDialog = ({
  open,
  widgetType,
  title,
  content,
  onUpdateClick,
  initialSettings,
  onClose,
  dialogActions,
}: CustomSettingsDialogProps) => {
  const [isOpen, setIsOpen] = useState<boolean>(open);
  const handleDialogOpen = () => setIsOpen(true);
  const handleCloseDialog = () => setIsOpen(false);
  const [settings, setSettings] = useState<WidgetSettings>(initialSettings);

  return (
    <CustomDialog
      open={open}
      onClose={handleCloseDialog}
      title={`${widgetType} Settings`}
      content={content}
      dialogActions={
        <>
          <Button
            autoFocus
            sx={{ mr: 'auto' }}
            size="medium"
            variant="outlined"
            startIcon={<CancelIcon />}
            onClick={handleCloseDialog}
          >
            Cancel
          </Button>
          <Button
            variant="contained"
            size="medium"
            startIcon={<SaveAsIcon />}
            onClick={() => {
              handleCloseDialog();
              onUpdateClick(settings);
            }}
          >
            Update
          </Button>
        </>
      }
    />
  );
};
