import { Grid, Paper } from '@mui/material';
import DiagramComponent from './DiagramComponent/DiagramComponent';
import Palette from './Palette/Palette';

const DiagramPage = () => {
  return (
    <Grid container spacing={0}>
      <Grid item xs={12} sm={2}>
        <Paper elevation={3}>
          <Palette />
        </Paper>
      </Grid>
      <Grid item xs={12} sm={10}>
        <Paper elevation={3}>
          <DiagramComponent />
        </Paper>
      </Grid>
    </Grid>
  );
};

export default DiagramPage;
