import { FileDownload } from '@mui/icons-material';
import { Box, Button, Container, IconButton, Tooltip } from '@mui/material';
import Link from 'next/link';
import { useEffect, useState } from 'react';
import AssetTemplateFileImporter from '~/components/AssetTemplate/AssetTemplateFileImporter';
import AssetTemplates from '~/components/AssetTemplate/AssetTemplates';
import PageName from '~/components/common/PageName/PageName';
import { useHasAdminAccess } from '~/hooks/useHasAdminAccess';
import { useHasPowerUserAccess } from '~/hooks/useHasPowerUserAccess';
import { useRolePermission } from '~/hooks/useRolePermission';
import { useGetAllBackOfficeAssetTypesQuery } from '~/redux/api/assetsApi';
import { AssetTypeOption } from '~/types/asset';
import { assetTypePathMapperFilterTemplates } from '~/utils/mappers/asset-type-mapper';

const Asset_Templates = () => {
  const { globalAdmin, admin } = useHasAdminAccess();
  const hasPowerUserAccess = useHasPowerUserAccess();
  const { hasPermission } = useRolePermission();
  const [importData, setImportData] = useState<boolean>(false);
  const [assetTypesWithPath, setAssetTypesWithPath] = useState<AssetTypeOption[]>([]);
  const {
    data: assetTypeListData,
    isLoading: isAssetTypeLoading,
    isSuccess: isSuccessfullBackOffieAssetTypes,
  } = useGetAllBackOfficeAssetTypesQuery();
  useEffect(() => {
    if (isSuccessfullBackOffieAssetTypes && assetTypeListData) {
      setAssetTypesWithPath(
        assetTypePathMapperFilterTemplates(
          assetTypeListData.map((item) => ({
            ...item,
            name: item.name,
            id: item.id,
          })),
        ),
      );
    }
  }, [assetTypeListData, isSuccessfullBackOffieAssetTypes, status]);
  return (
    <Container
      sx={{
        mt: 2,
        maxWidth: '100%',
        width: '100%',
        '@media (min-width: 1200px)': { width: '100%', maxWidth: '100%' },
      }}
    >
      <Box pb={0} pt={0} sx={{ display: 'flex' }}>
        <Box
          sx={{
            flexGrow: 1,
            display: 'flex',
            alignItems: 'center',
          }}
        >
          <PageName name="Asset templates" />

          {globalAdmin || admin || hasPowerUserAccess ? (
            <Box sx={{ display: 'flex', width: '100%', justifyContent: 'end', gap: 2 }}>
              <Tooltip title="Import" sx={{ float: 'right', mt: 1, mb: 1 }}>
                <IconButton onClick={() => setImportData(true)} color="primary">
                  <FileDownload />
                </IconButton>
              </Tooltip>
              {hasPermission('asset-instance.create') ? (
                <Button
                  variant="contained"
                  color="primary"
                  LinkComponent={Link}
                  href="/create-asset-template-instance"
                  sx={{ float: 'right', mt: 1, mb: 1 }}
                >
                  Add Asset template instance
                </Button>
              ) : null}
              {hasPermission('asset-template.create') ? (
                <Button
                  variant="contained"
                  color="primary"
                  LinkComponent={Link}
                  href="/create-asset-template"
                  sx={{ float: 'right', mt: 1, mb: 1, mr: 2 }}
                >
                  Add Asset template
                </Button>
              ) : null}
            </Box>
          ) : null}
        </Box>
      </Box>

      <AssetTemplateFileImporter
        open={importData}
        setImportData={setImportData}
        assetTypesWithPath={assetTypesWithPath}
      />
      <AssetTemplates />
    </Container>
  );
};
export default Asset_Templates;
