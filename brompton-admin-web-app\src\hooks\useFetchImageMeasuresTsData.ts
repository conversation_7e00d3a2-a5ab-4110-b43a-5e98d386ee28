import { useDispatch, useSelector } from 'react-redux';
import { getCustomerId } from '~/redux/selectors/customerSelectors';
import { useCallback, useEffect, useState } from 'react';
import { ThunkDispatch } from 'redux-thunk';
import { RootState } from '~/redux/store';
import { timeseriesApi } from '~/redux/api/timeseriesApi';
import { AssetMeasurementDetails } from '~/types/measures';
import { measuresApi } from '~/redux/api/measuresApi';
import { UnitOfMeasure } from '~/measurements/domain/types';
import { SingleScatterTimeSeriesData } from '~/types/timeseries';
import { AggByOptions, SamplePeriodOptions } from '~/types/dashboard';
import { getDbMeasureIdToAssetIdMap } from '~/redux/selectors/treeSelectors';
import { AnyAction } from 'redux';
import { ImageWidget, RealtimeSettings } from '~/types/widgets';
import { getGlobalEndDate, getGlobalStartDate } from '~/redux/selectors/widgetSelectors';
import {
  getAssetTz,
  getGlobalSamplePeriod,
  getGlobalTimeRangeType,
} from '~/redux/selectors/topPanleSelectors';

type MeasuresData = {
  isLoading: boolean;
  isError: boolean;
  error: string;
  tsData: SingleScatterTimeSeriesData;
  measureData: AssetMeasurementDetails;
  unitOfMeasures: UnitOfMeasure[];
};

type Params = {
  selectedTitles: string[];
  dataFetchSettings: ImageWidget & RealtimeSettings;
};

type ApiExtraArg = {
  measuresApi: typeof measuresApi;
  timeseriesApi: typeof timeseriesApi;
};

export function useFetchImageMeasuresTsData({ selectedTitles, dataFetchSettings }: Params) {
  const dispatch = useDispatch<ThunkDispatch<RootState, ApiExtraArg, AnyAction>>();
  const customerId = useSelector(getCustomerId);
  const dbMeasureIdToAssetIdMap = useSelector(getDbMeasureIdToAssetIdMap);
  const [state, setState] = useState<{
    data: undefined | MeasuresData[];
    isLoading: boolean;
    isError: boolean;
  }>({
    data: undefined,
    isLoading: false,
    isError: false,
  });

  const globalStartDate = useSelector(getGlobalStartDate);
  const globalEndDate = useSelector(getGlobalEndDate);
  const globalTimeRange = useSelector(getGlobalTimeRangeType);
  const globalSamplePeriod = useSelector(getGlobalSamplePeriod);
  const assetTz = useSelector(getAssetTz);

  const {
    startDate: selectedStartDate,
    endDate: selectedEndDate,
    aggBy: selectedAggBy,
    samplePeriod: selectedSamplePeriod,
    timeRange: selectedTimeRange,
    globalSamplePeriod: isGlobalSamplePeriodOverridden,
    overrideGlobalSettings: isGlobalTimeRangeSettingsOverridden,
    overrideAssetTz: isOverrideAssetTz,
    overrideAssetTzValue,
    isRealTime,
    refreshInterval,
    retainPeriod,
  } = dataFetchSettings;

  const [startDate, setStartDate] = useState(globalStartDate);
  const [endDate, setEndDate] = useState(globalEndDate);
  const [samplePeriod, setSamplePeriod] = useState(globalSamplePeriod);
  const [timeRange, setTimeRange] = useState(globalTimeRange);
  const [aggBy, setAggBy] = useState(selectedAggBy);
  const [assetTzOverride, setAssetTzOverride] = useState(isOverrideAssetTz);
  const [assetTzOverrideValue, setAssetTzOverrideValue] = useState(overrideAssetTzValue);
  useEffect(() => {
    setAggBy(selectedAggBy);

    if (isGlobalTimeRangeSettingsOverridden) {
      setStartDate(selectedStartDate);
      setEndDate(selectedEndDate);
      setTimeRange(selectedTimeRange);
    } else {
      setStartDate(globalStartDate);
      setEndDate(globalEndDate);
      setTimeRange(globalTimeRange);
    }

    if (isGlobalSamplePeriodOverridden) {
      setSamplePeriod(selectedSamplePeriod);
    } else {
      setSamplePeriod(globalSamplePeriod);
    }
    if (isOverrideAssetTz) {
      setAssetTzOverride(true);
      setAssetTzOverrideValue(overrideAssetTzValue);
    } else {
      setAssetTzOverride(false);
    }
  }, [
    globalEndDate,
    globalSamplePeriod,
    globalStartDate,
    globalTimeRange,
    isGlobalSamplePeriodOverridden,
    isGlobalTimeRangeSettingsOverridden,
    selectedAggBy,
    selectedEndDate,
    selectedSamplePeriod,
    selectedStartDate,
    selectedTimeRange,
    isOverrideAssetTz,
    overrideAssetTzValue,
  ]);

  const fetchMeasureData = useCallback(
    async (measureId: string) => {
      if (!dbMeasureIdToAssetIdMap[measureId]) {
        throw new Error(`No assetId found for ${measureId}`);
      }
      if (!measureId) {
        throw new Error(`Error invalid measureId: ${measureId}`);
      }
      const { data: measureData, isSuccess: isMeasureDataSuccess } = await dispatch(
        measuresApi.endpoints?.getMeasurementById.initiate({
          customerId,
          assetId: dbMeasureIdToAssetIdMap[measureId],
          measId: measureId,
        }),
      );

      if (!isMeasureDataSuccess || !measureData) {
        throw new Error(`Error fetching measure data for measureId: ${measureId}`);
      }

      return measureData;
    },
    [customerId, dbMeasureIdToAssetIdMap, dispatch],
  );

  const fetchTimeseriesData = useCallback(
    async (
      tsDbMeasureIds: number[],
    ): Promise<{ error: boolean; tsData: Record<number, SingleScatterTimeSeriesData> }> => {
      const { data: tsData, isSuccess: isTsSuccess } = await dispatch(
        timeseriesApi.endpoints.getMultiMeasurementSeries.initiate({
          customerId,
          measId: tsDbMeasureIds.filter(Boolean).sort().join(','),
          start: startDate,
          end: endDate,
          agg: AggByOptions[aggBy].serverValue,
          agg_period: SamplePeriodOptions[samplePeriod].serverValue,
          timeRangeType: timeRange,
          assetTz: isOverrideAssetTz ? assetTzOverrideValue : assetTz,
        }),
      );

      if (!isTsSuccess || !tsData) {
        return {
          error: true,
          tsData: {},
        };
      }

      return { error: false, tsData };
    },
    [
      dispatch,
      customerId,
      startDate,
      endDate,
      aggBy,
      samplePeriod,
      timeRange,
      isOverrideAssetTz,
      assetTzOverrideValue,
      assetTz,
    ],
  );

  const fetchUnitOfMeasure = useCallback(
    async (assetMeasurementTypeId: number) => {
      const { data: unitOfMeasures, isSuccess: isUnitOfMeasureSuccess } = await dispatch(
        measuresApi.endpoints?.getUnitsOfMeasure.initiate({
          measurementTypeId: assetMeasurementTypeId,
        }),
      );

      if (!isUnitOfMeasureSuccess || !unitOfMeasures) {
        throw new Error('Error fetching unit of measure data');
      }

      return unitOfMeasures;
    },
    [dispatch],
  ); // include assetMeasurementTypeId in the dependency array

  const fetchMeasuresData = useCallback(async () => {
    setState({ data: undefined, isLoading: true, isError: false });

    const apiResults = selectedTitles
      .filter((measureId) => measureId && measureId !== '')
      .map(async (measureId) => {
        try {
          const measureData = await fetchMeasureData(measureId);
          const unitOfMeasures = await fetchUnitOfMeasure(measureData.typeId);

          return {
            isLoading: false,
            isError: false,
            error: '',
            measureData,
            unitOfMeasures,
          } as MeasuresData;
        } catch (error) {
          return {
            isLoading: false,
            isError: true,
            error: error,
          } as MeasuresData;
        }
      });

    const results = await Promise.all(apiResults);
    const tsMeasureIds = results.map((result) => result.measureData?.measurementId);
    const { error, tsData } = await fetchTimeseriesData(tsMeasureIds);
    if (error) {
      setState({ data: undefined, isLoading: false, isError: true });
      return;
    }

    results.forEach((result) => {
      const seriesData = tsData[result.measureData.measurementId];
      if (seriesData?.error) {
        result.isLoading = false;
        result.isError = true;
        result.error = seriesData.error;
      } else {
        result.isLoading = false;
        result.tsData = seriesData;
      }
    });

    setState({ data: results, isLoading: false, isError: false });
  }, [selectedTitles, setState, fetchMeasureData, fetchUnitOfMeasure, fetchTimeseriesData]);

  useEffect(() => {
    if (selectedTitles.length > 0) fetchMeasuresData();
  }, [
    aggBy,
    customerId,
    dbMeasureIdToAssetIdMap,
    dispatch,
    endDate,
    fetchMeasureData,
    fetchTimeseriesData,
    fetchUnitOfMeasure,
    samplePeriod,
    selectedTitles,
    startDate,
    timeRange,
    assetTz,
    assetTzOverride,
    assetTzOverrideValue,
  ]);

  const fetchHistoryTimeseriesData = useCallback(
    async (
      tsDbMeasureIds: number[],
    ): Promise<{ error: boolean; tsData: Record<number, SingleScatterTimeSeriesData> }> => {
      if (tsDbMeasureIds.length === 0) {
        return { error: true, tsData: {} };
      }
      const endDate = new Date();
      const startDate = new Date(endDate.getTime() - retainPeriod * 60 * 1000);
      const { data: tsData, isSuccess: isTsSuccess } = await dispatch(
        timeseriesApi.endpoints.getMultipleHistoryMeasurementSeries.initiate({
          customerId,
          measId: tsDbMeasureIds.filter(Boolean).sort().join(','),
          start: startDate.getTime(),
          end: endDate.getTime(),
          assetTz: false,
        }),
      );

      if (!isTsSuccess || !tsData) {
        return {
          error: true,
          tsData: {},
        };
      }

      return { error: false, tsData };
    },
    [dispatch, customerId, retainPeriod],
  );

  useEffect(() => {
    if (!isRealTime) {
      return;
    }
    const intervalId = setInterval(() => {
      const fetcHistoryMeasuresData = async () => {
        setState({ data: undefined, isLoading: false, isError: false });
        const apiResults = selectedTitles
          .filter((measureId) => measureId && measureId !== '')
          .map(async (measureId) => {
            try {
              const measureData = await fetchMeasureData(measureId);
              const unitOfMeasures = await fetchUnitOfMeasure(measureData.typeId);
              return {
                isLoading: false,
                isError: false,
                error: '',
                measureData,
                unitOfMeasures,
              } as MeasuresData;
            } catch (error) {
              return {
                isLoading: false,
                isError: true,
                error: error,
              } as MeasuresData;
            }
          });
        const results = await Promise.all(apiResults);
        const tsMeasureIds = results.map((result) => result.measureData?.measurementId);
        const { error, tsData } = await fetchHistoryTimeseriesData(tsMeasureIds.filter(Boolean));
        if (error) {
          setState({ data: undefined, isLoading: false, isError: true });
          return;
        }
        results.forEach((result) => {
          const seriesData = tsData[result.measureData?.measurementId];
          if (seriesData?.error) {
            result.isLoading = false;
            result.isError = true;
            result.error = seriesData.error;
          } else {
            result.isLoading = false;
            result.tsData = seriesData;
          }
        });
        setState({
          ...state,
          data: results,
          isLoading: false,
          isError: false,
        });
      };
      if (selectedTitles && selectedTitles.length > 0) fetcHistoryMeasuresData();
    }, refreshInterval * 1000);

    return () => clearInterval(intervalId);
  }, [dispatch, isRealTime, refreshInterval, customerId, selectedTitles]);
  return state;
}
