import { render, screen, within } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import * as stories from './ConfirmationDialog.stories';
import { composeStories } from '@storybook/react';

const { ClosedDialog, OpenDeleteConfirmationDialog } = composeStories(stories);

describe('ConfirmationDialog', () => {
  test('closed dialog should not be visible', async () => {
    render(<ClosedDialog />);

    const result = screen.queryByText('Closed dialog');

    expect(result).toBeNull();
  });

  describe('Open dialog', () => {
    const deleteHandler = jest.fn();
    const cancelHandler = jest.fn();
    const user = userEvent.setup();
    let dialog: HTMLElement;

    beforeEach(() => {
      render(<OpenDeleteConfirmationDialog onDelete={deleteHandler} onCancel={cancelHandler} />);
      dialog = screen.getByRole('dialog');
    });

    it('should show given title', () => {
      const result = within(dialog).getByText('Delete measurement');

      expect(result).not.toBeNull();
    });

    it('should show given description', () => {
      const result = within(dialog).getByText('Are you sure you wish to delete this measurement?');

      expect(result).not.toBeNull();
    });

    it('should show delete button', () => {
      const deleteButton = within(dialog).getByRole('button', {
        name: 'Delete',
      });

      expect(deleteButton).not.toBeNull();
    });

    test('when clicking delete, delete callback should be called', async () => {
      const deleteButton = within(dialog).getByRole('button', {
        name: 'Delete',
      });

      await user.click(deleteButton);

      expect(deleteHandler.mock.calls.length).toBe(1);
    });

    it('should show cancel button', () => {
      const cancelButton = within(dialog).getByRole('button', {
        name: 'Cancel',
      });

      expect(cancelButton).not.toBeNull();
    });

    test('when clicking delete, delete callback should be called', async () => {
      const cancelButton = within(dialog).getByRole('button', {
        name: 'Cancel',
      });

      await user.click(cancelButton);

      expect(cancelHandler.mock.calls.length).toBe(1);
    });
  });
});
