import { Box, Typography } from '@mui/material';

import { CSSProperties, ReactNode } from 'react';

type NoMeasureSelectedProps = {
  style?: CSSProperties;
  message?: string | ReactNode;
};
const NoMeasureSelected = ({ message, style }: NoMeasureSelectedProps) => {
  return (
    <Box
      sx={{
        display: 'flex',
        justifyContent: 'center',
        height: '100%',
        alignItems: 'center',
        ...style,
      }}
    >
      <Typography>{message ? message : 'Please select a measure from widget settings.'}</Typography>
    </Box>
  );
};

export default NoMeasureSelected;
