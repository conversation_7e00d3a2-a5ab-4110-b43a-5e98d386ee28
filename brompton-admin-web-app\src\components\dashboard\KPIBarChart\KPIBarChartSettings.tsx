import {
  Box,
  Card,
  Checkbox,
  FormControl,
  FormControlLabel,
  FormGroup,
  FormLabel,
  Input,
} from '@mui/material';
import DataWidgetSettingsContainer from '~/components/common/DataWidgetSettingsContainer/DataWidgetSettingsContainer';
import SingleMeasureSelect from '~/components/common/SingleMeasureSelect';
import { KPIBarChart, setSingleMeasureWidgetSettings } from '~/types/widgets';

type KPIBarChartSettingsProps = {
  settings: KPIBarChart;
  handleSettingsChange: React.Dispatch<React.SetStateAction<KPIBarChart>>;
};
const KPIBarChartSettings = ({ settings, handleSettingsChange }: KPIBarChartSettingsProps) => {
  const handleOverriderBarColor = (
    event: React.ChangeEvent<HTMLInputElement>,
    checked: boolean,
  ) => {
    handleSettingsChange({
      ...settings,
      overrideBarColor: checked,
    });
  };
  const handleBarColorChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    handleSettingsChange({
      ...settings,
      barColor: event.target.value,
    });
  };
  const handlePreviousValueChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    handleSettingsChange({
      ...settings,
      showPrevious: event.target.checked,
    });
  };
  return (
    <DataWidgetSettingsContainer
      settings={settings}
      exculdedSettings={{
        aggBy: true,
        samplePeriod: true,
        globalSamplePeriod: true,
        timeRange: true,
      }}
      hideSettings={{
        aggBy: true,
        realtime: true,
        overrideGlobalSettings: true,
        globalSamplePeriod: true,
      }}
      setSettings={handleSettingsChange}
      dataTabChildren={
        <>
          <SingleMeasureSelect
            id={'image-stats-widget'}
            settings={settings}
            setSettings={handleSettingsChange as setSingleMeasureWidgetSettings}
          />
        </>
      }
      feelTabChidren={
        <>
          <Box mb={2}>
            <FormGroup>
              <FormControlLabel
                control={
                  <Checkbox
                    checked={settings.showPrevious}
                    onChange={handlePreviousValueChange}
                    name="showPrevious"
                  />
                }
                label="Show Previous Value"
              />
            </FormGroup>
          </Box>
          <Box>
            <FormGroup>
              <FormControlLabel
                control={
                  <Checkbox
                    checked={settings.overrideBarColor}
                    onChange={handleOverriderBarColor}
                    name="overrideBarColor"
                  />
                }
                label="Override Bar Color"
              />
            </FormGroup>
            {settings.overrideBarColor ? (
              <Box mb={2}>
                <FormControl fullWidth>
                  <FormLabel>Bar Color</FormLabel>
                  <Input
                    type="color"
                    fullWidth
                    name={'barColor'}
                    value={settings.barColor}
                    onChange={handleBarColorChange}
                  />
                </FormControl>
              </Box>
            ) : null}
          </Box>
        </>
      }
    />
  );
};

export default KPIBarChartSettings;
