import * as yup from 'yup';

export const unitsofGropsUnitvalidationSchema = yup.object().shape({
  measurementId: yup
    .string()
    // .oneOf(validMeasurementIds, 'Invalid measurement ID')
    .required('Measurement is required'),
  unitsGroup: yup
    .string()
    // .oneOf(validUnitsGroups, 'Invalid units group')
    .required('Units group is required'),
  unitOfMeasure: yup.string().required('Unit of measure is required'),
  is_default: yup.boolean().notRequired(),
});

export type UnitsOfGroupUnitFormValues = yup.InferType<typeof unitsofGropsUnitvalidationSchema>;
