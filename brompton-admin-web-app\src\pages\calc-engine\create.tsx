import { Box } from '@mui/material';
import CalcEngineBuilder from '~/components/CalcEngine/CalcEngineBuilder';
import HomeButton from '~/components/common/Home/HomeButton';
import PageName from '~/components/common/PageName/PageName';

const CreateExpression: React.FC = () => {
  return (
    <Box p={3}>
      <Box p={2} pb={0} pt={0} sx={{ display: 'flex' }}>
        <Box
          sx={{
            flexGrow: 1,
            display: 'flex',
            alignItems: 'center',
          }}
        >
          <PageName name="Expression Template" />
        </Box>
        <HomeButton />
      </Box>
      <CalcEngineBuilder />
    </Box>
  );
};
export default CreateExpression;
