import React from 'react';

import { ComponentStory, ComponentMeta } from '@storybook/react';

import AssetsTree from './AssetsTree';
import { Tree } from './tree';
import {
  ASSET_DEFAULT_ICON,
  COMPANY_DEFAULT_ICON,
  MEASUREMENT_DEFAULT_ICON,
  METRIC_DEFAULT_ICON,
} from './testing/mocks';

export default {
  title: 'AssetsTree',
  component: AssetsTree,
  argTypes: { onNodeToggle: { action: 'onNodeToggle' } },
} as ComponentMeta<typeof AssetsTree>;

const Template: ComponentStory<typeof AssetsTree> = (args) => (
  <AssetsTree {...args} />
);

export const RootNodeTree = Template.bind({});

RootNodeTree.args = {
  tree: new Tree('ID0', 'Stark Industries', 'company'),
  nodeTypeConfigs: {
    customer: {
      icon: COMPANY_DEFAULT_ICON,
      expandable: true,
      selectable: false,
      contextMenuActions: [],
    },
    asset: {
      icon: ASSET_DEFAULT_ICON,
      expandable: true,
      selectable: true,
      contextMenuActions: [],
    },
    measurement: {
      icon: MEASUREMENT_DEFAULT_ICON,
      expandable: true,
      selectable: true,
      contextMenuActions: [],
    },
    metric: {
      icon: METRIC_DEFAULT_ICON,
      expandable: false,
      selectable: true,
      contextMenuActions: [],
    },
  },
  checkboxSelection: true,
};

export const SingleChildTree = Template.bind({});

SingleChildTree.args = {
  ...RootNodeTree.args,
  tree: new Tree('ID0', 'Stark Industries', 'company', [
    new Tree('ID', 'child', 'activo'),
  ]),
  expandedNodeIds: ['ID0'],
};

export const FirstLevelTree = Template.bind({});

FirstLevelTree.args = {
  ...RootNodeTree.args,
  tree: new Tree('ID0', 'Stark Industries', 'company', [
    new Tree('ID1', 'child 1', 'activo'),
    new Tree('ID2', 'child 2', 'activo'),
  ]),
  expandedNodeIds: ['ID0'],
};

export const SecondLevelTree = Template.bind({});

SecondLevelTree.args = {
  ...RootNodeTree.args,
  tree: new Tree('ID0', 'Stark Industries', 'company', [
    new Tree('ID1', 'child', 'activo', [
      new Tree('ID2', 'grandchild', 'activo', []),
    ]),
  ]),
  expandedNodeIds: ['ID0', 'ID1'],
};

export const FirstLevelTreeWithUncheckedMetric = Template.bind({});

FirstLevelTreeWithUncheckedMetric.args = {
  ...RootNodeTree.args,
  tree: new Tree('ID0', 'Stark Industries', 'company', [
    new Tree('ID1', 'child 1', 'activo'),
    new Tree('ID2', 'child 2', 'metric'),
  ]),
  expandedNodeIds: ['ID0'],
};

export const FirstLevelTreeWithCheckedMetric = Template.bind({});

FirstLevelTreeWithCheckedMetric.args = {
  ...RootNodeTree.args,
  tree: new Tree('ID0', 'Stark Industries', 'company', [
    new Tree('ID1', 'child 1', 'activo'),
    new Tree('ID2', 'child 2', 'metric'),
  ]),
  expandedNodeIds: ['ID0'],
  selectedNodeIds: ['ID2'],
};

export const ThreeLevelTree = Template.bind({});

ThreeLevelTree.args = {
  ...RootNodeTree.args,
  tree: new Tree('ID0', 'Stark Industries', 'company', [
    new Tree('ID1', 'site 1', 'activo', [
      new Tree('ID3', 'Power', 'medicion', [
        new Tree('ID4', 'engine 1', 'metric'),
      ]),
      new Tree('ID5', 'fan', 'metric'),
    ]),
    new Tree('ID2', 'site 2', 'activo'),
  ]),
  selectedNodeIds: ['ID2', 'ID4'],
  expandedNodeIds: ['ID0', 'ID1', 'ID3'],
};

export const NoCheckboxThreeLevelTree = Template.bind({});

NoCheckboxThreeLevelTree.args = {
  ...ThreeLevelTree.args,
  selectedNodeIds: ['ID4'],
  checkboxSelection: false,
};
