import AddIcon from '@mui/icons-material/Add';
import { Box, Button } from '@mui/material';
import { useRouter } from 'next/router';
import { useDispatch, useSelector } from 'react-redux';
import DashboardsTable from '~/components/DashboardList/DashboardsTable';
import PageName from '~/components/common/PageName/PageName';
import { useHasAdminAccess } from '~/hooks/useHasAdminAccess';
import { useHasPowerUserAccess } from '~/hooks/useHasPowerUserAccess';
import { getActiveCustomer } from '~/redux/selectors/customerSelectors';
import { dashboardListSlice } from '~/redux/slices/dashboardListSlice';
import { dashboardSlice } from '~/redux/slices/dashboardSlice';

const DashboardList: React.FC = () => {
  const hasPowerUserAccess = useHasPowerUserAccess();
  const { globalAdmin, admin } = useHasAdminAccess();
  const router = useRouter();
  const dispatch = useDispatch();
  const activeCustomer = useSelector(getActiveCustomer);

  const handleAddNewDashboard = () => {
    dispatch(dashboardListSlice.actions.setCurrentDashboardId(-1));
    dispatch(dashboardSlice.actions.setNewDashboard());
    router.push(`/customer/${activeCustomer?.id}/dashboard/0`);
  };

  return (
    <Box sx={{ px: 3 }}>
      <Box py={2}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <PageName name="Dashboards" />

          {(globalAdmin || admin || hasPowerUserAccess) && (
            <Button
              variant="contained"
              onClick={handleAddNewDashboard}
              color="primary"
              startIcon={<AddIcon />}
            >
              New Dashboard
            </Button>
          )}
        </Box>
      </Box>

      <DashboardsTable />
    </Box>
  );
};

export default DashboardList;
