import * as React from 'react';
import Box from '@mui/material/Box';
import Drawer from '@mui/material/Drawer';
import NewReleasesIcon from '@mui/icons-material/NewReleases';
import List from '@mui/material/List';
import Typography from '@mui/material/Typography';
import Divider from '@mui/material/Divider';
import ListItem from '@mui/material/ListItem';
import ListItemButton from '@mui/material/ListItemButton';
import ListItemIcon from '@mui/material/ListItemIcon';
import { useDispatch, useSelector } from 'react-redux';
import { dashboardSlice } from '~/redux/slices/dashboardSlice';
import { getRightSideBarActiveTab } from '~/redux/selectors/dashboardSelectors';
import ReleasesPage from '~/pages/release';
import { Tooltip } from '@mui/material';
import AssignmentIcon from '@mui/icons-material/Assignment';
import Co2Icon from '@mui/icons-material/Co2';
const drawerWidth = 530;
type RightSideDrawerProps = {
  open: boolean;
};
export default function RightSideDrawer({ open }: RightSideDrawerProps) {
  const dispatch = useDispatch();
  const activeTab = useSelector(getRightSideBarActiveTab);
  const setActive = (
    text:
      | '/icons/alerts.svg'
      | '/icons/llm.svg'
      | '/icons/system-status.svg'
      | 'release-notes'
      | 'CO2e'
      | 'Reports',
  ) => {
    dispatch(dashboardSlice.actions.setRightSideBarActiveTab(text));
  };
  return (
    <>
      {!open ? (
        <></>
      ) : (
        <Drawer
          open={open}
          sx={{
            width: drawerWidth,
            transition: 'all .2s',
            visibility: open ? 'visible' : 'hidden',
            zIndex: 10,
            flexShrink: 0,
            '& .MuiDrawer-paper': {
              width: drawerWidth,
              boxSizing: 'border-box',
              position: 'relative',
            },
          }}
          variant="persistent"
          anchor="right"
        >
          <List sx={{ display: 'flex' }}>
            {[
              { tip: 'Alerts', img: '/icons/alerts.svg' },
              { tip: 'Smart Chat', img: '/icons/llm.svg' },
              { tip: 'System status', img: '/icons/system-status.svg' },
              { tip: 'CO2e', img: 'CO2e', icon: <Co2Icon /> },
              { tip: 'Reports', img: 'Reports', icon: <AssignmentIcon /> },
              { tip: 'Release notes', img: 'release-notes', icon: <NewReleasesIcon /> },
            ].map((text, index) => (
              <ListItem key={index} disablePadding>
                <ListItemButton
                  disableRipple
                  sx={{
                    justifyContent: 'center',
                    backgroundColor: activeTab === text.img ? 'primary.main' : undefined,
                  }}
                  onClick={(e) =>
                    setActive(
                      text.img as
                        | '/icons/alerts.svg'
                        | '/icons/llm.svg'
                        | '/icons/system-status.svg'
                        | 'release-notes'
                        | 'CO2e'
                        | 'Reports',
                    )
                  }
                >
                  <Tooltip title={text.tip} placement="top">
                    <ListItemIcon sx={{ justifyContent: 'center' }}>
                      {text.icon ? (
                        <>{text.icon}</>
                      ) : (
                        <img src={text.img as string} alt="icon" height={30} width={30} />
                      )}
                    </ListItemIcon>
                  </Tooltip>
                </ListItemButton>
              </ListItem>
            ))}
          </List>
          <Divider />

          <Box
            sx={{
              flexGrow: 1,
              bgcolor: 'background.default',
              p: 2,
              width: drawerWidth - 20,
              display: 'flex',
              alignContent: 'center',
              justifyContent: 'center',
            }}
          >
            {activeTab === '/icons/alerts.svg' && (
              <Typography variant="h6">System Alerts will be available soon. </Typography>
            )}
            {activeTab === '/icons/llm.svg' && (
              <Typography variant="h6">Smart Chat Feature will be available soon.</Typography>
            )}
            {activeTab === '/icons/system-status.svg' && (
              <Typography variant="h6">
                This will display system status of cloud services.
              </Typography>
            )}
            {activeTab === 'release-notes' && (
              <>
                <ReleasesPage />
              </>
            )}
            {activeTab === 'CO2e' && <Typography variant="h6">Carbon Accountancy</Typography>}
            {activeTab === 'Reports' && <Typography variant="h6">Reports</Typography>}
          </Box>
        </Drawer>
      )}
    </>
  );
}
