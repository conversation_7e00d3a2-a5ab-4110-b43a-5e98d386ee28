import LaunchIcon from '@mui/icons-material/Launch';
import {
  Alert,
  Autocomplete,
  Box,
  Checkbox,
  CircularProgress,
  FormControl,
  FormControlLabel,
  IconButton,
  Radio,
  RadioGroup,
  Snackbar,
  TextField,
  Tooltip,
  Typography,
} from '@mui/material';
import { ThunkDispatch } from '@reduxjs/toolkit';
import { useRouter } from 'next/router';
import { useEffect, useMemo, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { CustomError } from '~/errors/CustomerErrorResponse';
import { useGetAllAssetQuery, useGetAllBackOfficeAssetTypesQuery } from '~/redux/api/assetsApi';
import { useGetDashboardByCustomerIdQuery } from '~/redux/api/dashboardApi';
import { dashboardTemplateApi, useGetDashboardTemplatesQuery } from '~/redux/api/dashboardTemplate';
import { useGetAllMeasurementsQuery } from '~/redux/api/measuresApi';
import { getActiveCustomer } from '~/redux/selectors/customerSelectors';
import { getCurrentAssetType } from '~/redux/selectors/dashboardSelectors';
import { dashboardSlice } from '~/redux/slices/dashboardSlice';
import { RootState } from '~/redux/store';
import { AssetTypeOption } from '~/types/asset';
import { DashboardState, TimeRangeOptions } from '~/types/dashboard';
import { ImageTextDetails, ImageWidgetSettings } from '~/types/widgets';
import { assetTypePathMapperFilterTemplates } from '~/utils/mappers/asset-type-mapper';
import { assetsPathMapper, getPreviousDate } from '~/utils/utils';
type labesAndUnits = {
  label: string;
  unit: string;
  dashboard: {
    id: number;
    title: string;
  } | null;
  dashboardOrTemplate: 'dashboard' | 'template';
  assetOrAssetType: number | null;
  openDashboardInNewTab: boolean;
};
type MeasurementDashboard<T extends ImageWidgetSettings> = {
  labelAndUnits: labesAndUnits;
  measureId: string;
  setSettings: (value: (prevState: T) => T) => void;
  settings: T;
};
const MeasurementDashboard = ({
  labelAndUnits,
  measureId,
  setSettings,
  settings,
}: MeasurementDashboard<ImageWidgetSettings>) => {
  const dispatch = useDispatch<ThunkDispatch<RootState, any, any>>();
  const router = useRouter();
  const activeCustomer = useSelector(getActiveCustomer);
  const assetTypeTemplate = useSelector(getCurrentAssetType);
  const [openSnackBar, setOpenSnackBar] = useState<{
    open: boolean;
    message: string;
    severity: 'success' | 'error' | 'info';
  }>({
    open: false,
    message: '',
    severity: 'info',
  });
  const [assetTypesWithPath, setAssetTypesWithPath] = useState<AssetTypeOption[]>([]);
  const [assetToAssetType, seAssetToAssetType] = useState<number | null>(null);

  const {
    data: assetTypeListData,
    isLoading: isAssetTypeLoading,
    isSuccess: isSuccessfullBackOffieAssetTypes,
  } = useGetAllBackOfficeAssetTypesQuery(undefined, {
    skip: settings.mode === 'dashboard' || !assetTypeTemplate || assetTypeTemplate <= 0,
  });
  useEffect(() => {
    if (isSuccessfullBackOffieAssetTypes && assetTypeListData) {
      setAssetTypesWithPath(
        assetTypePathMapperFilterTemplates(
          assetTypeListData.map((item) => ({
            ...item,
            name: item.name,
            id: item.id,
          })),
        ),
      );
    }
  }, [assetTypeListData, isSuccessfullBackOffieAssetTypes]);
  const { data: assetMeasurements } = useGetAllMeasurementsQuery(
    {
      assetId: labelAndUnits.assetOrAssetType ?? 0,
      customerId: activeCustomer?.id ?? 0,
    },
    {
      skip:
        settings.mode === 'template' ||
        labelAndUnits.assetOrAssetType === null ||
        labelAndUnits.assetOrAssetType <= 0 ||
        router.pathname === '/measurement-browser',
    },
  );
  const { data: assetData, isFetching: isAssetReloading } = useGetAllAssetQuery(
    { customerId: activeCustomer?.id ?? 0, parentIds: [] },
    {
      skip: !activeCustomer || settings.mode === 'template',
      refetchOnMountOrArgChange: true,
    },
  );
  const assetsWithPath = useMemo(() => assetsPathMapper(assetData ?? []), [assetData]);
  useEffect(() => {
    if (
      assetData &&
      settings.mode === 'dashboard' &&
      labelAndUnits.dashboardOrTemplate === 'template' &&
      labelAndUnits.assetOrAssetType !== null
    ) {
      const assetToType = assetData?.find(
        (asset) => asset.id === labelAndUnits.assetOrAssetType,
      )?.assetTypeId;
      seAssetToAssetType(assetToType ?? null);
    }
  }, [labelAndUnits.assetOrAssetType, settings.mode, assetData, labelAndUnits.dashboardOrTemplate]);
  const { data: dashboardTemplates, isLoading: isLoadingDashboardTemplates } =
    useGetDashboardTemplatesQuery(
      {
        assetTypeId:
          settings.mode === 'dashboard'
            ? assetToAssetType ?? 0
            : labelAndUnits.assetOrAssetType ?? 0,
      },
      {
        skip:
          settings.mode === 'dashboard'
            ? assetToAssetType === null || assetToAssetType === 0
            : !assetTypeTemplate ||
              assetTypeTemplate <= 0 ||
              labelAndUnits.assetOrAssetType === null,
      },
    );
  useEffect(() => {
    if (settings.measureIdToImageTextDetails[measureId]?.dashboardOrTemplate === undefined) {
      setSettings((prevState) => ({
        ...prevState,
        measureIdToImageTextDetails: {
          ...prevState.measureIdToImageTextDetails,
          [measureId]: {
            ...prevState.measureIdToImageTextDetails[measureId],
            dashboardOrTemplate: 'template',
          },
        },
      }));
    }
  }, [settings.measureIdToImageTextDetails[measureId]?.dashboardOrTemplate]);

  const { data: dashboardList, isLoading: isLoadingDashboards } = useGetDashboardByCustomerIdQuery(
    {
      customerId: activeCustomer?.id ?? 0,
      search: null,
    },
    {
      skip: !activeCustomer?.id,
    },
  );

  const openDashboard = async () => {
    const dashboard = settings.measureIdToImageTextDetails[measureId].dashboard;
    const mode = settings.mode;
    const dashboardOrTemplate = settings.measureIdToImageTextDetails[measureId].dashboardOrTemplate;
    if (mode === 'dashboard' && dashboard !== null) {
      if (dashboardOrTemplate === 'dashboard') {
        dispatch(
          dashboardSlice.actions.setDashboardCrumb({
            dashboardId: dashboard.id,
            title: dashboard.title,
          }),
        );
        dispatch(dashboardSlice.actions.setCurrentDashboardId(dashboard.id));
        dispatch(dashboardSlice.actions.setCurrentDashboardTitle(dashboard.title));
        router.push(`/customer/${activeCustomer?.id}/dashboard/${dashboard.id}`);
      }
      if (dashboardOrTemplate === 'template') {
        setOpenSnackBar({
          open: true,
          message: 'Fetching dashboard template data...',
          severity: 'info',
        });
        const {
          isError,
          error,
          data: templateData,
        } = await dispatch(
          dashboardTemplateApi.endpoints.getDashboardTemplateDetails.initiate(dashboard.id),
        );
        setOpenSnackBar({
          open: false,
          message: 'Dashboard template data fetched successfully!',
          severity: 'success',
        });
        if (isError && error) {
          const err = error as CustomError;
          setOpenSnackBar({
            open: true,
            message: err.data.exception ?? 'Error fetching dashboard template. Please try again.',
            severity: 'error',
          });
        }
        if (templateData && templateData?.data && assetMeasurements) {
          const templateDetailsData = JSON.parse(templateData.data) as {
            widget: DashboardState['widget'];
            topPanel: DashboardState['template']['topPanel'];
            chart: DashboardState['template']['chart'];
          };
          const metricToMeasurementMap: Record<string, string[]> = {};
          assetMeasurements?.forEach((measurement) => {
            if (measurement.metric_id !== null) {
              const metricIdStr = measurement.metric_id.toString();
              const measurementIdStr = measurement.id.toString();

              if (!metricToMeasurementMap[metricIdStr]) {
                metricToMeasurementMap[metricIdStr] = [];
              }

              metricToMeasurementMap[metricIdStr].push(measurementIdStr);
            }
          });

          templateDetailsData.widget.widgets.map((widget) => {
            if (widget.type === 'chart') {
              widget.settings.settings.dashboardOrTemplate =
                widget.settings.settings.dashboardOrTemplate ?? 'template';
              widget.settings.settings.assetOrAssetType =
                settings.measureIdToImageTextDetails[measureId].assetOrAssetType ?? null;
              widget.settings.settings.mode = 'dashboard';
              if (
                'assetMeasure' in widget.settings.settings &&
                widget.settings.settings.assetMeasure
              ) {
                if (Array.isArray(widget.settings.settings.assetMeasure)) {
                  const measureIds: string[] = [];
                  if ('selectedTitles' in widget.settings.settings) {
                    widget.settings.settings.selectedTitles.forEach((title) => {
                      if (metricToMeasurementMap[title]) {
                        measureIds.push(...metricToMeasurementMap[title]); // <-- Fix here
                      }
                    });
                    widget.settings.settings.selectedTitles = [];
                  }
                  widget.settings.settings.assetMeasure.push({
                    assetId:
                      settings.measureIdToImageTextDetails[
                        measureId
                      ].assetOrAssetType?.toString() ?? '',
                    measureId: measureIds,
                  });
                } else {
                  const measureIds: string[] = [];
                  if (
                    'selectedDbMeasureId' in widget.settings.settings &&
                    metricToMeasurementMap[widget.settings.settings.selectedDbMeasureId]
                  ) {
                    measureIds.push(
                      ...metricToMeasurementMap[widget.settings.settings.selectedDbMeasureId],
                    );
                    widget.settings.settings.selectedDbMeasureId = '';
                  }
                  widget.settings.settings.assetMeasure.assetId =
                    settings.measureIdToImageTextDetails[measureId].assetOrAssetType?.toString() ??
                    '';
                  widget.settings.settings.assetMeasure.measureId = measureIds;
                }
              }
              return widget;
            }

            widget.settings.dashboardOrTemplate = widget.settings.dashboardOrTemplate ?? 'template';
            widget.settings.assetOrAssetType =
              settings.measureIdToImageTextDetails[measureId].assetOrAssetType ?? null;
            widget.settings.mode = 'dashboard';
            if (widget.type === 'map') {
              widget.settings.markers = widget.settings.markers.map((marker) => {
                if (marker.selectedTitles.length > 0) {
                  const measureIds: string[] = [];
                  marker.selectedTitles.forEach((title) => {
                    if (metricToMeasurementMap[title]) {
                      measureIds.push(...metricToMeasurementMap[title]); // <-- Fix here
                    }
                  });
                  measureIds.forEach((measureId) => {
                    marker.assetMeasures.push({
                      assetId:
                        settings.measureIdToImageTextDetails[
                          measureId
                        ].assetOrAssetType?.toString() ?? '',
                      measureId: measureId,
                    });
                  });
                  let labelUnits = {};
                  measureIds.forEach((measureId) => {
                    const measure = assetMeasurements?.find(
                      (measure) => measure.id === Number(measureId),
                    );

                    labelUnits = {
                      ...labelUnits,
                      [measureId]: {
                        label: measure?.tag ?? '',
                        unit: '',
                        value: '',
                      },
                    };
                  });
                  marker.labelAndUnits = labelUnits;
                }
                marker.selectedTitles = [];
                return marker;
              });
            } else {
              if ('assetMeasure' in widget.settings && widget.settings.assetMeasure) {
                if (Array.isArray(widget.settings.assetMeasure)) {
                  const measureIds: string[] = [];
                  if ('selectedTitles' in widget.settings) {
                    widget.settings.selectedTitles.forEach((title) => {
                      if (metricToMeasurementMap[title]) {
                        measureIds.push(...metricToMeasurementMap[title]); // <-- Fix here
                      }
                    });
                    widget.settings.selectedTitles = [];
                  }
                  if (widget.type === 'image') {
                    const selectedDbMeasureIdToName: {
                      [key: string]: string;
                    } = {};
                    measureIds.forEach((measureId) => {
                      const measureName = assetMeasurements?.find(
                        (measure) => measure.id === Number(measureId),
                      )?.tag;
                      selectedDbMeasureIdToName[measureId] = measureName ?? '';
                    });
                    widget.settings.dbMeasureIdToName = selectedDbMeasureIdToName;
                    const measureIdToImageTextDetails: Record<string, ImageTextDetails> = {};
                    measureIds.forEach((measureId) => {
                      const measureName = assetMeasurements?.find(
                        (measure) => measure.id === Number(measureId),
                      );
                      const existingTextDetails =
                        widget.settings.measureIdToImageTextDetails[measureName?.metric_id ?? ''];
                      measureIdToImageTextDetails[measureId] = {
                        label: measureName?.tag ?? '',
                        unit: existingTextDetails?.unit ?? '',
                        id: measureId,
                        positionX: existingTextDetails?.positionX ?? 100,
                        positionY: existingTextDetails?.positionY ?? 100,
                        value: existingTextDetails?.value ?? '',
                        dashboard: existingTextDetails?.dashboard ?? null,
                        openDashboardInNewTab: existingTextDetails?.openDashboardInNewTab ?? false,
                        dashboardOrTemplate:
                          existingTextDetails?.dashboardOrTemplate ?? 'dashboard',
                        assetOrAssetType: existingTextDetails?.assetOrAssetType ?? null,
                      };
                    });
                    widget.settings.measureIdToImageTextDetails = measureIdToImageTextDetails;
                  }
                  widget.settings.assetMeasure.push({
                    assetId:
                      settings.measureIdToImageTextDetails[
                        measureId
                      ].assetOrAssetType?.toString() ?? '',
                    measureId: measureIds,
                  });
                } else {
                  const measureIds: string[] = [];
                  if (
                    'selectedDbMeasureId' in widget.settings &&
                    metricToMeasurementMap[widget.settings.selectedDbMeasureId]
                  ) {
                    measureIds.push(...metricToMeasurementMap[widget.settings.selectedDbMeasureId]);
                    widget.settings.selectedDbMeasureId = '';
                  }
                  widget.settings.assetMeasure.assetId =
                    settings.measureIdToImageTextDetails[measureId].assetOrAssetType?.toString() ??
                    '';
                  widget.settings.assetMeasure.measureId = measureIds;
                }
              }
            }
            return widget;
          });
          dispatch(
            dashboardSlice.actions.setDashboardCrumb({
              dashboardId: -2,
              title: dashboard?.title + '-' + templateData?.asset_template?.assetType?.name,
              templateId: templateData?.id ?? undefined,
              assetId:
                settings.measureIdToImageTextDetails[measureId].assetOrAssetType ?? undefined,
              assetType: templateData?.asset_template?.assetType?.id ?? undefined,
            }),
          );
          router.push(`/customer/${activeCustomer?.id}/dashboard/-2`);
          dispatch(dashboardSlice.actions.setCurrentDashboardId(-2));
          dispatch(
            dashboardSlice.actions.setCurrentDashboardTitle(
              dashboard?.title + '-' + templateData?.asset_template?.assetType?.name,
            ),
          );
          dispatch(
            dashboardSlice.actions.setWidget({
              widgets: templateDetailsData.widget.widgets,
              deleteWidgets: [],
              widgetLayout: templateDetailsData.widget.widgetLayout,
              lastWidgetId: templateDetailsData.widget.lastWidgetId,
            }),
          );
          dispatch(
            dashboardSlice.actions.setWidgetsLayout(templateDetailsData.widget.widgetLayout),
          );
          dispatch(
            dashboardSlice.actions.setSamplePeriod(templateDetailsData.topPanel.samplePeriod),
          );
          dispatch(
            dashboardSlice.actions.setRefreshTimeInterval(
              templateDetailsData.topPanel.refreshInterval,
            ),
          );
          dispatch(
            dashboardSlice.actions.setTimeRangeType(templateDetailsData.topPanel.timeRangeType),
          );
          const minutes: number =
            TimeRangeOptions[templateDetailsData.topPanel.timeRangeType ?? 6].serverValue;
          const start = getPreviousDate(minutes);
          if (templateDetailsData.topPanel.timeRangeType !== 0) {
            dispatch(dashboardSlice.actions.setChartStartDate(new Date(start)));
            dispatch(dashboardSlice.actions.setChartEndDate(new Date()));
          } else {
            dispatch(
              dashboardSlice.actions.setChartStartDate(
                new Date(templateDetailsData.chart.startDate),
              ),
            );
            dispatch(
              dashboardSlice.actions.setChartEndDate(new Date(templateDetailsData.chart.endDate)),
            );
          }
        }
      }
    }
    if (mode === 'template' && dashboard !== null) {
      if (dashboardOrTemplate === 'dashboard') {
        dispatch(
          dashboardSlice.actions.setDashboardCrumb({
            dashboardId: dashboard.id,
            title: dashboard.title,
          }),
        );
        dispatch(dashboardSlice.actions.setCurrentDashboardId(dashboard.id));
        dispatch(dashboardSlice.actions.setCurrentDashboardTitle(dashboard.title));
        router.push(`/customer/${activeCustomer?.id}/dashboard/${dashboard.id}`);
      }
      if (dashboardOrTemplate === 'template') {
        dispatch(dashboardSlice.actions.setTemplateId(dashboard.id));
        dispatch(dashboardSlice.actions.setTemplateName(dashboard.title));
        dispatch(
          dashboardSlice.actions.setWidget({
            widgets: [],
            deleteWidgets: [],
            widgetLayout: [],
            lastWidgetId: 0,
          }),
        );
      }
    }
  };
  return (
    <>
      <Snackbar
        open={openSnackBar.open}
        onClose={() => {
          setOpenSnackBar({ open: false, message: '', severity: 'info' });
        }}
        autoHideDuration={3000}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert severity={openSnackBar.severity}>{openSnackBar.message}</Alert>
      </Snackbar>
      <FormControl component="fieldset">
        <RadioGroup
          row
          value={settings.measureIdToImageTextDetails[measureId]?.dashboardOrTemplate ?? 'template'}
          onChange={(event: React.ChangeEvent<HTMLInputElement>, value: string) => {
            setSettings((prevState) => ({
              ...prevState,
              measureIdToImageTextDetails: {
                ...prevState.measureIdToImageTextDetails,
                [measureId]: {
                  ...prevState.measureIdToImageTextDetails[measureId],
                  dashboardOrTemplate: value as 'dashboard' | 'template',
                  assetOrAssetType: null,
                  dashboard: null,
                },
              },
            }));
          }}
          name="dashboardOrTemplate"
        >
          <FormControlLabel value="template" control={<Radio />} label="Template" />
          <FormControlLabel value="dashboard" control={<Radio />} label="Dashboard" />
        </RadioGroup>
      </FormControl>
      {settings.mode === 'template' && labelAndUnits.dashboardOrTemplate === 'template' ? (
        <Box sx={{ display: 'flex' }}>
          <Autocomplete
            fullWidth
            disablePortal
            loading={isAssetReloading}
            id="combo-box-demo"
            options={assetTypesWithPath.map((item) => {
              return {
                id: item.value.toString(),
                label: item.label,
              };
            })}
            value={
              assetTypesWithPath
                .map((item) => {
                  return {
                    id: item.value.toString(),
                    label: item.label,
                  };
                })
                .find((item) => item.id === labelAndUnits.assetOrAssetType?.toString()) ?? null
            }
            onChange={(event, value) => {
              setSettings((prevState) => ({
                ...prevState,
                measureIdToImageTextDetails: {
                  ...prevState.measureIdToImageTextDetails,
                  [measureId]: {
                    ...prevState.measureIdToImageTextDetails[measureId],
                    assetOrAssetType: Number(value?.id) ?? null,
                    dashboard: null,
                  },
                },
              }));
            }}
            renderInput={(params) => <TextField {...params} label="Asset Type" />}
          />
        </Box>
      ) : null}
      {settings.mode === 'dashboard' && labelAndUnits.dashboardOrTemplate === 'template' ? (
        <Box sx={{ display: 'flex', mb: 2 }}>
          <Autocomplete
            fullWidth
            id={`asset-autocomplete`}
            loading={isAssetReloading}
            options={
              !isAssetReloading
                ? assetsWithPath.map((asset) => ({
                    label: asset.label,
                    id: asset.id,
                  }))
                : []
            }
            getOptionLabel={(option) => option?.label ?? ''}
            onChange={(event, value) => {
              setSettings((prevState) => ({
                ...prevState,
                measureIdToImageTextDetails: {
                  ...prevState.measureIdToImageTextDetails,
                  [measureId]: {
                    ...prevState.measureIdToImageTextDetails[measureId],
                    assetOrAssetType: value?.id ?? null,
                    dashboard: null,
                  },
                },
              }));
            }}
            value={assetsWithPath?.find((asset) => asset.id === labelAndUnits.assetOrAssetType)}
            renderInput={(params) => (
              <TextField {...params} label="Select Asset" variant="outlined" />
            )}
          />
        </Box>
      ) : null}
      <Box sx={{ display: 'flex', gap: 1 }}>
        <Autocomplete
          loading={isLoadingDashboards || isLoadingDashboardTemplates}
          id="dashboards-combo-box"
          options={
            labelAndUnits.dashboardOrTemplate === 'dashboard'
              ? dashboardList?.items?.map((dashboard) => ({
                  id: dashboard.id,
                  title: dashboard.title,
                })) ?? []
              : dashboardTemplates?.items?.map((dashboardTemp) => ({
                  id: dashboardTemp.id,
                  title: dashboardTemp.title,
                })) ?? []
          }
          getOptionLabel={(option) => option.title}
          onChange={(e: React.SyntheticEvent, value: { id: number; title: string } | null) => {
            setSettings((prevState) => ({
              ...prevState,
              measureIdToImageTextDetails: {
                ...prevState.measureIdToImageTextDetails,
                [measureId]: {
                  ...prevState.measureIdToImageTextDetails[measureId],
                  dashboard: value,
                },
              },
            }));
          }}
          sx={{ width: '100%' }}
          isOptionEqualToValue={(option, value) => option.id === value.id}
          value={labelAndUnits.dashboard ?? null}
          loadingText={
            labelAndUnits.dashboardOrTemplate === 'dashboard'
              ? 'Loading Dashboards...'
              : 'Loading Dashboard Templates...'
          }
          renderInput={(params) => (
            <TextField
              {...params}
              label={
                labelAndUnits.dashboardOrTemplate === 'template'
                  ? 'Link Dashboard Template'
                  : 'Link Dashboard'
              }
              InputProps={{
                ...params.InputProps,
                endAdornment: (
                  <>
                    {isLoadingDashboards || isLoadingDashboardTemplates ? (
                      <CircularProgress color="inherit" size={20} />
                    ) : null}
                    {params.InputProps.endAdornment}
                  </>
                ),
              }}
            />
          )}
        />
        {labelAndUnits.dashboard !== null && labelAndUnits.dashboard?.id && (
          <IconButton
            disableRipple
            disableTouchRipple
            id={'title-widget-link-icon'}
            edge="start"
            color="inherit"
            sx={{
              zIndex: 10,
              mr: 0.5,
            }}
            onClick={openDashboard}
          >
            <Tooltip
              title={
                <Typography variant="body2" color="inherit" fontSize={'0.7rem'}>
                  {labelAndUnits.dashboardOrTemplate === 'dashboard' ? (
                    <>Open Dashboard - {labelAndUnits?.dashboard.title}</>
                  ) : (
                    <>Open Dashboard Template- {labelAndUnits?.dashboard.title}</>
                  )}

                  {settings.mode}
                </Typography>
              }
            >
              <LaunchIcon />
            </Tooltip>
          </IconButton>
        )}
      </Box>
      {labelAndUnits.dashboard !== null && labelAndUnits.dashboard?.id && (
        <FormControl fullWidth>
          <FormControlLabel
            control={
              <Checkbox
                sx={{ mt: 1, ml: 2, mb: 1 }}
                color="primary"
                checked={labelAndUnits.openDashboardInNewTab}
                onChange={(event: React.ChangeEvent<HTMLInputElement>, checked: boolean) => {
                  if (checked) {
                    setSettings((prevState) => ({
                      ...prevState,
                      measureIdToImageTextDetails: {
                        ...prevState.measureIdToImageTextDetails,
                        [measureId]: {
                          ...prevState.measureIdToImageTextDetails[measureId],
                          openDashboardInNewTab: true,
                        },
                      },
                    }));
                  } else {
                    setSettings((prevState) => ({
                      ...prevState,
                      measureIdToImageTextDetails: {
                        ...prevState.measureIdToImageTextDetails,
                        [measureId]: {
                          ...prevState.measureIdToImageTextDetails[measureId],
                          openDashboardInNewTab: false,
                        },
                      },
                    }));
                  }
                }}
              />
            }
            label="Open in New Tab"
          />
        </FormControl>
      )}
    </>
  );
};

export default MeasurementDashboard;
