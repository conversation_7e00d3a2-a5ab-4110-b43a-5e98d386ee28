// npm test -- --testPathPattern="ResponsiveLayoutToggle.test.tsx" --verbose --watchAll=false
import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { Provider } from 'react-redux';
import { configureStore } from '@reduxjs/toolkit';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import '@testing-library/jest-dom';
import { useRouter } from 'next/router';

import ResponsiveLayoutToggle from '../ResponsiveLayoutToggle';
import { dashboardSlice } from '~/redux/slices/dashboardSlice';
import { RootState } from '~/redux/store';

// Mock Next.js router
jest.mock('next/router', () => ({
  useRouter: jest.fn(),
}));

// Create a mock theme for Material-UI components
const mockTheme = createTheme();

// Mock router object
const mockRouter = {
  pathname: '/customer/1/dashboard/1',
  push: jest.fn(),
  query: {},
  asPath: '',
  route: '',
  back: jest.fn(),
  beforePopState: jest.fn(),
  prefetch: jest.fn(),
  reload: jest.fn(),
  replace: jest.fn(),
  events: {
    on: jest.fn(),
    off: jest.fn(),
    emit: jest.fn(),
  },
  isFallback: false,
  isLocaleDomain: false,
  isReady: true,
  defaultLocale: 'en',
  domainLocales: [],
  isPreview: false,
};

// Helper function to create a test store with proper typing
const createTestStore = (initialState: Partial<RootState['dashboard']> = {}) => {
  return configureStore({
    reducer: {
      dashboard: dashboardSlice.reducer,
    },
    preloadedState: {
      dashboard: {
        ...dashboardSlice.getInitialState(),
        desktopMobile: 0,
        responsiveLayouts: {
          desktop: { widgetLayout: [], widgets: [] },
          mobile: { widgetLayout: [], widgets: [] },
        },
        ...initialState,
      },
    },
  });
};

// Helper function to render component with providers
const renderWithProviders = (
  component: React.ReactElement,
  { store = createTestStore(), ...renderOptions } = {},
) => {
  const Wrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => (
    <Provider store={store}>
      <ThemeProvider theme={mockTheme}>{children}</ThemeProvider>
    </Provider>
  );

  return {
    store,
    ...render(component, { wrapper: Wrapper, ...renderOptions }),
  };
};

describe('ResponsiveLayoutToggle', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    (useRouter as jest.Mock).mockReturnValue(mockRouter);
  });

  describe('Component Rendering Tests', () => {
    it('should render correctly with default props', () => {
      const store = createTestStore({ desktopMobile: 0 });
      renderWithProviders(<ResponsiveLayoutToggle />, { store });

      // Check that both toggle buttons are present
      expect(screen.getByLabelText('Desktop layout')).toBeInTheDocument();
      expect(screen.getByLabelText('Mobile layout')).toBeInTheDocument();

      // Check that the Paper container is present
      expect(screen.getByRole('group')).toBeInTheDocument();
    });

    it('should render correctly when disabled prop is false', () => {
      const store = createTestStore({ desktopMobile: 0 });
      renderWithProviders(<ResponsiveLayoutToggle disabled={false} />, { store });

      const desktopButton = screen.getByLabelText('Desktop layout');
      const mobileButton = screen.getByLabelText('Mobile layout');

      expect(desktopButton).not.toBeDisabled();
      expect(mobileButton).not.toBeDisabled();
    });

    it('should render correctly when disabled prop is true', () => {
      const store = createTestStore({ desktopMobile: 0 });
      renderWithProviders(<ResponsiveLayoutToggle disabled={true} />, { store });

      const desktopButton = screen.getByLabelText('Desktop layout');
      const mobileButton = screen.getByLabelText('Mobile layout');

      expect(desktopButton).toBeDisabled();
      expect(mobileButton).toBeDisabled();
    });

    it('should display correct icons for desktop and mobile buttons', () => {
      const store = createTestStore({ desktopMobile: 0 });
      renderWithProviders(<ResponsiveLayoutToggle />, { store });

      // Check for Material-UI icons by their test IDs or aria-labels
      const desktopButton = screen.getByLabelText('Desktop layout');
      const mobileButton = screen.getByLabelText('Mobile layout');

      expect(desktopButton).toBeInTheDocument();
      expect(mobileButton).toBeInTheDocument();

      // Verify the buttons contain the expected icons
      expect(desktopButton.querySelector('svg')).toBeInTheDocument();
      expect(mobileButton.querySelector('svg')).toBeInTheDocument();
    });

    it('should have correct accessibility attributes', () => {
      const store = createTestStore({ desktopMobile: 0 });
      renderWithProviders(<ResponsiveLayoutToggle />, { store });

      const desktopButton = screen.getByLabelText('Desktop layout');
      const mobileButton = screen.getByLabelText('Mobile layout');

      expect(desktopButton).toHaveAttribute('aria-label', 'Desktop layout');
      expect(mobileButton).toHaveAttribute('aria-label', 'Mobile layout');
      expect(desktopButton).toHaveAttribute('value', '0');
      expect(mobileButton).toHaveAttribute('value', '1');
    });

    it('should render different layouts based on router pathname', () => {
      // Test regular dashboard path
      (useRouter as jest.Mock).mockReturnValue({
        ...mockRouter,
        pathname: '/customer/1/dashboard/1',
      });

      const store = createTestStore({ desktopMobile: 0 });
      const { unmount } = renderWithProviders(<ResponsiveLayoutToggle />, { store });

      expect(screen.getByLabelText('Desktop layout')).toBeInTheDocument();
      expect(screen.getByLabelText('Mobile layout')).toBeInTheDocument();

      unmount();

      // Test dashboard template path
      (useRouter as jest.Mock).mockReturnValue({
        ...mockRouter,
        pathname: '/dashboard-template',
      });

      renderWithProviders(<ResponsiveLayoutToggle />, { store });

      // Should still render both buttons but with different styling
      expect(screen.getByLabelText('Desktop layout')).toBeInTheDocument();
      expect(screen.getByLabelText('Mobile layout')).toBeInTheDocument();
    });
  });

  describe('State Management Tests', () => {
    it('should display desktop mode as active when currentMode is 0', () => {
      const store = createTestStore({ desktopMobile: 0 });
      renderWithProviders(<ResponsiveLayoutToggle />, { store });

      const desktopButton = screen.getByLabelText('Desktop layout');
      const mobileButton = screen.getByLabelText('Mobile layout');

      // Check that the correct button has the selected state
      // Material-UI ToggleButton uses value prop to determine selection
      expect(desktopButton).toHaveAttribute('value', '0');
      expect(mobileButton).toHaveAttribute('value', '1');

      // The ToggleButtonGroup should have value="0" when desktop is selected
      const toggleGroup = screen.getByRole('group');
      expect(toggleGroup).toBeInTheDocument();
    });

    it('should display mobile mode as active when currentMode is 1', () => {
      const store = createTestStore({ desktopMobile: 1 });
      renderWithProviders(<ResponsiveLayoutToggle />, { store });

      const desktopButton = screen.getByLabelText('Desktop layout');
      const mobileButton = screen.getByLabelText('Mobile layout');

      expect(desktopButton).toHaveAttribute('value', '0');
      expect(mobileButton).toHaveAttribute('value', '1');

      // The ToggleButtonGroup should have value="1" when mobile is selected
      const toggleGroup = screen.getByRole('group');
      expect(toggleGroup).toBeInTheDocument();
    });

    it('should reflect current Redux state correctly', () => {
      const store = createTestStore({ desktopMobile: 0 });
      renderWithProviders(<ResponsiveLayoutToggle />, { store });

      // Verify the component reads from Redux state
      const state = store.getState();
      expect(state.dashboard.desktopMobile).toBe(0);

      // Verify the component renders based on state
      expect(screen.getByLabelText('Desktop layout')).toBeInTheDocument();
      expect(screen.getByLabelText('Mobile layout')).toBeInTheDocument();
    });
  });

  describe('User Interaction Tests', () => {
    it('should dispatch setDesktopMobileMode(0) when desktop button is clicked from mobile mode', async () => {
      const store = createTestStore({ desktopMobile: 1 }); // Start in mobile mode
      renderWithProviders(<ResponsiveLayoutToggle />, { store });

      const desktopButton = screen.getByLabelText('Desktop layout');

      // Click the desktop button
      await userEvent.click(desktopButton);

      // Verify the Redux state was updated
      const state = store.getState();
      expect(state.dashboard.desktopMobile).toBe(0);
    });

    it('should dispatch setDesktopMobileMode(1) when mobile button is clicked from desktop mode', async () => {
      const store = createTestStore({ desktopMobile: 0 }); // Start in desktop mode
      renderWithProviders(<ResponsiveLayoutToggle />, { store });

      const mobileButton = screen.getByLabelText('Mobile layout');

      // Click the mobile button
      await userEvent.click(mobileButton);

      // Verify the Redux state was updated
      const state = store.getState();
      expect(state.dashboard.desktopMobile).toBe(1);
    });

    it('should not change state when disabled and button is clicked', async () => {
      const store = createTestStore({ desktopMobile: 0 });
      renderWithProviders(<ResponsiveLayoutToggle disabled={true} />, { store });

      const mobileButton = screen.getByLabelText('Mobile layout');

      // Verify button is disabled
      expect(mobileButton).toBeDisabled();

      // Use fireEvent for disabled elements since userEvent can't interact with them
      fireEvent.click(mobileButton);

      // State should remain unchanged
      const state = store.getState();
      expect(state.dashboard.desktopMobile).toBe(0);
    });

    it('should handle rapid button clicks correctly', async () => {
      const store = createTestStore({ desktopMobile: 0 });
      renderWithProviders(<ResponsiveLayoutToggle />, { store });

      const mobileButton = screen.getByLabelText('Mobile layout');
      const desktopButton = screen.getByLabelText('Desktop layout');

      // Click mobile button
      await userEvent.click(mobileButton);
      expect(store.getState().dashboard.desktopMobile).toBe(1);

      // Click desktop button
      await userEvent.click(desktopButton);
      expect(store.getState().dashboard.desktopMobile).toBe(0);

      // Click mobile button again
      await userEvent.click(mobileButton);
      expect(store.getState().dashboard.desktopMobile).toBe(1);
    });

    it('should handle null mode change gracefully', async () => {
      const store = createTestStore({ desktopMobile: 0 });
      renderWithProviders(<ResponsiveLayoutToggle />, { store });

      const mobileButton = screen.getByLabelText('Mobile layout');

      // Simulate clicking the same button that's already selected
      await userEvent.click(mobileButton);

      // Should still work correctly
      expect(store.getState().dashboard.desktopMobile).toBe(1);
    });
  });

  describe('Tooltip Tests', () => {
    it('should show desktop tooltip on hover', async () => {
      const store = createTestStore({ desktopMobile: 0 });
      renderWithProviders(<ResponsiveLayoutToggle />, { store });

      const desktopButton = screen.getByLabelText('Desktop layout');

      await userEvent.hover(desktopButton);

      await waitFor(() => {
        expect(screen.getByRole('tooltip')).toBeInTheDocument();
        expect(screen.getByRole('tooltip')).toHaveTextContent('Desktop Layout');
      });
    });

    it('should show mobile tooltip on hover', async () => {
      const store = createTestStore({ desktopMobile: 0 });
      renderWithProviders(<ResponsiveLayoutToggle />, { store });

      const mobileButton = screen.getByLabelText('Mobile layout');

      await userEvent.hover(mobileButton);

      await waitFor(() => {
        expect(screen.getByRole('tooltip')).toBeInTheDocument();
        expect(screen.getByRole('tooltip')).toHaveTextContent('Mobile Layout');
      });
    });

    it('should hide tooltip when mouse leaves', async () => {
      const store = createTestStore({ desktopMobile: 0 });
      renderWithProviders(<ResponsiveLayoutToggle />, { store });

      const desktopButton = screen.getByLabelText('Desktop layout');

      await userEvent.hover(desktopButton);

      await waitFor(() => {
        expect(screen.getByRole('tooltip')).toBeInTheDocument();
      });

      await userEvent.unhover(desktopButton);

      await waitFor(() => {
        expect(screen.queryByRole('tooltip')).not.toBeInTheDocument();
      });
    });
  });

  describe('Edge Cases', () => {
    it('should handle undefined currentMode gracefully', () => {
      const store = createTestStore({ desktopMobile: undefined as any });

      // Should not crash when currentMode is undefined
      expect(() => {
        renderWithProviders(<ResponsiveLayoutToggle />, { store });
      }).not.toThrow();

      // Should default to desktop mode (0)
      expect(screen.getByLabelText('Desktop layout')).toBeInTheDocument();
      expect(screen.getByLabelText('Mobile layout')).toBeInTheDocument();
    });

    it('should handle invalid currentMode values', () => {
      const store = createTestStore({ desktopMobile: 999 as any });
      renderWithProviders(<ResponsiveLayoutToggle />, { store });

      // Should still render without crashing
      expect(screen.getByLabelText('Desktop layout')).toBeInTheDocument();
      expect(screen.getByLabelText('Mobile layout')).toBeInTheDocument();
    });

    it('should handle responsive layout state changes', async () => {
      const store = createTestStore({
        desktopMobile: 0,
        responsiveLayouts: {
          desktop: { widgetLayout: [{ i: '1', x: 0, y: 0, w: 4, h: 3 }], widgets: [] },
          mobile: { widgetLayout: [{ i: '1', x: 0, y: 0, w: 2, h: 3 }], widgets: [] },
        },
        widget: {
          widgets: [],
          widgetLayout: [{ i: '1', x: 0, y: 0, w: 4, h: 3 }],
          deleteWidgets: [],
          lastWidgetId: 1,
        },
      });

      renderWithProviders(<ResponsiveLayoutToggle />, { store });

      const mobileButton = screen.getByLabelText('Mobile layout');
      await userEvent.click(mobileButton);

      // Verify the responsive layout system is working
      const state = store.getState();
      expect(state.dashboard.desktopMobile).toBe(1);
      expect(state.dashboard.widget.widgetLayout).toEqual([{ i: '1', x: 0, y: 0, w: 2, h: 3 }]);
    });
  });

  describe('Redux Integration Tests', () => {
    it('should properly integrate with Redux store', () => {
      const store = createTestStore({ desktopMobile: 0 });
      renderWithProviders(<ResponsiveLayoutToggle />, { store });

      // Verify initial state
      expect(store.getState().dashboard.desktopMobile).toBe(0);

      // Component should render based on Redux state
      expect(screen.getByLabelText('Desktop layout')).toBeInTheDocument();
      expect(screen.getByLabelText('Mobile layout')).toBeInTheDocument();
    });

    it('should update Redux state when mode changes', async () => {
      const store = createTestStore({ desktopMobile: 0 });
      renderWithProviders(<ResponsiveLayoutToggle />, { store });

      const mobileButton = screen.getByLabelText('Mobile layout');
      await userEvent.click(mobileButton);

      // Verify Redux state was updated
      expect(store.getState().dashboard.desktopMobile).toBe(1);
    });

    it('should handle store state changes from external sources', () => {
      const store = createTestStore({ desktopMobile: 0 });
      const { rerender } = renderWithProviders(<ResponsiveLayoutToggle />, { store });

      // Simulate external state change
      store.dispatch(dashboardSlice.actions.setDesktopMobileMode(1));

      // Re-render to reflect state change
      rerender(<ResponsiveLayoutToggle />);

      // Component should reflect the new state
      expect(store.getState().dashboard.desktopMobile).toBe(1);
    });
  });
});
