import { Tree } from './tree';

describe('Tree', () => {
  describe('given a single node tree', () => {
    const tree = new Tree('id0', 'root', 'company');

    test('find root node should return it', () => {
      expect(tree.find('id0')).toStrictEqual({
        id: 'id0',
        tag: 'root',
        type: 'company',
      });
    });

    test('find non existing node should return null', () => {
      expect(tree.find('404')).toBeNull();
    });
  });

  describe('given a tree with two children', () => {
    const tree = new Tree('id0', 'root', 'company', [
      new Tree('id1', 'child1', 'activo'),
      new Tree('id2', 'child2', 'activo'),
    ]);

    test('find first child should return it', () => {
      expect(tree.find('id1')).toStrictEqual({
        id: 'id1',
        tag: 'child1',
        type: 'activo',
      });
    });

    test('find second child should return it', () => {
      expect(tree.find('id2')).toStrictEqual({
        id: 'id2',
        tag: 'child2',
        type: 'activo',
      });
    });
  });
});
