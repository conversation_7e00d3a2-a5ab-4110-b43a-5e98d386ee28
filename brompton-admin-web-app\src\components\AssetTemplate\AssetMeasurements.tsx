import { <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, TextField } from '@mui/material';
import DeleteIcon from '@mui/icons-material/Delete';
import {
  Control,
  Controller,
  FieldArrayWithId,
  FieldErrors,
  UseFieldArrayRemove,
} from 'react-hook-form';
import { createAssetTemplateData } from '~/measurements/domain/types';
import { ControlledAutocomplete } from '~/shared/forms/components/ControlledAutocomplete';
import { useGetAllBackOfficeAssetTypesMetricsQuery } from '~/redux/api/assetsApi';
import { useMemo } from 'react';
import { mapListToOptions } from '~/utils/utils';

type AssetMeasurementsProps = {
  index: number;
  remove: UseFieldArrayRemove;
  errors: FieldErrors<createAssetTemplateData>;
  field: FieldArrayWithId<createAssetTemplateData, 'measurements', 'id'>;
  control: Control<createAssetTemplateData, any>;
  valueTypeOptions: {
    id: string;
    label: string;
  }[];
  datasourceOptions: {
    id: string;
    label: string;
  }[];
  locationsListOption: {
    id: string;
    label: string;
  }[];
  dataTypesListOptions: {
    id: string;
    label: string;
  }[];
  measurementTypeListOptions: {
    id: string;
    label: string;
  }[];
  assetTypeId: number;
};
const AssetMeasurements = ({
  index,
  remove,
  errors,
  field,
  control,
  valueTypeOptions,
  datasourceOptions,
  locationsListOption,
  measurementTypeListOptions,
  dataTypesListOptions,
  assetTypeId,
}: AssetMeasurementsProps) => {
  const { data: assetTypeMetrics } = useGetAllBackOfficeAssetTypesMetricsQuery(
    {
      assetId: assetTypeId ? assetTypeId.toString() : '',
    },
    {
      skip: assetTypeId === undefined,
      refetchOnMountOrArgChange: true,
    },
  );
  const assetTypeMetricsListOptions = useMemo(
    () => mapListToOptions(assetTypeMetrics?.items ?? []),
    [assetTypeMetrics],
  );
  return (
    <Card sx={{ mt: 3 }}>
      <CardHeader
        title={'Measurement #' + (index + 1)}
        action={
          <>
            <Button onClick={() => remove(index)} startIcon={<DeleteIcon />} variant="contained">
              Remove
            </Button>
          </>
        }
      />
      <CardContent>
        <Box key={field.id} sx={{ display: 'flex', flexDirection: 'row', mb: 2, gap: 4 }}>
          <Grid container spacing={2}>
            <Grid item xs={12} sm={4}>
              <ControlledAutocomplete
                // eslint-disable-next-line @typescript-eslint/ban-ts-comment
                //@ts-ignore
                control={control}
                fieldName={`measurements.${index}.type_id`}
                label="Type Id"
                options={measurementTypeListOptions}
                required
              />
            </Grid>
            <Grid item xs={12} sm={4}>
              <ControlledAutocomplete
                // eslint-disable-next-line @typescript-eslint/ban-ts-comment
                //@ts-ignore
                control={control}
                fieldName={`measurements.${index}.data_type_id`}
                label="Data Type"
                options={dataTypesListOptions}
                required
              />
            </Grid>
            <Grid item xs={12} sm={4}>
              <ControlledAutocomplete
                // eslint-disable-next-line @typescript-eslint/ban-ts-comment
                //@ts-ignore
                control={control}
                fieldName={`measurements.${index}.value_type_id`}
                label="Select value type"
                options={valueTypeOptions}
                required
              />
            </Grid>
            <Grid item xs={12} sm={4}>
              <ControlledAutocomplete
                // eslint-disable-next-line @typescript-eslint/ban-ts-comment
                //@ts-ignore
                control={control}
                fieldName={`measurements.${index}.metric_id`}
                label="Select Metric"
                options={assetTypeMetricsListOptions}
                required
              />
            </Grid>

            <Grid item xs={12} sm={4}>
              <Controller
                name={`measurements.${index}.description`}
                control={control}
                render={({ field }) => (
                  <TextField
                    sx={{
                      mt: 2,
                    }}
                    {...field}
                    label="Description"
                    error={!!errors.measurements?.[index]?.description}
                    helperText={errors.measurements?.[index]?.description?.message || ''}
                    fullWidth
                  />
                )}
              />
            </Grid>
            <Grid item xs={12} sm={4}>
              <ControlledAutocomplete
                // eslint-disable-next-line @typescript-eslint/ban-ts-comment
                //@ts-ignore
                control={control}
                fieldName={`measurements.${index}.location_id`}
                label="Location"
                options={locationsListOption}
              />
            </Grid>
            <Grid item xs={12} sm={4}>
              <ControlledAutocomplete
                // eslint-disable-next-line @typescript-eslint/ban-ts-comment
                //@ts-ignore
                control={control}
                fieldName={`measurements.${index}.datasource_id`}
                label="Data Source"
                options={datasourceOptions}
              />
            </Grid>
            <Grid item xs={12} sm={4}>
              <Controller
                name={`measurements.${index}.meter_factor`}
                control={control}
                render={({ field }) => (
                  <TextField
                    sx={{
                      mt: 2,
                    }}
                    {...field}
                    label="Meter Factor"
                    type="number"
                    error={!!errors.measurements?.[index]?.meter_factor}
                    helperText={errors.measurements?.[index]?.meter_factor?.message || ''}
                    fullWidth
                  />
                )}
              />
            </Grid>
          </Grid>
        </Box>
      </CardContent>
    </Card>
  );
};
export default AssetMeasurements;
