import {
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
} from '@mui/material';

type ConfirmationDialogProps = {
  open: boolean;
  title: string;
  onDelete: () => unknown;
  onCancel: () => unknown;
  description?: string;
};

export const ConfirmationDialog = (props: ConfirmationDialogProps): JSX.Element => {
  const { open, title, description, onDelete, onCancel } = props;
  return (
    <Dialog maxWidth="xs" open={open} onClose={onCancel}>
      <DialogTitle>{title}</DialogTitle>
      {description && (
        <DialogContent>
          <DialogContentText variant="body1">{description}</DialogContentText>
        </DialogContent>
      )}
      <DialogActions sx={{ justifyContent: 'flex-start', p: 3 }}>
        <Button autoFocus sx={{ mr: 'auto' }} size="medium" onClick={onCancel}>
          Cancel
        </Button>
        <Button variant="contained" color="error" size="medium" onClick={onDelete}>
          Delete
        </Button>
      </DialogActions>
    </Dialog>
  );
};
