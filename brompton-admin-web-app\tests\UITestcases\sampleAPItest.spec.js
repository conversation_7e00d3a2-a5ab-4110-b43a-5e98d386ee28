import { test, expect } from '@playwright/test';
import { request } from 'http';
import { json } from 'stream/consumers';

var userId;

test('Get users', async ({ request }) => {
  const response = await request.get('https://reqres.in/api/users?page=2');
  console.log(await response.json());
  expect(response.status()).toBe(200);
});

test('Login user', async ({ request }) => {
  const response = await request.post('https://test.brompton.ai/api/v0/sessions', {
    data: {
      username: 'test',
      password: 'asdfasdf',
    },
    header: { Aceept: 'application/json' },
  });
  console.log(await response.json());
  expect(response.status()).toBe(201);

  var res = await response.json();
  userId = res.id;
});
test('Update user', async ({ request }) => {
  const response = await request.put('https://reqres.in/api/users/' + userId, {
    data: { name: 'API Testing', job: 'Automation' },
    header: { Aceept: 'application/json' },
  });
  console.log(await response.json());
  expect(response.status()).toBe(200);
});

test('Delete user', async ({ request }) => {
  const response = await request.delete('https://reqres.in/api/users/' + userId);
  expect(response.status()).toBe(204);
});
