import Box from '@mui/material/Box';
import Stepper from '@mui/material/Stepper';
import Step from '@mui/material/Step';
import StepButton from '@mui/material/StepButton';
import Button from '@mui/material/Button';
import Typography from '@mui/material/Typography';
import { Fragment, useMemo, useState } from 'react';
import { createAssetTemplateData, CreateAssetTemplateSchema } from '~/measurements/domain/types';
import { Controller, useFieldArray, useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import { ControlledAutocomplete } from '~/shared/forms/components/ControlledAutocomplete';
import { TextField } from '@mui/material';
import { useGetAllBackOfficeAssetTypesQuery } from '~/redux/api/assetsApi';
import { mapListToOptions } from '~/utils/utils';
import {
  useGetAllDatasourcesQuery,
  useGetAllDataTypesQuery,
  useGetAllLocationsQuery,
  useGetAllMeasureTypesQuery,
  useGetAllValueTypesQuery,
} from '~/redux/api/measuresApi';
import AddIcon from '@mui/icons-material/Add';
import AssetMeasurements from './AssetMeasurements';

const steps = ['Select Asset', 'Add Measurements'];

export default function AssetTemplateStepper() {
  const [activeStep, setActiveStep] = useState(0);
  const [currentMeausreIndex, setCurrentMeasureIndex] = useState<number>(-1);

  const [completed, setCompleted] = useState<{
    [k: number]: boolean;
  }>({});

  const totalSteps = () => {
    return steps.length;
  };

  const completedSteps = () => {
    return Object.keys(completed).length;
  };

  const isLastStep = () => {
    return activeStep === totalSteps() - 1;
  };
  const allStepsCompleted = () => {
    return completedSteps() === totalSteps();
  };

  const handleBack = () => {
    setActiveStep((prevActiveStep) => prevActiveStep - 1);
  };

  const handleStep = (step: number) => () => {
    setActiveStep(step);
  };

  const handleComplete = () => {
    const newCompleted = completed;
    newCompleted[activeStep] = true;
    setCompleted(newCompleted);
    handleNext();
  };

  const handleReset = () => {
    setActiveStep(0);
    setCompleted({});
  };
  const { data: assetTypeListData } = useGetAllBackOfficeAssetTypesQuery();

  const {
    control,
    handleSubmit,
    formState: { errors },
    watch,
  } = useForm<createAssetTemplateData>({
    defaultValues: {
      manufacturer: '',
      model_number: '',
      assetTypeId: 0,
      measurements: [],
      //   measurements: [
      //     {
      //       type_id: undefined,
      //       data_type_id: undefined,
      //       datasource_id: undefined,
      //       metric_id: undefined,
      //       value_type_id: undefined,
      //       meter_factor: undefined,
      //       description: '',
      //       location_id: undefined,
      //     },
      //   ],
    },
    resolver: yupResolver(CreateAssetTemplateSchema),
  });
  const handleNext = handleSubmit(() => {
    if (Object.keys(errors).length >= 2 && activeStep === 0) {
      return;
    }
    if (activeStep === 0) {
      const newActiveStep =
        isLastStep() && !allStepsCompleted()
          ? steps.findIndex((step, i) => !(i in completed))
          : activeStep + 1;
      setActiveStep(newActiveStep);
    }
  });
  const { fields, append, remove } = useFieldArray({
    control,
    name: 'measurements',
  });
  const { data: valueTypeList } = useGetAllValueTypesQuery();
  const { data: datasourceList } = useGetAllDatasourcesQuery({});
  const { data: locationsList } = useGetAllLocationsQuery();
  const { data: dataTypeList } = useGetAllDataTypesQuery();
  const { data: measurementTypeList } = useGetAllMeasureTypesQuery();

  const valueTypeOptions = useMemo(() => mapListToOptions(valueTypeList ?? []), [valueTypeList]);
  const datasourceOptions = useMemo(
    () => mapListToOptions(datasourceList?.items ?? []),
    [datasourceList],
  );
  const locationsListOption = useMemo(
    () => mapListToOptions(locationsList?.items ?? []),
    [locationsList],
  );
  const dataTypesListOptions = useMemo(() => mapListToOptions(dataTypeList ?? []), [dataTypeList]);
  const measurementTypeListOptions = useMemo(
    () => mapListToOptions(measurementTypeList ?? []),
    [measurementTypeList],
  );
  const assetTypeListOptions = useMemo(
    () =>
      mapListToOptions(
        assetTypeListData?.map((item) => ({
          ...item,
          name: `${item.name} (${item.asset_template_count})`,
          id: item.id,
        })) ?? [],
      ),
    [assetTypeListData],
  );

  const assetTypeId = watch('assetTypeId');

  return (
    <Box sx={{ width: '100%' }}>
      <form
        // onSubmit={handleSubmit(async (data) => {
        //   try {
        //     // await createAssetTemplate(data);
        //     // reset();
        //   } catch (err) {
        //     console.log(err);
        //     // Handle the error here
        //   }
        // })}
        onSubmit={handleNext}
        noValidate
      >
        <Stepper nonLinear activeStep={activeStep} sx={{ mt: 2 }}>
          {steps.map((label, index) => (
            <Step key={label} completed={completed[index]}>
              <StepButton color="inherit" onClick={handleStep(index)}>
                {label}
              </StepButton>
            </Step>
          ))}
        </Stepper>
        {activeStep === 0 ? (
          <>
            <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
              <ControlledAutocomplete
                // eslint-disable-next-line @typescript-eslint/ban-ts-comment
                //@ts-ignore
                control={control}
                fieldName={`assetTypeId`}
                label="Asset Type"
                options={assetTypeListOptions}
              />
              <Controller
                name="manufacturer"
                control={control}
                render={({ field: { onChange, onBlur, value }, fieldState }) => (
                  <TextField
                    error={!!fieldState.error}
                    helperText={fieldState.error?.message}
                    onChange={onChange}
                    onBlur={onBlur}
                    value={value}
                    label="Manufacturer"
                    variant="outlined"
                    margin="normal"
                    fullWidth
                    required
                  />
                )}
              />
              <Controller
                name="model_number"
                control={control}
                render={({ field: { onChange, onBlur, value }, fieldState }) => (
                  <TextField
                    error={!!fieldState.error}
                    helperText={fieldState.error?.message}
                    onChange={onChange}
                    onBlur={onBlur}
                    value={value}
                    label="Model Number"
                    variant="outlined"
                    margin="normal"
                    fullWidth
                    required
                  />
                )}
              />
            </Box>
          </>
        ) : null}
        {activeStep === 1 ? (
          <>
            <Button
              onClick={() =>
                append({ type_id: null, data_type_id: null, value_type_id: null, metric_id: null })
              }
              variant="contained"
              size="large"
              sx={{ mb: 2, mt: 3 }}
              startIcon={<AddIcon />}
            >
              Add Measurement
            </Button>
            <Typography color={'red'} mb={3}>
              {errors?.measurements?.message}
            </Typography>

            {/* <AssetTemplateTable
              fields={fields}
              remove={remove}
              setCurrentMeasureIndex={setCurrentMeasureIndex}
              valueTypeOptions={valueTypeOptions}
              datasourceOptions={datasourceOptions}
              locationsListOption={locationsListOption}
              dataTypesListOptions={dataTypesListOptions}
              measurementTypeListOptions={measurementTypeListOptions}
              assetTypeMetricsListOptions={assetTypeMetricsListOptions}
              //   assetTypeId={assetTypeId}
            /> */}

            {fields.map((field, index) => (
              <>
                {index === currentMeausreIndex ? (
                  <AssetMeasurements
                    index={index}
                    key={index}
                    control={control}
                    errors={errors}
                    field={field}
                    remove={remove}
                    valueTypeOptions={valueTypeOptions}
                    datasourceOptions={datasourceOptions}
                    locationsListOption={locationsListOption}
                    dataTypesListOptions={dataTypesListOptions}
                    measurementTypeListOptions={measurementTypeListOptions}
                    assetTypeId={assetTypeId}
                  />
                ) : null}
              </>
            ))}
          </>
        ) : null}
        <Fragment>
          <Box sx={{ display: 'flex', flexDirection: 'row', pt: 2 }}>
            <Button
              color="inherit"
              variant="outlined"
              disabled={activeStep === 0}
              onClick={handleBack}
              sx={{ mr: 1, width: 200 }}
            >
              Back
            </Button>
            <Box sx={{ flex: '1 1 auto' }} />
            <Button
              type="submit"
              variant="contained"
              size="large"
              onClick={handleNext}
              sx={{ mt: 2, width: 200 }}
            >
              Next
            </Button>
          </Box>
        </Fragment>
      </form>
    </Box>
  );
}
