import { createSelector } from 'reselect';
import { RootState } from '~/redux/store';

const userPreferences = (state: RootState) => state.dashboard.userPreferences;

export const getUserPreferences = createSelector(
  [userPreferences],
  (userPreferences) => userPreferences,
);

export const getDateTimeFormat = createSelector([userPreferences], (userPreferences) => {
  return userPreferences.DATE_FORMAT;
});

export const getDefaultCustomer = createSelector([userPreferences], (userPreferences) => {
  return userPreferences.DEFAULT_CUSTOMER;
});

export const getDateTimeFormatForChart = createSelector([userPreferences], (userPreferences) => {
  return userPreferences.DATE_FORMAT.replaceAll('/', '-');
});

export const getThousandSeparator = createSelector([userPreferences], (userPreferences) => {
  return userPreferences.THOUSAND_SEPARATOR === 'enabled';
});
