import { yupResolver } from '@hookform/resolvers/yup';
import { Dispatch, SetStateAction, useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { useFieldArray, useForm } from 'react-hook-form';
import { useDispatch } from 'react-redux';
import { ThunkDispatch } from 'redux-thunk';
import {
  AssetMeasurement,
  createAssetTemplateFromAsset,
  createTemplateFromAssetSchema,
  templatefromAssetmeasurementListData,
  TemplateFromAsssetMeasurementSchema,
} from '~/measurements/domain/types';
import { useGetAllBackOfficeAssetTypesMetricsQuery } from '~/redux/api/assetsApi';
import {
  calculationEngineApi,
  useGetCalculationEngineTemplatesQuery,
  useGetPollPeriodsQuery,
} from '~/redux/api/calculationEngine';
import {
  useGetAllDatasourcesQuery,
  useGetAllDataTypesQuery,
  useGetAllLocationsQuery,
  useGetAllMeasureTypesQuery,
  useGetAllValueTypesQuery,
} from '~/redux/api/measuresApi';
import { RootState } from '~/redux/store';
import { AssetDoDetails } from '~/types/asset';
import {
  assetTocalculationMeasurementSchema,
  assetToCalculationMeasurementSchemaData,
  CalculationMeasurementSchemaData,
} from '~/types/calc_engine';
import { mapListToOptions } from '~/utils/utils';
const useCreateTemplateFromAsset = ({
  activeStep,
  currentMeausreIndex,
  asset,
  measurements,
  measurementIds,
  setMeasurementIds,
}: {
  activeStep: number;
  currentMeausreIndex: number;
  asset: AssetDoDetails;
  measurements: AssetMeasurement[] | undefined;
  measurementIds: number[];
  setMeasurementIds: Dispatch<SetStateAction<number[]>>;
}) => {
  const dispatch = useDispatch<ThunkDispatch<RootState, any, any>>();
  const [existingMeasurementMetrics, setExistingMeasurementMetrics] = useState<string[]>([]);
  const [calcMeasurements, setCalcMeasurements] = useState<
    {
      metric_id: string;
      existing: boolean;
      calcMeasurementData: assetToCalculationMeasurementSchemaData;
    }[]
  >([]);
  const { data: assetTypeMetrics } = useGetAllBackOfficeAssetTypesMetricsQuery(
    {
      assetId: asset?.type_id ? asset?.type_id?.toString() : '',
    },
    {
      skip: asset?.type_id === undefined || asset?.type_id === 0,
      refetchOnMountOrArgChange: true,
    },
  );
  const { data: valueTypeList } = useGetAllValueTypesQuery();
  const { data: datasourceList } = useGetAllDatasourcesQuery({});
  const { data: locationsList } = useGetAllLocationsQuery();
  const { data: dataTypeList } = useGetAllDataTypesQuery();
  const { data: measurementTypeList } = useGetAllMeasureTypesQuery();
  const { data: expressionTemplates, isLoading: fetchingExpressionTemplates } =
    useGetCalculationEngineTemplatesQuery(undefined, { skip: activeStep !== 2 });
  const { data: pollPeriods } = useGetPollPeriodsQuery(undefined);
  const valueTypeOptions = useMemo(() => mapListToOptions(valueTypeList ?? []), [valueTypeList]);
  const datasourceOptions = useMemo(
    () =>
      mapListToOptions(
        datasourceList?.items.filter((source) => source.name !== 'TimeVaryingFactor') ?? [],
      ),
    [datasourceList],
  );

  const assetTypeMetricsListOptions = useMemo(
    () => mapListToOptions(assetTypeMetrics?.items ?? []),
    [assetTypeMetrics],
  );
  const locationsListOption = useMemo(
    () => mapListToOptions(locationsList?.items ?? []),
    [locationsList],
  );
  const dataTypesListOptions = useMemo(() => mapListToOptions(dataTypeList ?? []), [dataTypeList]);
  const measurementTypeListOptions = useMemo(
    () => mapListToOptions(measurementTypeList ?? []),
    [measurementTypeList],
  );
  const {
    control,
    handleSubmit,
    formState: { errors },
    getValues: formValues,
    trigger, // Added to manually trigger validation
    setValue, // To set specific values to the form
  } = useForm<createAssetTemplateFromAsset>({
    resolver: yupResolver(createTemplateFromAssetSchema),
    defaultValues: {
      save_as_global_asset_template: false,
      measurements: [], // Default values for measurements
    },
  });
  const { fields, append, remove, update } = useFieldArray({
    control,
    rules: {
      required: true,
    },
    name: 'measurements',
  });
  const {
    control: measurementsControl,
    handleSubmit: measurementSubmit,
    getValues: measurementGetValues,
    formState: { errors: measureErrors },
    setValue: setMeasureValue,
    reset: resetMeasure,
    clearErrors: measureClearError,
  } = useForm<templatefromAssetmeasurementListData>({
    defaultValues:
      currentMeausreIndex >= 0
        ? fields[currentMeausreIndex]
        : {
            type_id: undefined,
            data_type_id: undefined,
            value_type_id: undefined,
            metric_id: undefined,
            description: '',
            location_id: undefined,
            datasource_id: undefined,
            meter_factor: undefined,
          },
    resolver: yupResolver(TemplateFromAsssetMeasurementSchema),
  });

  const {
    control: calcMeasurementController,
    handleSubmit: calcMeasureHandleSubmit,
    getValues: calcMeasureGetValues,
    reset: resetCalcMeasureValues,
    watch: calcMeasureWatch,
    setValue: calcMeasureSetValue,
    formState: { errors: calcMeasureError },
  } = useForm<assetToCalculationMeasurementSchemaData>({
    defaultValues: {
      is_persisted: false,
      poll_period: null,
      writeback: false,
      expression_template_id: undefined,
      variable_inputs: [],
    },
    resolver: yupResolver(assetTocalculationMeasurementSchema),
  });
  const { fields: variableFields } = useFieldArray({
    control: calcMeasurementController,
    name: 'variable_inputs',
  });
  const calculationSource = datasourceList?.items?.find(
    (datasource) => datasource.name === 'Calculation',
  );
  const calculatedMeasures = useMemo(() => {
    return calculationSource
      ? fields.filter((metric) => metric.datasource_id === calculationSource.id)
      : [];
  }, [fields, calculationSource]);
  const hasDuplicates = () => {
    const values = fields.map((measure) => measure.metric_id?.toString());
    const uniqueValues = new Set(values);
    return values.length !== uniqueValues.size;
  };
  const appendedRef = useRef(false);

  const fetchCalculationByMeasureId = useCallback(
    async (measureId: number | undefined) => {
      if (!measureId) return;
      try {
        // dispatch the async thunk or RTK Query initiate call
        return await dispatch(
          calculationEngineApi.endpoints.getCalculationByMeasureId.initiate(measureId),
        ).unwrap(); // unwrap if you want to catch errors easily
      } catch (error) {
        console.error('Failed to fetch calculation:', error);
        return undefined;
      }
    },
    [dispatch],
  );

  useEffect(() => {
    if (!asset || !measurements) return;

    if (asset.type_id) {
      setValue('asset_type_id', asset.type_id);
    }

    if (appendedRef.current) return;

    if (fields.length !== 0 || measurementIds.length !== 0 || measurements.length === 0) {
      return;
    }

    const currentMeasurementIds = new Set<number>();
    const measurementsToAppend: {
      description: string | undefined;
      location_id: number | null;
      datasource_id: number | null;
      meter_factor: number | null;
      type_id: number | null;
      data_type_id: number | null;
      value_type_id: number | null;
      metric_id: number | null;
      id: number | null;
    }[] = [];

    for (const measurement of measurements) {
      const id = Number(measurement.id);
      if (!currentMeasurementIds.has(id)) {
        currentMeasurementIds.add(id);
        measurementsToAppend.push({
          type_id: measurement.typeId ?? null,
          data_type_id: measurement.dataTypeId ?? null,
          value_type_id: measurement.valueTypeId ?? null,
          metric_id:
            typeof measurement.metric_id === 'number'
              ? measurement.metric_id
              : Number(measurement.metric_id) || null,
          description: measurement.description ?? undefined,
          location_id: measurement.locationId ?? null,
          datasource_id: measurement.datasourceId ?? null,
          meter_factor: measurement.meterFactor ?? null,
          id: id || null,
        });
      }
    }

    async function processMeasurements() {
      const metricsToCalcs: Record<
        number,
        {
          pollPeriod: number | null;
          isPersisted: boolean;
          writeback: boolean;
          expressionTemplate: number | null;
          inputs: {
            variable: string;
            type: string;
            metric_id: number | null;
            constant_value: number | string | null | undefined;
            comment: string | null;
          }[];
        }
      > = {};
      for (const measurement of measurementsToAppend) {
        const isInvalidMeasurement =
          measurement.type_id === null ||
          measurement.data_type_id === null ||
          measurement.value_type_id === null;
        //   measurement.metric_id === null;

        if (!isInvalidMeasurement) {
          // eslint-disable-next-line @typescript-eslint/ban-ts-comment
          // @ts-ignore
          append(measurement);

          if (
            calculationSource &&
            measurement.datasource_id === calculationSource.id &&
            measurement.id
          ) {
            // Find the original measurement by id
            const findMeasure = measurements?.find((m) => m.id === measurement.id);
            if (findMeasure?.measurementId) {
              try {
                const calcInfo = await fetchCalculationByMeasureId(findMeasure.measurementId);
                const inputs = (calcInfo?.calcInputs ?? []).map((input) => {
                  // If you want to do something with input.measurementId, do it here
                  const measurement = measurements?.find(
                    (measure) => measure?.measurementId === input?.measurementId,
                  );
                  return {
                    variable: input.inputLabel,
                    type:
                      input.measurementId !== null
                        ? ('measurement' as 'measurement' | 'constant')
                        : ('constant' as 'measurement' | 'constant'),
                    metric_id: measurement?.metric_id ?? null,
                    constant_value:
                      input.constantNumber !== undefined
                        ? input.constantNumber
                        : input.constantString !== undefined
                        ? input.constantString
                        : null, // Default to null if both are undefined
                    comment: input.comment ?? null,
                  };
                });
                const expressionTemplate = calcInfo?.calcTemplate?.id ?? null;
                const isPersisted = calcInfo?.calcInstance?.ispersisted ?? false;
                const pollPeriod = calcInfo?.calcInstance?.pollPeriod ?? null;
                const writeback = pollPeriod !== null;
                if (findMeasure.metric_id) {
                  metricsToCalcs[findMeasure.metric_id] = {
                    pollPeriod,
                    isPersisted,
                    writeback,
                    expressionTemplate,
                    inputs,
                  };
                }
              } catch (error) {
                console.error('Failed to fetch calculation inputs:', error);
              }
            }
          }
        }
      }
      const calcMeasurementsArray = Object.entries(metricsToCalcs).map(
        ([measurementId, calcData]) => {
          // Find matching pollPeriod item to get the label
          const pollPeriodItem = pollPeriods?.items?.find(
            (poll) => poll.id === calcData.pollPeriod,
          );
          return {
            metric_id: measurementId,
            existing: true,
            calcMeasurementData: {
              writeback: calcData.writeback ?? null,
              expression_template_id: calcData.expressionTemplate ?? null,
              is_persisted: calcData.isPersisted,
              poll_period:
                calcData.pollPeriod !== null && calcData.pollPeriod !== undefined && pollPeriodItem
                  ? {
                      id: pollPeriodItem.id,
                      value: pollPeriodItem?.value,
                      label: pollPeriodItem?.value ?? null, // use label from pollPeriods or null fallback
                    }
                  : null,
              variable_inputs: (calcData.inputs ?? []).map((input) => ({
                variable: input.variable,
                type:
                  input.metric_id != null && input.metric_id !== undefined
                    ? ('measurement' as 'measurement' | 'constant')
                    : ('constant' as 'measurement' | 'constant'),
                metric_id: input.metric_id?.toString() ?? undefined,
                constant_value: input.constant_value ?? undefined,
                comment: input.comment ?? undefined,
              })),
            },
          };
        },
      );

      setCalcMeasurements(calcMeasurementsArray);
    }

    processMeasurements();

    if (measurements.length > 0) {
      const measurementIdsSet = new Set(measurements.map((m) => m.metric_id?.toString() ?? ''));
      const existingMetrics = measurements
        .filter((m) => measurementIdsSet.has(m.metric_id?.toString() ?? ''))
        .map((m) => m.metric_id?.toString() ?? '');
      setExistingMeasurementMetrics(existingMetrics);
    }

    setMeasurementIds(Array.from(currentMeasurementIds));
    appendedRef.current = true;
  }, [
    asset,
    pollPeriods,
    measurements,
    fields,
    measurementIds,
    setValue,
    append,
    setExistingMeasurementMetrics,
    calculationSource,
    fetchCalculationByMeasureId,
  ]);
  const steps = [
    'Asset Template Information',
    'Measurement Details',
    calculatedMeasures.length > 0 ? 'Calculated measurements' : undefined,
  ];
  return {
    steps,
    measurementIds,
    setExistingMeasurementMetrics,
    setMeasurementIds,
    existingMeasurementMetrics,
    calcMeasurements,
    setCalcMeasurements,
    hasDuplicates,
    calculationSource,
    calculatedMeasures,
    valueTypeOptions,
    datasourceOptions,
    assetTypeMetricsListOptions,
    locationsListOption,
    dataTypesListOptions,
    measurementTypeListOptions,
    expressionTemplates,
    fetchingExpressionTemplates,
    pollPeriods,
    calculationMeasurement: {
      calcMeasurementController,
      calcMeasureHandleSubmit,
      calcMeasureGetValues,
      calcMeasureError,
      calcMeasureWatch,
      resetCalcMeasureValues,
      calcMeasureSetValue,
      variableFields,
    },
    createAssetTemplateFromAssets: {
      control,
      handleSubmit,
      formState: { errors },
      getValues: formValues,
      trigger, // Added to manually trigger validation
      setValue,
    },
    fields,
    append,
    remove,
    update,
    measurementsAddEdit: {
      measurementSubmit,
      measurementGetValues,
      measureErrors,
      setMeasureValue,
      resetMeasure,
      measureClearError,
      measurementsControl,
    },
  };
};

export default useCreateTemplateFromAsset;
