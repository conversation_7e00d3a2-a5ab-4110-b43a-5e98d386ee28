import { yupResolver } from '@hookform/resolvers/yup';
import {
  <PERSON>ert,
  Autocomplete,
  Box,
  Button,
  Card,
  CardContent,
  CardHeader,
  Grid,
  Step,
  StepLabel,
  Stepper,
  TextField,
  Typography,
} from '@mui/material';
import { ThunkDispatch } from '@reduxjs/toolkit';
import { useRouter } from 'next/router';
import { useEffect, useMemo, useState } from 'react';
import { Controller, useForm, UseFormSetValue } from 'react-hook-form';
import { useDispatch, useSelector } from 'react-redux';
import AssetInstanceMeasureTable from '~/components/AssetTemplate/AssetInstanceMeasureTable';
import SearchAssetTemplates from '~/components/AssetTemplate/SearchAssetTemplates';
import { InvalidInputException } from '~/errors/exceptions';
import {
  createAssetTemplateDataWithId,
  createAssetTemplateInstance,
  CreateAssetTemplateInstanceSchema,
  measurementSchemaDataDTO,
  MeasurementSchemaForInstance,
} from '~/measurements/domain/types';
import {
  useCreateAssetTemplateInstanceMutation,
  useGetAllAssetQuery,
  useGetAllAssetTemplatedByAssetTypeQuery,
  useGetAllBackOfficeAssetTypesMetricsQuery,
  useGetAllBackOfficeAssetTypesQuery,
  useGetAllTimeZonesQuery,
} from '~/redux/api/assetsApi';
import { useGetCustomersQuery } from '~/redux/api/customersApi';
import {
  measuresApi,
  useGetAllDatasourcesQuery,
  useGetAllDataTypesQuery,
  useGetAllLocationsQuery,
  useGetAllMeasureTypesQuery,
  useGetAllUnitOfGroupsQuery,
  useGetAllValueTypesQuery,
  useGetUnitOMeasureWithMeasureTypeQuery,
} from '~/redux/api/measuresApi';
import { getActiveCustomer } from '~/redux/selectors/customerSelectors';
import { RootState } from '~/redux/store';
import { ControlledAutocomplete } from '~/shared/forms/components/ControlledAutocomplete';
import { ControlledTextField } from '~/shared/forms/components/ControlledTextField';
import { AlertMessage } from '~/shared/forms/types';
import { AssetDo, AssetTypeOption } from '~/types/asset';
import { assetTypePathMapperFilterTemplates } from '~/utils/mappers/asset-type-mapper';
import { assetsPathMapper, mapListToOptions } from '~/utils/utils';

function getSteps() {
  return ['Select Asset Template', 'Unit Group & Asset', 'Measurement'];
}

type CreateAssetTemplateInstanceProps = {
  selectedCustomerId?: number | undefined;
  asset?: AssetDo | undefined;
};
export default function CreateAssetTemplateInstance({
  selectedCustomerId,
  asset,
}: CreateAssetTemplateInstanceProps) {
  const ActiveCustomer = useSelector(getActiveCustomer);
  const [assetTypesWithPath, setAssetTypesWithPath] = useState<AssetTypeOption[]>([]);
  const { data: customersList } = useGetCustomersQuery({});
  const { data: unitOfGroupsList } = useGetAllUnitOfGroupsQuery();
  const { data: assetTypeListData, isSuccess: isSuccessfullBackOffieAssetTypes } =
    useGetAllBackOfficeAssetTypesQuery();
  const { data: locationsList } = useGetAllLocationsQuery();
  const { data: datasourceList } = useGetAllDatasourcesQuery({});
  const { data: valueTypeList } = useGetAllValueTypesQuery();
  const { data: dataTypeList } = useGetAllDataTypesQuery();
  const { data: measurementTypeList } = useGetAllMeasureTypesQuery();
  const { data: timeZonesList } = useGetAllTimeZonesQuery();
  const [selectedTemplate, setSelectedTemplet] = useState<createAssetTemplateDataWithId | null>(
    null,
  );
  const [
    createAssetTemplateInstance,
    { isError, isLoading, isSuccess, error, data: assetTemplateInstanceData },
  ] = useCreateAssetTemplateInstanceMutation();
  const router = useRouter();
  const { assetType, assetTemplate: assetTemplateId } = router.query;
  const { data: assetTemplates } = useGetAllAssetTemplatedByAssetTypeQuery(
    {
      assetTypeId: assetType !== undefined && assetType !== null ? assetType.toString() : '',
    },
    {
      refetchOnMountOrArgChange: true,
      skip: assetType === null || assetType === undefined,
    },
  );
  useEffect(() => {
    if (selectedCustomerId && asset) {
      const assetType = assetTypesWithPath.find((item) => item.value === asset.type_id);
      setAssetTemplateValue('asset.type_id', assetType?.value ?? 0);
    }
  }, [selectedCustomerId, asset]);
  useEffect(() => {
    if (assetType && assetTemplates && assetTemplateId) {
      setSelectedTemplet(
        assetTemplates.items?.find(
          // eslint-disable-next-line @typescript-eslint/ban-ts-comment
          //@ts-ignore
          (item) => item.id === parseInt(assetTemplateId.toString()),
        ) ?? null,
      );
      setAssetTemplateValue('asset.type_id', Number(assetType) ?? 0);
      setActiveStep(1);
    }
  }, [assetType, assetTemplates, setSelectedTemplet]);

  const [measurements, setMeasurements] = useState<measurementSchemaDataDTO[]>([]);
  const {
    control,
    handleSubmit,
    trigger,
    formState: { errors },
    getValues: getAssetTemplateValues,
    reset: resetAssetTemplate,
    setValue: setAssetTemplateValue,
    watch,
  } = useForm<createAssetTemplateInstance>({
    defaultValues: {
      units_group_id: 0,
      prefix: '',
      asset: {
        customer_id: ActiveCustomer?.id ?? 0,
        description: '',
        latitude: 0,
        longitude: 0,
        parent_ids: [],
        tag: '',
        time_zone: '',
        type_id: 0,
      },
    },
    resolver: yupResolver(CreateAssetTemplateInstanceSchema),
  });
  const units_group_id = watch('units_group_id');
  const dispatch = useDispatch<ThunkDispatch<RootState, any, any>>();
  useEffect(() => {
    if (selectedTemplate) {
      setMeasurements((selectedTemplate?.measurements as measurementSchemaDataDTO[]) ?? []);
    }
  }, [selectedTemplate]);
  useEffect(() => {
    if (selectedTemplate && units_group_id && measurements.length > 0) {
      const measuresList = (selectedTemplate.measurements ?? [])
        .filter((measure) => measure.type_id && measure.type_id !== null)
        .map(async (measure) => {
          const { data, error } = await dispatch(
            measuresApi.endpoints.getUnitOMeasureWithMeasureType.initiate({
              measurementType: measure.type_id as number,
              unitOfGroup: units_group_id,
            }),
          );
          if (error) {
            return measure;
          }
          if (data) {
            return {
              ...measure,
              unit_of_measure_id: data.items.find((item) => item.is_measurement_type_default)?.id,
            };
          }
          return measure;
        });
      Promise.all(measuresList).then((data) => {
        setMeasurements(data as measurementSchemaDataDTO[]);
      });
    }
  }, [units_group_id, selectedTemplate]);

  const [alertMessage, setAlertMessage] = useState<AlertMessage | undefined>(undefined);

  const locationsListOption = useMemo(
    () => mapListToOptions(locationsList?.items ?? []),
    [locationsList],
  );
  const measurementTypeListOptions = useMemo(
    () => mapListToOptions(measurementTypeList ?? []),
    [measurementTypeList],
  );
  const valueTypeOptions = useMemo(() => mapListToOptions(valueTypeList ?? []), [valueTypeList]);

  const unitOfGroupsListOptions = useMemo(
    () => mapListToOptions(unitOfGroupsList?.items ?? []),
    [unitOfGroupsList],
  );
  const datasourceOptions = useMemo(
    () =>
      mapListToOptions(
        datasourceList?.items.filter((source) => source.name !== 'TimeVaryingFactor') ?? [],
      ),
    [datasourceList],
  );
  useEffect(() => {
    if (isSuccessfullBackOffieAssetTypes && assetTypeListData) {
      setAssetTypesWithPath(
        assetTypePathMapperFilterTemplates(
          assetTypeListData.map((item) => ({
            ...item,
            name: `${item.name}`,
            id: item.id,
          })),
        ),
      );
    }
  }, [assetTypeListData, isSuccessfullBackOffieAssetTypes]);
  const dataTypesListOptions = useMemo(() => mapListToOptions(dataTypeList ?? []), [dataTypeList]);

  const [activeStep, setActiveStep] = useState(0);
  const steps = getSteps();
  const isLastStep = activeStep === steps.length - 1;

  const handleBack = () => {
    setActiveStep((prevActiveStep) => prevActiveStep - 1);
    if (activeStep === 1) {
      setSelectedTemplet(null);
    }
  };

  const handleReset = () => {
    setActiveStep(0);
  };
  const [currentMeausreIndex, setCurrentMeasureIndex] = useState<number>(-1);
  const [isEdit, setIsEdit] = useState<boolean>(false);
  const [isFirst, setIsFirst] = useState<boolean>(true);

  const { data: assetTypeMetrics } = useGetAllBackOfficeAssetTypesMetricsQuery(
    {
      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      //@ts-ignore
      assetId: selectedTemplate?.asset_type_id ? selectedTemplate?.asset_type_id.toString() : '',
    },
    {
      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      //@ts-ignore
      skip: selectedTemplate?.asset_type_id === undefined,
      refetchOnMountOrArgChange: true,
    },
  );
  const assetTypeMetricsListOptions = useMemo(
    () => mapListToOptions(assetTypeMetrics?.items ?? []),
    [assetTypeMetrics],
  );

  useEffect(() => {
    if (asset) {
      setAssetTemplateValue('asset.parent_ids', asset.id ? [asset.id.toString()] : []);
    }
  }, [asset]);
  const {
    control: measurementsControl,
    getValues: getMeasureValues,
    handleSubmit: measurementSubmit,
    formState: { errors: measureErrors },
    setValue: setMeasureValue,
    reset: resetMeasure,
    watch: watchMeasure,
  } = useForm<measurementSchemaDataDTO>({
    defaultValues:
      currentMeausreIndex >= 0 && measurements.length > 0
        ? measurements[currentMeausreIndex]
        : undefined,
    resolver: yupResolver(MeasurementSchemaForInstance),
  });

  const measure_Type_id = watchMeasure('type_id');
  const { data, isFetching: isLoadingUnits } = useGetUnitOMeasureWithMeasureTypeQuery(
    {
      measurementType: measure_Type_id,
      unitOfGroup: units_group_id,
    },
    {
      skip:
        units_group_id === 0 ||
        measure_Type_id === 0 ||
        measure_Type_id === undefined ||
        units_group_id === undefined,
    },
  );
  useEffect(() => {
    if (data) {
      const defaultUOM = data.items.find((item) => item.is_measurement_type_default);
      if (
        defaultUOM &&
        (getMeasureValues('unit_of_measure_id') === null ||
          getMeasureValues('unit_of_measure_id') === undefined)
      ) {
        setMeasureValue('unit_of_measure_id', defaultUOM?.id);
      }
    }
  }, [data, currentMeausreIndex]);
  // const customerId = watch('asset.customer_id');
  const customerId = ActiveCustomer?.id;
  const { data: customerAssets } = useGetAllAssetQuery(
    {
      customerId: customerId ?? 0,
      parentIds: [],
    },
    {
      skip: customerId === 0 || customerId === undefined,
      refetchOnMountOrArgChange: true,
    },
  );
  const handleNext = handleSubmit(() => {
    if (isLastStep) {
      if (getAssetTemplateValues('asset').parent_ids.length > 0) {
        const mapperPath = assetsPathMapper(customerAssets ?? []);
        const idToMap: { [key: number]: string } = {};
        const data = getAssetTemplateValues('asset')
          .parent_ids.map((id) => {
            const label =
              mapperPath?.find((path) => path.id === Number(id))?.label.replaceAll(' > ', '\\') +
                '\\' +
                getAssetTemplateValues('asset').tag !==
              undefined
                ? getAssetTemplateValues('asset').tag
                : '';
            +'\\';
            return {
              id,
              label,
            };
          })
          .reduce((acc, { id, label }) => {
            if (label) {
              acc[Number(id)] = label;
            }
            return acc;
          }, idToMap);
        Object.entries(idToMap).map(([id, label]) => {
          createAssetTemplateInstance({
            // eslint-disable-next-line @typescript-eslint/ban-ts-comment
            //@ts-ignore
            assetTemplateId: selectedTemplate?.id ?? 0,
            // eslint-disable-next-line @typescript-eslint/ban-ts-comment
            //@ts-ignore
            assetTypeId: selectedTemplate?.asset_type_id ?? 0,
            createAssetTemplateData: {
              assetTemplateInstance: {
                ...getAssetTemplateValues(),
                asset: {
                  ...getAssetTemplateValues('asset'),
                  parent_ids: [id],
                },
              },
              measurement: measurements.map((measure) => {
                return {
                  ...measure,
                  tag: (label ? label + '\\' : '') + measure.tag,
                };
              }),
            },
          });
        });
      } else {
        createAssetTemplateInstance({
          // eslint-disable-next-line @typescript-eslint/ban-ts-comment
          //@ts-ignore
          assetTemplateId: selectedTemplate?.id ?? 0,
          // eslint-disable-next-line @typescript-eslint/ban-ts-comment
          //@ts-ignore
          assetTypeId: selectedTemplate?.asset_type_id ?? 0,
          createAssetTemplateData: {
            assetTemplateInstance: getAssetTemplateValues(),
            measurement: measurements,
          },
        });
      }
    } else {
      if (activeStep === 1) {
        setMeasurements(
          measurements.map((measure) => {
            const { tag, metric_id, ...rest } = measure;
            return {
              tag:
                (getAssetTemplateValues()?.prefix ?? '') +
                assetTypeMetricsListOptions.find((item) => item.id === metric_id.toString())?.label,
              metric_id,
              ...rest,
            };
          }),
        );
      }
      setActiveStep((prevActiveStep) => prevActiveStep + 1);
    }
  });
  const handleMeasure = measurementSubmit((data) => {
    resetMeasure();
    if (!isEdit) {
      setIsFirst(false);
      setMeasurements(
        (currentMeasurements) =>
          [...currentMeasurements, { ...data }] as measurementSchemaDataDTO[],
      );
    } else {
      setIsEdit(false);
      setMeasurements((currentMeasurements) => {
        return currentMeasurements.map((item, idx) => {
          if (idx === currentMeausreIndex) {
            return { ...item, ...data };
          }
          return item;
        }) as measurementSchemaDataDTO[];
      });
    }
    setCurrentMeasureIndex(-1);
  });
  useEffect(() => {
    if (isSuccess && assetTemplateInstanceData) {
      setAlertMessage({
        message: `Asset Template instance created successfully!`,
        severity: 'success',
      });
      setSelectedTemplet(null);
      setMeasurements([]);
      setActiveStep(0);
      setIsFirst(true);
      setCurrentMeasureIndex(-1);
      setIsEdit(false);
      resetMeasure();
      resetAssetTemplate();
    }

    if (isError && error) {
      if (error instanceof InvalidInputException) {
        setAlertMessage({ message: error.message, severity: 'error' });
      } else {
        setAlertMessage({ message: 'Server error', severity: 'error' });
      }
    }
    // reset states to initial values
  }, [assetTemplateInstanceData, error, isError, isSuccess]);
  useEffect(() => {
    // reset alert message after 3 seconds if alert message is for success
    if (alertMessage?.severity === 'success') {
      const timer = setTimeout(() => {
        setAlertMessage(undefined);
      }, 3000);
      return () => clearTimeout(timer);
    }
  }, [alertMessage]);
  return (
    <Box sx={{ width: '100%' }}>
      <Stepper
        activeStep={activeStep}
        alternativeLabel
        sx={{ position: 'sticky', top: 0, backgroundColor: 'white', zIndex: 1000, padding: '10px' }}
      >
        {steps.map((label) => (
          <Step key={label}>
            <StepLabel>{label}</StepLabel>
          </Step>
        ))}
      </Stepper>
      <Box sx={{ width: '100%' }}>
        {activeStep === steps.length ? (
          <div>
            <Typography sx={{ mt: 2, mb: 1 }}>All steps completed</Typography>
            <Button onClick={handleReset}>Reset</Button>
          </div>
        ) : (
          <Box sx={{ width: '100%' }}>
            <Box sx={{ mt: 1.5 }}>
              {activeStep === 0 ? (
                <>
                  <SearchAssetTemplates
                    asset={asset}
                    selectedTemplate={selectedTemplate}
                    setSelectedTemplet={(value) => {
                      if (value) {
                        setSelectedTemplet(value);
                        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
                        //@ts-ignore
                        setAssetTemplateValue('asset.type_id', value.asset_type_id ?? 0);
                      }
                      if (value === null) {
                        setSelectedTemplet(value);
                      }
                      // setActiveStep(1);
                    }}
                  />
                  <Box
                    sx={{
                      width: '100%',
                      display: 'flex',
                      justifyContent: 'end',
                      pt: 2,
                      position: 'sticky',
                      bottom: 0,
                      backgroundColor: 'white',
                      zIndex: 1000,
                      padding: '10px',
                    }}
                  >
                    <Button
                      onClick={() => {
                        setActiveStep(1);
                      }}
                      disabled={isLoading || selectedTemplate === null}
                    >
                      Next
                    </Button>
                  </Box>
                </>
              ) : null}
              {activeStep === 1 ? (
                <>
                  <form
                    noValidate
                    onSubmit={handleSubmit(async (data) => {
                      try {
                        // await createAssetTemplate(data);
                        // reset();
                      } catch (err) {
                        console.log(err);
                        // Handle the error here
                      }
                    })}
                  >
                    <ControlledAutocomplete
                      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
                      //@ts-ignore
                      control={control}
                      fieldName={`units_group_id`}
                      label="Unit Of Group"
                      options={unitOfGroupsListOptions}
                      required
                    />
                    {/* <ControlledTextField
                      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
                      //@ts-ignore
                      control={control}
                      fieldName="prefix"
                      label="Prefix"
                      required
                    /> */}
                    {/* <ControlledAutocomplete
                      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
                      //@ts-ignore
                      control={control}
                      fieldName={`asset.customer_id`}
                      label="Customer"
                      options={customerListOptions}
                      required
                    /> */}
                    <Controller
                      name="asset.tag"
                      control={control}
                      render={({ field: { onChange, onBlur, value }, fieldState }) => (
                        <TextField
                          error={!!fieldState.error}
                          helperText={fieldState.error?.message}
                          onChange={onChange}
                          onBlur={onBlur}
                          value={value}
                          label="Asset Tag"
                          variant="outlined"
                          margin="normal"
                          fullWidth
                          required
                        />
                      )}
                    />
                    <ControlledAutocomplete
                      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
                      //@ts-ignore
                      control={control}
                      fieldName={`asset.type_id`}
                      disabled={getAssetTemplateValues()?.asset?.type_id !== 0}
                      label="Asset Type"
                      options={assetTypesWithPath.map((asset) => {
                        return {
                          id: asset.value.toString(),
                          label: asset.label,
                        };
                      })}
                      required
                    />
                    <Controller
                      name="asset.parent_ids"
                      control={control}
                      render={({ field }) => (
                        <Autocomplete
                          sx={{
                            mt: 2,
                          }}
                          fullWidth
                          multiple
                          defaultValue={
                            customerAssets
                              ?.filter((item) => item.id === asset?.id)
                              .map((item) => ({
                                id: item.id.toString(),
                                value: item.id,
                                label: item.tag,
                              })) ?? []
                          }
                          options={
                            customerAssets
                              ? [
                                  ...assetsPathMapper(customerAssets ?? []),
                                  ...customerAssets
                                    .filter((item) => item.parentIds.length === 0)
                                    .map((item) => ({
                                      id: item.id.toString(),
                                      value: item.id,
                                      label: item.tag,
                                    })),
                                ]
                              : []
                          }
                          getOptionLabel={(option) => option.label}
                          onChange={(_, value) => field.onChange(value.map((item) => item.id))}
                          renderInput={(params) => <TextField {...params} label="Parent Assets" />}
                          isOptionEqualToValue={(option, value) => option.id === value.id}
                        />
                      )}
                    />
                    <Controller
                      name="asset.time_zone"
                      control={control}
                      render={({ field: { onChange, onBlur, value }, fieldState }) => (
                        <Autocomplete
                          options={timeZonesList ?? []}
                          renderInput={(params) => (
                            <TextField
                              {...params}
                              label="Select a time zone"
                              error={!!fieldState.error}
                              helperText={fieldState.error?.message}
                            />
                          )}
                          onChange={(_, value) => onChange(value)}
                          onBlur={onBlur}
                          value={value ?? null}
                          sx={{ mt: 2, mb: 1 }}
                          fullWidth
                        />
                      )}
                    />
                    <Controller
                      name="asset.latitude"
                      control={control}
                      render={({ field: { onChange, onBlur, value }, fieldState, formState }) => (
                        <TextField
                          error={!!fieldState.error}
                          helperText={fieldState.error?.message}
                          onChange={(e) => {
                            onChange(e.target.value === '' ? null : e.target.value);
                            if (formState.isSubmitted) {
                              trigger('asset.longitude');
                            }
                          }}
                          onBlur={onBlur}
                          type="number"
                          value={value ?? ''}
                          label="Latitude"
                          variant="outlined"
                          margin="normal"
                          fullWidth
                        />
                      )}
                    />
                    <Controller
                      name="asset.longitude"
                      control={control}
                      render={({ field: { onChange, onBlur, value }, fieldState, formState }) => (
                        <TextField
                          error={!!fieldState.error}
                          helperText={fieldState.error?.message}
                          onChange={(e) => {
                            onChange(e.target.value === '' ? null : e.target.value);
                            if (formState.isSubmitted) {
                              trigger('asset.latitude');
                            }
                          }}
                          onBlur={onBlur}
                          type="number"
                          value={value ?? ''}
                          label="Longitude"
                          variant="outlined"
                          margin="normal"
                          fullWidth
                        />
                      )}
                    />
                    <ControlledTextField
                      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
                      //@ts-ignore
                      control={control}
                      fieldName="asset.description"
                      label="Description"
                      multiline
                    />
                  </form>
                </>
              ) : null}
              {activeStep === 2 ? (
                <>
                  <AssetInstanceMeasureTable
                    fields={measurements}
                    remove={(i) => {
                      setMeasurements(measurements.filter((_, idx) => idx !== i));
                      setCurrentMeasureIndex(-1);
                      setIsEdit(false);
                    }}
                    setCurrentMeasureIndex={(i) => {
                      setCurrentMeasureIndex(i);
                      setIsEdit(true);
                    }}
                    valueTypeOptions={valueTypeOptions}
                    datasourceOptions={datasourceOptions}
                    locationsListOption={locationsListOption}
                    dataTypesListOptions={dataTypesListOptions}
                    measurementTypeListOptions={measurementTypeListOptions}
                    assetTypeMetricsListOptions={assetTypeMetricsListOptions}
                    setMeasureValue={setMeasureValue as UseFormSetValue<any>}
                    isInstance={true}
                    units_group_id={units_group_id}
                    isEdit={isEdit}
                    currentMeausreIndex={currentMeausreIndex}
                    EditForm={
                      <form onSubmit={(e) => e.preventDefault()}>
                        <Card>
                          <CardHeader
                            title={
                              isEdit
                                ? 'Edit Measurement #' + (currentMeausreIndex + 1)
                                : 'Add Measurement'
                            }
                          />
                          <CardContent>
                            <Box sx={{ display: 'flex', flexDirection: 'row', mb: 2, gap: 4 }}>
                              <Grid container spacing={2}>
                                <Grid item xs={12} sm={4}>
                                  <Controller
                                    name={`tag`}
                                    control={measurementsControl}
                                    render={({ field }) => (
                                      <TextField
                                        sx={{
                                          mt: 2,
                                        }}
                                        defaultValue={getMeasureValues()?.tag}
                                        value={getMeasureValues()?.tag}
                                        label="Tag"
                                        error={!!measureErrors?.tag}
                                        helperText={measureErrors?.tag?.message || ''}
                                        fullWidth
                                        onChange={(e) => field.onChange(e.target.value)}
                                        required
                                      />
                                    )}
                                  />
                                </Grid>
                                <Grid item xs={12} sm={4}>
                                  <ControlledAutocomplete
                                    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
                                    //@ts-ignore
                                    control={measurementsControl}
                                    value={getMeasureValues().type_id ?? null}
                                    fieldName={`type_id`}
                                    loading
                                    label="Type Id"
                                    options={measurementTypeListOptions}
                                    required
                                  />
                                </Grid>
                                <Grid item xs={12} sm={4}>
                                  <ControlledAutocomplete
                                    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
                                    //@ts-ignore
                                    control={measurementsControl}
                                    value={getMeasureValues()?.data_type_id}
                                    loading
                                    fieldName={`data_type_id`}
                                    label="Data Type"
                                    options={dataTypesListOptions}
                                    required
                                  />
                                </Grid>
                                <Grid item xs={12} sm={4}>
                                  <ControlledAutocomplete
                                    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
                                    //@ts-ignore
                                    control={measurementsControl}
                                    value={getMeasureValues()?.value_type_id}
                                    fieldName={`value_type_id`}
                                    label="Select value type"
                                    loading
                                    options={valueTypeOptions}
                                    required
                                  />
                                </Grid>
                                <Grid item xs={12} sm={4}>
                                  <ControlledAutocomplete
                                    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
                                    //@ts-ignore
                                    control={measurementsControl}
                                    value={getMeasureValues()?.metric_id}
                                    fieldName={`metric_id`}
                                    label="Select Metric"
                                    loading
                                    options={assetTypeMetricsListOptions}
                                    required
                                  />
                                </Grid>

                                <Grid item xs={12} sm={4}>
                                  <Controller
                                    name={`description`}
                                    control={measurementsControl}
                                    render={({ field }) => (
                                      <TextField
                                        sx={{
                                          mt: 2,
                                        }}
                                        defaultValue={getMeasureValues()?.description}
                                        value={getMeasureValues()?.description}
                                        label="Description"
                                        error={!!measureErrors?.description}
                                        helperText={measureErrors?.description?.message || ''}
                                        fullWidth
                                        onChange={(e) => field.onChange(e.target.value)}
                                      />
                                    )}
                                  />
                                </Grid>
                                <Grid item xs={12} sm={4}>
                                  <ControlledAutocomplete
                                    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
                                    //@ts-ignore
                                    control={measurementsControl}
                                    value={getMeasureValues()?.location_id}
                                    fieldName={`location_id`}
                                    label="Location"
                                    options={locationsListOption}
                                  />
                                </Grid>
                                <Grid item xs={12} sm={4}>
                                  <ControlledAutocomplete
                                    control={measurementsControl}
                                    value={getMeasureValues()?.unit_of_measure_id}
                                    fieldName={`unit_of_measure_id`}
                                    label="Unit Of Measure"
                                    loading={isLoadingUnits}
                                    options={
                                      data?.items.map((item) => ({
                                        id: item.id.toString(),
                                        label: item.name,
                                      })) ?? []
                                    }
                                  />
                                </Grid>
                                <Grid item xs={12} sm={4}>
                                  <ControlledAutocomplete
                                    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
                                    //@ts-ignore
                                    control={measurementsControl}
                                    value={getMeasureValues()?.datasource_id}
                                    fieldName={`datasource_id`}
                                    label="Data Source"
                                    options={datasourceOptions}
                                  />
                                </Grid>
                                <Grid item xs={12} sm={4}>
                                  <Controller
                                    name={`meter_factor`}
                                    control={measurementsControl}
                                    render={({ field }) => (
                                      <TextField
                                        sx={{
                                          mt: 2,
                                        }}
                                        {...field}
                                        label="Meter Factor"
                                        defaultValue={getMeasureValues()?.meter_factor}
                                        type="number"
                                        error={!!measureErrors.meter_factor}
                                        helperText={measureErrors.meter_factor?.message || ''}
                                        fullWidth
                                        onChange={(e) => field.onChange(e.target.value)}
                                      />
                                    )}
                                  />
                                </Grid>
                              </Grid>
                            </Box>
                          </CardContent>
                          <Button
                            type="submit"
                            sx={{ ml: 3, mb: 3 }}
                            variant="contained"
                            onClick={handleMeasure}
                          >
                            {isEdit ? 'Update' : 'Add'}
                          </Button>
                          <Button
                            type="submit"
                            sx={{ ml: 3, mb: 3 }}
                            variant="outlined"
                            onClick={() => {
                              setCurrentMeasureIndex(-1);
                              setIsEdit(false);
                            }}
                          >
                            Cancel
                          </Button>
                        </Card>
                      </form>
                    }
                  />
                  {/* <Typography color="error">
                    {isValidMeasures.length > 0
                      ? 'Invalid Tag Names : ' +
                        isValidMeasures.map((measure) => measure.tag).join(', ') +
                        ' (Tag names should contain only alphabets)'
                      : ''}
                  </Typography> */}
                </>
              ) : null}
            </Box>
            {activeStep !== 0 && (
              <Box
                sx={{
                  display: 'flex',
                  flexDirection: 'row',
                  pt: 2,
                  position: 'sticky',
                  bottom: 0,
                  backgroundColor: 'white',
                  zIndex: 1000,
                  padding: '10px',
                }}
              >
                {/* sx={{ position: 'sticky', top: 0, backgroundColor: 'white', zIndex: 1000, padding: '10px' }} */}
                <Button
                  color="inherit"
                  disabled={activeStep === 0}
                  onClick={handleBack}
                  sx={{ mr: 1 }}
                >
                  Back
                </Button>
                <Box sx={{ flex: '1 1 auto' }} />

                <Button onClick={handleNext} disabled={isLoading || activeStep > steps.length}>
                  {isLoading ? (
                    <>Save & Finish</>
                  ) : (
                    <>{activeStep === steps.length - 1 ? 'Save & Finish' : 'Next'}</>
                  )}
                </Button>
              </Box>
            )}
          </Box>
        )}
      </Box>
      {alertMessage && (
        <Alert severity={alertMessage.severity} sx={{ mt: 3 }}>
          {alertMessage.message}
        </Alert>
      )}
    </Box>
  );
}
