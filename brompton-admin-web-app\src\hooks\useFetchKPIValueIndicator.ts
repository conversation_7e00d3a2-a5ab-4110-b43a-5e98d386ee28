import { DataWidget, KPIValueIndicator } from '~/types/widgets';
import { useEffect, useState } from 'react';
import { SingleScatterTimeSeriesData } from '~/types/timeseries';
import { AssetMeasurementDetails } from '~/types/measures';
import { UnitOfMeasure } from '~/measurements/domain/types';
import { useFetchMeasuresTsData } from './useFetchMeasuresTsData';
import { useGetMeasuresTsData } from './useGetMeasuresTsData';

type TrendResult = {
  tsData: SingleScatterTimeSeriesData;
  measureData: AssetMeasurementDetails;
  unitOfMeasures: UnitOfMeasure[];
};

type KPIData = {
  lastValue: number;
  differencePercentage: number;
};
const transformKPIValueIndicator = (results: TrendResult[], state: KPIValueIndicator): KPIData => {
  if (results.length === 0) {
    return {
      lastValue: 0,
      differencePercentage: 0,
    };
  }
  const res = results[0]?.tsData?.['ts,val']?.slice(-2);
  if (results.length === 0 || !results[0]?.tsData?.['ts,val'] || res.length < 1) {
    return {
      lastValue: 0,
      differencePercentage: 0,
    };
  }
  const value: number = res[res?.length - 1][1];
  const referenceValue = res[res?.length - 2]?.[1];
  const differencePercentage = ((referenceValue - value) / referenceValue) * 100;

  return {
    lastValue: value,
    differencePercentage,
  };
};
export function useFetchKPIValueIndicator(widgetId: string, state: KPIValueIndicator) {
  const selectedDbMeasureId = state.selectedDbMeasureId;

  const [selectedTitles, setSelectedTitles] = useState<string[]>([]);
  // useEffect(() => {
  //   if (selectedDbMeasureId !== '') setSelectedTitles([selectedDbMeasureId]);
  // }, [selectedDbMeasureId]);

  useEffect(() => {
    if (
      state.mode === 'dashboard' &&
      state.assetMeasure.assetId !== '' &&
      state.assetMeasure.measureId.some((measure) => measure !== '')
    ) {
      setSelectedTitles(state.assetMeasure.measureId);
    }
    if (state.mode === 'template') {
      if (state.selectedDbMeasureId !== '') {
        setSelectedTitles([state.selectedDbMeasureId]);
      } else {
        setSelectedTitles([]);
      }
    }
  }, [state.assetMeasure, state.selectedDbMeasureId]);

  const [allDataFetched, setAllDataFetched] = useState<{
    isLoading: boolean;
    isError: boolean;
    data: null | KPIData;
  }>({
    data: null,
    isLoading: true,
    isError: false,
  });

  const [chartResults, setChartResults] = useState<TrendResult[] | undefined>(undefined);
  // const { data: measureData, isLoading: isMeasureDataLoading } = useFetchMeasuresTsData({
  //   selectedTitles,
  //   dataFetchSettings: state,
  // });
  const { data: measureData, isLoading: isMeasureDataLoading } = useGetMeasuresTsData({
    selectedTitles,
    dataFetchSettings: state,
    assetMeasure: [state.assetMeasure],
  });
  useEffect(() => {
    if (isMeasureDataLoading) {
      setChartResults(undefined);
      setAllDataFetched((prev) => {
        return {
          ...prev,
          isLoading: true,
          isError: false,
        };
      });
    } else if (measureData) {
      const filteredList = measureData.filter(({ isError }) => !isError);
      setChartResults(filteredList);
    } else {
      setChartResults(undefined);
      setAllDataFetched((prev) => {
        return {
          ...prev,
          isError: true,
          isLoading: false,
        };
      });
    }
  }, [isMeasureDataLoading, measureData]);

  useEffect(() => {
    if (chartResults) {
      const colorBoxData = transformKPIValueIndicator(chartResults, state);
      setAllDataFetched({
        ...allDataFetched,
        data: colorBoxData,
        isLoading: false,
      });
    }
  }, [
    chartResults,
    state.title.isVisible,
    state.title.value,
    state.overrideGlobalSettings,
    state.startDate,
    state.endDate,
    state.timeRange,
    state.globalSamplePeriod,
    state,
  ]);
  return allDataFetched;
}
