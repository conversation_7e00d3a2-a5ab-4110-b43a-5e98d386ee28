import { yupResolver } from '@hookform/resolvers/yup';
import { VisibilityOff } from '@mui/icons-material';
import AccountCircle from '@mui/icons-material/AccountCircle';
import Lock from '@mui/icons-material/Lock';
import VisibilityIcon from '@mui/icons-material/Visibility';
import { Alert, Box, Button, InputAdornment, TextField } from '@mui/material';
import Link from 'next/link';
import { useState } from 'react';
import { Controller, useForm } from 'react-hook-form';
import * as yup from 'yup';
const loginSchema = yup.object({
  username: yup.string().required('Please enter username'),
  password: yup.string().required('Please enter password  '),
});

export type LoginFormData = yup.InferType<typeof loginSchema>;

type SignInFormProps = {
  loading: boolean;
  errorMessage: string | undefined;
  onValidSubmit: (data: LoginFormData) => unknown;
};

export default function SignInForm({
  loading,
  errorMessage,
  onValidSubmit,
}: SignInFormProps): JSX.Element {
  const [showPassword, setShowPassword] = useState<boolean>(false);
  const { control, handleSubmit } = useForm<LoginFormData>({
    defaultValues: {
      username: '',
      password: '',
    },
    resolver: yupResolver(loginSchema),
  });

  return (
    <form onSubmit={handleSubmit(onValidSubmit)} noValidate style={{ marginTop: 100 }}>
      <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
        <Controller
          name="username"
          control={control}
          render={({ field: { onChange, onBlur, value }, fieldState }) => (
            <TextField
              error={!!fieldState.error}
              helperText={fieldState.error?.message}
              onChange={onChange}
              onBlur={onBlur}
              value={value}
              autoComplete={'username'}
              label="Username"
              variant="outlined"
              margin="normal"
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <AccountCircle />
                  </InputAdornment>
                ),
              }}
              disabled={loading}
              fullWidth
              required
            />
          )}
        />
        <Controller
          name="password"
          control={control}
          render={({ field: { onChange, onBlur, value }, fieldState }) => (
            <TextField
              error={!!fieldState.error}
              helperText={fieldState.error?.message}
              onChange={onChange}
              onBlur={onBlur}
              value={value}
              autoComplete={'current-password'}
              label="Password"
              variant="outlined"
              type={!showPassword ? 'password' : 'text'}
              margin="normal"
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <Lock />
                  </InputAdornment>
                ),
                endAdornment: (
                  <InputAdornment
                    position="end"
                    sx={{ cursor: 'pointer' }}
                    onClick={() => {
                      setShowPassword(!showPassword);
                    }}
                  >
                    {!showPassword ? <VisibilityIcon /> : <VisibilityOff />}
                  </InputAdornment>
                ),
              }}
              disabled={loading}
              fullWidth
              required
            />
          )}
        />
        <Link href="/forgot-password" style={{ textDecoration: 'none', width: '100%' }}>
          Forgot password?
        </Link>
        <Button
          type="submit"
          variant="contained"
          size="large"
          sx={{ mt: 2, width: 200 }}
          disabled={loading}
        >
          Log in
        </Button>
        {errorMessage && (
          <Alert severity="error" sx={{ mt: 3 }}>
            {errorMessage}
          </Alert>
        )}
      </Box>
    </form>
  );
}
