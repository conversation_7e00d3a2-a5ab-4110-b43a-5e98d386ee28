import { Box, MenuItem, OutlinedInput, Select, Stack } from '@mui/material';
import FormControl from '@mui/material/FormControl';
import { SelectChangeEvent } from '@mui/material/Select';
import { GroupBy } from '~/components/common/GroupBy';
import {
  HeatmapChartWidget,
  HeatMapPastelColor,
  HeatMapPastelColorOptions,
  setSingleMeasureWidgetSettings,
} from '~/types/widgets';
import DataWidgetSettingsContainer from '../common/DataWidgetSettingsContainer/DataWidgetSettingsContainer';
import SingleMeasureSelect from '../common/SingleMeasureSelect';

type Props = {
  selectedDbMeasureIdToName: Record<string, string>;
  heatmapChartSettings: HeatmapChartWidget;
  handleHeatmapChartSettingsUpdate: (
    value: ((prevState: HeatmapChartWidget) => HeatmapChartWidget) | HeatmapChartWidget,
  ) => void;
};

export function HeatmapChartSettings({
  selectedDbMeasureIdToName,
  heatmapChartSettings,
  handleHeatmapChartSettingsUpdate,
}: Props) {
  const { groupY, groupX, pastelColor } = heatmapChartSettings;

  const handleGroupByY = (e: SelectChangeEvent) => {
    handleHeatmapChartSettingsUpdate({
      ...heatmapChartSettings,
      groupY: e.target.value,
    });
  };

  const handleGroupByX = (e: SelectChangeEvent) => {
    handleHeatmapChartSettingsUpdate({
      ...heatmapChartSettings,
      groupX: e.target.value,
    });
  };

  const handlePastelColor = (e: SelectChangeEvent<string>) => {
    handleHeatmapChartSettingsUpdate({
      ...heatmapChartSettings,
      pastelColor: e.target.value as HeatMapPastelColor,
    });
  };
  return (
    <Stack display="flex" gap={1}>
      <DataWidgetSettingsContainer
        hideSettings={{
          realtime: true,
        }}
        settings={heatmapChartSettings}
        setSettings={handleHeatmapChartSettingsUpdate}
        dataTabChildren={
          <>
            <Box sx={{ pt: 1 }}>
              <SingleMeasureSelect
                id={'heatmap-series'}
                settings={heatmapChartSettings}
                setSettings={handleHeatmapChartSettingsUpdate as setSingleMeasureWidgetSettings}
              />
            </Box>
            <FormControl sx={{ mt: 1 }} fullWidth>
              <GroupBy
                id={'heatmap-x'}
                label={'Group By(x)'}
                x={groupX}
                handleUpdate={handleGroupByX}
              />
            </FormControl>
            <FormControl sx={{ mt: 1 }} fullWidth>
              <GroupBy
                id={'heatmap-y'}
                label={'Group By(y)'}
                x={groupY}
                handleUpdate={handleGroupByY}
              />
            </FormControl>
          </>
        }
        feelTabChidren={
          <FormControl sx={{ mt: 1 }} fullWidth>
            <Select
              labelId={'pastel-color-'}
              id={'pastel-color'}
              value={pastelColor}
              label={'Pastel Color'}
              fullWidth
              input={
                <OutlinedInput
                  label={'Pastel Color'}
                  sx={{
                    p: 0.5,
                    '& legend': {
                      maxWidth: '100%',
                      height: 'fit-content',
                      '& span': {
                        opacity: 1,
                      },
                    },
                  }}
                />
              }
              onChange={handlePastelColor}
            >
              {HeatMapPastelColorOptions.map((option) => (
                <MenuItem key={option} value={option}>
                  {option}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        }
      ></DataWidgetSettingsContainer>
    </Stack>
  );
}
