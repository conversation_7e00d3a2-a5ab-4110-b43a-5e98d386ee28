import { test, expect } from '@playwright/test';

test('test', async ({ page }) => {
  await page.goto('https://test.pivotol.ai/login');
  await page.getByLabel('Username *').click();
  await page.getByLabel('Username *').fill('test');
  await page.getByLabel('Password *').click();
  await page.getByLabel('Password *').fill('Br0mpt0n!0T');
  await page.getByRole('button', { name: 'Log in' }).click();
  await page.locator('svg path[d="M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z"]').click();
  await page.waitForTimeout(5000);
  await page.getByRole('menuitem', { name: 'Dynamic Charts' }).click();
  await page.waitForTimeout(3000);
  await page.getByLabel('Time Range').click();
  await page.getByRole('button', { name: 'Last 90 days' }).click();
  await page.getByRole('button', { name: 'Apply' }).click();
  await page.getByLabel('Minutes').click();
  await page.getByRole('option', { name: '1 Hour' }).click();
  await page.getByLabel('TWA').click();
  await page.getByRole('option', { name: 'Average' }).click();
  await page.getByLabel('Select Asset').click();
  await page.getByRole('option', { name: 'Brenes > MAINPANEL_MQTT', exact: true }).click();
  await page.getByLabel('Select Measure').click();
  await page.getByRole('option', { name: 'Averagecurrent', exact: true }).click();
  await page.getByRole('checkbox').check();
  await page.getByRole('button', { name: 'Submit' }).click();
  await page.waitForTimeout(3000);
  await page.close();
});
