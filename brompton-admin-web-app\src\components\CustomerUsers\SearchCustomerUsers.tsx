import ModeEditIcon from '@mui/icons-material/ModeEdit';
import {
  Box,
  Button,
  Card,
  Chip,
  Grid,
  MenuItem,
  OutlinedInput,
  Select,
  Stack,
  TextField,
  Tooltip,
} from '@mui/material';
import { GridColDef, GridRenderCellParams, GridTreeNodeWithRender } from '@mui/x-data-grid';
import { Dispatch, SetStateAction, useEffect, useState } from 'react';
import { useSelector } from 'react-redux';
import { CustomError } from '~/errors/CustomerErrorResponse';
import { useHasAdminAccess } from '~/hooks/useHasAdminAccess';
import { useRolePermission } from '~/hooks/useRolePermission';
import {
  useEditCustomerUsersByIdMutation,
  useGetCustomerUsersByIdMutation,
  useGetCustomersQuery,
} from '~/redux/api/customersApi';
import { useCreateScopedUserMutation } from '~/redux/api/usersApi';
import { getActiveCustomer } from '~/redux/selectors/customerSelectors';
import { AlertMessage } from '~/shared/forms/types';
import { AlertSnackbar } from '~/shared/snackbars/components/AlertSnackbar';
import { useSnackbar } from '~/shared/snackbars/snackbar-hooks';
import { Customer } from '~/types/customers';
import { UserDto } from '~/types/users';
import CustomDialog from '../common/CustomDialog';
import DataTable from '../common/DataTable/DataTable';
import Loader from '../common/Loader';
import NewUserForm from '../user/NewUserForm';
import EditUserPage from './EditUser';
const roles = ['Admin', 'User', 'Power_user'];
type SearchCustomerUsersProps = { addUser: boolean; setAddUser: Dispatch<SetStateAction<boolean>> };
const SearchCustomerUsers = ({ addUser, setAddUser }: SearchCustomerUsersProps) => {
  const ActiveCustomer = useSelector(getActiveCustomer);
  const { globalAdmin, admin } = useHasAdminAccess();
  const [alertMessage, setAlertMessage] = useState<AlertMessage | undefined>(undefined);
  const [
    createScopedUser,
    { data: newUserData, isSuccess, error, isError, isLoading: createLoading },
  ] = useCreateScopedUserMutation();
  const { hasPermission } = useRolePermission();
  const { data } = useGetCustomersQuery({});
  const [customers, setCustomers] = useState<Customer[] | null>(null);
  const [customer, setCustomer] = useState<Customer | null>(null);
  const [showFilter, setShowFilter] = useState<boolean>(false);
  const [searchRoles, setSearchRoles] = useState<string[]>([]);
  const [name, setName] = useState<string>('');
  const [username, setUsername] = useState<string>('');
  const [email, setEmail] = useState<string>('');
  const [refetchData, setRefetchData] = useState<boolean>(false);
  const [snackbarState, showSuccessAlert, showErrorAlert] = useSnackbar();
  const [resetForm, setResetForm] = useState<boolean>(false);
  const [fetchUsers, { data: users, isLoading: usersLoading }] = useGetCustomerUsersByIdMutation();
  const [
    editCustomerUser,
    {
      isError: isEditError,
      isLoading: isEditLoading,
      isSuccess: isEditSuccess,
      data: editData,
      error: editError,
    },
  ] = useEditCustomerUsersByIdMutation();
  const [isEdit, setIsEdit] = useState<{
    isEdit: boolean;
    id: number | null;
  }>({
    isEdit: false,
    id: null,
  });
  useEffect(() => {
    if (addUser) {
      setResetForm(false);
    }
  }, [addUser]);
  useEffect(() => {
    if (isEditSuccess) {
      showSuccessAlert('User updated successfully!');
      setIsEdit({
        id: null,
        isEdit: false,
      });
    }
    if (isEditError && editError) {
      showErrorAlert((editError as any).data.message ?? 'Server error');
    }
  }, [editData, isEditSuccess, editError, isEditError, isEditLoading]);

  useEffect(() => {
    if (isSuccess && newUserData) {
      showSuccessAlert('User created successfully!');
      setAddUser(false);
      onSearch();
      setResetForm(true);
    }
    if (isError && error) {
      const err = (error as any).data as CustomError;
      const exception = (err?.exception as string)?.toUpperCase();
      setResetForm(false);
      if (exception === 'USER ALREADY EXISTS') {
        showErrorAlert('User already exists');
      } else {
        showErrorAlert(String(err?.exception) ?? 'Server error');
      }
    }
  }, [newUserData, isSuccess, error, isError]);
  const columns: readonly GridColDef<UserDto>[] = [
    {
      field: 'username',
      headerName: 'Username',
      flex: 1,
      renderCell(params) {
        return <span style={{ fontWeight: 'bold' }}>{params.row.username}</span>;
      },
    },
    {
      field: 'first_name',
      headerName: 'Name',
      flex: 1,
      renderCell(params) {
        return (
          <>
            {params.row.first_name} {params.row.last_name}
          </>
        );
      },
    },

    { field: 'email', headerName: 'Email', flex: 1 },
    {
      field: 'scoped_roles',
      headerName: 'Roles',
      flex: 1,
      renderCell: (params) => {
        const rolesWithCustomers = params.row.scoped_roles
          .filter((role) => role.customers !== null)
          .map((role, index) => {
            return (
              role.role.charAt(0).toUpperCase() +
              role.role.slice(1).replace('_', ' ').toLowerCase() +
              ' - ' +
              (role.customers?.map((item) => item.name).join(', ') || '')
            );
          })
          .join(', ');
        return (
          <Tooltip title={rolesWithCustomers} placement={'bottom-start'}>
            <Box>{rolesWithCustomers}</Box>
          </Tooltip>
        );
      },
    },
    ...(globalAdmin || admin
      ? [
          {
            field: 'delete',
            headerName: 'Actions',
            flex: globalAdmin || admin ? 1 : 0,
            renderCell: (
              params: GridRenderCellParams<UserDto, any, any, GridTreeNodeWithRender>,
            ) => (
              <>
                {hasPermission('user.update') ? (
                  <Tooltip title="Edit User">
                    <Button onClick={() => handleEdit(params.row.id)}>
                      <ModeEditIcon fontSize="small" />
                    </Button>
                  </Tooltip>
                ) : null}
              </>
            ),
          },
        ]
      : []),
  ];
  const handleEdit = (id: number) => {
    setIsEdit({
      isEdit: true,
      id: id,
    });
  };
  useEffect(() => {
    if (customer === null) {
      fetchUsers({});
    }
  }, [fetchUsers]);

  useEffect(() => {
    if (ActiveCustomer?.id) {
      fetchUsers({
        customer_id: ActiveCustomer.id.toString(),
      });
      setShowFilter(true);
    }
  }, [ActiveCustomer, fetchUsers]);

  const onSearch = () => {
    const activeCustomerId = ActiveCustomer?.id ?? null;

    fetchUsers({
      customer_id: activeCustomerId
        ? activeCustomerId.toString()
        : customers?.map((ids) => ids?.id).join(','),
      customer_name: name,
      email: email,
      user_name: username,
      role: searchRoles.join(','),
    });
    setShowFilter(true);
  };
  const onClearFilter = () => {
    setName('');
    setEmail('');
    setUsername('');
    setSearchRoles([]);
    setCustomers([]);
    setShowFilter(false);
    // setCustomers([]);
    // setName('');
    // setUsername('');
    // setEmail('');
    // setSearchRoles([]);
    // setShowFilter(false);
    // setRefetchData(true);
  };
  useEffect(() => {
    if (refetchData) {
      onSearch();
      setRefetchData(false);
    }
  }, [refetchData]);
  return (
    <>
      <AlertSnackbar {...snackbarState} />
      <>
        {usersLoading ? (
          <Grid xs={9}>
            <Loader />
          </Grid>
        ) : (
          <>
            {users ? (
              <DataTable
                columns={columns}
                data={users.items ?? []}
                filterOptions={
                  <Card sx={{ p: 3, height: 600 }}>
                    {/* <Autocomplete
                      id="combo-box-demo"
                      options={data ?? []}
                      loading={isLoading}
                      multiple
                      getOptionLabel={(option) => option.name}
                      onChange={handleCustomersChange}
                      value={customers ?? []}
                      onReset={() => {
                        setCustomer(null);
                      }}
                      renderInput={(params) => <TextField {...params} label="Select Customer" />}
                    /> */}

                    <TextField
                      onChange={(e) => setName(e.target.value)}
                      value={name}
                      label="Name"
                      variant="outlined"
                      margin="normal"
                      fullWidth
                    />
                    <TextField
                      onChange={(e) => setUsername(e.target.value)}
                      value={username}
                      label="UserName"
                      variant="outlined"
                      margin="normal"
                      fullWidth
                    />
                    <TextField
                      onChange={(e) => setEmail(e.target.value)}
                      value={email}
                      label="Email"
                      variant="outlined"
                      margin="normal"
                      fullWidth
                    />
                    <Select
                      multiple
                      value={searchRoles}
                      fullWidth
                      onChange={(e) => {
                        setSearchRoles(e.target.value as string[]);
                      }}
                      input={
                        <OutlinedInput
                          label="Roles"
                          sx={{
                            '& legend': {
                              maxWidth: '100%',
                              height: 'fit-content',
                              '& span': {
                                opacity: 1,
                              },
                            },
                          }}
                        />
                      }
                      renderValue={(selected) => (
                        <Stack gap={1} direction="row" flexWrap="wrap">
                          {selected.map((value: any) => {
                            return <Chip key={value} label={value} />;
                          })}
                        </Stack>
                      )}
                    >
                      {roles.map((role, i) => (
                        <MenuItem key={i} value={role}>
                          {role}
                        </MenuItem>
                      ))}
                    </Select>
                    <Box
                      sx={{
                        width: '100%',
                        display: 'flex',
                        gap: 2,
                        mt: 2,
                      }}
                    >
                      <Button fullWidth variant="contained" onClick={() => onSearch()}>
                        Search
                      </Button>
                      <Button fullWidth variant="outlined" onClick={() => onClearFilter()}>
                        Clear Filters
                      </Button>
                    </Box>
                  </Card>
                }
                filteredData={
                  <Box>
                    {showFilter ? (
                      <>
                        {customers ? (
                          <>
                            {customers.map((customer) => (
                              <Chip
                                key={customer.id}
                                sx={{ mr: 1 }}
                                label={customer.name}
                                onDelete={() => {
                                  setCustomers(customers.filter((item) => item.id !== customer.id));
                                  setRefetchData(true);
                                }}
                              />
                            ))}
                          </>
                        ) : null}
                        {name !== '' ? (
                          <Chip
                            label={name}
                            sx={{ mr: 1 }}
                            onDelete={() => {
                              setName('');
                              setRefetchData(true);
                            }}
                          />
                        ) : null}
                        {username !== '' ? (
                          <Chip
                            label={username}
                            sx={{ mr: 1 }}
                            onDelete={() => {
                              setUsername('');
                              setRefetchData(true);
                            }}
                          />
                        ) : null}
                        {email !== '' ? (
                          <Chip
                            label={email}
                            sx={{ mr: 1 }}
                            onDelete={() => {
                              setEmail('');
                              setRefetchData(true);
                            }}
                          />
                        ) : null}
                        {searchRoles.length > 0 ? (
                          <>
                            {searchRoles.map((role, i) => (
                              <Chip
                                key={i}
                                sx={{ mr: 1 }}
                                label={role}
                                onDelete={() => {
                                  setSearchRoles(searchRoles.filter((item) => item !== role));
                                  setRefetchData(true);
                                }}
                              />
                            ))}
                          </>
                        ) : null}
                        {(customers && customers.length > 0) ||
                        name !== '' ||
                        username !== '' ||
                        email !== '' ||
                        searchRoles.length > 0 ? (
                          <Chip
                            label={'Clear all'}
                            sx={{
                              color: 'primary.main',
                              border: 'unset',
                            }}
                            onClick={() => {
                              setCustomers([]);
                              setName('');
                              setUsername('');
                              setEmail('');
                              setSearchRoles([]);
                              setShowFilter(false);
                              setRefetchData(true);
                            }}
                          />
                        ) : null}
                      </>
                    ) : null}
                  </Box>
                }
              />
            ) : null}
          </>
        )}
      </>
      <CustomDialog
        open={addUser}
        content={
          <NewUserForm
            resetForm={resetForm}
            customerItemList={
              data
                ?.filter((customer) => customer.enabled)
                .map((customer) => ({
                  id: customer.id,
                  label: customer.name,
                })) ?? []
            }
            onClose={() => {
              setAddUser(false);
            }}
            alertMessage={alertMessage}
            loading={createLoading}
            onValidSubmit={createScopedUser}
          />
        }
        dialogActions={null}
        title="Add User"
        onClose={() => {
          setAddUser(false);
        }}
      />
      <CustomDialog
        open={isEdit.id !== null && isEdit.isEdit}
        content={
          <EditUserPage
            user={users?.items.find((user) => user.id === isEdit.id)}
            customers={data ?? []}
            onValidSubmit={editCustomerUser}
            onSaveSuccess={() => {
              setIsEdit({
                id: null,
                isEdit: false,
              });
            }}
            onCanceled={() => {
              setIsEdit({
                id: null,
                isEdit: false,
              });
            }}
          />
        }
        dialogActions={null}
        title="Edit User"
        onClose={() => {
          setIsEdit({
            id: null,
            isEdit: false,
          });
        }}
      />
    </>
  );
};

export default SearchCustomerUsers;
