import { createSelector } from 'reselect';
import { RootState } from '~/redux/store';

const selectTopPanelState = (state: RootState) => state.dashboard.topPanel;

const selectDashboardTemplateState = (state: RootState) => state.dashboard.template;

export const getGlobalTimeRangeType = createSelector(
  [selectTopPanelState],
  (dashboard) => dashboard.timeRangeType,
);

export const getGlobalSamplePeriod = createSelector(
  [selectTopPanelState],
  (dashboard) => dashboard.samplePeriod,
);

export const getRefreshInterval = createSelector(
  [selectTopPanelState],
  (state) => state.refreshInterval,
);

export const getAssetTz = createSelector([selectTopPanelState], (state) => state.assetTz);

export const getDashboardTemplateSamplePeriod = createSelector(
  [selectDashboardTemplateState],
  (state) => state.topPanel.samplePeriod,
);

export const getDashboardTemplateStartDate = createSelector(
  [selectDashboardTemplateState],
  (state) => state.chart.startDate,
);

export const getDashboardTemplateEndDate = createSelector(
  [selectDashboardTemplateState],
  (state) => state.chart.endDate,
);

export const getDashboardTemplateTimeRangeType = createSelector(
  [selectDashboardTemplateState],
  (state) => state.topPanel.timeRangeType,
);

export const getDashboardTemplateAssetTz = createSelector(
  [selectDashboardTemplateState],
  (state) => state.topPanel.assetTz,
);
