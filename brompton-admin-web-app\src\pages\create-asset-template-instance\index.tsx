import { Container } from '@mui/material';
import CreateAssetTemplateInstance from '~/components/AssetTemplateInstance/CreateAssetTemplateInstance/CreateAssetTemplateInstance';
import PageName from '~/components/common/PageName/PageName';

const CreateAssetTemplateInstancePage = () => {
  return (
    <Container maxWidth="xl" sx={{ pt: 2 }}>
      <PageName name="Create Asset Using Template" />

      <CreateAssetTemplateInstance />
    </Container>
  );
};
export default CreateAssetTemplateInstancePage;
