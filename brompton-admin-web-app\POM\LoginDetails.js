export class LoginDetails {
  constructor(page) {
    this.page = page;
    this.username = 'Username *';
    this.password = 'Password *';
    this.loginbutton = '#__next > div > div > form > div > button';
    this.forgot = 'Forgot password?';
    this.useremail = 'Username or Email';
    this.submit = 'Submit';

    //New User locators
    this.userclick = '//*[@id=__next]/div/div[1]/div/ul/li[4]/a/div[2]/span';
    this.newus = '//*[@id=__next]/div/div[2]/p/button';
    this.firstname = 'First Name';
    this.Lastname = 'Last Name';
    this.Usename = 'Username *';
    this.Pass = 'Password *';
    this.email = 'Email *';
    this.country = 'Country Code *';
    this.cellnumber = 'Phone Number *';
    this.sub1 = 'Select Customer for Admin';

    // Add New Customer
    this.customer1 = '//*[@id="__next"]/div/div[1]/div/ul/li[2]/a/div[2]/span';
    this.newcustomer = '//*[@id="__next"]/div/div[2]/p/button';
    this.nameid = 'Name id *';
    this.name = 'Name *';
    this.address = 'Address *';
  }

  async lauchURL() {
    await this.page.goto('https://test.brompton.ai/login');
  }

  async login(username1, password1) {
    await this.page.getByLabel(this.username).fill(username1);
    await this.page.getByLabel(this.password).fill(password1);
    await this.page.locator(this.loginbutton).click();
  }
  async invalidlogin(username2, password2) {
    await this.page.getByLabel(this.username).fill(username2);
    await this.page.getByLabel(this.password).fill(password2);
    await this.page.locator(this.loginbutton).click();
  }

  async forgetpass(emailid) {
    await this.page.getByText(this.forgot).click();
    await this.page.locator(this.useremail).fill(emailid);
    await this.page.getByText(this.submit).click();
  }

  async NewUser(f1, l1, u1, p1, e1, c1, n1) {
    await this.page.getByLabel(this.userclick).click();
    await this.page.getByLabel(this.firstname).fill(f1);
    await this.page.getByLabel(this.Lastname).fill(l1);
    await this.page.getByLabel(this.Usename).fill(u1);
    await this.page.getByLabel(this.Pass).fill(p1);
    await this.page.getByLabel(this.email).fill(e1);
    await this.page.getByLabel(this.country).fill(c1);
    await this.page.getByLabel(this.cellnumber).fill(n1);
    await this.page.getByLabel(this.sub1).click();
  }
  async NCustomer(x1, x2, x3) {
    await this.page.click(this.customer1);
    await this.page.click(this.newcustomercustomer);
    await this.page.getByLabel(this.nameid).fill(x1);
    await this.page.getByLabel(this.name).fill(x2);
    await this.page.getByLabel(this.address).fill(x3);
  }
}
