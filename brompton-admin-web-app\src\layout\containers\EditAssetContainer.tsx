import { Customer } from '~/types/customers';
import { Asset, AssetTypeOption, EditAssetDo } from '~/types/asset';
import { Typography } from '@mui/material';
import { useEffect, useState } from 'react';
import EditAssetForm from '../../components/dashboard/EditAssetForm';
import { AlertMessage } from '~/shared/forms/types';
import {
  useEditAssetMutation,
  useGetAllBackOfficeAssetTypesQuery,
  useGetAllTimeZonesQuery,
  useGetEditAssetByIdQuery,
} from '~/redux/api/assetsApi';
import {
  assetTypePathMapper,
  assetTypePathMapperFilterTemplates,
} from '~/utils/mappers/asset-type-mapper';
import { useDispatch } from 'react-redux';
import { dashboardSlice } from '~/redux/slices/dashboardSlice';
import { CustomError } from '~/errors/CustomerErrorResponse';

const EditAssetContainer = ({
  customer,
  parentAsset,
}: {
  customer: Customer;
  parentAsset?: Asset;
}): JSX.Element => {
  const dispatch = useDispatch();
  const {
    data: assetTypeListData,
    status,
    isSuccess: isSuccessfullBackOffieAssetTypes,
  } = useGetAllBackOfficeAssetTypesQuery();
  const [alertMessage, setAlertMessage] = useState<AlertMessage | undefined>(undefined);
  const [assetTypesWithPath, setAssetTypesWithPath] = useState<AssetTypeOption[]>([]);
  const { data: timeZoneListData } = useGetAllTimeZonesQuery();
  const { data: asset, isLoading: load } = useGetEditAssetByIdQuery(
    {
      customerId: customer.id,
      assetId: parentAsset?.id?.toString() ?? '',
    },
    {
      skip: parentAsset?.id === undefined,
    },
  );
  const { data: assetParent, isLoading: loading } = useGetEditAssetByIdQuery(
    {
      customerId: customer.id,
      assetId: parentAsset?.parent_ids.join(',') ?? '',
    },
    {
      refetchOnMountOrArgChange: true,
      skip:
        parentAsset?.id === undefined ||
        parentAsset?.parent_ids === undefined ||
        parentAsset?.parent_ids.length !== 1,
    },
  );

  // on asset type list loaded
  useEffect(() => {
    if (isSuccessfullBackOffieAssetTypes && assetTypeListData) {
      setAssetTypesWithPath(
        assetTypePathMapper(
          assetTypeListData.map((item) => ({
            ...item,
            name: item.name,
            id: item.id,
          })),
        ),
      );
    }
  }, [assetTypeListData, isSuccessfullBackOffieAssetTypes, status]);

  const [editAsset, { isSuccess: isEditAssetSuccessFull, isError: isEditError, error: editError }] =
    useEditAssetMutation();
  if (isEditAssetSuccessFull) {
  }
  useEffect(() => {
    if (isEditError && editError) {
      const err = editError as CustomError;
      setAlertMessage({ message: err.data.message ?? 'Server error', severity: 'error' });
    }
    if (isEditAssetSuccessFull) {
      setAlertMessage({
        message: `Asset updated successfully!`,
        severity: 'success',
      });
      setTimeout(() => {
        dispatch(dashboardSlice.actions.selectMainPanel('assetDetails'));
      }, 2000);
    }
  }, [isEditAssetSuccessFull, editError, isEditError]);
  return (
    <>
      <Typography variant="h4">Edit asset</Typography>
      {parentAsset && (
        <Typography variant="subtitle1">Parent: {assetParent?.tag ?? 'N/A'}</Typography>
      )}
      <EditAssetForm
        timeZoneList={timeZoneListData ?? []}
        assetTypesWithPath={assetTypesWithPath}
        loading={load}
        parentId={parentAsset ? parentAsset.id : undefined}
        alertMessage={alertMessage}
        asset={asset ? (asset as EditAssetDo) : undefined}
        onValidSubmit={async (asset) => {
          await editAsset({
            customerId: customer.id,
            assetId: parentAsset?.id ? parentAsset?.id.toString() : '',
            editAsset: asset,
          });
          if (isEditAssetSuccessFull) {
            dispatch(dashboardSlice.actions.selectMainPanel('MeasureDetails'));
          }
        }}
      />
    </>
  );
};

export default EditAssetContainer;
