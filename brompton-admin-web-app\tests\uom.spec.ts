import { test, expect } from '@playwright/test';

test('test', async ({ page }) => {
  await page.goto('https://test.pivotol.ai/login');
  await page.getByLabel('Username *').click();
  await page.getByLabel('Username *').fill('test');
  await page.getByLabel('Password *').click();
  await page.getByLabel('Password *').fill('Br0mpt0n!0T');
  await page.getByRole('button', { name: 'Log in' }).click();
  await page.waitForTimeout(2000);
  await page.getByRole('button', { name: 'CalcEng<PERSON>' }).click();
  await page.waitForTimeout(3000);
  await page.getByRole('menuitem', { name: 'Unit of Measure' }).click();
  await page.waitForTimeout(3000);
  await page.getByRole('button', { name: 'Add Unit of Measure' }).click();
  await page.getByLabel('Unit of measure', { exact: true }).click();
  await page.getByLabel('Unit of measure', { exact: true }).fill('test100');
  await page.getByRole('combobox', { name: 'Measurement Type' }).click();
  await page.getByRole('option', { name: 'Acceleration' }).click();
  await page.getByRole('button', { name: 'Add' }).click();
  await page.waitForTimeout(1000);
  await page.close();
});
