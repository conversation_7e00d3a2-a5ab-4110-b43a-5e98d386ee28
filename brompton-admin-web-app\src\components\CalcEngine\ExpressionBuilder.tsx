import {
  Alert,
  Box,
  Button,
  Checkbox,
  FormControl,
  FormControlLabel,
  Grid,
  MenuItem,
  Select,
  SelectChangeEvent,
  Typography,
} from '@mui/material';
import { ChangeEvent, Dispatch, ReactNode, SetStateAction, useEffect, useState } from 'react';
import { CustomError } from '~/errors/CustomerErrorResponse';
import {
  useCreateCalculationEngineInstanceMutation,
  useGetPollPeriodsQuery,
} from '~/redux/api/calculationEngine';
import { useGetAllMeasurementsByCustomerQuery } from '~/redux/api/measuresApi';
import { AlertMessage } from '~/shared/forms/types';
import { calc_engine_template } from '~/types/calc_engine';
import { Customer } from '~/types/customers';
import SelectVariables from '../dashboard/CalcEngine/SelectVariables';
import ExpressionTemplateDetails from './ExpressionTemplateDetails';

type ExpressionBuilderProps = {
  expressionTemplate: calc_engine_template | null;
  setStep: Dispatch<SetStateAction<number>>;
  customer: Customer | null;
};
type variables = {
  variable_name: string;
  variable_value_Type: 'measurement' | 'custom';
  variable_value: string;
  comments: string;
};
const ExpressionBuilder = ({ expressionTemplate, setStep, customer }: ExpressionBuilderProps) => {
  const [variables, setVariables] = useState<variables[]>([]);
  const [outputMeasure, setOutputMeasure] = useState<string>('');
  const [isPersistance, setIsPersistance] = useState<boolean>(false);
  const [pollPeriod, setPollPeriod] = useState<number | null>(null);
  const { data: pollPeriods } = useGetPollPeriodsQuery();
  const [alertMessage, setAlertMessage] = useState<AlertMessage | undefined>(undefined);
  const [createInstance, { isError, isLoading, isSuccess, data: createInstanceData, error }] =
    useCreateCalculationEngineInstanceMutation();
  const { data: measurementsList, isFetching } = useGetAllMeasurementsByCustomerQuery(
    { customerId: customer?.id ?? 0 },
    {
      skip: !customer || customer.id === 0,
      refetchOnMountOrArgChange: true,
    },
  );
  useEffect(() => {
    if (isSuccess && createInstanceData) {
      setAlertMessage({
        message: `Expression Template Instance created successfully!`,
        severity: 'success',
      });
    }

    if (isError && error) {
      const err = error as CustomError;
      setAlertMessage({ message: err.data.message ?? 'Server error', severity: 'error' });
    }
  }, [createInstanceData, error, isError, isSuccess]);
  useEffect(() => {
    if (expressionTemplate) {
      const variables = expressionTemplate.expression.match(/\$\w+/g);
      const uniqueVariables = new Set(variables);
      setVariables(
        Array.from(uniqueVariables)?.map((variable) => {
          return {
            variable_name: variable,
            variable_value_Type: 'measurement',
            variable_value: '',
            comments: '',
          };
        }) ?? [],
      );
    }
  }, [expressionTemplate]);
  const handleVariableChange = (
    event: ChangeEvent<HTMLInputElement | HTMLTextAreaElement>,
    index: number,
  ) => {
    const newVariables = [...variables];
    newVariables[index].variable_value = event.target.value;
    setVariables(newVariables);
  };
  const handleVariableTypeChange = (event: ChangeEvent<HTMLInputElement>, index: number) => {
    const newVariables = [...variables];
    newVariables[index].variable_value = '';
    newVariables[index].variable_value_Type = event.target.value as 'measurement' | 'custom';
    setVariables(newVariables);
  };
  const handleCommentsChange = (
    event: ChangeEvent<HTMLInputElement | HTMLTextAreaElement>,
    index: number,
  ) => {
    const newVariables = [...variables];
    newVariables[index].comments = event.target.value;
    setVariables(newVariables);
  };
  const handleOutputMeasure = (event: SelectChangeEvent<string>, child: ReactNode) => {
    setOutputMeasure(event.target.value);
  };
  const handleVariableMeasureChange = (event: SelectChangeEvent<string>, index: number) => {
    const newVariables = [...variables];
    newVariables[index].variable_value = event.target.value;
    setVariables(newVariables);
  };
  const handlePollPeriodChange = (event: SelectChangeEvent<string>) => {
    setPollPeriod(Number(event.target.value));
  };
  const saveInstance = () => {
    createInstance({
      customerId: customer?.id ?? 0,
      templateId: expressionTemplate?.id ?? 0,
      outputMesurementId: Number(outputMeasure),
      ispersisted: isPersistance,
      pollPeriod: isPersistance ? pollPeriod ?? 0 : undefined,
      inputs: variables.map((variable) => {
        return {
          inputLabel: variable.variable_name,
          constantType: !Number.isNaN(variable.variable_value) ? 'number' : 'string',
          constantValue:
            variable.variable_value_Type === 'custom' ? variable.variable_value : undefined,
          measurementId:
            variable.variable_value_Type === 'measurement'
              ? Number(variable.variable_value)
              : undefined,
          comments: variable.comments,
        };
      }),
    });
  };
  return (
    <Box mt={3} pl={3} pr={3}>
      <Typography variant="h6">Expression Builder</Typography>
      <Typography variant="body1">Expression Template: {expressionTemplate?.name}</Typography>
      <Box mt={3} mb={3}>
        <Typography variant="h6">Customer : {customer?.name}</Typography>
        <Typography variant="body1">Customer ID : {customer?.id}</Typography>
      </Box>
      <ExpressionTemplateDetails selectedTemplate={expressionTemplate} />
      <Box mt={3}>
        <FormControl fullWidth sx={{ mb: 2 }}>
          <Select
            sx={{
              width: 300,
              p: 0.3,
              '& fieldset': {
                '& legend': {
                  maxWidth: '100%',
                  height: 'auto',
                  '& span': {
                    opacity: 1,
                  },
                },
              },
            }}
            value={outputMeasure}
            onChange={handleOutputMeasure}
            label="Output Measure"
          >
            {measurementsList?.items.map((measurement, index) => {
              return (
                <MenuItem key={measurement.id} value={measurement.id}>
                  {measurement.tag}
                </MenuItem>
              );
            })}
          </Select>
        </FormControl>
        <Grid container spacing={2}>
          {variables.map((variable, index) => {
            return (
              <>
                {measurementsList ? (
                  <SelectVariables
                    variables={variable}
                    measurementsList={measurementsList ?? []}
                    isLoading={isFetching}
                    setVariables={setVariables}
                    currentVariableIndex={index}
                    key={index}
                  />
                ) : null}
              </>
            );
          })}
        </Grid>

        <Box m={3} ml={0}>
          <FormControlLabel
            control={
              <Checkbox
                checked={isPersistance}
                onChange={(event) => setIsPersistance(event.target.checked)}
              />
            }
            label="Persistance"
          />
        </Box>
        {isPersistance ? (
          <FormControl fullWidth sx={{ mb: 2 }}>
            <Select
              sx={{
                width: 300,
                p: 0.3,
                '& fieldset': {
                  '& legend': {
                    maxWidth: '100%',
                    height: 'auto',
                    '& span': {
                      opacity: 1,
                    },
                  },
                },
              }}
              value={pollPeriod?.toString() ?? ''}
              onChange={handlePollPeriodChange}
              label="Poll Period"
            >
              {pollPeriods?.items.map((pollPeriod) => {
                return (
                  <MenuItem key={pollPeriod.id} value={pollPeriod.id}>
                    {pollPeriod.value}
                  </MenuItem>
                );
              })}
            </Select>
          </FormControl>
        ) : null}
      </Box>
      <Box width={'100%'} mt={4}>
        <Button onClick={() => setStep(0)}>Back</Button>
        <Button
          onClick={saveInstance}
          disabled={
            isSuccess ||
            isLoading ||
            !outputMeasure ||
            variables.some((variable) => !variable.variable_value)
          }
        >
          Submit
        </Button>
      </Box>
      {alertMessage && (
        <Alert severity={alertMessage.severity} sx={{ mt: 3 }}>
          {alertMessage.message}
        </Alert>
      )}
    </Box>
  );
};

export default ExpressionBuilder;
