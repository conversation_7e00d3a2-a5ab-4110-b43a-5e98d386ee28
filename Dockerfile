# Stage 1: Build the react-date-time-range-picker package
FROM node:16.15.0 as picker-builder

# Set working directory
WORKDIR /app/react-date-time-range-picker

# Copy the react-date-time-range-picker package files
COPY react-date-time-range-picker/package*.json ./
COPY react-date-time-range-picker/ ./

# Install dependencies and build the package
RUN yarn install
RUN yarn build

# Stage 2: Build the brompton-admin-web-app
FROM node:16.15.0 as app-builder

# Set working directory
WORKDIR /app/brompton-admin-web-app

# Copy the brompton-admin-web-app package files
COPY brompton-admin-web-app/package*.json ./
COPY brompton-admin-web-app/ ./

# Copy the built react-date-time-range-picker package and node_modules from the previous stage
COPY --from=picker-builder /app/react-date-time-range-picker/dist ../react-date-time-range-picker/dist
COPY --from=picker-builder /app/react-date-time-range-picker/node_modules ../react-date-time-range-picker/node_modules

# Install dependencies
RUN yarn install

# Verify the environment variable
ARG ENVIRONMENT
RUN echo "ENVIRONMENT is $ENVIRONMENT"

# Conditionally build the app based on the environment
RUN if [ "$ENVIRONMENT" = "prod" ]; then \
      yarn build:prod; \
    elif [ "$ENVIRONMENT" = "test" ]; then \
      yarn build:uat; \
    else \
      yarn build:dev; \
    fi

# Stage 3: Run the app
FROM node:16.15.0

# Set working directory
WORKDIR /app

# Copy the built app from the previous stage
COPY --from=app-builder /app/brompton-admin-web-app ./

# Expose the port the app runs on
EXPOSE 8080

# Command to run the app
CMD ["yarn", "start"]