import { test, expect } from '@playwright/test';
test('deleteasset', async ({ page }) => {
  // opening the URL
  await page.goto('https://test.pivotol.ai/login'); // 60 seconds
  // Go to the username  and password
  await page.getByLabel('Username *').click();
  await page.getByLabel('Username *').fill('normaltest');
  await page.getByLabel('Password *').click();
  await page.getByLabel('Password *').fill('password123');
  // click on Login Button
  await page.click('#__next > div > div > form > div > button');
  await page.waitForTimeout(3000);
  // click on new dashboard
  await page.getByText('Add Dashboard').click({ timeout: 80000 });

  await page.waitForTimeout(3000);
  // select customer
  await page.getByLabel('Select Customer').click();
  await page.getByRole('combobox', { name: 'Customer' }).click();
  await page.getByRole('option', { name: 'Customer', exact: true }).click();
  const image = page.getByText('TAutoAssets');
  await image.click({ button: 'right' });
  await page.waitForTimeout(5000);
  //console.log('right click');
  //await page.waitForTimeout(5000);
  // click on delete option
  const image1 = page.locator('//*[@id="basic-menu"]/div[3]/ul/li[7]');
  await image1.click();
  //click on delete button on pop-up window
  const image2 = page.locator('xpath=/html/body/div[2]/div[3]/div/div[2]/button[2]');
  await image2.click();
  await page.waitForTimeout(6000);
  await page.close();
});
