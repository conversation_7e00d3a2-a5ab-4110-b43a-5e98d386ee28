import dynamic from 'next/dynamic';
import { useSelector } from 'react-redux';
import CommonWidgetContainer from '~/components/common/CommonWidgetContainer';
import Error from '~/components/common/Error/Error';
import Loader from '~/components/common/Loader';
import NoMeasureSelected from '~/components/common/NoMeasureSelected';
import { useFetchKPIBars } from '~/hooks/useFetchKPIBars';
import { getZoomEnabled } from '~/redux/selectors/dashboardSelectors';
import { KPIPercentage } from '~/types/widgets';
import { hasNoMeasureSelected } from '~/utils/utils';
import KPIPercentageDialog from './KPIPercentageDialog';
const Plot = dynamic(() => import('react-plotly.js'), { ssr: false });

type KPIPercentageContainerProps = {
  id: string;
  settings: KPIPercentage;
};

const KPIPercentageContainer = ({ id, settings }: KPIPercentageContainerProps) => {
  const {
    chartData,
    isLoading,
    layoutData,
    isError,
    removedResults,
    successAndFailedMeasurements,
  } = useFetchKPIBars(id, settings);
  const noMeasuresSelected = hasNoMeasureSelected(settings);
  const enabledZoom = useSelector(getZoomEnabled);
  return (
    <CommonWidgetContainer
      id={id}
      successAndFailedMeasurements={successAndFailedMeasurements}
      removedResults={removedResults}
      settings={settings}
      widgetName="KPI Percentage"
      widgetContent={
        <>
          {noMeasuresSelected && <NoMeasureSelected />}
          {isLoading ? (
            <Loader />
          ) : (
            <>
              {chartData && (
                <Plot
                  data={chartData}
                  useResizeHandler={true}
                  style={{ width: '100%', height: '100%' }}
                  layout={{
                    ...layoutData,
                    xaxis: {
                      ...layoutData.xaxis,
                      fixedrange: enabledZoom ? true : undefined,
                    },
                    yaxis: {
                      ...layoutData.yaxis,
                      title: 'Value',
                      position: 0,
                      fixedrange: enabledZoom ? true : undefined,
                    },
                    legend: {
                      x: 0, // Position legend at the left
                      y: -0.1, // Position legend slightly below the x-axis
                      xanchor: 'left', // Anchor the legend to the right side of the x position
                      yanchor: 'top', // Anchor the legend to the top side of the y position
                    },
                    autosize: true,
                  }}
                  config={{
                    responsive: true,
                    displaylogo: false,
                    displayModeBar: false, // This will hide the entire mode bar
                    modeBarButtonsToRemove: ['pan2d', 'lasso2d'],
                  }}
                />
              )}
            </>
          )}
        </>
      }
      widgetType="kpi-percentage"
      settingsDialog={KPIPercentageDialog}
    />
  );
};

export default KPIPercentageContainer;
