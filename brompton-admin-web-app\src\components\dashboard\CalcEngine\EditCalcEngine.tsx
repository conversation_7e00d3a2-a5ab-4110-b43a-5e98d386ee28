import {
  Alert,
  Box,
  Button,
  Checkbox,
  FormControl,
  FormControlLabel,
  Grid,
  MenuItem,
  Select,
  SelectChangeEvent,
  Typography,
} from '@mui/material';
import { Dispatch, ReactNode, SetStateAction, useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import ExpressionTemplateDetails from '~/components/CalcEngine/ExpressionTemplateDetails';
import Loader from '~/components/common/Loader';
import { CustomError } from '~/errors/CustomerErrorResponse';
import { EditAssetMeasurementForm } from '~/measurements/domain/types';
import { assetsApi } from '~/redux/api/assetsApi';
import {
  useGetCalculationByMeasureIdQuery,
  useGetCalculationEngineTemplatesQuery,
  useGetPollPeriodsQuery,
  useUpdateCalculationEngineInstanceMutation,
} from '~/redux/api/calculationEngine';
import {
  measuresApi,
  useEditMeasureMutation,
  useGetAllMeasurementsByCustomerQuery,
} from '~/redux/api/measuresApi';
import { getActiveCustomer } from '~/redux/selectors/customerSelectors';
import { AlertMessage } from '~/shared/forms/types';
import { Asset } from '~/types/asset';
import { calc_engine_template } from '~/types/calc_engine';
import SelectVariables from './SelectVariables';
type variables = {
  variable_name: string;
  variable_value_Type: 'measurement' | 'custom';
  variable_value: string;
  comments: string;
  id?: string;
};
type EditCalcEngineProps = {
  parentAsset: Asset;
  measure: EditAssetMeasurementForm;
  measurementId: string[];
  assetPath: string;
  isWriteback: boolean;
  setCalcEngine: Dispatch<SetStateAction<boolean>>;
};
const EditCalcEngine = ({
  measure,
  parentAsset,
  measurementId,
  assetPath,
  isWriteback,
  setCalcEngine,
}: EditCalcEngineProps) => {
  const dispatch = useDispatch();
  const activeCustomer = useSelector(getActiveCustomer);
  const [expressionTemplate, setExpressionTemplate] = useState<calc_engine_template | null>(null);
  const [isPersistance, setIsPersistance] = useState<boolean>(false);
  const [variables, setVariables] = useState<variables[]>([]);
  const [pollPeriod, setPollPeriod] = useState<number | null>(null);
  const [alertMessage, setAlertMessage] = useState<AlertMessage | undefined>(undefined);
  const [instanceMessage, setInstanceMessage] = useState<AlertMessage | undefined>(undefined);
  const [updateTemplate, { isError, isLoading, isSuccess, error: editError, data: editData }] =
    useUpdateCalculationEngineInstanceMutation();
  const { data: pollPeriods } = useGetPollPeriodsQuery();
  const [writeback, setWriteback] = useState<boolean>(false);
  useEffect(() => {
    setWriteback(isWriteback);
  }, [isWriteback]);
  const [
    editMeasurement,
    {
      data: editAssetMeasurement,
      isSuccess: successFull,
      error: errorData,
      isError: haveError,
      isLoading: isLoadingEdit,
    },
  ] = useEditMeasureMutation();
  const { data: measurementsList, isFetching: fetchingMeasures } =
    useGetAllMeasurementsByCustomerQuery(
      { customerId: activeCustomer?.id ?? 0 },
      {
        skip: !activeCustomer || activeCustomer.id === 0,
        refetchOnMountOrArgChange: true,
      },
    );

  useEffect(() => {
    if (isSuccess) {
      setInstanceMessage({
        message: `Expression Template Instance updated successfully!`,
        severity: 'success',
      });
    }

    if (isError && editError) {
      const err = editError as CustomError;
      setInstanceMessage({ message: err.data.message ?? 'Server error', severity: 'error' });
    }
  }, [editData, isError, isSuccess]);
  useEffect(() => {
    if (successFull) {
      setAlertMessage({
        message: `Measure updated successfully!`,
        severity: 'success',
      });

      dispatch(assetsApi.util.invalidateTags([{ type: 'Asset', id: parentAsset.id }]));
      dispatch(assetsApi.util.invalidateTags([{ type: 'Asset' }]));
      dispatch(measuresApi.util.invalidateTags(['Measure']));
      updateTemplate({
        customerId: activeCustomer?.id ?? 0,
        templateId: expressionTemplate?.id ?? 0,
        pollPeriod: pollPeriod ?? 0,
        ispersisted: isPersistance,
        iswriteback: writeback,
        //eslint-disable-next-line @typescript-eslint/ban-ts-comment
        //@ts-ignore
        outputMesurementId: measure.measurement_id,
        id: calcEngineTemplates ? calcEngineTemplates.calcInstance?.id : 0,
        inputs: variables.map((variable) => {
          return {
            inputLabel: variable.variable_name,
            constantType: !Number.isNaN(variable.variable_value) ? 'number' : 'string',
            constantValue:
              variable.variable_value_Type === 'custom'
                ? variable.variable_value.toString()
                : undefined,
            measurementId:
              variable.variable_value_Type === 'measurement'
                ? Number(variable.variable_value)
                : undefined,
            comment: variable.comments ?? '',
            inputId: variable.id ? Number(variable.id) : undefined,
          };
        }),
      });
    }
    if (haveError && errorData) {
      const err = errorData as CustomError;

      setAlertMessage({ message: err.data.message ?? 'Server error', severity: 'error' });
    }
  }, [parentAsset.id, successFull, haveError, errorData, editAssetMeasurement]);
  const handlePollPeriodChange = (event: SelectChangeEvent<string>) => {
    setPollPeriod(Number(event.target.value));
  };
  const { data: expressionTemplates } = useGetCalculationEngineTemplatesQuery();
  const { data: calcEngineTemplates, isFetching } = useGetCalculationByMeasureIdQuery(
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    //@ts-ignore
    measure.measurement_id,
    {
      refetchOnMountOrArgChange: true,
    },
  );
  useEffect(() => {
    if (calcEngineTemplates) {
      setPollPeriod(calcEngineTemplates.calcInstance.pollPeriod);
      setIsPersistance(calcEngineTemplates.calcInstance.ispersisted);
      setExpressionTemplate(calcEngineTemplates.calcTemplate as unknown as calc_engine_template);
      setVariables(
        calcEngineTemplates.calcInputs.map(
          (input) =>
            ({
              variable_name: input.inputLabel,
              variable_value_Type: input.measurementId ? 'measurement' : 'custom',
              variable_value: input.measurementId
                ? input.measurementId ?? ''
                : input.constantNumber
                ? input.constantNumber
                : input.constantString
                ? input.constantString
                : '',
              comments: input.comments,
              id: input.id?.toString(),
            } as variables),
        ),
      );
    }
  }, [calcEngineTemplates]);
  const handleChange = (event: SelectChangeEvent<number | null>, child: ReactNode) => {
    setExpressionTemplate(
      (expressionTemplates?.items?.find(
        (template) => template.id === event.target.value,
      ) as calc_engine_template) || null,
    );
  };
  const saveInstance = () => {
    editMeasurement({
      customerId: activeCustomer?.id ?? 0,
      assetId: measurementId[1],
      measId: measurementId[2],
      editAssetMeasurement: {
        ...measure,
        writeback: writeback,
        tag: (assetPath === '' ? parentAsset.tag : assetPath) + '\\' + measure.tag,
      } as EditAssetMeasurementForm,
    });
  };
  return (
    <Box pl={5}>
      {isFetching ? (
        <Loader />
      ) : (
        <>
          <Box mt={3}>
            {measure?.tag && <h3>Selected Measure: {measure.tag}</h3>}
            {measure?.description && <p>{measure.description}</p>}
          </Box>
          <Box mb={3}>
            <Select
              disabled
              value={expressionTemplate?.id ?? ''}
              sx={{
                width: 300,
                '& fieldset': {
                  '& legend': {
                    maxWidth: '100%',
                    height: 'auto',
                    '& span': {
                      opacity: 1,
                    },
                  },
                },
              }}
              onChange={handleChange}
              label="Expression Template"
            >
              {expressionTemplates?.items?.map((template) => (
                <MenuItem key={template.id} value={template.id}>
                  {template.name}
                </MenuItem>
              ))}
            </Select>
          </Box>
          <ExpressionTemplateDetails selectedTemplate={expressionTemplate} />
          <Grid container spacing={2}>
            {variables.map((variable, index) => {
              return (
                <>
                  {measurementsList ? (
                    <SelectVariables
                      variables={variable}
                      measurementsList={measurementsList ?? []}
                      isLoading={fetchingMeasures}
                      setVariables={setVariables}
                      currentVariableIndex={index}
                      key={index}
                    />
                  ) : null}
                </>
              );
            })}
          </Grid>
          <Box m={3} ml={0}>
            <FormControlLabel
              control={
                <Checkbox
                  checked={isPersistance}
                  disabled={calcEngineTemplates?.calcInstance?.ispersisted ?? false}
                  onChange={(event) => setIsPersistance(event.target.checked)}
                />
              }
              label="Persistance"
            />
          </Box>
          {isPersistance ? (
            <FormControl fullWidth sx={{ mb: 2 }}>
              <Select
                sx={{
                  width: 300,
                  p: 0.3,
                  '& fieldset': {
                    '& legend': {
                      maxWidth: '100%',
                      height: 'auto',
                      '& span': {
                        opacity: 1,
                      },
                    },
                  },
                }}
                value={pollPeriod?.toString() ?? ''}
                onChange={handlePollPeriodChange}
                disabled={calcEngineTemplates?.calcInstance?.ispersisted ?? false}
                label="Poll Period"
              >
                {pollPeriods?.items.map((pollPeriod) => {
                  return (
                    <MenuItem key={pollPeriod.id} value={pollPeriod.id}>
                      {pollPeriod.value}
                    </MenuItem>
                  );
                })}
              </Select>
              <FormControlLabel
                control={
                  <Checkbox
                    disabled={calcEngineTemplates?.calcInstance?.ispersisted ?? false}
                    checked={writeback}
                    onChange={(event) => setWriteback(event.target.checked)}
                  />
                }
                label="Writeback"
              />
            </FormControl>
          ) : null}
        </>
      )}
      <Box sx={{ display: 'flex', gap: 2 }}>
        <Button color="primary" variant="outlined" onClick={() => setCalcEngine(false)}>
          Previous
        </Button>
        <Button
          onClick={saveInstance}
          color="primary"
          variant="contained"
          disabled={
            //   isSuccess ||
            //   isLoading ||
            //   measureLoading ||
            isLoadingEdit ||
            isLoading ||
            expressionTemplate === null ||
            variables.some((variable) => !variable.variable_value) ||
            variables.find((variable) => variable.variable_value_Type === 'measurement') ===
              undefined
          }
        >
          Submit
        </Button>
      </Box>
      {alertMessage && (
        <Alert severity={alertMessage.severity} sx={{ mt: 3, mb: 3 }}>
          <Typography>{alertMessage.message}</Typography>
        </Alert>
      )}
      {instanceMessage && (
        <Alert severity={instanceMessage.severity} sx={{ mt: 3 }}>
          <Typography>{instanceMessage.message}</Typography>
        </Alert>
      )}
    </Box>
  );
};
export default EditCalcEngine;
